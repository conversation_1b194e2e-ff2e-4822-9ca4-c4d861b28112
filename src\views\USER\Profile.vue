<template>
  <div>
    <div class="profile">
      <h2 class="xxxl text weight-600 bg py-4 profile-title px-3">Profile Settings</h2>
      <div class="profile_banner my-3 pl-8 space-between">
        <div class="flex">
          <div class="py-5 pl-7">
            <avatar size="130px" :user="user" />
          </div>
          <div class="profile_separator my-1 ml-10">
          </div>
          <div class="mt-5 mb-8 ml-8">
            <div class="mb-5 l weight-400">Name: {{user?.first_name + ' ' + user?.last_name}}</div>
            <div class="mb-5 l weight-400">Role: {{ userRole }}</div>
            <div class="mb-5  weight-400">Email:  {{user.email}}</div>
            <div ><button class="btn btn-black mr-2" :disabled="updatePassword" @click="updatePassword = true">Update Password</button></div>
          </div>
        </div>
        <div class="mt-3 mr-3">
          <img class="profile_logo_image" src="~@/assets/images/brand-transparent.png" />
        </div>
      </div>
      <!-- <div class="mt-6 mb-3 text weight-500 l">
        Settings
      </div>
      <hr/> -->
      <!-- <form class="profile_form s h-center">
        <div class="column">
          <div class="input-group mb-3 imp">
          <label>Enter First Name</label>
          <input type="text" placeholder="Enter your First Name"/>
          </div>
          <div class="input-group mb-3 imp">
          <label>Enter Email ID</label>
          <input type="text" placeholder="Enter your Email ID"/>
          </div>
          <div class="input-group mb-3 imp">
          <label>Enter Company Name</label>
          <input type="text" placeholder="Enter your Company Name"/>
          </div>
          <div class="input-group mb-3">
          <label>Enter Company PAN Number</label>
          <input type="text" placeholder="Enter your PAN Number "/>
          </div>
        </div>
        <div class="column ml-10">
          <div class="input-group mb-3 imp">
          <label>Enter Last Name</label>
          <input type="text" placeholder="Enter your First Name" />
          </div>
          <div class="input-group mb-3 imp">
          <label>Enter Phone Number</label>
          <input type="text" placeholder="Enter your Phone Number" />
          </div>
          <div class="input-group mb-3 imp">
          <label>Enter Company GST Number</label>
          <input type="text" placeholder="Enter your Company GST Number" />
          </div>
          <div class="input-group mb-3">
          <label>Additional Notes</label>
          <input type="text" placeholder="If there is any  additional notes please add here" />
          </div>
        </div>
      </form> -->
    </div>
    <section class="update text v-center column p-5" v-if="updatePassword">
    <p class="py-5" >Update Password</p>
    <div class="update-group bg py-5 px-20 v-center column">
      <div data-validation="Password" class="input-group imp m my-4">
        <label>Enter Current Password</label>
        <div class="input-view">
        <input v-model="currentPassword" v-on:keyup.enter="tryUpdate" placeholder="Enter current Password" :type="passwordType">
        <img  v-if="passwordType==='password'" src="~@/assets/images/icons/open-eye.svg" alt="" @click="passwordType = 'text'" >
        <img  v-if="passwordType==='text'" src="~@/assets/images/icons/hide-eye.svg" alt="" @click="passwordType = 'password'">
      </div></div>
      <div data-validation="Password" class="input-group imp m my-4">
        <label>Enter New Password</label>
        <div class="input-view">
        <input v-model="newPassword" v-on:keyup.enter="tryUpdate" placeholder="Enter new Password" :type="newPasswordType">
        <img  v-if="newPasswordType==='password'" src="~@/assets/images/icons/open-eye.svg" alt="" @click="newPasswordType = 'text'" >
        <img  v-if="newPasswordType==='text'" src="~@/assets/images/icons/hide-eye.svg" alt="" @click="newPasswordType = 'password'">
      </div></div>
      <div class="v-cente flex-end my-10">
        <button class="btn btn-black mx-2" @click="updatePassword = false">Cancel</button>
        <button class="btn mr-2" @click="tryUpdate" >Update</button>
      </div>
    </div>
  </section>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import avatar from '../../components/common/avatar.vue'
import Loader from '@/plugins/loader'
import { logout, updatePassword } from '@/api/session'
import updateValidation from '@/helper/formValidation/updatePassword'
import ConfirmationDialog from '@/plugins/confirmationDialog'

export default {
  components: { avatar },
  data () {
    return {
      updatePassword: false,
      passwordType: 'password',
      newPasswordType: 'password',
      currentPassword: '',
      newPassword: '',
      userRole: ''
    }
  },
  methods: {
    tryUpdate () {
      ConfirmationDialog(
        'Updating the password would log you out of all your sessions. Are you sure you want to continue?',
        (res) => {
          if (res) {
            const loader = new Loader()
            if (updateValidation(this.newPassword)) {
              loader.show()
              updatePassword(this.currentPassword, this.newPassword).then(res => {
                const invalidPass = 'Invalid Password'
                if (res.message === invalidPass) {
                  this.$notify.alert(res.message)
                } else {
                  this.$notify.success('Password updated successfully')
                  setTimeout(() => {
                    logout()
                  }, 2000)
                  this.updatePassword = false
                }
              }).catch(err => {
                this.$notify.alert(err?.message ?? 'Something went wrong')
              }).finally(() => {
                loader.hide()
              })
            }
          }
        }
      )
    }
  },
  computed: {
    ...mapGetters(['user', 'isExternalCollaborator'])
  },
  mounted () {
    if (this.isExternalCollaborator) {
      this.userRole = 'EXTERNAL-COLLABORATOR'
    } else if (this.user.projectLevelRole === null) {
      this.userRole = this.user.tenantLevelRole
    } else {
      this.userRole = this.user.projectLevelRole
    }
  }
}
</script>

<style lang="scss" scoped>
.profile {
  &_banner{
    background: rgba(254, 215, 153, 0.6);
    border-radius: 4px;
  }
  &-title{
    margin: -12px;
    margin-bottom : 0px;
  }
  &_image{
  border: 5px solid var(--brand-color);
  border-radius: 50%;
  }
  &_separator{
    background-color: var(--text-color);
    opacity: 0.4;
    width: 1px;
    height: 160px;
  }
  &_logo_image{
    width: 353.25px;
    height: 100px;
  }
  &_form{
    max-width: 900px;
    margin: 0 auto;
    margin-top: 25px;
  }
  .column{
    width: 50%;
  }
}
.update {
  h1 {
    font-size: 36px;
    font-weight: 500;
  }
  &-group {
    max-width: 700px;
    width: 100%;
    border-radius: 4px;
    min-height: calc(100vh - 300px);
    & > div {
      width: 100%;
      button {
        width: 140px;
      }
    }
  }
}
.input-view{
    border: 1px solid rgba(59, 59, 59, 0.4666666667);
    width: 100%;
    border-radius: 0.285em;
    background-color: transparent;
    display: flex;
    align-items: center;
    &:focus-within {
      box-shadow: 0 0 0 1px var(--brand-color-1);
    }
}
.input-view input{
  padding-inline: .85em;
  padding-block: .85em;
  height: 100%;
  flex-grow: 1;
  border: 0;
  outline: 0;
  // font-size: 1em;
  background-color: transparent;
  &::placeholder {
      color: var(--text-color);
      opacity: 0.5;
    }
  }
  .input-view img{
      padding-inline: 0.5em;
  }
</style>
