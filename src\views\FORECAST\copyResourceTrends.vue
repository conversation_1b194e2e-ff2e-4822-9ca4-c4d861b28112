<template>
    <div v-if="loading" class="loader center s">
      <loading-circle />
    </div>
    <div v-else class="trends">
      <div class="trends-header space-between">
        <div>
            <h1 class="weight-500 xxxl">Resource Trends</h1>
        </div>
        <span v-if="isOnProjectLevel">
            <span class="mx-3">
                <router-link
          :to="'/trendsandforecast/copy-material-forecast'"
          >
          <button
            class="toggle toggle-left pointer "
          >
            Forecast
          </button>
        </router-link>
            <button
            class="toggle toggle-right pointer toggle-selected "
          >
            Trends
          </button>
        </span>
          <button class="btn "  v-if="materialData" @click="exportToCsv">Export CSV</button>
        </span>
      </div>
      <div class="trends-filters">
        <!-- <div class="space-between"> -->
          <!-- <div class="flex gap-1  flex-wrap"> -->
            <div class="date-range">
              <span>From:</span>
              <input
                type="date"
                class="ml-3"
                v-model="filters.from"
                @change="dateValidation"
              />
              <span class="ml-3">Upto:</span>
              <input
                type="date"
                class="ml-3"
                v-model="filters.upto"
                @change="applyButton = false"
                :min="filters.from"
              />
            </div>
              <div class="flex gap-1">
                <button class="btn button" :disabled="applyButton" @click="applyFilters">Apply</button>
            </div>
          <!-- </div> -->
        <!-- </div> -->
      </div>
      <div v-if="!selectedMaterial" class="forecast-empty h-center v-center mt-5">
  <h4>No resouce  has been used in the given period</h4>
    </div>
      <div class="trends-container"  v-else>
        <div class="copy-dtx-table trends-table" v-if="materialData">
          <table>
          <thead>
            <tr>
              <th>Sl.No</th>
              <th>Resource ID</th>
              <th>Resource Name</th>
              <!-- <th>Actual</th>
              <th>Planned </th> -->
            </tr>
          </thead>
          <tbody>
            <tr v-for="(resource,index) in Object.values(resourceMap)" :key="resource.id" @click="setupChartView(resource.id,resource?.material_name)" :class="{'selectedMaterial':selectedMaterial===resource.id}">
              <td>{{  index+1 || '--' }}</td>
              <td>{{  resource?.custom_material_id || '--' }}</td>
              <td>{{  resource?.material_name || '--' }}</td>
              <!-- <td>{{ resource?.inventory || '--'  }}</td>
              <td>{{  resource.quantity || '--' }}</td> -->
            </tr>
          </tbody>
        </table>
      </div>
        <i v-else>no materials are available...</i>
        <div class="trends-container-graph px-2 v-center h-center">
          <div  v-if="chartLoading">

          </div>
      <div v-else-if="selectedMaterial" class="hw-100 column v-center">
        <label> {{ resourceMap[selectedMaterial].material_name }}</label>
        <apexchart
        v-if="series.length === 2"
        class="lineCharts-single"
        type="line"
        :options="chartOptions"
        :series="series"
        >
        </apexchart>
        <apexchart
        v-if="statusSeries.length >= 0"
        class="lineCharts-single"
        type="line"
        :options="statusChartOptions"
        :series="statusSeries"
        >
        </apexchart>
      </div>

        </div>
      </div>
    </div>
  </template>

<script>
/* eslint-disable no-unmodified-loop-condition */
// import MultiselectDropdown from '../../components/common/multiselectDropdown.vue'
import { mapGetters } from 'vuex'
import { getResourceTrendsData, GetProjectAssocResources, getResourceStatusData } from '@/api'
import loadingCircle from '../../components/common/loadingCircle.vue'
import { arrayToCsv } from '@/helper/file/arrayToCsv'
import { getDayDifference, getWeeksInRangeFormatted, getWeekNumber } from '@/utils/date'
import { alert } from '@/plugins/notification'

export default {
  name: 'MaterialForecast',
  components: {
    // MultiselectDropdown,
    loadingCircle
  },
  methods: {
    setYearlyDataForResourceProduction (resourceProductionData) {
      let plannedChartSeries = []
      let actualChartSeries = []
      const firstYear = Math.min(new Date(resourceProductionData.message?.planned?.[0]?.date)?.getFullYear() || new Date(resourceProductionData.message?.actual?.[0]?.date).getFullYear(), new Date(resourceProductionData.message?.actual?.[0]?.date).getFullYear() || new Date(resourceProductionData.message?.planned?.[0]?.date)?.getFullYear())
      const lastYear = Math.max(new Date(resourceProductionData.message?.planned?.at(-1)?.date).getFullYear() || new Date(resourceProductionData.message?.actual?.at(-1)?.date).getFullYear(), new Date(resourceProductionData.message?.actual?.at(-1)?.date).getFullYear() || new Date(resourceProductionData.message?.planned?.at(-1)?.date).getFullYear())
      const months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec'
      ]
      const monthMap = {
        0: 'Jan',
        1: 'Feb',
        2: 'Mar',
        3: 'Apr',
        4: 'May',
        5: 'Jun',
        6: 'Jul',
        7: 'Aug',
        8: 'Sep',
        9: 'Oct',
        10: 'Nov',
        11: 'Dec'
      }
      let years = []
      if (!firstYear || !lastYear) {
        this.loading = false
        alert('No data is available for resource production')
        return
      }
      if (lastYear - firstYear > 0) {
        let currentYear = firstYear
        while (currentYear <= lastYear) {
          years.push(currentYear)
          currentYear++
        }
      } else {
        years = [firstYear ?? lastYear]
      }
      const chartLabel = []
      for (const year of years) {
        for (const month of months) {
          chartLabel.push(`${month}-${String(year).substring(2)}`)
        }
      }
      plannedChartSeries = new Array(chartLabel.length).fill(0)
      actualChartSeries = new Array(chartLabel.length).fill(0)
      resourceProductionData.message.planned.forEach((resource) => {
        const month = new Date(resource.date).getMonth()
        const yearstring = String(new Date(resource.date).getFullYear()).substring(2)
        const monthIndex = chartLabel.findIndex(item => item === `${monthMap[month]}-${yearstring}`)
        if (monthIndex !== -1) {
          plannedChartSeries[monthIndex] = resource.sum ?? 0
        }
      })
      resourceProductionData.message.actual.forEach((resource) => {
        const month = new Date(resource.date).getMonth()
        const yearstring = String(new Date(resource.date).getFullYear()).substring(2)
        const monthIndex = chartLabel.findIndex(item => item === `${monthMap[month]}-${yearstring}`)
        if (monthIndex !== -1) {
          actualChartSeries[monthIndex] = resource.sum ?? 0
        }
      })
      this.chartOptions.xaxis = {
        type: 'category',
        categories: [...chartLabel]
      }
      this.series = [{
        name: 'Actual',
        type: 'column',
        data: actualChartSeries
        // data: [2, 3, 4]
      }, {
        name: 'Planned',
        type: 'area',
        data: plannedChartSeries
        // data: [5, 6, 7]
      }]
    },
    setYearlyDataForResourceStatus (statusData) {
      const availableDates = statusData.map(status => new Date(status.date_value).getFullYear())
      const firstYear = Math.min(...availableDates)
      const lastYear = Math.max(...availableDates)
      const months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec'
      ]
      const monthMap = {
        0: 'Jan',
        1: 'Feb',
        2: 'Mar',
        3: 'Apr',
        4: 'May',
        5: 'Jun',
        6: 'Jul',
        7: 'Aug',
        8: 'Sep',
        9: 'Oct',
        10: 'Nov',
        11: 'Dec'
      }
      let years = []
      if (!firstYear || !lastYear) {
        this.loading = false
        alert('No data is available for resource status overview')
        return
      }
      if (lastYear - firstYear > 0) {
        let currentYear = firstYear
        while (currentYear <= lastYear) {
          years.push(currentYear)
          currentYear++
        }
      } else {
        years = [firstYear ?? lastYear]
      }
      const chartLabel = []
      for (const year of years) {
        for (const month of months) {
          chartLabel.push(`${month}-${String(year).substring(2)}`)
        }
      }
      const statusSeries = {}
      statusData.forEach((status) => {
        const month = new Date(status.date_value).getMonth()
        const yearstring = String(new Date(status.date_value).getFullYear()).substring(2)
        const monthIndex = chartLabel.findIndex(item => item === `${monthMap[month]}-${yearstring}`)
        if (monthIndex !== -1) {
          if (statusSeries[status.state_value]) {
            statusSeries[status.state_value].data[monthIndex] = Math.round(status.hours)
          } else {
            statusSeries[status.state_value] = {
              name: status.state_value,
              data: new Array(chartLabel.length).fill(0)
            }
            statusSeries[status.state_value].data[monthIndex] = Math.round(status.hours)
          }
        }
      })
      this.statusChartOptions.xaxis = {
        type: 'category',
        categories: [...chartLabel]
      }
      this.statusSeries = Object.values(statusSeries)
      console.log(chartLabel)
      console.log(this.statusSeries)
    },
    parseData (resourceProductionData, statusData) {
      let plannedChartSeries = []
      let actualChartSeries = []
      const resourceStatusData = {}

      if (this.selectedType === 'day') {
        const dates = this.generateDatesInRange(
          new Date(this.appliedFilters.from),
          new Date(this.appliedFilters.upto)
        )
        plannedChartSeries = new Array(Object.values(dates).length).fill(0)
        actualChartSeries = new Array(Object.values(dates).length).fill(0)

        statusData.message.forEach((resource) => {
          const formattedDate = new Date(resource.date_value).toISOString().split('T')[0]
          if (resourceStatusData[resource.state_value]) {
            resourceStatusData[resource.state_value].data[dates[formattedDate]] = Math.round(resource.hours)
          } else {
            resourceStatusData[resource.state_value] = {
              name: resource.state_value,
              data: new Array(Object.values(dates).length).fill(0)
            }
            resourceStatusData[resource.state_value].data[dates[formattedDate]] = Math.round(resource.hours)
          }
        })
        this.statusChartOptions.xaxis = {
          type: 'datetime',
          categories: Object.keys(dates)
        }
        this.statusSeries = Object.values(resourceStatusData)

        resourceProductionData.message.planned.forEach((resource) => {
          const fromatedDate = new Date(resource.date).toISOString().split('T')[0]
          if (dates[fromatedDate]) {
            plannedChartSeries[dates[fromatedDate]] = resource.sum
          }
        })
        resourceProductionData.message.actual.forEach((resource) => {
          const fromatedDate = new Date(resource.date).toISOString().split('T')[0]
          if (dates[fromatedDate]) {
            actualChartSeries[dates[fromatedDate]] = resource.sum
          }
        })
        this.chartOptions.labels = [...this.chartOptions.labels]
        this.chartOptions.xaxis = {
          type: 'datetime'
        }
        this.series = [{
          name: 'Actual',
          type: 'column',
          data: actualChartSeries
          // data: [2, 3, 4]
        }, {
          name: 'Planned',
          type: 'area',
          data: plannedChartSeries
          // data: [5, 6, 7]
        }]
      } else if (this.selectedType === 'year') {
        this.setYearlyDataForResourceProduction(resourceProductionData)
        this.setYearlyDataForResourceStatus(statusData.message)
      } else if (this.selectedType === 'week') {
        const weekMap = getWeeksInRangeFormatted(
          new Date(this.appliedFilters.from),
          new Date(this.appliedFilters.upto)
        )
        const weekNumbers = Object.keys(weekMap)
        plannedChartSeries = new Array(Object.keys(weekMap).length).fill(0)
        actualChartSeries = new Array(Object.keys(weekMap).length).fill(0)

        statusData.message.forEach((resource) => {
          const weekIndex = getWeekNumber(new Date(resource.date_value))
          const index = weekNumbers.findIndex(item => Number(item) === weekIndex)
          if (index !== -1) {
            if (resourceStatusData[resource.state_value]) {
              resourceStatusData[resource.state_value].data[index] = Math.round(resource.hours)
            } else {
              resourceStatusData[resource.state_value] = {
                name: resource.state_value,
                data: new Array(Object.keys(weekMap).length).fill(0)
              }
              resourceStatusData[resource.state_value].data[index] = Math.round(resource.hours)
            }
          }
        })

        this.statusChartOptions.xaxis = {
          categories: Object.values(weekMap)
        }
        this.statusSeries = Object.values(resourceStatusData)

        resourceProductionData.message.planned.forEach((resource) => {
          const weekIndex = getWeekNumber(new Date(resource.date))
          const index = weekNumbers.findIndex(item => Number(item) === weekIndex)
          if (index !== -1) {
            plannedChartSeries[index] = resource.sum
          }
        })
        resourceProductionData.message.actual.forEach((resource) => {
          const weekIndex = getWeekNumber(new Date(resource.date))
          const index = weekNumbers.findIndex(item => Number(item) === weekIndex)
          if (index !== -1) {
            plannedChartSeries[index] = resource.sum
          }
        })
        this.chartOptions.xaxis = {
          type: 'category',
          categories: Object.values(weekMap)
        }
        this.series = [{
          name: 'Actual',
          type: 'column',
          data: actualChartSeries
          // data: [2, 3, 4]
        }, {
          name: 'Planned',
          type: 'area',
          data: plannedChartSeries
          // data: [5, 6, 7]
        }]
      }
    },
    applyFilters () {
      try {
        this.series = []
        this.statusSeries = []
        this.appliedFilters = this.filters
        this.loading = true
        const days = getDayDifference(this.appliedFilters.from, this.appliedFilters.upto)
        if (days >= 90) {
          this.selectedType = 'year'
        } else if (days >= 14) {
          this.selectedType = 'week'
        } else {
          this.selectedType = 'day'
        }
        const body = {
          resourceId: this.selectedMaterial,
          startDate: this.appliedFilters.from,
          endDate: this.appliedFilters.upto,
          filterBy: this.selectedType
        }
        Promise.all([getResourceTrendsData(body), getResourceStatusData(body)]).then(([res, statusData]) => {
          this.parseData(res, statusData)
          this.loading = false
        })
      } catch (error) {
        this.loading = false
      }
    },
    getOneMonthBeforeDate (currentDate) {
      const oneMonthBefore = new Date(currentDate)
      oneMonthBefore.setMonth(oneMonthBefore.getMonth() - 1)
      return this.formatDate(oneMonthBefore)
    },
    generateDatesInRange (startDate, endDate) {
      const dates = {}
      const xAxis = []
      this.statusChartOptions.xaxis.categories = []
      const options = {
        year: '2-digit',
        month: '2-digit',
        day: '2-digit'
      }
      this.chartOptions.labels = []
      const currentDate = new Date(startDate)
      let i = 0
      while (currentDate <= endDate) {
        dates[new Date(currentDate).toISOString().split('T')[0]] = i
        xAxis.push(new Date(currentDate).toISOString())
        this.statusChartOptions.xaxis.categories.push(currentDate.toLocaleString('en-GB', options))
        currentDate.setDate(currentDate.getDate() + 1)
        i++
      }
      this.chartOptions.labels = xAxis
      return dates
    },
    formatDate (date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    setInitialDates () {
      const currentDate = new Date()
      const currentFormattedDate = this.formatDate(currentDate)
      const oneMonthBeforeFormattedDate =
          this.getOneMonthBeforeDate(currentDate)
      this.filters.from = oneMonthBeforeFormattedDate
      this.filters.upto = currentFormattedDate
    },
    setupChartView (materialId, materialName) {
      this.selectedMaterial = materialId
      this.applyFilters()
      if (materialId) {
        return
      }
      this.chartLoading = true
      this.materialSeries = [{ name: 'Forecasted Qty', data: [] }, { name: 'Current Inventory', data: [] }, { name: 'Projected Inventory', data: [] }]
      this.chartOptions.xaxis.categories = []
      const dates = this.generateDatesInRange(
        new Date(this.appliedFilters.from),
        new Date(this.appliedFilters.upto)
      )
      const numberOfDates = Object.keys(dates).length
      this.materialSeries[0].data = new Array(
        numberOfDates
      ).fill(0)
      this.materialSeries[1].data = new Array(
        numberOfDates
      ).fill(this.materialData[this.selectedMaterial].inventory ?? 0)
      this.chartLoading = false
    },
    exportToCsv () {
      try {
        const materialCsv = []
        let projectNames = ''
        let MaterialGroupNames = ''
        for (const resource of Object.values(this.resourceMap)) {
          resource['Material ID'] = resource.custom_material_id
          resource['Material Name'] = resource.material_name
          materialCsv.push(resource)
        }
        if (this.appliedFilters.projectIds.length > 0) {
          for (let i = 0; i < this.appliedFilters.projectIds.length; i++) {
            for (let j = 0; j < this.projects.length; j++) {
              if (this.appliedFilters.projectIds[i] === this.projects[j].value) {
                projectNames = projectNames + this.projects[j]?.label + ',   '
                break
              }
            }
          }
        } else {
          this.projects.forEach((project) => {
            projectNames = projectNames + project?.label + ',  '
          })
        }

        if (this.appliedFilters.materialGroupIds.length > 0) {
          for (let i = 0; i < this.appliedFilters.materialGroupIds.length; i++) {
            for (let j = 0; j < this.list_of_material_group.length; j++) {
              if (parseInt(this.appliedFilters.materialGroupIds[i]) === parseInt(this.list_of_material_group[j].value)) {
                MaterialGroupNames = MaterialGroupNames + this.list_of_material_group[j]?.label + ',  '
                break
              }
            }
          }
        } else {
          this.list_of_material_group.forEach((materialGrp) => {
            MaterialGroupNames = MaterialGroupNames + materialGrp?.label + ',  '
          })
        }
        arrayToCsv(
          materialCsv,
          ['id', '__typename', 'material_id', 'required_date', 'material_name', 'product_code', 'quantity', 'unit_of_material', 'custom_material_id'],
          'material-forecast-data.csv', { To: this.chartOptions.xaxis.categories.at(-1), from: this.chartOptions.xaxis.categories[0], Material_Group: MaterialGroupNames, Projects: projectNames }
        )
      } catch (err) {
        console.log(err)
      }
    },
    dateValidation (e) {
      this.applyButton = false
      if ((new Date(e.target.value) < new Date(this.filters?.upto)) && this.filters.upto) {
      } else {
        this.filters.upto = new Date(e.target.value).toISOString().split('T')[0]
      }
    },
    getResourceList () {
      GetProjectAssocResources([this.openProjectId], 'project').then(res => {
        res.core_material_master.forEach(item => {
          this.resourceMap[item.id] = item
        })
        this.selectedMaterial = Object.values(this.resourceMap)[0].id
        this.applyFilters()
      })
    }
  },
  data () {
    return {
      list_of_material_group: [],
      applyButton: true,
      materialSeries: [],
      chartLoading: false,
      materialData: {}, // contains materials list after calculating total quantity
      selectedMaterial: null, // contains all material data without adding quatity of same materials
      filters: {
        from: '',
        upto: '',
        projectIds: [],
        materialGroupIds: []
      },
      appliedFilters: {
        projectIds: [],
        materialGroupIds: []
      },
      loading: false,
      resourceMap: {},
      selectedType: 'day',
      projects: [],
      projectSeries: [],
      series: [],
      chartOptions: {
        chart: {
          type: 'line',
          stacked: false,
          zoom: {
            enabled: true
          }
        },
        stroke: {
          width: [0, 2],
          curve: 'smooth'
        },
        plotOptions: {
          bar: {
            columnWidth: '30%'
          }
        },
        title: {
          text: 'Resource Production Overview',
          align: 'left'
        },
        fill: {
          opacity: [0.85, 0.25, 1],
          gradient: {
            inverseColors: false,
            shade: 'light',
            type: 'vertical',
            opacityFrom: 0.85,
            opacityTo: 0.55,
            stops: [0, 100, 100, 100]
          }
        },
        markers: {
          size: 0
        },
        labels: [],
        xaxis: {
          type: 'category',
          categories: []
        },
        yaxis: {
          min: 0
        },
        tooltip: {
          shared: true,
          intersect: false,
          y: {
            formatter: function (y) {
              if (typeof y !== 'undefined') {
                return y.toFixed(0) + ' Materials'
              }
              return y
            }
          }
        }
      },
      statusSeries: [],
      statusChartOptions: {
        chart: {
          height: 350,
          type: 'line',
          zoom: {
            enabled: true
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'smooth'
        },
        title: {
          text: 'Resource Status Overview',
          align: 'left'
        },
        grid: {
          borderColor: '#e7e7e7',
          row: {
            colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
            opacity: 0.5
          }
        },
        markers: {
          size: 1
        },
        xaxis: {
          categories: []
        },
        yaxis: {
          title: {
            text: 'Hours'
          }
        },
        legend: {
          position: 'bottom',
          horizontalAlign: 'center'
        }
      }
    }
  },
  computed: {
    ...mapGetters(['user', 'getUserById', 'isOnProjectLevel', 'openProjectId']),
    ...mapGetters(['tenantUsersList', 'tenantProjectList'])
  },
  mounted () {
    this.setInitialDates()
    this.getResourceList()
    // this.applyFilters()
  },
  watch: {

  }
}

</script>

  <style lang="scss">
  .trends {
    // background-color: blue;
    display: flex;
    flex-direction: column;
    &-header {
    align-items: end;
    justify-content: space-between;
    padding: 12px 0 12px 0;
    border-bottom: var(--border);
    margin-bottom: 15px;
    }
    &-filters {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-right: auto;
    flex-wrap: nowrap;
    .date-range {
      font-size: 12px;
      & >input {
      padding: 8px;
      height: 100%;
      border: 0.7px solid var(--brand-color);
      border-radius: 4px;
      padding-inline: 4px;
      background: var(--brand-light-color);
      }
    }
      // **For smaller laptop screens**
  @media (max-width: 1200px) {
    .trends-filters {
      gap: 7px;
      button {
      transform: scale(0.95);
    }
    }
    // gap: 8px;
    .date-range {
      font-size: 11px;

      & > input {
        padding: 6px;
      }
    }
  }

  // **For iPad Pro and smaller**
  @media (max-width: 1024px) {
  .trends-filters {
    gap: 5px; // Reduce space between elements
    padding: 8px; // Reduce padding to save space
    button {
      transform: scale(0.9);
    }
  }

  .date-range input {
    padding: 5px;
  }

}

  // **For iPad Mini and smaller screens**
  @media (max-width: 768px) {
    gap: 3px;
    .trends-filters {
      button {
      transform: scale(0.8);
    }
    }
    .date-range {
      font-size: 9px;

      & > input {
        padding: 4px;
      }
    }
    button {
      transform: scale(0.9);
    }
  }
    }
    &-container {
        // background-color: blue;
      height: calc(100% - 127px);
      display: grid;
      grid-template-columns: 1fr 1fr;
      column-gap: 1rem;
      padding: 1rem;
      .trends-table {
      margin-block: 4.3rem;
      flex: 1;
      min-height: 250px;
      max-height: 500px;
      overflow-y: auto;
      }
    &-graph{
      height: 100%;
      overflow-y: auto;
      & label {
        padding-block: 5px;
        padding-inline: 10px;
        background-color: var(--brand-light-color);
        box-shadow: rgba(17, 17, 26, 0.05) 0px 4px 16px, rgba(17, 17, 26, 0.05) 0px 8px 32px;
        border: .7px solid rgba(30, 30, 30, .2);
        border-radius: 4px;
      }
    }
    }
  }
  .lineCharts {
    // margin-top: 4rem;
    &-single {
      width: 100%;
    }
  }

  .date-range {
    input {
      height: 100%;
      border: .7px solid var(--brand-color);
      border-radius: 4px;
      padding-inline: 4px;
      background: var(--brand-light-color);
    }
  }

  .loader {
    padding: 18rem;
  }
  .selectedMaterial{
    background-color: #F1F3F5 !important;
  }
  .hw-100{
    width: 100%;
    height: 100%;
  }
  .apexcharts-zoom-icon ,.apexcharts-zoomout-icon, .apexcharts-zoomin-icon, .apexcharts-zoom-icon, .apexcharts-pan-icon, .apexcharts-menu-icon, .apexcharts-reset-icon {
      width: 25px;  // Adjust the width to increase the size
      height: 25px; // Adjust the height to increase the size
      transform: scale(1) !important;

    }
    .apexcharts-toolbar{
      gap: 7px;
      z-index:3!important;
    }
    .toggle {
  font-size: 1em;
  padding: 0.5em 1.2em;
  color: var(--black);
  font-weight: 500;
  border: none;
  box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
  background-color: var(--side-bar-color);

  &:active {
    box-shadow: inset 0.2em 0.2em 0.2em rgba(0, 0, 0, 0.25);
  }
  &-selected {
    background-color: var(--brand-color);
  }
}
select{
          padding: 10px;
          border: 0.1px solid #dcd8d8;
          border-radius: 8px;
        }
  </style>
