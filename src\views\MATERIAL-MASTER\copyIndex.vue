<template>
    <div class="material-master" @click="closeDrawer" style="margin-bottom: 0;">
      <!-- <div class="material-master-bar v-center space-between px-3" style="background-color:aqua;"> -->
        <div class="header-container">
        <div class="header v-center space-between">
            <div>
                <h1 v-if="view === 'material'" class="weight-500 xxxl">Material Master</h1>
                <h1 v-else class="weight-500 xxxl">Resource Master</h1>
            </div>
            <div class="v-center">
                <button
                v-if="showAddMaterialButton && view === 'material'"
                class="btn btn-black pointer"
                @click="createNew = true"
                >
                + Add Material
            </button>
            <button
            v-else-if="showAddMaterialButton && view === 'resource'"
            class="btn btn-black pointer"
            @click="createNew = true"
            >
            + Add Resource
            </button>
            </div>
        </div>
        <div class="flex filter-options">
          <div>
            <label for="">Sort By</label>
            <select v-model="selectedSort" class="sort" name="" id="">
              <option value="1">Latest</option>
              <option value="2">Oldest</option>
              <option value="3">Sort: A to Z</option>
              <option value="4">Sort: Z to A</option>
            </select>
          </div>
            <div class="mr-3">
            <button
              class="toggle toggle-left pointer"
              :class="{ 'toggle-selected': view === 'material' }"
              @click="changeView('material')"
            >
              Material
            </button>
            <button
              class="toggle toggle-right pointer"
              :class="{ 'toggle-selected': view === 'resource' }"
              @click="changeView('resource')"
            >
              Resource
            </button>
          </div>
          <!-- <button v-if="showImportMaterialButton" class="btn btn-brand mr-3 pointer" @click="openImportModal">
            Import Material
          </button> -->
          <button class="btn btn-brand mr-3 pointer" @click="downloadCsvData">
            Export csv
          </button>
          <button @click.stop="toggleDrawer" class="btn addfilter">
              <div v-if="getAppliedFilterNumber" class="addfilter-badge">{{ getAppliedFilterNumber}}</div>
              <div v-if="getAppliedFilterNumber" class="addfilter-ping"></div>
            Add Filter
          </button>
        </div>
    </div>
        <div class="material-master-action m flex">
          <div class="drawer" :class="drawer ? 'open' : 'close'" ref="drawer">
            <div class="filters relative" @click.stop>
              <div class="input-group m">
                <h5 class="filter-by">
                  Filter by Name, ID, Product Code, and Description
                </h5>
                <input
                  :class="{
                    selectedOne:  filtersTemp.searchKeyWord,
                  }"
                  type="text"
                  v-model="filtersTemp.searchKeyWord"
                  placeholder=" Filter by Name, ID, Product Code, and Description"
                />
              </div>
              <h5 class="filter-by mt-5">Filter By Tags:</h5>
              <filterTag
                :clear="clear"
                :tags="getTags"
                @new-tag="addNewTag"
                @clear="clearAllTags"
                @update-tags="updateTags"
                :type="1"
                title="Add tags to filter"
              />
              <span
                class="v-center mt-2 m underline pointer gap-1"
                v-if="moreFilter"
                @click="moreFilter = !moreFilter"
                >show less
                <img
                  src="~@/assets/images/icons/arrow-up-icon.svg"
                  height="15px"
                  alt=""
              /></span>
              <span
                class="v-center mt-2 m underline pointer gap-1"
                v-else
                @click="moreFilter = !moreFilter"
                >view more filters
                <img
                  src="~@/assets/images/icons/arrow-down-icon.svg"
                  height="15px"
                  alt=""
              /></span>
              <div v-if="moreFilter">
                <h5 class="filter-by mt-5" v-if="view === 'resource'">
                  Filter by Resource status :
                </h5>
                <single-select-drop-down
                  :clear="clear"
                  :options="resourceStateValues"
                  :current="selectedStatus"
                  @selected="addResourceSatatus"
                  :componentfor="'materialFilter'"
                  v-if="view === 'resource'"
                />
                <h5 class="filter-by">Lead Time :</h5>
                <div class="input-group-ind">
                  <label for="">From :</label>
                  <label for="">To :</label>
                  <input
                    type="number"
                    placeholder="From"
                     @change="updatedleadTimeFrom"
                    :value="filtersTemp.leadtime.from"
                    :class="{ selectedOne: filtersTemp.leadtime.from }"
                  />
                  <input
                    type="number"
                    placeholder="To"
                    @change="updatedleadTimeTo"
                    :value="filtersTemp.leadtime.to"
                    :min="filtersTemp.leadtime.from"
                    :class="{ selectedOne: filtersTemp.leadtime.to }"
                  />
                </div>
                <h5 class="filter-by">Effective Date:</h5>
                <div class="input-group-ind">
                  <label for="">From :</label>
                  <label for="">To :</label>
                  <input
                    type="date"
                    placeholder="From"
                     @change="updateEffectiveDateFrom"
                    :value="filtersTemp.effectiveDate.from"
                    :class="{ selectedOne: filtersTemp.effectiveDate.from }"
                  />
                  <input
                    type="date"
                    placeholder="To"
                    @change="updateEffectiveDateTo"
                    :value="filtersTemp.effectiveDate.to"
                    :min="filtersTemp.effectiveDate.from"
                    :class="{ selectedOne: filtersTemp.effectiveDate.to }"
                  />
                </div>

                <h5 class="filter-by">Filter By Custom-List:</h5>
                <filter-by-custom-list
                  :clear="clear"
                  :initial-values="customListItmes"
                  @filter-cusList="addCusListFilters"
                />
              </div>
            </div>
            <div class="v-center mt-3 space-between button-box">
              <span class="v-center gap-1">
                <button class="btn btn-black" @click="cancelFilter">
                  Cancel
                </button>
                <button class="btn" @click="clearAllData">Clear All</button></span
              >
              <button class="btn" @click="applyFilter">Apply</button>
            </div>
          </div>
        <!-- </div> -->
      </div>
      <copy-material-master-table
        :view="view"
        @updateList="updateList"
        :materialMasterList="materialMasterData"
        @selectPage="selectPage"
        :pageNumber="pageNumber"
        :perPage="perPage"
        :totalCount="totalCount"
      />
      <modal
        title="Import Material"
        :open="importMaterialModal"
        @close="closeImportModal"
      >
        <import-material v-if="importMaterialModal" @close="closeImportModal" />
      </modal>
      <modal
        :title="
          view === 'material' ? 'Create New Material' : 'Create New Resource'
        "
        :open="createNew"
        @close="materialCreated"
      >
        <create-material-master
          v-if="createNew"
          :view="view"
          :open="createNew"
          @created="materialCreated"
          @close="materialCreated"
        />
      </modal>
    </div>
  </template>

<script>
import { mapGetters, mapMutations } from 'vuex'
// import MaterialMasterTable from '../../components/materialMaster/materialMasterTable.vue'
import copyMaterialMasterTable from '../../components/materialMaster/copyMaterialMasterTable.vue'
import { generateS3DownloadingUrl, GetAllformFieldsOfAllVersionsByFormTypeId, GetMaterialMasterData, ResourceState } from '@/api'
import Loader from '@/plugins/loader'
import { arrayToCsv } from '@/helper/file/arrayToCsv'
import Modal from '../../components/common/modal.vue'
import ImportMaterial from '../../components/materialMaster/importMaterial.vue'
// import { debounce } from '@/utils/debounce'
import CreateMaterialMaster from '../../components/materialMaster/createMaterialMaster.vue'
import filterTag from '../../components/common/filterTag.vue'
import FilterByCustomList from '../../components/common/filterByCustomList.vue'
import singleSelectDropDown from '../../components/common/singleSelectDropDown.vue'
import config from '@/config'
import { alert } from '@/plugins/notification'
export default {
  components: {
    Modal,
    ImportMaterial,
    CreateMaterialMaster,
    filterTag,
    FilterByCustomList,
    singleSelectDropDown,
    copyMaterialMasterTable
  },
  data: () => ({
    selectedSort: '1',
    view: 'material',
    totalCount: 0,
    pageNumber: 1,
    perPage: 20,
    materialMasterData: [],
    clear: false,
    searchKeyWord: '',
    importMaterialModal: false,
    updateOnSearch: null,
    createNew: false,
    drawer: false,
    customListItmes: null,
    effectiveDate: {
      from: null,
      to: null
    },
    leadtime: {
      from: null,
      to: null
    },
    moreFilter: false,
    resourceStateValues: [],
    selectedStatus: {},
    tagsFilter: [],
    filtersTemp: {
      searchKeyWord: '',
      customListItmes: {},
      effectiveDate: { from: null, to: null },
      leadtime: { from: null, to: null },
      tagsFilter: []
    }
  }),
  computed: {
    ...mapGetters(['user', 'getUserById', 'isOnProjectLevel', 'collaborator']),
    ...mapGetters('tag', ['getTags']),
    ...mapGetters('material', ['getFilters']),
    isCopyMM () {
      return this.$route.path.includes('copy-materialmaster')
    },
    showAddMaterialButton () {
      return (
        (this.user.tenantLevelRole === 'ADMIN' ||
            this.user.tenantLevelRole === 'COLLABORATOR') &&
          !this.isOnProjectLevel &&
          !this.collaborator
      )
    },
    showImportMaterialButton () {
      return (
        this.user.tenantLevelRole === 'ADMIN' ||
          this.user.tenantLevelRole === 'COLLABORATOR' ||
          this.user.tenantLevelRole === 'EDITOR'
      )
    },
    getAppliedFilterNumber () {
      let filterNo = 0
      if (this.getFilters.searchKeyWord) {
        filterNo++
      }
      if (this.tagsFilter.length > 0) {
        filterNo++
      }
      if (this.getFilters.lead?.from || this.getFilters.lead?.to) {
        filterNo++
      }
      if (this.getFilters?.date?.from || this.getFilters.effective?.date?.to) {
        filterNo++
      }
      if (this.getFilters.customListItmes) {
        filterNo += Object.values(this.getFilters?.customListItmes)?.length
      }
      if (!filterNo) {
        return false
      }
      return String(filterNo)?.padStart(2, '0')
    }
  },
  methods: {
    ...mapMutations('material', ['updateFilters']),
    clearAllData () {
      this.updateFilters({})
      this.selectedStatus = {}
      this.clear = true
      this.searchKeyWord = ''
      this.leadtime.from = ''
      this.leadtime.to = ''
      this.effectiveDate.from = ''
      this.effectiveDate.to = ''
      this.customListItmes = null
      this.tagsFilter = []
      this.resetTempFilters()
      this.fetchMaterialMasterData()
      setTimeout(() => {
        this.clear = false
      }, 1000)
    },
    applyFilter () {
      const filtersTemp = JSON.parse(JSON.stringify(this.filtersTemp))
      this.searchKeyWord = filtersTemp.searchKeyWord
      this.customListItmes = filtersTemp.customListItmes
      this.leadtime = filtersTemp.leadtime
      this.effectiveDate = filtersTemp.effectiveDate
      this.tagsFilter = this.getTags
      this.filtersTemp.tagsFilter = this.tagsFilter
      this.fetchMaterialMasterData()
    },
    resetFiltersFromState () {
      this.searchKeyWord = this.getFilters.searchKeyWord ?? ''
      this.leadtime = { ...this.getFilters.lead } ?? {}
      this.effectiveDate = { ...this.getFilters.date } ?? {}
      this.customListItmes = { ...this.getFilters.customListItmes } ?? {}
      this.filtersTemp.customListItmes = { ...this.getFilters.customListItmes } ?? {}
    },
    changeView (view) {
      this.view = view
      this.pageNumber = 1
      this.fetchMaterialMasterData(1)
      this.clearAllData()
      this.$router.push(`?view=${view}`)
    },
    selectPage (pageNumber) {
      this.pageNumber = pageNumber
      this.$router.push(`/copy-materialmaster/${pageNumber}`)
      // this.fetchMaterialMasterData()
    },
    updateList (updateObject) {
      if (updateObject.data.productCodeName) {
        updateObject.data.material_product_code = {
          id: updateObject.data.product_code,
          product_code: updateObject.data.productCodeName
        }
      }
      this.$set(this.materialMasterData, updateObject.index, updateObject.data)
    },
    downloadCsvData () {
      const filter = {
        type: this.view,
        jump: 0,
        perPage: 1000,
        searchKeyword: this.searchKeyWord
          ? `%${this.searchKeyWord}%`
          : undefined,
        tagId: this.getTags.length
          ? this.getTags[this.getTags.length - 1]?.id
          : undefined,
        customListItmes: this.customListItmes
          ? this.customListItmes
          : undefined,
        effectiveDate: this.effectiveDate,
        leadtime: this.leadtime,
        resourseStatusId:
            this.view === 'resource'
              ? this.selectedStatus?.id ?? undefined
              : undefined
      }
      const materialData = []
      const templateVersions = {}
      const formFields = {}
      Promise.allSettled([GetMaterialMasterData(filter, true), GetAllformFieldsOfAllVersionsByFormTypeId(config.STANDARD_MATERIAL_FORM.form_type, this.isOnProjectLevel)])
        .then(([material, template]) => {
          material = material.value?.core_material_master
          template = template.value
          for (const version of template.core_form_templates[0]?.template_versions) {
            // const versionId = version.id
            for (const item of version.template_fields) {
              if (item.caption === 'Created By' || item.caption === 'Created On' || item.caption === 'Updated By' || item.caption === 'Updated On' || item.caption === 'Due Date' || item.caption === 'Project Id') {
                continue
              } else if (templateVersions[item.field_id] && templateVersions[item.field_id].includes(item.caption.trim())) {
              } else if (templateVersions[item.field_id]) {
                templateVersions[item.field_id] += '/' + item.caption
              } else {
                templateVersions[item.field_id] = item.caption
              }
            }
          }
          for (const fieldId in templateVersions) {
            formFields[templateVersions[fieldId]] = '--'
          }
          for (const item of material) {
            const data = {}
            data[this.view === 'resource' ? 'Resourse ID' : 'Material ID'] = item.custom_material_id ?? ' --'
            data[this.view === 'resource' ? 'Resourse Name' : 'Material Name'] = item.material_name ? `"${item.material_name.replaceAll('"', '')}"` : '--'
            data.Description = item.material_description ? `"${item.material_description}"` : '--'
            data['Product Code'] = item.material_product_code?.product_code ? `"${item.material_product_code?.product_code }"` : '--'
            data['ERP Id'] = item.erp_material_id ? `"${item.erp_material_id }"` : '--'
            data['Gross Weight'] = item.gross_weight ?? ' --'
            data[this.view === 'resource' ? 'Resourse Group' : 'Material Group'] = (this.view === 'resource' ? item.resource_group_details?.name : item.material_group_details?.name) ?? '--'
            data.Inventory = item.inventory ?? ' --'
            data['Lead Time'] = item.lead_time ?? ' --'
            data['Storage Location'] = item.material_storage_location?.name ? `"${item.material_storage_location?.name }"` : '--'
            data['PLM Id'] = item.plm_material_id ?? ' --'
            data['PLM Record Id'] = item.plm_record_id ?? ' --'
            data['Effective Date'] = item.effective_date ?? ' --'
            data['Unit Cost'] = item.unit_cost ?? ' --'
            data['Unit Of Measurement'] = item.material_unit_details?.name ? `"${item.material_unit_details?.name}"` : '--'
            data['Unit Sale Price'] = item.unit_sale_price ?? ' --'
            data['Weight Unit'] = item.material_weight_details?.name ?? ' --'
            data.Source = item.material_source.source ?? ' --'
            data.Status = item.material_status.status ?? '--'
            if (item.tag_materials.length > 0) {
              item.tag_materials.forEach((tag) => {
                data.Tags = (data.Tags === '--' && !tag.tag?.name) ? '--' : `"${(data.Tags ?? '').replaceAll('"', '') + tag.tag?.name + ', ' }"`
              })
            } else {
              data.Tags = data.Tags || '--'
            }
            if (item.material_document_associations.length > 0) {
              item.material_document_associations.forEach((doc) => {
                data.Documents = (data.Documents === '--' && !doc.core_document?.doc_name) ? '--' : `"${(data.Documents ?? '').replaceAll('"', '') + doc.core_document?.doc_name + ', ' }"`
              })
            } else {
              data.Documents = data.Documents || '--'
            }
            Object.assign(data, formFields)
            const form = item.core_form
            // const versionId = form?.template_version_id

            for (const md of form?.forms_metadata_by_id ?? []) {
              const field = templateVersions[md.field_id]
              data[field] = `"${
                         md.string_value ??
                         md.time_value?.split('+')[0] ??
                         md.int_value ??
                         md.date_value ??
                         md.bool_value ??
                         md.point_value ??
                         md.string_value ?? '--'}"`
            }

            for (const configData of form?.forms_config_lists ?? []) {
              const field = templateVersions[configData.field_id]
              data[field] = `"${(configData.custom_list_value) ?? '--' }"`
            }
            materialData.push(data)
          }
          arrayToCsv(materialData, [], this.view === 'resource' ? 'Resourse Data.csv' : 'materialMasterData.csv')
        })
        .catch((err) => {
          console.log({ err })
        })
    },
    openImportModal () {
      this.importMaterialModal = true
    },
    closeImportModal () {
      this.importMaterialModal = false
    },
    materialCreated () {
      this.createNew = false
      this.fetchMaterialMasterData(0)
    },
    fetchMaterialMasterData (number, updateFilters = true) {
      // the given number represents the required page number , initially it will 1 by deafult
      if (updateFilters) {
        this.updateFilters({
          searchKeyWord: this.searchKeyWord,
          date: this.effectiveDate,
          lead: this.leadtime,
          customListItmes: this.customListItmes,
          tagsFilter: this.tagsFilter
        })
      }
      const searchId = this.$route?.params?.materialId
      if (searchId) {
        this.searchKeyWord = searchId
      }
      let pageNumber
      if (number) {
        pageNumber = number
      } else {
        pageNumber = this.$route?.params?.pageNumber ?? 1
      }
      this.pageNumber = JSON.parse(this.$route.params?.pageNumber ?? 1)
      const filter = {
        type: this.view,
        jump: (pageNumber - 1) * this.perPage,
        perPage: this.perPage,
        searchKeyword: this.searchKeyWord
          ? `%${this.searchKeyWord}%`
          : undefined,
        tagId: this.tagsFilter.length
          ? this.tagsFilter[this.tagsFilter.length - 1]?.id
          : undefined,
        customListItmes: this.customListItmes
          ? this.customListItmes
          : undefined,
        effectiveDate: this.effectiveDate,
        leadtime: this.leadtime,
        resourseStatusId:
            this.view === 'resource'
              ? this.selectedStatus?.id ?? undefined
              : undefined
      }
      const loader = new Loader('.material-master-table')
      loader.show()
      const self = this
      this.materialMasterData = []
      GetMaterialMasterData(filter, undefined, this.selectedSort)
        .then((res) => {
          self.materialMasterData = res.core_material_master
          self.totalCount = res.core_material_master_aggregate.aggregate.count
          loader.hide()
          self.loadAllThumbnails()
        })
        .catch((err) => {
          loader.hide()
          console.log(err)
          alert('something went wrong')
        })
    },
    ...mapMutations('tag', ['addNewTag', 'updateTags', 'clearAllTags']),
    toggleFilters () {
      this.showFilters = !this.showFilters
    },
    toggleDrawer (event) {
      if (!this.drawer) {
        this.openDrawer()
      } else {
        this.closeDrawer()
      }
    },
    openDrawer () {
      this.tagsFilter = JSON.parse(JSON.stringify(this.filtersTemp.tagsFilter))
      this.customListItmes = { ...this.filtersTemp.customListItmes }
      this.clearAllTags()
      for (const tag of this.tagsFilter) {
        this.addNewTag(tag)
      }
      this.clear = false
      this.drawer = true
      this.$nextTick(() => {
        this.$refs.drawer.focus()
      })
    },
    closeDrawer () {
      this.drawer = false
      this.resetTempFilters()
    },
    resetTempFilters () {
      this.filtersTemp = JSON.parse(JSON.stringify({
        searchKeyWord: this.searchKeyWord,
        customListItmes: this.customListItmes,
        leadtime: this.leadtime,
        effectiveDate: this.effectiveDate,
        tagsFilter: this.tagsFilter
      }))
      this.clear = true
    },
    cancelFilter () {
      this.closeDrawer()
      this.resetTempFilters()
    },
    addCusListFilters (filters) {
      this.filtersTemp.customListItmes = filters
    },
    getResourseStatus () {
      ResourceState().then((res) => {
        this.resourceStateValues = res.custom_list_values.map((item) => {
          return {
            name: item.name,
            id: item?.id,
            label: 'Resource Status'
          }
        })
        this.resourceStateValues.unshift({
          name: 'Not Selected',
          id: -1,
          label: 'Resource Status'
        })
      })
    },
    addResourceSatatus (status) {
      if (status.id === -1) {
        this.selectedStatus = {}
      } else {
        this.selectedStatus = status
      }
    },
    updatedleadTimeTo (e) {
      this.filtersTemp.leadtime.to = e.target.value
    },
    updatedleadTimeFrom (e) {
      this.filtersTemp.leadtime.from = e.target.value
    },
    updateEffectiveDateTo (e) {
      this.filtersTemp.effectiveDate.to = e.target.value
    },
    updateEffectiveDateFrom (e) {
      this.filtersTemp.effectiveDate.from = e.target.value
    },
    loadAllThumbnails () {
      const S3ThumbNailObjects = []
      this.materialMasterData.map((item) => {
        if (item.blob_reference_key) {
          S3ThumbNailObjects.push({
            fileName: 'thumbnail.jpg',
            S3Key: item.blob_reference_key
          })
        }
      })
      generateS3DownloadingUrl({
        S3Objects: S3ThumbNailObjects
      }).then((res) => {
        const thumbnailMap = {}
        for (const thumbnail of res.url) {
          thumbnailMap[thumbnail.S3Key] = thumbnail.url
        }
        for (const material of this.materialMasterData) {
          const blobkey = material.blob_reference_key
          const url = thumbnailMap[blobkey]
          if (url) {
            material.thumbnailUrl = url
          }
        }
        this.materialMasterData = [...this.materialMasterData]
      })
        .catch((err) => {
          console.log(err)
        })
    },
    keyPress (e) {
      if (!this.drawer) { return }
      if (e instanceof KeyboardEvent && e.code === 'Escape') {
        this.cancelFilter()
      } else if (e instanceof KeyboardEvent && e.code === 'Enter') {
        this.fetchMaterialMasterData()
      }
    }
  },
  mounted () {
    this.resetFiltersFromState()
    if (
        this.$route.query?.view === 'material' ||
        this.$route.query?.view === 'resource'
    ) {
      this.view = this.$route.query?.view
    }
    this.fetchMaterialMasterData()
    this.getResourseStatus()
  },
  created () {
    // this.updateOnSearch = debounce(this.fetchMaterialMasterData.bind(null, 1, false), 300)
    document.body.addEventListener('keydown', this.keyPress)
  },
  watch: {
    // '$store.state.tag.tags' () {
    //   this.fetchMaterialMasterData(1, false)
    // },
    '$route.params.pageNumber' () {
      this.fetchMaterialMasterData()
    },
    selectedSort (value) {
      if (value === '0') return
      this.fetchMaterialMasterData()
    }
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
    this.clearAllTags()
    this.updateFilters({})
  }
}
</script>

  <style lang="scss" s MaterialMasterTablecoped>
    .sort {
    height: 2em;
    width: 10rem;
    margin: 0 10px 0 10px;
    // margin-left: 2px;
    font-size: 14px;
    background-color: var(--brand-light-color);
    line-height: 1;
    border: 1px solid var(--brand-color);
    border-radius: 4px;
    padding: 4px 12px;
    }
  .material-master {
    padding-bottom: 0;
    .header-container {
        display: flex;
        flex-direction: column;
        gap: 5px;
        header {
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid var(--border);
        }
    }
    .filter-options {
        margin: 10px 0 15px 0;
        margin-left: auto;  /* Push it to the right */
        display: flex; /* Ensure buttons inside align properly */
        align-items: center;
    }
    &-bar {
      height: 60px;
      margin: -12px;
      margin-bottom: 0;
      background: var(--bg-color);
      border-bottom: var(--border);
    }
    padding-bottom: 10px;
    margin-bottom: 50px;
  }
  .toggle {
    font-size: 1em;
    padding: 0.5em 1.2em;
    color: var(--black);
    font-weight: 500;
    border: none;
    box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
    background-color: var(--side-bar-color);

    &:active {
      box-shadow: inset 0.2em 0.2em 0.2em rgba(0, 0, 0, 0.25);
    }

    &-left {
      border-radius: 0.3rem 0px 0px 0.3rem;
    }

    &-right {
      border-radius: 0px 0.3rem 0.3rem 0px;
    }

    &-selected {
      background-color: var(--brand-color);
    }
  }
  .drawer {
    position: fixed;
    margin-top: 20px;
    background-color: #f8f4f0;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    border-radius: 5px;
    max-width: 80%;
    width: 400px;
    overflow: auto;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
  }
  .drawer.open {
    opacity: 1;
    visibility: visible;
  }
  .filter-by {
    margin-top: 15px;
    margin-bottom: 8px;
  }
  .input-group-ind {
    color: var(--text-color);
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 1rem;
    input,
    select,
    textarea {
      border: 1px solid #3b3b3b77;
      display: block;
      width: 100%;
      border-radius: 0.285em;
      font-size: 1em;
      padding: 0.85em;
      background-color: transparent;
      &::placeholder {
        color: var(--text-color);
        opacity: 0.5;
      }
      &:focus {
        box-shadow: 0 0 0 1px var(--brand-color-1);
      }
    }
    label {
      display: block;
      width: 100%;
      font-size: 1em;
      margin-bottom: 0.2em;
    }
  }
  .selectedOne {
    background-color: var(--brand-light-color) !important;
  }
  .button-box {
    background-color: #f8f4f0;
    position: sticky;
    bottom: -20px;
    display: flex;
    align-items: center;
  }
  .addfilter {
    position: relative;
    overflow: visible;
    &-badge {
      position: absolute;
        top: 0;
        right: 10px;
        transform: translate(50%, -50%);
        border-radius: 50%;
        background-color:var(--brand-color-1);
        z-index: 2;
        padding: 3px;
    }
    &-ping {
        animation: ping 1.5s ease-in-out infinite both;
        padding: 10px;
        background-color:black;
        border-radius: 50%;
        transform: translate(50%, -50%);
        position: absolute;
        top: 0;
        right: 10px;
        z-index: 1;
      }
  }

  @keyframes ping {
    0% {
      transform: translate(50%, -50%);
      padding: 5px;
      opacity: 1;
    }
    100% {
      transform: translate(50%, -50%);
      padding: 18px;
      opacity: 0;
    }
  }
  </style>
