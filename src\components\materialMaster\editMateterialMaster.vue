<template>
  <div class="relative">
  <div class="material-master-edit s">
    <div class="grid-2 mt-2">
      <div class="input-group imp" data-validation="CustomMaterialId">
        <label v-if="view === 'material'" class="key">Material Id:</label>
        <label v-else class="key">Resource Id:</label>
        <input type="text" v-model="materialMasterTemp.custom_material_id" :disabled="notAdmin || materialMasterTemp.sequence_id" />
      </div>
      <div class="input-group imp" data-validation="CustomMaterialName">
        <label v-if="view === 'material'" class="key">Material Name:</label>
        <label v-else class="key">Resource Name:</label>
        <input type="text" v-model="materialMasterTemp.material_name" />
      </div>
    </div>
    <div class="grid-2 mt-2">
      <div class="input-group imp" data-validation="UnitOfMaterial">
        <label class="key">Unit Of {{ materialMasterTemp.type == materialTypeIdMap.material ?  'Material' : 'Resource'}}:</label>
        <select v-if="materialMasterTemp.type == materialTypeIdMap.resource" v-model="materialMasterTemp.unit_of_material" :disabled="true">
            <option :value="resourceUnit.id">{{ resourceUnit.name }}</option>
        </select>
        <select v-else v-model="materialMasterTemp.unit_of_material">
          <option v-for="item in list_of_unit_of_material" :value="item.id" :key="item.id">{{ item.name }}</option>
        </select>
      </div>
      <div class="input-group imp" data-validation="PlmMaterialId">
        <label class="key">PLM {{ materialMasterTemp.type == materialTypeIdMap.material ?  'Material' : 'Resource'}} Id:</label>
        <input type="text" v-model="materialMasterTemp.plm_material_id" :disabled="disabledFields.includes('plm_material_id')"/>
      </div>
    </div>
    <div class="grid-2">
      <div class="input-group mt-2">
          <label class="key">Product Code:</label>
          <input type="text" v-model="materialMasterTemp.productCodeName" :disabled="notAdmin || materialMasterTemp?.material_product_code?.sequence_id"/>
    </div>
        <div class="input-group mt-2 imp">
          <label class="key">Type:</label>
        <select v-model="materialMasterTemp.type" >
          <option v-for="(value, key) in materialType" :value="key" :key="key">{{ value }}</option>
        </select>
    </div>
  </div>
    <div v-if="moreInput">
    <div class="input-group mt-2">
      <label class="key">{{ view === 'material' ?  'Material' : 'Resource'}} Description:</label>
      <textarea type="text" v-model="materialMasterTemp.material_description"/>
    </div>
    <div class="grid-2 mt-2">
      <div class="input-group">
        <label class="key">PLM Record Id:</label>
        <input type="text"  disabled v-model="materialMasterTemp.plm_record_id" />
      </div>
      <div class="input-group">
        <label class="key">ERP {{ view === 'material' ?  'Material' : 'Resource'}} Id:</label>
        <input type="text" v-model="materialMasterTemp.erp_material_id" :disabled="disabledFields.includes('erp_material_id')"/>
      </div>
    </div>
    <div v-if="materialMasterTemp.type == materialTypeIdMap.material" class="input-group mt-2">
      <label class="key">Material Group:</label>
      <select v-model="materialMasterTemp.material_group">
          <option v-for="item in list_of_material_group" :value="item.id" :key="item.id">{{ item.name }}</option>
      </select>
    </div>
    <div v-else class="input-group mt-2">
      <label class="key">Resource Group:</label>
      <select v-model="materialMasterTemp.resource_group">
          <option v-for="item in list_of_resource_group" :value="item.id" :key="item.id">{{ item.name }}</option>
      </select>
    </div>
    <div class="grid-2 mt-2">
      <div class="input-group" data-validation="GrossWeight">
        <label class="key">Gross Weight:</label>
        <input type="number" v-model="materialMasterTemp.gross_weight" :disabled="disabledFields.includes('gross_weight')" @keydown="changeNumber"/>
      </div>
      <div class="input-group">
        <label class="key">Weight Unit:</label>
        <select v-model="materialMasterTemp.weight_unit">
          <option v-for="item in list_of_weight_unit" :value="item.id" :key="item.id">{{ item.name }}</option>
        </select>
      </div>
    </div>
    <div class="grid-2 mt-2">
      <div class="input-group" data-validation="UnitCost">
        <label class="key">Unit Cost:</label>
        <input type="number" v-model.number="materialMasterTemp.unit_cost" @keydown="changeNumber"/>
      </div>
      <div class="input-group" data-validation="UnitSalePrice">
        <label class="key">Unit Sale Price:</label>
        <input type="number" v-model.number="materialMasterTemp.unit_sale_price" @keydown="changeNumber"/>
      </div>
    </div>
    <div class="grid-2 mt-2">
      <div class="input-group" data-validation="Quantity">
        <label class="key">Inventory</label>
        <input type="number" v-model.number="materialMasterTemp.inventory" @keydown="changeNumber"/>
      </div>
      <effective-date :date="materialMasterTemp.effective_date" @date-selected="materialMasterTemp.effective_date=$event"/>
    </div>
    <div class="grid-2 mt-2">
      <div class="input-group">
        <label class="key">Lead Time:</label>
        <input type="number" v-model.number="materialMasterTemp.lead_time" @keydown="changeNumber"/>
      </div>
      <div class="input-group">
        <label class="key">Storage Loc:</label>
        <select v-model="materialMasterTemp.storage_loc">
          <option v-if="list_of_address_location.length === 0" disabled>No address locations available.</option>
          <option v-for="item in list_of_address_location" :value="item.id" :key="item.id">{{ item.name }}</option>
        </select>
      </div>
    </div>
    <div class="grid-1 mt-2">
      <div>
        <label class="key"  >Tags</label>
        <tagInput title="Add tags to material"  :type="1"  :lastParentId="tagGroupData?.tagArray[selected_tag_line]?.at(-1)?.id" @addNewTag="addnewTag" :tagGroupData="tagGroupData" @parentLevelSelected="changeSelectedTagLine"/>
        <div class="tag-container">
          <div class="tags" v-for="tag in selectedTags" :key="tag.id">
            <div class="selected-tags">
              {{ tag.name }}
            </div>
          </div>
          <div class="tag-container"  >
            <label class="key">Attached Tags:</label>
            <div :class="{'tags mt-2 p-1':true, 'tags-line-selected p-1': selected_tag_line===index }"  v-for="(tagParentwise,index) in tagGroupData.tagArray" :key="index" @click="selected_tag_line=index">
              <div v-for="(tag, cindex) in tagParentwise" :key="tag.id "  class="flex v-center tags-line ">
                  <img width="8px" src="~@/assets/images/right-arrow-icon.svg" alt="" :class="{ 'tags-line-first-img': cindex === 0 }" />
                <div :class="{
                  'attached-tags v-center h-center ': true
              }"

                >
                {{ tag.name }}
                    <img
                :class="{
                  'pointer ml-1 close-icon': true,
                  }"
                @click="removeAttachedTags(tag.id)"
                @mouseover="sethoveredtag(cindex,index)"
                @mouseleave="sethoveredtag(null,null)"
                src="~@/assets/images/icons/close-icon.svg" width="16px"
              />
              <div :class="{'attachedTags_overLay': cindex>=hoverd_tag?.cindex && index===hoverd_tag?.index}">
              </div>
              </div>

              </div>
            </div>
            <div v-if="!attachedTags || attachedTags.length === 0">
              <b class="no-tag-found">No tag is Attached!</b>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="grid-1 mt-2">
      <div>
        <label class="key" >
        Attach Documents</label>
        <div class="document-attach" >
          <div v-if="materialMasterTemp.material_document_associations?.length > 0 " class="weight-500 document-attach-view input-group imp " >
            <span v-for="file in materialMasterTemp.material_document_associations" :key="file.id">
            <span v-show="file.flag!=='deleted'" class="document-attach-list" v-overflow-tooltip>{{ file?.core_document?.doc_name }}</span>
          </span>
          </div>
          <span v-tooltip="' Link Documents '" class="document-attach-edit" @click="documentSelectDialog = true"> <img src="~@/assets/images/icons/file-icon.svg" width="10px" height="10px" alt="" >
          </span>
          </div>      </div>
    </div>
  </div>
  <div  class="input-group mt-3" v-show="moreInput">
      <label>{{ formTemplateData.name }}</label>
    </div>
    <div class="grid-1 mt-2">
  <template v-for="(ele, index) in templateField">
    <component
      v-if="!ele.autogenerated"
      :ref="ele.field_id"
      :key="index"
      :is="ele.form_field.key + '_COMPONENT'"
      :data="ele"
      :mode="!materialMasterTemp.core_form ? 'EDIT' : 'CREATE'"
      :value="formValueData[ele.field_id]"
      :viewOnly="formValueData[ele.field_id] ? false : undefined"
      @customChange= "handleInputChange"
      v-show="moreInput"
    />
  </template>
</div>
  <div class="mt-2 m underline pointer" @click="toggleMore">
    <span v-if="moreInput">
      Show less
    </span>
    <span v-else>
      Add More Details
    </span>
  </div>

    <modal
    :open="documentSelectDialog"
    @close="documentSelectDialog = false"
    :closeOnOutsideClick="true"
    title=""
    >
    <document-selector @close="documentSelectDialog = false" :restrictUpload="true" @files-selected="handleFileSelection" v-if="documentSelectDialog"  :linkedDocs="[...materialMasterTemp.material_document_associations]" :material-attachment="true"/>
  </modal>
  </div>
  <div class="flex flex-end py-3 l sticky">
      <button class="btn btn-black mr-3 pointer" @click="$emit('close')">CANCEL</button>
      <button class="btn pointer" @click="save">SAVE</button>
    </div>
</div>
</template>

<script>
import {
  CreateProductCodeData,
  UpdateMaterialMasterData,
  AddMaterialTags,
  DeleteMaterialTags,
  UpdateProductCode,
  UnitOfMaterial,
  WeightUnit,
  AddressLocation,
  attachDocToMaterail,
  removeAttachments,
  ResourceGroup,
  getDetailFormTemplate,
  SaveFormData,
  upadateMaterialForms,
  GetFormDataByFormId,
  EditForm
} from '@/api'
import config from '../../config'
import { GetFormValueMap } from '@/helper/formValue.js'
import { alert, success } from '@/plugins/notification'
import Loader from '@/plugins/loader'
import TagInput from '../common/tagInput-edit-material.vue'
import Modal from '../common/modal.vue'
import { mapGetters } from 'vuex'
import effectiveDate from '../../components/common/effectiveDate.vue'
import MaterialMasterValidation from '@/helper/formValidation/materialMaster.js'
import DocumentSelector from '../document/documentSelectorDialog.vue'
import { TagTrie } from '@/utils/tagsHelper'
import timeComponent from '@/components/form/elements/timeComponent.vue'
import configurationList from '@/components/form/elements/configurationList.vue'
import fileComponent from '@/components/form/elements/fileComponent.vue'
import dateComponent from '@/components/form/elements/dateComponent.vue'
import longTextComponent from '@/components/form/elements/longTextComponent.vue'
import numberComponent from '@/components/form/elements/numberComponent.vue'
import switchComponent from '@/components/form/elements/switchComponent.vue'
import textComponent from '@/components/form/elements/textComponent.vue'
import companyComponent from '@/components/form/elements/companyComponent.vue'
import userComponent from '@/components/form/elements/userComponent.vue'
import multiUserComponent from '@/components/form/elements/multiUserComponent.vue'
import materialComponent from '@/components/form/elements/materialComponent.vue'
import locationComponent from '@/components/form/elements/locationComponent.vue'
import productCodeComponent from '@/components/form/elements/productCodeComponent.vue'
import { restrictKeys } from '@/utils/validations'
import { GetTenantConfigTypes } from '@/api/apis/tenantConfigFeature'
export default {
  name: 'material-master-edit',
  components: {
    TagInput,
    effectiveDate,
    DocumentSelector,
    Modal,
    BOOLEAN_COMPONENT: switchComponent,
    NUMBER_COMPONENT: numberComponent,
    TEXT_COMPONENT: textComponent,
    LONG_TEXT_COMPONENT: longTextComponent,
    DATE_COMPONENT: dateComponent,
    TIME_COMPONENT: timeComponent,
    CONFIGURATION_LIST_COMPONENT: configurationList,
    ATTACHMENT_COMPONENT: fileComponent,
    COMPANY_COMPONENT: companyComponent,
    USER_COMPONENT: userComponent,
    MULTI_USER_COMPONENT: multiUserComponent,
    MATERIALS_COMPONENT: materialComponent,
    LOCATION_COMPONENT: locationComponent,
    PRODUCT_CODE_COMPONENT: productCodeComponent
  },
  props: {
    materialMaster: {
      type: Object,
      default: () => ({})
    },
    view: {
      type: String
    }
  },
  data () {
    return {
      materialTypeIdMap: {
        material: 1,
        resource: 2
      },
      materialType: {},
      resourceUnit: {},
      documentSelectDialog: false,
      addProduct: false,
      productCodeName: '',
      selectedDate: null,
      parentProductCode: null,
      materialMasterTemp: {},
      productCodeList: [],
      moreInput: false,
      disabledFields: [],
      list_of_unit_of_material: [],
      list_of_weight_unit: [],
      list_of_material_group: [],
      list_of_resource_group: [],
      list_of_address_location: [],
      attachedTags: [],
      selectedTags: [],
      deleteSelectedTags: [],
      tagGroupData: {
        firstLevelParents: [],
        tagArray: []
      },
      hoverd_tag: {
        cindex: null,
        index: null
      },
      selected_tag_line: 0,
      tagTrie: new TagTrie(),
      lastParentId: null,
      window: window,
      buttonDisabled: false,
      templateId: '',
      formTemplateData: {},
      formValueData: {},
      metadataPayloadValueMap: {},
      validationErrors: '' // this is for getting valiadtion errors
    }
  },
  computed: {
    ...mapGetters('tag', ['getTags']),
    ...mapGetters(['isBeaconAdmin', 'isTenantAdmin', 'isOnProjectLevel', 'tenantDefaultsData']),
    notAdmin () {
      return !this.isTenantAdmin
    },
    templateField () {
      if (this.formTemplateData && this.formTemplateData.template_versions) {
        const activeVersion =
        this.formTemplateData.template_versions.find((item) => item.active) ||
        {}
        return activeVersion.template_fields || []
      }
      return []
    }
  },
  methods: {
    changeNumber (event) {
      if (['e', 'E', '+', '-'].includes(event.key)) {
        restrictKeys(event)
      }
    },
    handleInputChange (data) {
      if (this.materialMasterTemp.core_form) {
        this.metadataPayloadValueMap[data.field_id] = data.value
      }
    },
    async getSTFtempId () {
      if (this.tenantDefaultsData?.material_temp_id) {
        this.templateId = this.tenantDefaultsData.material_temp_id
        return
      }
      const res = await GetTenantConfigTypes()
      const tenantDefaults = res.tenant_defaults?.[0]
      this.$store.commit('setTenentDefaults', tenantDefaults)
      this.templateId = tenantDefaults.tenant_feature_configuration?.MATERIAL.FORM_TEMPLATE_DEFAULT
    },
    getFormTemplateData () {
      getDetailFormTemplate(this.templateId).then((res) => {
        this.formTemplateData = res.core_form_templates_by_pk
      })
    },
    saveForm () {
      const form = this.$refs
      const formValue = {}
      this.validationErrors = []
      let isValid = true // Track if the form is valid
      Object.keys(form).forEach((key) => {
        this.validateInputData(form[key][0]?.data, form)
        if (form[key].length && form[key][0]?.data?.autogenerated === false) {
          formValue[key] = form[key][0].componentValue
        }
      })
      if (this.validationErrors.length > 0) {
        isValid = false
        this.validationErrors.forEach(error => alert(error))
        return
      }
      const inputPayload = []
      if (!this.materialMasterTemp.core_form) {
        Object.keys(formValue).forEach((key) => {
          if (formValue[key] !== undefined && formValue[key] !== null) {
            if (formValue[key] !== 0 && formValue[key].length !== 0) {
              inputPayload.push({
                field_id: key,
                value: formValue[key]
              })
            }
          }
        })
        const body = [{
          templateId: this.templateId,
          dueDate: new Date().toISOString().split('T')[0],
          inputPayload: inputPayload,
          status: 1
        }]
        if (inputPayload.length > 0) {
          const loader = new Loader()
          loader.show()
          SaveFormData(body, this.isOnProjectLevel).then((res) => {
            // this means you are uploading a material custom field form
            if (this.materialMasterTemp.id) {
              const formId = res.insertedFormIds[0]
              this.materialMasterTemp.core_form = { id: formId }
              upadateMaterialForms(this.materialMasterTemp.id, formId)
                .then(() => {
                  loader.hide()
                })
            }
          })
        }
      } else {
        const metadataPayload = []
        for (const [key, value] of Object.entries(this.metadataPayloadValueMap)) {
          metadataPayload.push({ field_id: key, value })
        }
        const payload = {
          formId: this.materialMasterTemp.core_form.id,
          metadataPayload
        }
        if (metadataPayload.length > 0) {
          const loader = new Loader()
          loader.show()
          EditForm(payload).then(() => {
            loader.hide()
          })
        }
      }
      return isValid
    },
    async save () {
      const loader = new Loader()
      try {
        if (!MaterialMasterValidation(this.materialMasterTemp)) {
          return
        }
        const isFormValid = await this.saveForm()
        if (!isFormValid) {
          return
        }
        if (this.materialMasterTemp.inventory && this.materialMasterTemp.inventory > 2147483647) return alert('Inventory limit exceeded. Lower the value!')
        this.materialMasterTemp.tag_materials = this.tagGroupData?.tagArray?.flat(1).map(tag => {
          return {
            tag: {
              id: tag.id,
              name: tag.name,
              parent_id: tag.parentId
            }
          }
        })
        loader.show()
        const updateObj = {
          id: this.materialMasterTemp.id,
          type: this.materialMasterTemp.type,
          custom_material_id: this.materialMasterTemp.custom_material_id,
          gross_weight: this.disabledFields.includes('gross_weight') ? this.materialMaster.gross_weight : this.materialMasterTemp.gross_weight,
          lead_time: this.materialMasterTemp.lead_time,
          material_description: this.materialMasterTemp.material_description,
          material_name: this.materialMasterTemp.material_name,
          inventory: this.materialMasterTemp.inventory,
          effective_date: this.materialMasterTemp.effective_date,
          storage_loc: this.materialMasterTemp.storage_loc,
          unit_cost: this.materialMasterTemp.unit_cost,
          unit_of_material: this.disabledFields.includes('unit_of_material') ? this.materialMaster.unit_of_material : this.materialMasterTemp.unit_of_material,
          unit_sale_price: this.materialMasterTemp.unit_sale_price,
          weight_unit: this.disabledFields.includes('weight_unit') ? this.materialMaster.weight_unit : this.materialMasterTemp.weight_unit,
          erp_material_id: this.disabledFields.includes('erp_material_id') ? this.materialMaster.erp_material_id : this.materialMasterTemp.erp_material_id,
          product_code: this.materialMasterTemp.product_code,
          // plm_record_id: this.disabledFields.includes('plm_record_id') ? this.materialMaster.plm_record_id : this.materialMasterTemp.plm_record_id,
          plm_material_id: this.disabledFields.includes('plm_material_id') ? this.materialMaster.plm_material_id : this.materialMasterTemp.plm_material_id
          // parent_id: this.materialMasterTemp.parent_id
        }

        if (this.materialMasterTemp.type === this.materialTypeIdMap.material) {
          updateObj.material_group = this.materialMasterTemp.material_group
          updateObj.resource_group = null
        } else {
          updateObj.resource_group = this.materialMasterTemp.resource_group
          updateObj.material_group = null
        }

        if (
      this.materialMasterTemp.productCodeName?.length &&
      !this.materialMasterTemp.material_product_code?.product_code && this.isTenantAdmin
        ) {
          const body = {
            product_code: this.materialMasterTemp.productCodeName
          }
          const res = await CreateProductCodeData(body)

          const id = res.insert_product_code_one.id
          updateObj.product_code = id
        } else if (this.materialMasterTemp.productCodeName?.length &&
        this.materialMasterTemp.product_code && this.isTenantAdmin) {
          await UpdateProductCode(this.materialMasterTemp.product_code, this.materialMasterTemp.productCodeName)
        }
        await UpdateMaterialMasterData(updateObj)

        await this.updateDocAttchments()
        if (this.tagGroupData.tagArray) {
          const tagMaterials = []
          for (const tagArray of this.tagGroupData.tagArray) {
            for (const tag of tagArray) {
              tagMaterials.push({
                tag_id: tag.id,
                material_id: updateObj.id
              })
            }
          }
          // here we are deleteing all tags associated with  given material is deleted and then new tags will attached
          await DeleteMaterialTags(updateObj.id)
          await AddMaterialTags(tagMaterials)
        }

        success('Material Master Data updated successfully')
        loader.hide()
        this.materialMasterTemp.material_unit_details = {
          name: this.list_of_unit_of_material.filter((element) => {
            if (element.id === this.materialMasterTemp.unit_of_material) {
              return element.name
            }
          })[0]?.name,
          id: this.materialMasterTemp.unit_of_material
        }
        this.materialMasterTemp.material_product_code = {
          product_code: this.$apexchartsproductCodeName
        }
        this.materialMasterTemp.material_weight_details = {
          name: this.list_of_weight_unit.filter((element) => {
            if (element.id === this.materialMasterTemp.weight_unit) {
              return element.name
            }
          })[0]?.name,
          id: this.materialMasterTemp.weight_unit
        }
        this.materialMasterTemp.material_group_details = {
          name: this.list_of_material_group.filter((element) => {
            if (element.id === this.materialMasterTemp.material_group) {
              return element.name
            }
          })[0]?.name,
          id: this.materialMasterTemp.material_group
        }
        this.materialMasterTemp.resource_group_details = {
          name: this.list_of_resource_group.filter((element) => {
            if (element.id === this.materialMasterTemp.resource_group) {
              return element.name
            }
          })[0]?.name,
          id: this.materialMasterTemp.resource_group
        }
        this.$emit('updateAndClose', this.materialMasterTemp)
      } catch (err) {
        if (err.message.includes('unique_product_code_validation')) {
          alert('Product code already exist')
        } else if (err.message.includes('core_material_master_custom_material_id_tenant_id_key')) {
          alert('Material ID Already exists!')
        } else if (err.message.includes('tag_material_pkey')) {
          alert('Tag Already attached!')
        } else {
          console.log(err)
          alert('Something went wrong')
        }
        loader.hide()
      }
    },
    resetForm () {
      this.materialMaster = {}
    },
    toggleMore () {
      this.moreInput = !this.moreInput
    },
    listOfUnitOfMaterial () {
      UnitOfMaterial().then((res) => {
        this.list_of_unit_of_material = res.custom_list_values.map((item) => {
          if (item.name === 'Hours (hrs)') {
            this.resourceUnit = item
            if (this.materialMasterTemp.type === 2) {
              this.materialMasterTemp.unit_of_material = item.id
            }
          }
          return {
            name: item.name,
            id: item.id
          }
        })
      })
    },
    listOfWeightUnit () {
      WeightUnit().then((res) => {
        this.list_of_weight_unit = res.custom_list_values.map((item) => {
          return {
            name: item.name,
            id: item.id
          }
        })
      })
    },
    getFormData () {
      if (this.materialMasterTemp.core_form) {
        GetFormDataByFormId(this.materialMasterTemp.core_form.id, this.isOnProjectLevel).then(
          (res) => {
            this.formValueData = GetFormValueMap(res.core_forms_by_pk)
          }
        )
      }
    },
    listOfMaterialGroup () {
      ResourceGroup('Material Group').then((res) => {
        this.list_of_material_group = res.custom_list_values.map((item) => {
          return {
            name: item.name,
            id: item.id
          }
        })
      })
      ResourceGroup('Resource Group').then((res) => {
        this.list_of_resource_group = res.custom_list_values.map((item) => {
          return {
            name: item.name,
            id: item.id
          }
        })
      })
    },
    listOfAddressLocation () {
      AddressLocation().then((res) => {
        this.list_of_address_location = res.address_locations
      })
    },
    handleFileSelection (selectedFileData) {
      // the selectedfiles data is comming from modal (contains  all the datat related to selected docs)
      this.documentSelectDialog = false
      this.materialMasterTemp.material_document_associations = selectedFileData
    },
    async updateDocAttchments () {
      const addnewAttachments = []
      const deleteAttachments = []
      this.materialMasterTemp.material_document_associations.forEach((element) => {
        if (element.flag === 'new') {
          addnewAttachments.push({ material_id: this.materialMasterTemp.id, document_id: element.core_document.id })
        } else if (element.flag === 'deleted') {
          deleteAttachments.push(element.core_document.id)
        }
      })
      addnewAttachments.length > 0 && await attachDocToMaterail(addnewAttachments)
      deleteAttachments.length > 0 && removeAttachments(this.materialMasterTemp.id, deleteAttachments)
      // this is filtering the data that passed to material master table after updation
      // if any doc get delelted ,  here it  removes it
      this.materialMasterTemp.material_document_associations = this.materialMasterTemp.material_document_associations.filter((element) => {
        if (element.flag) {
          if (element.flag !== 'deleted') {
            return element
          }
        } else {
          return element
        }
      })
    },
    getAttachedTags () {
      this.attachedTags = this.$props.materialMaster.tag_materials.map(item => {
        return {
          id: item.tag.id,
          name: item.tag.name,
          parentId: item.tag.parent_id
        }
      })
      this.tagTrie._generateTreeFromUnorderedList(this.attachedTags)
      this.tagGroupData = this.tagTrie.groupTagInArray()
    },
    removeAttachedTags (tagId) {
      this.tagTrie.deleteTagById2(tagId)
      this.tagGroupData = this.tagTrie.groupTagInArray()
    },
    sethoveredtag (cindex, index) {
      this.hoverd_tag.cindex = cindex
      this.hoverd_tag.index = index
    },
    addnewTag (tag) {
      this.tagTrie.insertTag({ name: tag.name, id: tag.id, parentId: tag.parent_id })
      this.tagGroupData = this.tagTrie.groupTagInArray()
    },
    changeSelectedTagLine (id) {
      if (id === null) {
        this.selected_tag_line = id
      } else {
        this.tagGroupData.tagArray.forEach((element, index) => {
          if (element[0].id === id) { this.selected_tag_line = index }
        })
      }
    },
    keyPress (e) {
      const activeElement = this.window.document.activeElement.tagName.toLowerCase()
      const activeElementCheck = ['input', 'select', 'textarea'].includes(activeElement)
      if (e instanceof KeyboardEvent && e.code === 'Enter' && activeElement !== 'button') {
        this.window.document.activeElement.blur()
      }
      if (this.buttonDisabled || activeElementCheck) {
        return
      }
      if (e instanceof KeyboardEvent && e.code === 'Enter') {
        this.save()
      }
    },
    validateInputData (formInput, form) {
      if (formInput?.form_field?.key === 'USER') {
        if (formInput.required && (!form[formInput.field_id][0].componentValue || !form[formInput.field_id][0].componentValue.length)) {
          this.validationErrors.push(`${formInput.caption} is a mandatory field`)
        }
      }
      if (formInput?.form_field?.key === 'BOOLEAN') {
        // we are comparing the value to null instead of checking if it is a falsy value cause the boolean component value will false in one case and it is valid value
        if (formInput.required && form[formInput.field_id][0].componentValue === null) {
          this.validationErrors.push(`${formInput.caption} is a mandatory field`)
        }
        return
      } else if (formInput?.form_field?.key === 'MATERIALS') {
        if (formInput.required && form[formInput.field_id][0].componentValue.length < 1) {
          this.validationErrors.push(`${formInput.caption} is a mandatory field`)
        }
        return
      }
      if (formInput.required && !form[formInput.field_id][0].componentValue) {
        this.validationErrors.push(`${formInput.caption} is a mandatory field`)
      }
    }
  },
  watch: {
    'materialMasterTemp.type' () {
      if (Number(this.materialMasterTemp?.type) === 2) {
        this.materialMasterTemp.unit_of_material = this.resourceUnit.id
      }
    }
  },
  async mounted () {
    this.listOfUnitOfMaterial()
    this.listOfWeightUnit()
    this.listOfMaterialGroup()
    this.listOfAddressLocation()
    this.getAttachedTags()
    await this.getSTFtempId()
    if (this.templateId) {
      await this.getFormTemplateData()
    }
    this.getFormData()
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
    this.materialMasterTemp = JSON.parse(JSON.stringify(this.materialMaster))
    this.materialType = config.MATERIAL_TYPE
    this.materialMasterTemp.productCodeName = this.materialMasterTemp.material_product_code?.product_code
    if (this.materialMasterTemp.material_source?.id === 1) {
      this.disabledFields.push('unit_of_material', 'gross_weight', 'weight_unit', 'erp_material_id')
    } else if (this.materialMasterTemp.material_source?.id === 2) {
      this.disabledFields.push('unit_of_material', 'gross_weight', 'weight_unit')
    } else if (this.materialMasterTemp.material_source?.id === 3) {
      this.disabledFields.push('unit_of_material', 'gross_weight', 'weight_unit', 'plm_material_id')
    }
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
  }
}
</script>

<style lang="scss" scoped >
.material-master-edit {
  width: 600px;
  max-height: 70vh;
  padding-right: 5px;
  overflow-y: auto;
  .document-attach{
    display: flex;
    align-items: center;
    justify-content: start;
    flex-wrap: wrap;
    width: 100%;
    max-height: 100px;
    min-height: 50px;
    // border-radius: 4px;
    // border: 2px solid rgba(var(--brand-rgb),0.4);
    cursor: pointer;
    &-view{
      display: flex;
      flex-wrap: wrap;
      gap: 5px;

    }
    &-edit{
      border: 2px solid rgba(41, 39, 39, 0.2);
      border-radius: 3px;
      padding: 4px 6px;
      width: 25px;
      display: flex;
      justify-content: center;
      margin-left: 5px;
    }
    &-list{
      border: 2px solid rgba(var(--brand-rgb));
      border-radius: 3px;
      background-color: var(--brand-color);
      padding: 5px 5px 5px 5px ;
    width: 100px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    cursor:context-menu;
    }
    span{
      cursor: pointer;
      user-select: none;
      background-color: transparent;
    }
  }
}
.tags {
  display: flex;
flex-wrap: wrap;
margin-left: 5px;
  margin-right: 5px;
  row-gap: 5px;
   &-line-selected{
    background-color: var(--brand-light-color);
   border: .4px solid var(--brand-color);
   border-radius: 5px;
   min-width: min-content;
   }
}
.tag-container {
  margin-top: 4px;
}

.no-tag-found{
  margin-left: 5px;
}
.attached-tags{
  cursor:pointer;
  background-color: var(--brand-color);
  padding: 0.6rem;
  font-size: small;
  border-radius: 0.4rem;
position:relative;
  &-fade{
background-color: rgb(0, 0,0,.6) ;
color:white
}
  & img{
 z-index:4;
  }
  & img{
    display: none;
  }
}
.tags-line{
  text-overflow: ellipsis;
  overflow: hidden;
   white-space: nowrap;
  & img{
    margin-inline: .5rem;
  }
  &-first-img{
    display: none;
  }
  .attached-tags:hover > img {
display: block;
scale: 1.2;
// -webkit-box-shadow:0px 0px 123px 21px rgba(215,224,221,0.78);
// -moz-box-shadow: 0px 0px 123px 21px rgba(215,224,221,0.78);
// box-shadow: 0px 0px 123px 21px rgba(215,224,221,0.78);
  }
  .attachedTags_overLay{
    background-color: black;
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 5px;
    opacity: .3;
    z-index: 1;
  }
}
</style>
