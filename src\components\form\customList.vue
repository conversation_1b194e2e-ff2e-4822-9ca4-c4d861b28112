<template>
  <div class="custom-list">
    <div class="input-group">
      <label>Configuration List:</label>
      <div class="flex">
        <input
          type="text"
          v-model="serarchKeyword"
          @focus="open = true"
        />
        <!-- EDIT BUTTON ENABLE -->
        <img
          width="32"
          @click="openEditList"
          v-if="false"
          class="ml-2"
          src="~@/assets/images/edit-icon.svg"
          alt=""
        />
      </div>
      <div class="custom-list--group" v-if="open">
        <div
          class="custom-list--group-item"
          v-for="item in filterdList"
          :key="item.id"
          @click.prevent="selectItem(item)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { GetActiveCustomLists, CreateCustomList, GetCustomList } from '@/api'
import { alert, success } from '@/plugins/notification'
export default {
  name: 'CustomList',
  props: {
    listId: {
      type: [String, Number],
      default: ''
    }
  },
  data () {
    return {
      serarchKeyword: '',
      customLists: [],
      open: false,
      editList: false,
      selectedList: {}
    }
  },
  computed: {
    filterdList () {
      return this.customLists.filter((item) => {
        return item.name
          .toLowerCase()
          .includes(this.serarchKeyword.toLowerCase())
      })
    }
  },
  watch: {
    listId: {
      handler (val) {
        this.serarchKeyword = ''
        this.getCustomList()
      },
      immediate: true
    }
  },
  methods: {
    blur (e) {
      setTimeout(() => {
        this.open = false
      }, 200)
    },
    selectItem (item) {
      this.serarchKeyword = item.name
      this.open = false
      this.$emit('input', item)
    },
    openEditList () {
      this.editList = true
    },
    closeEditList () {
      this.editList = false
    },
    async getCustomLists () {
      const res = await GetActiveCustomLists()
      this.customLists = res.core_custom_list
    },
    async getCustomList () {
      if (!this.listId) return
      const res = await GetCustomList(this.listId)
      this.selectedList = res.core_custom_list_by_pk
      this.serarchKeyword = this.selectedList.name
    },
    saveNewList () {
      if (this.newCustomList.name === '') {
        alert('Please enter list name')
        return
      }
      const name = this.newCustomList.name
      const data = this.newCustomList.data.map(
        (item) => item.name !== '' && item
      )
      CreateCustomList(data, name).then((res) => {
        success('List created successfully')
        this.getCustomLists()
        this.closeNewList()
      })
    }
  },
  mounted () {
    this.getCustomLists()
    this.getCustomList()
  }
}
</script>

<style lang="scss" scoped >
.custom-list {
  position: relative;
  & .input-group {
    margin-bottom: 10px;
    font-size: 12px;
    & label {
      margin-bottom: 5px;
      font-weight: 500;
      width: auto;
    }
    & input {
      width: 100%;
      padding: 5px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-weight: 500;
      &[type="checkbox"] {
        width: auto;
        margin: 0;
        margin-left: 10px;
        &:focus {
          outline: none !important;
          border: none !important;
        }
      }
    }
  }
  &--group {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    z-index: 1;
    &-item {
      padding: 5px;
      cursor: pointer;
      &:hover {
        background: rgba(var(--brand-rgb), 0.4);
      }
    }
    &-add {
      padding: 5px;
      cursor: pointer;
      margin: 5px;
      text-align: center;
      background: rgba(var(--brand-rgb), 0.5);
      &:hover {
        background: rgba(var(--brand-rgb), 1);
      }
    }
  }
  &-modal {
    width: 600px;
    min-height: 300px;
    max-height: 400px;
    &--add {
      text-align: right;
      font-size: 11px;
    }
    &--action {
      margin-top: 20px;
      font-size: 13px;
    }
    &-table {
      max-height: 200px;
      position: relative;
      overflow-y: auto;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      & thead {
        & tr {
          position: sticky;
          top: 0;
          background: #ffe393;
          z-index: 1;
        }
        & th {
          font-size: 14px;
          font-weight: 500;
          text-align: left;
          padding: 6px;
          &:last-child {
            width: 30px;
          }
        }
      }
      & tbody {
        & tr {
          border-bottom: 1px solid rgba(var(--brand-rgb), 0.4);
          &:last-child {
            border-bottom: none;
          }
          & td {
            padding: 5px;
            &:last-child {
              width: 30px;
            }
            & img {
              cursor: pointer;
            }
            & input {
              width: 100%;
              padding: 5px;
              border-radius: 4px;
              outline: none;
              border: none;
              background: transparent;
              &:focus {
                border: 1px solid rgba(var(--brand-rgb), 0.5);
              }
            }
          }
        }
      }
    }
  }
}
</style>
