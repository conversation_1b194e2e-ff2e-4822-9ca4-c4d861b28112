<template>
  <div>
    <div v-if="loading" class="flex center loading-container">
      <LoadingCircle />
    </div>
  <div v-else class="sheduler-container">
    <div class="timeLine-filters">
      <div>Filters</div>
      <div class="space-between">
        <div class="flex gap-1">
          <multiselect-dropdown
            label="Projects"
            :options="projects"
            @selected="setProjectFilters"
            :initiallySelected="appliedFilters.projectIds"
          />
          <multiselect-dropdown
            label="Users"
            v-if="view==='users'"
            :options="associatedUsers"
            @selected="setUserFilters"
            :initially-selected="appliedFilters.userIds"
          />
          <multiselect-dropdown
          label="Resources"
          :options="resources"
          @selected="setResourceFilters"
          :initially-selected="appliedFilters.resourceIds"
          v-else/>
          <multiselect-dropdown
            label="Tags"
            :options="tags"
            @selected="setTagFilters"
            :initially-selected="appliedFilters.tags"
          />
          <button class="btn btn-black pointer" @click="clearAllFilters">Clear all filters</button>
          <button class="btn" :disabled="!applyButton" @click="applyFilters">
            Apply
          </button>
        </div>

        <div></div>
      </div>
    </div>
    <timeLine-view
    v-if="!editMode && timeLineData.length > 0"
    :primaryColumnData="primaryColumnData"
    :timeLineData="timeLineData"
    :currentDate="currentDate"
    :viewModeParent="viewModeParent"
    :view="view"
    @editModeOn="goToEditMode"
    />
  <time-line-edit
  v-else-if="editMode && timeLineData.length > 0"
  :usersData="primaryColumnData"
    :timeLineData="timeLineData"
    :currentDate="currentDate"
    :viewModeParent="viewModeParent"
    :assigneeMap="assigneeMap"
    :generalTaskData="generalTaskData"
    @disableEditMode="disableEditMode"
    @saveData="updateTimeLine"

  />
  <div v-else class="h-center v-center loading-container">
   <h3>No projects available at the moment</h3>
  </div>

  </div>
</div>
</template>

<script>
import '@/assets/scheduler/dhtmlxscheduler'
import MultiselectDropdown from '../../components/common/multiselectDropdown.vue'
import { mapGetters, mapMutations } from 'vuex'
import {
  GetAllTaskTags,
  GetUserListByPojIds,
  getTimeLineDataApi,
  getProjectExchangeToken,
  getProjectsUserAsViewer,
  insertTasksData,
  deleteChanagesInTimeLine,
  UpdateTasksWithToken,
  getCalenderDataByids,
  GetProjectAssocResources
} from '@/api'
import { debounce } from '@/utils/debounce'
import TimeLineView from '../timeLine/timeLineView.vue'
import TimeLineEdit from '../timeLine/timeLineEdit.vue'
import LoadingCircle from '@/components/common/loadingCircle.vue'
import { alert, success } from '@/plugins/notification'

// let scheduler
export default {
  name: 'TimeLine',
  components: {
    MultiselectDropdown,
    TimeLineView,
    TimeLineEdit,
    LoadingCircle
  },
  props: {
    view: {
      type: String,
      default: 'users'
    }
  },
  data () {
    return {
      applyButton: true,
      filters: {
        from: '',
        upto: '',
        projectIds: [],
        userIds: [],
        tags: [],
        resourceIds: []
      },
      appliedFilters: {
        resourceIds: [],
        projectIds: [],
        userIds: [],
        tags: [],
        from: '',
        upto: ''
      },
      projects: [],
      tags: [],
      resources: [],
      associatedUsers: [],
      timeLineData: [],
      primaryColumnData: {},
      groupColors: {
        0: '#e0dcdc', // for disabled tasks
        1: '#20B2AA', // LightSeaGreen
        2: '#008B8B', // DarkCyan
        3: '#2E8B57', // SeaGreen
        4: '#00CED1', // DarkTurquoise
        5: '#008000', // Green
        6: '#ADFF2F', // GreenYellow
        7: '#5F9EA0', // CadetBlue
        8: '#9ACD32', // YellowGreen
        9: '#40E0D0', // Turquoise
        10: '#48D1CC', // MediumTurquoise
        11: '#66CDAA', // MediumAquaMarine
        12: '#7FFF00', // Chartreuse
        13: '#00FF7F', // SpringGreen
        14: '#D2691E', // Chocolate
        15: '#00FA9A', // MediumSpringGreen
        16: '#CD853F', // Peru
        17: '#3CB371', // MediumSeaGreen
        18: '#008080', // Teal
        19: '#556B2F', // DarkOliveGreen
        20: '#A0522D', // Sienna
        21: '#00FF00', // Lime
        22: '#228B22', // ForestGreen
        23: '#808000', // Olive
        24: '#008000', // Green
        25: '#8FBC8B', // DarkSeaGreen
        26: '#008B8B', // DarkCyan
        27: '#32CD32', // LimeGreen
        28: '#20B2AA', // LightSeaGreen
        29: '#006400', // DarkGreen
        30: '#CD853F' // Peru
      },
      editMode: false,
      currentDate: this.appliedFilters?.from ?? new Date(),
      viewModeParent: 'weekly',
      loading: false,
      assigneeMap: {},
      generalTaskData: {},
      viewerRoleProjects: null
    }
  },
  mounted () {
    this.clearTokenHashMap()
    this.setup()
  },
  computed: {
    ...mapGetters(['user', 'getUserById']),
    ...mapGetters(['tenantUsersList', 'tenantProjectList']),
    ...mapGetters('timeLine', ['getProjectTokens', 'getCalederHashMap']),
    selectedProjIds () {
      return this.filters.projectIds
    }
  },
  methods: {
    ...mapMutations('timeLine', ['addNewToken', 'clearTokenHashMap', 'addCalenderHashMap']),
    clearAllFilters () {
      this.filters = {
        resourceIds: [],
        projectIds: [],
        userIds: [],
        tags: [],
        from: '',
        upto: ''
      }
      this.applyFilters()
    },
    setup () {
      if (this.$props.view === 'resources') {
        this.getResourceList()
      } else {
        this.getUsersList()
      }
      this.getTimeLineData()
      this.getProjectList() // getting project data list
      this.getTaskTags() // get task tags data list
    },
    getProjectList () {
      const projectIds = []
      this.projects = []
      this.tenantProjectList.forEach((item) => {
        projectIds.push(item?.id)
        this.projects.push({
          value: item?.id,
          label: item?.name
        })
      })
      this.getCalenderData(projectIds)
    },
    getTaskTags () {
      GetAllTaskTags().then(res => {
        res.tag.forEach((item) => {
          this.tags.push({
            value: item?.id,
            label: item?.name
          })
        })
      })
    },
    getResourceList () {
      this.resources = []
      GetProjectAssocResources(this.filters.projectIds).then((res) => {
        this.resources = res.core_material_master.map((resource) => {
          return {
            label: resource.material_name,
            value: resource.id
          }
        })
      }).catch(() => {
        alert('Unable to fetch')
      })
    },
    getUsersList () {
      this.assigneeMap = {}
      this.associatedUsers = [] // TO reset users list based on selected project
      const userIdForTemp = []
      GetUserListByPojIds(this.filters.projectIds).then((res) => {
        this.associatedUsers = res.project_user_association.filter((user) => {
          // this is to store assignees of given projects
        // assiignee map will look like
        // {
        //   prjectid:[userid1,userId2]
        //   prjectid2:[userid1,userId2]
        // }
          if (Object.hasOwn(this.assigneeMap, user.associated_project?.id)) {
            this.assigneeMap[user.associated_project?.id].push(user.associated_user.id)
          } else {
            this.assigneeMap[user.associated_project?.id] = [user.associated_user.id]
          }
          // to remove the duplication need to check whether the user Data is already added or not
          // since there is no project id while fetching project_associated users initilly need to this one
          if (!userIdForTemp.includes(user.associated_user.id)) {
            userIdForTemp.push(user.associated_user.id)
            return true
          }
        }).map((filteredUser) => {
          return {
            label:
            filteredUser.associated_user.first_name +
              ' ' +
              filteredUser.associated_user.last_name,
            value: filteredUser.associated_user.id
          }
        })
      }).catch((err) => {
        console.log(err)
      })
    },
    setProjectFilters (projectIds) {
      this.filters.projectIds = projectIds
    },
    setUserFilters (userIds) {
      this.filters.userIds = userIds
    },
    setResourceFilters (resourceIds) {
      this.filters.resourceIds = resourceIds
    },
    setTagFilters (tags) {
      this.filters.tags = tags
    },
    applyFilters () {
      this.applyButton = false
      this.editMode = false
      this.appliedFilters = this.filters
      this.getTimeLineData()
    },
    async getTimeLineData (date = new Date(), viewMode = this.viewModeParent) {
      this.loading = true
      this.generalTaskData = {}
      this.primaryColumnData = {}
      const viewerProjects = await getProjectsUserAsViewer(this.user.userId)
      // viewerRoleProjects will contains all project ids where the given user is associated as viewer
      //   this array will be used as later to disbale the given tasks from editing
      this.viewerRoleProjects = []
      for (const projects of viewerProjects?.project_user_association) {
        this.viewerRoleProjects.push(projects?.associated_project?.id)
      }

      getTimeLineDataApi(this.appliedFilters, this.$props.view === 'resources').then((res) => {
        this.timeLineData = []
        if (this.$props.view === 'resources') {
          this.setDataForResources(res)
        } else {
          this.setDataForUsers(res)
        }
        this.disableEditMode({ date, viewMode })
        this.editMode = false
        this.loading = false
        this.applyButton = true
      })
        .catch((err) => {
          console.log(err)
          this.loading = false
          this.applyButton = true
        })
    },
    setDataForResources (res) {
      for (const task of res.core_tasks) {
        // const formattedResources = []
        for (const resource of task.task_material_associations[0].core_bom.bom_items) {
          let colorCode = 0
          if (task.core_project?.id <= 30) {
            colorCode = task.core_project?.id
          } else {
            colorCode = task.core_project?.id % 30
          }
          this.timeLineData.push({
            start_date: task.planned_start_date,
            end_date: task.planned_end_date,
            text: task?.text,
            section_id: resource?.core_material?.id,
            project_name: task.core_project?.name,
            projectId: task.core_project?.id,
            projectColor: task.is_critical ? 'red' : this.groupColors[Math.abs(colorCode)],
            taskId: task.id,
            id: task.id + resource?.core_material?.id,
            readonly: this.viewerRoleProjects.includes(task.core_project?.id),
            parentTask: task.parent_core_task.name
          })
          if (!Object.hasOwn(this.primaryColumnData, resource.core_material?.id)) {
            if (this.appliedFilters.resourceIds.length) {
              if (this.appliedFilters.resourceIds.includes(resource?.core_material?.id)) {
                this.primaryColumnData[resource?.core_material?.id] = {
                  key: resource.core_material?.id,
                  label: resource?.core_material?.material_name
                }
              }
            } else {
              this.primaryColumnData[resource?.core_material?.id] = {
                key: resource.core_material?.id,
                label: resource?.core_material?.material_name
              }
            }
          }
        }
      }
    },
    setDataForUsers (res) {
      for (const task of res.core_tasks) {
        const formattedAssignees = []
        // to add the task data to general hashMap
        if (!this.generalTaskData[task.id]) {
          this.generalTaskData[task.id] = task
          this.generalTaskData[task.id].assigneeIds = []
        }

        let colorCode = 0

        const hex = task.core_project?.id.replace(/-/g, '')
        // eslint-disable-next-line no-undef
        const decimalNumber = BigInt(`0x${hex}`)
        // eslint-disable-next-line no-undef
        const numericHash = Number(decimalNumber % BigInt(30))
        if (numericHash >= 0 && numericHash <= 30) {
          colorCode = numericHash
        } else {
          colorCode = numericHash % 30
        }
        // there will be a conflict comes when switching b/w monthly abd weekly , so need to these to more data to avoid that issue
        this.generalTaskData[task.id].parentTask = task.parent_core_task.name
        this.generalTaskData[task.id].readonly = this.viewerRoleProjects.includes(task.core_project?.id)
        this.generalTaskData[task.id].projectColor = task.is_critical ? 'red' : this.groupColors[Math.abs(colorCode)]
        for (const assignee of task.task_assignees) {
          this.timeLineData.push({
            start_date: task.planned_start_date,
            end_date: task.planned_end_date,
            text: task?.text,
            section_id: assignee.assignee?.id,
            project_name: task.core_project?.name,
            projectId: task.core_project?.id,
            projectColor: task.is_critical ? 'red' : this.groupColors[Math.abs(colorCode)],
            taskId: task.id,
            id: task.id + assignee.assignee?.id,
            readonly: this.viewerRoleProjects.includes(task.core_project?.id),
            parentTask: task.parent_core_task.name
          })
          // this is to create  avaialable user Data
          if (!Object.hasOwn(this.primaryColumnData, assignee.assignee.id)) {
            if (this.appliedFilters.userIds.length) {
              if (this.appliedFilters?.userIds?.includes(assignee.assignee?.id)) {
                this.primaryColumnData[assignee.assignee?.id] = {
                  key: assignee.assignee.id,
                  label: assignee.assignee?.first_name.toUpperCase() + ' ' + assignee.assignee?.last_name.toUpperCase()
                }
              }
            } else {
              this.primaryColumnData[assignee.assignee?.id] = {
                key: assignee.assignee.id,
                label: assignee.assignee?.first_name.toUpperCase() + ' ' + assignee.assignee?.last_name.toUpperCase()
                // taskId: task.id
              }
            }
          }
          formattedAssignees.push(
            {
              first_name: assignee.assignee?.first_name,
              last_name: assignee.assignee?.last_name,
              id: assignee.assignee.id,
              status: null
            })
          this.generalTaskData[task.id].assigneeIds.push(assignee.assignee?.id)
        }
        this.generalTaskData[task.id].attached_bom = task.task_material_associations[0]?.core_bom ? {
          id: task.task_material_associations[0]?.core_bom.id,
          name: task.task_material_associations[0]?.core_bom.name,
          associationId: task.task_material_associations[0]?.id
        } : null
        this.generalTaskData[task.id].task_assignees = formattedAssignees
      }
    },
    goToEditMode (data) {
      this.currentDate = new Date(data.date)
      this.viewModeParent = data.viewMode
      this.editMode = true
    },
    disableEditMode (data) {
      this.currentDate = new Date(data.date)
      this.viewModeParent = data.viewMode
      this.editMode = false // this  will be done after timeLine data fecthed
    },
    validateDates (e) {
      if (new Date(e.target.value) > new Date(this.filters.upto) && this.filters.upto) { this.filters.upto = null }
    },
    async updateTimeLine ({ date, viewMode, updatedTasks, generalTaskDataTemp }) {
      try {
        if (!updatedTasks.length > 0) {
          alert('No changes has been made')
          this.editMode = false
          return
        }
        this.loading = true
        const updateObject = {}
        const deletedOperations = {}
        const insertOperations = {}
        const updateApiArray = []
        const insertApiArray = []
        for (const task of updatedTasks) {
          const deletedAssignees = []
          const deletedDocs = []
          const newAssigneeArray = []
          const newDocArray = []
          const newTagArray = []
          const newTaskMaterialArray = []
          const {
            id: taskId,
            planned_start_date: plannedStartDate,
            planned_end_date: plannedEndDate,
            task_assignees: taskAssignees,
            core_project: { id: projectId },
            task_docs: taskDocs,
            attached_bom: attachedBom,
            tag_tasks: tags,
            duration,
            progress,
            description,
            text,
            type
          } = { ...generalTaskDataTemp[task] }
          // checking  project token is avaialble in project token's Hash map
          if (!this.getProjectTokens[projectId]) {
          // creating project token for saving the data , and storing the value as
            const response = await getProjectExchangeToken(projectId)
            this.addNewToken({ token: response.message, projectId: projectId })
          }
          // if there is no hash map with given project id will create  new obj with projectId
          if (!deletedOperations[projectId]) { deletedOperations[projectId] = [] }
          if (!insertOperations[projectId]) { insertOperations[projectId] = [] }
          if (!updateObject[projectId]) { updateObject[projectId] = [] }
          // getting deleted and added assinee ids
          for (const user of taskAssignees) {
            if (user.status === 'new') {
              newAssigneeArray.push({ task_id: taskId, user_id: user.id })
            } else if (user.status === 'deleted') {
              deletedAssignees.push(user.id)
            }
          }
          if (taskDocs?.length) {
            for (const doc of taskDocs) {
              if (doc.flag === 'deleted') {
                deletedDocs.push(doc.id)
              } else if (doc.flag === 'new') {
                newDocArray.push({ task_id: taskId, document_id: doc.id })
              }
            }
          }
          for (const tag of tags) {
            newTagArray.push({ tag_id: tag.id ?? tag.tag.id, task_id: taskId })
          }
          // attaching a bom to  a  task
          attachedBom && newTaskMaterialArray.push(`{task_id: {_eq: "${taskId}"}}, _set: {bom_id:"${attachedBom.id}"}`)

          // if (updateObject[projectId]) {
          updateObject[projectId].push({
            where: { id: { _eq: taskId } },
            _set: {
              planned_start_date: new Date(plannedStartDate).toISOString().substr(0, 10),
              planned_end_date: new Date(plannedEndDate).toISOString().substr(0, 10),
              duration,
              progress,
              type,
              description,
              name: text
            }
          })
          deletedAssignees.length > 0 && deletedOperations[projectId].push({ typeName: 'delete_task_assignee', conditions: `{task_id: {_eq: "${taskId}"}, user_id: {_in: [${deletedAssignees.map(id => `"${id}"`)}]}}` })
          deletedDocs.length > 0 && deletedOperations[projectId].push({
            typeName: 'delete_task_document_association',
            conditions: `{task_id: {_eq: "${taskId}"}, document_id: {_in:${JSON.stringify(deletedDocs)} }}`
          })
          deletedOperations[projectId].push({ typeName: 'delete_tag_task', conditions: `{task_id: {_eq: "${taskId}"}}` })
          // material task attachment has been pushed to deletedOperations array , where query update query will be generated automatically
          newTaskMaterialArray.length > 0 && deletedOperations[projectId].push({ typeName: 'update_task_material_association', conditions: (newTaskMaterialArray) })
          newDocArray?.length > 0 && insertOperations[projectId].push({ typeName: 'insert_task_document_association', conditions: (newDocArray) })
          newTagArray.length > 0 && insertOperations[projectId].push({ typeName: 'insert_tag_task', conditions: (newTagArray) })
          newAssigneeArray.length > 0 && insertOperations[projectId].push({ typeName: 'insert_task_assignee', conditions: (newAssigneeArray) })
        }
        for (const [key, value] of Object.entries(deletedOperations)) {
          updateApiArray.push(deleteChanagesInTimeLine(this.generateDynamicDeleteQuery(value), this.getProjectTokens[key]))
        }
        for (const [key, value] of Object.entries(updateObject)) {
          updateApiArray.push(UpdateTasksWithToken(value, this.getProjectTokens[key]))
        }
        if (Object.values(insertOperations)[0].length) {
          for (const [key, value] of Object.entries(insertOperations)) {
            insertApiArray.push(insertTasksData(this.generateDynamicInsertQuery(value), this.getProjectTokens[key]))
          }
        }
        await Promise.all(updateApiArray)
        await Promise.all(insertApiArray)
        this.getTimeLineData(date, viewMode)
        success('Timeline updated successfully ')
        this.loading = false
      } catch (error) {
        console.log(error)
        alert('Some thing went wrong')
        this.loading = false
      }
    },
    generateDynamicDeleteQuery (deleteOperations) {
      const mutations = deleteOperations.map(({ typeName, conditions }, index) => `
    ${typeName}_${index + 1}: ${typeName}(where: ${conditions}) {
      affected_rows
    }
  `)
      return `
    mutation deleteChangesTimeLineQuery {
      ${mutations.join('\n')}
    }
  `
    },
    generateDynamicInsertQuery (insertOperation) {
      const mutations = insertOperation.map(({ typeName, conditions }, index) => `
    ${typeName}_${index + 1}: ${typeName}(objects: [${conditions.map(obj => `{${Object.entries(obj).map(([key, value]) => `${key}: ${JSON.stringify(value)}`).join(',')}}`).join(',') }]) {
      affected_rows
    }
  `)

      return `
    mutation insertChangesTimeLineQuery {
      ${mutations.join('\n')}
    }
  `
    },
    getCalenderData (projectids) {
      getCalenderDataByids(projectids).then((res) => {
        for (const project of res.core_project_calendar) {
          const days = []
          const holidays = []
          for (const workDays of project.calendar_working_days) {
            days.push(workDays.work_day)
          }
          for (const holiday of project.calendar_holidays) {
            holidays.push(new Date(holiday.date))
          }
          this.addCalenderHashMap({
            calenderData: {
              project_id: project.project_id,
              working_hours: project.working_hours,
              workDays: days,
              holidays: holidays
            },
            projectId: project.project_id
          })
        }
      })
    }

  },
  watch: {
    selectedProjIds () {
      debounce(() => {
        if (this.$props.view === 'resources') {
          this.getResourceList()
        } else {
          this.getUsersList()
        }
      }, 500, this)()
    },
    view () {
      this.appliedFilters = {
        resourceIds: [],
        projectIds: [],
        userIds: [],
        tags: [],
        from: '',
        upto: ''
      }
      this.setup()
    }
  }
}
</script>

<style>
/* Add custom styles if needed */
</style>

<style lang="scss" scoped >
@import "../../assets/scheduler/dhtmlxscheduler.css";
.timeLine {
  &-filters {
    margin: 4px;
    margin-top: 1rem;
    padding: 10px;
    border: var(--border);
    position: sticky;
    top: 0px;
    z-index: 3;
    background-color: white;
  }
}
.loading-container {
  min-height: calc(100vh - 170px);
}
.sheduler-container{
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px);
}
</style>
