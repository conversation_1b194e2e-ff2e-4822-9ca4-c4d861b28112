<template>
  <div :class="{
    'form-input': true,
    'form-input--required': data.required
    }" >
      <label>{{data.caption}}:
        <img
      v-if="data.visibility"
      class="mx-1"
      width="15"
      src="~@/assets/images/eye.svg"
      alt="view"
      />
      </label>
      <input :disabled="viewOnly" v-model="tempValue" type="time" @change="emitChange"/>
  </div>
</template>

<script>
export default {
  name: 'timeComponent',
  props: {
    mode: {
      type: String,
      default: 'TEMPLATE'
    },
    data: {
      type: Object,
      default: () => {}
    },
    value: {
      type: String,
      default: ''
    },
    viewOnly: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      componentValue: '',
      tempValue: ''
    }
  },
  watch: {
    value (val) {
      this.componentValue = val
      this.componentValue = this.value ? this.value : '00:00:00.00000+00:00'
      this.tempValue = this.componentValue.split(':').slice(0, 2).join(':')
    },
    tempValue (val) {
      this.componentValue = val + ':00.00000+00:00'
    }
  },
  created () {
    this.componentValue = this.value ? this.value : '00:00:00.00000+00:00'
    this.tempValue = this.componentValue.split(':').slice(0, 2).join(':')
  },
  methods: {
    emitChange () {
      this.$emit('customChange', {
        field_id: this.$props.data.field_id,
        value: this.componentValue
      })
    }
  }
}
</script>

<style lang="scss" scoped >

</style>
