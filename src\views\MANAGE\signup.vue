<template>
  <section class="login text v-center column p-5">
    <h1>Signup User</h1>
    <p class="py-5" >To your account to access your account</p>
    <div class="login-group bg py-5 px-20 v-center column">
      <img class="my-10" src="~@/assets/images/brand-transparent.png" alt="">
      <div class="input-group imp m my-4" data-validation="Password">
        <label>Enter your Password</label>
        <input v-model="password" placeholder="Enter your Password" type="password">
      </div>
      <div class="input-group imp m my-4" data-validation="ConfirmPassword">
        <label>Confirm your Password</label>
        <input v-model="confirmPassword" placeholder="Enter your Password" type="password">
      </div>
      <div class="flex-end my-4 l">
        <button @click="trySignup" class="btn" >LogIn</button>
      </div>
    </div>
  </section>
</template>

<script>
import signupValidation from '@/helper/formValidation/signupValidation.js'
import { SignupUser } from '@/api/session.js'
import { alert, success } from '@/plugins/notification'

export default {
  name: 'SignupPage',
  data () {
    return {
      password: '',
      confirmPassword: ''
    }
  },
  methods: {
    trySignup () {
      const body = {
        password: this.password,
        confirmPassword: this.confirmPassword
      }
      if (signupValidation(body)) {
        delete body.confirmPassword
        SignupUser(this.password, this.$route.query.token).then(data => {
          if (data.message === 'Signup Successful') {
            success('You signedUp successfully.')
            this.$router.push('/login')
          } else {
            alert('There was an error, signing up.')
          }
        }).catch(() => {
          alert('There was an error, signing up.')
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped >
.login {
  h1 {
    font-size: 36px;
    font-weight: 500;
  }
  &-group {
    max-width: 700px;
    width: 100%;
    border-radius: 4px;
    min-height: calc(100vh - 300px);
    & > div {
      width: 100%;
      button {
        width: 140px;
      }
    }
  }
}
</style>
