import { runQuery, runMutation } from '../graphQl'
import Config from '../../../src/config'
import store from '../../store'

import {
  GetAllProjects,
  CreateProject,
  DeleteProject,
  UpdateProject,
  GetCurrentProject,
  AddUserToProject,
  DeleteUserFromProject,
  RestoreUserFormProject,
  UpdateUserRoleForProject,
  GetProjectLevelRole,
  GetAllProjectsForTsQuery,
  GetAssociatedUsersListQuery,
  tenantUserAssociation,
  GetUserListByPojIdsQuery
} from '../query/project'

export const GetAllProjectsList = (sorting, selectedTab, search) => {
  const conditions = { deleted: { _eq: false } }
  let orderBy = { created_on: 'desc' }
  if (sorting === '2') {
    orderBy = { created_on: 'asc' }
  } else orderBy = { created_on: 'desc' }
  if (selectedTab) {
    conditions.active = {}
    if (selectedTab === 'active') {
      conditions.active = { _eq: true }
    } else if (selectedTab === 'inactive') conditions.active = { _eq: false }
  }
  const collaboratorId = store.getters?.collaboratorId
  if (search?.length) {
    conditions.name = { _ilike: `%${search}%` }
  }
  if (store.getters?.collaborator) {
    const userId = store.getters?.user?.userId
    if (window.location.pathname.includes('/document-view')) {
      conditions.collaborator_documents = {
        source_tenant_id:
        { _eq: collaboratorId },
        target_tenant_user_id: { _eq: userId },
        core_document: {
          deleted: {
            _eq: false
          }
        }
      }
    } else if (window.location.pathname.includes('/bom')) {
      conditions.bom_projects = { collaborator_boms: { source_tenant_id: { _eq: collaboratorId } } }
    } else if (window.location.pathname.includes('/project-planner')) {
      conditions.core_tasks = { task_assignees: { user_id: { _eq: userId } }, tenant_id: { _eq: collaboratorId } }
    } else if (window.location.pathname.includes('/form')) {
      const userId = store.getters?.user?.userId
      conditions.core_forms = {
        forms_user_list:
        { user_id: { _eq: userId } },
        tenant_id: { _eq: collaboratorId }
      }
    } else if (window.location.pathname.includes('/insights')) {
      const userId = store.getters?.user?.userId
      conditions._or = [
        {
          core_forms: {
            forms_user_list:
          { user_id: { _eq: userId } },
            tenant_id: { _eq: collaboratorId }
          }
        },
        {
          task_assignees:
          {
            user_id:
             { _eq: userId }
          },
          tenant_id:
              { _eq: collaboratorId }
        }
      ]
    }
  }
  return runQuery(GetAllProjects(), { conditions, orderBy }, 'tenant')
}

export const CreateNewProject = (data) => {
  return runMutation(CreateProject(), { data }, 'tenant')
}

export const UpdateProjectById = ({ id, data }) => {
  return runMutation(UpdateProject(), { id, data }, 'tenant')
}

export const DeleteProjectById = (variables) => {
  return runMutation(DeleteProject(), variables, 'tenant')
}

export const GetCurrentProjectData = () => {
  return runQuery(GetCurrentProject(), null, 'project')
}

export const AddUserToProjectData = (variables) => {
  return runMutation(AddUserToProject(), variables, 'project')
}

export const DeleteUserFromProjectData = (variables) => {
  return runMutation(DeleteUserFromProject(), variables, 'project')
}

export const RestoreUserFormProjectData = (variables) => {
  return runMutation(RestoreUserFormProject(), variables, 'project')
}

export const UpdateUserRoleForProjectData = (variables) => {
  return runMutation(UpdateUserRoleForProject(), variables, 'project')
}

export const GetProjectUserAssociation = () => {
  return runQuery(GetProjectLevelRole(), null, 'project')
}
export const GetAllProjectsForTs = () => {
  return runQuery(GetAllProjectsForTsQuery(), null, 'tenant')
}
export const GetAssociatedUsersList = (projectId) => {
  return runQuery(GetAssociatedUsersListQuery(), { projectId }, 'tenant')
}
export const GetUserListByPojIds = (projectIds, status = [1]) => {
  let conditions = {}
  if (projectIds.length > 0) {
    conditions = { _and: { associated_project: { id: { _in: projectIds } }, status: { _in: status } } }
  } else {
    conditions = { _and: { status: { _in: status } } }
  }
  return runQuery(GetUserListByPojIdsQuery(), { conditions }, 'tenant')
}
export const getProjectExchangeToken = async (projectId) => {
  return new Promise((resolve, reject) => {
    const tenantToken = localStorage.getItem(Config.localstorageKeys.TENANT)
    return fetch(Config.serverEndpoint + '/user/tokenExchange', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + tenantToken
      },
      body: JSON.stringify({ projectId: projectId })
    }).then(
      e => e.json()
    ).then(response => {
      resolve(response)
    })
  })
}
export const tenantUsers = (id) => {
  return runQuery(tenantUserAssociation(), { id }, 'project')
}
