<template>
  <div class="tenant-settings-list">
    <div class="tenant-settings-list__header s p-2 v-center space-between">
      <h2 class="weight-500 l">Tenant Settings</h2>
    </div>
    <RouterLink to="/settings/custom-list">
      <div class="tenant-settings-item v-center">
        <div class="tenant-settings-item__name v-center s elipsis-text">
          <span>Custom List</span>
        </div>
        <div class="toggle-button center">
          <img src="~@/assets/images/down-arrow-icon.svg" alt="" />
        </div>
      </div>
    </RouterLink>
    <RouterLink to="/settings/copy-storage-locations">
      <div class="tenant-settings-item v-center">
        <div class="tenant-settings-item__name v-center s elipsis-text">
          <span>Storage Locations</span>
        </div>
        <div class="toggle-button center">
          <img src="~@/assets/images/down-arrow-icon.svg" alt="" />
        </div>
      </div>
    </RouterLink>

    <RouterLink to="/settings/tags">
      <div class="tenant-settings-item v-center">
        <div class="tenant-settings-item__name v-center s elipsis-text">
          <span>Tags</span>
        </div>
        <div class="toggle-button center">
          <img src="~@/assets/images/down-arrow-icon.svg" alt="" />
        </div>
      </div>
    </RouterLink>
    <RouterLink to="/settings/tenant-calendar" v-if="!isOnProjectLevel">
      <div class="tenant-settings-item v-center">
        <div class="tenant-settings-item__name v-center s elipsis-text">
          <span>Tenant Calendar</span>
        </div>
        <div class="toggle-button center">
          <img src="~@/assets/images/down-arrow-icon.svg" alt="" />
        </div>
      </div>
    </RouterLink>
    <RouterLink to="/settings/calendar" v-if="isOnProjectLevel">
      <div class="tenant-settings-item v-center">
        <div class="tenant-settings-item__name v-center s elipsis-text">
          <span>Calendar</span>
        </div>
        <div class="toggle-button center">
          <img src="~@/assets/images/down-arrow-icon.svg" alt="" />
        </div>
      </div>
    </RouterLink>

    <RouterLink to="/settings/copy-forms">
      <div class="tenant-settings-item v-center">
        <div class="tenant-settings-item__name v-center s elipsis-text">
          <span>Forms</span>
        </div>
        <div class="toggle-button center">
          <img src="~@/assets/images/down-arrow-icon.svg" alt="" />
        </div>
      </div>
    </RouterLink>
    <RouterLink to="/settings/user-groups">
      <div class="tenant-settings-item v-center">
        <div class="tenant-settings-item__name v-center s elipsis-text">
          <span>User Groups</span>
        </div>
        <div class="toggle-button center">
          <img src="~@/assets/images/down-arrow-icon.svg" alt="" />
        </div>
      </div>
    </RouterLink>
    <RouterLink to="/settings/copy-workflows/1">
      <div class="tenant-settings-item v-center">
        <div class="tenant-settings-item__name v-center s elipsis-text">
          <span>Workflow</span>
        </div>
        <div class="toggle-button center">
          <img src="~@/assets/images/down-arrow-icon.svg" alt="" />
        </div>
      </div>
    </RouterLink>
    <RouterLink to="/settings/sequence-generator">
      <div class="tenant-settings-item v-center">
        <div class="tenant-settings-item__name v-center s elipsis-text">
          <span>Sequence Generation</span>
        </div>
        <div class="toggle-button center">
          <img src="~@/assets/images/down-arrow-icon.svg" alt="" />
        </div>
      </div>
    </RouterLink>
    <RouterLink to="/settings/config-feature">
      <div class="tenant-settings-item v-center">
        <div class="tenant-settings-item__name v-center s elipsis-text">
          <span>Tenant Feature Configration</span>
        </div>
        <div class="toggle-button center">
          <img src="~@/assets/images/down-arrow-icon.svg" alt="" />
        </div>
      </div>
    </RouterLink>
  </div>
</template>

<script>
import { RouterLink } from 'vue-router'
import { mapGetters } from 'vuex'
export default {
  name: 'TenantSettingsList',
  components: { RouterLink },
  computed: {
    ...mapGetters(['isOnProjectLevel'])
  }
}
</script>

<style lang="scss" scoped >
.tenant-settings-list {
  width: 20%;
  height: 100%;
  overflow: auto;
  background-color: var(--bg-color);
  border-right: 1px solid var(--brand-color);
  position: relative;
  margin-top: -10px;
  a {
    text-decoration: none;
    color: var(--text-color);
  }
  &__header {
    position: sticky;
    top: 0;
    background-color: var(--brand-color);
  }
}
.tenant-settings-item {
  border: 1px solid var(--brand-color);
  background-color: rgba(var(--brand-rgb), 0.2);
  margin: 6px 6px;
  padding-left: 6px;
  cursor: pointer;
  &__name {
    width: calc(100% - 30px);
    height: 30px;
  }
  .selected {
    background-color: rgba(var(--brand-rgb), 0.6);
  }
  .obsolete {
    border-color: var(--alert);
    color: var(--alert);
  }
  & .toggle-button {
    height: 30px;
    width: 30px;
    background-color: rgba(var(--brand-rgb), 1);
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.5;
    & img {
      height: 16px;
      width: 16px;
      transform: rotate(-90deg);
    }
  }
}
.router-link-active {
  .tenant-settings-item {
    background-color: rgba(var(--brand-rgb), 0.6);
  }
  & .toggle-button {
    opacity: 1;
    pointer-events: all;
  }
}
</style>
