<template>
  <div>
    <div class="container">
      <div class="grid-2" ref="addUserBody">
        <div class="input-group mt-2">
           <label class="key">AttachBom:</label>
           <div
           class="selected-bom"
           v-if="attachedBom"
           >
            <div class="elipsis-text bom-name" v-overflow-tooltip>
    {{attachedBom.name}}
            </div>
            <div class="flex">
              <img class="pointer"
              @click="deselectBom"
              src="~@/assets/images/icons/close-icon.svg"
              width="16px"/>
            </div>
           </div>
           <div v-else class="dropdown-wrapper">
             <custom-dropdown :list="bomList" :selected="attachedBom" @select="handleBomSelection"/>
           </div>
         </div>
         <div class="input-group mt-2">
          <label class="key">Quantity:</label>
<input class="selected-bom" type="number" v-model="quantity" @keydown="changeNumber"/>
         </div>
          </div>
    </div>
    <div class="gap-1 flex-end">
      <button class="btn btn-black" @click="$emit('close')">Cancel</button>
      <button class="btn" @click="SubmitBomTask" v-if="showAddTask">Save</button>
    </div>
  </div>
</template>

<script>
import { GetAllProjectBomList, GetAllTaskBom } from '@/api'
import { alert } from '../../plugins/notification'
import { mapGetters } from 'vuex'
import CustomDropdown from '../common/customDropdown.vue'
import { restrictKeys } from '@/utils/validations'

export default {
  name: 'AddBomToTaskBody',
  props: {
    task: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    CustomDropdown
  },
  data () {
    return {
      openPopup: false,
      taskAssociatedBom: null,
      taskAssociatedId: null,
      selectedBom: [],
      bomList: [],
      searchKeyword: '',
      selectedBomId: null,
      quantity: 0,
      previouslyAttachedBom: null,
      attachedBom: null
    }
  },
  mounted () {
    GetAllTaskBom(this.task?.id).then((res) => {
      if (res.task_material_association.length) {
        this.taskAssociatedBom = { id: res.task_material_association[0]?.target_bom?.id, quantity: res.task_material_association[0]?.metadata?.quantity }
        this.taskAssociatedId = res.task_material_association[0]?.id
        this.selectedBom.push(res.task_material_association[0]?.target_bom)
        this.quantity = res.task_material_association[0]?.metadata?.quantity
        this.previouslyAttachedBom = { id: res.task_material_association[0]?.target_bom?.id, quantity: res.task_material_association[0]?.metadata?.quantity, associationId: res.task_material_association[0]?.id }
        this.attachedBom = res.task_material_association[0]?.target_bom
      }
    })
    GetAllProjectBomList(true)
      .then((res) => {
        this.bomList = res.core_bom.filter(bom => bom.id !== this.taskAssociatedBom)
      })
      .catch((err) => {
        console.log(err)
        alert(`${err.message}`)
      })
  },
  computed: {
    ...mapGetters(['user']),
    checkDisabled () {
      if (this.selectedBom.length) {
        return true
      }
      return false
    },
    showAddTask () {
      return (
        (this.user.projectLevelRole === 'ADMIN' ||
          this.user.projectLevelRole === 'EDITOR' ||
          this.user.projectLevelRole === 'COLLABORATOR')
      )
    }
  },
  methods: {
    handleBomSelection (bom) {
      console.log(bom)
      this.attachedBom = bom
      this.quantity = 1
    },
    changeNumber (event) {
      if (['e', 'E', '+', '-'].includes(event.key)) {
        restrictKeys(event)
      }
    },
    closeDropdowm () {
      this.openPopup = false
    },
    SubmitBomTask () {
      let updatesArray = []
      const currentlyAttachedBom = this.attachedBom ?? {}
      if (parseInt(this.quantity)) {
        currentlyAttachedBom.quantity = parseInt(this.quantity)
      }
      if (this.previouslyAttachedBom && currentlyAttachedBom?.id) {
        if (this.previouslyAttachedBom?.id !== currentlyAttachedBom?.id) {
          this.previouslyAttachedBom.tag = 'deleted'
          currentlyAttachedBom.tag = 'new'
          updatesArray = [this.previouslyAttachedBom, currentlyAttachedBom]
        } else {
          if (this.previouslyAttachedBom.quantity === currentlyAttachedBom.quantity) {
            updatesArray = null
          } else {
            currentlyAttachedBom.tag = 'updated'
            currentlyAttachedBom.associationId = this.previouslyAttachedBom.associationId
            updatesArray = [currentlyAttachedBom]
          }
        }
      } else if (!this.previouslyAttachedBom && currentlyAttachedBom.id) {
        currentlyAttachedBom.tag = 'new'
        updatesArray = [currentlyAttachedBom]
      } else if (this.previouslyAttachedBom && !currentlyAttachedBom.id) {
        this.previouslyAttachedBom.tag = 'deleted'
        updatesArray = [this.previouslyAttachedBom]
      }
      this.$emit('bom-update-and-close', { updatesArray })
    },
    updateSelectedBom (bomName) {
      if (this.selectedBom.length < 1) {
        this.selectedBom.push(bomName)
        this.openPopup = false
      } else {
        alert('Only one bom can be attached per task')
        this.openPopup = false
      }
    },
    removeBomFromTask (index, bom) {
      this.selectedBom.pop()
    },
    deselectBom () {
      this.attachedBom = null
      this.quantity = 0
    },
    addSeletedBom () {
      this.selectedBom = this.bomList.filter(obj => obj.id === this.selectedBomId)
      this.quantity = 1
    }
  }
}

</script>

<style lang="scss" scoped>
.bom-name {
  max-width: 8rem;
}

.dropdown-wrapper {
  font-size: 8.4px;
  color: black;
}
.container {
  padding: 17px 17px 17px 17px;
  width: 400px;
  height: 100px;
}
.deselect-bom {
  min-width: fit-content;
}
.selected-bom {
    border: 1px solid rgba(59, 59, 59, 0.4666666667);
    width: 100%;
    border-radius: 0.285em;
    font-size: 1em;
    padding: 0.17em;
    padding-inline: .5em;
    display: flex;
    justify-content: space-between;
}
.input-group{
  padding: .3em;
  & label {
    margin-bottom: 0;
  }
  & input{
padding: .3em;
padding-inline: .5em;
  }
  & select{
    padding: .3em;
    padding-inline: .5em;
  }
}

</style>
