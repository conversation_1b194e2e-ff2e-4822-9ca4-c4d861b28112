<template>
    <div class="project-list">
      <div class="project-list-nav v-center space-between" >
        <h2 class="weight-500 xxxl v-center heading">
          Projects
        </h2>
        <div class="v-center add-btn">
          <button v-if="isTenantAdminOrCollaborator" @click="openDrawer" class="btn">
            + Add Project
          </button>
        </div>
      </div>
      <div class="filter-container v-center">
        <div class="toggle-btn">
          <button :class="{ active: selectedTab === 'active' }" @click="selectedTab = 'active'" class="toggle-option">Active</button>
          <button :class="{ active: selectedTab === 'inactive' }" @click="selectedTab = 'inactive'" class="toggle-option">Inactive</button>
        </div>
        <div class="filter-right">
          <div>
            <label for="">Sort By</label>
            <select v-model="selectedSort" class="sort" name="" id="">
              <option value="1">Latest</option>
              <option value="2">Oldest</option>
            </select>
          </div>
          <div class="input-group search ml-4">
            <input
            class="input-search"
            v-model="searchKeyword"
            @input="updateOnSearch"
            type="text"
            placeholder="Search"
            />
          </div>
        </div>
      </div>
      <div class="project-list-container my-5">
        <copy-project-table
          :projectList="getSearchResult"
          :perPage="perPage"
          :pageNumber="pageNumber"
          :showHeader="true"
          :loading="loading"
          :collaborator="collaborator"
          @delete="deleteProject"
          @edit="editProject"
        ></copy-project-table>
        <div class="v-center space-between mt-3">
        <pagination2
          v-if="getSearchResult.length > perPage"
          :length="getSearchResult.length"
          :perPage="perPage"
          :pageNumber="pageNumber"
          @selectPage="selectPage"
          class="mt-3"
        />
        <span class="mt-5">Total Projects : &nbsp; <b class="mr-5"> {{ totalcount }}</b></span>
        </div>
      </div>
      <div v-if="showProjectAction" class="drawer" :class="drawer ? 'open' : 'close'">
        <div class="project-form-container">
          <project-form
            ref="projectForm"
            :isUpdate="openId !== null"
            :drawer="drawer"
            @cancel="closeDrawer"
            @save="saveProject"
            @update="updateProject"
            :closeOnOutsideClick="true"
            :buttonDisabled="buttonDisabled"
          ></project-form>
        </div>
      </div>
    </div>
  </template>
<script>
import {
  GetAllProjectsList,
  CreateNewProject,
  DeleteProjectById,
  UpdateProjectById,
  AddNewTask
} from '@/api'
import copyProjectTable from '../../components/common/copyProjectTable.vue'
import ProjectForm from '@/components/manage/projectForm.vue'
import { arraySearch } from '@/utils/array'
// import Pagination from '../../components/common/pagination.vue'
import Pagination2 from '../../components/common/pagination2.vue'
import { mapGetters } from 'vuex'
import { projectExchangeToken } from '../../api/session'
import { GetTaskStatuses } from '../../api/apis/projectPlanner'
import config from '../../config'
import http from '../../api/http'
import { alert } from '@/plugins/notification'
import { debounce } from '@/utils/debounce'

export default {
  name: 'Projects',
  components: { copyProjectTable, ProjectForm, Pagination2 },
  data () {
    return {
      length: 50,
      first: 1,
      second: 10,
      selectedTab: 'active',
      selectedSort: '1',
      drawer: false,
      openId: null,
      searchKeyword: '',
      projectList: [],
      pageNumber: 1,
      perPage: 10,
      loading: false,
      status: '',
      buttonDisabled: false, // this for disabling update button  of project updation
      updateOnSearch: null,
      totalcount: 0

    }
  },
  computed: {
    ...mapGetters(['user', 'getUserById', 'collaborator']),
    getSearchResult () {
      return arraySearch(this.projectList, this.searchKeyword, { fieldsToSearch: ['name'] })
    },
    isTenantAdminOrCollaborator () {
      return (
        this.user.tenantLevelRole === 'ADMIN' && !this.collaborator)
    },
    showProjectAction () {
      return (
        this.user.tenantLevelRole === 'ADMIN' ||
            this.user.tenantLevelRole === 'EDITOR' ||
            this.user.tenantLevelRole === 'COLLABORATOR'
      )
    }
  },
  watch: {
    selectedSort (value) {
      if (value === '0') return
      this.getAllProjectsList()
    },
    selectedTab () {
      this.getAllProjectsList()
    }
  },
  methods: {
    selectPage (pageNumber) {
      this.pageNumber = pageNumber
    },
    openDrawer () {
      this.drawer = true
    },
    closeDrawer () {
      this.$refs.projectForm.resetForm()
      this.openId = null
      this.drawer = false
    },
    editProject (obj) {
      this.openId = obj.id
      this.$refs.projectForm.name = obj.name
      this.$refs.projectForm.latitude = obj.latitude
      this.$refs.projectForm.longitude = obj.longitude
      this.$refs.projectForm.selectedTenant = obj.associated_tenant?.id
      this.$refs.projectForm.startDate = obj.planned_start_date
      this.$refs.projectForm.endDate = obj.planned_end_date
      this.$refs.projectForm.cost = obj.project_cost
      this.$refs.projectForm.revenue = obj.project_revenue
      this.$refs.projectForm.address = obj.address?.address
      this.$refs.projectForm.state = obj.address?.state
      this.$refs.projectForm.city = obj.address?.city
      this.$refs.projectForm.pincode = obj.address?.pincode
      this.openDrawer()
    },
    saveProject (obj) {
      this.buttonDisabled = true
      const data = {
        name: obj.name.trim(),
        planned_start_date: obj.planned_start_date,
        planned_end_date: obj.planned_end_date,
        project_cost: obj.project_cost,
        project_revenue: obj.project_revenue,
        address: obj.address,
        location:
            obj.latitude && obj.longitude
              ? `(${obj.latitude}, ${obj.longitude})`
              : null
      }
      CreateNewProject(data).then((res) => {
        this.getAllProjectsList()
        projectExchangeToken(null, res.insert_core_projects_one.id).then(() => {
          if (obj.copyProjectPlan) {
            const copyProjectId = obj.copyProjectPlan
            http.POST(config.serverEndpoint + '/project/copy-project-plan', {
              copyProjectId,
              assignees: obj.assignees,
              boms: obj.boms,
              bomTaskAssoc: obj.bomTaskAssoc,
              docTaskAssoc: obj.docTaskAssoc,
              documents: obj.documents
            }, 'project').then((res) => {
              console.log(res)
              this.$notify.success('Copy Project Plan Successful.')
              this.closeDrawer()
              this.buttonDisabled = false
            }).catch(() => {
              this.$notify.alert('Failed to copy project plan')
              this.buttonDisabled = false
            })
          } else {
            AddNewTask({
              name: data.name,
              planned_end_date: data.planned_end_date,
              planned_start_date: data.planned_start_date,
              type: 2,
              level: 0,
              status: this.status,
              order_index: '1'
            }).then(() => {
              this.getAllProjectsList()
              this.$notify.success('Project Created Successfully')
              this.closeDrawer()
              this.buttonDisabled = false
            }).catch(() => {
              this.$notify.alert('Failed to create project task')
              this.buttonDisabled = false
            })
          }
        })
      }).catch((err) => {
        this.buttonDisabled = false
        if (err?.message?.includes('Uniqueness violation')) {
          return this.$notify.alert('Project name already exists')
        }
        this.$notify.alert(err?.message ?? 'Something went wrong')
      })
    },
    deleteProject (obj) {
      DeleteProjectById({ id: obj.id }).then((res) => {
        this.getAllProjectsList()
        this.$notify.success('Project Deleted Successfully')
      })
    },
    updateProject (obj) {
      this.buttonDisabled = true
      const data = {
        name: obj.name?.trim(),
        planned_start_date: obj.planned_start_date,
        planned_end_date: obj.planned_end_date,
        project_cost: obj.project_cost,
        project_revenue: obj.project_revenue,
        address: obj.address,
        location:
            obj.latitude && obj.longitude
              ? `(${obj.latitude}, ${obj.longitude})`
              : null
      }
      UpdateProjectById({ id: this.openId, data }).then((res) => {
        this.getAllProjectsList()
        this.closeDrawer()
        this.$notify.success('Project Updated Successfully')
        this.buttonDisabled = false
      }).catch(err => {
        this.buttonDisabled = false
        if (err?.message?.includes('Uniqueness violation')) {
          return this.$notify.alert('Project name already exists')
        }
        this.$notify.alert(err?.message ?? 'Something went wrong')
      })
    },
    getAllProjectsList () {
      this.loading = true
      GetAllProjectsList(this.selectedSort, this.selectedTab, this.searchKeyword)
        .then((res) => {
          //  the same api call is used to fetching project data in vuex project list as well as project list in project module , no need to get update  project list in vuex if there is a search keyword , which is comming from Project lsit from project module
          if (!this.searchKeyword) {
            this.$store.commit('setTenantProjectList', res?.core_projects)
          }
          this.loading = false
          this.totalcount = res.core_projects_aggregate.aggregate.count
          this.projectList = res.core_projects?.map((item) => {
            const location = item.location
              ? item.location.replace('(', '').replace(')', '').split(',')
              : []
            return {
              ...item,
              latitude: location[0] || '',
              longitude: location[1] || ''
            }
          })
        })
        .catch((error) => {
          console.log(error)
          alert('Something went wrong')
          this.loading = false
        })
    },
    taskStatus () {
      GetTaskStatuses().then((res) => {
        this.tasks = res.custom_list_values.map((item) => {
          return {
            label: item.name,
            id: item.id
          }
        })
        const todoId = this.tasks.find((task) => task.label === 'TO DO')?.id
        this.status = todoId
      })
    }
  },
  mounted () {
    this.taskStatus()
  },
  created () {
    this.updateOnSearch = debounce(() => {
      this.pageNumber = 1
      this.getAllProjectsList()
    }, 500)
    this.getAllProjectsList()
  }
}
</script>
  <style lang="scss" scoped>
.project-list{
    height: 70%;
    margin: 0;
    padding: 8px 5px 0;
    .filter-container{
      // background-color: green;
      margin-top: 1em;
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 2rem;
      // background-color: purple;
      // margin-top: 1em;
      // display: flex;
      // justify-content: flex-end;
      // gap: auto;
      // align-items: center;
.toggle-btn {
  height: 2.2em;
  display: inline-flex;
  border: 1px solid #ccc;
  border-radius: 6px;
  background-color: #e0e0e0;
  align-items: center;
  overflow: hidden;
  position: relative;
}

.toggle-option {
  flex: 1;
  padding: 6px 14px;
  border: none;
  background-color: #e0e0e0;
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  position: relative;
  z-index: 1;
}

.toggle-option.active {
  background-color: white;
  color: black;
  border: 2px solid #bbb;
  transition: all 0.15s ease-in-out;
  border-radius: 6px;
  z-index: 2;
}

.toggle-option:first-child.active {
  box-shadow: 4px 0 6px -2px rgba(0, 0, 0, 0.4);
}

.toggle-option:last-child.active {
  box-shadow: -4px 0 6px -2px rgba(0, 0, 0, 0.4)
}
.filter-right {
  display: flex;
  align-items: center;
  gap: 10px; /* Adjust spacing */
}
    .sort {
    height: 2em;
    width: 10rem;
    margin: 0 0 0 10px;
    // margin-left: 2px;
    font-size: 14px;
    background-color: var(--brand-light-color);
    line-height: 1;
    border: 1px solid var(--brand-color);
    border-radius: 4px;
    padding: 4px 12px;
    }
  .input-search {
    height: fit-content;
  }
  }
.project-form-container {
  height: 100%;
  }
  .drawer {
    top:60px;
    overflow-y: auto;
  }
  @media screen and (max-width: 1366px) {
  .project-list-nav h2 {
    font-size: 22px;
  }
  .add-btn .btn {
    font-size: 14px;
    padding: 6px 12px;
  }
}

@media screen and (max-width: 1200px) {
  .project-list-nav h2 {
    font-size: 18px;
  }
  .add-btn .btn {
    font-size: 13px;
    padding: 5px 10px;
  }
}

@media screen and (max-width: 1024px) {
  .project-list-nav h2 {
    font-size: 16px;
  }
  .add-btn .btn {
    font-size: 12px;
    padding: 4px 8px;
  }
}
}
  </style>
