import { alert } from '@/plugins/notification'
export const formValidationCheck = (elements, valueMap = {}) => {
  for (let i = 0; i < elements.length; i++) {
    const element = elements[i]
    const required = element.required
    const formField = element.form_field
    const caption = element.caption
    const fieldId = element.field_id
    if (required) {
      const value = valueMap[fieldId]
      switch (element.field_type_id) {
      case 1: // Text
        if (!value) {
          alert(`Please enter ${caption || formField}`)
          return false
        }
        break
      case 2: // Long Text
        if (!value) {
          alert(`Please enter ${caption || formField}`)
          return false
        }
        break
      case 3: // Number
        if (value === null || value === undefined || value === '' || isNaN(value)) {
          alert(`Please enter ${caption || formField}`)
          return false
        }
        break
      case 7: // Date
        if (!value) {
          alert(`Please enter ${caption || formField}`)
          return false
        }
        break
      case 8: // Time
        if (!value) {
          alert(`Please enter ${caption || formField}`)
          return false
        }
        break
      case 12: // Boolean
        if (value === null || value === undefined || value === '') {
          alert(`Please enter ${caption || formField}`)
          return false
        }
        break
      }
    }
  }
  return true
}
