<template>
  <div class="alert-modal-container" v-show="open">
    <div class="alert-modal-box">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    closeOnOutsideClick: {
      type: Boolean,
      default: false
    },
    open: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    checkOutsideClick (e) {
      if (e.target.classList.contains('alert-modal-container')) {
        this.$emit('close')
      }
    },
    closeOnEscapeKey (event) {
      if (event.key === 'Escape' && this.isOpen) {
        this.$emit('close')
      }
    }
  },
  mounted () {
    document.addEventListener('keydown', this.closeOnEscapeKey)
    if (this.closeOnOutsideClick) {
      this.$el.addEventListener('mousedown', this.checkOutsideClick)
    }
  },
  beforeDestroy () {
    document.removeEventListener('keydown', this.closeOnEscapeKey)
    this.$el.remove('click', this.checkOutsideClick)
  }
}
</script>
<style lang="scss" scoped>
.alert-modal {
  &-container {
    animation: slide-in-fwd-center .9s alternate both;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 3;
    background-color: rgba(0, 0, 0, 0.2);
    //   backdrop-filter: blur(0px);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  &-box {
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
    border-radius: 10px;
  }
}

@keyframes slide-in-fwd-center {
  0% {
    transform: translateZ(-1400px);
    opacity: 0;
  }
  100% {
    transform: translateZ(0);
    opacity: 1;
  }
}
</style>
