<template>
  <div class="form-template fh">
    <div class="form-template-bar v-center space-between px-3">
      <h1 class="weight-500 xl">
        Form Template
      </h1>
      <div class="v-center">
        <div class="input-group search m mx-3">
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="Search by name"
          />
        </div>
        <button v-if="createNewTemplate" class="btn btn-black mx-1" @click="[workflowModal=true, getTemplatesForWFL()]">Link to workflows</button>
        <router-link to="/form-builder" class="btn btn-black"  v-if="user.tenantLevelRole === 'ADMIN'">
          <button v-if="createNewTemplate" class="btn btn-black">Create New Template</button>
        </router-link>
      </div>
    </div>
    <div class="form-template-container">
      <div class="fh center" v-if="loading">
        <loading-circle />
      </div>
      <div
        class="form-template-no-form fh center"
        v-if="!loading && filteredTemplates.length === 0"
      >
        No Form Templates
      </div>
      <div
        :class="{
          fh: true,
          'mt-2': true,
          'grid-2': selectedTemplate.open,
        }"
        style="align-items: flex-start"
        v-if="!loading && filteredTemplates.length"
      >
        <div class="form-template-group fh">
          <div class="form-template-group-header form-template-group-row">
            <div class="form-template-group-header-item" v-overflow-tooltip>
              Name
            </div>
            <div class="form-template-group-header-item" v-overflow-tooltip>
              Type
            </div>
            <div class="form-template-group-header-item" v-overflow-tooltip>
              Created By
            </div>
            <div class="form-template-group-header-item" v-overflow-tooltip>
              Created At
            </div>
            <div v-if="showFormAction" class="form-template-group-header-item" v-overflow-tooltip>
              Action
            </div>
          </div>
          <div
            v-for="item in filteredTemplates"
            :key="item.id"
          >
          <div class="form-template-group-item form-template-group-row">
            <div class="form-template-group-item-name" v-overflow-tooltip>
              {{ item.name }}
            </div>
            <div class="form-template-group-item-type" v-overflow-tooltip>
              {{ item.core_form_type.name }}
            </div>
            <div class="form-template-group-item-created-by" v-overflow-tooltip>
              {{ getFullName(item.template_created_by) }}
            </div>
            <div class="form-template-group-item-created-at" v-overflow-tooltip>
              {{ item.created_on | timeStampToDateTime }}
            </div>
            <div v-if="showFormAction" class="form-template-group-item-action" v-overflow-tooltip>
              <img
              v-if="user.tenantLevelRole === 'ADMIN'"
              width="24"
              v-tooltip="`Edit Template`"
              class="pointer"
              src="~@/assets/images/edit-icon.svg"
              @click="
                      $router.push(
                        `/form-editor/${item.id}`
                      )
              "
              alt="" />
              <img
              class="ml-2"
                width="24"
                v-tooltip="`Preview Template`"
                src="~@/assets/images/icons/preview-icon.svg"
                alt=""
                @click="openPreview(item.id)"
              />
            </div>
          </div>
          </div>
        </div>
        <div class="space-between">
          <Pagination2 :length="totalcount" :pageNumber="pageNumber" :perPage="perPage" @selectPage="selectPage" class="mt-3 mx-2"/>
          <span class="mt-5">Total form templates : &nbsp; <b class="mr-5"> {{ totalcount }}</b></span>
        </div>
        <div class="form-template-preview fh" v-if="selectedTemplate.open">
          <div class="form-template-preview-header v-center space-between">
            <h3>Preview</h3>
            <div class="v-center">
              <select @change="changeVersion" v-model="selectedTemplate.version">
              <option
                v-for="version in selectedTemplate.data"
                :key="version.id"
                :value="version.id"
              >
                V-{{ version.version_id }} ({{
                  version.active ? "Latest" : ""
                }})
              </option>
            </select>
            <img class="ml-4" @click="closePreview" width="18px" src="~@/assets/images/close.png" alt="">
            </div>
          </div>
          <div class="form-template-preview-body">
            <form-preview
              :formName="selectedTemplate.name"
              :formTemplateBody="getSelectedVersionItems"
              :formType="selectedTemplate.type"
            />
          </div>
        </div>
      </div>
    </div>
    <modal
    :open="workflowModal"
      @close="workflowModal = false"
      :closeOnOutsideClick="true"
      title="Link to Workflows"
    >
    <div v-if="loadingForWFL" class="fh center">
        <loading-circle />
      </div>
      <div v-else-if="!loadingForWFL && filteredTemplatesfForWFL.length" class="form-template-workflowlink-container">
        <div class="form-template-workflowlink-body">
        <input type="text" class="input-group search m" v-model="searchKeywordForWFL" placeholder="Search by name" />
        <div class="grid-2 mt-2" v-for="item in filteredTemplatesfForWFL" :key="item.id" >
          <div class="" v-overflow-tooltip>{{ item.name }}</div>
          <select name="form-template-workflowlink-formName" id="" >
            <option value="" v-for="workflow in workflowTempaltes" :key="workflow.id">{{workflow.name}}</option>

          </select>
        </div>
      </div>
<div class=" form-template-workflowlink-buttonBox m flex flex-end p-2">
<button class="btn btn-black">Cancel</button>
<button class="btn">Save</button>
</div>
    </div>

    <div v-else class="fh center">
No Form Templates
    </div>
    </modal>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getFormTemplateswithPagination, getFormTemplates, getDetailFormTemplate, getAllWorkFLowTemplates } from '@/api'
import loadingCircle from '../../components/common/loadingCircle.vue'
import Modal from '@/components/common/modal.vue'
import { timeStampToDateTime } from '@/filters/dateFilter.js'
import FormPreview from '../../components/form/formPreview.vue'
import Config from '@/config'
import { alert } from '@/plugins/notification'
import Pagination2 from '@/components/common/pagination2.vue'
export default {
  components: { loadingCircle, FormPreview, Modal, Pagination2 },
  name: 'form-template',
  filters: {
    timeStampToDateTime
  },
  data: () => ({
    perPage: 10,
    pageNumber: 1,
    totalcount: 0,
    templates: [],
    loading: false,
    searchKeyword: '',
    selectedTemplate: {
      id: 0,
      open: false,
      loading: false,
      data: [],
      name: '',
      type: '',
      version: 0
    },
    Config: Config,
    workflowModal: false,
    templatesForWFL: [],
    loadingForWFL: false,
    searchKeywordForWFL: '',
    workflowTempaltes: []
  }),
  computed: {
    ...mapGetters(['user', 'getUserById']),
    filteredTemplates () {
      return this.templates.filter((item) =>
        item.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
      )
    },
    filteredTemplatesfForWFL () {
      return this.templatesForWFL
    },
    getSelectedVersionItems () {
      const version = this.selectedTemplate.data.find(
        (item) => item.id === this.selectedTemplate.version
      ) ?? { template_fields: [] }
      return version.template_fields
    },
    createNewTemplate () {
      return (
        this.user.tenantLevelRole === 'ADMIN' ||
          this.user.tenantLevelRole === 'COLLABORATOR'
      )
    },
    showFormAction () {
      return (
        this.user.tenantLevelRole === 'ADMIN' ||
          this.user.tenantLevelRole === 'COLLABORATOR' ||
          this.user.tenantLevelRole === 'EDITOR'
      )
    }
  },
  methods: {
    async getTemplates () {
      this.loading = true
      try {
        const res = await getFormTemplateswithPagination((this.pageNumber - 1) * this.perPage, this.perPage, true)
        this.templates = res.core_form_templates
        this.totalcount = res.core_form_templates_aggregate.aggregate.count
        console.log(res)
      } catch (err) {
        alert('Unable to fetch form templates')
      } finally {
        this.loading = false
      }
    },
    async getTemplatesForWFL () {
      try {
        this.loadingForWFL = true
        const res = await getFormTemplates(true)
        const res2 = await getAllWorkFLowTemplates(100, 1, '', true)
        this.workflowTempaltes = res2.workflow_templates
        // this arguement true is for to avoid standard Material form
        this.templatesForWFL = res.core_form_templates
        this.loadingForWFL = false
      } catch (error) {
        alert('Error fetching  form templates')
        console.error('Error fetching templates', error)
      }
    },
    closePreview () {
      this.selectedTemplate = {
        id: 0,
        open: false,
        loading: false,
        data: [],
        name: '',
        type: '',
        version: 0
      }
    },
    getFullName (user) {
      return `${user.first_name} ${user.last_name}`
    },
    openPreview (id) {
      this.selectedTemplate.id = id
      this.selectedTemplate.open = true
      getDetailFormTemplate(id).then((res) => {
        const template = res.core_form_templates_by_pk
        this.selectedTemplate.name = template.name
        this.selectedTemplate.type = template.core_form_type.name
        this.selectedTemplate.data = template.template_versions.map((item) => ({
          id: item.id,
          version_id: item.version_id,
          active: item.active,
          template_fields: item.template_fields.map((field) => ({
            ...field,
            key: field.form_field.key
          }))
        }))
        this.selectedTemplate.version =
          template.template_versions?.find((item) => item.active)?.id ?? 0
      })
    },
    selectPage (page) {
      this.pageNumber = page
      this.getTemplates()
    }
  },
  created () {
    this.getTemplates()
  }
}
</script>

<style lang="scss" scoped >
.form-template {
  &-bar {
    height: 60px;
    margin: -12px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  &-container {
    height: calc(100% - 60px);
  }
  &-no-form {
    font-size: 20px;
    color: var(--text-color);
  }
  &-group {
    overflow: auto;
    position: relative;
    & > div {
      & > div {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        & > div {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      }
    }
    &-header {
      background-color: var(--brand-color);
      position: sticky;
      top: 0;
      &-item {
        padding: 4px 10px;
        font-size: 14px;
        font-weight: 600;
        color: var(--text-color);
      }
    }
    &-row {
      display: grid;
      grid-template-columns: 2fr 3fr 1fr 1fr 80px;
    }
    &-item {
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      & > div {
        padding: 4px 10px;
        font-size: 14px;
        color: var(--text-color);
      }
    }
  }
  &-preview {
    &-header {
      padding: 5px 10px;
      background: var(--brand-color);
      & > h3 {
        font-size: 14px;
        font-weight: 600;
        color: var(--text-color);
      }
      & select {
        font-size: 10px;
        background-color: var(--brand-light-color);
        line-height: 1;
        border: 1px solid rgba(0, 0, 0, 0.3);
        border-radius: 4px;
        padding: 2px 6px;
      }
    }
    &-body {
      height: calc(100% - 40px);
      padding: 10px;
      background: var(--bg-color);
      overflow-y: auto;
    }
  }
  &-workflowlink{
  &-container{
    display: flex;
    flex-direction: column;
width: 100%;
height: 100%;
background: var(--bg-color);
  }
  &-formName{
  max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
&-body{
flex-grow: 1;
max-height: 80vh;
overflow: auto;
}
&-buttonBox{
gap:10px
}
}

}
</style>
