import {
  // runMutation,
  runQuery
} from '../graphQl'
import * as storageLocationQuery from '../query/storageLocations'

export const getStorageLocations = (offset = 0, limit = 10, search) => {
  const conditions = {
    deleted: { _eq: false }
  }
  if (search?.length) {
    conditions.name = { _ilike: `%${search}%` }
  }
  return runQuery(storageLocationQuery.GetAllStorageLocations(), { conditions, offset, limit }, 'tenant')
}

export const createStorageLocation = (data) => {
  return runQuery(storageLocationQuery.CreateStorageLocation(), { data }, 'tenant')
}

export const updateStorageLocations = (id, data) => {
  return runQuery(storageLocationQuery.UpdateStorageLocationById(), { id, data }, 'tenant')
}
