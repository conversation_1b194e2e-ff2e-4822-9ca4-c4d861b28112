<template>
  <div class="kpi-card">
    <div class="kpi-card-title">{{title}}</div>
    <div
      class="kpi-card-pie"
      :data-value="`${value}%`"
      :style="{background: getBackgroundColor}">
    </div>
  </div>
</template>

<script>
export default {
  name: 'kpiCard',
  props: {
    title: {
      type: String,
      default: ''
    },
    value: {
      type: [Number, String],
      default: 88.88
    },
    color: {
      type: String,
      default: '#00b0ff'
    }
  },
  computed: {
    getBackgroundColor () {
      return `conic-gradient(${this.color} 0% ${this.value}%, #e9e7e6 ${this.value}% 100%)`
    }
  }
}
</script>

<style lang="scss" scoped >
.kpi-card {
  padding: 10px;
  margin: 10px 0;
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.2);
  background-color: var(--bg-color);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  &-title {
    font-size: 16px;
    color: var(--text-color);
    margin-bottom: 10px;
  }
  &-pie {
    margin: 0px auto;
    margin-top: 10px;
    font-size: 13px;
    width: 5em;
    height: 5em;
    border-radius: 50%;
    position: relative;
    &::before {
      content: "";
      display: block;
      position: absolute;
      width: 3.8em;
      height: 3.8em;
      top: 0.6em;
      left: 0.6em;
      border-radius: 50%;
      background: var(--bg-color);
    }
    &::after {
      content: attr(data-value);
      font-size: 0.9em;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
</style>
