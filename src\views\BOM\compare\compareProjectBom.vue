<template>
  <div v-if="loadingBomDetail" class="fh center">
    <loading-circle />
  </div>
  <div v-else class="bom-compare">
    <div class="bom-compare-bar v-center space-between px-3">
      <h1 class="weight-500 xxl v-center">
        <img
          class="mr-3"
          src="~@/assets/images/icons/arrow-back.svg"
          width="30px"
          alt=""
          @click="goBack"
        />
        BOM Compare
      </h1>
      <div class="bom-compare-action m v-center">
        <label class="ml-5">From</label>
        <div class="dropdown ml-3" @mouseleave="disableDropDown">
      <button :class="[assStyleClass]" @click="showLeftDropdown = !showLeftDropdown">
      <div v-overflow-tooltip v-if=" selectedLeftBom.version !== null">{{ selectedLeftBom.bom_name}} (V-{{ selectedLeftBom.version }})</div>
      </button>
      <div>
        <div class="flex gap-2 dropdown-content-single " v-if="showLeftDropdown">
        <input type="text" class="dropdown-content-search" placeholder="Search here..." v-model="searchkeyword">
        <div class="bom-list"  v-for="(option, index) in BomList" :key="index">
          <label
          class="option-label"
          v-overflow-tooltip
          @click="() => selectBom({bom_name: option.name, bom_versions: option.bom_versions, leftBom: true})"
          :class="{ current: option.name === selectedLeftBom.bom_name }"
        >
          {{ option.name }}
        </label>
        <ul v-if="activeLeftBom === option.name">
        <li class="bom-versions" v-for="(version, vIndex) in sortedBomVersions(option.bom_versions)" :key="vIndex" @click="selectBomVersion({bom_versions: version, bom_name: option.name, leftBom: true})">Version - {{ version.version_no }} {{ version.active === true ? "(Latest)" : null}}</li>
      </ul>
        </div>
      </div>
      </div>
    </div>
        <label class="ml-5">To</label>
        <div class="dropdown ml-3" @mouseleave="disableDropDown">
      <button :class="[assStyleClass]" @click="showRightDropdown = !showRightDropdown">
      <div v-overflow-tooltip v-if=" selectedRightBom.version !== null">{{ selectedRightBom.bom_name}} (V-{{ selectedRightBom.version }})</div>
      </button>
      <div>
        <div class="flex gap-2 dropdown-content-single " v-if="showRightDropdown">
        <input type="text" class="dropdown-content-search" placeholder="Search here..." v-model="searchkeyword">
        <div class="bom-list"  v-for="(option, index) in BomList" :key="index">
          <label
          class="option-label"
          v-overflow-tooltip
          @click="() => selectBom({bom_name: option.name, rightbom_versions: option.bom_versions,  leftBom: false})"
          :class="{ current: option.name === selectedRightBom.bom_name }"
        >
          {{ option.name }}
        </label>
        <ul v-if="activeRightBom === option.name">
        <li  class="bom-versions" v-for="(version, vIndex) in sortedBomVersions(option.bom_versions)" :key="vIndex" @click="selectBomVersion({bom_versions: version, bom_name: option.name, leftBom: false})">Version - {{ version.version_no }} {{ version.active === true ? "(Latest)" : null}}</li>
      </ul>
        </div>
      </div>
      </div>
    </div>
      </div>
    </div>
    <div class="bom-compare-container">
      <compare
        :leftVersion="selectedLeftBom.leftVersionId"
        :rightVersion="selectedRightBom.rightVersionId"
        ref="compare"
      />
    </div>
  </div>
</template>

<script>
import { GetAllProjectBomList, GetAllBomList } from '@/api'
// import { GetAllBomList } from '@/api'
import loadingCircle from '../../../components/common/loadingCircle.vue'
import Compare from '../../../components/bom/compare/compare.vue'
export default {
  components: { loadingCircle, Compare },
  name: 'BomCompare',
  props: {
    productBom: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    loadingBomDetail: false,
    productCode: null,
    bomList: [],
    selectedLeftBom: { bom_name: null, version: null, leftVersionId: null },
    selectedRightBom: { bom_name: null, version: null, rightVersionId: null },
    showLeftDropdown: false,
    showRightDropdown: false,
    searchkeyword: null,
    activeLeftBom: null,
    activeRightBom: null
  }),
  computed: {
    assStyleClass () {
      switch (this.componentfor) {
      case 'materialFilter':
        return 'dropdown-toggle-filter'
      default :
        return 'dropdown-toggle'
      }
    },
    BomList () {
      if (this.searchkeyword) {
        return this.bomList.filter((element) => element?.name?.toLowerCase()?.match(new RegExp(this.searchkeyword?.toLowerCase())))
      } else {
        return this.bomList
      }
    }
  },
  methods: {
    sortedBomVersions (bomVersions) {
      return [...bomVersions].sort((a, b) => a.version_no - b.version_no)
    },
    disableDropDown () {
      this.showLeftDropdown = false
      this.showRightDropdown = false
      this.searchkeyword = ''
    },
    goBack () {
      this.$router.go(-1)
    },
    getProductBomDetail () {
      this.loadingBomDetail = true
      GetAllBomList(this.productCode)
        .then((res) => {
          this.bomList = res.core_bom
        })
        .catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loadingBomDetail = false
        })
    },
    getBomDetail () {
      this.loadingBomDetail = true
      GetAllProjectBomList()
        .then((res) => {
          this.bomList = res.core_bom
        })
        .catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loadingBomDetail = false
        })
    },
    selectBomVersion (versionValue) {
      if (versionValue.leftBom) {
        this.selectedLeftBom = { bom_name: versionValue.bom_name, version: versionValue.bom_versions.version_no, leftVersionId: versionValue.bom_versions.id }
      } else {
        this.selectedRightBom = { bom_name: versionValue.bom_name, version: versionValue.bom_versions.version_no, rightVersionId: versionValue.bom_versions.id }
      }
      this.disableDropDown()
    },
    selectBom (optionValue) {
      if (optionValue.leftBom) {
        const leftActiveVersion = optionValue.bom_versions.find(version => version.active) || optionValue.bom_versions.find(version => version.version_no === 1)
        this.selectedLeftBom = { bom_name: optionValue.bom_name, version: leftActiveVersion.version_no, leftVersionId: leftActiveVersion.id }
        this.activeLeftBom = optionValue.bom_name
      } else {
        const rightActiveVersion = optionValue.rightbom_versions.find(version => version.active) || optionValue.rightbom_versions.find(version => version.version_no === 1)
        this.selectedRightBom = { bom_name: optionValue.bom_name, version: rightActiveVersion.version_no, rightVersionId: rightActiveVersion.id }
        this.activeRightBom = optionValue.bom_name
      }
    }
  },
  created () {
    this.productCode = this.$route.params.productCode
    if (this.productBom) {
      this.getProductBomDetail()
    } else {
      this.getBomDetail()
    }
  }
}
</script>

<style lang="scss" scoped >
.dropdown {
    position: relative;
    display: inline-block;
    &-label{
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    }
  }
  .dropdown-toggle {
    background-color: var(--brand-light-color);
    border: 1px solid var(--brand-color);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 6px 10px;
    cursor: pointer;
    width: 184px;
    border-radius: 0.3em;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: 27px;
  }

  .option-label:hover{
    background-color: var(--brand-light-color);
  }
  .option-label {
    width: 100%;
    display: block;
    cursor: pointer;
    transition: background-color 0.2s;
    padding: 3px ;
    font-size: 1rem;
    overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
  }
  .dropdown-content-search{
    margin-block: 5px;
    padding: 5px 10px 5px  10px;
    border-radius: 5px;
    width: 100%;
    border: 1px solid  #888;
  }
  .current {
        background: var(--brand-color);
    }
  .dropdown-content-search::placeholder {
    color: #888;
    font-style: italic;
    }
    .dropdown-toggle-filter {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 170px;
    border-radius: 0.3em;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    border: 1px solid #3B3B3B77;
    border-radius: 0.285em;
    font-size: 1em;
    background-color: transparent;
  }
.dropdown-content-single {
    position: absolute;
    background-color: #fff;
    display: inline-block;
    box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
    z-index: 20;
    width: 100%;
    max-height: 240px;
    overflow: scroll;
    border-top: none; /* Remove top border to improve appearance */
  }

    .bom-versions {
      // display: none;
      background-color: rgb(246 234 215);
      min-width: 120px;
      height:auto;
      padding-top: 10px;
      padding-bottom: 5px;
      padding-left: 5px;
      margin-left: 30px;
      box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
      font-size: 1rem;
      margin-bottom: 6px;
    }
    // .bom-list:hover .bom-versions {
    //   display: block;
    // }
.bom-compare {
  height: 100%;
  &-bar {
    height: 60px;
    margin: -12px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  &-action {
    margin-right: 20px;
    label {
      font-size: 14px;
      color: var(--text-color);
      margin-bottom: 5px;
      display: block;
    }
  }
  &-container {
    height: calc(100% - 60px);
    overflow: auto;
  }
}
</style>
