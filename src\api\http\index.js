import Config from '@/config.js'
const getToken = (tokenType) => {
  let token = localStorage.getItem(Config.localstorageKeys.AUTH)
  if (tokenType === 'auth') {
    token = localStorage.getItem(Config.localstorageKeys.AUTH)
  } else if (tokenType === 'tenant') {
    token = localStorage.getItem(Config.localstorageKeys.TENANT)
  } else if (tokenType === 'project') {
    token = localStorage.getItem(Config.localstorageKeys.PROJECT)
  }
  return token
}
export default {
  GET: (url, tokenType = 'auth') => {
    const token = getToken(tokenType)
    return new Promise((resolve, reject) => {
      fetch(url, {
        headers: {
          Authorization: 'Bearer ' + token
        }
      }).then(e => e.json())
        .then(resolve)
        .catch(reject)
    })
  },
  PUT: (url, body, tokenType = 'auth') => {
    const token = getToken(tokenType)
    return new Promise((resolve, reject) => {
      fetch(url, {
        method: 'PUT',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + token
        },
        body: JSON.stringify(body)
      }).then(e => e.json())
        .then(resolve)
        .catch(reject)
    })
  },
  POST: (url, body, tokenType = 'auth') => {
    const token = getToken(tokenType)
    return new Promise((resolve, reject) => {
      fetch(url, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + token
        },
        body: JSON.stringify(body)
      }).then(e => e.json())
        .then(resolve)
        .catch(reject)
    })
  },
  DELETE: (url, tokenType = 'auth') => {
    const token = getToken(tokenType)
    return new Promise((resolve, reject) => {
      fetch(url, {
        method: 'DELETE',
        Authorization: 'Bearer ' + token
      }).then(e => e.json())
        .then(resolve)
        .catch(reject)
    })
  }
}
