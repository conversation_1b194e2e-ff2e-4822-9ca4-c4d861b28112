<template>
  <div class="material-mapping">
    <h1 class="weight-500 xl mb-2">Material Master Mapping</h1>
    <p class="mb-4">
      Please map the columns from the uploaded file to the material master data.
    </p>
    <div class="material-mapping-row">
      <label class="weight-500 l">Dtx Column</label>
      <label class="weight-500 l">File Column</label>
    </div>
    <div class="material-mapping-container">
      <div
        v-for="(item, index) in mappingObj"
        :key="item.key"
        class="material-mapping-row"
      >
        <label for="">{{ item.label }} <b v-if="item.required">*</b> :</label>
        <select type="text" v-model="mappingObj[index].mapWith">
          <option value="">Select</option>
          <option v-for="col in fileCol" :key="col" :value="col">
            {{ col }}
          </option>
        </select>
      </div>
    </div>
    <div class="v-center flex-end pt-3">
      <button class="btn btn-black mx-3" @click="$emit('back')">Back</button>
      <button class="btn" @click="startSavingObject" >Save</button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    fileData: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      mappingObj: [
        {
          label: 'Custom Material ID',
          key: 'custom_material_id',
          mapWith: '',
          required: true
        },
        {
          label: 'ERP Material ID',
          key: 'erp_material_id',
          mapWith: '',
          required: true
        },
        {
          label: 'Gross Weight',
          key: 'gross_weight',
          mapWith: '',
          required: true
        },
        {
          label: 'Inventory',
          key: 'inventory',
          mapWith: '',
          required: true
        },
        {
          label: 'Lead Time',
          key: 'lead_time',
          mapWith: '',
          required: true
        },
        {
          label: 'Material Description',
          key: 'material_description',
          mapWith: '',
          required: true
        },
        {
          label: 'Material Group',
          key: 'material_group',
          mapWith: '',
          required: true
        },
        {
          label: 'Material Name',
          key: 'material_name',
          mapWith: '',
          required: true
        },
        {
          label: 'Others',
          key: 'others',
          mapWith: '',
          required: true
        },
        {
          label: 'Parent ID',
          key: 'parent_id',
          mapWith: '',
          required: true
        },
        {
          label: 'PLM Material ID',
          key: 'plm_material_id',
          mapWith: '',
          required: true
        },
        {
          label: 'PLM Record ID',
          key: 'plm_record_id',
          mapWith: '',
          required: true
        },
        {
          label: 'Quantity',
          key: 'quantity',
          mapWith: '',
          required: true
        },
        {
          label: 'Storage Location',
          key: 'storage_loc',
          mapWith: '',
          required: true
        },
        {
          label: 'Unit Cost',
          key: 'unit_cost',
          mapWith: '',
          required: true
        },
        {
          label: 'Unit of Material',
          key: 'unit_of_material',
          mapWith: '',
          required: true
        },
        {
          label: 'Unit Sale Price',
          key: 'unit_sale_price',
          mapWith: '',
          required: true
        },
        {
          label: 'Weight Unit',
          key: 'weight_unit',
          mapWith: '',
          required: true
        }
      ],
      fileCol: []
    }
  },
  methods: {
    startSavingObject () {
      const listData = []
      this.fileData.forEach((item) => {
        const obj = {}
        this.mappingObj.forEach((mapItem) => {
          obj[mapItem.key] = item[mapItem.mapWith] || null
        })
        listData.push(obj)
      })
      this.$emit('save', listData)
    }
  },
  mounted () {
    this.fileCol = Object.keys(this.fileData[0])
    this.mappingObj.forEach((item) => {
      if (this.fileCol.includes(item.key)) {
        item.mapWith = item.key
      }
    })
  }
}
</script>

<style lang="scss" scoped >
.material-mapping {
  padding-right: 20px;
  &-container {
    max-height: 300px;
    overflow: auto;
  }
  &-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    label {
      width: 200px;
      b {
        color: var(--brand-color);
      }
    }
    select {
      flex-grow: 1;
      height: 30px;
      background-color: transparent;
      border: 1px solid #c0c0c0;
      border-radius: 5px;
      padding: 0 10px;
    }
  }
}
</style>
