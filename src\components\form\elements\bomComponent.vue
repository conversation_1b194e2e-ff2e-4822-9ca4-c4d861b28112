<template>
    <div :class="{
      'form-input': true,
      'form-input--required': data.required
      }" >
    <label>{{data.caption}} :
        <img
        v-if="data.visibility"
        class="mx-1"
        width="15"
        src="~@/assets/images/eye.svg"
        alt="view"
        />
        </label>
        <div class="form-user-bedge" v-if="mode === 'TEMPLATE'">
          <div class="no-user-selected text-center p-2">Select a BOM</div>
        </div>
        <input v-if="!viewOnly" type="text" v-model="searchKeyword" @click="open = !open" :style="{ height: '40px' }" placeholder="Select a BOM"/>
        <div v-if="open"  class="bomLists">
          <div v-if="getBomList.length === 0" class="form-input--option">
            <div class="form-input--option__name">No BOM found</div>
          </div>
          <div class="form-input--option flex" v-for="bom in getBomList" :key="bom.id" @click="toggleBom(bom)">
            <input type="checkbox" :checked="componentValue.some((item) => item === bom.id)"/>
            <div class="ml-2">
                {{ bom.name }}
            </div>
          </div>
        </div>
        <div class="form-user-bedge" v-if="getSelectedBom.length">
      <div v-for="item in getSelectedBom" :key="item.id" class="form-user-bedge__item">
        <div >
          <div class="flex">
            <div class="form-user-bedge__name">
              {{ item.name }}
            </div>
          </div>
        </div>

        <div v-if="!viewOnly" class="form-user-bedge__action" >
          <img src="~@/assets/images/delete-gray-icon.svg" width="20px" @click="removeUser(item)" />
        </div>
      </div>
    </div>
    </div>
  </template>

<script>
import { GetAllProjectBomList } from '@/api'
import { mapGetters } from 'vuex'
export default {
  name: 'bomComponent',
  props: {
    mode: {
      type: String,
      default: 'TEMPLATE'
    },
    data: {
      type: Object,
      default: () => {}
    },
    value: {
      type: Array,
      default: () => []
    },
    viewOnly: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      componentValue: [],
      bomList: [],
      searchKeyword: '',
      open: false,
      insert: [],
      delete: [],
      initialValue: []
    }
  },
  computed: {
    ...mapGetters(['isOnProjectLevel']),
    getSelectedBom () {
      return this.bomList.filter((bom) => {
        return this.componentValue.some(item => item === bom.id)
      })
    },
    getBomList () {
      if (!Array.isArray(this.bomList)) {
        return []
      }
      let bomList
      if (this.searchKeyword) {
        bomList = this.bomList.map((item) => {
          return {
            id: item.id,
            name: item.name
          }
        }).filter((item) => `${item.name}`.replace(' ', '').toLowerCase().includes(this.searchKeyword.toLowerCase().replace(' ', '')))
      } else {
        bomList = this.bomList.map((item) => {
          return {
            id: item.id,
            name: item.name
          }
        })
      }
      return bomList
    }
  },
  created () {
    window.addEventListener('click', this.closeDropdown)
    this.setValue()
    if (this.mode !== 'TEMPLATE' && this.isOnProjectLevel) {
      GetAllProjectBomList(true, true).then((res) => {
        this.bomList = res.core_bom
      }).catch((error) => {
        console.error('Error fetching BOM list:', error)
      })
    }
    this.initialValue = this.value.map((item) => item.bom_id)
  },
  destroyed () {
    window.removeEventListener('click', this.closeDropdown)
  },
  watch: {
    value () {
      this.setValue()
    }
  },
  methods: {
    setValue () {
      this.componentValue = this.value.map((item) => item.bom_id)
    },
    emitChange () {
      const value = {}
      if (this.insert.length) {
        value.insert = this.insert
      }
      if (this.delete.length) {
        value.delete = this.delete
      }
      this.$emit('customChange', {
        field_id: this.$props.data.field_id,
        value
      })
    },
    closeDropdown (event) {
      if (this.$el.contains(event.target)) {
        return
      }
      this.open = false
    },
    toggleBom (bom) {
      const index = this.componentValue.findIndex((item) => item === bom.id)
      if (index === -1) {
        this.componentValue.push(bom.id)
        const indexInInitialArray = this.initialValue.findIndex((item) => item === bom.id)
        if (indexInInitialArray === -1) {
          this.insert.push(bom.id)
          this.emitChange()
        } else {
          const indexInDeleteArray = this.delete.findIndex((item) => item === bom.id)
          if (indexInDeleteArray !== -1) {
            this.delete.splice(indexInDeleteArray, 1)
            this.emitChange()
          }
        }
      } else {
        this.componentValue.splice(index, 1)
        const indexInInitialArray = this.initialValue.findIndex((item) => item === bom.id)
        if (indexInInitialArray !== -1) {
          this.delete.push(this.initialValue[indexInInitialArray])
          this.emitChange()
        } else {
          const indexInInsertArray = this.insert.findIndex((item) => item === bom.id)
          if (indexInInsertArray !== -1) {
            this.insert.splice(indexInInsertArray, 1)
            this.emitChange()
          }
        }
      }
      this.emitChange()
    },
    removeUser (bom) {
      const index = this.componentValue.findIndex((item) => item === bom.id)
      this.componentValue.splice(index, 1)
      const indexInInitialArray = this.initialValue.findIndex((item) => item === bom.id)
      if (indexInInitialArray !== -1) {
        this.delete.push(this.initialValue[indexInInitialArray])
        this.initialValue.splice(indexInInitialArray, 1)
        this.emitChange()
      } else {
        const indexInInsertArray = this.insert.findIndex((item) => item === bom.id)
        if (indexInInsertArray !== -1) {
          this.insert.splice(indexInInsertArray, 1)
          this.emitChange()
        }
      }
    }
  }
}
</script>

  <style lang="scss" scoped >
.form-user-bedge {
  display: flex;
  flex-wrap: wrap;
  border: 1px solid #e5e5e5;
  margin-top: 10px;
  .form-user-bedge__item {
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 5px 10px;
    margin: 5px;
    display: flex;
    align-items: center;
    .form-user-bedge__name {
      font-size: 12px;
      font-weight: 600;
      color: #333333;
      & + span {
        font-size: 12px;
      }
    }
    .form-user-bedge__email {
      font-size: 12px;
      color: #333333;
    }
    .form-user-bedge__action {
      margin-left: 10px;
      cursor: pointer;
    }
  }
  .options{
    font-size: 12px;
  margin-bottom: 1em;
  position: relative;
    position: absolute;
    left: 0;
    width: 100%;
    background-color: #fff;
    border: 1px solid rgb(179, 179, 179);
    border-radius: 0.3em;
    z-index: 1;
    padding: 10px;
    max-height: 200px;
    overflow-y: auto;
  }
  .no-user-selected {
    font-size: 12px;
    color: var(--brand-color);
    text-align: center;
  }
}
.bomLists {
    position: absolute;
    /* top: 100%; */
    left: 0;
    width: 100%;
    background-color: #fff;
    border: 1px solid rgb(179, 179, 179);
    border-radius: 0.3em;
    z-index: 1;
    padding: 10px;
    max-height: 145px;
    overflow-y: auto;
  }
  </style>
