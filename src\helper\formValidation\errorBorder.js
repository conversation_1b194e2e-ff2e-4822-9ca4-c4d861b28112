const renderBorder = (selector, addclass = true) => {
  if (selector) {
    const element = document.querySelector(`[data-validation="${selector}"]`)
    if (element) {
      if (addclass) {
        element.classList.add('data-error')
        const input = element.querySelector('input, textarea, select')
        if (input) {
          input.addEventListener('focus', () => {
            element.classList.remove('data-error')
          })
        }
      } else {
        element.classList.remove('data-error')
      }
    }
  }
}

export default (selectors, addclass = true) => {
  if (typeof selectors === 'string') {
    renderBorder(selectors, addclass)
  } else if (Array.isArray(selectors)) {
    selectors.forEach(e => renderBorder(e, addclass))
  }
}

export const removeErrorBorder = (selectorArray) => {
  selectorArray.forEach(selector => {
    const elements = document.querySelectorAll(`[data-validation="${selector}"]`)
    elements.forEach((ele) => {
      ele.classList.remove('data-error')
    })
  })
}
