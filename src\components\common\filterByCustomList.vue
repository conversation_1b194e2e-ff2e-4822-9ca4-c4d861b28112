<template>
    <div class="filter-cusList">
<div  class="mt-5 filter-cusList-list " >
  <label for="" class="text-wrap">Filter by UOM </label>
  <single-select-drop-down
  :clear="clear"
:options="list_of_unit_of_material"
:current=initialValues.unit_of_material
@selected="addNewCustomSearch"
:componentfor="'materialFilter'"
/></div>
<div  class="mt-5 filter-cusList-list " >
  <label for="" class="text-wrap">Filter by Weight Unit </label>
  <single-select-drop-down
  :clear="clear"
:options="list_of_weight_unit"
:current=initialValues.weight_unit
@selected="addNewCustomSearch"
:componentfor="'materialFilter'"
/></div>
<div  class="mt-5 filter-cusList-list " >
  <label for="" class="text-wrap">Filter by Material Group </label>
  <single-select-drop-down
  :clear="clear"
:options="list_of_material_group"
:current=initialValues.material_group
@selected="addNewCustomSearch"
:componentfor="'materialFilter'"
/></div>
<div  class="mt-5 filter-cusList-list " >
  <label for="" class="text-wrap">Filter by storage Location </label>
  <single-select-drop-down
  :clear="clear"
:options="list_of_address_location"
:current=initialValues.storage_loc
@selected="addNewCustomSearch"
:componentfor="'materialFilter'"
/>
</div>
</div>
</template>
<script>
import singleSelectDropDown from './singleSelectDropDown.vue'
import {
  UnitOfMaterial,
  WeightUnit,
  MaterialGroup,
  AddressLocation
} from '@/api'
export default {
  name: 'filterByCustomList',
  components: {
    singleSelectDropDown
  },
  data: () => ({
    list_of_weight_unit: [],
    list_of_unit_of_material: [],
    list_of_material_group: [],
    list_of_address_location: [],
    selectedList: {}

  }),
  props: {
    initialValues: {
      type: Object
    },
    clear: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    setup () {
      this.selectedList = JSON.parse(JSON.stringify(this.initialValues))
      UnitOfMaterial().then((res) => {
        this.list_of_unit_of_material = res.custom_list_values.map((item) => {
          return {
            name: item.name,
            id: item.id,
            label: 'unit_of_material'
          }
        })
        this.list_of_unit_of_material.unshift({
          name: 'Not Selected', id: -1, label: 'unit_of_material'
        })
      })

      WeightUnit().then((res) => {
        this.list_of_weight_unit = res.custom_list_values.map((item) => {
          return {
            name: item.name,
            id: item.id,
            label: 'weight_unit'
          }
        })
        this.list_of_weight_unit.unshift({
          name: 'Not Selected', id: -1, label: 'weight_unit'
        })
      })

      MaterialGroup().then((res) => {
        this.list_of_material_group = res.custom_list_values.map((item) => {
          return {
            name: item.name,
            id: item.id,
            label: 'material_group'
          }
        })
        this.list_of_material_group.unshift({
          name: 'Not Selected', id: -1, label: 'material_group'
        })
      })

      AddressLocation().then((res) => {
        this.list_of_address_location = res.address_locations.map((item) => {
          return {
            name: item.name,
            id: item.id,
            label: 'storage_loc'
          }
        })
        this.list_of_address_location.unshift({
          name: 'Not Selected', id: -1, label: 'storage_loc'
        })
      })
    },
    addNewCustomSearch (selectedItem) {
      if (selectedItem.id === -1) {
        delete this.selectedList[selectedItem.label]
      } else { this.selectedList[selectedItem.label] = selectedItem }
      this.$emit('filter-cusList', this.selectedList)
    }
  },
  watch: {
    'intialValues' () {
      this.selectedList = JSON.parse(JSON.stringify(this.initialValues))
    }
  },
  created () {
    this.setup()
  }
}

</script>

<style lang="scss">
.filter-cusList{
&-list{
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
 place-items: center;
 justify-items: center;
 & label{
  justify-self: flex-start;
 }
  & img {
    height: 1.3em;
  }
}
}
</style>
