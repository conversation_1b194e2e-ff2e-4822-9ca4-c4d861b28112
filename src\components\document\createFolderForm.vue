<template>
  <div class="folder-form s" >
    <div class="input-group">
      <label for=""> Folder Name </label>
      <input v-model="folderName" type="text" placeholder="Folder Name" />
    </div>
    <div class="input-group mt-3">
      <label for=""> Folder Description </label>
      <textarea v-model="folderDescription" type="text" placeholder="Folder Description" />
    </div>
    <div class="flex flex-end mt-5">
      <button class="btn btn-black mx-3" @click="onCancel">Cancel</button>
      <button class="btn" @click="createFolder" :disabled="buttonDisabled">Create Folder</button>
    </div>
  </div>
</template>

<script>
import { insertDocument } from '@/api'
import { success, alert } from '@/plugins/notification'
import Loader from '@/plugins/loader'

export default {
  name: 'CreateFolderForm',
  props: {
    folder: {
      type: Object,
      default: () => ({})
    },
    parentId: {
      type: String,
      default: null
    },
    parentFolder: {
      type: Object,
      default: () => ({})
    },
    // will get all the documents  details in given root directory
    allDocuments: {
      type: Array,
      default: () => ([])
    },
    open: {
      type: Boolean,
      default: false
    },
    buttonDisabled: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    folderName: '',
    folderDescription: ''
  }),
  methods: {
    resetForm () {
      this.folderName = ''
      this.folderDescription = ''
    },
    onCancel () {
      this.resetForm()
      this.$emit('close')
    },
    onSuccessCallback (res) {
      this.resetForm()
      success('Folder created successfully')
      this.$emit('create')
      this.$emit('close')
    },
    onErrorCallback (err) {
      alert('Error creating folder')
      console.log(err)
    },
    createFolder () {
      this.buttonDisabled = true
      const newFolderName = this.folderName.toUpperCase()
      const newFolderArray = []
      for (let i = 0; i < this.allDocuments.length; i++) {
        newFolderArray.push(this.allDocuments[i].doc_name)
      }
      if (newFolderArray.includes(newFolderName)) {
        alert('Folder Name Already exists!')
        this.buttonDisabled = false
      } else if (this.folderName.trim() === '') {
        alert('Folder name cannot be empty. Please provide a valid name.')
        this.folderName = ''
      } else {
        const loader = new Loader()
        loader.show()
        insertDocument(
          null,
          this.folderDescription,
          this.folderName.toUpperCase(),
          null,
          null,
          true,
          this.$route.params.documentId || null,
          null
        ).then(this.onSuccessCallback)
          .catch(this.onErrorCallback)
          .finally(() => {
            this.buttonDisabled = false
            loader.hide()
          })
      }
    },
    keyPress (e) {
      if (!this.open || this.buttonDisabled) return
      if (e instanceof KeyboardEvent && e.code === 'Escape') {
        this.onCancel()
      } else if (e instanceof KeyboardEvent && e.code === 'Enter') {
        this.createFolder()
      }
    }
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
  }
}
</script>

<style class="scss" scoped >
.folder-form {
  width: 400px
}
</style>
