import config from '../../config'
import { runMutation, runQuery } from '../graphQl'
import http from '../http'
import * as projectPlannerQueries from '../query/projectPlanner'
import store from '../../store'

export const getProjectTasksAndLinks = async (variables, view = 'chart') => {
  let isCollaborator = false
  let token = 'project'
  variables.conditions = {}
  if (!store.getters?.isOnProjectLevel) {
    token = 'tenant'
    if (store.getters?.collaborator) {
      isCollaborator = true
      if (store.getters.projectIdForCollaborator) {
        variables.conditions.project_id = { _eq: store.getters.projectIdForCollaborator }
      } else {
        return { core_tasks: [], core_task_links: [] }
      }
    }
  }
  if (variables.from) {
    variables.conditions.planned_end_date = { _gt: variables.from }
  }
  if (variables.upto) {
    if (variables.from) {
      variables.conditions.planned_end_date._lt = variables.upto
    } else {
      variables.conditions.planned_end_date = { _lt: variables.upto }
    }
  }
  if (variables.assignees?.length) {
    variables.conditions.task_assignees = { user_id: { _in: variables.assignees } }
  }
  if (variables.tagId) {
    variables.conditions = {
      tag_tasks: { tag: { id: { _eq: variables.tagId } } }
    }
  }
  if (variables.taskName) {
    variables.conditions.name = { _ilike: '%' + variables.taskName + '%' }
  }
  variables.from = undefined
  variables.upto = undefined
  variables.assignees = undefined
  variables.tagId = undefined
  variables.taskName = undefined
  variables.skipLinksFetching = view === 'board'
  variables.isCollaborator = isCollaborator
  return runQuery(projectPlannerQueries.GetTasksAndLinks(), variables, token)
}

export const AddBomToTaskMutation = (bomId, taskId, quantity) => {
  const data = {
    source_feature_id: config.CORE_FEATURES.TASKS,
    source_id: taskId,
    target_feature_id: config.CORE_FEATURES.BOM,
    target_id: bomId,
    link_type_id: 3,
    metadata: {
      quantity
    }
  }
  data.metadata = JSON.stringify(data.metadata)
  return runMutation(projectPlannerQueries.addBomToTask(), { data }, 'project')
}

export const UpdateBomTaskMutation = (id, quantity = 1) => {
  const metadata = { quantity }
  return runMutation(projectPlannerQueries.UpdateBomTaskAssociation(), { id, metadata }, 'project')
}

export const DeleteBomTaskMutation = (id) => {
  return runMutation(projectPlannerQueries.DeleteBomFromTask(), { id: id }, 'project')
}

export const GetAllTaskBom = (taskId) => {
  let token = 'project'
  if (store.getters?.collaborator) {
    token = 'tenant'
  }
  return runMutation(projectPlannerQueries.GetAllTaskBom(), { taskId }, token)
}
export const GetAllTaskBomByToken = (taskId, token) => {
  return runMutation(projectPlannerQueries.GetAllTaskBom(), { taskId }, 'current', token)
}

export const AddNewTask = async (data) => {
  return runMutation(projectPlannerQueries.AddTask(), { data }, 'project')
}

export const CreateTask = async (data) => {
  data.attached_bom = undefined
  return runMutation(projectPlannerQueries.CreateTask(), { data }, 'project')
}

export const UpdateTask = async (id, data) => {
  data.attached_bom = undefined
  return runMutation(projectPlannerQueries.UpdateTaskQuery(), { id, data }, store.getters?.collaborator ? 'tenant' : 'project')
}

export const UpdateTasks = async (taskUpdates) => {
  return runMutation(projectPlannerQueries.updateTasksQuery(), { taskUpdates }, store.getters?.collaborator ? 'tenant' : 'project')
}
export const UpdateTasksWithToken = async (taskUpdates, token) => {
  return runMutation(projectPlannerQueries.updateTasksQuery(), { taskUpdates }, 'current', token)
}

export const addNewAssignees = async (assignees) => {
  return runMutation(projectPlannerQueries.addNewAssigneesMutation(), { assignees }, 'project')
}

export const deleteAssignees = async (conditions) => {
  return runMutation(projectPlannerQueries.deleteAssigneesMutation(), { conditions }, 'project')
}

export const InsertTaskLinks = async (source, target, type, lag = null) => {
  return runMutation(projectPlannerQueries.InsertTaskLinks(), { src_task_id: source, target_task_id: target, link_type_id: type, lag }, 'project')
}

export const deleteTaskLinks = async (source, target) => {
  return runMutation(projectPlannerQueries.deleteTaskLinks(), { src_task_id: source, target_task_id: target }, 'project')
}

export const GetTaskStatuses = async () => {
  const conditions = { deleted: { _eq: false }, custom_list: { name: { _eq: 'Task Status' } } }
  if (store.getters?.collaborator) {
    const collaboratorId = store.getters?.collaboratorId
    conditions.tenant_id = { _eq: collaboratorId }
  }
  return runQuery(projectPlannerQueries.getTaskStatuses(), { conditions }, 'tenant')
}

export const getAllTaskUsers = (status) => {
  return runQuery(projectPlannerQueries.getAllTaskUsersQuery(), { status }, 'project')
}

export const ChangeGanttAssignee = (status, oldId, newId, targetTenantId) => {
  return runQuery(projectPlannerQueries.ChangeAssigneeQuery(), { status, old_user_id: oldId, new_user_id: newId, tenant_id: targetTenantId }, 'project')
}

export const GetProjectImports = async () => {
  return runQuery(projectPlannerQueries.getProjectImports(), null, 'project')
}

export const SaveBaseline = (name) => {
  return runMutation(projectPlannerQueries.saveBaseline(), { name }, 'project')
}

export const GetS3DownloadUrl = (body) => {
  return http.POST(config.serverEndpoint + '/files/download', body)
}

export const GetBaselineFromS3 = (url) => {
  return http.GET(url)
}
// this is done from BE
// export const updateProjectedEndDate = (taskId, projectedEndDate, progress, token) => {
//   return runMutation(projectPlannerQueries.updateProjectedEndDateMutation(), { id: taskId, projected_end_date: projectedEndDate, progress }, 'current', token)
// }

export const updateTaskLink = (data, targetTaskId, sourceTaskId) => {
  return runMutation(projectPlannerQueries.updateTaskLinkMutation(), { data, target_task_id: targetTaskId, src_task_id: sourceTaskId }, 'project')
}

export const GetAllTaskDocs = (taskId) => {
  let token = 'project'
  if (store.getters?.collaborator) {
    token = 'tenant'
  }
  return runMutation(projectPlannerQueries.GetAllTaskDocs(), { taskId }, token)
}
export const GetAllTaskDocsByToken = (taskId, token) => {
  return runMutation(projectPlannerQueries.GetAllTaskDocs(), { taskId }, 'current', token)
}
export const attchTaskDocs = (data) => {
  return runMutation(projectPlannerQueries.addDocumentToTask(), { data }, 'project')
}
export const removeTaskDocuments = async (docId, taskId) => {
  return runMutation(projectPlannerQueries.removeTaskDocs(), { document_id: docId, task_id: taskId }, 'project')
}

export const deleteTasks = async (taskIds) => {
  return runMutation(projectPlannerQueries.deleteTasks(), { taskIds }, 'project')
}

export const checkTimesheetEntry = async (taskId) => {
  return runQuery(projectPlannerQueries.checkTimesheetEntry(), { taskId }, 'project')
}

export const checkIfTaskIsParent = async (taskId) => {
  return runQuery(projectPlannerQueries.checkIfTaskIsParent(), { taskId }, 'project')
}
export const checkTimeRemainigExist = async (taskId, userId, entryDate) => {
  return runMutation(projectPlannerQueries.checkTimeRemainigExistQuery(), { task_id: taskId, user_id: userId, entry_date: entryDate }, 'project')
}
export const timeSheetAggregate = async (taskId, userId) => {
  return runMutation(projectPlannerQueries.timeSheetAggregate(), { task_id: taskId, user_id: userId }, 'project')
}

export const updateTaskTimeSheetData = (data) => {
  return runQuery(projectPlannerQueries.updateTaskTimeSheetDataQuery(), data, 'tenant')
}

export const computeSpiAndCost = () => {
  return http.GET(config.serverEndpoint + '/project/trigger-project-plan-computation', 'project')
}

export const getAllTasks = async (filterObj) => {
  const filters = {}
  if (filterObj.projectId) {
    filters.project_id = { _eq: filterObj.projectId }
  }
  if (filterObj.type) {
    filters.type = { _eq: filterObj.type }
  }
  if (filterObj.startDate && filterObj.endDate) {
    filters.planned_start_date = { _gte: filterObj.startDate, _lte: filterObj.endDate }
  }
  return runQuery(projectPlannerQueries.getAllTasksQuery(), { filters }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
