<template>
    <div class="file-detail">
      <div v-if="loading" class="fh fw center">
        <loading-circle />
      </div>
      <div v-else class="fh fw">
        <div class="file-detail-header v-center space-between">
          <div class="title v-center">
            {{ getSelectedFile?.doc_name }}
          </div>
          <img
          class="close"
          src="~@/assets/images/icons/close-icon.svg"
          @click="$emit('close')"
          alt=""
          width="24px"
          />
        </div>
        <div class="flex" >
          <div :style="{
            width: openComment ? 'calc(100% - 400px)' : '100%'
          }">
            <div class="file-detail-image-container p-3 h-center">
              <iframe
              v-if="fileTypes1.includes(getSelectedFile?.doc_ext)"
              :src="`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(
                blobKeyURlMapping[
                  getSelectedFile.blob_key?? null
                ]
              )}`"
              frameborder="0"
              width="100%"
            ></iframe>
            <iframe
              v-else-if="getSelectedFile?.doc_ext==='pdf'"
              :src="pdfUrl"
              frameborder="0"
              width="100%"
              height="100%"
            ></iframe>
            <div class="file-detail-image-container-csvbox" v-else-if="getSelectedFile?.doc_ext === 'csv'">
              <table>
                <thead>
                  <tr>
                    <th v-for="(header, index) in csv.headers" :key="index">
                      {{ header }}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(row, rowIndex) in csv.rows" :key="rowIndex">
                    <td v-for="(item, itemIndex) in row" :key="itemIndex">
                      {{ item }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <img
              v-else-if="
               fileTypes3.includes(getSelectedFile?.doc_ext)"
              :src="
                blobKeyURlMapping[getSelectedFile.blob_key?? null]"
              alt=""
            />
            <img
              v-else
              src="~@/assets/images/no_file_icon.svg"
              alt=""
            />
            </div>
            <div class="file-detail-corusol">
              <div
                v-for="(item, index) in revisionList"
                :key="item.id"
                class="file-detail-corusol--item"
                :class="{ active: index === selectedIndex }"
                @click="selectedIndex = index"
              >
                <div class="file-detail-corusol--item-img">
                  <img
                  v-if="blobKeyThumbURlMapping[item?.thumbnail_blob_key]"
                  :src="blobKeyThumbURlMapping[item?.thumbnail_blob_key]"
                  alt=""
                  height="100%"
                  />
                  <img
                  v-else
                  src="~@/assets/images/no_file_icon.svg"
                  alt=""
                  height="100%"
                  />
                </div>
                <div class="file-detail-corusol--item-title elipsis-text">
                  {{ item?.doc_name }}
                </div>
                <div class="file-detail-corusol--item-date elipsis-text">
                  {{ item.created_on | duration }}
                </div>
              </div>
            </div>
          </div>
          <div class="file-detail-comment" :style="{
            width: openComment ? '400px' : '0px'
          }" >
            <comment-panel v-if="openComment" :document="getSelectedFile" @closeComment="openComment = false  "/>
          </div>
        </div>
      </div>
      <button class="comment-btn" v-if="!openComment" @click="openComment = true">
        <img src="~@/assets/images/icons/comment-icon.svg" alt="">
      </button>
    </div>
  </template>

<script>
import { generateS3DownloadingUrl, getAllRevisionDocuments } from '@/api'
import loadingCircle from '../common/loadingCircle.vue'
import { Duration } from '@/utils/date'
import CommentPanel from './commentPanel.vue'
import Config from '../../config'
import { parseCSV } from '@/utils/dataHandler'
export default {
  components: { loadingCircle, CommentPanel },
  name: 'attachedDocView',
  filters: {
    duration (value) {
      return Duration(value)
    }
  },
  props: {
    linkedDocs: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      revisionList: [],
      loading: false,
      blobKeyURlMapping: {},
      selectedIndex: 0,
      openComment: false,
      fileTypes1: ['odt', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
      fileTypes2: ['pdf', 'html', 'gif', 'mp4', 'webm', 'ogg', 'mp3', 'wav'],
      fileTypes3: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp', 'svg', 'ico'],
      encodeURIComponent: encodeURIComponent,
      blobKeyThumbURlMapping: {},
      csv: {
        headers: [],
        rows: []
      },
      pdfUrl: null
    }
  },
  computed: {
    getRootFile () {
      return this.revisionList.find((item) => item.version_of === null)
    },
    getSelectedFile () {
      if (this.revisionList[this.selectedIndex]?.doc_ext === 'csv') {
        this.loadCSV(this.revisionList[this.selectedIndex])
      } else if (this.revisionList[this.selectedIndex]?.doc_ext === 'pdf') {
        this.loadPDF(this.revisionList[this.selectedIndex])
      }
      return this.revisionList[this.selectedIndex]
    }
  },
  methods: {
    loadS3Url (items) {
      const S3Objects = items.map((item) => {
        return {
          fileName: item?.doc_name,
          S3Key: item?.blob_key
        }
      })
      const S3ThumbNailObjects = items.filter(doc => doc.thumbnail_blob_key).map((item) => {
        return {
          fileName: item?.doc_name,
          S3Key: item?.thumbnail_blob_key
        }
      })
      generateS3DownloadingUrl({
        S3Objects
      }).then((res) => {
        const obj = {}
        res.url.forEach((item) => {
          obj[item.S3Key] = item.url
        })
        this.blobKeyURlMapping = obj
      })
      generateS3DownloadingUrl({
        S3Objects: S3ThumbNailObjects
      }).then((res) => {
        const obj = {}
        res.url.forEach((item) => {
          obj[item.S3Key] = item.url
        })
        this.blobKeyThumbURlMapping = obj
      })
    },
    selectRevision (index) {
      this.selectedIndex = index
    },
    async getLatestVersionOfEachFiles () {
      const promiseArray = []
      this.linkedDocs.forEach(element => {
        const DocId = element.id ? element.id : element.document_id
        promiseArray.push(getAllRevisionDocuments(DocId))
      })
      const allRevisionData = await Promise.all(promiseArray)
      this.revisionList = allRevisionData.map(element => {
        //  we dont need to show  the doc which is in  checkout state , (only in version)
        if (element.core_documents[0].state === Config.DOC_STATE_MAP.CHECKOUT) {
          // this condtion is for  when a doc created it will be in check in state and it become the latest version , then we can checkout the same one
          //  if there is no  associated version then need to follow given codition (check how the data is comming )
          return element.core_documents.length === 1 ? element.core_documents[0] : element.core_documents[1]
        } else {
          return element.core_documents.at(-1)
        }
      })
      this.loadS3Url(this.revisionList)
    },
    async loadCSV (selectedFile) {
      const url =
        this.blobKeyURlMapping[
          selectedFile.inherited_from_doc_id
            ? selectedFile.inherited_from_document?.blob_key
            : selectedFile?.blob_key
        ]
      if (!url) {
        return
      }
      try {
        const response = await fetch(url)
        const data = await response.text()
        this.handleCSV(data)
      } catch (error) {
        console.error('Error loading CSV:', error)
      }
    },
    handleCSV (csvData) {
      const { headers, rows } = parseCSV(csvData)
      this.csv.headers = headers
      this.csv.rows = rows
    },
    async loadPDF (selectedFile) {
      const url =
        this.blobKeyURlMapping[
          selectedFile.inherited_from_doc_id
            ? selectedFile.inherited_from_document?.blob_key
            : selectedFile?.blob_key
        ]
      if (!url) {
        return
      }
      try {
        const response = await fetch(url)
        const data = await response.blob()
        this.pdfUrl = URL.createObjectURL(data.slice(0, data.size, 'application/pdf'))
      } catch (error) {
        console.error('Error loading CSV:', error)
      }
    }
  },
  mounted () {
    this.getLatestVersionOfEachFiles()
  }
}
</script>

  <style lang="scss" scoped >
  .file-detail {
    position: fixed;
    bottom: 0;
    right: 0;
    top: 0;
    left: 0;
    z-index: 100;
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(2px);
    animation: slideIn 0.3s ease-in-out;
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    @keyframes slideIn {
      0% {
        transform: translateX(100%);
      }
      100% {
        transform: translateX(0);
      }
    }
    &-header {
      height: 40px;
      display: flex;
      align-items: center;
      padding: 0 12px;
      .close {
        cursor: pointer;
        &:hover {
          background-color: rgba(255, 255, 255, 0.3);
          border-radius: 50%;
        }
      }
      .title {
        font-size: 18px;
        font-weight: 500;
        color: var(--white);
      }
    }
    &-image-container {
      width: 100%;
      height: calc(100vh - 80px - 22vh);
      border-radius: 8px;
      img {
        max-height: 100%;
        max-width: 100%;
        object-fit: contain;
      }
      &-csvbox{
height:100%;
background-color: rgb(255, 255, 255);
width: 100%;
overflow: auto;
padding: 5px;
table{
  border-collapse: collapse;
}
 th,td {
  padding: 5px;
  border: 1px solid;
}
    }
    }
    &-corusol {
      border-radius: 8px;
      width: calc(100% - 100px);
      padding: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow-x: auto;
      &::-webkit-scrollbar {
        height: 5px;
      }
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
      }
      &::-webkit-scrollbar-thumb {
        background: var(--brand-color);
      }
      &::-webkit-scrollbar-thumb:hover {
        background: var(--brand-color-1);
      }
      &--item {
        height: 20vh;
        min-width: 18vh;
        max-width: 18vh;
        border-radius: 1vh;
        margin-right: 1.5vh;
        cursor: pointer;
        position: relative;
        transition: transform 0.3s ease-in-out;
        &:hover {
          .file-detail-corusol--item-title,
          .file-detail-corusol--item-date {
            display: block;
          }
        }
        &.active {
          border: 2px solid var(--brand-color);
          transform: scale(1.1);
        }
        &-img {
          height: 100%;
          width: 100%;
          border-radius: 1vh;
          overflow: hidden;
          img {
            height: 100%;
            width: 100%;
            object-fit: cover;
          }
        }
        &-title {
          position: absolute;
          display: none;
          bottom: 0;
          left: 0;
          right: 0;
          padding: 4px 8px;
          background-color: rgba(0, 0, 0, 0.5);
          color: var(--white);
          font-size: 14px;
          font-weight: 500;
          border-bottom-left-radius: 1vh;
          border-bottom-right-radius: 1vh;
        }
        &-date {
          position: absolute;
          display: none;
          top: 0;
          left: 0;
          right: 0;
          padding: 4px 8px;
          background-color: rgba(0, 0, 0, 0.5);
          color: var(--white);
          font-size: 14px;
          font-weight: 500;
          border-top-left-radius: 1vh;
          border-top-right-radius: 1vh;
        }
      }
    }
    &-comment {
      transition: width 0.3s ease-in-out;
      border-radius: 4px;
      height: 90vh;
      background-color: var(--white);
    }
    .comment-btn {
      position: absolute;
      bottom: 0;
      right: 0;
      margin: 12px;
      background: var(--white);
      cursor: pointer;
      border-radius: 50%;
      height: 45px;
      width: 45px;
      border: none;
      box-shadow: 4px 4px 4px 0 rgba(0,0,0, 0.25);
      & img {
        width: 30px;
        height: 30px;
      }
      &:hover {
        background: var(--brand-color);
        & img {
          filter: invert(1);
        }
      }
      &:active {
        box-shadow: inset 4px 4px 4px 0 rgba(0,0,0, 0.25);
      }
    }
  }
  </style>
