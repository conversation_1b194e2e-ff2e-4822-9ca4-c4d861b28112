<template>
    <div>
            <div class="product-bom-table" v-if="Object.keys(productsAndBoms).length">
              <div class="product-bom-table--container">
                <table>
                  <thead>
                    <tr>
                      <th>Product Code</th>
                      <th>BOMS</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="( value, key ) in productsAndBoms" :key="key">
                      <td>{{!isOnProjectLevel ? key: '' }}</td>
                      <td class="bom-name-container">
                        <span v-for="(bom, index) in value" class="boms" :key="index">
                          {{ bom }}
                          <!-- <span v-if="index < value.length - 1">, </span> -->
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div v-else-if="loading">
               <span class="value">Loading ...</span>
            </div>
            <div v-else>
              <span class="value">
                This material is not used by any   {{isOnProjectLevel?' Project level boms  ': 'bom'}}
              </span>
            </div>
          </div>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    productsAndBoms: {
      type: Object,
      default () { return ({}) }
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters(['isOnProjectLevel'])
  }
}
</script>
<style lang="scss">
.value {
    margin: 0 4px;
    font-size: 0.9em;
    color: var(--text-color);
  }
.product-bom-table {
  max-height: 220px;
  margin-top: 10px;
  &--container {
    max-height: 220px;
    overflow: auto;

    table {
      width: 100%;
      position: relative;
      border-collapse: collapse;

      th {
        position: sticky;
        top: -1px;
        font-weight: 500;
        background-color: var(--brand-color);
      }

      th,
      td {
        text-align: left;
        font-size: 12px;
        padding: 8px 4px;
      }

      tr:nth-child(odd) {
        background-color: rgba(var(--brand-rgb), 0.05);
        border: 1px solid var(--brand-color);
      }

      .chip {
        padding: 2px 8px;
        font-size: 10px;
        background-color: rgba(var(--brand-rgb), 0.5);
        border-radius: 2px;
      }
    }
  }
}

.boms {
    text-decoration: none;
    border: .1px solid rgb(111, 111, 111, .3);
    padding-inline: 5px;
    padding-block: 3px;
    border-radius: 3px;
}
.bom-name-container{
  display: flex;
flex-wrap: wrap;
gap:5px
}
</style>
