<template>
    <table class="calendar-table">
      <thead>
        <tr class="m">
          <th>Calendar Name</th>
          <th>Project Name</th>
          <th>Description</th>
          <th>Work Days</th>
          <th>Work Time Hours</th>
          <!-- <th></th> -->
          <th v-show="isTenantAdmin">Action</th>
        </tr>
      </thead>
      <tbody v-if="!loading">
        <tr class="s">
          <td>{{ selectedCalendar?.name || '--' }}</td>
          <td>{{ selectedCalendar?.core_project?.name || '--' }}</td>
          <td>{{ selectedCalendar?.description || '--' }}</td>
          <td>
      <span v-if="selectedCalendar?.calendar_working_days && selectedCalendar.calendar_working_days.length > 0">
        <span v-for="(workingDay, index) in selectedCalendar.calendar_working_days" :key="index">
          <!-- Only display days where deleted is false -->
          <span v-if="!workingDay.deleted">
            {{ workingDay.day_name }}
            <!-- Add a comma or other separator if needed -->
            <span v-if="index < selectedCalendar.calendar_working_days.length - 1">, </span>
          </span>
        </span>
      </span>
      <span v-else>--</span>
    </td>
          <td>{{ selectedCalendar?.working_hours || '--' }}</td>
          <td v-show="isTenantAdmin">
            <button @click="editItem" class="btn">Edit</button>
          </td>
        </tr>
      </tbody>
      <tbody v-else>
        <tr>
          <td colspan="6">
            <div class="center">
              <loading-circle />
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </template>

<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    selectedCalendar: {
      type: Object,
      required: true
    }
  },
  watch: {
  },
  data () {
    return {
      loading: false
    }
  },
  methods: {
    editItem () {
      this.$emit('edit', this.selectedCalendar)
    },
    deleteItem () {
      // Handle delete action for the item
    }
  },
  computed: {
    ...mapGetters([
      'isOnProjectLevel', 'user'
    ]),
    isTenantAdmin () {
      return this.user.projectLevelRole === 'ADMIN'
    }
  }
}
</script>

<style lang="scss" scoped >
.calendar-table {
  width: 100%;
  font-size: 16px;
  border-collapse: collapse;
  position: relative;

  th {
    background: var(--brand-color);
    font-weight: 500;
    padding: 12px 4px;
    position: sticky;
    top: 0;
  }

  td {
    border-bottom: 1px solid var(--brand-color);
  }

  th,
  td {
    text-align: center;
    padding: 8px 4px;

    &:nth-child(1) {
      width: 50px;
    }
  }
}</style>
