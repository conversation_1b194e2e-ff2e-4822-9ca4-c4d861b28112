<template>
  <div class="form-preview">
    <div class="form-preview-header">
      <div class="form-preview-header-title">
        <div class="form-preview-header-title-name">{{ formName }}</div>
        <div class="form-preview-header-title-type">{{ formType }}</div>
      </div>
    </div>
    <div class="form-preview-autogenerated">
      <template v-for="(ele, index) in formTemplateBody">
        <div :key="index" v-if="ele.autogenerated && ele.caption !== 'Project Id'">
          <span class="label">{{ ele.caption }}:</span>
          <span class="value">--</span>
        </div>
      </template>
    </div>
    <template v-for="(ele, index) in formTemplateBody">
      <component
        v-if="!ele.autogenerated"
        :key="index"
        :is="ele.key + '_COMPONENT'"
        :data="ele"
        :viewOnly="true"
      />
    </template>
    <!-- // no need fo save or cancel buttons  -->
    <!-- <div class="v-center flex-end">
      <button class="btn btn-black mx-2">Cancel</button>
      <button class="btn btn">Save</button>
    </div> -->
  </div>
</template>

<script>
import timeComponent from '@/components/form/elements/timeComponent.vue'
import configurationList from './elements/configurationList.vue'
import fileComponent from './elements/fileComponent.vue'
import dateComponent from '@/components/form/elements/dateComponent.vue'
import longTextComponent from '@/components/form/elements/longTextComponent.vue'
import numberComponent from '@/components/form/elements/numberComponent.vue'
import switchComponent from '@/components/form/elements/switchComponent.vue'
import textComponent from '@/components/form/elements/textComponent.vue'
import companyComponent from '@/components/form/elements/companyComponent.vue'
import userComponent from '@/components/form/elements/userComponent.vue'
import multiUserComponent from '@/components/form/elements/multiUserComponent.vue'
import TagsComponent from '@/components/form/elements/tagsComponent.vue'
import materialComponent from './elements/materialComponent.vue'
import locationComponent from './elements/locationComponent.vue'
import productCodeComponent from './elements/productCodeComponent.vue'
import documentsComponent from './elements/documentsComponent.vue'
import bomComponent from './elements/bomComponent.vue'

export default {
  name: 'FormPreview',
  components: {
    BOOLEAN_COMPONENT: switchComponent,
    NUMBER_COMPONENT: numberComponent,
    TEXT_COMPONENT: textComponent,
    LONG_TEXT_COMPONENT: longTextComponent,
    DATE_COMPONENT: dateComponent,
    TIME_COMPONENT: timeComponent,
    CONFIGURATION_LIST_COMPONENT: configurationList,
    ATTACHMENT_COMPONENT: fileComponent,
    COMPANY_COMPONENT: companyComponent,
    USER_COMPONENT: userComponent,
    MULTI_USER_COMPONENT: multiUserComponent,
    MATERIALS_COMPONENT: materialComponent,
    LOCATION_COMPONENT: locationComponent,
    PRODUCT_CODE_COMPONENT: productCodeComponent,
    BOM_COMPONENT: bomComponent,
    TAGS_COMPONENT: TagsComponent,
    DOCUMENTS_COMPONENT: documentsComponent
  },
  props: {
    formName: {
      type: String
    },
    formType: {
      type: String
    },
    formTemplateBody: {
      type: Array
    }
  },
  data () {
    return {}
  }
}
</script>

<style lang="scss" scoped >
.form-preview {
  &-header {
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 14px;
    margin-bottom: 10px;
    &-title {
      &-name {
        font-size: 20px;
        font-weight: 500;
      }
      &-type {
        font-size: 14px;
        font-weight: 500;
        color: #a0a0a0;
      }
    }
  }
  &-autogenerated {
    background-color: rgba(var(--brand-rgb), 0.2);
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    grid-gap: 10px;
    & .label {
      font-size: 14px;
      color: var(--brand-color-1);
      font-weight: 500;
    }
    & .value {
      margin-left: 6px;
      font-size: 16px;
      color: var(--text-color);
    }
  }
}
</style>
