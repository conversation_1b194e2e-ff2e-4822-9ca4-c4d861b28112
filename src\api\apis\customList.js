import { runMutation, runQuery } from '../graphQl'
import * as customListQuery from '../query/customList'

export const CreateCustomList = (data, name) => {
  return runMutation(customListQuery.CreateCustomListQuery(), { data, name }, 'tenant')
}

export const GetActiveCustomLists = () => {
  return runMutation(customListQuery.GetActiveCustomListsQuery(), {}, 'tenant')
}

export const GetCustomList = (id) => {
  return runMutation(customListQuery.GetCustomListQuery(), { id }, 'tenant')
}
export const getGivenCustomListsData = (ids) => { // expecting an array of customlist ids
  return runMutation(customListQuery.GetCustomListsQuery(), { ids }, 'tenant')
}

export const GetTaskStatus = () => {
  return runQuery(customListQuery.GetTaskStatusQuery(), {}, 'tenant')
}

export const getMaterialAttatchedCount = (id) => {
  return runQuery(customListQuery.getAttachedMaterialCountQuery(), { id }, 'tenant')
}

export const GetResourceGroupCount = (id) => {
  return runQuery(customListQuery.GetResourceGroupCountQuery(), { id }, 'tenant')
}

export const ResourceStateValueCount = (id) => {
  return runQuery(customListQuery.ResourceStateValueCountQuery(), { id }, 'tenant')
}

export const MaterialTypeCount = (id) => {
  return runQuery(customListQuery.MaterialTypeCountQuery(), { id }, 'tenant')
}

export const GetUnitOfMaterialCount = (id) => {
  return runQuery(customListQuery.GetUnitOfMaterialCountQuery(), { id }, 'tenant')
}

export const GetTaskInStatusCount = (id) => {
  return runQuery(customListQuery.getStatusAssociatedTaskCount(), { id }, 'tenant')
}

export const ResourceState = () => {
  return runQuery(customListQuery.ResourceStateQuery(), {}, 'tenant')
}

export const DeleteCustomList = (id) => {
  return runMutation(customListQuery.DeleteCustomListQuery(), { id }, 'tenant')
}

export const UpdateCustomList = (id, data) => {
  return runMutation(customListQuery.UpdateCustomListQuery(), { id, data }, 'tenant')
}

export const CreateCustomListValues = (data) => {
  return runMutation(customListQuery.CreateCustomListValuesQuery(), { data }, 'tenant')
}

export const UpdateCustomListValues = (id, data) => {
  return runMutation(customListQuery.UpdateCustomListValuesQuery(), { id, data }, 'tenant')
}
export const DeleteCustomListValues = (id) => {
  return runMutation(customListQuery.DeleteCustomListValuesQuery(), { id }, 'tenant')
}
export const ChangeDefaultCustomValue = (oldId, newId) => {
  return runMutation(customListQuery.changeDefaultValueQuery(), { oldId, newId }, 'tenant')
}
export const UpdateUOMCustomValue = (oldId, newId) => {
  return runMutation(customListQuery.UpdateUnitOfMaterialQuery(), { oldId, newId }, 'tenant')
}
export const validateCustomList = (name) => {
  return runQuery(customListQuery.validateCustomListQuery(), { name }, 'tenant')
}

export const UpdateMaterialCustomListValue = (oldId, newId) => {
  return runMutation(customListQuery.materialCustomListUpdateQuery(), { oldId, newId }, 'tenant')
}
