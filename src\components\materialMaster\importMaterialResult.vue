<template>
  <div class="import-result">
    <h1 class="weight-500 xl mb-2">Publishing Your Material Master Data</h1>
    <div class="import-result-kpi">
      <div class="import-result-kpi-item">
        <div class="import-result-kpi-item-label">Total</div>

        <div class="import-result-kpi-item-value">
          {{ publishingRes.total }}
        </div>
      </div>
      <div class="import-result-kpi-item">
        <div class="import-result-kpi-item-label">Done</div>

        <div class="import-result-kpi-item-value">
          {{ publishingRes.count }}
        </div>
      </div>
      <div class="import-result-kpi-item">
        <div class="import-result-kpi-item-label">Success</div>

        <div class="import-result-kpi-item-value">
          {{ publishingRes.success }}
        </div>
      </div>
      <div class="import-result-kpi-item">
        <div class="import-result-kpi-item-label">Failed</div>

        <div class="import-result-kpi-item-value">
          {{ publishingRes.error }}
        </div>
      </div>
    </div>
    <div class="import-result-table">
      <table class="table">
        <thead>
          <tr>
            <th>Material ID</th>
            <th>Material Name</th>
            <th>ERP Material ID</th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in publishingRes.data" :key="item.custom_material_id">
            <td>{{ item.data.custom_material_id }}</td>
            <td>{{ item.data.material_name }}</td>
            <td>{{ item.data.plm_material_id }}</td>
            <td>
              <div
                v-if="item.loading"
                class="import-result-table-status loading xs"
              >
                <loading-circle />
              </div>
              <div
                v-if="item.done && !item.error"
                class="import-result-table-status success"
              >
                <img
                  title="Success"
                  src="~@/assets/images/check.png"
                  alt="check"
                />
              </div>
              <div
                v-if="item.done && item.error"
                class="import-result-table-status error"
              >
                <img
                  :title="item.errorMessage"
                  src="~@/assets/images/close.png"
                  alt="close"
                />
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="v-center flex-end pt-3">
      <button class="btn" @click="$emit('close')">Close</button>
    </div>
  </div>
</template>

<script>
import loadingCircle from '../common/loadingCircle.vue'
export default {
  components: { loadingCircle },
  props: {
    publishingRes: {
      type: Object,
      required: true
    }
  }
}
</script>

<style lang="scss" scoped >
.import-result {
  &-kpi {
    display: flex;
    justify-content: space-between;
    grid-gap: 10px;
    &-item {
      width: 25%;
      padding: 6px 20px;
      border-radius: 6px;
      background-color: rgba(var(--brand-rgb), 0.1);
      &-value {
        width: 100%;
        font-size: 16px;
        font-weight: 500;
        text-align: center;
      }
      &-label {
        width: 100%;
        font-size: 14px;
        font-weight: 600;
        color: var(--brand-color-1);
      }
    }
  }
  .import-result-table {
    margin-top: 20px;
    max-height: 300px;
    overflow-y: auto;
    position: relative;
    table {
      width: 100%;
      border-collapse: collapse;
      thead {
        position: sticky;
        top: 0;
        z-index: 1;
      }
      tr:nth-child(even) {
        border: 1px solid var(--brand-color);
        background-color: rgba(var(--brand-rgb), 0.1);
      }
      th {
        background-color: var(--brand-color);
        color: var(--text-color);
      }
      th,
      td {
        padding: 8px;
        text-align: left;
        font-size: 12px;
      }
      th {
        padding: 6px 4px;
        font-weight: 600;
        &:first-child {
          width: 100px;
        }
        &:nth-child(2) {
          width: 200px;
        }
        &:nth-child(3) {
          width: 120px;
        }
        &:last-child {
          width: 50px;
        }
      }
    }
    .import-result-table-status {
    }
    img {
      width: 16px;
    }
  }
}
</style>
<style lang="scss">
.import-result-table-status {
  .loading-circle {
    font-size: 18px;
  }
}
</style>
