import {
  GetAllProjectBomList
} from '@/api'
export default {
  namespaced: true,
  state: {
    selectedBomId: null,
    bomList: [],
    bomListLoading: false,
    selectedBomVersionId: null,
    bomSearchKeyword: ''
  },
  getters: {
    selectedBomId: state => state.selectedBomId,
    bomList: state => state.bomList,
    bomListLoading: state => state.bomListLoading,
    selectedBomVersionId: state => state.selectedBomVersionId,
    bomSearchKeyword: state => state.bomSearchKeyword
  },
  mutations: {
    SET_SELECTED_BOM_ID (state, payload) {
      state.selectedBomId = payload.bom_id || null
    },
    SET_BOM_LIST (state, payload) {
      state.bomList = payload
    },
    SET_BOM_LIST_LOADING (state, payload) {
      state.bomListLoading = payload
    },
    SET_SELECTED_BOM_VERSION_ID (state, payload) {
      state.selectedBomVersionId = +payload.bom_version_id || null
    },
    SET_BOM_SEARCH_KEYWORD (state, payload) {
      state.bomSearchKeyword = payload
    }
  },
  actions: {
    setBaseData ({ commit }, payload) {
      commit('SET_SELECTED_BOM_ID', payload)
      commit('SET_SELECTED_BOM_VERSION_ID', payload)
    },
    setSelectedBomId ({ commit }, payload) {
      commit('SET_SELECTED_BOM_ID', payload)
    },
    setSelectedBomVersionId ({ commit }, payload) {
      commit('SET_SELECTED_BOM_VERSION_ID', payload)
    },
    getBomList ({ commit, rootState }) {
      commit('SET_BOM_LIST_LOADING', true)
      GetAllProjectBomList().then((res) => {
        commit('SET_BOM_LIST', res.core_bom)
      }).finally(() => {
        commit('SET_BOM_LIST_LOADING', false)
      })
    },
    addBomSearchKeyword ({ commit }, payload) {
      commit('SET_BOM_SEARCH_KEYWORD', payload)
    }
  }
}
