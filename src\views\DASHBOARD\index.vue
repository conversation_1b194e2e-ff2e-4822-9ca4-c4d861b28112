<template>
  <div class="dashboard">
    <component :is="getComponent" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import beaconAdmin from './beaconAdmin.vue'
import tenantAdmin from './tenant.vue'
export default {
  components: { beaconAdmin, tenantAdmin },
  name: 'dashboard',
  computed: {
    ...mapGetters(['isBeaconAdmin']),
    getComponent () {
      if (this.isBeaconAdmin) {
        return 'beaconAdmin'
      } else {
        return 'tenantAdmin'
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
