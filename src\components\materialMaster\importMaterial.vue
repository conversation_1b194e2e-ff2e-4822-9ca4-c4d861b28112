<template>
  <div class="import-material">
    <div v-if="!mapping">
      <input
        ref="fileUpload"
        type="file"
        id="file-upload"
        @change="readCsvFile"
      />
      <label for="file-upload" v-if="notUploaded" class="h-center">
        <span>Upload your <b>.csv</b> file</span>
      </label>
      <p v-else-if="loading" class="h-center">
        <span
          >Uploading {{ fileName }} {{ loadedFilesSize }}/{{ fileSize }}</span
        >
      </p>
      <p v-else-if="hasError" class="h-center">
        <span>{{ errorMessage }}</span>
      </p>
      <p v-else class="h-center">
        <span
          >{{ fileName }} uploaded,
          <span @click.stop="removeFile">Click here</span> to remove file</span
        >
      </p>
      <div class="v-center flex-end pt-3">
        <button class="btn btn-black mx-3" @click="$emit('close')">
          Cancel
        </button>
        <button class="btn" :disabled="!(fileData && fileData.length)" @click="mapping = true">
          Next
        </button>
      </div>
    </div>
    <div v-if="mapping && !publishingFile">
      <material-mapping :fileData="fileData" @back="mapping = false" @save="saveMappingObject" />
    </div>
    <div v-if="publishingFile">
      <import-material-result :publishingRes="publishingRes" @close="$emit('close')" />
    </div>
  </div>
</template>

<script>
import materialMapping from './materialMapping.vue'
import { bulkUpload } from '@/api/materialMaster'
import ImportMaterialResult from './importMaterialResult.vue'
export default {
  components: { materialMapping, ImportMaterialResult },
  data () {
    return {
      notUploaded: true,
      loading: false,
      hasError: false,
      errorMessage: '',
      fileData: null,
      fileName: null,
      fileSize: 0,
      loadedFilesSize: 0,
      mapping: false,
      publishingFile: false,
      publishingRes: null
    }
  },
  methods: {
    readCsvFile (e) {
      const file = e.target.files[0]
      const reader = new FileReader()
      this.notUploaded = false
      this.fileName = file.name
      this.fileSize = file.size
      this.loading = true
      reader.onloadstart = () => {
        this.hasError = false
      }
      reader.onprogress = (e) => {
        this.loadedFilesSize = e.loaded
      }
      reader.onload = (e) => {
        this.loadedFilesSize = e.loaded
        this.readDone(e.target.result)
      }
      reader.onerror = (e) => {
        this.errorMessage = 'Something went wrong'
        this.hasError = true
        this.loading = false
      }
      if (file.type === 'text/csv') {
        reader.readAsText(file)
      } else {
        this.errorMessage = 'Please upload a valid csv file'
        this.hasError = true
        this.loading = false
      }
    },
    readDone (filedata) {
      try {
        const csv = filedata
        const lines = csv.split('\n')
        const result = []
        const headers = lines[0].split(',')
        for (let i = 1; i < lines.length; i++) {
          const obj = {}
          const currentline = lines[i].split(',')
          for (let j = 0; j < headers.length; j++) {
            const key = headers[j].replace(/"/g, '')
            if (currentline[j]) {
              obj[key] = true
            } else if (currentline[j] === 'false') {
              obj[key] = false
            } else if (currentline[j] === 'null') {
              obj[key] = null
            } else if (currentline[j] === 'undefined') {
              obj[key] = undefined
            } else if (currentline[j] === '') {
              obj[key] = null
            } else if (!isNaN(currentline[j])) {
              obj[key] = Number(currentline[j])
            } else {
              obj[key] = currentline[j].replace(/"/g, '')
            }
          }
          result.push(obj)
        }
        this.fileData = result
        this.loading = false
        this.hasError = false
      } catch (e) {
        this.errorMessage = 'Please upload a valid csv file'
        this.hasError = true
        this.loading = false
      }
      // remove selected file
      this.$refs.fileUpload.value = ''
    },
    removeFile () {
      this.notUploaded = true
      this.loading = false
      this.hasError = false
      this.fileData = null
      this.fileName = null
      this.fileSize = 0
      this.loadedFilesSize = 0
    },
    saveMappingObject (mappingObject) {
      this.publishingFile = true
      const requestList = []
      mappingObject.forEach((item) => {
        requestList.push({
          data: item,
          loading: false,
          done: false,
          error: false,
          errorMessage: ''
        })
      })
      bulkUpload(requestList, (res) => {
        this.publishingRes = res
      })
    }
  }
}
</script>

<style lang="scss" scoped >
.import-material {
  width: 600px;
  input {
    display: none;
  }
  label,
  p {
    padding: 20px;
    border: 1px dashed #ccc;
    border-radius: 4px;
    cursor: pointer;
    &:hover {
      background: #eee;
    }
  }
}
</style>
