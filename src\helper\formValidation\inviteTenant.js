import {
  emptyString,
  noSpecialChars,
  email,
  length,
  RegexValidator,
  isValidLatitudeTenant,
  isValidLongitudeTenant,
  checkTrue
} from './index'
import { alert } from '@/plugins/notification'
export default (body) => {
  let count = 0
  if (!emptyString(body.firstName, 'First Name', 'FirstName', false)) {
    count++
  }
  if (!emptyString(body.lastName, 'Last Name', 'LastName', false)) {
    count++
  }
  if (!noSpecialChars(body.firstName, 'First Name', 'FirstName')) {
    count++
  }
  if (!emptyString(body.email, 'Email', 'Email', false)) {
    count++
  }
  if (!email(body.email, 'Email', 'Email', false)) {
    count++
  }
  if (!emptyString(body.companyName, 'Company Name', 'CompanyName', false)) {
    count++
  }
  if (!email(body.companyEmail, 'Company Email', 'CompanyEmail', false)) {
    count++
  }
  if (!length(body.companyPhone, 'Phone Number', 6, 10, 'CompanyPhoneNumber', false)) {
    count++
  }
  if (!RegexValidator(body.GSTIN, '[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}', 'Invalid Company Gst Number', 'CompanyGstNumber', false)) {
    count++
  }
  if (!RegexValidator(body.PAN, '[A-Z]{5}[0-9]{4}[A-Z]{1}', 'Company PAN Number', 'CompanyPanNumber', false)) {
    count++
  }
  if (!isValidLatitudeTenant(body.latitude, 'Latitude', 'Latitude', false)) {
    count++
  }
  if (!isValidLongitudeTenant(body.longitude, 'Longitude', 'Longitude', false)) {
    count++
  }
  if (!checkTrue(body.industryVertical, 'industry', 'Industry', false)) {
    count++
  }
  if (count > 0) {
    alert('Please fill required fields')
    return false
  }
  return count === 0
}
