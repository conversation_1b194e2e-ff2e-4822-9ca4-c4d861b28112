<template>
    <div v-if="loading" class="loader center s">
      <loading-circle />
    </div>
    <div v-else class="forecast" >
      <div class="forecast-header space-between" >
        <div>
          <h1 class="weight-500 xxxl">Material Demand Forecast</h1>
        </div>
        <span v-if="isOnProjectLevel" >
          <span class="mx-3">
            <button
              class="toggle toggle-left pointer toggle-selected"
            >
              Forecast
            </button>
            <router-link
            :to="'/trendsandforecast/copy-resource-trends'"
            >
              <button
              class=" toggle toggle-right pointer"
            >
              Trends
            </button>
            </router-link>
          </span>
          <button class="btn "  v-if="materialData" @click="exportToCsv">Export CSV</button>
        </span>
      </div>
      <div class="forecast-filters">
            <multiselect-dropdown
            class="select-dropdown"
              label="Projects"
              :options="projects"
              @selected="setProjectFilters"
              :initiallySelected="appliedFilters.projectIds"
            />
            <multiselect-dropdown
             class="select-dropdown"
              label="Material Group"
              :options="list_of_material_group"
              @selected="setMaterialGroupFilters"
              :initiallySelected="appliedFilters.materialGroupIds"
            />
            <div class="date-range">
              <span>From:</span>
              <input
                type="date"
                class="ml-3"
                v-model="filters.from"
                @change="dateValidation"
              />
              <span class="ml-3">Upto:</span>
              <input
                type="date"
                class="ml-3"
                v-model="filters.upto"
                @change="applyButton = false"
                :min="filters.from"
              />
            </div>
            <div class="flex gap-1">
              <button class="btn btn-black button pointer" @click="resetFilters">Reset</button>
            <button class="btn button pointer" :disabled="applyButton" @click="applyFilters">
              Apply
            </button>
            </div>
      </div>
      <div v-if="!selectedMaterial" class="forecast-empty h-center v-center mt-5">
    <h4>No material required over the given period</h4>
    </div>
      <div class="forecast-container" v-else>
        <div class="copy-dtx-table forecast-table" v-if="materialData">
          <table>
          <thead style="z-index: 3;">
            <tr>
              <th v-overflow-tooltip>Material ID</th>
              <th v-overflow-tooltip>Material Name</th>
              <th v-overflow-tooltip>Product Code</th>
              <th v-overflow-tooltip>Inventory</th>
              <th v-overflow-tooltip>Quantity</th>
              <th v-overflow-tooltip>UOM</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(material) in materialData" :key="material.material_id" @click="setupChartView(material.material_id,material?.material_name)" :class="{'selectedMaterial':selectedMaterial===material.material_id}">
              <td v-overflow-tooltip>{{  material?.custom_material_id || '--' }}</td>
              <td v-overflow-tooltip>{{  material?.material_name || '--' }}</td>
              <td v-overflow-tooltip>{{  material?.product_code || '--' }}</td>
              <td v-overflow-tooltip>{{ material?.inventory || '--'  }}</td>
              <td v-overflow-tooltip>{{  material.quantity || '--' }}</td>
              <td v-overflow-tooltip>{{  material?.unit_of_material || '--' }}</td>
            </tr>
          </tbody>
        </table>
        </div>
        <i v-else>no materials are available...</i>
        <div class="forecast-container-graph px-2 v-center h-center">
          <div  v-if="chartLoading">
          </div>
      <div v-else-if="selectedMaterial" class="hw-100 v-center column">
        <label> {{ materialData[selectedMaterial].material_name }}</label>
        <apexchart
          class="lineCharts-single"
          type="line"
          :options="chartOptions"
          :series="materialSeries"
        />
      </div>
      </div>
     </div>
   </div>
  </template>

<script>
/* eslint-disable no-unmodified-loop-condition */
import MultiselectDropdown from '../../components/common/multiselectDropdown.vue'
import { mapGetters } from 'vuex'
import { MaterialGroup, getDemandForecastData } from '@/api'
import loadingCircle from '../../components/common/loadingCircle.vue'
import { getProjectUserAssociation } from '../../api/apis/userFlow'
import { arrayToCsv } from '@/helper/file/arrayToCsv'

export default {
  name: 'MaterialForecast',
  components: {
    MultiselectDropdown,
    loadingCircle
  },
  methods: {
    resetFilters () {
      this.filters = {
        from: '',
        upto: '',
        projectIds: [],
        materialGroupIds: []
      }
      this.setInitialDates()
      this.applyFilters()
    },
    applyFilters () {
      try {
        this.chartOptions.xaxis.categories = []
        this.materialSeries = []
        this.selectedMaterial = null
        this.materialData = {}
        this.materialList = []
        this.loading = true
        const body = {
          projectIds: this.filters.projectIds,
          materialGroupIds: this.filters.materialGroupIds,
          startDate: new Date(this.filters.from),
          endDate: new Date(this.filters.upto)
        }
        getDemandForecastData(body).then((res) => {
          this.materialList = JSON.parse(JSON.stringify(res.message.materialForecast))
          res.message.materialForecast.forEach((material) => {
            if (Object.hasOwn(this.materialData, material.material_id)) {
              this.materialData[material.material_id].quantity += material.quantity
            } else {
              this.materialData[material.material_id] = material
            }
          })
          this.appliedFilters = this.filters
          this.generateDatesInRange(new Date(this.filters.from), new Date(this.filters.upto))
          this.selectedMaterial = Object.keys(this.materialData)[0]
          this.selectedMaterial && this.setupChartView(this.materialData[this.selectedMaterial].material_id, this.materialData[this.selectedMaterial]?.material_name)
          this.loading = false
        })
      } catch (error) {
        this.loading = false
      }
    },
    setProjectFilters (projectIds) {
      this.filters.projectIds = projectIds
      this.applyButton = false
    },
    setMaterialGroupFilters (materialGrps) {
      this.filters.materialGroupIds = materialGrps
      this.applyButton = false
    },
    getOneMonthAfterDate (currentDate) {
      const oneMonthAfter = new Date(currentDate)
      oneMonthAfter.setMonth(oneMonthAfter.getMonth() + 1)
      return this.formatDate(oneMonthAfter)
    },
    generateDatesInRange (startDate, endDate) {
      const dates = {}
      const options = {
        year: '2-digit',
        month: '2-digit',
        day: '2-digit'
      }
      const currentDate = new Date(startDate)
      let i = 0
      while (currentDate <= endDate) {
        const date = currentDate.toLocaleString('en-GB', options)
        dates[new Date(currentDate).toISOString().split('T')[0]] = i
        this.chartOptions.xaxis.categories.push(date)
        currentDate.setDate(currentDate.getDate() + 1)
        i++
      }
      return dates
    },
    formatDate (date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    setInitialDates () {
      const currentDate = new Date()
      const currentFormattedDate = this.formatDate(currentDate)
      const oneMonthAfterFormattedDate =
          this.getOneMonthAfterDate(currentDate)
      this.filters.from = currentFormattedDate
      this.filters.upto = oneMonthAfterFormattedDate
    },
    getProjectsWithAdminRole () {
      if (this.user.tenantLevelRole === 'ADMIN' || this.user.tenantLevelRole === 'EDITOR' || this.user.tenantLevelRole === 'VIEWER' || this.user.tenantLevelRole === 'COLLABORATOR') {
        this.tenantProjectList.forEach((item) => {
          this.projects.push({
            value: item?.id,
            label: item?.name
          })
        })
      } else {
        getProjectUserAssociation(this.user.userId).then((response) => {
          response.project_user_association.forEach((element) => {
            if (element.associated_project) {
              this.projects.push({
                value: element?.associated_project?.id,
                label: element?.associated_project?.name
              })
            }
          })
        })
      }
    },
    listOfMaterialGroup () {
      MaterialGroup().then((res) => {
        this.list_of_material_group = res.custom_list_values.map((item) => {
          return {
            label: item.name,
            value: item.id
          }
        })
      })
    },
    setupChartView (materialId, materialName) {
      this.chartLoading = true
      this.selectedMaterial = materialId
      const chartListMaterials = this.materialList.filter((material) => material.material_id === materialId)
      this.materialSeries = [{ name: 'Forecasted Qty', data: [] }, { name: 'Current Inventory', data: [] }, { name: 'Projected Inventory', data: [] }]
      this.chartOptions.xaxis.categories = []
      const dates = this.generateDatesInRange(
        new Date(this.appliedFilters.from),
        new Date(this.appliedFilters.upto)
      )
      const numberOfDates = Object.keys(dates).length
      this.materialSeries[0].data = new Array(
        numberOfDates
      ).fill(0)
      this.materialSeries[1].data = new Array(
        numberOfDates
      ).fill(this.materialData[this.selectedMaterial].inventory ?? 0)

      chartListMaterials.forEach((material) => {
        const index = dates[new Date(material.required_date).toISOString().split('T')[0]]
        this.materialSeries[0].data[index] = material.quantity
      })
      let inventory = this.materialData[this.selectedMaterial].inventory ?? 0
      this.materialSeries[0].data.forEach((qty, index) => {
        const projectedInventory = inventory - qty
        // if (projectedInventory > 0) {
        this.materialSeries[2].data[index] = projectedInventory
        // } else {
        //   this.materialSeries[2].data[index] = 0
        // }
        inventory -= qty
      })
      this.chartLoading = false
    },
    exportToCsv () {
      const materialCsv = []
      let projectNames = ''
      let MaterialGroupNames = ''
      for (const id in this.materialData) {
        this.materialData[id]['Material ID'] = this.materialData[id].custom_material_id
        this.materialData[id]['Material Name'] = this.materialData[id].material_name
        this.materialData[id]['Product Code'] = this.materialData[id].product_code
        this.materialData[id].Quantity = this.materialData[id].quantity
        this.materialData[id]['Unit of Material'] = this.materialData[id].unit_of_material
        materialCsv.push(this.materialData[id])
      }
      if (this.appliedFilters.projectIds.length > 0) {
        for (let i = 0; i < this.appliedFilters.projectIds.length; i++) {
          for (let j = 0; j < this.projects.length; j++) {
            if (this.appliedFilters.projectIds[i] === this.projects[j].value) {
              projectNames = projectNames + this.projects[j]?.label + ',   '
              break
            }
          }
        }
      } else {
        this.projects.forEach((project) => {
          projectNames = projectNames + project?.label + ',  '
        })
      }

      if (this.appliedFilters.materialGroupIds.length > 0) {
        for (let i = 0; i < this.appliedFilters.materialGroupIds.length; i++) {
          for (let j = 0; j < this.list_of_material_group.length; j++) {
            if (parseInt(this.appliedFilters.materialGroupIds[i]) === parseInt(this.list_of_material_group[j].value)) {
              MaterialGroupNames = MaterialGroupNames + this.list_of_material_group[j]?.label + ',  '
              break
            }
          }
        }
      } else {
        this.list_of_material_group.forEach((materialGrp) => {
          MaterialGroupNames = MaterialGroupNames + materialGrp?.label + ',  '
        })
      }
      arrayToCsv(
        materialCsv,
        ['material_id', 'required_date', 'material_name', 'product_code', 'quantity', 'unit_of_material', 'custom_material_id'],
        'material-forecast-data.csv', { To: this.chartOptions.xaxis.categories.at(-1), from: this.chartOptions.xaxis.categories[0], Material_Group: MaterialGroupNames, Projects: projectNames }
      )
    },
    dateValidation (e) {
      this.applyButton = false
      if ((new Date(e.target.value) < new Date(this.filters?.upto)) && this.filters.upto) {
      } else {
        this.filters.upto = new Date(e.target.value).toISOString().split('T')[0]
      }
    },
    keyPress (e) {
      if (this.applyButton) { return }
      if (e instanceof KeyboardEvent && e.code === 'Enter') {
        this.applyFilters()
      }
    }
  },
  data () {
    return {
      list_of_material_group: [],
      applyButton: true,
      materialSeries: [],
      chartLoading: false,
      materialData: {}, // contains materials list after calculating total quantity
      materialList: [],
      selectedMaterial: null, // contains all material data without adding quatity of same materials
      filters: {
        from: '',
        upto: '',
        projectIds: [],
        materialGroupIds: []
      },
      appliedFilters: {
        projectIds: [],
        materialGroupIds: []
      },
      loading: false,
      projects: [],
      projectSeries: [],
      chartOptions: {
        chart: {
          width: 900,
          height: 350,
          type: 'line',
          zoom: {
            enabled: true
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'smooth',
          dashArray: [0, 5]
        },
        title: {
          text: '',
          align: 'left'
        },
        grid: {
          row: {
            colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
            opacity: 0.5
          }
        },
        xaxis: {
          categories: []
        }
      }
    }
  },
  computed: {
    ...mapGetters(['user', 'getUserById', 'isOnProjectLevel']),
    ...mapGetters(['tenantUsersList', 'tenantProjectList'])
  },
  mounted () {
    this.setInitialDates()
    this.getProjectsWithAdminRole()
    this.listOfMaterialGroup()
    this.applyFilters()
  },
  watch: {

  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
  }
}

</script>

<style lang="scss">
  .forecast {
    display: flex;
    flex-direction: column;
    &-header {
    align-items: end;
    justify-content: space-between;
    padding: 12px 0 12px 0;
    border-bottom: var(--border);
    // background-color: blue;
    margin-bottom: 15px;
    }
    &-filters {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    margin-right: auto;
    flex-wrap: nowrap;
    .select-dropdown {
      z-index: 3;
    }
  .date-range {
    font-size: 12px;

    & > input {
      padding: 8px;
      height: 100%;
      border: 0.7px solid var(--brand-color);
      border-radius: 4px;
      padding-inline: 4px;
      background: var(--brand-light-color);
    }
  }

  // **For smaller laptop screens**
  @media (max-width: 1200px) {
    gap: 8px;

    .date-range {
      font-size: 11px;

      & > input {
        padding: 6px;
      }
    }
  }

  // **For iPad Pro and smaller**
  @media (max-width: 1024px) {
  .forecast-filters {
    gap: 5px; // Reduce space between elements
    padding: 8px; // Reduce padding to save space
  }

  .date-range input {
    padding: 5px;
  }

  button {
    transform: scale(0.9);
  }
}

  // **For iPad Mini and smaller screens**
  @media (max-width: 768px) {
    .date-range {
      font-size: 9px;

      & > input {
        padding: 4px;
      }
    }

    button {
      transform: scale(0.9);
    }
  }
}
.forecast-table {
      margin-block: 4.3rem;
      flex: 1;
      min-height: 250px;
      max-height: 310px;
      overflow-y: auto;
      // width: 300px;
      table {
        thead {
          z-index: 3;
        }

th,
td {
  max-width: 100px;
  /* Set your desired max width for each column */
  overflow: hidden;
  /* Prevents content from overflowing */
  text-overflow: ellipsis;
  /* Adds ellipsis (...) for overflowing text */
  white-space: nowrap;
}
}
  }
    &-container {
      display: flex;
      gap: 1rem;
      padding: 1.5rem 0 0 0;

  .forecast-container-graph {
    flex: 1;
  }
    &-graph{
      & label {
        padding-block: 5px;
        padding-inline: 10px;
        background-color: var(--brand-light-color);
        box-shadow: rgba(17, 17, 26, 0.05) 0px 4px 16px, rgba(17, 17, 26, 0.05) 0px 8px 32px;
        border: .7px solid rgba(30, 30, 30, .2);
        border-radius: 4px;
      }
    }
    }
  }
  .lineCharts {
    // margin-top: 4rem;
    &-single {
      width: 100%;
    }
  }

  .loader {
    padding: 18rem;
  }
  .selectedMaterial{
    background-color: #F1F3F5 !important;
  }
  .hw-100{
    width: 100%;
    height: 100%;
  }
  .apexcharts-zoom-icon ,.apexcharts-zoomout-icon, .apexcharts-zoomin-icon, .apexcharts-zoom-icon, .apexcharts-pan-icon, .apexcharts-menu-icon, .apexcharts-reset-icon {
      width: 25px;  // Adjust the width to increase the size
      height: 25px; // Adjust the height to increase the size
      transform: scale(1) !important;

    }
    .apexcharts-toolbar{
      gap: 7px;
      z-index:3!important;
    }
    .toggle {
    font-size: 1em;
    padding: 0.5em 1.2em;
    color: var(--black);
    font-weight: 500;
    border: none;
    box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
    background-color: var(--side-bar-color);

    &:active {
      box-shadow: inset 0.2em 0.2em 0.2em rgba(0, 0, 0, 0.25);
    }
    &-selected {
      background-color: var(--brand-color);
    }
  }
  @media screen and (max-width: 1366px) {
      .forecast-filters {
        .filter-content {
          .select-dropdown {
            // background-color: blue;
            // height: 24px;
            // width: 70px;
          }
        }
        // background-color: blue;
      }
}

@media screen and (max-width: 1200px) {

}

@media screen and (max-width: 1024px) {

}
@media screen and (max-width: 1440px) {
  .filter-content {
    gap: 5px;
  }

  .select-dropdown {
    transform: scale(0.95);
  }

  .date-range input {
    // font-size: 10px;
    // transform: scale(0.95);
  }

  .button {
    transform: scale(0.95);
  }
}

@media screen and (max-width: 1280px) {
  .filter-content {
    gap: 6px;
  }

  .select-dropdown {
    transform: scale(0.9);
  }

  .date-range input {
    transform: scale(0.9);
  }

  .button {
    transform: scale(0.9);
  }
}

@media screen and (max-width: 1024px) {
  .filter-content {
    gap: 0.5rem;
  }

  .select-dropdown {
    transform: scale(0.85);
  }

  .date-range input {
    transform: scale(0.85);
  }

  .button {
    transform: scale(0.85);
  }
}

@media screen and (max-width: 1366px) {
  .forecast-container{
  min-height: 400px;
 }
}
</style>
