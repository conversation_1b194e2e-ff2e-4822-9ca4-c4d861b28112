import {
// runMutation,
  runQuery
} from '../graphQl'
import * as beaconDbQuery from '../query/beaconDashboard'
import http from '../http'
import config from '../../config.js'
import store from '../../store'

export const getBeaconTemplateVersionIds = () => {
  return runQuery(beaconDbQuery.getBeaconTemplateVersionIdsQuery(), { sales_template_name: config.BEACON_DASHBOARD.TEMPLATE_NAME, target_template_name: config.BEACON_DASHBOARD.TARGET_TEMPLATE_NAME }, 'tenant')
}

export const getBeaconMarginData = async (templateVersionId, startDate, endDate, selectedUserIds = undefined) => {
  return http.POST(config.serverEndpoint + '/dashboard/beacon/beacon-margin', {
    templateVersionId,
    startDate,
    endDate,
    selectedUserIds
  }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
export const getTopProductsData = async (templateVersionId, startDate, endDate) => {
  return http.POST(config.serverEndpoint + '/dashboard/beacon/top-products', {
    templateVersionId,
    startDate,
    endDate
  }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
export const getTargetvsRealityData = async (templateVersionId, startDate, endDate, selectedUserIds = undefined) => {
  return http.POST(config.serverEndpoint + '/dashboard/beacon/target-reality', {
    templateVersionId,
    startDate,
    endDate,
    selectedUserIds
  }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
export const getTotalRevenueData = async (templateVersionId, startDate, endDate, selectedUserIds = undefined, filterBy) => {
  return http.POST(config.serverEndpoint + '/dashboard/beacon/revenue-weekly', {
    templateVersionId,
    startDate,
    endDate,
    selectedUserIds,
    filterBy
  }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
export const getRevenueBreakupData = async (templateVersionId, startDate, endDate, selectedUserIds = undefined) => {
  return http.POST(config.serverEndpoint + '/dashboard/beacon/revenue-breakup', {
    templateVersionId,
    startDate,
    endDate,
    selectedUserIds
  }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
export const getNewLeadOftLeadData = async (templateVersionId, startDate, endDate, selectedUserIds = undefined) => {
  return http.POST(config.serverEndpoint + '/dashboard/beacon/newleads-ofts', {
    templateVersionId,
    startDate,
    endDate,
    selectedUserIds
  }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
export const getTargetRealityData = async ({ templateVersionId, targetSalesVersionId, startDate, endDate }, selectedUserIds = undefined) => {
  return http.POST(config.serverEndpoint + '/dashboard/beacon/target-reality', {
    templateVersionId,
    targetSalesVersionId,
    startDate,
    endDate,
    selectedUserIds
  }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
export const getBeaconSalesData = async (templateVersionId, startDate, endDate, selectedUserIds = undefined) => {
  return http.POST(config.serverEndpoint + '/dashboard/beacon/sales-summary', {
    templateVersionId,
    startDate,
    endDate,
    selectedUserIds
  }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
