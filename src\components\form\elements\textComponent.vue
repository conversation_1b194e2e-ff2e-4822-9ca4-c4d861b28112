<template>
  <div :class="{
    'form-input': true,
    'form-input--required': data.required
    }" >
      <label>{{data.caption}}:
        <img
      v-if="data.visibility"
      class="mx-1"
      width="15"
      src="~@/assets/images/eye.svg"
      alt="view"
      />
      </label>
      <input  :disabled="viewOnly" v-model="componentValue" type="text" @change="emitChange">
  </div>
</template>

<script>
export default {
  name: 'textComponent',
  props: {
    mode: {
      type: String,
      default: 'TEMPLATE'
    },
    data: {
      type: Object,
      default: () => {}
    },
    value: {
      type: String,
      default: ''
    },
    viewOnly: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      componentValue: ''
    }
  },
  watch: {
    value (val) {
      this.componentValue = val
    }
  },
  created () {
    this.componentValue = this.value
  },
  methods: {
    emitChange () {
      this.$emit('customChange', {
        field_id: this.$props.data.field_id,
        value: this.componentValue
      })
    }
  }
}
</script>

<style lang="scss" scoped >

</style>
