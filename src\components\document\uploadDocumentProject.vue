<template>
    <div class="upload-document">
        <div class="file-preview s">
          <div class="grid-2">
            <div class="file-preview-image">
              <img :src="fileBase64" alt="" />
            </div>
            <div class="file-preview-details">
              <div :class="{
                  'input-group':true,
                  }">
                <label for="">File Name</label>
                <input type="text" :class="{'border-red':validateImageName || duplicateName}" v-model="name" @blur ="checkDuplicateNames" />
              </div>
              <div class="input-group mt-3">
                <label for="">File Description </label>
                <textarea type="text" v-model="description" />
              </div>
              <div class="input-group mt-3">
                <label for="">File Type</label>
                <input type="text" :value="fileType" disabled />
              </div>
              <div class="input-group mt-3">
                <label for="">File Size</label>
                <input type="text" :value="fileSize" disabled />
              </div>
              <div class="flex-end mt-5">
                <button class="btn btn-black mx-3" @click="$emit('close')">Cancel</button>
                <button class="btn" @click="uploadImage" :disabled="buttonDisabled" >Upload</button>
              </div>
            </div>
          </div>
        </div>
      <modal :open="openWarning" :title="'warning'" @close="openWarning=false">
        <div class="file-preview s px-5 v-center h-center ">
          Kindly choose files having the '.{{ this.folder?.blob_key && folder?.blob_key?.split('.')[1] }}' file extension
        </div>
        <div class="flex-end mt-5">
        <button class="btn btn-black mt-3" @click="openWarning=false">ok</button>
        </div>
      </modal>
    </div>
  </template>

<script>
import modal from '../common/modal.vue'
import { generateS3SubmittingUrl, insertDocument } from '@/api'
import { mapGetters } from 'vuex'
import { success, alert } from '@/plugins/notification'
import Loader from '@/plugins/loader'

export default {
  components: { modal },
  props: {
    folder: {
      type: Object,
      default: () => ({})
    },
    is_revision: {
      type: Boolean,
      default: false
    },
    parentFolder: {
      type: Object,
      default: () => ({})
    },
    open: {
      type: Boolean,
      default: false
    },
    // will get all the documents  details in given root directory
    allDocuments: {
      type: Array,
      default: () => ([])
    },
    fileDescription: String,
    fileData: File,
    fileBase64: String,
    fileName: String,
    fileExtension: String,
    fileType: String,
    fileSize: Number
  },
  data () {
    return {
      openWarning: false,
      name: '',
      description: '',
      duplicateName: false
    }
  },
  computed: {
    ...mapGetters(['openTenantId']),
    compId () {
      // component unique id
      return this._uid
    },
    // this is checking whether any '.' character is included with the file name
    validateImageName () {
      const regex = /^[^.]*$/
      if (regex.test(this.name)) {
        return false
      } else {
        return true
      }
    }
  },
  mounted () {
    this.name = this.$props.fileName
    this.description = this.$props.fileDescription
  },
  methods: {
    async uploadImage () {
      this.buttonDisabled = true
      if (this.validateImageName) {
        alert('Dots are not allowed in the file name.')
        this.buttonDisabled = false
        return
      }
      if (this.checkDuplicateNames()) {
        this.buttonDisabled = false
        return
      }
      if (this.name === '') {
        alert('Please Enter file name')
        this.buttonDisabled = false
        return
      }
      const loader = new Loader()
      try {
        loader.show()
        const { url } = await generateS3SubmittingUrl({
          tenantId: this.openTenantId,
          feature: (this.is_revision ? this.parentFolder.doc_name : this.folder.doc_name).replace(/\s/g, '').split('.')[0].toLowerCase(),
          featureId: (this.is_revision ? this.folder.doc_name : this.name).replace(/\s/g, '').split('.')[0].toLowerCase(),
          fileName: this.name + '.' + this.fileExtension
        })
        await fetch(url, {
          method: 'PUT',
          body: this.fileData,
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        const blobkey = url.split('?')[0].split('.com/').pop()
        const decodedBlobkey = decodeURIComponent(blobkey)
        await insertDocument(
          decodedBlobkey,
          this.description,
          this.name + '.' + this.fileExtension,
          this.fileSize,
          null,
          false,
          this.is_revision ? this.folder.parent_id : this.folder.id,
          this.is_revision ? this.folder.id : null,
          this.fileExtension
        )
        this.$emit('uploaded')
        loader.hide()
        success('Document uploaded successfully')
        this.buttonDisabled = false
      } catch (error) {
        console.log(error)
        loader.hide()
        this.buttonDisabled = false
        alert('Something went wrong')
      }
    },

    checkDuplicateNames () {
      for (let i = 0; i < this.allDocuments.length; i++) {
        this.duplicateName = false
        if (this.allDocuments[i].doc_name === this.name + '.' + this.fileExtension) {
          this.duplicateName = true
          alert('Please chanage the file name to aviod duplication')
          return true
        }
      }
    },
    keyPress (e) {
      if (!this.open || this.buttonDisabled) return
      if (e instanceof KeyboardEvent && e.code === 'Escape') {
        this.closePopover()
      } else if (e instanceof KeyboardEvent && e.code === 'Enter') {
        this.uploadImage()
      }
    }
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
  }
}
</script>

  <style lang="scss" scoped >
  .upload-document {
    &-input {
      // label {
      //   font-size: 1em;
      //   padding: 0.35em 1.2em;
      //   border-radius: 0.3em;
      //   color: var(--black);
      //   font-weight: 500;
      //   border: none;
      //   box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
      //   background-color: var(--brand-color);
      // }
      input {
        display: none;
      }
    }
    .file-preview {
      min-width: 300px;
      .file-preview-image {
        width: 300px;
        height: 300px;
        img {
          width: 100%;
          height: 100%;
          -o-object-fit: cover;
          object-fit: contain;
          -o-object-position: center;
          object-position: center;
          background: #dbdbdb;
          border: 1px solid #cfcfcf;
          border-radius: 10px;
        }
      }
    }
  }
  .border-red{
    border-color: red;
  }
  .border-red:focus{
    border-color: red;
    box-shadow: none;
  }
  </style>
