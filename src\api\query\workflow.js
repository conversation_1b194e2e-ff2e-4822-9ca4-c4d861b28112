import { GQL } from '../graphQl'

export const GetAllWorkflowByVersionByIdQuery = () => GQL`query GetAllWorkflowByVersionByIdQuery($id: uuid!) {
  workflow_versions_by_pk(id: $id) {
    id
    template_id
    version_no
    created_on
    created_by
    workflow_stages {
      created_by
      created_on
      department_id
      duration
      end_coordinates
      id
      name
      type
      start_coordinates
      sequence
      phase_id
      sequence
      escalation_threshold
      trigger_actions {
      trigger_action
        trigger_timing
        mandatory
        form_template_id
        core_form_template{
        name
        }
        trigger_action
        metadata
      }
           core_user_group {
        name
      }
    }
    workflow_transitions {
      created_by
      created_on
      id
      line_vertices
      name
      next_step
      prev_step
      template_version_id
    }
    workflow_template {
      name
      id
      feature_id
    }
  }
}
`

export const getDashboardDataQuery = () => GQL`query MyQuery ($conditions:user_steps_view_bool_exp) {
  user_steps_view(where: $conditions) {
    due_date
    planned_start_date
    workflow_type
    step_name
    template_version_id
    workflow_instance_id
    workflow_name
    core_form{
      id
      template_version{
        core_form_template{
          id
          name
        }
      }
    }
  }
}`

export const CreateWorkflowTemplateWIthPhasesMutation = () => GQL`mutation CreateWorkflowTemplateWIthPhasesMutation($data: workflow_templates_insert_input!) {
  insert_workflow_templates_one(object: $data) {
    id
    workflow_versions {
      id
      workflow_stages {
        id
        name
        end_coordinates
        start_coordinates
      }
    }
  }
} `
export const GetWorkflowTypesQuery = () => GQL`query GetWorkflowTypesQuery {
  custom_list_values(where: {custom_list: {system_generated: {_eq: true}, name: {_eq: "WORKFLOW_TYPES"}}}) {
    id
    name
  }
}`
export const CreateWorkflowStepsMutation = () => GQL`mutation CreateWorkflowStepsMutation($data: [workflow_stages_insert_input!]!) {
  insert_workflow_stages(objects: $data) {
    returning {
      id
      sequence
      start_coordinates
      end_coordinates
      name
    }
  }
}`
export const CreateWorkflowTransitionMutation = () => GQL`mutation CreateWorkflowTransitionMutation($data: [workflow_transitions_insert_input!]!) {
  insert_workflow_transitions(objects: $data) {
    returning {
      id
    }
  }
}`
export const getWorkFLowTemplatesQuery = () => GQL`query getWorkFLowTemplatesQuery($limit: Int, $offset: Int, $searchKeyword: String) {
  workflow_templates(
    where: {tenant_id: {_is_null: false} name: {_ilike: $searchKeyword}}
    order_by: {created_on: desc}
    limit: $limit
    offset: $offset
  ) {
    created_by
    created_on
    feature_id
    id
    name
    tenant_id
    type
    type_value {
      name
    }
    system_generated
    created_by_user {
      email
      first_name
      id
      last_name
    }
    type_value {
      id
      name
    }
      core_form_template {
      value:id
      label:name
      form_type
    }
  }
  workflow_templates_aggregate(where: {tenant_id: {_is_null: false} name: {_ilike: $searchKeyword}}) {
    aggregate {
      count
    }
  }
}
`
export const getfullWorkFLowTemplatesQuery = () => GQL`query getfullWorkFLowTemplatesQuery {
  workflow_templates(
    where: {tenant_id: {_is_null: false}}
    order_by: {created_on: desc}
  ) {
    created_by
    created_on
    feature_id
    id
    name
    tenant_id
    type
    type_value {
      name
    }
    system_generated
    created_by_user {
      email
      first_name
      id
      last_name
    }
    type_value {
      id
      name
    }
      core_form_template {
      id
      name
      form_type
    }
  }
}
`
export const GetWorkFlowDataByTemplateIdQuery = () => GQL`query GetWorkFlowDataByTemplateIdQuery($id: uuid!) {
  workflow_templates_by_pk(id: $id) {
    workflow_versions(order_by: {created_on: asc}) {
      active
      id
      template_id
      version_no
      draft
      created_by
      created_on
      created_by_user {
        email
        first_name
        id
        last_name
      }
    }
    name
    id
  }
}
`
export const updateWorkFlowTemplateMutaion = () => GQL`mutation UpdateWorkflowTemplate($draft: Boolean!, $templateId: uuid!, $updates: workflow_update_input!) {
  update_workflow_template(draft: $draft, template_id: $templateId, updates: $updates) {
    message
  }
}

`
export const UpdateWorkflowTemplateNameMutation = () => GQL`mutation UpdateWorkflowTemplateNameMutation($id:uuid!, $name:String!){
  update_workflow_templates_by_pk( pk_columns:{id:$id}  _set:{name:$name}){
    id
  }
}`
export const workflowDataBasedOnInstanceIdQuery = () => GQL`query workflowDataBasedOnInstanceIdQuery($id: uuid!) {
  workflow_instances_by_pk(id: $id) {
    id
    state_value {
      id
      name
    }
    transition_history(where: {end_date: {_is_null: true}}) {
      id
      prev_step {
        id
        name
      }
      initiated_by
    }
    workflow_version {
      id
      workflow_template {
        id
        name
        type_value {
          name
        }
      }
    }
    workflow_instance_steps(where: {workflow_step: {workflow_stage_instances: {id: {_eq: $id}}}}) {
      step_id
      planned_start_date
      planned_end_date
        instance_step_assignees {
        user_id
      }
      core_attachments {
        file_name
        file_size
        blob_key
        created_by_user {
          first_name
          last_name
        }
      }
    }
    workflow_stage {
      id
      name
      end_step
      duration
      trigger_actions {
        trigger_timing
        mandatory
        core_form_template {
          id
          name
        }
      }
      core_user_group {
        name
        core_user_group_members(where: {deleted: {_eq: false}}) {
          user_id
          deleted
        }
      }
      next_transition {
        id
        name
      }
    }
  }
}`
export const getTransitionDataQuery = () => GQL`query getTransitionDataQuery($transitionId: uuid) {
  transition_history(where: {instance_id: {_eq: $transitionId}}, order_by: {start_date: asc}) {
    action_taken
    completed_by
    end_date
    step_id
    id
    prev_step_id
    start_date
  }
  workflow_instance_steps(where: {workflow_instance_id: {_eq: $transitionId}}) {
    step_id
    planned_start_date
    planned_end_date
    projected_start_date
    projected_end_date
  }
}`
export const getNextStepWithTransitionIdQuery = () => GQL`query getNextStepWithTransitionIdQuery($id: uuid!) {
  workflow_transitions_by_pk(id: $id) {
    name
    next_workflow_stage {
      id
      name
      duration
      core_user_group {
      name
        core_user_group_members {
          user_id
        }
      }
    }
  }
}`

export const CheckIfUserGroupIsAttachedToWorkflowQuery = () => GQL`query CheckIfUserGroupIsAttachedToWorkflow($id: uuid!) {
  workflow_stages(where: {department_id: {_eq: $id}}) {
    workflow_version {
      workflow_template {
        id
        name
      }
    }
  }
}`
