import { GQL } from '../graphQl'

export const insertDocumentQuery = () => GQL`mutation insertDocumentQuery ($insertObj: core_documents_insert_input!){
  insert_core_documents_one(
    object: $insertObj
  ) {
    id
  }
}
`

export const updateLastdocVersionQuery = () => GQL`mutation updateLastdocVersionQuery($blob_key: String, $doc_id: uuid) {
  update_core_documents(_set: {blob_key: $blob_key}, where: {id: {_eq: $doc_id}}) {
    affected_rows
  }
}`

export const getAllParentFoldersQuery = () => GQL`query getAllParentFoldersQuery ( $conditions: core_documents_bool_exp ){
  core_documents(
    order_by: {created_on: desc},
    where: $conditions
  ) {
    view_only
    blob_key
    created_by
    created_on
    description
    doc_name
    doc_type
    doc_size
    folder
    id
    parent_id
    project_id
    state
    tenant_id
    updated_by
    updated_on
    version_of
    checked_out_by
  }
}`

export const checkDocExistsQuery = () => GQL`query FindDocByFolderIdAndDocumentName($folderId: uuid!, $docName: String!) {
  core_documents(where: {parent_id: {_eq: $folderId}, doc_name: {_eq: $docName}}, limit: 1) {
    id
  }
}`

export const getDocumentsByParentIdQuery = () => GQL`query getDocumentsByParentIdQuery($conditions: core_documents_bool_exp, $skipInheritedField: Boolean! ) {
  core_documents(
    order_by: {created_on: desc}
    where: $conditions
  ) {
    plm_revision_no
    plm_document_id
    view_only
    blob_key
    created_by
    created_on
    description
    doc_name
    doc_type
    doc_size
    ongoing_revision_form_id
    source_form_id
    folder
    id
    parent_id
    project_id
    state
    tenant_id
    updated_by
    updated_on
    version_of
    checked_out_by
    thumbnail_blob_key
    doc_ext
    inherited_from_doc_id @skip (if: $skipInheritedField)
    inherited_from_document {
          blob_key
          created_by
          created_on
          description
          doc_name
          doc_type
           doc_size
           folder
           id
           parent_id
            project_id
           state
            tenant_id
            updated_by
           updated_on
           version_of
            checked_out_by
           thumbnail_blob_key
           doc_ext
           associated_versions_aggregate(where:{deleted:{_eq:false}}) {
            aggregate {
              count
            }
           }
           main_version_document{
            associated_versions(
              where: {folder: {_eq: false}, deleted: {_eq: false}}
              order_by: {created_on: asc}
            ) {
              id
            }
           }
           associated_versions(
            where: {folder: {_eq: false}, deleted: {_eq: false}}
            order_by: {created_on: asc}
          ) {
            id
          }
    }
    associated_versions_aggregate(where:{deleted:{_eq:false}}) {
      aggregate {
        count
      }
    }
    associated_versions(
      where: {folder: {_eq: false}, deleted: {_eq: false}}
      order_by: {created_on: asc}
    ) {
      blob_key
      version_of
      thumbnail_blob_key
      deleted
      created_on
      doc_name
      state
      id
    }
  }
}
`
// here getDocumentsByParentIdQuery2 is for geting data with non attached materials (material id == null)
export const getDocumentsByParentIdQuery2 = () => GQL`query getDocumentsByParentIdQuery2($conditions: core_documents_bool_exp ) {
  core_documents(
    order_by: {created_on: desc}
    where: $conditions
  ) {
    blob_key
    created_by
    created_on
    description
    doc_name
    doc_type
    doc_size
    folder
    id
    parent_id
    project_id
    state
    tenant_id
    updated_by
    updated_on
    version_of
    checked_out_by
    thumbnail_blob_key
    doc_ext
    thumbnail_blob_key
    associated_versions(
      where: {folder: {_eq: false}, deleted: {_eq: false}}
      order_by: {created_on: desc}
    ) {
      state
      doc_name
      thumbnail_blob_key
      id
      blob_key
    }
    associated_versions_aggregate {
      aggregate {
        count
      }
    }
  }
}`

export const updateDocumentNameQuery = () => GQL`mutation updateDocumentNameQuery(
  $id: uuid,
  $doc_name: String) {
  update_core_documents(where: {
    id: {
      _eq: $id
    }
  }, _set: {
    doc_name: $doc_name
  }) {
    affected_rows
  }
}
`
export const deleteDocumentQuery = () => GQL`mutation deleteDocumentQuery(
  $id: uuid) {
  update_core_documents(where: {
    id: {
      _eq: $id
    }
  }, _set: {
    deleted: true
  }) {
    affected_rows
  }
}
`

export const getUsersWithAccessToSharedDocument = () => GQL`query GetUsersWithSharedDocument ($documentId: uuid!) {
  collaborator_documents(where: {document_id: {_eq: $documentId}}) {
    id
    tenant_user_association_by_target{
      associated_user{
        first_name
        last_name
        id
      }
    }
      created_by_user {
      id
      first_name
      last_name
    }
  }
}`

export const shareDocument = () => GQL`mutation shareDocument ($data: [collaborator_documents_insert_input!]!) {
  insert_collaborator_documents(objects: $data) {
    affected_rows
  }
}`

export const removeDocAccess = () => GQL`mutation removeSharedDocAccess($id: Int!) {
  delete_collaborator_documents_by_pk(id: $id) {
    document_id
  }
}`

export const getDocumentByIdQuery = () => GQL`query getDocumentsByParentIdQuery2($conditions: core_documents_bool_exp ) {
  core_documents(
    order_by: {created_on: desc}
    where: $conditions
  ) {
    blob_key
    created_by
    created_on
    description
    doc_name
    doc_type
    doc_size
    folder
    id
    parent_id
    project_id
    state
    tenant_id
    updated_by
    updated_on
    version_of
    checked_out_by
    doc_ext
    thumbnail_blob_key
    associated_versions(
      where: {folder: {_eq: false}, deleted: {_eq: false}}
      order_by: {created_on: desc}
    ) {
      id
    }
    associated_versions_aggregate {
      aggregate {
        count
      }
    }
  }
}
`
export const SwitchDownloadQuery = () => GQL`mutation SwitchDownloadQuery($id:uuid, $value: Boolean) {
  update_core_documents(where: {id: {_eq: $id},folder: {_eq: false}},_set:{view_only:$value}) {
    affected_rows
  }
}`

export const getAllCollaboratorDocumentQuery = () => GQL`query getAllCollabDocument($conditions: core_documents_bool_exp, $skipInheritedField: Boolean!) {
  core_documents(
    order_by: {created_on: desc}
    where: $conditions
  ) {
    view_only
    blob_key
    created_by
    created_on
    description
    doc_name
    doc_type
    doc_size
    folder
    id
    parent_id
    project_id
    state
    tenant_id
    updated_by
    updated_on
    version_of
    checked_out_by
    doc_ext
    thumbnail_blob_key
    inherited_from_doc_id @skip (if: $skipInheritedField)
    inherited_from_document {
          blob_key
          created_by
          created_on
          description
          doc_name
          doc_type
           doc_size
           folder
           id
           parent_id
            project_id
           state
            tenant_id
            updated_by
           updated_on
           version_of
            checked_out_by
           thumbnail_blob_key
           doc_ext
           associated_versions_aggregate(where:{deleted:{_eq:false}}) {
            aggregate {
              count
            }
           }
           main_version_document{
            associated_versions(
              where: {folder: {_eq: false}, deleted: {_eq: false}}
              order_by: {created_on: asc}
            ) {
              id
            }
           }
           associated_versions(
            where: {folder: {_eq: false}, deleted: {_eq: false}}
            order_by: {created_on: asc}
          ) {
            id
          }
    }
    associated_versions(
      where: {folder: {_eq: false}, deleted: {_eq: false}}
      order_by: {created_on: asc}
    ) {
      id
      thumbnail_blob_key
      state
    }
    associated_versions_aggregate {
      aggregate {
        count
      }
    }
  }
}`

export const getAllRevisionDocumentsQuery = () => GQL`query getAllRevisionDocumentsQuery($conditions: core_documents_bool_exp, $skipInheritedField: Boolean!) {
  core_documents(
    where: $conditions
    order_by: {created_on: desc}
  ) {
    blob_key
    created_by
    created_on
    description
    doc_name
    doc_type
    doc_size
    doc_ext
    thumbnail_blob_key
    folder
    id
    parent_id
    project_id
    state
    tenant_id
    updated_by
    updated_on
    version_of
    checked_out_by
    inherited_from_doc_id @skip (if: $skipInheritedField)
    inherited_from_document {
          blob_key
          created_by
          created_on
          description
          doc_name
          doc_type
           doc_size
           folder
           id
           parent_id
            project_id
           state
            tenant_id
            updated_by
           updated_on
           version_of
            checked_out_by
           thumbnail_blob_key
           doc_ext
    }
    associated_versions_aggregate {
      aggregate {
        count
      }
    }
  }
}
`

export const setDocumentStateQuery = () => GQL`mutation setDocumentStateQuery($id: uuid!, $state: Int, $checked_out_by: uuid) {
  update_core_documents_by_pk(pk_columns: {id: $id}, _set: {state: $state, checked_out_by: $checked_out_by}) {
    doc_name
    state
    checked_out_by
  }
}`

export const checkDuplicateFolderName = () => GQL`query checkDuplicateFolderName($conditions: core_documents_bool_exp ) {
  core_documents(where: $conditions) {
    doc_name
  }
}
`
export const inheritDocument = () => GQL`mutation inheritDocument ($insertObj: core_documents_insert_input!){
  insert_core_documents_one(object: $insertObj) {
    id
  }
}`

export const updateInheritedDocument = () => GQL`mutation updateInheritedDocument ($latestId: uuid!, $docId:uuid!){
  update_core_documents_by_pk(
   pk_columns: { id: $docId}
   _set:{
   inherited_from_doc_id: $latestId
 }
 ) {
  id
}
}`

export const DeleteDocByVersionIDQuery = () => GQL`mutation DeleteDocByVersionIDQuery($id: uuid) {
  update_core_documents(
    where: {associated_versions: {id: {_eq: $id}}}
    _set: {deleted: false}
  ) {
    affected_rows
  }
}`

export const renameDocumentMutation = () => GQL`mutation renameDocument ($id:uuid!, $newName: String!) {
 update_core_documents_by_pk (
   pk_columns : {id : $id},
   _set : {doc_name : $newName}
 )  {
  id
 }
}`

export const getAllAttachedDocs = () => GQL`query getAllAttachedDocs{
  task_document_association{
      task_id
      document_id
      core_document {
        doc_name
        id
        doc_type
        doc_size
        blob_key
      }
      core_task {
        name
      }
   }
}`

export const insertDocAnnotationMutation = () => GQL`mutation insertDocAnnotationMutation ($objects:[core_annotation_elements_insert_input!]!) {
  insert_core_annotation_elements(objects:$objects) {
    affected_rows
  }
}
`
export const deleteAllAnnotationQuery = () => GQL`mutation deleteAllAnnotationQuery($docId: uuid) {
  delete_core_annotation_elements(where: {document_id: {_eq: $docId}}) {
    affected_rows
  }
}

`
export const getAnnotationDataApiQuery = () => GQL`query getAnnotationDataApiQuery($docId: uuid) {
  core_annotation_elements(
    where: {document_id: {_eq: $docId}}
    order_by: {page_no: asc}
  ) {
    angle
    bg_color
    blob_key
    char_spacing
    crop_x
    created_on
    created_by
    crop_y
    document_id
    fill
    fill_rule
    flip_x
    flip_y
    font_size
    height
    id
    left
    line_height
    metadata
    min_width
    opacity
    origin_x
    origin_y
    page_no
    paint_first
    path_align
    path_side
    rx
    ry
    scale_x
    skew_y
    skew_x
    scale_y
    stroke
    stroke_dash_offset
    stroke_line_cap
    stroke_line_join
    stroke_miter_limit
    stroke_uniform
    stroke_width
    text
    text_align
    top
    type
    width
    x1
    x2
    y1
    y2
    id
      created_by
    created_on
    status
    created_by_user {
      first_name
      id
      last_name
      email
    }
    updated_by_user {
      first_name
      last_name
      id
    }
  }
}
`

export const updateAnnotationQuery = () => GQL`mutation updateAnnotationQuery ($updateObjects: [core_annotation_elements_updates!]!) {
  update_core_annotation_elements_many(updates: $updateObjects) {
    affected_rows
  }
}`

export const deleteAnnoatationElementsQuery = () => GQL`mutation deleteAnnoatationElementsQuery($ids: [Int!]) {
  delete_core_annotation_elements(where: {id: {_in: $ids}}) {
    affected_rows
  }
}`
