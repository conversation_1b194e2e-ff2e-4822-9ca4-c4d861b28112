<template>
    <div id="dropdown">
        <div class="aselect pointer">
            <div class="selector" @mouseleave="visible=false">
                <div class="label" @click="visible=true">
                    <span> {{ selected.name }} </span>
                </div>
                <div class="arrow"  @click="visible=true"></div>
                <div v-if="visible">
                  <div class="form-input" >
                    <div class="baselinelist">
                    <slot></slot>
                      <div :class="{ current: item.id === selected.id }"  v-for="item in list" :key="item.id" @click="select(item)">
          <div style="padding-left: 10px; padding-top: 5px">{{ item.name }}</div>
        </div></div>
        <div style="padding-left: 108px; height: 24px;">
          <button v-if="selected.id !== -1 && clearBaseline" @click="clear" class="clearbaseline">Clear</button>
        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

export default {
  name: 'Dropdown',
  props: {
    list: {
    },
    selected: {
      default: { id: -1, name: 'Select' }
    },
    clearBaseline: {
      type: Function
    }
  },
  data () {
    return {
      visible: false
    }
  },
  methods: {
    toggle () {
      this.visible = !this.visible
      this.$emit('toggle')
    },
    select (item) {
      this.$emit('selected', item)
      this.toggle()
    },
    clear () {
      this.clearBaseline()
      this.toggle()
    }
  }
}
</script>

<style lang="scss">

.aselect {
    width: 160px;

    .selector {
        border: 1px solid black;
        position: relative;
        z-index: 1;
        border-radius: 0.3em;

        .arrow {
            position: absolute;
            right: 10px;
            top: 40%;
            width: 0;
            height: 0;
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            border-top: 10px solid #888;
            transform: rotateZ(0deg) translateY(0px);
            transition-duration: 0.3s;
            transition-timing-function: cubic-bezier(.59, 1.39, .37, 1.01);
        }

        .expanded {
            transform: rotateZ(180deg) translateY(2px);
        }

        .label {
            display: block;
            border-radius: 0.3em;
            padding: 3px;
            font-size: 14px;
        }
    }

    ul {
        overflow: scroll;
        max-height: 14rem;
        width: 100%;
        list-style-type: none;
        padding: 0;
        margin: 0;
        font-size: 14px;
        border: 1px solid gainsboro;
        position: absolute;
        z-index: 1;
        background: #fff;
    }

    li {
        padding: 5px;
        color: #666;

        &:hover {
            cursor: pointer;
            color: white;
            background: var(--brand-color-1);
        }
    }

    .current {
        background: var(--brand-color);
    }

    .hidden {
        visibility: hidden;
    }

    .visible {
        visibility: visible;
    }
    .clearbaseline {
    color: var(--white);
    background-color: var(--black);
    font-size: 1em;
    border-radius: 0.3em;
    font-weight: 500;
    box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
    }
    .form-input{
      position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: #fff;
    border: 1px solid rgb(179, 179, 179);
    border-radius: 0.3em;
    z-index: 1;
    max-height: 200px;
}

.baselinelist {
    overflow: auto;
    max-height: 11rem;
    width: 158px;
    list-style-type: none;
    padding: 0;
    margin: 0;
    font-size: 14px;
 }
}</style>
