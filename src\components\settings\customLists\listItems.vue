 <template>
  <div class="custom-list-items">
    <div class="custom-list-items--bar v-center space-between">
      <h4>Custom List</h4>
      <img
      v-if="isTenantAdmin"
        v-tooltip="'Add New List'"
        src="~@/assets/images/add-new-icon.svg"
        alt=""
        @click="addNewCustomList"
      />
    </div>
    <div class="custom-list-items-container"  v-click-outside="cancelEdit" >
      <div v-if="addNew" class="custom-list-items--add-new v-center">
        <input type="text" v-model="listName" >
        <img @click="save" class="save"  src="~@/assets/images/icons/save-icon.svg" alt="">
        <img @click="close" class="close"  src="~@/assets/images/close.png" alt="">
      </div>
      <div
        v-for="(item, index) in list"
        :key="item.id"
        class="custom-list-items-item v-center"
        :class="{'selected': selected?.id === item.id}"
      >
        <div class="custom-list-items-item__name v-center s elipsis-text" @click="select(item, index)">
                <input
                  v-if="!item.system_generated === true && selectToEdit === item.id"
                  v-model="editName"
                  type="text"
                />
                <p v-else>{{ item.name }}</p>
        </div>
        <div class="toggle-button center" v-if=" item.system_generated === false && showEdit">
           <img
                  @click="[edit(item),close()]"
                  src="~@/assets/images/edit-icon.svg"
                  alt=""
                />
                <img :v-tooltip="tooltipFunc(item)" :class="item.template_fields.length === 0 ? '' : 'disabled'"   @click.stop="deleteItem(item)" src="~@/assets/images/delete-icon.svg" width="18px" alt="" />
        </div>
        <div class="col-action flex" v-else-if="selectToEdit === item.id">
                <img
                width="18px"
                  @click="editCustomList(index)"
                  src="~@/assets/images/icons/save-icon.svg"
                  alt=""
                  class="mr-1"
                />
                <img
                class="mr-1"
                  style="width: 18px;"
                  @click.stop="cancelEdit(item)"
                  src="~@/assets/images/close.png"
                  alt=""
                />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { validateCustomList, CreateCustomList, DeleteCustomList, UpdateCustomList } from '@/api'
import { success, alert } from '../../../plugins/notification'
import { mapGetters } from 'vuex'
import ConfirmationDialog from '@/plugins/confirmationDialog'
import Loader from '@/plugins/loader'

export default {
  name: 'custom-list-items',
  props: {
    list: {
      type: Array,
      required: true
    },
    selected: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      addNew: false,
      listName: '',
      creating: false,
      selectToEdit: null,
      editName: ''
    }
  },
  computed: {
    ...mapGetters(['user']),
    isTenantAdmin () {
      return this.user.tenantLevelRole === 'ADMIN'
    },
    showEdit () {
      return this.user.tenantLevelRole === 'ADMIN' && !this.selectToEdit
    }
  },
  methods: {
    tooltipFunc (item) {
      return item?.template_fields?.length === 0 ? '' : 'Attatched to Forms'
    },
    addNewCustomList () {
      this.addNew = true
      this.$emit('select', { id: null, index: null })
    },
    edit (item) {
      this.selectToEdit = item.id
      this.editName = item.name
    },
    cancelEdit () {
      this.selectToEdit = null
      this.editName = ''
    },
    deleteItem (item) {
      if (item.template_fields.length === 0) {
        ConfirmationDialog(
          'Are you sure you want to delete this custom list?',
          (res) => {
            if (res) {
              DeleteCustomList(item.id).then(res => {
                success('Custom List Deleted Successfully')
                this.$emit('update')
              })
            }
          }
        )
      } else alert('Cannot delete the list due to attachment in forms')
    },
    close () {
      this.addNew = false
      this.listName = ''
    },
    async save () {
      if (!this.listName) return
      const customName = `%${this.listName}%`
      this.creating = true
      const loader = new Loader()
      loader.show()
      const res = await validateCustomList(customName)
      if (!res.core_custom_list.length) {
        CreateCustomList([], this.listName).then(res => {
          this.$emit('update', res.data)
          this.creating = false
          this.addNew = false
          this.listName = ''
        }).catch(err => {
          if (err?.message?.includes('Uniqueness violation')) {
            this.$notify.alert('Custom list value already exists')
          } else {
            this.$notify.alert(err?.message ?? 'Something went wrong')
          }
        }).finally(() => {
          loader.hide()
        })
      } else {
        const id = res.core_custom_list[0].id
        UpdateCustomList(id, {
          deleted: false, name: this.listName
        }).then((res) => {
          this.$emit('update', res.data)
          success('Custom list Created successfully')
        }).catch(err => {
          if (err?.message?.includes('Uniqueness violation')) {
            this.$notify.alert('Custom list value already exists')
          } else {
            this.$notify.alert(err?.message ?? 'Something went wrong')
          }
        }).finally(() => {
          loader.hide()
        })
      }
    },
    editCustomList (index) {
      UpdateCustomList(this.selectToEdit, {
        name: this.editName,
        deleted: false
      }).then((res) => {
        this.$emit('update', { id: this.selected.id, index })
        success('Custom list edited successfully')
        this.selectToEdit = null
        this.editName = ''
      }).catch(err => {
        if (err?.message?.includes('Uniqueness violation')) {
          this.$notify.alert('This Value cant renamed, "Add New List"')
        } else {
          this.$notify.alert(err?.message ?? 'Something went wrong')
        }
      })
    },
    select (item, index) {
      if (this.addNew) {
        this.close()
      }
      if (!this.selectToEdit) {
        this.$emit('select', { id: item.id, index })
      }
    }
  }
}
</script>

<style lang="scss" scoped >
.custom-list-items {
  height: 100%;
  background-color: var(--bg-color);
  padding: 6px;
  overflow: auto;
  &--add-new {
    position: relative;
    margin-top: 6px;
    .col-action {
      width: 60px;
      img {
        width: 20px;
        cursor: pointer;
      }
    }
    input {
      width: 100%;
      height: 30px;
      border: 1px solid var(--brand-color);
      background: transparent;
      padding-right: 50px;
      font-size: 14px;
    }
    img {
      position: absolute;
      cursor: pointer;
      &.close {
        width: 18px;
        right: 2px;
      }
      &.save {
        width: 20px;
        right: 25px;
      }
    }
  }
  &--bar {
    margin: -6px;
    margin-bottom: 0px;
    padding: 10px;
    background-color: var(--brand-color);
    h4 {
      font-size: 14px;
      font-weight: 500;
    }
    img {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }
  &-item {
    border: 1px solid var(--brand-color);
    background-color: rgba(var(--brand-rgb), 0.2);
    margin: 6px 0px;
    padding-left: 6px;
    cursor: pointer;
    &__name {
      width: calc(100% - 30px);
      height: 30px;
    }
    &.selected {
      background-color: rgba(var(--brand-rgb), 0.6);
    }
  }
  .disabled {
  //background-color: #d3d3d3; /* Light grey background */
  color: #7a7a7a; /* Dark grey text */
  opacity: 0.6;
  //pointer-events: none;
  }
}
</style>
