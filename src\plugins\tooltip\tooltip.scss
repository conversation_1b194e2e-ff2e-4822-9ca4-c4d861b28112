div.tooltip-container-text {
  position: fixed;
  background: #ffffff;
  color: #000000;
  font-size: 10px;
  max-width: 200px;
  padding: 4px 8px;
  border-radius: 4px;
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
  z-index: 1000;
  // add bottom notches
  &::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #ffffff;
  }
}
div.tooltip-container-top {
  position: fixed;
  background: #ffffff;
  color: #000000;
  font-size: 10px;
  max-width: 200px;
  padding: 4px 8px;
  border-radius: 4px;
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
  z-index: 1000;
  // add bottom notches
  &::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #ffffff;
  }
}
div.tooltip-container-left {
  position: fixed;
  background: #ffffff;
  color: #000000;
  font-size: 10px;
  max-width: 200px;
  padding: 4px 8px;
  border-radius: 4px;
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
  z-index: 1000;
  // add bottom notches
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 100%;
    transform: rotate(-90deg) translateX(50%);
    border: 5px solid transparent;
    border-top-color: #ffffff;
  }
}