<template>
  <div>
    <div v-if="loading" class="flex center loading-container">
    <LoadingCircle />
    </div>
    <div ref="ganttContainer" id="gantt-edit-here" v-show="!loading"></div>
    <modal
      :open="showForm"
      @close="closeModal"
      :closeOnOutsideClick="true"
      title="Task Details"
    >
      <task-edit-form
        v-if="selectedTask"
        :task="selectedTask"
        @close="closeModal"
        @update-and-close="updateAndCloseModal"
        @delete-task="handleTaskDeletion"
        view="chart"
      />
    </modal>
    <modal
      :open="showDeleteLinkModal"
      @close="cancelDeleteLink"
      title="Delete Task Link"
    >
      <div class="create-new-input s p-4">
        <div class="grid-2 mb-3">
          <div class="input-group">
            <label>Lag (In hours)</label>
            <input type="number" v-model="newlyAddedLag">
          </div>
        </div>
        <div class="space-between py-3 l">
          <div>
            <button @click="updateTaskLinkData" class="btn mr-3">SAVE</button>
            <button @click="cancelDeleteLink" class="btn btn-black mr-3">CANCEL</button>
          </div>
          <div>
            <button @click="deleteLink" class="btn" style="margin-right: 12px">DELETE LINK</button>
          </div>
        </div>
      </div>
    </modal>
  </div>
</template>

<script>
import {
  CreateTask,
  GetTaskStatuses,
  UpdateTasks,
  deleteAssignees,
  AddTaskTags,
  deleteTaskTagsAll,
  addNewAssignees,
  deleteTaskLinks,
  InsertTaskLinks,
  updateTaskLink,
  addTaskComments,
  attchTaskDocs,
  removeTaskDocuments,
  DeleteBomTaskMutation,
  AddBomToTaskMutation,
  UpdateBomTaskMutation,
  deleteTasks
} from '@/api'
import { mapGetters } from 'vuex'
import { success, alert } from '@/plugins/notification'
import { Gantt } from '../../assets/gantt/dhtmlxgantt'
import Modal from '../common/modal.vue'
import TaskEditForm from './taskEditForm.vue'
import LoadingCircle from '@/components/common/loadingCircle.vue'
import { getGanttTypeFromNumber, getGanttTypeFromString } from '@/helper/gantt/getGanttType'

/**
 * Gantt Instance
 * @type {import('../../assets/gantt/dhtmlxgantt').GanttStatic}
 */
let gantt

export default {
  components: { Modal, TaskEditForm, LoadingCircle },
  props: {
    zoomLevel: {
      type: String,
      default () {
        return 'Weeks'
      }
    },
    tasks: {
      type: Object,
      default () {
        return { data: [], links: [] }
      }
    },
    autoCompute: {
      type: Boolean,
      required: true
    },
    saveProjectPlan: {
      type: Boolean,
      required: true
    },
    showSlack: {
      type: Boolean,
      required: true
    },
    criticalPath: {
      type: Boolean,
      required: true
    },
    projectedPlan: {
      type: Boolean,
      required: true
    },
    calendar: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      attachBomUpdates: [],
      deletedTasks: [],
      taskStatus: {},
      newlyAddedLag: null,
      lagUpdationsArray: [],
      showForm: false,
      selectedTask: null,
      loading: false,
      showDeleteLinkModal: false,
      selectedLink: null,
      taskLinkId: null,
      showSlackLayerId: '',
      currentTasks: {},
      editTasks: {
        newTasks: {
          ids: {},
          data: []
        },
        updatedTasks: {
          ids: {},
          data: []
        },
        deletedTasks: []
      },
      createdLinks: [],
      newTaskIds: {},
      updatedDocs: []
    }
  },
  computed: {
    ...mapGetters(['currentProject', 'user', 'collaborator', 'isExternalCollaborator'])
  },
  methods: {
    handleTaskDeletion (id) {
      gantt.deleteTask(id)
      if (!this.selectedTask.$new) {
        this.deletedTasks.push(id)
      }
      gantt.hideLightbox()
    },
    drawSlack (task) {
      const slack = gantt.getFreeSlack(task)
      if (!slack) {
        return null
      }
      const state = gantt.getState().drag_mode

      if (state === 'resize' || state === 'move') {
        return null
      }
      const slackStart = new Date(task.end_date)
      const slackEnd = gantt.calculateEndDate(slackStart, slack)
      const sizes = gantt.getTaskPosition(task, slackStart, slackEnd)
      const el = document.createElement('div')
      el.className = 'slack'
      el.style.left = sizes.left + 'px'
      el.style.top = sizes.top + 1 + 'px'
      el.style.width = sizes.width + 'px'
      el.style.height = sizes.height - 1 + 'px'
      return el
    },
    updateTaskLinkData () {
      this.lagUpdationsArray.push({
        lag: this.newlyAddedLag,
        targetTaskId: this.selectedLink.target,
        sourceTaskId: this.selectedLink.source
      })
      gantt.getLink(this.selectedLink.id).lag = this.newlyAddedLag ?? null
      gantt.updateLink(this.selectedLink.id)
      this.endPopup()
    },
    AutoShedule () {
      try {
        if (this.autoCompute) {
          gantt.config.auto_scheduling = true
          gantt.config.auto_scheduling_strict = true
          gantt.config.auto_scheduling_compatibility = true
          gantt.autoSchedule()
          gantt.batchUpdate(() => {
            const leafTaskIds = []
            const parentIdsOfLeafTasks = []
            gantt.eachTask((task) => {
              const taskData = gantt.getTask(task.id)
              taskData.slack = gantt.getTotalSlack(taskData)
              if (gantt.isCriticalTask(taskData)) {
                taskData.is_critical = gantt.isCriticalTask(taskData)
              }
              gantt.updateTask(task.id, taskData)

              // if a task is a leaf task and if any other leaf tasks of its parent is not added store the data
              if (!gantt.hasChild(task.id) && !parentIdsOfLeafTasks.includes(task.parent)) {
                leafTaskIds.push(task.id)
                parentIdsOfLeafTasks.push(task.parent)
              }
            })
            for (const taskId of leafTaskIds) {
              this.updateParentProgress(taskId)
            }
          })
        }
      } catch (err) {
        console.log(err)
      }
    },
    CancelAutoShedule () {
      gantt.config.auto_scheduling = false
      gantt.config.auto_scheduling_strict = false
      gantt.config.auto_scheduling_compatibility = false
    },
    ganttInit () {
      GetTaskStatuses().then((res) => {
        for (const item of res.custom_list_values) {
          this.taskStatus[item.name] = item.id
        }
      }).catch(() => {
        alert('Failed to fetch task statuses')
      })
      // if gantt exists destroy the instance
      if (gantt) {
        gantt.clearAll()
        gantt.destructor()
        gantt = undefined
      }
      // getting the new gantt instance
      gantt = Gantt.getGanttInstance()

      // setting gantt date format
      gantt.config.date_format = '%Y-%m-%d %H:%i:%s'
      gantt.templates.parse_date = (date) => {
        return new Date(date)
      }
      gantt.config.server_utc = true
      gantt.config.auto_types = true
      // config for columns
      gantt.config.columns = [
        { name: 'text', tree: true, min_width: 200, resize: true, align: 'left' },
        {
          name: 'start_date',
          label: 'Start date',
          align: 'center',
          width: 100,
          min_width: 100,
          resize: true
        },
        {
          name: 'end_date',
          label: 'End date',
          align: 'center',
          width: 100,
          min_width: 100,
          resize: true
        },
        {
          name: 'duration',
          label: 'Duration(Hrs)',
          align: 'center',
          resize: true,
          width: 100,
          min_width: 100
        },
        {
          name: 'progress',
          label: 'Progress',
          align: 'center',
          resize: true,
          width: 70,
          template: (task) => {
            return Math.round(task.progress > 1 ? task.progress : task.progress * 100) + '%'
          }
        }
      ]
      if (this.user.projectLevelRole !== 'EDITOR' && !this.collaborator) {
        gantt.config.columns.push({
          name: 'add',
          width: 30,
          align: 'center',
          resize: false
        })
      }
      gantt.templates.grid_row_class = function (start, end, task) { if (task.type === 3) { return 'nested_task' } return '' }
      // gantt work time hours
      gantt.config.work_time = true

      const customCalendar = {
        id: 'global',
        worktime: {
          hours: this.calendar.workTimeHours,
          days: this.calendar.workTimeDays
        }
      }

      // Add the custom calendar to the Gantt chart
      gantt.addCalendar(customCalendar)
      const calendar = gantt.getCalendar('global')

      // setting up holidays
      for (const holiday of this.calendar?.holidays) {
        calendar.setWorkTime({ date: new Date(holiday.date), hours: false })
      }

      // making tasks with children bold
      gantt.templates.grid_row_class = function (start, end, task) {
        return gantt.hasChild(task.id) ? 'weight-600' : ''
      }
      gantt.templates.task_class = function (start, end, task) {
        if (gantt.hasChild(task.id)) {
          return 'no_progress'
        }
      }
      // setting config for draggable config columns
      gantt.config.grid_elastic_columns = true
      gantt.attachEvent('onGanttRender', () => {
        gantt.config.highlight_critical_path = this.$props.criticalPath
      })
      gantt.config.layout = {
        css: 'gantt_container',
        cols: [
          {
            width: 400,
            maxWidth: 800,
            // adding horizontal scrollbar to the grid via the scrollX attribute
            rows: [
              { view: 'grid', scrollX: 'gridScroll', scrollable: true, scrollY: 'scrollVer' },
              { view: 'scrollbar', id: 'gridScroll' }
            ]
          },
          { resizer: true, width: 1 },
          {
            rows: [
              { view: 'timeline', scrollX: 'scrollHor', scrollable: true, scrollY: 'scrollVer' },
              { view: 'scrollbar', id: 'scrollHor' }
            ]
          },
          { view: 'scrollbar', id: 'scrollVer' }
        ]
      }
      gantt.config.round_dnd_dates = false
      gantt.config.open_tree_initially = true
      gantt.config.duration_unit = 'hour'
      gantt.config.skip_off_time = false
      gantt.config.min_column_width = 50
      gantt.config.auto_scheduling = false
      gantt.config.auto_scheduling_strict = false
      gantt.config.show_tasks_outside_timescale = true
      gantt.config.smart_rendering = true
      gantt.config.sort = true
      gantt.config.static_background = true
      gantt.config.fit_tasks = true
      gantt.config.order_branch = true
      gantt.config.order_branch_free = true
      gantt.config.order_branch = 'marker'
      gantt.templates.timeline_cell_class = (task, date) => {
        if (this.$props.zoomLevel === 'Days' || this.$props.zoomLevel === 'Weeks') {
          if (!calendar.isWorkTime({ date, unit: 'day' })) {
            return 'weekend'
          }
        }
      }
      // enabling toolips plugins
      gantt.plugins({
        tooltip: true,
        drag_timeline: true,
        auto_scheduling: true,
        critical_path: true
      })

      // setting gantt zoom events
      gantt.ext.zoom.init({
        levels: [
          {
            name: 'Days',
            min_column_width: 90,
            scale_height: 60,
            scales: [
              { unit: 'month', step: 1, format: '%F, %Y' },
              { unit: 'day', step: 1, format: '%d %M' }
            ]
          },
          {
            name: 'Weeks',
            min_column_width: 120,
            scale_height: 60,
            scales: [
              { unit: 'month', step: 1, format: '%F, %Y' },
              {
                unit: 'week',
                step: 1,
                format: function (date) {
                  const dateToStr = gantt.date.date_to_str('%d %M')
                  const endDate = gantt.date.add(gantt.date.add(date, 1, 'week'), -1, 'day')
                  return dateToStr(date) + ' - ' + dateToStr(endDate)
                }
              }
            ]
          },
          {
            name: 'quarter',
            min_column_width: 90,
            scale_height: 60,
            scales: [
              {
                unit: 'quarter',
                step: 1,
                format: function quarterLabel (date) {
                  const month = date.getMonth()
                  let qNum

                  if (month >= 9) {
                    qNum = 4
                  } else if (month >= 6) {
                    qNum = 3
                  } else if (month >= 3) {
                    qNum = 2
                  } else {
                    qNum = 1
                  }

                  return date.getFullYear() + ' ' + 'Q' + qNum
                }
              },
              { unit: 'month', step: 1, format: '%M' }
            ]
          },
          {
            name: 'Months',
            min_column_width: 90,
            scale_height: 60,
            scales: [
              { unit: 'year', step: 1, format: '%Y' },
              { unit: 'month', step: 1, format: '%F' }
            ]
          },
          {
            name: 'year',
            min_column_width: 90,
            scale_height: 60,
            scales: [
              { unit: 'year', step: 1, format: '%Y' }
            ]
          }
        ]
      })

      // setting zoom level based on the value passed from props
      gantt.ext.zoom.setLevel(this.$props.zoomLevel)

      // attaching new function to showLightbox for showing custom dialog
      gantt.showLightbox = (id) => {
        this.selectedTask = JSON.parse(JSON.stringify(gantt.getTask(id)))
        this.selectedTask.hasChild = gantt.hasChild(id)
        gantt.ext.tooltips.tooltip.hide()
        this.showForm = true
      }

      // attaching new function to showLightbox for hiding custom dialog
      gantt.hideLightbox = () => {
        this.showForm = false
        this.selectedTask = null
      }

      // listening to gantt ready to do things after the gantt initialization is complete
      gantt.attachEvent('onGanttReady', () => {
        // creating custom tooltip for the gantt task bars
        gantt.ext.tooltips.tooltipFor({
          selector: '.gantt_task_line',
          html: function (event, domElement) {
            const id = event.target?.parentNode?.attributes?.task_id?.nodeValue
            if (id) {
              const task = gantt.getTask(id)
              const taskAssignees = []
              for (const assignee of task?.task_assignees) {
                if (assignee?.status !== 'deleted') {
                  taskAssignees.push(`<br>${assignee.assignee?.first_name || assignee?.first_name} ${assignee.assignee?.last_name || assignee?.last_name}`)
                }
              }
              if (task.type === 'milestone') {
                return `<b>Milestone:</b>  ${task?.text}  <br/>
                <b>Date: </b> ${task?.start_date.toLocaleDateString('en-US')}<br/>
                `
              } else {
                return `<b>Task:</b>  ${task?.text}  <br/>
                <b>Duration:</b>  ${task?.duration} hours <br/>
                <b>Progress:</b>  ${Math.round(task?.progress * 100)} % <br/>
                <b>Start Date: </b> ${new Date(task?.start_date).toLocaleDateString('en-GB')}<br/>
                <b>End Date: </b> ${new Date(task?.end_date).toLocaleDateString('en-GB')}<br/>
                <b>Projected Start Date: </b> ${task.projected_start_date ? new Date(task.projected_start_date).toLocaleDateString('en-GB') : '--'}<br/>
                <b>Projected End Date: </b> ${task.projected_end_date ? new Date(task.projected_end_date).toLocaleDateString('en-GB') : '--'}<br/>
                <b>Cost: </b> ${task?.cost ?? '--' }<br/>
                <b>Spi: </b> ${task?.spi ?? '--' }<br/>
                ${task.critical_progress ? `<b>Critical Progress: </b> ${task?.critical_progress} %<br/>` : '' }
                <b>Slack: </b> ${task?.slack ? task?.slack + ' hrs' : '--'}<br/>
                <b>Assigned To:</b>  ${taskAssignees} <br/>
                <b>Actual Start Date: </b> ${task.actual_start_date ? new Date(new Date(task.actual_start_date).setUTCHours(0, 0, 0, 0)).toLocaleDateString('en-GB') : '--'}<br/>
                <b>Actual End Date: </b> ${task.actual_end_date ? new Date(new Date(task.actual_end_date).setUTCHours(0, 0, 0, 0)).toLocaleDateString('en-GB') : '--'}<br/>
                `
              }
            }
          }
        })

        // disabling tooltip for task in left section of gantt
        gantt.ext.tooltips.tooltipFor({
          selector: '.gantt_row',
          html: function () {
            return null
          }
        })

        // custom tooltip for gantt links
        gantt.ext.tooltips.tooltipFor({
          selector: '.gantt_task_link',
          html: function (event, domElement) {
            const targetLinkId =
                domElement?.closest('.gantt_task_link')?.dataset.linkId
            if (gantt.isLinkExists(targetLinkId)) {
              const link = gantt.getLink(targetLinkId)
              const source = gantt.getTask(link.source)
              const target = gantt.getTask(link.target)
              return `Link from <b>${source.text}</b> to <b>${target.text}</b>`
            }
          }
        })
      })
      gantt.attachEvent('onLinkValidation', (link) => {
        const sourceId = link.source
        const targetId = link.target
        let linkErr
        gantt.eachParent((task) => {
          if (task.id === targetId) {
            linkErr = true
            return false
          }
        }, sourceId)

        if (linkErr) {
          return false
        }
        gantt.eachParent((task) => {
          if (task.id === sourceId) {
            linkErr = true
            return false
          }
        }, targetId)

        if (linkErr) {
          return false
        }

        const existingLink = gantt.getLinks().find((link) => {
          return link.source === sourceId && link.target === targetId
        })
        if (existingLink) {
          return false
        }
        return !gantt.isCircularLink(link)
      })

      gantt.attachEvent('onLinkCreated', (link) => {
        this.createdLinks.push({ source: link.source, target: link.target, type: link.type })
        return true
      })

      gantt.attachEvent('onLinkDblClick', (link) => {
        gantt.ext.tooltips.tooltip.hide()
        this.taskLinkId = link
        this.showDeleteLinkModal = true
        this.selectedLink = gantt.getLink(link)
        this.newlyAddedLag = this.selectedLink.lag ?? 0
        return false
      })

      gantt.attachEvent('onBeforeTaskDrag', (id, mode, e) => {
        const modes = gantt.config.drag_mode

        // disabling resize and drag for parent tasks
        if (mode === modes.resize || mode === modes.move) {
          // disabling move and resize for parent tasks
          return !gantt.hasChild(id)
        }
        return true
      })

      gantt.attachEvent('onBeforeRowDragEnd', (id, parent, tindex) => {
        // Check if the task is at the topmost level (root task)
        if (!parent) {
          return false
        }
        return true
      })

      gantt.attachEvent('onAfterTaskDrag', (id, mode, e) => {
        const modes = gantt.config.drag_mode

        // disabling resize and drag for parent tasks
        if (mode === modes.resize || mode === modes.move) {
          if (gantt.config.auto_scheduling) {
            gantt.autoSchedule()
          }
          this.updateParentProgress(id)
        }
      })
      // listening to progress change in order to update the progress of the parent task
      gantt.attachEvent('onTaskDrag', (id, mode, task, original) => {
      // gantt object containing the type of drag modes
        const modes = gantt.config.drag_mode
        // updation of progress from the gantt
        if (mode === modes.progress) {
          if (original.actual_start_date === null) {
            task.actual_start_date = new Date()
          }
          if (task.progress === 1) {
            task.actual_end_date = new Date()
          }
          if (task.parent !== 0) {
            this.updateParentProgress(id)
          }
        }
        if (mode === modes.resize || mode === modes.move) {
        // if end_date or start_date is beyond the limit then change the limit and re-render the gantt
          if (this.shouldUpdateGanttDateConfig(original, task)) {
            this.updateGanttDateConfig(id)
          }
        }
        return true
      })

      // updating parent progress when a new task is added
      gantt.attachEvent('onAfterTaskAdd', (id) => {
        // updating parent progress when a new task is added
        this.updateParentProgress(id)
      })

      // listening to parse event which would be called while gantt is parsing the tasks
      gantt.attachEvent('onParse', () => {
        // changing the gantt start_date and end_date before the gantt is rendered
        const range = gantt.getSubtaskDates()
        const scaleUnit = gantt.getState().scale_unit
        // setting gantt start_date and end_date for the timeline
        if (range.start_date && range.end_date) {
          gantt.config.start_date = gantt.calculateEndDate(range.start_date, -7, scaleUnit)
          gantt.config.end_date = gantt.calculateEndDate(range.end_date, 7, scaleUnit)
        }

        // sorting gantt based on order_index
        gantt.sort((a, b) => {
          const aOrderIndex = a.order_index?.split('.')
          const bOrderIndex = b.order_index?.split('.')
          const smallOrderIndex = aOrderIndex?.length < bOrderIndex?.length ? aOrderIndex : bOrderIndex
          for (let i = 0; i < smallOrderIndex?.length; i++) {
            if (parseInt(aOrderIndex[i]) > parseInt(bOrderIndex[i])) {
              return 1
            } else if (parseInt(aOrderIndex[i]) < parseInt(bOrderIndex[i])) {
              return -1
            }
          }
        })
        gantt.batchUpdate(() => {
          gantt.eachTask((task) => {
            task.attached_bom = task.task_material_associations[0]?.target_bom ? {
              id: task.task_material_associations[0]?.target_bom.id,
              name: task.task_material_associations[0]?.target_bom.name,
              quantity: task.task_material_associations[0]?.metadata?.quantity,
              associationId: task.task_material_associations[0]?.id
            } : null
            task.progress =
            task.progress > 0 ? task.progress / 100 : task.progress
            task.type = getGanttTypeFromNumber(task.type)
            task.task_assignees = task.task_assignees?.map(data => (
              {
                id: data.user_id,
                first_name: data.assignee.first_name,
                last_name: data.assignee.last_name,
                status: null
              }))
            const hasChild = gantt.hasChild(task.id)
            if (hasChild) {
              task.color = '#3CB371'
            }
            if (task.type === 'project' && !hasChild) {
              task.type = 'task'
              task.color = '#3CB371'
            }
            gantt.updateTask(task.id)
          })
        })
      })

      gantt.createDataProcessor({
        task: {
          create: (data) => {
            const task = gantt.getTask(data.id)
            this.addNewTask(JSON.parse(JSON.stringify(task)))
          },
          update: (data, id) => {
            const task = gantt.getTask(id)
            this.pushUpdatedTask(JSON.parse(JSON.stringify(task)))
          },
          delete: (id) => {
            // console.log(id)
          }
        },
        link: {
          create: (data) => {

          },
          update: (data, id) => {

          },
          delete: (id) => {

          }
        }
      })

      gantt.init(this.$refs.ganttContainer)
      gantt.config.deepcopy_on_parse = true
      gantt.attachEvent('onBeforeParse', () => {
        gantt.clearAll()
      })
      if (this.$props.tasks.data.length === 1) {
        this.$props.tasks.data[0].end_date = this.$props.tasks.data[0].planned_end_date
      }
      if (this.$props.projectedPlan) {
        const projectedTasks = this.$props.tasks.data.map(item => {
          if (item.projected_start_date) {
            item.start_date = item.projected_start_date
          }
          if (item.projected_end_date) {
            item.end_date = item.projected_end_date
          }
          return item
        })
        gantt.parse({ tasks: projectedTasks, links: this.$props.tasks.links })
        gantt.config.readonly = true
      } else {
        if (this.collaborator) {
          // disabling resize of gantt
          gantt.config.drag_resize = false
          gantt.config.details_on_dblclick = true
          gantt.config.drag_links = false
          gantt.config.drag_move = false
        }
        gantt.parse(this.$props.tasks)
      }
      gantt.render()
    },
    changeGanttZoom (value) {
      gantt.ext.zoom.setLevel(value)
    },
    closeModal () {
      if (this.selectedTask.$new) {
        gantt.deleteTask(this.selectedTask?.id)
      }
      gantt.hideLightbox()
    },
    getRequiredTaskFields (task) {
      if (Array.isArray(task?.task_docs)) {
        const taskDocs = []
        task.task_docs.forEach(taskDoc => {
          if (taskDoc.flag === 'new') {
            taskDocs.push({
              document_id: taskDoc.id
            })
          }
        })
        task.task_document_associations = {
          data: taskDocs
        }
      }

      // this conditon to provide the tag data in right format , while editing new  tasks the tags data will come as  correct format but
      // if there is any change reflected to  parent tasks then it will fetch the parent task data and update it , at this time  taskData of parent conatains tag details as {...,tag_tasks:{tag:{id:,name:,pareant_id:....}}}
      if (task?.tag_tasks?.[0]?.tag) {
        task.tag_tasks = task.tag_tasks?.map((element) => {
          return {
            id: element.tag.id,
            name: element.tag.name,
            parent_id: element.tag.parent_id
          }
        })
      }
      return {
        id: task.id,
        name: task.text,
        description: task.description,
        planned_start_date: this.projectedPlan ? task.planned_start_date : task.start_date,
        planned_end_date: this.projectedPlan ? task.planned_end_date : task.end_date,
        actual_start_date: task.actual_start_date ?? null,
        actual_end_date: task.actual_end_date ?? null,
        projected_start_date: this.projectedPlan ? task.start_date : undefined,
        projected_end_date: this.projectedPlan ? task.end_date : undefined,
        progress: task.progress,
        parent_task_id: task.parent,
        duration: this.projectedPlan ? undefined : task.duration,
        level: task.$level,
        slack: task.slack,
        type: task.type,
        status: task.status,
        is_critical: task.is_critical,
        task_assignees: task.task_assignees,
        task_document_associations: task.task_document_associations,
        tag_tasks: task.tag_tasks,
        attached_bom: task.attached_bom,
        order_index: task.order_index
      }
    },
    addNewTask (task) {
      task.progress = Math.abs(task.progress * 100)
      this.newTaskTags = task.tags
      this.editTasks.newTasks.ids[task.id] = {
        index: this.editTasks.newTasks.data.length,
        id: task.id,
        uploadedId: null
      }
      if (Array.isArray(task?.task_assignees)) {
        const taskAssignees = task?.task_assignees?.map(data => ({ user_id: data.id }))
        task.task_assignees = {
          data: taskAssignees
        }
      }
      if (task.progress === 0) {
        task.status = this.taskStatus['TO DO']
      } else if (task.progress === 100) {
        const done = 'DONE'
        task.status = this.taskStatus[done]
      } else {
        task.status = this.taskStatus['IN PROGRESS']
      }
      this.editTasks.newTasks.data.push(this.getRequiredTaskFields(task))
    },
    pushUpdatedTask (taskObj) {
      const actualDate = new Date()
      // setting the task type
      taskObj.type = getGanttTypeFromString(taskObj.type)
      taskObj.progress = Math.round(taskObj.progress > 1 ? taskObj.progress : taskObj.progress * 100)
      if (taskObj.parent === 0) {
        taskObj.parent = null
      }
      // checking if the task is newly added task
      if (this.editTasks.newTasks.ids[taskObj.id]) {
        const taskIndex = this.editTasks.newTasks.ids[taskObj.id].index
        if (Array.isArray(taskObj?.task_assignees)) {
          const taskAssignees = taskObj?.task_assignees?.map(data => ({ user_id: data.id }))

          taskObj.task_assignees = {
            data: taskAssignees
          }
        }
        this.editTasks.newTasks.data[taskIndex] = this.getRequiredTaskFields(taskObj)
        if (taskObj.progress === 0) {
          taskObj.status = this.taskStatus['TO DO']
        } else if (taskObj.progress === 100) {
          const done = 'DONE'
          taskObj.status = this.taskStatus[done]
        } else {
          taskObj.status = this.taskStatus['IN PROGRESS']
        }
        return
      }
      if (!taskObj.actual_start_date && taskObj.progress > 0) {
        taskObj.actual_start_date = actualDate
      }
      if (taskObj.progress === 100) {
        taskObj.actual_end_date = actualDate
      }
      // if task is already in the update list then mutate the value
      const taskIndex = this.editTasks.updatedTasks.ids[taskObj.id]?.index
      if (Number.isInteger(taskIndex)) {
        this.editTasks.updatedTasks.data[taskIndex] = this.getRequiredTaskFields(taskObj)
      } else {
        this.editTasks.updatedTasks.ids[taskObj.id] = {
          id: taskObj.id,
          index: this.editTasks.updatedTasks.data.length
        }
        this.editTasks.updatedTasks.data.push(this.getRequiredTaskFields(taskObj))
      }
    },
    async updateProjectPlan () {
      if (!this.autoCompute) {
        alert('Please enable auto compute before updating')
        this.$emit('save-error')
        return
      }
      this.loading = true
      const deleteTagArray = [] // in this array contain all the task ids which altered in tags ie, will remove all the tags and and update with latest tags as new entri
      const tagTasksArray = [] // this is an array stords all the task id and tagid og newly created tasks
      try {
        gantt.batchUpdate(() => {
          gantt.eachTask((task) => {
            // checking whether its the 0th level task or not
            if (task.parent) {
              const parentOrderIndex = gantt.getTask(task.parent).order_index
              const currentOrderIndex = parentOrderIndex + '.' + (task.$local_index + 1)
              if (task?.order_index !== currentOrderIndex) {
                task.order_index = currentOrderIndex
                gantt.updateTask(task.id, task)
              }
            } else {
              if (task.order_index !== '1') {
                task.order_index = '1'
                gantt.updateTask(task.id, task)
              }
            }
          })
        })
        if (this.editTasks.newTasks.data.length) {
          const newTasks = this.editTasks.newTasks.data
          const newTaskIds = this.editTasks.newTasks.ids
          for (let i = 0; i < newTasks.length; i++) {
            const task = JSON.parse(JSON.stringify(newTasks[i]))
            task.id = undefined
            if (Number(task.parent_task_id)) {
              task.parent_task_id = newTaskIds[task.parent_task_id]?.uploadedId
            }
            if (newTasks[i].attached_bom) {
              task.task_material_associations = {
                data: [
                  {
                    bom_id: newTasks[i].attached_bom.id,
                    quantity: newTasks[i].attached_bom.quantity
                  }
                ]
              }
            }

            task.tag_tasks = {
              data: task.tag_tasks?.map(tag => ({ tag_id: tag.id })) ?? []
            }

            const res = await CreateTask(task)
            newTaskIds[newTasks[i].id].uploadedId = res.insert_core_tasks_one?.id
          }
          this.newTaskIds = newTaskIds
        }
        // updating attached boms
        const attachBomUpationPromiseArr = []
        for (const item of this.attachBomUpdates) {
          if (item.tag === 'deleted') {
            attachBomUpationPromiseArr.push(DeleteBomTaskMutation(item.associationId))
          } else if (typeof item.taskId === 'string') {
            if (item.tag === 'new') {
              attachBomUpationPromiseArr.push(AddBomToTaskMutation(item.id, item.taskId, item.quantity))
            } else if (item.tag === 'updated') {
              attachBomUpationPromiseArr.push(UpdateBomTaskMutation(item.associationId, item.quantity))
            }
          } else {
            const newTaskId = this.newTaskIds[item.taskId]?.uploadedId
            attachBomUpationPromiseArr.push(AddBomToTaskMutation(item.id, newTaskId, item.quantity))
          }
        }
        await Promise.all(attachBomUpationPromiseArr)
        for (const task of this.tasks.data) {
          const { id } = task
          this.currentTasks[id] = task
        }
        this.currentTasks = JSON.parse(JSON.stringify(this.currentTasks))
        if (this.editTasks.updatedTasks.data.length) {
          const updatesArr = []
          let promiseArr2 = []
          const newTaskComments = []
          const deletedAssignees = []
          const newAssignees = []
          for (const task of this.editTasks.updatedTasks.data) {
            const clonedTask = JSON.parse(JSON.stringify(task))
            clonedTask.task_document_associations = undefined
            const id = task.id
            if (this.currentTasks[id]) {
              const task = this.currentTasks[id]
              const getStatus = (progress) => {
                if (progress === 0) {
                  return 'TO-DO'
                } else if (progress === 100) {
                  return 'DONE'
                } else {
                  return 'IN-PROGRESS'
                }
              }
              const originalStatus = getStatus(task.progress)
              const updatedStatus = getStatus(clonedTask.progress)
              if (originalStatus !== updatedStatus) {
                const newString = `Task status was modified from ${originalStatus} to ${updatedStatus}`
                newTaskComments.push({
                  comment: newString,
                  parent_id: null,
                  task_id: id
                })
              }
              const plannedStartDate = new Date(clonedTask.planned_start_date).toISOString().substr(0, 10)
              const plannedEndDate = new Date(clonedTask.planned_end_date).toISOString().substr(0, 10)
              const updatedPlannedStartDate = new Date(clonedTask.planned_start_date).toISOString().substr(0, 10)
              const updatedPlannedEndDate = new Date(clonedTask.planned_end_date).toISOString().substr(0, 10)
              if (task.duration !== clonedTask.duration || plannedStartDate !== updatedPlannedStartDate || plannedEndDate !== updatedPlannedEndDate) {
                newTaskComments.push({
                  comment: 'Task dates/schedule were modified',
                  parent_id: null,
                  task_id: id
                })
              }
              if (task.text !== clonedTask.name || task.description !== clonedTask.description) {
                const newStringValue = 'Task details were modified'
                newTaskComments.push({
                  comment: newStringValue,
                  parent_id: null,
                  task_id: id
                })
              }
              if (task.progress !== clonedTask.progress) {
                const newStringValue = `Task progress was modified from ${task.progress} to ${clonedTask.progress}%`
                newTaskComments.push({
                  comment: newStringValue,
                  parent_id: null,
                  task_id: id
                })
              }
            }
            clonedTask.id = undefined
            clonedTask.tag_tasks = undefined
            clonedTask.attached_bom = undefined
            delete clonedTask?.task_assignees
            task.task_assignees.forEach(element => {
              if (element?.status === 'new') {
                const assigneeObj = { task_id: id, user_id: element.id }
                if (element.tenantId) {
                  assigneeObj.target_tenant_id = element.tenantId
                }
                newAssignees.push(assigneeObj)
              } else if (element?.status === 'deleted') {
                const deleteAssigneeObj = { user_id: { _eq: element.id }, task_id: { _eq: id } }
                deletedAssignees.push(deleteAssigneeObj)
              }
            })
            if (newAssignees.length > 0) {
              const newString = `${newAssignees.length} Task assignees have been updated`
              newTaskComments.push({
                comment: newString,
                parent_id: null,
                task_id: id
              })
            }
            if (deletedAssignees.length > 0) {
              const newString = `${deletedAssignees.length} Task assignees has been removed`
              newTaskComments.push({
                comment: newString,
                parent_id: null,
                task_id: id
              })
            }
            // adding  tag details as an object to tagTasksArray
            for (const tag of task?.tag_tasks) {
              tagTasksArray.push({
                tag_id: tag.id,
                task_id: id
              })
            }
            // here we push the task id for  deleteing all tags associated with the   given task
            deleteTagArray.push(id)
            // await DeleteTaskTags(id) // here the id is the id of task tags
            // await AddTaskTags(taskTags)
            if (this.collaborator) {
              updatesArr.push({
                where: { id: { _eq: id } },
                _set: { progress: clonedTask.progress }
              })
            } else {
              updatesArr.push({
                where: { id: { _eq: id } },
                _set: clonedTask
              })
            }
          }
          if (newAssignees.length) {
            promiseArr2 = [...promiseArr2, addNewAssignees(newAssignees)]
          }
          if (deletedAssignees.length) {
            promiseArr2 = [...promiseArr2, deleteAssignees({ _or: deletedAssignees })]
          }
          if (this.deletedTasks.length && !this.collaborator) {
            promiseArr2 = [...promiseArr2, deleteTasks(this.deletedTasks)]
          }
          if (newTaskComments.length) {
            await addTaskComments(newTaskComments.filter((comment) => {
              const isDeletedTaskComment = this.deletedTasks.findIndex((taskId) => taskId === comment.task_id)
              return isDeletedTaskComment === -1
            }))
          }
          if (deleteTagArray.length && !this.collaborator && !this.isExternalCollaborator) {
            await deleteTaskTagsAll(deleteTagArray)
          }
          if (tagTasksArray.length && !this.isExternalCollaborator) {
            await AddTaskTags(tagTasksArray)
          }
          await Promise.all(promiseArr2)
          await UpdateTasks(updatesArr)
        }
        if (this.createdLinks.length !== 0) {
          const linkData = []
          for (const link of this.createdLinks) {
            if (Number.isInteger(Number(link.source)) || Number.isInteger(Number(link.target))) {
              for (const lagUpdation of this.lagUpdationsArray) {
                if (lagUpdation.sourceTaskId === link.source && lagUpdation.targetTaskId === link.target && lagUpdation.lag) {
                  link.lag = lagUpdation.lag
                }
              }
              if (Number.isInteger(Number(link.source))) {
                link.source = this.newTaskIds[link.source]?.uploadedId
              }
              if (Number.isInteger(Number(link.target))) {
                link.target = this.newTaskIds[link.target]?.uploadedId
              }
              linkData.push({ source: link.source, target: link.target, type: link.type, lag: link.lag })
            } else {
              linkData.push({ source: link.source, target: link.target, type: link.type })
            }
          }
          const promiseArray = linkData.map(link => InsertTaskLinks(link.source, link.target, link.type, link.lag))
          await Promise.all(promiseArray)
        }
        this.lagUpdationsArray.forEach((link) => {
          if (!(Number.isInteger(Number(link.sourceTaskId)) || Number.isInteger(Number(link.targetTaskId)))) { updateTaskLink({ lag: link.lag !== '' ? link.lag : null }, link.targetTaskId, link.sourceTaskId) }
        })
        this.attachTaskDocument(this.updatedDocs)
        this.$notify.success('Updated project plan successfully')
        this.$emit('save-success')
      } catch (err) {
        this.$emit('save-error')
        console.log(err)
        this.$notify.alert(err?.message ?? 'Something went wrong')
      } finally {
        this.loading = false
      }
    },
    addAssigneesToChildTasks (taskData) {
      const task = gantt.getTask(this.selectedTask.id)
      const childTaskIds = gantt.getChildren(task.id)
      const pushChildTasks = (id) => {
        const tasks = gantt.getTask(id)
        const arrayDataMap = new Map(tasks.task_assignees?.map(item => [item.id, item]))

        taskData.task_assignees.forEach(newItem => {
          const existingItem = arrayDataMap.get(newItem.id)
          if (existingItem) {
            if (newItem.status === 'deleted') {
              existingItem.status = newItem.status
            }
          } else {
            if (newItem.status === 'new') {
              tasks.task_assignees.push({ ...newItem })
            }
          }
        })
        const childIds = gantt.getChildren(tasks.id)
        for (const childId of childIds) {
          pushChildTasks(childId)
        }
        gantt.updateTask(tasks.id, tasks)
      }

      for (const childTaskId of childTaskIds) {
        pushChildTasks(childTaskId)
      }
    },
    updateAndCloseModal (taskData, taskDocs) {
      let hasProgressUpdated = false
      let hasDurationUpdated = false
      let hasDateChanged = false
      const task = gantt.getTask(this.selectedTask.id)
      this.addAssigneesToChildTasks(taskData)
      if (taskData.attachedBom) {
        for (const item of taskData.attachedBom) {
          item.taskId = this.selectedTask.id
          this.attachBomUpdates.push(item)
          if (item.tag === 'deleted') {
            task.attached_bom = null
          }
          if (item.tag === 'new' || item.tag === 'updated') {
            task.attached_bom = {
              id: item.id,
              name: item.name,
              quantity: item.quantity
            }
          }
        }
      }
      const shiftDays = gantt.calculateDuration(task.start_date, new Date(taskData.start_date))
      task.text = taskData.name
      task.description = taskData.description
      task.duration = Number(taskData.duration)
      task.level = task.$level
      task.status = taskData.status
      task.tag_tasks = taskData.tags.flat(1)
      hasDateChanged = this.shouldUpdateGanttDateConfig(task, taskData)
      task.start_date = new Date(taskData.start_date)
      task.end_date = gantt.calculateEndDate(task)
      if (Math.round(task.progress * 100) !== Number(taskData.progress)) {
        hasProgressUpdated = true
      }

      if (task.duration !== taskData.duration) hasDurationUpdated = true

      if (hasProgressUpdated && task.actual_start_date === null) {
        task.actual_start_date = new Date()
      }
      if (hasProgressUpdated && taskData.progress === 100) {
        task.actual_end_date = new Date()
      }
      task.progress = taskData.progress / 100
      task.type = getGanttTypeFromNumber(taskData.type)
      task.timeSpent = taskData.timeSpent
      task.task_assignees = taskData.task_assignees
      task.task_docs = taskDocs
      if (hasDateChanged) {
        this.updateGanttDateConfig(task.id)
      }

      if (task.$new) {
        gantt.addTask(task, task.parent)
        task.$new = false
      } else {
        for (const newDocs of taskDocs) {
          this.updatedDocs.push({ ...newDocs, taskId: task.id })
        }
        gantt.updateTask(task.id, task)
      }

      // if date has been shifted then update the date of the child task
      if (shiftDays) {
        this.updateSubtaskDates(task.id, shiftDays)
      }
      const todoStatus = this.tasks.data.find((task) => task.label === 'TO DO')
      const doneStatus = this.tasks.data.find((task) => task.label === 'DONE')
      const inProgressStatus = this.tasks.data.find((task) => task.label === 'IN PROGRESS')
      if (task.progress <= 0 && todoStatus) {
        this.taskData.status = todoStatus.id
      } else if (task.progress >= 100 && doneStatus) {
        this.taskData.status = doneStatus.id
      } else if (inProgressStatus) {
        this.taskData.status = inProgressStatus.id
      }
      gantt.hideLightbox()
      // if progress or duration is updated then update the parent progress also
      if (hasProgressUpdated || hasDurationUpdated) {
        this.updateParentProgress(task.id)
      }
    },
    updateSubtaskDates (id, shiftDays) {
      gantt.batchUpdate(() => {
        gantt.eachTask((child) => {
          child.start_date = gantt.calculateEndDate(child.start_date, shiftDays)
          child.end_date = gantt.calculateEndDate(child.start_date, child.duration)
          gantt.updateTask(child.id)
        }, id)
      })
    },
    updateParentProgress (id) {
      gantt.batchUpdate(() => {
        gantt.eachParent((task) => {
          const childTaskIds = gantt.getChildren(task.id)
          let completedDuration = 0
          let totalCriticalDuration = 0
          let progress = 0
          let totalWeightage = 0
          const parentTaskDuration = task.duration
          for (const childTaskId of childTaskIds) {
            const childTask = gantt.getTask(childTaskId)
            const weightage = childTask.duration / parentTaskDuration
            totalWeightage += weightage
            progress += weightage * childTask.progress
            if (childTask.is_critical) {
              totalCriticalDuration += childTask.duration
              completedDuration += childTask.duration * childTask.progress
            }
          }
          task.progress = progress / totalWeightage
          task.critical_progress = Math.round((completedDuration / totalCriticalDuration) * 100 ?? 0)
          if (task.progress === 1) {
            task.actual_end_date = new Date()
          }
          gantt.updateTask(task.id)
        }, id)
      })
    },
    shouldUpdateGanttDateConfig (originalTask, task) {
      if (+new Date(task.start_date) !== +new Date(originalTask.start_date)) {
        return true
      }
      if (+new Date(task.end_date) !== +new Date(originalTask.end_date)) {
        return true
      }
      return false
    },
    updateGanttDateConfig (id) {
      const childTask = gantt.getTask(id)
      // if end_date or start_date is beyond the limit then change the limit and re-render the gantt
      if (+new Date(childTask.start_date) - +gantt.getState().min_date <= 200000000) {
        gantt.config.start_date = gantt.date.add(new Date(childTask.start_date), -3, 'day')
      }
      if (+childTask.end_date - +gantt.getState().max_date >= -200000000) {
        gantt.config.end_date = gantt.date.add(childTask.end_date, 3, 'day')
      }
    },
    cancelDeleteLink () {
      this.newlyAddedLag = null
      this.showDeleteLinkModal = false
    },
    deleteLink () {
      this.newlyAddedLag = null
      gantt.deleteLink(this.taskLinkId)
      deleteTaskLinks(this.selectedLink.source, this.selectedLink.target)
        .then((data) => {
          success('Link Deleted Sucessfully!')
        })
        .catch(error => {
          alert('Something went Wrong!', error)
        })
      this.endPopup()
    },
    endPopup () {
      this.showDeleteLinkModal = false
      this.taskLinkId = null
    },
    async attachTaskDocument (taskDocs) {
      const addnewAttachments = []
      taskDocs.forEach((element) => {
        if (element.flag === 'new') {
          addnewAttachments.push({ task_id: element.taskId, document_id: element.id })
        } else if (element.flag === 'deleted') {
          removeTaskDocuments(element.id, element.taskId)
        }
      })
      addnewAttachments.length > 0 && await attchTaskDocs(addnewAttachments)
      // this is filtering the data that passed to material master table after updation
      // if any doc get delelted ,  here it  removes it
      taskDocs = taskDocs.filter((element) => {
        if (element.flag) {
          if (element.flag !== 'deleted') {
            return element
          }
        } else {
          return element
        }
      })
    }
  },
  watch: {
    autoCompute (value) {
      if (value) {
        this.AutoShedule()
      } else {
        this.CancelAutoShedule()
      }
    },
    showSlack (value) {
      if (value) {
        this.showSlackLayerId = gantt.addTaskLayer(this.drawSlack)
        gantt.render()
      } else {
        if (this.showSlackLayerId) {
          gantt.removeTaskLayer(this.showSlackLayerId)
          this.showSlackLayerId = ''
        }
      }
    },
    projectedPlan () {
      this.ganttInit()
    },
    tasks () {
      this.ganttInit()
    },
    zoomLevel (newZoom, oldZoom) {
      if (newZoom !== oldZoom) {
        this.changeGanttZoom(newZoom)
      }
    },
    saveProjectPlan () {
      if (this.saveProjectPlan) {
        this.updateProjectPlan()
      }
    },
    criticalPath (newCriticalPath) {
      gantt.config.highlight_critical_path = newCriticalPath
      gantt.render()
    }
  },
  mounted () {
    window.gantt = undefined
    window.Gantt = undefined
    this.ganttInit()
  },
  beforeDestroy () {
    // clearing all gantt cache
    gantt.clearAll()
    gantt.destructor()
    gantt = undefined
  }
}
</script>

  <style lang="scss">
  @import "~dhtmlx-gantt/codebase/dhtmlxgantt.css";
  @import "@/assets/scss/sizeVars.scss";

  #gantt-edit-here {
    height: 70vh;
    @media screen and (min-width: $xxl) {
    height: 80vh;
    }
    .gantt_task_bg {
      .weekend {
        background-color: var(--gray);
      }
    }
    .gantt_grid_scale .gantt_grid_head_cell,
      .gantt_task .gantt_task_scale .gantt_scale_cell {
          font-weight: 550;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.7);
      }

      // to remove the level 0 add button
      .gantt_grid_head_cell.gantt_grid_head_add.gantt_last_cell {
        display: none;
       }
  }

.gantt_row_milestone .gantt_add{ display: none; }

  .create-new-input {
    width: 600px;
    max-height: 700px;
    overflow: auto;
    margin-right: 3px;
  }

.slack {
position: absolute;
border-radius: 0;
opacity: 0.7;
border: none;
border-right: 1px solid #b6b6b6;
margin-left: -2px;
background: #b6b6b6;
background: repeating-linear-gradient(45deg, #FFFFFF,
#FFFFFF 5px,
#b6b6b6 5px,
#b6b6b6 10px);
}
  </style>
