import {
  // runMutation,
  runQuery
} from '../graphQl'
import * as insightQuery from '../query/insights'
import * as projectPlannerQuery from '../query/projectPlanner'
import http from '../http'
import config from '../../config.js'
import store from '../../store'

export const getinsightsDataApi = async (tenantId) => {
  return runQuery(insightQuery.getinsightsDataApiQuery(), { tenantId, now: new Date(new Date().setHours(0, 0, 0, 0)).toLocaleDateString('en-US') }, 'tenant')
}

export const getinsightsDataApiForProject = (tenantId, projectId, userId) => {
  if (store.getters.user.projectLevelRole === 'ADMIN') {
    return runQuery(insightQuery.getinsightsDataApiForProjectAdmin(), { tenantId, projectId }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
  } else {
    return runQuery(insightQuery.getinsightsDataApiForProject(), { tenantId, projectId, userId }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
  }
}

export const getParentLevelTasks = () => {
  return runQuery(insightQuery.getParentLevelTask(), {}, 'project')
}

export const GetChildrenTasks = (id) => {
  return runQuery(insightQuery.getChildrenTasks(), { id }, 'project')
}

export const getResourceChartData = (projectId) => {
  return runQuery(insightQuery.ResourceChartDataQuery(), { projectId }, 'tenant')
}

export const getDashBoardResourceStatus = async () => {
  return http.POST(config.serverEndpoint + '/dashboard/projects/resource-state-by-project', {}, 'tenant')
}

export const getDashBoardFormData = async (projectIds, isProject = false) => {
  return http.POST(config.serverEndpoint + '/dashboard/forms/overdue-and-active', { projectIds: projectIds }, isProject ? 'project' : 'tenant')
}
export const getBurnoutchartDataApi = async (projectIds, startDate, endDate) => {
  return http.POST(config.serverEndpoint + '/dashboard/projects/milestone-burnup', { projectIds, startDate, endDate }, 'project')
}
export const getbudgetOverViewChartApi = async (projectIds, startDate, endDate) => {
  return http.POST(config.serverEndpoint + '/dashboard/projects/budget-overview', { projectIds, startDate, endDate }, 'project')
}
export const getResourceActualvsPlanApi = async (filter) => { // need this  body required  to fetch  paticular resourse
  return http.POST(config.serverEndpoint + '/dashboard/projects/resource-production-actual-vs-plan', { resourceIds: filter?.resourceIds, tagId: filter?.tags?.id, resourceGroupIds: filter?.ResourceGroup }, 'project')
}
export const getTaskStatusAgg = async () => {
  return http.POST(config.serverEndpoint + '/dashboard/projects/tasks-status-agg', {}, 'project')
}

export const getTaskDatabyuserId = (userId, currentDate, afterfivedys, tokenType = 'tenant') => {
  const overDueConditions = { _and: [{ task_assignees: { user_id: { _eq: userId } } }, { planned_end_date: { _lt: currentDate } }, { progress: { _neq: 100 } }, { core_project: { deleted: { _eq: false } } }, { type: { _eq: 1 } }] }
  const runningConditions = { _and: [{ task_assignees: { user_id: { _eq: userId } } }, { planned_start_date: { _lte: currentDate } }, { planned_end_date: { _gte: currentDate } }, { core_project: { deleted: { _eq: false } } }, { type: { _eq: 1 } }] }
  const upcomingConditions = { _and: [{ task_assignees: { user_id: { _eq: userId } } }, { planned_start_date: { _gt: currentDate } }, { planned_start_date: { _lte: afterfivedys } }, { core_project: { deleted: { _eq: false } } }, { type: { _eq: 1 } }] }
  if (store.getters.collaborator && store.getters.projectIdForCollaborator) {
    overDueConditions._and.push({ project_id: { _eq: store.getters.projectIdForCollaborator } })
    runningConditions._and.push({ project_id: { _eq: store.getters.projectIdForCollaborator } })
    upcomingConditions._and.push({ project_id: { _eq: store.getters.projectIdForCollaborator } })
  }
  return runQuery(insightQuery.getTaskDatabyuserIdQuery(), { overDueConditions, runningConditions, upcomingConditions }, tokenType)
}

export const getTemplateIdsWithUserId = (userId) => {
  return runQuery(insightQuery.getTemplateIdWithUserIdQuery(), { userId }, 'tenant')
}

export const getFormDatabyuserId = (afterfivedys, userId, fieldIds = [], tokenType = 'tenant') => {
  const conditions = {
    forms_user_list:
    { user_id: { _eq: userId } },
    form_type_id: { _neq: 11 },
    status: { _in: [1, 3] },
    due_date: { _lte: afterfivedys }
  }
  if (store.getters.collaborator) {
    // this is to filter forms which are not in draft mode for collab
    if (store.getters.projectIdForCollaborator) {
      conditions.project_id = { _eq: store.getters.projectIdForCollaborator }
    }
  }
  return runQuery(insightQuery.getFormDatabyuserIdQuery(), { conditions, field_id: fieldIds }, tokenType)
}

export const getTaskDatabyTaskId = (taskId, token, isCollaborator = false) => {
  return runQuery(projectPlannerQuery.getTaskDataByIdQuery(), { isCollaborator, taskId }, 'current', token)
}
