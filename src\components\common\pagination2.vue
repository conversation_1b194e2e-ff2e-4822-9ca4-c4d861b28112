<template>
    <div class="pagination" v-if="length">
      <!-- <button class="nav-button" :disabled="pageNumber === 1" @click="$emit('selectPage', 1)">
        &#60;&#60;
      </button> -->
      <button class="nav-button" :disabled="pageNumber === 1" @click="$emit('selectPage', pageNumber - 1)">
        &#60;
      </button>
      <div v-for="num in numberArray" :key="num.id">
        <span v-if="num === -1"> ... </span>
        <button
          v-else
          class="page-button"
          :class="{ active: num === pageNumber }"
          @click="$emit('selectPage', num)"
        >
          {{ num }}
        </button>
      </div>
      <button class="nav-button" :disabled="pageNumber === totalPage" @click="$emit('selectPage', pageNumber + 1)">
        &#62;
      </button>
      <!-- <button class="nav-button" :disabled="pageNumber === totalPage" @click="$emit('selectPage', totalPage)">
        &#62;&#62;
      </button> -->
    </div>
  </template>

<script>
export default {
  name: 'Pagination',
  props: {
    length: {
      type: Number,
      default: 0
    },
    pageNumber: {
      type: [Number, String],
      default: 1
    },
    perPage: {
      type: [Number, String],
      default: 10
    }
  },
  computed: {
    totalPage () {
      return (
        Math.ceil(this.length / this.perPage)
      )
    },
    numberArray () {
      const totalButtonCount = this.totalPage
      if (totalButtonCount <= 3) {
        return [...Array(totalButtonCount).keys()].map((i) => i + 1)
      }
      if (this.pageNumber === 1) {
        return [1, 2, -1, totalButtonCount]
      }
      if (this.pageNumber === 2) {
        return [1, 2, 3, -1, totalButtonCount]
      }
      if (this.pageNumber === totalButtonCount) {
        return [1, -1, totalButtonCount - 1, totalButtonCount]
      }
      if (this.pageNumber === totalButtonCount - 1) {
        return [1, -1, totalButtonCount - 2, totalButtonCount - 1, totalButtonCount]
      }
      return [-1, this.pageNumber - 1, this.pageNumber, this.pageNumber + 1, -1]
    }
  }
}
</script>

  <style scoped lang="scss">
  .pagination {
    display: flex;
    width: fit-content;
    align-items: center;
    gap: 5px;
    padding-top: 15px;
  }

  .page-button, .nav-button {
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ccc;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .page-button:hover, .nav-button:hover {
    background: #CFD8DC;
  }

  .page-button.active {
    background: #CFD8DC;
    // color: white;
    // border-color: black;
  }

  .nav-button {
    background: #f5f5f5;
  }

  .nav-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .ellipsis {
    padding: 0 10px;
    font-size: 16px;
  }
  </style>
