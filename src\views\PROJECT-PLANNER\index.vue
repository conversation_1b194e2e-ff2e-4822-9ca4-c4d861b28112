<template>
  <div>
    <div v-if="loading" class="flex center loading-container">
      <LoadingCircle />
    </div>
    <div v-else-if="isOnProjectLevel || collaborator" class="container">
            <div class="space-between v-center project-planner-bar px-3">
        <h4 class="weight-500 xxl">Project Planner</h4>
        <div class="project-metrics flex" v-if="!loading">
          <div class="spi-text center" v-if="tasks?.data?.[0]?.spi">
            <span>SPI :  </span>
            <span>{{ tasks?.data?.[0]?.spi }}</span>
          </div>
          <div class="spi-text center" v-if="tasks?.data?.[0]?.cost">
            <span>COST :  </span>
            <span>{{ tasks?.data?.[0]?.cost }}</span>
          </div>
        </div>
        <div class="mr-3" v-if="(isProjectEditor || isProjectAdmin || isProjectCollaborator)">
          <button class="btn pointer spi-button" @click="computeSpi" :disabled="spiComputationLoading">
            <Spinner v-if="spiComputationLoading"/>
           <span v-else>Compute SPI and cost</span>
          </button>
        </div>
        <div class="mr-3" v-if="!error">
          <button class="toggle toggle-left pointer" :class="{ 'toggle-selected': view === 'chart' }"
            @click="changeView('chart')">
            Chart
          </button>
          <button class="toggle toggle-right pointer" :class="{ 'toggle-selected': view === 'board' }"
            @click="changeView('board')">
            Board
          </button>
        </div>
      </div>
      <div v-if="!error && view === 'chart'">
        <div class="flex-end my-2 gap-1 v-center">
          <div class="v-center">
           <span class="mr-3">Projected Plan:</span>
            <label class="switch">
              <input type="checkbox" v-model="projectedPlan" @click="toggleProjectedPlan">
              <span class="slider round"></span>
            </label>
          </div>
          <div class="flex v-center gap-1" v-if="!collaborator">
            <span>Show Slack:</span>
            <label class="switch">
            <input type="checkbox" v-model="showSlack" @click="toggleShowSlack">
            <span class="slider round"></span>
            </label>
        </div>
          <div class="flex v-center gap-1" v-if="!collaborator">
            <span>Critical Path:</span>
            <label class="switch">
            <input type="checkbox" v-model="criticalPath" @click="toggleCriticalPath">
            <span class="slider round"></span>
            </label>
        </div>
          <div class="flex-end gap-1">
            <div v-if="(isProjectEditor || isProjectAdmin || isProjectCollaborator || collaborator) && isEditMode === true" class="auto-compute flex">
              <label class="mt-2 mr-2" for="">Auto Compute:</label>
              <label class="switch">
                <input type="checkbox" v-model="autoCompute" @click="toggleAutoCompute">
                <span class="slider round"></span>
              </label>
            </div>
          </div>
          <div>
            <button v-if="!collaborator && isEditMode === true" @click="OpenAssigneeChangeModal" class="btn btn-black">Change Assignee</button>
          </div>
          <div class="v-center" v-show="(isProjectEditor || isProjectAdmin || isProjectCollaborator) && isEditMode===false">
            Baseline
            <dropdown class="ml-2" :selected="selectedBaseline" @toggle="newBaseline = false" :list="filterbaselineList" @selected="selectBaseline" :clearBaseline="clearBaseline">
              <input placeholder="Search here..." class="searchbar" v-model="searchkeyword">
              <li @click="newBaseline = !newBaseline">Set baseline</li>
              <div class="dropdown-input v-center" v-if="newBaseline">
                   <input type="text" v-model="newBaselineName">
                   <img  class="save" @click="setNewBaseline" src="~@/assets/images/icons/save-icon.svg" alt="">
                   <img  class="close" @click="closeBaseLine" src="~@/assets/images/close.png" alt="">
               </div>
               <div>
               </div>
            </dropdown>
          </div>
          <gantt-zoom :zoomLevel="zoomLevel" @update-scale="updateScale" />
          <button class="btn btn-black" @click="switchToEditMode" v-if="(isProjectEditor || isProjectAdmin || isProjectCollaborator || collaborator) && isEditMode===false">Edit Project Plan</button>
          <div class="flex gap-1 mr-3">
            <button class="btn btn-black" v-if="(isProjectEditor ||isProjectAdmin || isProjectCollaborator || collaborator) && isEditMode === true"
              @click="saveProjectPlan = true">Save</button>
            <button class="btn btn-black" v-if="(isProjectEditor || isProjectAdmin || isProjectCollaborator || collaborator) && isEditMode === true" @click="refreshData">Cancel</button>
          </div>
        </div>
        <gantt  v-if="!isEditMode"
        :tasks="tasks"
        :calendar = "calendar"
        :zoomLevel="zoomLevel"
        @task-selected="lastSelectedTaskId = $event"
        :criticalPath="criticalPath"
        :baseline="baseline"
        :projectedPlan ="projectedPlan"
        :showSlack = "showSlack"
        :clearSelectedBaseLine="clearSelectedBaseLine"
        />
        <gantt-edit-view v-else
        :showSlack = "showSlack"
        :tasks="tasks"
        :zoomLevel="zoomLevel"
        :saveProjectPlan="saveProjectPlan"
        @save-error="saveProjectPlan = false"
        @save-success="refreshData"
        :criticalPath="criticalPath"
        :calendar = "calendar"
        :autoCompute="autoCompute"
        :projectedPlan="projectedPlan"
        />
      </div>
      <div v-else-if="!error && view == 'board'">
        <kanban class="kanban" :tasks="tasks" :filters="filters" @addFilter="addFilters" />
      </div>
      <div v-else class="flex mt-3 l weight-400 v-center">{{ error }}</div>
    </div>
    <div v-else>
      <div class="space-between v-center project-planner-bar px-3">
        <h4 class="weight-500 xxl">Time Line</h4>
                <div class="mr-3">
          <button class="toggle toggle-left pointer" :class="{ 'toggle-selected': timelineView === 'users' }"
            @click="changeTimeLineView('users')">
            Users
          </button>
          <button class="toggle toggle-right pointer" :class="{ 'toggle-selected': timelineView === 'resources' }"
            @click="changeTimeLineView('resources')">
            Resources
          </button>
        </div>
      </div>
<timeLine :view="timelineView"/>
    </div>
    <modal
    :open="changeAssigneeConfig.openModal === true"
    @close="cancelAssignee"
    :closeOnOutsideClick="true"
    title="Change Assignee">
    <div v-if="changeAssigneeConfig.modalLoading" class="flex center loader">
      <LoadingCircle />
    </div>
    <div v-else class="modalbox">
    <div>
     <div class="grid-2">
      <div class="input-group imp" data-validation="project">
        <label class="key">Project Selected:</label>
        <select :disabled="true">
          <option>{{this.currentProject.name}}</option>
        </select>
      </div>
      <div class="imp" data-validation="status">
        <label class="key">Task Status:</label>
        <div class="input-group">
        <input type="text"  @click="open = !open; $event.stopPropagation()" :value="displaySelectedStatus" placeholder="Select Status" readonly />
      </div>
      <div v-if="open" ref="dropdown">
        <div  class="statusList">
          <div class="flex mt-1">
          <input type="checkbox" :checked="isAllSelected"
          @change="toggleSelectAll"/>
          <div class="ml-3">Select All</div>
        </div>
          <div class="flex mt-1" v-for="(status) in changeAssigneeConfig.taskStatus" :value="status.id" :key="status.id" @click="toggleTaskStatus(status)">
            <input type="checkbox" :checked="changeAssigneeConfig.status.some((item) => item.id === status.id)"/>
            <div class="ml-3">
                {{ status.name }}
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
      <div class="input-group imp" data-validation="current-assignee">
        <label class="key">Current Assignee:</label>
        <select :disabled="!changeAssigneeConfig.status" v-model="changeAssigneeConfig.currentAssignee" >
          <option v-if="!changeAssigneeConfig.currentAssignees.length">No users found</option>
          <option v-for="(assignee, index) in changeAssigneeConfig.currentAssignees" :key="index" :value="assignee.user_id">
            {{ assignee.assignee.first_name }} {{ assignee.assignee.last_name }}
      </option>
        </select>
      </div>
      <div class="grid-2 mt-2">
          <div class="input-group imp">
            <label class="key">Company:</label>
            <select :disabled="!changeAssigneeConfig.currentAssignee" v-model="changeAssigneeConfig.newAssigneeTenantId" @change="getAllUsers">
              <option v-for="item in tenantList" :key="item.id" :value="item.id">
                    {{ item.company_name }}
                  </option>
                  <optgroup label="Child Tenants" v-if="childTenantsList.length">
                    <option v-for="childTenant in filterChildTenant" :key="childTenant.id"
                      :value="childTenant.target_tenant.id"
                      :disabled="childTenant.status === 2 || childTenant.status === 3">
                      {{ childTenant.target_tenant.company_name }}
                    </option>
                  </optgroup>
            </select>
          </div>
          <div class="input-group imp mt-3">
            <label class="key">New Assignee</label>
            <div :class="{
              'form-input': true,
            }">
              <div v-if="changeAssigneeConfig.selectedAssignee.id" class="form-user-bedge">
                <div>
                  <div class="flex">
                    <div class="form-user-bedge__name">
                      {{ changeAssigneeConfig.selectedAssignee.first_name }} {{ changeAssigneeConfig.selectedAssignee.last_name }}
                    </div>
                    &nbsp; {{ changeAssigneeConfig.selectedAssignee?.status === 4 ? "(Invitation not accepted yet)" : null }}
                  </div>

                  <div class="form-user-bedge__email">{{ changeAssigneeConfig.selectedAssignee.email }}</div>
                </div>

                <div v-if="changeAssigneeConfig.selectedAssignee.id" class="form-user-bedge__action" @click="removeUser">
                  <img src="~@/assets/images/delete-gray-icon.svg" width="20px" />
                </div>
              </div>
              <input v-else type="text" class="form-user-search" v-model="changeAssigneeConfig.userSearchKeyword" @focus="changeAssigneeConfig.userListOpen = true" />
              <div class="form-input--options" v-if="changeAssigneeConfig.userListOpen && !changeAssigneeConfig.selectedAssignee.id">
                <div v-if="getUserList.length === 0" class="form-input--option">
                  <div class="form-input--option__name">No users found</div>
                </div>
                <div class="form-input--option" v-for="user in getUserList" :key="user.id" @click="selectUser(user)">
                  <div class="form-input--option__name">
                    {{ user.first_name }} {{ user.last_name }}
                  </div>
                  <div class="form-input--option__email">{{ user.email }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
     <div class="buttons">
      <button :disabled="!changeAssigneeConfig.selectedAssignee.id" @click="saveAssignee" class="btn">Save</button>
      <button @click="cancelAssignee" class="btn btn-black">Cancel</button>
     </div>
    </div>
  </div>
    </modal>
  </div>
</template>
<script>
import { getProjectTasksAndLinks, SaveBaseline, GetProjectImports, GetS3DownloadUrl, GetCurrentCalendarList, computeSpiAndCost, getAllTaskUsers, GetTaskStatus, ChangeGanttAssignee, getTenantUsersListwithInvitedUsers, GetUserListByPojIds } from '@/api'
import { mapGetters } from 'vuex'
import LoadingCircle from '@/components/common/loadingCircle.vue'
import Dropdown from '../../components/common/dropdown.vue'
import Gantt from '@/components/projectPlanner/gantt.vue'
import GanttEditView from '@/components/projectPlanner/ganttEditView.vue'
import GanttZoom from '@/components/projectPlanner/ganttZoom.vue'
import Kanban from '@/components/projectPlanner/kanban.vue'
import Loader from '@/plugins/loader'
import { success, alert } from '@/plugins/notification'
import TimeLine from '@/components/projectPlanner/timeLine.vue'
import Spinner from '@/components/common/spinner.vue'
import modal from '@/components/common/modal.vue'

export default {
  name: 'ProjectPlanner',
  components: { Gantt, GanttEditView, GanttZoom, LoadingCircle, Kanban, Dropdown, TimeLine, Spinner, modal },
  data () {
    return {
      changeAssigneeConfig: {
        modalLoading: false,
        status: [],
        newAssigneeTenantId: null,
        selectedAssignee: {},
        currentAssignee: null,
        currentAssignees: [],
        tenantUsers: [],
        taskStatus: [],
        openModal: false,
        userListOpen: false
      },
      autoCompute: false,
      loading: false,
      zoomLevel: 'Days',
      isEditMode: false,
      saveProjectPlan: false,
      showSlack: false,
      tasks: {
        data: [],
        links: []
      },
      error: '',
      messages: [],
      view: 'chart',
      timelineView: 'users',
      filters: {
        tagsFilter: [],
        assigneeFilter: [],
        from: '',
        upto: '',
        taskNameFilter: ''
      },
      projectedPlan: false,
      criticalPath: false,
      newBaseline: false,
      newBaselineName: '',
      baselineList: [],
      selectedBaseline: { id: -1, name: 'Select' },
      baseline: null,
      calendar: {
        holidays: [],
        workTimeHours: [],
        workTimeDays: []
      },
      spiComputationLoading: false,
      clearSelectedBaseLine: false,
      searchkeyword: null,
      open: false
    }
  },
  watch: {
    '$store.state.projectIdForCollaborator' () {
      this.isEditMode = false
      this.autoCompute = false
      this.saveProjectPlan = false
      this.init()
    },
    'changeAssigneeConfig.status' (newStatus) {
      if (newStatus) {
        this.fetchAssignee(newStatus)
      }
    }
  },
  computed: {
    ...mapGetters(['tenantUsersList', 'currentProject', 'isProjectAdmin', 'isProjectEditor', 'isOnProjectLevel', 'isProjectCollaborator', 'collaborator', 'tenantList', 'childTenantsList', 'openProjectId', 'openTenantId']),
    filterbaselineList () {
      if (this.searchkeyword) {
        return this.baselineList.filter((element) => element?.name?.toLowerCase()?.match(new RegExp(this.searchkeyword?.toLowerCase())))
      } else {
        return this.baselineList
      }
    },
    displaySelectedStatus () {
      if (this.changeAssigneeConfig.status.length > 3) {
        const statusName = this.changeAssigneeConfig.status.slice(0, 3).map(status => status.name).join(', ')
        const remainingCount = this.changeAssigneeConfig.status.length - 3
        return `${statusName} +${remainingCount}`
      } else {
        return this.changeAssigneeConfig.status.map(status => status.name).join(', ')
      }
    },
    filteredNewAssignees () {
      // Exclude the current assignee from the list of new assignees
      return this.changeAssigneeConfig.currentAssignees.filter(
        (assignee) => assignee.user_id !== this.changeAssigneeConfig.currentAssignee
      )
    },
    filterChildTenant () {
      return this.childTenantsList.filter((item) => item.target_tenant.status === 1)
    },
    getUserList () {
      if (!Array.isArray(this.changeAssigneeConfig.tenantUsers)) {
        return []
      }
      let usersList
      if (this.changeAssigneeConfig.userSearchKeyword) {
        usersList = this.changeAssigneeConfig.tenantUsers.map((item) => {
          return {
            ...item.associated_user
          }
        }).filter((item) => `${item.first_name} ${item.last_name}`.replace(' ', '').toLowerCase().includes(this.changeAssigneeConfig.userSearchKeyword.toLowerCase().replace(' ', '')))
      } else {
        usersList = this.changeAssigneeConfig.tenantUsers.map((item) => {
          return {
            ...item.associated_user
          }
        })
      }
      return usersList
    },
    isAllSelected () {
      if (this.changeAssigneeConfig.taskStatus.length) {
        return this.changeAssigneeConfig.status.length === this.changeAssigneeConfig.taskStatus.length
      } else {
        return this.changeAssigneeConfig.status
      }
    }
  },
  methods: {
    handleClickOutside (event) {
      // Check if the click target is outside the dropdown element
      if (this.$refs.dropdown && !this.$refs.dropdown.contains(event.target) && event.target !== this.$refs.dropdown) {
        this.open = false // Close the dropdown
      }
    },
    toggleTaskStatus (status) {
      const index = this.changeAssigneeConfig.status.findIndex((item) => item.id === status.id)
      if (index === -1) {
        this.changeAssigneeConfig.status.push(status)
      } else {
        this.changeAssigneeConfig.status.splice(index, 1)
      }
    },
    toggleSelectAll () {
      if (this.isAllSelected) {
        // If all are selected, clear the selection
        this.changeAssigneeConfig.status = []
      } else {
        // If not all are selected, select all
        this.changeAssigneeConfig.status = this.changeAssigneeConfig.taskStatus.map(status => status)
      }
    },
    async fetchAssignee (status) {
      const statusId = status.map(s => s.id)
      try {
        const response = await getAllTaskUsers(statusId)
        this.changeAssigneeConfig.currentAssignees = response.task_assignee
      } catch (error) {
        console.error('Error fetching assignees:', error)
      }
    },
    selectUser (user) {
      this.changeAssigneeConfig.selectedAssignee = user
      this.changeAssigneeConfig.userListOpen = false
    },
    removeUser () {
      this.changeAssigneeConfig.selectedAssignee = {}
      this.changeAssigneeConfig.userListOpen = true
    },
    saveAssignee () {
      const statusChangesId = this.changeAssigneeConfig.status.map((item) => item.id)
      ChangeGanttAssignee(statusChangesId, this.changeAssigneeConfig.currentAssignee, this.changeAssigneeConfig.selectedAssignee.id, this.changeAssigneeConfig.newAssigneeTenantId).then(res => {
        if (res.update_task_assignee <= 0) {
          success('No changes in assignees!')
          return
        }
        success('Assignee changed succesfully!')
        this.cancelAssignee()
        this.init()
      }).catch(error => {
        alert(error?.message ?? 'something went wrong. Try again!')
      })
    },
    cancelAssignee () {
      this.changeAssigneeConfig.status = []
      this.changeAssigneeConfig.currentAssignee = null
      this.changeAssigneeConfig.selectedAssignee = {}
      this.changeAssigneeConfig.newAssigneeTenantId = null
      this.changeAssigneeConfig.openModal = false
    },
    getAllUsers () {
      this.changeAssigneeConfig.selectedAssignee = {}
      if (this.changeAssigneeConfig.newAssigneeTenantId != null) {
        if (this.openTenantId === this.changeAssigneeConfig.newAssigneeTenantId) {
          GetUserListByPojIds([this.openProjectId], [1, 4]).then((res) => {
            this.changeAssigneeConfig.tenantUsers = res.project_user_association
          })
        } else {
          getTenantUsersListwithInvitedUsers(this.changeAssigneeConfig.newAssigneeTenantId, true).then((res) => {
            this.changeAssigneeConfig.tenantUsers = res.tenant_user_association
          })
        }
      }
    },
    async OpenAssigneeChangeModal () {
      this.changeAssigneeConfig.openModal = true
      this.changeAssigneeConfig.modalLoading = true
      const [taskStatusRes] = await Promise.all([GetTaskStatus()])
      this.changeAssigneeConfig.taskStatus = taskStatusRes.custom_list_values
      this.changeAssigneeConfig.modalLoading = false
    },
    clickOutside (e) {
      if (!this.$refs.options?.contains(e.target)) {
        this.closeOptions()
      }
    },
    closeBaseLine () {
      this.newBaseline = !this.newBaseline
      this.newBaselineName = ''
    },
    changeTimeLineView (view) {
      this.$router.push(`?view=${view}`)
      this.timelineView = view
      this.selectedBaseline = { id: -1, name: 'Select' }
    },
    computeSpi () {
      this.spiComputationLoading = true
      computeSpiAndCost().then((res) => {
        this.fetchProjectTasksAndLinks()
        success('Successfully updated SPI and cost')
      }).catch(() => {
        alert('Failed to update SPI and cost')
      }).finally(() => {
        this.spiComputationLoading = false
      })
    },
    toggleShowSlack () {
      this.showSlack = !this.showSlack
      if (this.showSlack) {
        this.selectedBaseline = { id: -1, name: 'Select' }
        this.baseline = null
        this.projectedPlan = false
      }
    },
    clearBaseline () {
      this.clearSelectedBaseLine = !this.clearSelectedBaseLine
      if (this.clearSelectedBaseLine) {
        this.selectedBaseline = { id: -1, name: 'Select' }
        this.baseline = null
      }
    },
    toggleAutoCompute () {
      this.autoCompute = !this.autoCompute
    },
    addFilters (dateFilter, assigneeFilter, tagsFilter, taskNameFilter) {
      let userFilter = []
      let from, upto
      if (dateFilter.from) {
        from = new Date(dateFilter.from).toISOString()
      }
      if (dateFilter.upto) {
        upto = new Date(dateFilter.upto).toISOString()
      }
      this.filters.from = from
      this.filters.upto = upto
      this.filters.assigneeFilter = assigneeFilter
      this.filters.tagsFilter = tagsFilter
      this.filters.taskNameFilter = taskNameFilter
      if (assigneeFilter.length) userFilter = assigneeFilter.map((item) => item.id)
      this.fetchProjectTasksAndLinks(from, upto, userFilter, tagsFilter, taskNameFilter)
    },
    updateScale (event) {
      this.zoomLevel = event.target.value
    },
    toggleProjectedPlan () {
      this.projectedPlan = !this.projectedPlan
      if (this.projectedPlan) {
        this.selectedBaseline = { id: -1, name: 'Select' }
        this.baseline = null
        this.showSlack = false
      }
    },
    toggleCriticalPath () {
      this.criticalPath = !this.criticalPath
    },
    switchToEditMode () {
      this.isEditMode = true
      this.projectedPlan = false
      this.fetchProjectTasksAndLinks()
    },
    async fetchProjectTasksAndLinks (from = null, upto = null, assigneeFilter, tagsFilter, taskNameFilter) {
      try {
        this.loading = true
        if (this.currentProject?.id || this.collaborator) {
          if (this.$route.query?.view !== 'board' && !this.collaborator) {
            this.fetchProjectImports()
          }
          const variables = {
          }
          if (from) {
            variables.from = from
          }
          if (upto) {
            variables.upto = upto
          }
          if (assigneeFilter?.length) {
            variables.assignees = assigneeFilter
          }
          if (tagsFilter?.length) {
            variables.tagId = tagsFilter?.length ? tagsFilter[tagsFilter?.length - 1]?.id : undefined
          }
          if (taskNameFilter) {
            variables.taskName = taskNameFilter
          }
          const view = this.$route.query?.view ?? 'chart'
          const [projectPlanner, calendar] = await Promise.all([getProjectTasksAndLinks(variables, view), GetCurrentCalendarList()])
          this.loading = false
          if (!this.$route.query?.view || this.$route.query.view === 'chart') {
            if (!projectPlanner.core_tasks.length) {
              this.error =
                'The selected project does not have any tasks associated with it'
              return
            }
          }
          this.tasks.data = projectPlanner.core_tasks
          this.tasks.links = projectPlanner.task_links
          this.calendar.workTimeHours = [
                `${calendar?.core_project_calendar[0]?.working_hours_start.split(':').slice(0, -1).join(':')}-${calendar?.core_project_calendar[0]?.working_hours_end.split(':').slice(0, -1).join(':')}`
          ]
          const workingDaysArr = new Array(7).fill(0)
          for (const workingDay of calendar?.core_project_calendar[0]?.calendar_working_days) {
            if (!workingDay.deleted) workingDaysArr[workingDay.work_day] = 1
          }
          this.calendar.workTimeDays = workingDaysArr
          this.calendar.holidays = calendar?.core_project_calendar[0]?.calendar_holidays || []
        } else {
          this.error = 'Please select a project to see the project plan'
          this.loading = false
        }
      } catch (error) {
        this.error =
          error?.message ?? 'Something went wrong ! Please try again'
        this.loading = false
      }
    },
    changeView (view) {
      this.$router.push(`?view=${view}`)
      this.view = view
      this.selectedBaseline = { id: -1, name: 'Select' }
      this.fetchProjectTasksAndLinks()
    },
    refreshData () {
      this.isEditMode = false
      this.autoCompute = false
      this.saveProjectPlan = false
      this.fetchProjectTasksAndLinks()
    },
    setNewBaseline () {
      const loader = new Loader()
      loader.show()
      if (!this.newBaselineName.trim()) {
        loader.hide()
        return
      }
      SaveBaseline(this.newBaselineName.trim()).then((res) => {
        this.newBaseline = false
        this.fetchProjectImports()
        this.newBaselineName = ''
        success('New baseline saved')
      }
      ).catch(err => {
        if (err?.message === 'GraphQL error: Baseline name already exists') {
          alert('Baseline name already exists')
        } else {
          alert(err?.message ?? 'Something went wrong')
        }
      }).finally(() => {
        loader.hide()
      })
    },
    fetchProjectImports () {
      GetProjectImports().then((res) => {
        this.baselineList = res?.project_imports
      }).catch(() => {
        alert('Failed to fetch baseline')
      })
    },
    selectBaseline (baseline) {
      const loader = new Loader()
      loader.show()
      this.selectedBaseline = baseline
      this.showSlack = false
      this.clearSelectedBaseLine = false
      GetS3DownloadUrl({ S3Key: baseline.blob_key, fileName: baseline?.id }).then((res) => {
        if (res.url) {
          fetch(res.url).then(res => res.json()).then(res => {
            this.baseline = res.data
          })
        }
      }).catch((err) => {
        console.log(err)
        alert('Failed to fetch')
      }).finally(() => {
        loader.hide()
      })
    },
    init () {
      if (this.isOnProjectLevel || this.collaborator) {
        if (this.$route.query?.view !== 'chart' && this.$route.query?.view !== 'board') {
          this.$router.replace({ query: { view: 'chart' } })
        }
        this.view = this.$route.query?.view
        this.fetchProjectTasksAndLinks()
      }
      if (!this.isOnProjectLevel) {
        if (this.$route.query?.view === 'users' || this.$route.query?.view === 'resources') {
          this.timelineView = this.$route.query?.view
        }
      }
    }
  },
  mounted () {
    document.addEventListener('click', this.handleClickOutside)
    if (this.collaborator) {
      this.$store.dispatch('fetchProjectList').then(() => {
        this.init()
      })
    } else {
      this.init()
    }
  },
  beforeDestroy () {
    // Remove event listener when the component is destroyed
    document.removeEventListener('click', this.handleClickOutside)
  }
}
</script>
<style lang="scss" scoped>
  .more-button {
    cursor: pointer;
    border-radius: 50%;
    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
  }
.searchbar {
    height: 30px;
    background: transparent;
    padding-right: 52px;
    font-size: 14px;
    width: 152px;
    margin-block: 5px;
    padding: 5px 10px 5px  10px;
    border-radius: 5px;
    border: 1px solid  #888;
}
.loading-container {
  min-height: calc(100vh - 170px);
}
.loader {
  width: 600px;
  height: 300px;
  max-width: 100%;
  max-height: 100%;
}
.modalbox {
  display: flex;
  flex-direction: column;
  width: 600px;
  min-height: 350px;
  max-width: 100%;
  max-height: 100%;
  .grid-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
  .form-user-bedge {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #dbd8d8;;
  padding: 5px 10px;
  border-radius: 5px;
  margin-top: 5px;
  .form-user-bedge__name {
    font-size: 12px;
    font-weight: 600;
    color: #000000;
  }
  .form-user-bedge__email {
    font-size: 12px;
    color: #000000;
  }
  .form-user-bedge__action {
    margin-left: 10px;
    cursor: pointer;
  }
}
.form-user-search{
  padding:1.1em;
}
  .buttons {
  display: flex; /* Align buttons horizontally */
  justify-content: flex-end; /* Push buttons to the right */
  gap: 10px; /* Space between buttons */
  margin-top: auto; /* Push the buttons to the bottom */
  padding-top: 20px;
  }
}

.spi-button {
  display: flex;
  justify-content: center;
  width: 180px;
}

.project-metrics{
  margin-left: auto;
.spi-text{
  font-size: 1.2rem;
  margin-right: 1rem;
  span{
   margin-right: 0.5rem;
   font-weight: 600;
   display: block;
 }
}
}
.project-planner-bar {
  height: 60px;
  margin: -12px;
  margin-bottom: 0;
  background: var(--bg-color);
  border-bottom: var(--border);
}

.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--side-bar-color);
  -webkit-transition: .4s;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: .4s;
  transition: .4s;
}

input:checked+.slider {
background-color: var(--brand-color);
}

input:focus+.slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked+.slider:before {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

.toggle {
  font-size: 1em;
  padding: 0.5em 1.2em;
  color: var(--black);
  font-weight: 500;
  border: none;
  box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
  background-color: var(--side-bar-color);

  &:active {
    box-shadow: inset 0.2em 0.2em 0.2em rgba(0, 0, 0, 0.25);
  }

  &-left {
    border-radius: 0.3rem 0px 0px 0.3rem
  }

  &-right {
    border-radius: 0px 0.3rem 0.3rem 0px
  }

  &-selected {
    background-color: var(--brand-color);
  }
}
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}
.switch input {
 opacity: 0;
 width: 0;
 height: 0;
}
.slider {
 position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--side-bar-color);
  -webkit-transition: .4s;
  transition: .4s;
}
.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: .4s;
  transition: .4s;
}

input:checked+.slider {
  background-color: var(--brand-color);
}
input:focus+.slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked+.slider:before {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}
.dropdown-content-search{
    margin-block: 5px;
    padding: 5px 10px 5px  10px;
    border-radius: 5px;
    width: 100%;
    border: 1px solid  #888;
  }
  .dropdown-content-search::placeholder {
    color: #888;
    font-style: italic;
    }
.dropdown-input{
    position: relative;
    margin-top: 6px;
    input {
      width: 100%;
      height: 30px;
      border: 1px solid var(--brand-color);
      background: transparent;
      padding-right: 50px;
      font-size: 14px;
    }
    img {
      position: absolute;
      cursor: pointer;
      &.close {
        width: 18px;
        right: 2px;
      }
      &.save {
        width: 20px;
        right: 25px;
      }
    }
}
.statusList {
    position: absolute;
    background-color: #fff;
    border: 1px solid rgb(179, 179, 179);
    border-radius: 0.3em;
    z-index: 1;
    padding: 10px;
    width: 48%;
    max-height: 145px;
    overflow-y: auto;
  }

</style>
