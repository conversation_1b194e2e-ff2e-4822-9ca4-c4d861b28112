import './tooltip.scss'
const TOOLTIP_CONFIG = {
  showDelay: 0,
  hideDelay: 0,
  direction: 'top',
  hasHtml: false,
  msg: ''
}
const ToolTip = document.createElement('div')
ToolTip.setAttribute('id', 'tooltip')
ToolTip.style.top = '-1000px'
ToolTip.style.left = '-1000px'
document.getElementsByTagName('body')[0].append(ToolTip)

const hideTooltip = function () {
  // ToolTip.style.display = 'none'
  ToolTip.style.top = '-1000px'
  ToolTip.style.left = '-1000px'
}

const showTooltip = function (el, config) {
  const getBox = el.getBoundingClientRect()
  setToolTipInnerText(config)
  setToolTipPosition(getBox, config)
}

const setToolTipInnerText = function (config) {
  if (!config.hasHtml) {
    ToolTip.setAttribute('class', 'tooltip-container-text')
  }
  ToolTip.innerHTML = config.msg
}

const setToolTipPosition = function (targetBox, config) {
  const screenWidth = window.innerWidth
  const screenHeight = window.innerHeight
  const toolTipBox = ToolTip.getBoundingClientRect()
  ToolTip.style.position = 'fixed'
  ToolTip.style.left = undefined
  ToolTip.style.top = undefined
  ToolTip.style.bottom = undefined
  ToolTip.style.right = undefined
  // given conditons are becoming true if there is  space to place tooltip
  // tooltip placement config starts

  const bottom = targetBox.y + toolTipBox.height + 10 < screenHeight
  const top = targetBox.y > toolTipBox.height + 10
  const right = targetBox.x + toolTipBox.width / 2 + 10 < screenWidth
  const left = targetBox.x > toolTipBox.width / 2 + 10

  if (top && left && right && bottom) {
    config.direction = 'top'
  } else if (top) {
    if (top && right && left) { config.direction = 'top' } else if (right) { config.direction = 'right' } else if (left) { config.direction = 'left' } else { config.direction = 'top' }
  } else if (bottom) {
    if (bottom && right && left) { config.direction = 'bottom' } else if (right) { config.direction = 'right' } else if (left) { config.direction = 'left' } else { config.direction = 'top' }
  }

  // tooltip placement config ends

  switch (config.direction) {
  case 'left':
    ToolTip.setAttribute('class', 'tooltip-container-left')
    ToolTip.style.left = `${targetBox.left - toolTipBox.width - 10}px`
    ToolTip.style.top = `${targetBox.top + targetBox.height / 2 - toolTipBox.height / 2}px`
    break
  case 'right':
    ToolTip.style.left = `${targetBox.left + targetBox.width + 10}px`
    ToolTip.style.top = `${targetBox.top + targetBox.height / 2 - toolTipBox.height / 2}px`
    break
  case 'top':
    ToolTip.setAttribute('class', 'tooltip-container-top')
    ToolTip.style.left = `${targetBox.left + targetBox.width / 2 - toolTipBox.width / 2}px`
    ToolTip.style.top = `${targetBox.top - toolTipBox.height - 10}px`
    break
  default:
    ToolTip.style.left = `${targetBox.left + targetBox.width / 2 - toolTipBox.width / 2}px`
    ToolTip.style.top = `${targetBox.top + targetBox.height + 10}px`
  }
}

const AddToolTip = function (el, config) {
  el.addEventListener('mouseover', showTooltip.bind(null, el, config))
  el.addEventListener('mouseout', hideTooltip)
}

const RemoveToolTip = function (el) {
  hideTooltip()
  el.removeEventListener('mouseover', showTooltip)
  el.removeEventListener('mouseout', hideTooltip)
}

const aswTooltip = {
  isLiteral: true,
  inserted: function (el, binding, vnode) {
    let config = {}
    const msg = binding.value || binding.expression
    if (typeof msg === 'string') {
      config = {
        ...TOOLTIP_CONFIG,
        msg
      }
    } else {
      config = {
        ...TOOLTIP_CONFIG,
        ...msg
      }
    }
    AddToolTip(el, config)
  },
  unbind: function (el, binding, vnode) {
    RemoveToolTip(el)
  }
}

export default aswTooltip
