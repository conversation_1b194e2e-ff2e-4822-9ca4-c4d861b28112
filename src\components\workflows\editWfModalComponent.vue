<template>
  <div>
    <div class="modalcontainer">
      <!-- edit node starts  -->
      <div v-if="shape === 'node-rect'">
        <div class="input-group mt-1">
          <label class="key">Enter the name</label>
          <input
            type="text"
            v-model="editData.newLabel"
            placeholder="Enter the new name"
            maxlength="50"
          />
        </div>
        <div class="input-group mt-1">
          <label class="key">Select the Department </label>
          <div class="v-center gap-1">
          <select class="flex-grow " name="" v-model="editData.userGroup" @change="userGroupchange">
            <option v-if="userGroups.length <= 0" disabled>
              No department available
            </option>
            <option v-for="grp in userGroups" :value="grp.id" :key="grp.id">
              {{ grp.name }}
            </option>
          </select>
                  <img
                  v-if="showremoveUserGroup"
                  src="~@/assets/images/delete-icon.svg"
                  width="18"
                  alt="delete icon"
                  class="pointer"
                  @click="removeUserGroup"
                />
                </div>
        </div>
        <div class="grid-2 gap-1">
          <div class="input-group mt-1">
            <label class="key">Duration</label>
            <input
              type="number"
              v-model="editData.duration"
              placeholder="Enter the duration"
            />
          </div>
          <div class="input-group mt-1">
            <label class="key">Escalation threshold</label>
            <input
              type="number"
              v-model="editData.escalation"
              placeholder="Enter the  escalation threshold"
            />
          </div>
        </div>
        <!-- adding more next step  -->
        <div class="gap-1" >
          <hr class="my-4" />
          <div class="mt-1">
            <h3 class="v-center mb-2">
              Trigger Action
              <img
                class="pointer ml-2"
                src="@/assets/images/icons/info-icon.svg"
                width="15"
                height="15"
                alt="info icon"
                v-tooltip="
                  'An action will trigger once the workflow completes.'
                "
              />
            </h3>
            <custom-dropdown
              :list="triggerTypes"
              @select="handleActionType"
              class="form-template-container-wft"
            />
            <div class="my-2" v-if="selectedActionType['1'] && showSelectFormTemplate">
              <label>Select Form Template</label>
              <div class="flex v-center gap-1">
                <custom-dropdown
                  :list="formTempaltesFiltered"
                  :selected="editData.formTemplate"
                  @select="hadleTemplateSelect"
                  @toggle="formSearchKeyword = ''"
                  @deselect="(tempalate) => {}"
                  class="form-template-container-wft flex-grow"
                >
                  <template #before-items>
                    <div
                      class="fh input-group center p-3 form-template-container-wft-search-box"
                    >
                      <input
                        type="text p-2"
                        class="form-template-container-wft-input"
                        v-model="formSearchKeyword"
                        placeholder="Search"
                      />
                    </div>
                    <div
                      class="fh center py-4"
                      v-if="formTempaltesFiltered.length === 0"
                    >
                      No forms templates
                    </div>
                  </template>
                </custom-dropdown>
                <img
                  src="~@/assets/images/delete-icon.svg"
                  width="18"
                  alt="delete icon"
                  class="pointer"
                         @click="removeAction(1)"
                />
              </div>
            </div>

            <div v-if="selectedActionType['2']" class="mt-3">
              <label>File upload</label>
              <div class="v-center space-between mt-1">
                <!-- doc upload start or end consent   -- start -->
                <!-- as per Ravi's  request disbaled selection (default value will bw on start ) -->
                <div class="v-center gap-1 grow-1 pnt-disabled">
                <span>On : </span>
                <div class="toggle-btn">
                  <button
                    :class="[
                      'toggle-option',
                      { active: docUploadTime === 'on_start' },
                    ]"
                    @click="toggleButton('on_start')"
                  >
                    Start
                  </button>
                  <button
                    :class="[
                      'toggle-option',
                      { active: docUploadTime === 'on_end' },
                    ]"
                    @click="toggleButton('on_end')"
                  >
                    End
                  </button>
                </div>
                </div>
                <!-- doc upload start or end consent   -- end -->
                <div class="v-center gap-1 grow-1">
                <span>Mandatory : </span>
                <input type="checkbox" v-model="editData.uploadMandatory"/>
              </div>
                  <img
                  src="~@/assets/images/delete-icon.svg"
                  width="18"
                  alt="delete icon"
                  class="pointer"
                  @click="removeAction(2)"
                />
              </div>

            </div>
          </div>
        </div>
      </div>
      <!--  edit node end  -->
      <!-- edit phase start -->
      <div class="input-group mt-1" v-else-if="shape === 'phase-rect'">
        <label class="key">Enter the phase name:</label>
        <input
          type="text"
          v-model="editData.newLabel"
          placeholder="Enter new name"
          maxlength="30"
        />
      </div>
      <!-- edit phase end -->
      <!-- edit node start -->
      <div class="input-group" v-else-if="shape === 'edge'">
        <label class="key">Enter the edge name:</label>
        <input
          type="text"
          v-model="editData.newLabel"
          placeholder="Enter new name"
          maxlength="30"
        />

        <!-- transition doc selector  start -->
        <!-- <div class="my-2 input-group" v-if="shape === 'edge'">
          <hr class="my-2" />
          <h4>Trigger Actions</h4>
          <div class="grid-3 gap-1 mt-2">
            <custom-dropdown
              :list="[{ id: 1, name: 'Document' }]"
              :selected="selectedActionType"
              @select="handleActionType"
              @deselect="selectedActionType = {}"
              class="form-template-container-wft"
            />
            <custom-dropdown
              :list="[]"
              :selected="selectedActionType"
              @select="handleActionType"
              @deselect="selectedActionType = { id: -1, name: 'Select' }"
              class="form-template-container-wft"
            />
            <custom-dropdown
              :list="[]"
              :selected="selectedActionType"
              @select="handleActionType"
              @deselect="selectedActionType = { id: -1, name: 'Select' }"
              class="form-template-container-wft"
            />
          </div>
        </div> -->
        <!-- transition doc selector  end -->
      </div>
    </div>
    <div class="gap-1 flex-end">
      <button class="btn btn-black" @click="$emit('cancel')">Cancel</button>
      <button class="btn" @click="$emit('save',editData)">Save</button>
    </div>
  </div>
</template>
<script>
import customDropdown from '@/components/common/customDropdown.vue'
export default {
  name: 'editWfModalComponent',
  components: { customDropdown },
  props: {
    shape: {
      type: String,
      default: 'node-rect'
    },
    data: {
      type: Object,
      default: () => ({})
    },
    userGroups: {
      type: Array,
      default: () => []
    },
    allFormsTemplate: {
      type: Array,
      default: () => []
    },
    showSelectFormTemplate: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    editData: { userGroup: null },
    triggerTypes: [],
    selectedActionType: {},
    formSearchKeyword: '',
    docUploadTime: null, // default value for document upload time,
    window: window
  }),
  computed: {
    formTempaltesFiltered () {
      if (this.formSearchKeyword === '') {
        return this.allFormsTemplate
      }
      const forms = this.allFormsTemplate.filter((item) =>
        item.name.toLowerCase().includes(this.formSearchKeyword.toLowerCase())
      )
      return forms
    },
    showremoveUserGroup () {
      return this.editData.userGroup && this.editData.userGroup !== null && this.editData.userGroup !== undefined
    }
  },
  mounted () {
    this.editData = this.data
    // this is use full when tried to edit the node
    if (this.editData.docUploadTime) {
      this.selectedActionType['2'] = { id: 2, name: 'File upload' }
      this.docUploadTime = this.editData.docUploadTime
    }
    if (this.editData.formTemplate?.id) {
      this.selectedActionType = { ...this.selectedActionType, 1: { id: 1, name: 'Form Create' } }
    }
    if (this.showSelectFormTemplate) {
      this.triggerTypes = [
        { id: -1, name: 'Select' },
        { id: 1, name: 'Form Create' },
        { id: 2, name: 'File upload' }
      ]
    } else {
      this.triggerTypes = [
        { id: -1, name: 'Select' },
        { id: 2, name: 'File upload' }
      ]
    }
    // this is used to remove the text selection on the modal
    if (this.window.getSelection) {
      this.window.getSelection().removeAllRanges()
    } else if (this.window.document.selection) {
      this.window.document.selection.empty() // For IE < 9
    }
  },
  methods: {
    handleActionType (type) {
      // this.selectedActionType = { ...this.selectedActionType, [type.id]: type }
      this.selectedActionType = { [type.id]: type }
      if (type.id === 2) {
        // file upload
        this.editData.docUploadTime = this.docUploadTime = 'on_start'
        this.editData.formTemplate = {}
      } else if (type.id === 1) {
        this.editData.docUploadTime = null
        this.editData.uploadMandatory = null
      } else if (type.id === -1) {
        this.selectedActionType = {}
        this.docUploadTime = null
        this.editData.formTemplate = {}
        this.editData.uploadMandatory = null
      }
    },
    hadleTemplateSelect (template) {
      this.editData = { ...this.editData, formTemplate: template }
    },
    toggleButton (btn) {
      this.docUploadTime = btn
      if (btn === 'on_start') {
        this.editData.docUploadTime = 'on_start'
      } else {
        this.editData.docUploadTime = 'on_end'
      }
    },
    removeAction (typeId) {
      if (typeId === 1) {
        this.editData.formTemplate = {}
        this.selectedActionType[1] = null
      } else if (typeId === 2) {
        this.docUploadTime = null
        this.editData.docUploadTime = null
        this.editData.uploadMandatory = null
        this.selectedActionType[2] = null
      }
    },
    userGroupchange (data) {
      this.editData = { ...this.editData }
    },
    removeUserGroup () {
      this.editData = {
        ...this.editData,
        userGroup: null
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.toggle-btn {
  height: 1.3em;
  display: inline-flex;
  border: 1px solid #ccc;
  border-radius: 6px;
  background-color: #e0e0e0;
  //padding: 1px; //not necessary
  align-items: center;
  overflow: hidden;
}

.toggle-option {
  flex: 1;
  padding: 8px 16px;
  border: none;
  background-color: #e0e0e0;
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  // transition: all 0.1s ease-in-out;
}

.toggle-option.active {
  background-color: white;
  color: black;
  font-weight: 600;
  border: 1px solid #bbb;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2), 0 2px 4px rgba(0, 0, 0, 0.15),
    inset 0 0 6px rgba(0, 0, 0, 0.2);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.15s ease-in-out;
  border-radius: 6px;
  // background-color: var(--brand-light-color);
  // color: black;
  // font-weight: 600;
  // border: 1px solid var(--brand-dark-color);
  // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
</style>
