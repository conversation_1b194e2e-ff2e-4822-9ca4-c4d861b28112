import { GQL } from '../graphQl'

export const CreateProductCode = () => GQL`mutation CreateProductCode ($product_code: String!){
  insert_product_code_one(
    object: {
      product_code: $product_code
    }
  )
  {
    id
  }
}`

export const getAllProductCodeQuery = () => GQL`query getAllProductCodeQuery($conditions: product_code_bool_exp) {
  product_code(where: $conditions, order_by: {product_code: asc}) {
    id
    product_code
    linked_material {
      status
    }
  }
}
`
export const productCodeStatusQuery = () => GQL`query productCodeStatusQuery ($id: uuid) {
  core_material_master (where: {product_code:{_eq: $id}}) {
    status
  }
}`

export const getProductCodeQuery = () => GQL`query getProductCodeQuery($id: uuid!) {
  product_code_by_pk(id: $id) {
    id
    product_code
  }
}
`
export const getAllChildProductCodeByBomVersionQuery = () => GQL`query getAllChildProductCodeByBomVersionQuery($bomVersionId: uuid!) {
  bom_items(
    where: {bom_version_id: {_eq: $bomVersionId}, core_material: {material_product_code: {product_code: {_is_null: false}}}}
  ) {
    associated_bom_version {
      core_bom {
        name
        product_code
        id
      }
      id
      version_no
    }
    core_material {
      material_product_code {
        id
        product_code
      }
    }
  }
}`

export const getAllImpactedProductCodeQuery = () => GQL`query getAllImpactedProductCodeQuery($bomId: uuid!) {
  bom_versions(
    where: {active: {_eq: true}, core_bom: {project_id: {_is_null: true}}, bom_items: {associated_bom_version: {core_bom: {id: {_eq: $bomId}}}}}
  ) {
    core_bom {
      product_bom {
        product_code
      }
    }
  }
}`

export const updateProductCode = () => GQL`mutation updateProductCode($id:uuid!,$product_code:String!){
  update_product_code(
    where: {id: {_eq: $id}}
    _set: {product_code: $product_code}
  ){
    returning{
      id
    }
  }
}
`
