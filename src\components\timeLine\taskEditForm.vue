<template>
    <div class="gantt-edit-form">
      <form>
        <div class="grid-2 mt-2">
          <div class="input-group mt-2">
            <label class="key">Name:</label>
            <input autofocus type="text" v-model="taskData.name"  disabled="true"/>
          </div>
          <div class="input-group mt-2">
            <label class="key">Description:</label>
            <input type="text" v-model="taskData.description" disabled="true"/>
          </div>
        </div>
        <div class="grid-2 mt-2">
          <div class="input-group mt-2">
            <label class="key">Duration (hours):</label>
            <input type="number" v-model="taskData.duration" min='0' :disabled="task?.hasChild" @input="calculateEndDate(taskData.duration, taskData.start_date)" @keydown="changeNumber"/>
          </div>
          <div class="input-group mt-2">
            <label class="key">Progress:</label>
            <input type="number" v-model="taskData.progress" disabled="true" @input="validateProgress"/>
          </div>
        </div>
        <div class="grid-2 mt-2">
          <div class="input-group mt-2">
            <label class="key">Start Date:</label>
            <input type="date" v-model="taskData.start_date" :disabled="task?.hasChild" @change=" calculateEndDate(taskData.duration, taskData.start_date)" />
          </div>
          <div class="input-group mt-2">
            <label class="key">End Date:</label>
            <input type="date" v-model="taskData.end_date" disabled/>
          </div>
        </div>
        <div class="grid-2 mt-2">
          <div class="input-group mt-2">
            <label class="key">Type</label>
            <select v-model="taskData.type" disabled>
              <option value="1">Task</option>
              <option value="2" v-if="taskData.type === 2">Project</option>
              <option value="3">Milestone</option>
            </select>
          </div>
          <div class="input-group mt-2">
            <label class="key">Cost:</label>
            <input type="number" v-model="taskData.cost" disabled="true"/>
          </div>
        </div>
       <div class="grid-2 mt-2">
         <div class="input-group mt-2">
             <label class="key">AttachBom:</label>
             <div class="selected-bom" v-if="taskData.attached_bom">
              <div>
                 {{ taskData.attached_bom.name }}
              </div>
             </div>
             <select v-model="taskData.attached_bom" v-else disabled="true">
             <option v-for="bom in bomList" :key="bom.id" :value="bom">{{ bom.name }}</option>
             </select>
           </div>
           <div class="input-group mt-2" v-if="taskData.attached_bom">
          <label class="key">Quantity for attached BOM:</label>
          <input type="number" v-model="taskData.attached_bom_quantity" min="1" disabled="true" @change="validateBomQuantity"/>
        </div>
          </div>
          <div class="input-group mt-2"><label class="key">Assignee</label></div>
        <div class="add-user__body" ref="addUserBody">
          <div class="input-group mt-2 add-user__search">
            <div>
              <div class="add-user__search_box">
                <input
                placeholder="Search for users"
                type="text"
                v-model="searchKeyword"
                @click="openPopup = true"
              />
              <div  v-if="searchKeyword" class="add-user__search_close v-center h-center">
              <img   c src="~@/assets/images/icons/close-icon.svg" width="20px" @click="searchKeyword=''" alt="">
            </div>
              </div>

              <div class="add-user__selected mt-4 ">
                <div
                  class="assignees-box"
                  v-for="(user, index) in selectedAssignees"
                  :key="user.id"
                >
                  <div class="add-user__list-item__avatar">
                    <avatar :user="user" size="24px" />
                  </div>
                  <div class="add-user__list-item__name">
                    {{ user.first_name }}
                    {{ user.last_name }}
                  </div>
                  <img
                    class="pointer ml-1"
                    @click="removeAssignee(index,user)"
                    src="~@/assets/images/icons/close-icon.svg"
                    width="16px"
                    alt=""
                  />
                </div>
              </div>
            </div>
            <div class="add-user__list" v-if="openPopup" >
              <div class="add-user__list-no-result" v-if="!nonAddedUsers.length">
                No users found
              </div>
              <div
                class="add-user__list-item"
                v-for="user in nonAddedUsers"
                :key="user.associated_user.id"
                tabindex="0"
                @click="updateSelectedUsers(user.associated_user)"
              >
                <div class="add-user__list-item__avatar">
                  <avatar :user="user.associated_user" size="24px" />
                </div>
                <div class="add-user__list-item__name">
                  {{ user.associated_user.first_name }}
                  {{ user.associated_user.last_name }}
                </div>
                <div class="add-user__list-item__action"></div>
              </div>
            </div>
          </div>
        <div class="grid-1 mt-2">
          <div>
        <label class="key"  >Tags</label>
        <tagInput
        title="Add tags to task"
         :type="2"
         :timeLineEdit="true"
         :lastParentId="tagGroupData?.tagArray[selected_tag_line]?.at(-1)?.id"
         :tagGroupData="tagGroupData"
          @addNewTag="addnewTag"
          @parentLevelSelected="changeSelectedTagLine"/>
        <div class="tag-container">
          <div class="tags" v-for="tag in selectedTags" :key="tag.id">
            <div class="selected-tags">
              {{ tag.name }}
            </div>
          </div>
          <div class="tag-container"   >
            <label class="key" v-if="tagGroupData?.tagArray.length> 0" >Attached Tags:</label>
            <div v-else>
              <b class="no-tag-found">No tag is Attached!</b>
            </div>
            <div :class="{'tags mt-2 p-1':true, 'tags-line-selected p-1': selected_tag_line===index }"  v-for="(tagParentwise,index) in tagGroupData.tagArray" :key="index" @click="selected_tag_line=index">
              <div v-for="(tag, cindex) in tagParentwise" :key="tag.id "  class="flex v-center tags-line ">
                  <img width="8px" src="~@/assets/images/right-arrow-icon.svg" alt="" :class="{ 'tags-line-first-img': cindex === 0 }" />
                <div :class="{
                  'attached-tags v-center h-center ': true
              }"

                >
                {{ tag.name }}
                    <img
                :class="{
                  'pointer ml-1 close-icon': true,
                  }"
                @click="removeAttachedTags(tag.id)"
                @mouseover="sethoveredtag(cindex,index)"
                @mouseleave="sethoveredtag(null,null)"
                src="~@/assets/images/icons/close-icon.svg" width="16px"
              />
              <div :class="{'attachedTags_overLay': cindex>=hoverd_tag?.cindex && index===hoverd_tag?.index}">
              </div>
              </div>

              </div>
            </div>
          </div>
          <div class="grid-1 mt-2">
        <label class="key" >
        Attach Documents</label>
        <div class="document-attach">
          <div v-if="attachedDocs.length > 0" class="weight-500 document-attach-view input-group imp "><span v-for="file in attachedDocs" :key="file.id">
            <span v-show="file.flag!=='deleted'" class="document-attach-list" v-overflow-tooltip>{{ file.core_document?.doc_name || file.doc_name }}</span></span></div>
          <span v-tooltip="' Link Documents '" class="document-attach-edit"  @click="documentSelectDialog = false"> <img src="~@/assets/images/icons/file-icon.svg" width="10px" height="10px" alt="">
          </span>
        </div>
    </div>
        </div>
      </div>
        </div>
        </div>
      </form>
      <div class="flex space-between pt-3 m">
          <button
            class="btn btn-black mr-3"
            @click="$emit('close')"
            type="button"
          >
            CANCEL
          </button>
          <button
            class="btn"
            type="button"
            @click="saveAndClose()"
          >
            SAVE
          </button>
        </div>
        <modal
      :open="documentSelectDialog"
      @close="documentSelectDialog = false"
      :closeOnOutsideClick="true"
      title=""
      >
      <document-selector @close="documentSelectDialog = false" @files-selected="handleFileSelection" v-if="documentSelectDialog"  :linkedDocs="attachedDocs" :uploadDocButton="uploadDocButton" :token="getProjectTokens[projectId]" :projectId="projectId" />
    </modal>
    </div>
  </template>
<script>
import { mapGetters, mapMutations } from 'vuex'
import avatar from '../common/avatar.vue'
import { getGanttTypeFromString } from '@/helper/gantt/getGanttType'
import { GetTaskStatuses } from '../../api/apis/projectPlanner'
import {
  GetAllTaskDocsByToken,
  GetAllProjectBomListByToken,
  getProjectExchangeToken,
  getCalenderDataByids,
  GetAllTaskBomByToken
} from '@/api'
import { TagTrie } from '@/utils/tagsHelper'
import Modal from '../common/modal.vue'
import DocumentSelector from '../document/documentSelectorDialog.vue'
import tagInput from '@/components/common/tagInput-edit-material.vue'
import { alert } from '@/plugins/notification'
import { restrictKeys } from '@/utils/validations'
export default {
  components: { avatar, DocumentSelector, Modal, tagInput },
  props: {
    task: {
      type: Object,
      default: () => ({})
    },
    view: {
      type: String,
      default: 'chart'
    },
    assigneeMap: { type: Object, required: true, default: () => { } },
    open: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      taskData: {
        name: '',
        description: '',
        duration: null,
        start_date: '',
        end_date: '',
        progress: '0',
        type: '',
        timeSpent: '',
        status: null,
        task_assignees: [],
        attached_bom: null,
        tags: [],
        cost: 0,
        attached_bom_quantity: ''
      },
      bomList: [],
      selectedAssignees: [],
      searchKeyword: '',
      openPopup: false,
      tasks: [],
      attachedTags: [],
      selectedTags: [],
      tagGroupData: {
        firstLevelParents: [],
        tagArray: []
      },
      hoverd_tag: {
        cindex: null,
        index: null
      },
      selected_tag_line: 0,
      tagTrie: new TagTrie(),
      lastParentId: null,
      documentSelectDialog: false,
      attachedDocs: [],
      uploadDocButton: true,
      projectId: this?.task?.core_project?.id,
      enableProgress: false,
      window: window

    }
  },
  computed: {
    ...mapGetters(['tenantUsersList']),
    ...mapGetters('timeLine', ['getProjectTokens']),
    ...mapGetters(['user']),
    ...mapGetters('timeLine', ['getCalederHashMap']),
    nonAddedUsers () {
      return this.tenantUsersList
        .filter((user) => {
          return (
            this.assigneeMap[this.projectId].includes(user.associated_user.id) &&
              !this.selectedAssignees.find(
                (item) => item?.id === user.associated_user.id
              )
          )
        })
        .filter((user) => {
          return (
            user.associated_user.first_name
              .toLowerCase()
              .includes(this.searchKeyword.toLowerCase()) ||
              user.associated_user.last_name
                .toLowerCase()
                .includes(this.searchKeyword.toLowerCase())
          )
        })
    },
    getAppliedTags () {
      return this.attachedTags.filter((item) => item.status !== 'deleted')
    }
  },
  mounted () {
    this.$el.addEventListener('click', this.handleOutsideClick)
    const task = JSON.parse(JSON.stringify(this.$props.task))
    this.disableProgress(task.task_assignees)
    this.getBasicSetup(task)
    this.taskData.name = task.text
    this.taskData.description = task.description
    this.taskData.duration = task.duration
    this.taskData.status = task.status
    this.taskData.id = task.id
    this.taskData.assigneeIds = task.assigneeIds
    this.taskData.cost = task?.cost ?? 0
    this.taskData.is_critical = task?.is_critical
    this.taskData.attached_bom = task?.attached_bom ?? null
    this.taskData.progress = task.progress
    this.taskData.start_date = new Date(task.start_date ?? task.planned_start_date)
      .toISOString()
      .substr(0, 10)
    this.taskData.end_date = new Date(task.end_date ?? task.planned_end_date)
      .toISOString()
      .substr(0, 10)
    this.taskData.timeSpent = task?.timeSpent ?? 0
    // while mounting the component setting assigned users into selectedAssignees and submission data (taskData)
    if (!Array.isArray(task.task_assignees)) {
      task.task_assignees = []
    }
    for (const element of task.task_assignees) {
      if (element?.status !== 'deleted') {
        this.selectedAssignees.push({
          id: element?.user_id ?? element?.id,
          first_name: element?.assignee?.first_name ?? element?.first_name,
          last_name: element?.assignee?.last_name ?? element?.last_name,
          status: element?.status ?? null
        })
      }
    }
    this.taskData.task_assignees = task?.task_assignees?.map((element) => {
      return {
        id: element?.user_id ?? element?.id,
        first_name: element?.assignee?.first_name ?? element?.first_name,
        last_name: element?.assignee?.last_name ?? element?.last_name,
        status: element?.status ?? null
      }
    })

    GetAllTaskBomByToken(task.id, this.getProjectTokens[this.projectId]).then((res) => {
      this.taskData.attached_bom_quantity = res.task_material_association[0]?.metadata.quantity
      this.taskData.attached_bom = res.task_material_association[0]?.target_bom
    })
    this.taskData.type = getGanttTypeFromString(task.type)
    this.getAttachedTags()
    this.taskStatusList()
  },
  beforeDestroy () {
    this.$el.removeEventListener('click', this.handleOutsideClick)
    document.body.removeEventListener('keydown', this.keyPress)
  },
  methods: {
    ...mapMutations('timeLine', ['addNewToken', 'addCalenderHashMap']),
    deselectBom () {
      this.taskData.attached_bom = null
    },
    changeNumber (event) {
      if (['e', 'E', '+', '-'].includes(event.key)) {
        restrictKeys(event)
      }
    },
    disableProgress (taskAssignee) {
      const newSelectedAssignee = taskAssignee.map((item) => {
        return item.id
      })
      if (newSelectedAssignee.includes(this.user.userId)) {
        this.enableProgress = true
      }
      if (this.user.tenantLevelRole === 'ADMIN') this.enableProgress = true
    },
    closeAssigneePopup () {
      this.openPopup = false
    },
    async getCalendarsList () {
      if (!this.getProjectTokens[this.projectId]) {
        const response = await getProjectExchangeToken(this.projectId)
        this.addNewToken({ token: response.message, projectId: this.projectId })
      }
      return getCalenderDataByids([this.projectId]).then((res) => {
        for (const project of res.core_project_calendar) {
          const days = []
          const holidays = []
          for (const workDays of project.calendar_working_days) {
            days.push(workDays.work_day)
          }
          for (const holiday of project.calendar_holidays) {
            holidays.push(new Date(holiday.date))
          }
          this.addCalenderHashMap({
            calenderData: {
              project_id: project.project_id,
              working_hours: project.working_hours,
              workDays: days,
              holidays: holidays
            },
            projectId: project.project_id
          })
        }
      }).catch(err => console.log(err.message)).finally(() => {
        this.loading = false
      })
    },
    handleTagSelection (selectedTag) {
      const newTags = selectedTag.filter(tag => !this.selectedTags.some(selected => selected.id === tag.id))
      this.selectedTags.push(...newTags)
    },
    handleOutsideClick (e) {
      if (!this?.$refs?.addUserBody?.contains(e.target)) {
        this.openPopup = false
      }
    },
    updateSelectedUsers (user) {
      let flag = false
      this.taskData.task_assignees.forEach((element, index) => {
        if (element?.id === user.id) {
          this.taskData.task_assignees[index].status = this.taskData.assigneeIds.includes(user.id) ? null : 'new'
          flag = true
        }
      })
      if (!flag) this.taskData.task_assignees.push({ id: user?.id, first_name: user?.first_name, last_name: user?.last_name, status: 'new' })
      this.selectedAssignees.push(user)
      this.openPopup = false
      this.searchKeyword = ''
    },
    removeAssignee (index, user) {
      this.taskData.task_assignees.forEach((element, index) => {
        if (element?.id === user?.id) {
          this.taskData.task_assignees[index].status = 'deleted'
        }
      })
      this.selectedAssignees.splice(index, 1)
    },
    taskStatusList () {
      GetTaskStatuses().then((res) => {
        this.tasks = res.custom_list_values.map((item) => {
          return {
            label: item.name,
            id: item.id
          }
        })
        if (this.taskData.status === null || this.taskData.status === undefined) {
          this.updateStatusBasedOnProgress(this.taskData.progress)
        }
      })
    },
    validateProgress () {
      if (this.taskData.progress > 100) {
        this.taskData.progress = 100
      } else if (this.taskData.progress < 0) {
        this.taskData.progress = 0
      }
    },
    updateStatusBasedOnProgress (progress) {
      const todoStatus = this.tasks.find((task) => task.label === 'TO DO')
      const doneStatus = this.tasks.find((task) => task.label === 'DONE')
      const inProgressStatus = this.tasks.find((task) => task.label === 'IN PROGRESS')
      if (progress <= 0 && todoStatus) {
        this.taskData.status = todoStatus.id
      } else if (progress >= 100 && doneStatus) {
        this.taskData.status = doneStatus.id
      } else if (inProgressStatus) {
        this.taskData.status = inProgressStatus.id
      }
    },
    saveAndClose () {
      this.taskData.tags = this.tagGroupData.tagArray
      let previouslyAttachedBom = null
      if (this.$props.task?.attached_bom) {
        previouslyAttachedBom = JSON.parse(JSON.stringify(this.$props.task.attached_bom))
      }
      const currentlyAttachedBom = JSON.parse(JSON.stringify(this.taskData?.attached_bom ?? {}))
      if (previouslyAttachedBom && currentlyAttachedBom) {
        if (previouslyAttachedBom.id !== currentlyAttachedBom.id) {
          previouslyAttachedBom.tag = 'deleted'
          currentlyAttachedBom.tag = 'new'
          this.taskData.attachedBom = [previouslyAttachedBom, currentlyAttachedBom]
        } else {
          this.taskData.attachedBom = null
        }
      } else if (!previouslyAttachedBom && currentlyAttachedBom) {
        currentlyAttachedBom.tag = 'new'
        this.taskData.attachedBom = [currentlyAttachedBom]
      } else if (previouslyAttachedBom && !currentlyAttachedBom) {
        previouslyAttachedBom.tag = 'deleted'
        this.taskData.attachedBom = [previouslyAttachedBom]
      }
      this.taskData.task_docs = this.attachedDocs.map((doc) => {
        return {
          ...doc.core_document, flag: doc.flag
        }
      })
      this.$emit('update-and-close', this.taskData)
    },
    calculateEndDate (duration, startDate) {
      const endDate = new Date(startDate)
      // here we have stored all the caleder data for the each project in scheduler to global state
      const projectCalender = this.getCalederHashMap[this.projectId]
      // if  the  duration is less than one day working hour then it will  set the end date as start date itself
      if (duration <= projectCalender.working_hours) {
        // checking whether the given  start date is belongs to any holiday or weekend
        while (duration > projectCalender.working_hours) {
          const dayIndex = endDate.getDay()
          if (projectCalender.workDays.includes(dayIndex) && !projectCalender.holidays.includes(endDate)) {
            duration -= projectCalender.working_hours
            return (this.taskData.end_date = new Date(endDate).toISOString().substr(0, 10))
          }
          endDate.setDate(endDate.getDate() + 1)
        }
        return (this.taskData.end_date = new Date(endDate).toISOString().substr(0, 10))
      } else {
        while (duration > projectCalender.working_hours) {
          const dayIndex = endDate.getDay()
          if (projectCalender.workDays.includes(dayIndex) && !projectCalender.holidays.includes(endDate)) {
            duration -= projectCalender.working_hours
          }
          endDate.setDate(endDate.getDate() + 1)
        }
        const newEndDate = new Date(endDate).toISOString().substr(0, 10)
        this.taskData.end_date = newEndDate
        return newEndDate
      }
    },
    getAttachedTags () {
      this.attachedTags = this.$props.task.tag_tasks.map(item => {
        return {
          // after updating the all tags will come as  tag object else it comes inside an object like  {tag:{}}
          id: item.tag?.id ?? item.id,
          name: item.tag?.name ?? item.name,
          parentId: item.tag?.parent_id ?? item.parentId
        }
      })
      this.tagTrie._generateTreeFromUnorderedList(this.attachedTags)
      this.tagGroupData = this.tagTrie.groupTagInArray()
    },
    removeAttachedTags (tagId) {
      this.tagTrie.deleteTagById2(tagId)
      this.tagGroupData = this.tagTrie.groupTagInArray()
    },
    sethoveredtag (cindex, index) {
      this.hoverd_tag.cindex = cindex
      this.hoverd_tag.index = index
    },
    addnewTag (tag) {
      this.tagTrie.insertTag({ name: tag.name, id: tag.id, parentId: tag.parent_id })
      this.tagGroupData = this.tagTrie.groupTagInArray()
    },
    changeSelectedTagLine (id) {
      if (id === null) {
        this.selected_tag_line = id
      } else {
        this.tagGroupData.tagArray.forEach((element, index) => {
          if (element[0].id === id) { this.selected_tag_line = index }
        })
      }
    },
    handleFileSelection (selectedFileData) {
      // the selectedfiles data is comming from modal (contains  all the datat related to selected docs)
      this.documentSelectDialog = false
      this.attachedDocs = selectedFileData
    },
    async getBasicSetup (task) {
      if (!this.getProjectTokens[this.projectId]) {
        const response = await getProjectExchangeToken(this.projectId)
        this.addNewToken({ token: response.message, projectId: this.projectId })
      }
      GetAllProjectBomListByToken(this.getProjectTokens[this.projectId], this.projectId)
        .then((res) => {
          this.bomList = res.core_bom.filter(bom => bom.state === 2 && bom.checked_out_by === null && bom.id !== this.taskAssociatedBom)
        })
        .catch((err) => {
          alert(`${err.message}`)
        })
      if (!task.task_docs) {
        GetAllTaskDocsByToken(task.id, this.getProjectTokens[this.projectId]).then((res) => {
          if (res.task_document_association) {
            this.attachedDocs = res.task_document_association.map((element) => {
              return element
            })
          }
        })
      } else {
        this.attachedDocs = task.task_docs
      }
      this.getCalendarsList()
    },
    validateBomQuantity () {
      if (parseInt(this.taskData.attached_bom_quantity) <= 0) {
        alert('Quantity should be greater than or equal to 1')
        this.taskData.attached_bom_quantity = 1
      }
    },
    keyPress (e) {
      const activeElement = this.window.document.activeElement.tagName.toLowerCase()
      const activeElementCheck = ['input', 'select', 'textarea'].includes(activeElement)
      if (e instanceof KeyboardEvent && e.code === 'Enter' && activeElement !== 'button') {
        this.window.document.activeElement.blur()
      }
      if (!this.open || activeElementCheck) return
      if (e instanceof KeyboardEvent && e.code === 'Enter') {
        this.saveAndClose()
      }
    }

  },
  watch: {
    'taskData.progress': function (newProgress) {
      this.updateStatusBasedOnProgress(newProgress)
    },
    'task.tag_tasks': function () {
      this.getAttachedTags()
    }
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
  }
}

</script>
  <style lang="scss" scoped>
 form {
  width: 40vw;
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 8px;
}
  .gantt-edit-form{
    position: relative;
  }

  .selected-bom {
      border: 1px solid rgba(59, 59, 59, 0.4666666667);
      width: 100%;
      border-radius: 0.285em;
      font-size: 1em;
      padding: 0.85em;
      display: flex;
      justify-content: space-between;
  }

  .add-user {
    &__body {
      position: relative;
    }
    &__search {
      margin-bottom: 1rem;
      &_box{
      position:relative;
      width: 30%;
    }
      input {
        padding: 0.5rem;
        border-radius: 0.25rem;
        width: 100%;
        margin-right: 10px;
      }
      &_close{
      position: absolute;
      width:30px;
      background-color: var(--bg-color);
      height:90%;
      border-radius: 0.25rem;
    /* left: 0; */
    right: 3px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    }
    }
    &__selected {
  display: grid;
  grid-template-columns: repeat(3,1fr);
  gap:4px
    }
    &__list {
      width: 30%;
      max-height: 20rem;
      overflow-y: auto;
      position: absolute;
      background: white;
      left: 0px;
      right: 0px;
      z-index: 1;
      top: 40px;
      padding: 10px;
      box-shadow: 2px 2px 4px rgb(0 0 0 / 20%);
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
      &-no-result {
        padding: 1rem;
        text-align: center;
      }
      &-item {
        display: flex;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e8e8e8;
        cursor: pointer;
        &:hover {
          background: #f8f8f8;
        }
        &__avatar {
          margin-right: 1rem;
        }
        &__name {
          flex: 1;
          white-space: nowrap;
        }
        &__action {
          &__btn {
            padding: 0.25rem;
            border: 1px solid #e8e8e8;
            border-radius: 0.25rem;
            background: #fff;
            cursor: pointer;
            i {
              font-size: 1.25rem;
            }
          }
        }
      }
    }
  }
  .tags {
    display: flex;
  flex-wrap: wrap;
  margin-left: 5px;
    margin-right: 5px;
    row-gap: 5px;
     &-line-selected{
      background-color: var(--brand-light-color);
     border: .4px solid var(--brand-color);
     border-radius: 5px;
     min-width: min-content;
     }
  }
  .tag-container {
    margin-top: 4px;
  }

  .no-tag-found{
    margin-left: 5px;
  }
  .attached-tags{
    cursor:pointer;
    background-color: var(--brand-color);
    padding: 0.6rem;
    font-size: small;
    border-radius: 0.4rem;
  position:relative;
    &-fade{
  background-color: rgb(0, 0,0,.6) ;
  color:white
  }
    & img{
   z-index:4;
    }
    & img{
      display: none;
    }
  }
  .tags-line{
    text-overflow: ellipsis;
    overflow: hidden;
     white-space: nowrap;
    & img{
      margin-inline: .5rem;
    }
    &-first-img{
      display: none;
    }
    .attached-tags:hover > img {
  display: block;
  scale: 1.2;
    }
    .attachedTags_overLay{
      background-color: black;
      position: absolute;
      width: 100%;
      height: 100%;
      border-radius: 5px;
      opacity: .3;
      z-index: 1;
    }
  }
  .assignees-box{
    display: flex;
    align-items: center;
    background-color: var(--brand-color);
    padding: 0.6rem;
    font-size: small;
    border-radius: 0.4rem;
  }
  .button-box{
    padding: 10px 5px 5px 0;
    position: sticky;
    width: 100%;
    background-color:var(--bg-color);
    right:10px;
    bottom:0;
    border-top: .1px solid rgb(0, 0,0,.1);
    margin-top: 10px ;
  }
  .document-attach{
      display: flex;
      align-items: center;
      justify-content: start;
      flex-wrap: wrap;
      width: 100%;
      // max-height: 100px;
      // min-height: 50px;
      // border-radius: 4px;
      // border: 2px solid rgba(var(--brand-rgb),0.4);
      height: auto;
      border-radius: 4px;
      padding: 0.6rem;
      cursor: pointer;
      &-view{
        display: flex;
        flex-wrap: wrap;
        gap: 14px;

      }
      &-edit{
        border: 2px solid rgba(41, 39, 39, 0.2);
        border-radius: 3px;
        padding: 4px 6px;
        width: 25px;
        display: flex;
        justify-content: center;
        margin-left:5px;
      }
      &-list{
        border: 2px solid rgba(var(--brand-rgb));
        border-radius: 3px;
        background-color: var(--brand-color);
        padding: 5px 5px 5px 5px ;
      width: 100px;
      text-overflow: ellipsis;
      overflow-y: auto;
      white-space: nowrap;
      cursor:context-menu;
      }
      span{
        cursor: pointer;
        user-select: none;
        background-color: transparent;
      }
    }
  </style>
