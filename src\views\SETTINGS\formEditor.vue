<template>
    <div class="form-builder">
      <div class="form-builder-nav">
        <h2 class="weight-500 xxxl v-center">
          <router-link to="/settings/copy-forms">
            <img src="~@/assets/images/icons/arrow-back.svg" class="mt-1" width="30px" alt="">
          </router-link>
          Edit Your Form Template Here</h2>
      </div>
      <loading v-if="loading"/>
      <template v-else>
      <div class="form-builder-elements">
        <form-elements />
      </div>
      <div class="form-builder-playground">
        <form-playground :is-edit="true"/>
      </div>
      <div class="form-builder-config">
        <form-config />
      </div>
      </template>
     </div>
  </template>

<script>
import FormConfig from '../../components/form/formConfig.vue'
import formElements from '../../components/form/formElements.vue'
import FormPlayground from '../../components/form/formPlayground.vue'
import loading from '../../components/common/loading.vue'

export default {
  components: { formElements, FormPlayground, FormConfig, loading },
  name: 'FormBuilder',
  data () {
    return {
      loading: false
    }
  },
  methods: {
    async initFormEdit () {
      const formId = this.$route.params.formId
      await Promise.all([
        this.$store.dispatch('form/getFormTypeList'),
        this.$store.dispatch('form/getFormFields'),
        this.$store.dispatch('form/resetFormTemplate'),
        this.$store.dispatch('form/getFormTemplateDetailsForUpdation', formId)
      ])
      this.loading = false
    }
  },
  computed: {},
  created () {
    this.loading = true
    this.initFormEdit()
  }
}
</script>

  <style lang="scss" scoped >
  .form-builder {
    height: 100%;
    display: grid;
    grid-template-columns: 210px 1fr 300px;
    grid-template-rows: auto 1fr;
    grid-template-areas:
      "nav nav nav"
      "elements playground config";
    grid-gap: 10px;
    .form-builder-nav {
      grid-area: nav;
      background-color: var(--bg-color);
      margin: -12px;
      padding: 16px;
      margin-bottom: 0;
    }
    .form-builder-elements {
      grid-area: elements;
      background-color: rgba(var(--brand-rgb), 0.2);
      height: 100%;
      overflow-y: auto;
    }
    .form-builder-playground {
      grid-area: playground;
      background-color: var(--bg-color);
      height: 100%;
      overflow-y: auto;
    }
    .form-builder-config {
      grid-area: config;
      background-color: rgba(var(--brand-rgb), 0.2);
      height: 100%;
      overflow-y: auto;
    }
  }
  </style>
