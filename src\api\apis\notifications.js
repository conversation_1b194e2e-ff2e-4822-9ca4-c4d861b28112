import config from '@/config'
import { runMutation, runSubscription } from '../graphQl'
import http from '../http'
import { changeReadStatus, liveNotification } from '../query/notifications'

export const LiveNotifications = async (callback, pages, limits, user) => {
  const tenantId = user?.tenantId
  const userId = user?.userId
  const offsetPage = pages * limits
  return runSubscription(liveNotification(), { offsetPage, limits, tenantId, userId }, callback, 'tenant')
}

export const ChangeReadStatus = (id) => {
  return runMutation(changeReadStatus(), { id }, 'tenant')
}

export const SendNotificationsForReadingAndDownloadingDocs = (documentId, type) => {
  return http.POST(config.serverEndpoint + '/documents/notify-view-and-download', { documentId, type }, 'tenant')
}
