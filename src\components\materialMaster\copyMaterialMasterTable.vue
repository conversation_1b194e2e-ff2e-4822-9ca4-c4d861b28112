<template>
    <div  class="material-master-table--container" >
        <div class="copy-dtx-table material-master-table--table">
        <table >
          <thead v-if="view === 'material'">
            <tr>
              <th v-overflow-tooltip>Material ID</th>
              <th v-overflow-tooltip>Thumbnail</th>
              <th v-overflow-tooltip>Product Code</th>
              <th v-overflow-tooltip>Material Name</th>
              <th v-overflow-tooltip>ERP ID</th>
              <th v-overflow-tooltip>UOM</th>
              <th v-overflow-tooltip>Lead Time</th>
              <th v-overflow-tooltip>Inventory</th>
              <th v-overflow-tooltip class="attachments">Attachments</th>
              <th v-overflow-tooltip>Description</th>

              <th v-overflow-tooltip>Action</th>
            </tr>
          </thead>
          <thead v-else>
            <tr>
              <th v-overflow-tooltip>Resource ID</th>
              <th v-overflow-tooltip>Thumbnail</th>
              <th v-overflow-tooltip>Product Code</th>
              <th v-overflow-tooltip>Resource Name</th>
              <!-- <th>PLM ID</th> -->
              <th v-overflow-tooltip>ERP ID</th>
              <th v-overflow-tooltip>UOM</th>
              <th v-overflow-tooltip>Lead Time</th>
              <th v-overflow-tooltip>Inventory</th>
              <!-- <th>Unit Cost</th> -->
              <th class="attachments" v-overflow-tooltip>Attachments</th>
              <th v-overflow-tooltip>Description</th>
              <th v-overflow-tooltip>Status</th>
              <th v-overflow-tooltip>Action</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(mm, index) in materialMasterList" :key="mm.id"
            :class="{'obsolete-row': mm.status === 2}">
            <td class="elipsis-text" v-overflow-tooltip>{{ mm.custom_material_id || '--' }}</td>
            <td class="thumbnail" :class="{'relative': popoverIndex===index, 'pointer':true}"    @mouseover ="popoverIndex = index" @mouseleave="popoverIndex=null">
              <img v-if="mm.thumbnailUrl" :src="mm.thumbnailUrl" height="40" width="40" alt="">
              <img v-else src="~@/assets/images/thumbnail-placeholder.png" height="40" width="40" alt="">
              <div class="chip-popover v-center h-center" v-if="mm.thumbnailUrl && popoverIndex===index">
                <img :src="mm.thumbnailUrl" alt="">
                </div>
            </td>
              <td class="elipsis-text" v-overflow-tooltip>{{ mm.material_product_code?.product_code || '--' }}</td>
              <td class="elipsis-text" v-overflow-tooltip>{{ mm.material_name || '--' }}</td>
              <!-- <td v-overflow-tooltip>{{ mm.plm_material_id || '--' }}</td> -->
              <td class="elipsis-text" v-overflow-tooltip>{{ mm.erp_material_id || '--' }}</td>
              <td v-if="view === 'material'" :class="{redColor:!list_of_unit_of_material[mm.unit_of_material]}" v-overflow-tooltip>{{ mm.material_unit_details.name || '--' }}</td>
              <td v-else :class="{redColor:!list_of_unit_of_material[mm.unit_of_material]}" v-overflow-tooltip>{{ mm.material_unit_details.name || '--' }}</td>
              <td class="elipsis-text" v-overflow-tooltip>{{ mm.lead_time || '--' }}</td>
              <td class="elipsis-text" v-overflow-tooltip>{{ mm.inventory || '--' }}</td>
              <!-- <td>{{ mm.unit_cost || '--' }}</td> -->
              <td>
                 <span class="chip pointer"    @click="openDetail(mm.material_document_associations)">
                  <img v-tooltip="'View Documents'" src="~@/assets/images/icons/file-icon.svg" width="10px" height="100%" alt="" v-if="mm?.material_document_associations?.length"> {{(mm?.material_document_associations?.length) || '0'}} files
                </span>
              </td>
              <td class="elipsis-text" v-overflow-tooltip>{{ mm.material_description || '--' }}</td>
              <td v-if="view !== 'material'">
                <select :disabled="isOnProjectLevel || mm.status===4 || mm.status===2 || user.tenantLevelRole === 'VIEWER' || collaborator" class="custom-list-select" v-model="materialMasterList[index].resource_state_value.id" @change="saveStatus(mm.id, $event)">
                <option v-for="item in resourceStateValues" :value="item.id" :key="item.id" >{{item.name}}</option>
              </select>
            </td>
            <td class="action-column column">
  <div class="action-icons">
    <img
      v-show="showEditButton && (mm.status === 3 || mm.status === 1)"
      @click="openEditModal(index)"
      v-tooltip="view === 'material' ? 'Edit material' : 'Edit Resource'"
      src="~@/assets/images/pencil.svg"
      alt=""
      class="edit"
    />

    <img
      v-show="showLockButton && mm.status === 1"
      @click="openChangeStatusModal(index, 'lock')"
      v-tooltip="view === 'material' ? 'Lock material' : 'Lock Resource'"
      src="~@/assets/images/icons/locked-icon-black.svg"
      alt=""
      class="mx-1 pointer edit"
      width="16"
    />

    <img
      v-show="showUnLockButton && mm.status === 4"
      @click="openChangeStatusModal(index, 'unlock')"
      v-tooltip="view === 'material' ? 'Unlock material' : 'Unlock Resource'"
      src="~@/assets/images/icons/unlock-icon.svg"
      alt=""
      class="mx-1 pointer edit edit-1"
      width="16"
    />

    <img
      v-show="showObsoleteButton && mm.status === 1"
      @click="openMaterialObsoleteModal(index)"
      v-tooltip="view === 'material' ? 'Make material Obsolete' : 'Make Resource Obsolete'"
      src="~@/assets/images/trash-2.svg"
      alt=""
      class="pointer edit edit-1"
      width="16"
    />

    <img
      v-show="showActiveButton && mm.status === 2"
      @click="openChangeStatusModal(index, 'activate')"
      v-tooltip="view === 'material' ? 'Make material Active' : 'Make Resource Active'"
      src="~@/assets/images/icons/undo-icon.svg"
      alt=""
      class="pointer edit edit-1"
      width="16"
    />

    <img
      @click="openDetailModal(index)"
      v-tooltip="view === 'material' ? 'View material Details' : 'View Resource Details'"
      src="~@/assets/images/eye.svg"
      alt=""
      class="pointer mx-1 edit edit-1"
      width="16"
    />
  </div>
</td>

            </tr>
          </tbody>
        </table>
      </div>
      <div class="flex space-between">
      <pagination-2
        class="mt-4"
        :length="totalCount"
        :pageNumber="pageNumber"
        :perPage="perPage"
        @selectPage="$emit('selectPage', $event)"
      />
      <div class="total-count mt-4">
        <span>Total Materials :  &nbsp;  <b> {{ totalCount }}</b></span>
      </div>
      </div>
      <modal
        @close="closeDetailModal"
        :open="detailModalObject?.isOpen"
        :title="view === 'material' ? 'Material Master Detail View':'Resource Master Detail View'"
        :closeOnOutsideClick="true"
      >
        <detail-material-master
          :type="view"
          v-if="detailModalObject.selectedIndex !== -1"
          :materialMaster="materialMasterList[detailModalObject.selectedIndex]"
        />
        <detail-material-master
          :type="view"
          v-else-if="detailModalObject.selectedMaterialObj"
          :materialMaster="detailModalObject.selectedMaterialObj"
        />
      </modal>
      <modal
        @close="closeEditModal"
        :open="editModalObject?.isOpen"
        :title="view === 'material' ? 'Update Material Master':'Update Resource Master'"
      >
        <edit-mateterial-master
          @close="closeEditModal"
          :view="view"
          @updateAndClose="updateAndClose"
          v-if="editModalObject.selectedIndex !== -1"
          :materialMaster="materialMasterList[editModalObject.selectedIndex]"
        />
      </modal>
      <modal
        @close="closeMaterialObsoleteModal"
        :open="materialObsoleteObject?.isOpen"
        :title="view === 'material' ? 'Are you sure you want to make the material obsolete' : 'Are you sure you want to make the resource obsolete'"
      >
        <div v-if="materialObsoleteObject.selectedIndex !== -1">
          <span>Making the material <span class="weight-600">{{materialMasterList[materialObsoleteObject.selectedIndex].material_name}}</span> as obsolete will impact the boms associated with this material</span>
          <span class="productbom-title block mt-4">Products and BOMS where this material is used:</span>
          <associated-bom-table :loading="false" :productsAndBoms="materialObsoleteObject.productsAndBoms"/>
          <div class="flex flex-end py-3 m">
             <button class="btn btn-black mr-3" @click="closeMaterialObsoleteModal">CANCEL</button>
            <button class="btn" @click="confirmMaterialObsolete">CONFIRM</button>
         </div>
          <div>
          </div>
        </div>
      </modal>
      <attached-doc-view
        v-if="detailObject.open"
        :linkedDocs="detailObject.linkedDocs"
        @close="closeDetail"
      />
    </div>
  </template>

<script>
import { UpdateMaterialStatus, GetProductsUsingMaterialQuery, UnitOfMaterial, getFormIdByformType, ResourceState, ChangeResourceStatus, GetMaterialMasterData } from '@/api'
import Modal from '../common/modal.vue'
// import pagination from '../common/pagination.vue'
import pagination2 from '../common/pagination2.vue'
import AssociatedBomTable from './associatedBomTable.vue'
import DetailMaterialMaster from './detailMaterialMaster.vue'
import EditMateterialMaster from './editMateterialMaster.vue'
import ConfirmationDialog from '@/plugins/confirmationDialog'
import { success, alert } from '@/plugins/notification'
import Loader from '@/plugins/loader'
import AttachedDocView from '../../components/document/attachedDocView.vue'
import { mapGetters } from 'vuex'
import Config from '@/config'
export default {
  components: { pagination2, Modal, DetailMaterialMaster, EditMateterialMaster, AssociatedBomTable, AttachedDocView },
  name: 'materialMasterTable',
  props: {
    view: {
      type: String
    },
    materialMasterList: {
      type: Array,
      default: () => ([])
    },
    pageNumber: {
      type: Number,
      default: 1
    },
    perPage: {
      type: Number,
      default: 20
    },
    totalCount: {
      type: Number,
      default: 0
    }
  },
  data: () => ({
    resourceStateValues: [],
    detailModalObject: {
      isOpen: false,
      selectedIndex: -1,
      selectedMaterialObj: null
    },
    editModalObject: {
      isOpen: false,
      selectedIndex: -1
    },
    materialObsoleteObject: {
      isOpen: false,
      selectedIndex: -1,
      productsAndBoms: null
    },
    detailObject: {
      open: false,
      linkedDocs: []
    },
    popoverIndex: null,
    list_of_unit_of_material: {},
    Config: Config
  }),
  computed: {
    ...mapGetters(['user', 'getUserById']),
    ...mapGetters(['isTenantAdmin', 'isProjectAdmin', 'isTenantViewer', 'isProjectViewer', 'isOnProjectLevel', 'collaborator']),
    showEditButton () {
      return (
        (this.user.tenantLevelRole === 'ADMIN' ||
            this.user.tenantLevelRole === 'COLLABORATOR' ||
            this.user.tenantLevelRole === 'EDITOR') && !this.isOnProjectLevel && !this.collaborator
      )
    },
    showLockButton () {
      return (
        (this.user.tenantLevelRole === 'ADMIN' ||
            this.user.tenantLevelRole === 'COLLABORATOR') && !this.isOnProjectLevel && !this.collaborator
      )
    },
    showUnLockButton () {
      return (
        (this.user.tenantLevelRole === 'ADMIN' ||
            this.user.tenantLevelRole === 'COLLABORATOR') && !this.isOnProjectLevel && !this.collaborator
      )
    },
    showObsoleteButton () {
      return (
        this.user.tenantLevelRole === 'ADMIN' && !this.isOnProjectLevel && !this.collaborator
      )
    },
    showActiveButton () {
      return (
        this.user.tenantLevelRole === 'ADMIN' && !this.isOnProjectLevel && !this.collaborator
      )
    }
  },
  methods: {
    tooltipFunc () {

    },
    saveStatus (materialId, event) {
      ChangeResourceStatus(event?.target?.value, materialId).then(res => {
        success('Status Changed')
      }).catch(() => {
        alert('Unable to change status')
      })
    },
    openDetailModal (index) {
      this.detailModalObject.isOpen = true
      this.detailModalObject.selectedIndex = index
      this.$router.replace({ query: { selectedMaterialId: this.materialMasterList[index].id } })
    },
    closeDetailModal () {
      this.detailModalObject.isOpen = false
      this.detailModalObject.selectedIndex = -1
      this.detailModalObject.selectedMaterialObj = null
      this.$router.replace({ query: {} })
    },
    openEditModal (index) {
      this.editModalObject.isOpen = true
      this.editModalObject.selectedIndex = index
    },
    closeEditModal () {
      this.editModalObject.isOpen = false
      this.editModalObject.selectedIndex = -1
    },
    openMaterialObsoleteModal (index) {
      const loader = new Loader()
      loader.show()
      const productsAndBoms = {}
      GetProductsUsingMaterialQuery(this.$props.materialMasterList[index].id).then((res) => {
        res.core_material_master[0].bom_material_items.forEach(item => {
          const productCode = item.core_bom.product_bom ? item.core_bom.product_bom.product_code : ''
          if (productCode) {
            if (productsAndBoms[productCode]) {
              productsAndBoms[productCode].push(item.core_bom.name)
            } else {
              productsAndBoms[productCode] = [item.core_bom.name]
            }
          }
        })
        if (Object.keys(productsAndBoms).length) {
          this.materialObsoleteObject.isOpen = true
          this.materialObsoleteObject.selectedIndex = index
          this.materialObsoleteObject.productsAndBoms = productsAndBoms
        } else {
          ConfirmationDialog(`Are you sure you want to make the material <b> ${this.materialMasterList[index].material_name} </b> obselete`, (res) => {
            if (res) {
              this.materialObsoleteObject.selectedIndex = index
              this.confirmMaterialObsolete()
            }
          })
        }
      }).catch(() => {
        alert('Failed to fetch data')
      })
        .finally(() => {
          loader.hide()
        })
    },
    closeMaterialObsoleteModal () {
      this.materialObsoleteObject.isOpen = false
      this.materialObsoleteObject.productsAndBoms = null
      this.materialObsoleteObject.selectedIndex = -1
    },
    UpdateMaterialStatus (index, status) {
      const loader = new Loader()
      loader.show()
      const materialMasterTemp = JSON.parse(JSON.stringify(this.$props.materialMasterList[index]))
      return new Promise((resolve, reject) => {
        UpdateMaterialStatus(materialMasterTemp.id, status).then(() => {
          loader.hide()
          materialMasterTemp.status = status
          success('Updated material Status')
          materialMasterTemp.status = status
          this.$emit('updateList', {
            data: materialMasterTemp,
            index
          })
          resolve()
        }).catch((err) => {
          loader.hide()
          alert('Something went wrong')
          reject(err)
        })
      })
    },
    confirmMaterialObsolete () {
      this.UpdateMaterialStatus(this.materialObsoleteObject.selectedIndex, 2).then(() => {
        this.closeMaterialObsoleteModal()
      }).catch(() => {
        alert('Unable to make material obsolete')
      })
    },
    openChangeStatusModal (index, status) {
      const { material_name: materialName } = this.$props.materialMasterList[index]
      ConfirmationDialog(`Are you sure you want to ${status} the material ${materialName}`, (res) => {
        if (res) {
          if (status === 'lock') {
            this.UpdateMaterialStatus(index, 4)
          } else if (status === 'unlock' || status === 'activate') {
            this.UpdateMaterialStatus(index, 1)
          }
        }
      })
    },
    updateAndClose (newData) {
      this.$emit('updateList', {
        data: newData,
        index: this.editModalObject.selectedIndex
      })
      this.editModalObject.isOpen = false
      this.editModalObject.selectedIndex = -1
    },
    openDetail (docs) {
      const newDocArray = docs.map((doc) => {
        if (doc.core_document?.associated_versions?.length <= 0) {
          return doc
        } else {
          const latestDoc = doc.core_document?.associated_versions.at(-1)
          if (latestDoc.state === Config.DOC_STATE_MAP.CHECKIN) {
            return latestDoc
          } else if (doc.core_document?.associated_versions.length > 1) {
            return doc.core_document?.associated_versions.at(-2)
          } else {
            return doc
          }
        }
      })

      if (docs.length > 0) {
        this.detailObject = {
          open: true,
          linkedDocs: newDocArray
        }
      } else {
        alert('No documents avaiable')
      }
    },
    closeDetail () {
      this.detailObject = {
        open: false,
        fileId: ''
      }
    },
    listOfUnitOfMaterial () {
      UnitOfMaterial().then((res) => {
        this.list_of_unit_of_material = {}
        res.custom_list_values.forEach((item) => {
          this.list_of_unit_of_material[item.id] = {}
          this.list_of_unit_of_material[item.id].name = item.name
          this.list_of_unit_of_material[item.id].id = item.id
        })
      })
    },
    openCustomFieldForm (materialId, formId, mm) {
      getFormIdByformType(Config?.STANDARD_MATERIAL_FORM?.form_type).then(res => {
        const templateId = res.core_form_templates[0].id
        if (formId) {
          this.$router.push(`/form/editform/${templateId}/${Config.STANDARD_MATERIAL_FORM?.name}/${formId}/${materialId}?name=${mm.material_name}&id=${mm.custom_material_id}&plm=${mm.plm_material_id}&uom=${ mm.material_unit_details.name}`)
        } else {
          this.$router.push(`/form/createform/${templateId}/${Config.STANDARD_MATERIAL_FORM?.name}/${materialId}/${this.view}/${this.$route.params.pageNumber}?name=${mm.material_name}&id=${mm.custom_material_id}&plm=${mm.plm_material_id}&uom=${ mm.material_unit_details.name}`)
        }
      })
    }
  },
  async mounted () {
    ResourceState().then(res => {
      this.resourceStateValues = res.custom_list_values
    })
    this.listOfUnitOfMaterial()
    const materialId = this.$route.query.selectedMaterialId ?? this.$route.query.selectedmaterialid
    // showing the material details on selection
    if (materialId) {
      let materialDetails = this.materialMasterList.find(material => material.id === materialId)
      if (!materialDetails) {
        const filter = {
          jump: 0,
          perPage: 1000,
          id: materialId
        }
        const res = await GetMaterialMasterData(filter, true)
        materialDetails = res.core_material_master[0]
      }
      this.detailModalObject.isOpen = true
      this.detailModalObject.selectedMaterialObj = materialDetails
    }
  }
}
</script>

  <style lang="scss" scoped>
  .total-count {
      background-color: #F1F3F5;
      padding: 0.5rem 0.5rem 0.5rem;
      border: 1px solid #F1F3F5;
      border-radius: 0.2em;
      width: fit-content;
      padding-inline: 10px;
      margin-left: auto;
      }
    .mm-table {
      th {
        z-index: 4;
      }
      .chip {
      img {
        width: 10px;
        height: 100%;
        z-index: 5;
      }
          padding: 2px 8px;
          font-size: 10px;
          background-color: rgba(var(--brand-rgb), 0.5);
          z-index: 0;
              border-radius: 2px;
              position: relative;
              &-popover{
                position: absolute;
      width: 145px;
      height: 81px;
      background: transparent;
      /* left: -145px; */
      top: -30px;
      z-index: 4;
      display: flex;
      justify-content: start;
      transform: translateX(30%);

                & img{
                 width: auto;
                  height: 100%;
                  object-fit: fill;
                  border:1px solid rgb(51, 40, 40, 0.5);
                  border-radius: 4px;
                  -webkit-box-shadow: -11px 4px 49px -32px rgba(0,0,0,0.75);
                -moz-box-shadow: -11px 4px 49px -32px rgba(0,0,0,0.75);
                box-shadow: -11px 4px 49px -32px rgba(0,0,0,0.75);
                }
              }
        }
      .thumbnail {
        img {
          width: 40px;
          height: 40px;
        }
      }
    }
    .custom-list-select {
    max-width: 100%;
    min-width: 20%;
    // margin: 0 0 0 10px;
    margin-right: auto;
    font-size: 12px;
    background-color: var(--brand-light-color);
    line-height: 1;
    border: 1px solid var(--brand-color);
    border-radius: 4px;
    padding: 2px 1px;
  }
  .obsolete-row{
    background-color: var(--gray);
  }
  .material-master-table {
    height: calc(100% - 60px);
    margin-top: 10px;
    &--table {
      max-height: calc(100vh - 250px);
    }
    &--container {
      max-height: calc(100% - 60px);
      overflow: auto;
      table {

th,
td {
  max-width: 100px;
  /* Set your desired max width for each column */
  overflow: hidden;
  /* Prevents content from overflowing */
  text-overflow: ellipsis;
  /* Adds ellipsis (...) for overflowing text */
  white-space: nowrap;
}
.column {
padding: 10px 8px;
}
.edit-1 {
  margin: 0 2px;
}
}
  }
  .pagination-footer{
  padding-bottom: 10px;
  width:100%;
  display: flex;
  justify-content: space-between;
    &-total{
      margin-top: 16px;
      display: flex;
      background: #F1F3F5;
      padding: 0.5rem 0.5rem 0.5rem;
      border: 1px solid #F1F3F5;
      border-radius: 0.2em;
      width: fit-content;
      padding-inline: 10px;
      margin-left: auto;
    }
  }
  .productbom-title{
    color: var(--text-color-1);
    font-weight: 500;
    font-size: 0.9em;
  }
  .btn{
    color:var(--white)
  }
  .redColor{
    color:red
  }
  .attachments{
    z-index: 1;
  }
}
  </style>
