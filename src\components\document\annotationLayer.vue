<template>
    <div class="annotation-container">
      <div style="position: fixed; z-index: 2;">
        <input type="radio" id="selection" v-model="tool" value="selection" />
        <label for="selection">Selection</label>
        <input type="radio" id="line" v-model="tool" value="line" />
        <label for="line">Line</label>
        <input type="radio" id="rectangle" v-model="tool" value="rectangle" />
        <label for="rectangle">Rectangle</label>
        <input type="radio" id="pencil" v-model="tool" value="pencil" />
        <label for="pencil">Pencil</label>
        <input type="radio" id="text" v-model="tool" value="text" />
        <label for="text">Text</label>
      </div>
      <div style="position: fixed; z-index: 2; bottom: 0; padding: 10px;">
        <button @click="undo">Undo</button>
        <button @click="redo">Redo</button>
      </div>
      <textarea
        v-if="action === 'writing'"
        ref="textAreaRef"
        @blur="handleBlur"
        :style="{
          position: 'fixed',
          top: `${selectedElement.y1 - 2 + panOffset.y}px`,
          left: `${selectedElement.x1 + panOffset.x}px`,
          font: '24px sans-serif',
          margin: 0,
          color:'blue',
          padding: 0,
          border: 2,
          outline: 0,
          resize: 'auto',
          overflow: 'hidden',
          whiteSpace: 'pre',
          background: 'transparent',
          zIndex: 2
        }"
      ></textarea>
      <canvas
        id="canvas"
        :width="window.innerWidth"
        :height="window.innerHeight"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
        style="position: absolute; z-index: 1;"
      >
        Canvas
      </canvas>
    </div>
  </template>

<script>
import rough from 'roughjs/bundled/rough.esm'
import getStroke from 'perfect-freehand'

const generator = rough.generator()

export default {
  data () {
    return {
      elements: [],
      action: 'none',
      tool: 'pencil',
      selectedElement: null,
      panOffset: { x: 0, y: 0 },
      startPanMousePosition: { x: 0, y: 0 },
      pressedKeys: new Set(),
      window: window
    }
  },
  mounted () {
    this.window.addEventListener('keydown', this.handleKeyDown)
    this.window.addEventListener('keyup', this.handleKeyUp)
    document.addEventListener('keydown', this.handleUndoRedo)
    document.addEventListener('wheel', this.handlePan)
  },
  beforeDestroy () {
    this.window.removeEventListener('keydown', this.handleKeyDown)
    this.window.removeEventListener('keyup', this.handleKeyUp)
    document.removeEventListener('keydown', this.handleUndoRedo)
    document.removeEventListener('wheel', this.handlePan)
  },
  watch: {
    elements () {
      this.draw()
    }
  },
  methods: {
    handleKeyDown (event) {
      this.pressedKeys.add(event.key)
    },
    handleKeyUp (event) {
      this.pressedKeys.delete(event.key)
    },
    handleUndoRedo (event) {
      if ((event.metaKey || event.ctrlKey) && event.key === 'z') {
        if (event.shiftKey) {
          this.redo()
        } else {
          this.undo()
        }
      }
    },
    handlePan (event) {
      this.panOffset.x -= event.deltaX
      this.panOffset.y -= event.deltaY
    },
    draw () {
      const canvas = document.getElementById('canvas')
      const context = canvas.getContext('2d')
      const roughCanvas = rough.canvas(canvas)

      context.clearRect(0, 0, canvas.width, canvas.height)
      context.save()
      context.translate(this.panOffset.x, this.panOffset.y)

      this.elements.forEach((element) => {
        if (this.action === 'writing' && this.selectedElement.id === element.id) return
        this.drawElement(roughCanvas, context, element)
      })
      context.restore()
    },
    createElement (id, x1, y1, x2, y2, type) {
      switch (type) {
      case 'line':
      case 'rectangle': {
        const roughElement =
              type === 'line'
                ? generator.line(x1, y1, x2, y2)
                : generator.rectangle(x1, y1, x2 - x1, y2 - y1)
        return { id, x1, y1, x2, y2, type, roughElement } }
      case 'pencil':
        return { id, type, points: [{ x: x1, y: y1 }] }
      case 'text':
        return { id, type, x1, y1, x2, y2, text: '' }
      default:
        throw new Error(`Type not recognised: ${type}`)
      }
    },
    nearPoint (x, y, x1, y1, name) {
      return Math.abs(x - x1) < 5 && Math.abs(y - y1) < 5 ? name : null
    },
    onLine (x1, y1, x2, y2, x, y, maxDistance = 1) {
      const a = { x: x1, y: y1 }
      const b = { x: x2, y: y2 }
      const c = { x, y }
      const offset = this.distance(a, b) - (this.distance(a, c) + this.distance(b, c))
      return Math.abs(offset) < maxDistance ? 'inside' : null
    },
    positionWithinElement (x, y, element) {
      const { type, x1, x2, y1, y2 } = element
      switch (type) {
      case 'line': {
        const on = this.onLine(x1, y1, x2, y2, x, y)
        const start = this.nearPoint(x, y, x1, y1, 'start')
        const end = this.nearPoint(x, y, x2, y2, 'end')
        return start || end || on }
      case 'rectangle': {
        const topLeft = this.nearPoint(x, y, x1, y1, 'tl')
        const topRight = this.nearPoint(x, y, x2, y1, 'tr')
        const bottomLeft = this.nearPoint(x, y, x1, y2, 'bl')
        const bottomRight = this.nearPoint(x, y, x2, y2, 'br')
        const inside = x >= x1 && x <= x2 && y >= y1 && y <= y2 ? 'inside' : null
        return topLeft || topRight || bottomLeft || bottomRight || inside }
      case 'pencil': {
        const betweenAnyPoint = element.points.some((point, index) => {
          const nextPoint = element.points[index + 1]
          if (!nextPoint) return false
          return this.onLine(point.x, point.y, nextPoint.x, nextPoint.y, x, y, 5) != null
        })
        return betweenAnyPoint ? 'inside' : null }
      case 'text':
        return x >= x1 && x <= x2 && y >= y1 && y <= y2 ? 'inside' : null
      default:
        throw new Error(`Type not recognised: ${type}`)
      }
    },
    distance (a, b) {
      return Math.sqrt(Math.pow(a.x - b.x, 2) + Math.pow(a.y - b.y, 2))
    },
    getElementAtPosition (x, y, elements) {
      return elements
        .map((element) => ({ ...element, position: this.positionWithinElement(x, y, element) }))
        .find((element) => element.position !== null)
    },
    adjustElementCoordinates (element) {
      const { type, x1, y1, x2, y2 } = element
      if (type === 'rectangle') {
        const minX = Math.min(x1, x2)
        const maxX = Math.max(x1, x2)
        const minY = Math.min(y1, y2)
        const maxY = Math.max(y1, y2)
        return { x1: minX, y1: minY, x2: maxX, y2: maxY }
      } else {
        if (x1 < x2 || (x1 === x2 && y1 < y2)) {
          return { x1, y1, x2, y2 }
        } else {
          return { x1: x2, y1: y2, x2: x1, y2: y1 }
        }
      }
    },
    cursorForPosition (position) {
      switch (position) {
      case 'tl':
      case 'br':
      case 'start':
      case 'end':
        return 'nwse-resize'
      case 'tr':
      case 'bl':
        return 'nesw-resize'
      default:
        return 'move'
      }
    },
    resizedCoordinates (clientX, clientY, position, coordinates) {
      const { x1, y1, x2, y2 } = coordinates
      switch (position) {
      case 'tl':
      case 'start':
        return { x1: clientX, y1: clientY, x2, y2 }
      case 'tr':
        return { x1, y1: clientY, x2: clientX, y2 }
      case 'bl':
        return { x1: clientX, y1, x2, y2: clientY }
      case 'br':
      case 'end':
        return { x1, y1, x2: clientX, y2: clientY }
      default:
        return null
      }
    },
    handleMouseDown (event) {
      const { clientX, clientY } = event
      if (this.tool === 'selection') {
        const element = this.getElementAtPosition(clientX - this.panOffset.x, clientY - this.panOffset.y, this.elements)
        if (element) {
          const offsetX = clientX - element.x1
          const offsetY = clientY - element.y1
          this.setState({ selectedElement: { ...element, offsetX, offsetY } })
          if (element.position === 'inside') {
            this.setState({ action: 'moving' })
          } else {
            this.setState({ action: 'resizing' })
          }
        }
      } else if (this.tool === 'text') {
        const element = this.createElement(
          this.elements.length,
          clientX - this.panOffset.x,
          clientY - this.panOffset.y,
          clientX - this.panOffset.x,
          clientY - this.panOffset.y,
          this.tool
        )
        this.setState({ elements: [...this.elements, element], action: 'writing', selectedElement: element })
      } else {
        const id = this.elements.length
        const element = this.createElement(
          id,
          clientX - this.panOffset.x,
          clientY - this.panOffset.y,
          clientX - this.panOffset.x,
          clientY - this.panOffset.y,
          this.tool
        )
        this.setState({ elements: [...this.elements, element], action: this.tool === 'pencil' ? 'sketching' : 'drawing' })
      }
    },
    handleMouseMove (event) {
      const { clientX, clientY } = event
      if (this.tool === 'selection') {
        const { selectedElement, action } = this
        if (selectedElement) {
          if (action === 'moving') {
            const elements = this.elements.map((element) => {
              if (element.id === selectedElement.id) {
                const x1 = clientX - selectedElement.offsetX
                const y1 = clientY - selectedElement.offsetY
                return { ...element, x1, y1, x2: x1 + (selectedElement.x2 - selectedElement.x1), y2: y1 + (selectedElement.y2 - selectedElement.y1) }
              }
              return element
            })
            this.setState({ elements })
          } else if (action === 'resizing') {
            const { x1, y1, x2, y2 } = selectedElement
            const { x1: newX1, y1: newY1, x2: newX2, y2: newY2 } = this.resizedCoordinates(
              clientX - this.panOffset.x,
              clientY - this.panOffset.y,
              selectedElement.position,
              { x1, y1, x2, y2 }
            )
            const elements = this.elements.map((element) => {
              if (element.id === selectedElement.id) {
                return { ...element, x1: newX1, y1: newY1, x2: newX2, y2: newY2 }
              }
              return element
            })
            this.setState({ elements })
          }
        }
      } else if (this.tool === 'pencil' && this.action === 'sketching') {
        const { elements } = this
        const lastElement = elements[elements.length - 1]
        const newPoints = [...lastElement.points, { x: clientX - this.panOffset.x, y: clientY - this.panOffset.y }]
        const updatedElement = { ...lastElement, points: newPoints }
        const updatedElements = [...elements.slice(0, -1), updatedElement]
        this.setState({ elements: updatedElements })
      } else if (this.action === 'drawing') {
        const elements = this.elements.map((element, index) => {
          if (index === this.elements.length - 1) {
            const { x1, y1 } = element
            const roughElement = this.tool === 'line' ? generator.line(x1, y1, clientX - this.panOffset.x, clientY - this.panOffset.y) : generator.rectangle(x1, y1, clientX - this.panOffset.x - x1, clientY - this.panOffset.y - y1)
            return { ...element, x2: clientX - this.panOffset.x, y2: clientY - this.panOffset.y, roughElement }
          }
          return element
        })
        this.setState({ elements })
      }
    },
    handleMouseUp () {
      const { action, selectedElement } = this
      if (action === 'writing') {
        const textArea = this.$refs.textAreaRef
        textArea.style.border = '2px solid white'
        textArea.focus()
      } else if (selectedElement && (action === 'resizing' || action === 'moving')) {
        const { id, x1, y1, x2, y2, type } = selectedElement
        const updatedElement = { id, x1, y1, x2, y2, type, roughElement: this.tool === 'line' ? generator.line(x1, y1, x2, y2) : generator.rectangle(x1, y1, x2 - x1, y2 - y1) }
        const elements = this.elements.map((element) => (element.id === selectedElement.id ? updatedElement : element))
        this.setState({ elements })
      }
      this.setState({ action: 'none', selectedElement: null })
    },
    handleBlur (event) {
      const { value } = event.target
      const { selectedElement, elements } = this
      const updatedElement = { ...selectedElement, text: value }
      const updatedElements = elements.map((element) => (element.id === selectedElement.id ? updatedElement : element))
      this.setState({ elements: updatedElements, action: 'none', selectedElement: null })
    },
    drawElement (roughCanvas, context, element) {
      const { type, x1, y1 } = element
      switch (type) {
      case 'line':
      case 'rectangle':
        roughCanvas.draw(element.roughElement)
        break
      case 'pencil': {
        const stroke = getStroke(element.points)
        context.fill(new Path2D(this.getSvgPathFromStroke(stroke)))
        break }
      case 'text':
        context.font = '24px sans-serif'
        context.fillText(element.text, x1, y1)
        break
      default:
        throw new Error(`Type not recognised: ${type}`)
      }
    },
    undo () {
      this.elements.pop()
    },
    redo () {
      // Implement redo functionality
    },
    setState (newState) {
      Object.keys(newState).forEach((key) => {
        this[key] = newState[key]
      })
    },
    getSvgPathFromStroke (stroke) {
      if (!stroke.length) return ''

      const d = stroke.reduce(
        (acc, [x0, y0], i, arr) => {
          const [x1, y1] = arr[(i + 1) % arr.length]
          acc.push(x0, y0, (x0 + x1) / 2, (y0 + y1) / 2)
          return acc
        },
        ['M', ...stroke[0], 'Q']
      )

      d.push('Z')
      return d.join(' ')
    }
  }

}
</script>

  <style scoped>
  .annotation-container{
    position: absolute;
    top: 0px;
    left:0px;
    width: 100vw;
    height: 100vh;
    background:transparent;
  }
  </style>
