<template>
    <div class="progress-bar">
      <div
        class="progress-fill"
        :style="{ width: percentage + '%', backgroundColor: getColor(percentage)}"
      ></div>
    </div>
</template>

<script>
export default {
  props: {
    percentage: {
      type: Number,
      required: true,
      validator: (value) => value >= 0 && value <= 100 // Ensure valid range
    }
  },
  methods: {
    getColor (value) {
      if (value <= 30) return '#ff6666' // Light Red
      if (value <= 70) return '#ffcc4d' // Light Yellow
      return '#5eb85e' // Light Green
    }
  }
}
</script>

<style scoped>
.progress-bar {
  width: 60%;
  height: 10px;
  background-color: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
}
.progress-fill {
  height: 100%;
  transition: width 0.3s ease-in-out;
}
</style>
