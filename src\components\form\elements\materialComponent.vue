<template>
  <div
    :class="{
      'form-input': true,
      'form-input--required': data.required,
    }"
  >
    <div class="v-center space-between">
      <label>{{ data.caption }}:
        <img
      v-if="data.visibility"
      class="mx-1"
      width="15"
      src="~@/assets/images/eye.svg"
      alt="view"
      />
      </label>
      <button class="btn" v-if="!viewOnly" @click="[open = true,componentValueTemp = componentValue]">
        Add Material
      </button>
    </div>
    <div class="form-input--material">
      <!-- Add no material condition -->
      <div
        v-if="!componentValue.length"
        class="form-input--material__no-material"
      >
        No Material Selected Yet!
      </div>
      <div
        v-else-if="loadingSelectedMaterials"
        class="form-input--material__no-material"
      >
        <loading-circle />
      </div>
      <div v-else class="material-table dtx-table">
        <div class="bom-detail-table bom-detail-head bom-detail-header" >
    <div>Product Code</div>
    <div>Material ID</div>
    <div>Quantity</div>
    <div>BOM</div>
    <div></div>
    <div></div>
  </div>
  <div v-for="(item, index) in componentValue" :key="index">
        <div
          class="bom-detail-table bom-detail-body"
        >
          <div  v-overflow-tooltip>
          {{ selectedMaterialsMap[item.material_id]?.material_product_code?.product_code || "--" }}
          </div>
          <div v-overflow-tooltip>{{ selectedMaterialsMap[item.material_id]?.material_name || "--" }}</div>
          <div><input
                  :disabled="viewOnly"
                  type="number"
                  :min="1"
                  v-model="componentValue[index].quantity"
                  @change="handleQuantityChange(index)"
                /></div>
          <div>
            <div v-if="!bomAssociations[item.material_id]">
                  <img
                  v-if="selectedMaterialsMap[item.material_id]?.product_code && !isExternalCollaborator"
                    :class="{
                      'add-bom': true,
                      active: true,
                      'ml-2': true
                    }"
                    v-tooltip="'Associate Bom'"
                    src="~@/assets/images/icons/add-new-icon.svg"
                    width="20px"
                    alt=""
                    @click="!viewOnly && openAssociateBomModal(item, index)"
                  />
                  <img
                    v-else
                    :class="{
                      'add-bom': true,
                      active: false,
                      'ml-2': true,
                    }"
                    src="~@/assets/images/icons/add-new-icon.svg"
                    width="20px"
                    alt=""
                  />
                </div>
                <div
                  v-else
                  :class="!isExternalCollaborator ? '':'disabled'"
                  class="associated-bom"
                  v-tooltip="bomAssociations[item.material_id].name"
                  @click="!viewOnly && openAssociateBomModal(item, index)"
                >
                  {{ bomAssociations[item.material_id].name }}
                </div>
          </div>
          <div >
            <img v-if="bomAssociations[item.material_id]" @click="openBomListData(item, index)" :class="{
            open: openBomDetails && selectedIndex === index,
            close: !(openBomDetails && selectedIndex === index),
          }" v-tooltip="'View BOM Details'" width="16" src="~@/assets/images/eye.svg" alt="" />
          </div>
            <div class="action" @click="!viewOnly && removeMaterial(index)">
                <img
                  width="16px"
                  src="~@/assets/images/close.png"
                  alt=""
                />
                </div>
        </div>
        <div v-if="openBomDetails && selectedIndex === index" class="bom-child bom-details">
          <bom-detail-head :materialComponent="true" :headings="visibleHeadings"/>
          <bom-detail-body
            :showEdit="false"
            :materialComponent="true"
            :bomVersionId="bomAssociations[item.material_id]?.versionId"
            :bomId="bomAssociations[item.material_id]?.id"
          />
        </div>
      </div>
      </div>
    </div>
    <modal
      title="Select your materials"
      :open="open"
      @close="open = false"
      :closeOnOutsideClick="true"
    >
    <div class="relative">
      <div class="form-input--table pr-1">
        <div class="material-bar">
          <div class="material-bar--title">Add Material</div>
          <div class="input-group search">
            <input
              type="text"
              v-model="searchKeyWord"
              @input="updateOnSearch"
              placeholder=" Filter by Name, ID, Product Code, and Description"
            />
          </div>
        </div>
        <filterTag
          :tags="getTags"
          @new-tag="addNewTag"
          @clear="clearTags"
          @update-tags="updateTags"
          :type="1"
          :clear="clear"
          title="Add tags to filter"
        />
        <div class="material-table dtx-table mb-4"  :style="{height: '420px'}" >
          <table style="min-width: 800px">
            <thead>
              <tr>
                <th></th>
                <th>Product Code</th>
                <th>Material ID</th>
                <th>PLM ID</th>
                <th>ERP ID</th>
                <th>UOM</th>
                <th>Description</th>
                <th>Lead Time</th>
              </tr>
            </thead>
            <tbody v-if="!materialMasterLoading">
              <tr v-for="item in materialMasterData" :key="item.material_id">
                <td >
                  <img
                  @click="removeSelected(item)"
                    v-if="
                      componentValueTemp.findIndex(
                        (selectedItem) => selectedItem.material_id === item.id
                      ) !== -1
                    "
                    src="~@/assets/images/icons/checked-icon.svg"
                    alt=""
                  />
                  <img
                  @click="toggleToSelected(item)"
                    v-else
                    src="~@/assets/images/icons/unchecked-icon.svg"
                    alt=""
                  />
                </td>
                <td v-overflow-tooltip>{{ item.material_product_code?.product_code || "--" }}</td>
                <td v-overflow-tooltip>{{ item.custom_material_id || "--" }}</td>
                <td v-overflow-tooltip>{{ item.plm_material_id || "--" }}</td>
                <td v-overflow-tooltip>{{ item.erp_material_id || "--" }}</td>
                <td v-overflow-tooltip>{{ item.material_unit_details?.name || "--" }}</td>
                <td v-overflow-tooltip>{{ item.material_description || "--" }}</td>
                <td>{{ item.lead_time || "--" }}</td>
              </tr>
            </tbody>
          </table>
          <div class="center p-6">
            <loading-circle v-if="materialMasterLoading" />
          </div>
        </div>
      </div>
      <div class="footer-box v-center">
        <pagination
        :length="totalCount"
        :perPage="perPage"
        :pageNumber="pageNumber"
        @selectPage="selectPage"
      />
      <div class="mt-4">
        <button class="btn btn-black mx-2" @click="addSelectedMaterials" >Add</button>
        <button class="btn mx-2" @click="[clear=true,open = false]">Cancel</button>
      </div>
      </div>
    </div>
    </modal>
        <modal
      :open="associateBom.show"
      @close="closeAssociateBomModal"
      title="Associate Bom"
    >
      <select-bom
        v-if="associateBom.show"
        :selectedBomId="associateBom.associated_bom_id"
        :product_code="associateBom.product_code"
        :selectedBomVersionId="associateBom.associated_bom_version_id"
        @selectBom="selectAssociateBom"
        @close="closeAssociateBomModal"
        :showCloseButton="true"
      />
    </modal>
  </div>
</template>

<script>
import {
  GetMaterialMasterForBom,
  GetMaterialListByIdsQuery
} from '@/api'
import SelectBom from '@/components/bom/common/selectBom.vue'
import modal from '../../common/modal.vue'
import Pagination from '../../common/pagination.vue'
import LoadingCircle from '../../common/loadingCircle.vue'
import filterTag from '../../common/filterTag.vue'
import { debounce } from '@/utils/debounce'
import { mapGetters, mapMutations } from 'vuex'
import bomDetailBody from '../../../components/bom/common/bomDetailBody.vue'
import BomDetailHead from '../../../components/bom/common/bomDetailHead.vue'
export default {
  components: { modal, Pagination, LoadingCircle, filterTag, SelectBom, bomDetailBody, BomDetailHead },
  name: 'MaterialComponent',
  props: {
    mode: {
      type: String,
      default: 'TEMPLATE'
    },
    data: {
      type: Object,
      default: () => {}
    },
    value: {
      type: Array,
      default: () => []
    },
    viewOnly: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      bomAssociations: {},
      selectedAssociatedBom: null,
      open: false,
      associateBom: {
        show: false,
        product_code: null,
        associated_bom_id: null,
        associated_bom_name: null,
        associated_bom_version: null,
        associated_bom_version_id: null,
        item: null,
        index: null
      },
      materialMasterData: [],
      selectedMaterialsMap: {},
      materialMasterLoading: false,
      loadingSelectedMaterials: false,
      pageNumber: 1,
      perPage: 10,
      totalCount: 0,
      searchKeyWord: '',
      loading: false,
      updateOnSearch: null,
      componentValue: [],
      componentValueTemp: [],
      delete: [],
      insert: [],
      update: [],
      clear: false,
      openBomDetails: false,
      selectedIndex: null
    }
  },
  watch: {
    searchKeyword (val) {
      // this.getMaterialData()
    },
    open (val) {
      if (val) {
        // this.getMaterialData()
      }
    },
    '$store.state.tag.tags': function () {
      this.fetchMaterialMasterData()
    }
  },
  computed: {
    ...mapGetters('tag', ['getTags'], ['selectedBomId']),
    ...mapGetters(['isExternalCollaborator']),
    ...mapGetters(['isOnProjectLevel']),
    addedBomItems () {
      return this.componentValue.map((item) => item.id)
    },
    visibleHeadings () {
      return [
        'Product Code',
        'Material ID',
        'UOM',
        'Description',
        'Unit Size',
        'Quantity',
        'Material Name'
      ]
    }
  },
  methods: {
    ...mapMutations('tag', ['clearAllTags']),
    removeAttachedBom (index, materialId) {
      if (materialId) {
        this.$delete(this.bomAssociations, materialId)
        this.componentValue[index].bom_id = null
      }
    },
    closeAssociateBomModal (bom) {
      this.associateBom.show = false
      this.associateBom.item = null
      this.associateBom.associated_bom_id = null
      this.associateBom.associated_bom_name = null
      this.associateBom.associated_bom_version = null
      this.associateBom.associated_bom_version_id = null
      this.associateBom.index = null
    },
    selectAssociateBom (bom) {
      const materialId = this.associateBom.material_id
      const isEmpty = Object.keys(bom).length === 0
      if (isEmpty) {
        this.removeAttachedBom(this.associateBom.index, materialId)
      } else if (this.selectedAssociatedBom && bom.id === this.selectedAssociatedBom.id) {
      }
      // Update BOM association
      if (materialId) {
        this.$set(this.bomAssociations, materialId, { id: bom.id || null, name: bom.name || '', versionId: bom.versionId, productCode: bom.productCode })
      }
      this.selectedAssociatedBom = bom
      this.componentValue[this.associateBom.index].bom_id = this.bomAssociations[materialId].id || null
      const materialComponent = this.componentValue[this.associateBom.index]
      if (materialComponent.field_id) {
        this.update.push(materialComponent)
      } else {
        const insertedObj = this.insert.find((item) => item.material_id === materialComponent.material_id)
        if (insertedObj) {
          insertedObj.bom_id = materialComponent.bom_id
        }
      }
      this.emitChanges()
    },
    openAssociateBomModal (item, index) {
      const productCode = this.selectedMaterialsMap[item.material_id]?.product_code
      this.associateBom.show = true
      this.associateBom.item = item
      this.associateBom.product_code = productCode
      this.associateBom.associated_bom_id = item?.core_bom?.id
      this.associateBom.associated_bom_name = item?.core_bom?.name
      this.associateBom.associated_bom_version = item?.core_bom?.bom_versions[0]?.version_no
      this.associateBom.associated_bom_version_id = item?.core_bom?.bom_versions[0]?.id
      this.associateBom.index = index
      this.associateBom.material_id = item.material_id
    },
    ...mapMutations('tag', ['addNewTag', 'updateTags']),
    selectPage (page) {
      this.pageNumber = page
      this.fetchMaterialMasterData()
    },
    emitChanges () {
      const value = {}
      if (this.insert.length) {
        value.insert = this.insert
      }
      if (this.delete.length) {
        value.delete = this.delete
      }
      if (this.update.length) {
        value.update = this.update
      }
      this.$emit('customChange', {
        field_id: this.$props.data.field_id,
        value
      })
    },
    handleUnitCostChange (index) {
      const material = this.componentValue[index]
      if (material.field_id) {
        const updatedObj = this.update.find((item) => item.material_id === material.material_id)
        if (updatedObj) {
          updatedObj.unit_cost = material.unit_cost
        } else {
          this.update.push({
            material_id: material.material_id,
            unit_cost: material.unit_cost,
            quantity: material.quantity
          })
        }
      } else {
        const insertedObj = this.insert.find((item) => item.material_id === material.material_id)
        if (insertedObj) {
          insertedObj.unit_cost = material.unit_cost
        }
      }
      this.emitChanges()
    },
    handleQuantityChange (index) {
      const material = this.componentValue[index]
      if (material.field_id) {
        const updatedObj = this.update.find((item) => item.material_id === material.material_id)
        if (updatedObj) {
          updatedObj.quantity = material.quantity
        } else {
          this.update.push({
            material_id: material.material_id,
            unit_cost: material.unit_cost,
            quantity: material.quantity
          })
        }
      } else {
        const insertedObj = this.insert.find((item) => item.material_id === material.material_id)
        if (insertedObj) {
          insertedObj.quantity = material.quantity
        }
      }
      this.emitChanges()
    },
    removeMaterial (index) {
      const material = this.componentValue[index]
      if (material.field_id) {
        this.delete.push(material.material_id)
      }
      this.componentValue.splice(index, 1)
      this.insert = this.insert.filter(doc => doc.material_id !== material.material_id)
      this.emitChanges()
    },
    toggleToSelected (item) {
      const index = this.componentValueTemp.findIndex(
        (selectedItem) => selectedItem.id === item.id
      )
      if (index > -1) {
        this.componentValueTemp.splice(index, 1)
      } else {
        this.componentValueTemp.push({
          material_id: item.id,
          quantity: 0,
          unit_cost: 0
        })
      }
      this.componentValue = []
    },
    // add the selected material to form
    addSelectedMaterials () {
      this.clear = true
      this.clearAllTags()
      for (const item of this.componentValueTemp) {
        item.unit_cost = 1
        if (!item.field_id) {
          item.quantity = 1
          this.insert.push(item)
        }
      }
      this.emitChanges()
      this.componentValue = JSON.parse(JSON.stringify(this.componentValueTemp))
      this.loadSelectedMaterialMap()
      this.open = false
      this.componentValueTemp = [] // this is temparorary variable storig for selelctd variable making this null when the value is added to real variable compolentValue
    },
    // remove the selelcted material from modal
    removeSelected (item) {
      const index = this.componentValueTemp.findIndex(
        (selectedItem) => {
          if (selectedItem.field_id) {
            this.delete.push(selectedItem.material_id)
          }
          return selectedItem.material_id === item.id
        }
      )
      this.emitChanges()
      if (index !== -1) {
        this.componentValueTemp.splice(index, 1)
      }
    },
    loadSelectedMaterialMap () {
      const self = this
      this.loadingSelectedMaterials = true
      const materialMap = {}
      const materialIds = this.componentValue.map((item) => item.material_id)
      GetMaterialListByIdsQuery(materialIds).then((res) => {
        res.core_material_master.forEach((item) => {
          materialMap[item.id] = item
        })
        self.selectedMaterialsMap = materialMap
        this.loadingSelectedMaterials = false
      })
    },
    fetchMaterialMasterData () {
      this.materialMasterLoading = true
      const filter = {
        jump: (this.pageNumber - 1) * this.perPage,
        perPage: this.perPage,
        searchKeyword: this.searchKeyWord ? `%${this.searchKeyWord}%` : undefined
      }
      const self = this
      if (this.getTags.length > 0) {
        const body = {
          jump: (this.pageNumber - 1) * this.perPage,
          perPage: this.perPage,
          searchKeyword: this.searchKeyWord ? `%${this.searchKeyWord}%` : undefined,
          tagId: this.getTags.length ? this.getTags[this.getTags.length - 1]?.id : undefined
        }
        GetMaterialMasterForBom(body).then((res) => {
          self.materialMasterData = res.core_material_master
          self.totalCount = res.core_material_master_aggregate.aggregate.count
          this.materialMasterLoading = false
        }).catch((err) => {
          console.log(err)
          this.materialMasterLoading = false
        })
      } else {
        GetMaterialMasterForBom(filter).then((res) => {
          self.materialMasterData = res.core_material_master
          self.totalCount = res.core_material_master_aggregate.aggregate.count
          this.materialMasterLoading = false
        }).catch((err) => {
          console.log(err)
          this.materialMasterLoading = false
        })
      }
    },
    clearTags () {
      this.clear = false
      this.clearAllTags()
    },
    keyPress (e) {
      if (!this.open) {
      } else if (e instanceof KeyboardEvent && e.code === 'Escape') {
        this.clear = true
        this.open = false
      } else if (e instanceof KeyboardEvent && e.code === 'Enter') {
        this.addSelectedMaterials()
        this.open = false
      }
    },
    openBomListData (bomItem, index) {
      if (this.selectedIndex === index) {
        // If the clicked row is already open, close it
        this.openBomDetails = !this.openBomDetails
      } else {
        // If another row is clicked, open BOM details for that row
        this.selectedIndex = index
        this.openBomDetails = true
      }
    },
    getBomUrl (productCode, bomId, versionId) {
      if (this.isOnProjectLevel) {
        return `/bom/project/bom/${bomId}?bomVersionId=${versionId}`
      } else {
        // Construct and return the URL based on the bom object
        return `/bom/product/${productCode}/bom/${bomId}?bomVersionId=${versionId}` // Example URL construction
      }
    }
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
    this.updateOnSearch = debounce(() => {
      this.pageNumber = 1
      this.fetchMaterialMasterData()
    }, 500)
    this.fetchMaterialMasterData()
    this.componentValue = this.value
    this.componentValue.forEach(item => {
      if (item.core_bom) {
        this.$set(this.bomAssociations, item.material_id, {
          id: item.core_bom.id,
          name: item.core_bom.name,
          versionId: item.core_bom.bom_versions[0].id,
          productCode: item.core_bom.product_code
        })
      }
    })
    this.selectedAssociatedBom = this.componentValue[0]?.core_bom ? JSON.parse(JSON.stringify(this.componentValue[0]?.core_bom)) : null
    this.loadSelectedMaterialMap()
  },
  destroyed () {
    document.body.removeEventListener('keydown', this.keyPress)
  }

}
</script>

<style lang="scss" scoped >
.disabled {
  cursor: pointer;
  pointer-events: none;
}
.form-input {
  &-loading {
    width: 800px;
    min-height: 800px;
    max-height: 900px;
  }
  &--table {
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden;
  }
  &--material {
    position: relative;
    display: inline-block;
    width: 100%;
    margin-top: 10px;
    border: 1px solid rgb(179, 179, 179);
    min-height: 60px;
    max-height: 200px;
    overflow-y: auto;
    border-radius: 4px;
    &__no-material {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: rgb(179, 179, 179);
    }
  }
}
.add-bom {
      opacity: 0.4;
      cursor: not-allowed;
      &.active {
        opacity: 1;
        cursor: pointer;
      }
    }
.material-table {
  input {
    font-size: 12px;
    padding: 5px;
    width: 50px;
    border: 1px solid var(--brand-color-1);
  }
  th,
    td {
      max-width: 80px; /* Set your desired max width for each column */
        overflow: hidden; /* Prevents content from overflowing */
        text-overflow: ellipsis; /* Adds ellipsis (...) for overflowing text */
        white-space: nowrap;
    }
}
.footer-box{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.bom-details {
    width: 97%;
    margin-top: 10px;
    margin-left: 10px;
    border: 1px solid rgb(231 193 151);
    min-height: 60px;
    max-height: 200px;
    overflow-y: auto;
    border-radius: 4px;
}
</style>
