import { emptyString, positiveFloat } from './index'
export default (body, skipMaterialIdCheck) => {
  if (!skipMaterialIdCheck) {
    if (
      !emptyString(body.custom_material_id, 'Material Id', 'CustomMaterialId')
    ) {
      return false
    }
  }
  if (!emptyString(body.material_name, 'Material Name', 'CustomMaterialName')) {
    return false
  }
  if (!emptyString(body.plm_material_id, 'PLM Material Id', 'PlmMaterialId')) {
    return false
  }
  if (
    !emptyString(body.unit_of_material, 'Unit Of Material', 'UnitOfMaterial')
  ) {
    return false
  }
  if (
    body.gross_weight &&
    !positiveFloat(body.gross_weight, 'Gross weight', 'GrossWeight')
  ) {
    return false
  }
  if (
    body.unit_sale_price &&
    !positiveFloat(body.unit_sale_price, 'Unit sale price', 'UnitSalePrice')
  ) {
    return false
  }
  if (
    body.unit_cost &&
    !positiveFloat(body.unit_cost, 'Unit cost', 'UnitCost')
  ) {
    return false
  }
  if (body.inventory && !positiveFloat(body.inventory, 'Inventory', 'Inventory')) {
    return false
  }
  if (!body.gross_weight) {
    body.gross_weight = null
  }
  if (!body.unit_sale_price) {
    body.unit_sale_price = null
  }
  if (!body.unit_cost) {
    body.unit_cost = null
  }
  if (!body.gross_weight) {
    body.gross_weight = null
  }
  if (!body.inventory) {
    body.inventory = 0
  }
  if (!body.storage_loc) {
    body.storage_loc = null
  }
  if (!body.lead_time) {
    body.lead_time = null
  }
  return true
}
