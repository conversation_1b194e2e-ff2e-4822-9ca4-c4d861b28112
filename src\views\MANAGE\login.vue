<template>
  <section class="login text v-center column">
    <div class="px-4 py-10">
          <h1>Login</h1>
    <p class="py-5" >To your account to access your account</p>
    <div class="login-group bg px-20 v-center column">
      <img class="my-10" src="~@/assets/images/brand-transparent.png" alt="">
      <div data-validation="Email" class="input-group imp m my-4">
        <label>Enter your Email/Phone Number</label>
        <input v-model="username" placeholder="Enter your User Name" type="text">
      </div>
      <div data-validation="Password" class="label-2 imp m my-4">
        <label>Enter your Password</label>
        <div class="input-view">
        <input v-model="password" v-on:keyup.enter="tryLogin" placeholder="Enter your Password" :type="passwordType">
        <img  v-if="passwordType==='password'" src="~@/assets/images/icons/open-eye.svg" alt="" @click="passwordType = 'text'" >
        <img  v-if="passwordType==='text'" src="~@/assets/images/icons/hide-eye.svg" alt="" @click="passwordType = 'password'">
      </div></div>
      <div class="flex-end">
        <span class="pointer underline">
          <router-link to="/forgot-password">
          Forgot Password ?
        </router-link>
        </span>
      </div>
      <div class="v-center my-4">
        <input v-model="remember_me" type="checkbox" id="remember_me">
        <label class="mx-2" for="remember_me">Remember My Password</label>
      </div>
      <div class="flex-end my-4 l">
        <button @click="tryLogin" class="btn">LogIn</button>
      </div>
    </div>
  </div>
  </section>
</template>

<script>
import Cookie from '@/utils/cookie'
import UserLoginValidation from '@/helper/formValidation/userLogin'
import { login } from '@/api/session'
import Loader from '@/plugins/loader'
export default {
  name: 'loginPage',
  data () {
    return {
      username: '',
      password: '',
      remember_me: false,
      passwordType: 'password'
    }
  },
  methods: {
    checkRememberMe () {
      if (this.remember_me) {
        Cookie.write('UN', this.username)
        Cookie.write('UP', this.password)
      }
    },
    loginCallback (loginRes) {
      if (!loginRes.loggedIn) {
        this.$notify.alert(loginRes.message)
      } else {
        this.checkRememberMe()
        this.$notify.success(loginRes.message)
        const lastLocation = localStorage.getItem('lastPath') || '/'
        localStorage.setItem('lastPath', '')
        window.history.replaceState({}, '', '/')
        window.location.href = lastLocation
      }
    },
    tryLogin (event) {
      console.log(event)
      if (event.type === 'keypress' && event.code !== 'Enter') {
        return
      }
      const loader = new Loader()
      if (UserLoginValidation(this.username, this.password)) {
        loader.show()
        login(this.username, this.password).then(res => {
          this.loginCallback(res)
        }).catch(err => {
          this.$notify.alert(err?.message ?? 'Something went wrong')
        }).finally(() => {
          loader.hide()
        })
      }
    }
  },
  created () {
    this.username = Cookie.read('UN')
    this.password = Cookie.read('UP')
    document.body.addEventListener('keypress', this.tryLogin)
  },
  destroyed () {
    document.body.removeEventListener('keypress', this.tryLogin)
  }
}
</script>

<style lang="scss" scoped >
.login {
height: 100vh;
// display: flex;
justify-content: center;
&>div{
  max-height: 100%;
  display: flex;
  flex-direction: column;
// justify-content: center;
align-items: center;
  max-width: 700px;
  min-width: 80%;
  max-height: 100%;
}
  h1 {
    font-size: 36px;
    font-weight: 500;
  }
  &-group {
    max-width: 700px;
    width: 100%;
    border-radius: 4px;
    min-height: fit-content;
    & > div {
      width: 100%;
      button {
        width: 140px;
      }
    }
  }
}
.input-view{
    border: 1px solid rgba(59, 59, 59, 0.4666666667);
    width: 100%;
    border-radius: 0.285em;
    background-color: transparent;
    display: flex;
    align-items: center;
    &:focus-within {
      box-shadow: 0 0 0 1px var(--brand-color-1);
    }
}
.input-view input{
  padding-inline: .85em;
  padding-block: .85em;
  height: 100%;
  flex-grow: 1;
  border: 0;
  outline: 0;
  // font-size: 1em;
  background-color: transparent;
  &::placeholder {
      color: var(--text-color);
      opacity: 0.5;
    }
  }
  .input-view img{
      padding-inline: 0.5em;
  }
.label-2{
    display: block;
    width: 100%;
    font-size: 1.15em;
    margin-bottom: 0.36em
}
</style>
