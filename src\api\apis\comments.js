import store from '@/store'
import { runQuery, runMutation } from '../graphQl'
import * as commentsQuery from '../query/comments'

export const addComments = (targetType, targetId, comment, parentId, VersionId) => {
  const insertObject = {
    comment: comment,
    parent_id: parentId
  }
  if (targetType === 'bom') {
    insertObject.bom_id = targetId
    insertObject.bom_version_id = VersionId
  } else if (targetType === 'document') {
    insertObject.document_id = targetId
  } else if (targetType === 'form') {
    insertObject.form_id = targetId
  }
  return runMutation(commentsQuery.addCommentsQuery(), { insertObject }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}

export const addTaskComment = (targetType, targetId, comment, parentId) => {
  const body = {
    comment: comment,
    parent_id: parentId
  }
  if (targetType === 'task') {
    body.taskId = targetId
  }
  return runMutation(commentsQuery.addTaskCommentQuery(), body, 'project')
}

export const addTaskComments = (comments) => {
  return runMutation(commentsQuery.addTaskCommentsMutation(), { comments }, store.getters?.collaborator ? 'tenant' : 'project')
}

export const updateComment = (id, comment, project = false) => {
  return runMutation(commentsQuery.updateCommentQuery(), { id, comment }, project ? 'project' : 'tenant')
}

export const deleteComment = (id, project = false) => {
  return runMutation(commentsQuery.deleteCommentQuery(), { id }, project ? 'project' : 'tenant')
}

export const getAllDocumentsComments = (documentId, parentId) => {
  const conditions = {
    deleted: { _eq: false },
    _or: [
      { document_id: { _eq: documentId } },
      {
        core_document: {
          main_version_document: {
            id: {
              _eq: documentId
            }
          }
        }
      }
    ],
    bom_id: { _is_null: true },
    parent_id: parentId ? { _eq: parentId } : { _is_null: true }
  }
  if (!store.getters?.collaborator) {
    conditions.form_id = { _is_null: true }
  }
  return runQuery(commentsQuery.getAllDocumentsCommentsQuery(), { conditions }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}

export const getAllBomComments = (bomId, parentId) => {
  const conditions = {
    deleted: { _eq: false },
    document_id: { _is_null: true },
    bom_id: { _eq: bomId },
    parent_id: parentId ? { _eq: parentId } : { _is_null: true }
  }
  if (!store.getters?.collaborator) {
    conditions.form_id = { _is_null: true }
  }
  return runQuery(commentsQuery.getAllBomCommentsQuery(), { conditions }, 'tenant')
}

export const getAllTaskComments = (taskId, parentId) => {
  let token = 'project'
  const conditions = {
    deleted: { _eq: false },
    document_id: { _is_null: true },
    bom_id: { _is_null: true },
    task_id: { _eq: taskId },
    parent_id: parentId ? { _eq: parentId } : { _is_null: true }
  }
  if (store.getters?.collaborator) {
    token = 'tenant'
  } else {
    conditions.form_id = { _is_null: true }
  }
  return runQuery(commentsQuery.getAllTaskCommentsQuery(), { conditions }, token)
}

export const getAllFormComments = (formId, parentId) => {
  return runQuery(commentsQuery.getAllFormCommentsQuery(formId, parentId), {}, 'tenant')
}
