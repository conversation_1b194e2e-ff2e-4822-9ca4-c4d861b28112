<template>
  <div ref="pdfviewContainer" class="pdfviewContainer">
  <div class="pdfviewContainer-statusbox">
    <div v-for="(obj, index) in pdf.selectedObjects" :key="index">
      <statusbox :obj="obj" :index="index+1" :customList="customList" @updateStatus="updateStatus" :revisionList="revisionList"/>
    </div>

  </div>
    <div id="pdf-container" class="pdf-container">
      <div class="toolbar h-center">
        <div
          :class="{ 'v-center h-center flex-grow toolbar-box': true, 'disable-div': !doAnnotation }"
          v-if="!viewOnly2"
        >
          <div class="tool tool-brush-size" v-click-outside="closeStyleBox">
    <div class="tool-button" @click="styleBox=!styleBox" >Styles</div>
            <div  v-if="styleBox" class="tool-drag-box" >
              <div class="tool-drag-box-container">
                <p for=""> Brush size / Stroke Width </p>
                <progress-drag
                  @dragValue="changeStrokeWidth"
                  :activeValue="strokeWidth * 2"
                >  <div
                  :style="{
                    width: strokeWidth + 'px',
                    height: strokeWidth + 'px',
                  }"
                  class="tool-drag-box-sroke-size"
                ></div>
              </progress-drag>
                <p for=""> Font Size</p>

                <progress-drag
                  @dragValue="changeFontSize"
                  :activeValue="fontSize"
                >
                <div
                  :style="{ fontSize: fontSize + 'px' }"
                  class="tool-drag-box-font-size"
                >
                  Aa
                </div>
              </progress-drag>
              </div>
            </div>
          </div>
          <div class="tool">
            <button
              :class="{
                'color-tool mx-1': true,
                'toolbar-active-color': pdf?.color === 'black',
              }"
              style="background-color: black"
              @click="selectBrushColor('black')"
            ></button>
            <button
              :class="{
                'toolbar-color-tool mx-1': true,
                'toolbar-active-color': pdf?.color === 'red',
              }"
              style="background-color: red"
              @click="selectBrushColor('red')"
            ></button>
            <button
              :class="{
                'toolbar-color-tool mx-1': true,
                'toolbar-active-color': pdf?.color === 'blue',
              }"
              style="background-color: blue"
              @click="selectBrushColor('blue')"
            ></button>
            <button
              :class="{
                'toolbar-color-tool mx-1': true,
                'toolbar-active-color': pdf?.color === 'green',
              }"
              style="background-color: green"
              @click="selectBrushColor('green')"
            ></button>
            <button
              :class="{
                'toolbar-color-tool mx-1': true,
                'toolbar-active-color': pdf?.color === 'yellow',
              }"
              style="background-color: yellow"
              @click="selectBrushColor('yellow')"
            ></button>
          </div>
          <div class="tool">
            <button
              :class="{
                'tool-button': true,
                'toolbar-activetool': pdf.active_tool === 0,
              }"
              @click="enableSelector"
            >
              <img
                class="tool-button-icon"
                src="~@/assets/images/icons/annotation/selelctor.svg"
                title="Selector"
                width="18px"
                alt=""
              />
            </button>
          </div>
          <div class="tool">
            <button
              :class="{
                'tool-button': true,
                'toolbar-activetool': pdf.active_tool === 1,
              }"
              @click="enablePencil"
            >
              <img
                class="tool-button-icon"
                src="~@/assets/images/icons/annotation/pencil.svg"
                title="Free Hand"
                width="18px"
                alt=""
              />
            </button>
          </div>
          <div class="tool">
            <button
              :class="{
                'tool-button': true,
                'toolbar-activetool': pdf.active_tool === 2,
              }"
              @click="enableAddText"
            >
              <img
                class="tool-button-icon"
                src="~@/assets/images/icons/annotation/text.svg"
                title="Text"
                width="18px"
                alt=""
              />
            </button>
          </div>
          <div class="tool">
            <button
              :class="{
                'tool-button': true,
                'toolbar-activetool': pdf.active_tool === 3,
              }"
              @click="enableAddArrow"
            >
              <img
                class="tool-button-icon"
                src="~@/assets/images/icons/annotation/rightArrow.svg"
                title="Arrow"
                width="18px"
                alt=""
              />
            </button>
          </div>
          <div class="tool">
            <button
              :class="{
                'tool-button': true,
                'toolbar-activetool': pdf.active_tool === 4,
              }"
              @click="enableRectangle"
            >
              <img
                class="tool-button-icon"
                src="~@/assets/images/icons/annotation/rectangle.svg"
                title="Rectangle"
                width="18px"
                alt=""
              />
            </button>
          </div>
          <div class="tool">
            <button
              :class="{
                'tool-button': true,
                'toolbar-activetool': selectedTool === 'image',
              }"
              @click="(e) => addImage(e)"
            >
              <img
                class="tool-button-icon"
                src="~@/assets/images/icons/annotation/image.svg"
                title="Images"
                width="18px"
                alt=""
              />
            </button>
          </div>
          <div class="tool" v-click-outside="closeiconList">
            <button
              :class="{ 'tool-button-fixedimg': true }"
              @click="openIconList"
            >
              <img
                v-if="pdf.iconObject.id"
                :src="iconsMap[pdf.iconObject.id]"
                width="20"
                height="20"
                title="Icons"
              />
              <img
                v-else
                src="~@/assets/images/icons/annotation/icons.svg"
                width="20"
                height="20"
                title="Icons"
              />
              <ul
                :class="`tool-button-fixedimg-list ${
                  iconList ? ' openHeight' : ' closedHeight'
                }`"
                v-keyboard-selection
                tabindex="0"
                ref="dropdownList"
              >
                <li   @click="(e) => LoadImage(e, approvedIcon, 1)">
                  <img
                    :src="iconsMap[1]"
                    width="30"
                    height="30"
                    alt=""
                  />
                </li>
                <li   @click="(e) => LoadImage(e, tickIcon, 2)">
                  <img
                    :src="iconsMap[2]"
                    width="30"
                    height="30"
                    alt=""
                  />
                </li>
                <li    @click="(e) => LoadImage(e, rejectedIcon, 3)">
                  <img
                    :src="iconsMap[3]"
                    width="30"
                    height="30"
                    alt=""
                  />
                </li>
                <li  @click="(e) => LoadImage(e, closed, 4)">
                  <img
                    :src="iconsMap[4]"
                    width="30"
                    height="30"
                    alt=""
                  />
                </li>
              </ul>
            </button>
          </div>
          <div class="tool">
            <button class="tool-button" @click="deleteSelectedObject">
              <img
                class="tool-button-icon" src="~@/assets/images/delete-icon.svg"
                title="Delete"
                width="18px"                alt=""
              />
            </button>
          </div>
          <div class="tool ">
      <div class="switch">
        <input id="switch-y" name="tripple" type="radio" value="Y" class="switch-input" checked  @change="showStatusBased(customList.todo)"/>
        <label for="switch-y" class="switch-label switch-label-y">All</label>
        <input id="switch-i" name="tripple" type="radio" value="I" class="switch-input" @change="showStatusBased(customList.progress)" />
        <label for="switch-i" class="switch-label switch-label-i">Progress</label>
        <input id="switch-n" name="tripple" type="radio" value="N" class="switch-input" @change="showStatusBased(customList.done)"/>
        <label for="switch-n" class="switch-label switch-label-n">Done</label>
        <span class="switch-selector"></span>
      </div>
          </div>
          <div class="tool ">
            <button class=" tool-button v-center h-center" @click="saveInBE">
              <img
                class="tool-button-icon mx-1"
                src="~@/assets/images/icons/save-icon.svg"
                title="Save"
                width="20px"
                alt=""
              />
                Save
            </button>
          </div>
        </div>
        <div class="flex-grow"></div>
        <div class="tool moreIcon" v-click-outside="closeOpenMoreIcon">
          <img
            className="moreIcon-icon"
            src="~@/assets/images/icons/more-icon.svg"
            alt=""
            @click="openMoreIcon"
          />
          <ul :class="{'moreIcon-list':true, 'moreIcon-list-expanded':moreIcon}"
                v-keyboard-selection
                tabindex="0"
                ref="moreIconList">
            <li
              class="v-center gap-1"
              v-if="!viewOnly"
              @click="[savePDF(), (moreIcon = false)]"
            >
              Download
              <img
                class="tool-button-icon"
                src="~@/assets/images/icons/cloud-download.svg"
                title="Free Hand"
                width="14px"
                alt=""
              />
            </li>
            <li v-if="doAnnotation" @click="turnOffAnnotation">Turn Off</li>
            <li v-else @click="turnOnAnnotation">Turn On</li>
            <li class="moreIcon-version" @mouseenter="versionMenu =true" @mouseleave="versionMenu=false">
              Import Annotation
              <div :class="` ${ versionMenu ?'moreIcon-version-menuActive': 'moreIcon-version-menu' }`">
              <ul>
              <li v-for="(revision, index) in revisionList" :key="revision.id" @click="loadAnnotationOfVersion(revision.id)"> Version {{index + 1}}  {{ revision.id=== fileId ? ' (current)': '' }}</li>
              </ul>
              </div>
            </li>
            <li @click="resetZoom">Reset zoom</li>
            <li v-if="!viewOnly2" @click="clearPage">Clear page</li>
            <li  v-if="!viewOnly2" @click="clearAllPages">Clear all pages</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { PDFAnnotate } from './fabric/pdfAnnotate'
import tickIcon from '../../assets/images/icons/annotation/tick.svg'
import approvedIcon from '../../assets/images/icons/annotation/approvedIcon.svg'
import rejectedIcon from '../../assets/images/icons/annotation/rejected.svg'
import closed from '../../assets/images/icons/annotation/closed.svg'
import {
  insertDocAnnotations,
  getAnnotationDataApi,
  generateS3DownloadingUrl,
  deleteAnnoatationElements,
  updateAnnotations,
  GetTaskStatuses
} from '@/api'
import Loader from '@/plugins/loader'
import {
  alert,
  success
} from '@/plugins/notification'
import progressDrag from '../common/progressDrag.vue'
import statusbox from './annotateStatusBox.vue'
import { mapGetters, mapMutations } from 'vuex'

export default {
  name: 'pdfviwer',
  props: {
    pdfUrl: {
      type: String,
      default: ''
    },
    fileId: String,
    viewOnly: {
      default: false,
      type: Boolean
    },
    state: {
      type: [String, Number],
      default: null
    },
    zoomValue: {
      type: Number,
      default: 1
    },
    revisionList: {
      type: Array,
      default: () => []
    }
  },
  components: { progressDrag, statusbox },
  data: function () {
    return {
      pdf: {},
      selectedTool: 'selector',
      approvedIcon,
      tickIcon,
      rejectedIcon,
      closed,
      iconList: false,
      moreIcon: false,
      doAnnotation: true,
      loader: new Loader(),
      iconsMap: {
        1: approvedIcon,
        2: tickIcon,
        3: rejectedIcon,
        4: closed
      },
      strokeWidth: 1,
      fontSize: 12,
      styleBox: false,
      versionMenu: false,
      activeAnnotationRevision: this.fileId,
      customList: {},
      document: window.document
    }
  },
  methods: {
    ...mapMutations('document', ['addIconsUrlMap']),
    async setup (fileId = null) {
      this.pdf = {}
      window.loader = this.loader
      window.loader.show()
      await this.loadiconsURLs()
      const annotationobj = await this.getAnnotationData(fileId ?? this.fileId)
      const { s3ImageObj } = annotationobj
      if (s3ImageObj.size > 0) {
        const S3Objects = Array.from(s3ImageObj.keys()).map((item, index) => {
          return {
            fileName: encodeURIComponent(index),
            S3Key: item
          }
        })
        const { url } = await generateS3DownloadingUrl({ S3Objects })
        url.forEach((obj) => {
          const position = s3ImageObj.get(obj.S3Key)
          annotationobj.imageObj[position.pageNo][position.index].src = obj.url
        })
      }
      this.loadDoc(annotationobj)
      // const icosIds = [1, 2, 3, 4]
    },
    loadDoc (annotationobj) {
      try {
        this.pdf = new PDFAnnotate(
          'pdf-container',
          this.pdfUrl,
          {
            onPageUpdated (page, annotationobj, newData) {},
            ready () {
              console.log('Plugin initialized successfully')
              window.loader.hide()
            },
            scale: 1.5,
            pageImageCompression: 'MEDIUM'
          },
          annotationobj,
          this.loader,
          this.viewOnly2
        )
      } catch (error) {
        window.loader.hide()
      }
    },
    openMoreIcon () {
      this.moreIcon = !this.moreIcon
      if (this.moreIcon) { this.$refs.moreIconList.focus() }
    },
    changeActiveTool (tool) {
      this.selectedTool = tool
    },
    enableSelector (event) {
      event.preventDefault()
      this.pdf.enableSelector()
    },

    enablePencil (event) {
      event.preventDefault()
      this.pdf.enablePencil()
    },

    enableAddText (event) {
      event.preventDefault()
      this.pdf.enableAddText()
    },

    enableAddArrow (event) {
      event.preventDefault()
      this.pdf.enableAddArrow(() => {
        this.selectedTool = 'selector'
      })
    },

    addImage (event, path) {
      this.pdf.addImageToCanvas()
    },
    LoadImage (event, path, id) {
      event.preventDefault()
      if (Object.hasOwn(this.getIconsUrlMap, id)) {
        this.pdf.addImageToCanvas(
          URL.createObjectURL(this.getIconsUrlMap[id]),
          id
        )
      } else {
        fetch(path)
          .then((response) => response.blob())
          .then((blob) => {
            const imageObjectUrl = URL.createObjectURL(blob)
            this.pdf.addImageToCanvas(imageObjectUrl, id)
          })
      }
    },
    blobMakerForIcons (path) {
      return new Promise((resolve, reject) => {
        fetch(path)
          .then((res) => res.blob())
          .then((blob) => resolve(blob))
      })
    },

    enableRectangle (event) {
      event.preventDefault()
      this.pdf.enableRectangle()
    },
    deleteSelectedObject (event) {
      this.pdf.deleteSelectedObject()
      event.preventDefault()
    },
    savePDF () {
      this.pdf.savePdf(`${this.fileId}`.pdf) // save with given file name
    },

    clearPage () {
      this.pdf.clearActivePage()
    },
    selectBrush (event) {
      const width = event.target.value
      this.pdf.setBrushSize(width)
    },
    selectBrushColor (color) {
      this.pdf.setColor(color)
    },
    setFontSize (event) {
      this.pdf.setFontSize(event.target.value)
    },
    saveInBE (event) {
      this.pdf.saveInBE(this.insertAnnotation, this.fileId, this.customList)
    },
    async insertAnnotation (updateObjs, newObjects, deletedIds) {
      this.loader.show()
      try {
        await insertDocAnnotations(newObjects)
        await updateAnnotations(updateObjs)
        if (deletedIds?.length > 0) {
          await deleteAnnoatationElements(deletedIds)
        }
        this.$emit('close')
        this.loader.hide()
        success('Annotaion saved successfully')
      } catch (error) {
        console.log(error)
        this.loader.hide()
        alert('Some thing went wrong, Please try again')
      }
    },
    getAnnotationData (fileId) {
      return new Promise((resolve, reject) => {
        getAnnotationDataApi(fileId).then((res) => {
          const annotationobj = {}
          const imageObj = {}
          const s3ImageObj = new Map()
          res.core_annotation_elements.forEach((obj) => {
            switch (obj.type) {
            case 1: {
              //      "type": "Textbox"
              if (!annotationobj[obj.page_no]) {
                annotationobj[obj.page_no] = []
              }
              annotationobj[obj.page_no].push({
                id: obj.id,
                angle: obj.angle,
                backgroundColor: obj.bg_color,
                charSpacing: obj.char_spacing,
                direction: 'ltr',
                fill: obj.fill,
                fillRule: obj.fill_rule,
                flipX: obj.flip_x,
                flipY: obj.flip_y,
                fontFamily: 'Times New Roman',
                fontSize: obj.font_size,
                fontStyle: 'normal',
                fontWeight: 'normal',
                globalCompositeOperation: 'source-over',
                height: obj.height,
                left: obj.left,
                lineHeight: obj.line_height,
                linethrough: false,
                minWidth: obj.min_width,
                opacity: obj.opacity,
                originX: obj.origin_x,
                originY: obj.origin_y,
                overline: false,
                paintFirst: obj.paint_first,
                path: null,
                pathAlign: obj.path_align,
                pathSide: obj.path_side,
                pathStartOffset: 0,
                scaleX: obj.scale_x,
                scaleY: obj.scale_y,
                shadow: null,
                skewX: obj.skew_x,
                skewY: obj.skew_y,
                splitByGrapheme: false,
                stroke: obj.stroke,
                strokeDashArray: null,
                strokeDashOffset: obj.stroke_dash_offset,
                strokeLineCap: obj.stroke_line_cap,
                strokeLineJoin: obj.stroke_line_join,
                strokeMiterLimit: obj.stroke_miter_limit,
                strokeUniform: obj.stroke_uniform,
                strokeWidth: obj.stroke_width,
                styles: [],
                text: obj.text,
                textAlign: obj.text_align,
                textBackgroundColor: '', // Assuming there's a corresponding backend field
                top: obj.top,
                type: 'textbox',
                underline: false, // Assuming there's a corresponding backend field
                version: '5.3.0',
                visible: true,
                width: obj.width,
                selectable: !this.viewOnly2,
                updatedBy: obj.updated_by_user,
                createdBy: obj.created_by_user,
                createdOn: obj.created_on,
                status: obj.status,
                docId: obj.document_id
              })

              break
            }
            case 2: {
              //  "type": "Rectangle"
              if (!annotationobj[obj.page_no]) {
                annotationobj[obj.page_no] = []
              }
              annotationobj[obj.page_no].push({
                id: obj.id,
                angle: obj.angle,
                backgroundColor: obj.bg_color,
                fill: obj.fill,
                fillRule: obj.fill_rule,
                flipX: obj.flip_x,
                flipY: obj.flip_y,
                globalCompositeOperation: 'source-over',
                height: obj.height,
                left: obj.left,
                opacity: obj.opacity,
                originX: obj.origin_x,
                originY: obj.origin_y,
                paintFirst: obj.paint_first,
                rx: obj.rx,
                ry: obj.ry,
                scaleX: obj.scale_x,
                scaleY: obj.scale_y,
                shadow: null,
                skewX: obj.skew_x,
                skewY: obj.skew_y,
                stroke: obj.stroke,
                strokeDashArray: null,
                strokeDashOffset: obj.stroke_dash_offset,
                strokeLineCap: obj.stroke_line_cap,
                strokeLineJoin: obj.stroke_line_join,
                strokeMiterLimit: obj.stroke_miter_limit,
                strokeUniform: obj.stroke_uniform,
                strokeWidth: obj.stroke_width,
                top: obj.top,
                type: 'rect',
                version: '5.3.0',
                visible: true,
                width: obj.width,
                selectable: !this.viewOnly2,
                updatedBy: obj.updated_by_user,
                createdBy: obj.created_by_user,
                createdOn: obj.created_on,
                status: obj.status,
                docId: obj.document_id
              })
              break
            }
            case 3: {
              //    "type": "LineArrow"
              if (!annotationobj[obj.page_no]) {
                annotationobj[obj.page_no] = []
              }
              annotationobj[obj.page_no].push({
                id: obj.id,
                angle: obj.angle,
                backgroundColor: obj.bg_color,
                fill: obj.fill,
                fillRule: obj.fill_rule,
                flipX: obj.flip_x,
                flipY: obj.flip_y,
                globalCompositeOperation: 'source-over',
                height: obj.height,
                left: obj.left,
                opacity: obj.opacity,
                originX: obj.origin_x,
                originY: obj.origin_y,
                paintFirst: obj.paint_first,
                rx: obj.rx,
                ry: obj.ry,
                scaleX: obj.scale_x,
                scaleY: obj.scale_y,
                shadow: null,
                skewX: obj.skew_x,
                skewY: obj.skew_y,
                stroke: obj.stroke,
                strokeDashArray: null,
                strokeDashOffset: obj.stroke_dash_offset,
                strokeLineCap: obj.stroke_line_cap,
                strokeLineJoin: obj.stroke_line_join,
                strokeMiterLimit: obj.stroke_miter_limit,
                strokeUniform: obj.stroke_uniform,
                strokeWidth: obj.stroke_width,
                top: obj.top,
                type: 'lineArrow',
                version: '5.3.0',
                visible: true,
                width: obj.width,
                x1: obj.x1,
                x2: obj.x2,
                y1: obj.y1,
                y2: obj.y2,
                selectable: !this.viewOnly2,
                updatedBy: obj.updated_by_user,
                createdBy: obj.created_by_user,
                createdOn: obj.created_on,
                status: obj.status,
                docId: obj.document_id
              })
              break
            }
            case 4: {
              // free hand
              if (!annotationobj[obj.page_no]) {
                annotationobj[obj.page_no] = []
              }
              // annotationobj[obj.page_no].push({
              //   globalCompositeOperation: 'source-over',
              //   shadow: null,
              //   strokeDashArray: null,
              //   version: '5.3.0'
              // })
              annotationobj[obj.page_no].push({
                id: obj.id,
                angle: obj.angle,
                backgroundColor: obj.bg_color,
                fill: obj.fill,
                fillRule: obj.fill_rule,
                flipX: obj.flip_x,
                flipY: obj.flip_y,
                globalCompositeOperation: 'source-over',
                height: obj.height,
                left: obj.left,
                opacity: obj.opacity,
                originX: obj.origin_x,
                originY: obj.origin_y,
                paintFirst: obj.paint_first,
                path: JSON.parse(obj.metadata),
                scaleX: obj.scale_x,
                scaleY: obj.scale_y,
                shadow: null,
                skewX: obj.skew_x,
                skewY: obj.skew_y,
                stroke: obj.stroke,
                strokeDashArray: null,
                strokeDashOffset: obj.stroke_dash_offset,
                strokeLineCap: obj.stroke_line_cap,
                strokeLineJoin: obj.stroke_line_join,
                strokeMiterLimit: obj.stroke_miter_limit,
                strokeUniform: obj.stroke_uniform,
                strokeWidth: obj.stroke_width,
                top: obj.top,
                type: 'path',
                version: '5.3.0',
                visible: true,
                width: obj.width,
                selectable: !this.viewOnly2,
                updatedBy: obj.updated_by_user,
                createdBy: obj.created_by_user,
                createdOn: obj.created_on,
                status: obj.status,
                docId: obj.document_id
              })
              break
            }
            case 5: {
              //  "type": "Image"
              let src = ''
              if (!imageObj[obj.page_no]) {
                imageObj[obj.page_no] = []
              }
              if (obj.blob_key) {
                s3ImageObj.set(obj.blob_key, {
                  blobKey: obj.blob_key,
                  pageNo: obj.page_no,
                  index: imageObj[obj.page_no].length
                })
              } else if (obj.metadata) {
                const blob = this.getIconsUrlMap[JSON.parse(obj.metadata)]
                if (!blob) {
                  break
                }
                src = URL.createObjectURL(blob)
              }
              imageObj[obj.page_no].push({
                id: obj.id,
                blobKey: obj.blob_key,
                angle: obj.angle,
                backgroundColor: obj.bg_color,
                cropX: obj.crop_x,
                cropY: obj.crop_y,
                crossOrigin: null,
                filters: [],
                fill: obj.fill,
                fillRule: obj.fill_rule,
                flipX: obj.flip_x,
                flipY: obj.flip_y,
                globalCompositeOperation: 'source-over',
                height: obj.height,
                left: obj.left,
                opacity: obj.opacity,
                originX: obj.origin_x,
                originY: obj.origin_y,
                paintFirst: obj.paint_first,
                scaleX: obj.scale_x,
                scaleY: obj.scale_y,
                shadow: null,
                skewX: obj.skew_x,
                skewY: obj.skew_y,
                src: src,
                stroke: obj.stroke,
                strokeDashArray: null,
                strokeDashOffset: obj.stroke_dash_offset,
                strokeLineCap: obj.stroke_line_cap,
                strokeLineJoin: obj.stroke_line_join,
                strokeMiterLimit: obj.stroke_miter_limit,
                strokeUniform: obj.stroke_uniform,
                strokeWidth: obj.stroke_width,
                top: obj.top,
                type: 'image',
                version: '5.3.0',
                visible: true,
                width: obj.width,
                assetId: JSON.parse(obj.metadata),
                pageNo: obj.page_no,
                selectable: !this.viewOnly2,
                updatedBy: obj.updated_by_user,
                createdBy: obj.created_by_user,
                createdOn: obj.created_on,
                status: obj.status,
                docId: obj.document_id
              })
              break
            }
            default:
            }
          })
          resolve({ annotationobj, imageObj, s3ImageObj })
        })
      })
    },
    turnOffAnnotation () {
      this.doAnnotation = false
      this.pdf.hideAllAnnotations()
      this.moreIcon = false
    },
    turnOnAnnotation () {
      this.doAnnotation = true
      this.pdf.showAllAnnotations()
      this.moreIcon = false
    },
    closeOpenMoreIcon () {
      this.moreIcon = false
    },
    closeiconList () {
      this.iconList = false
    },
    openIconList () {
      this.iconList = !this.iconList
      if (this.iconList) {
        this.$refs.dropdownList.focus()
      }
    },
    loadiconsURLs () {
      return new Promise((resolve, reject) => {
        if (Object.keys(this.getIconsUrlMap).length >= 1) {
          resolve()
        } else {
          Promise.all([
            this.blobMakerForIcons(approvedIcon),
            this.blobMakerForIcons(tickIcon),
            this.blobMakerForIcons(rejectedIcon),
            this.blobMakerForIcons(closed)
          ]).then((res) => {
            res.forEach((blob, index) => {
              const url = blob
              // basesd on given order , id is given from 1 to  4
              this.addIconsUrlMap({ url, id: index + 1 })
            })
            resolve()
          })
        }
      })
    },
    clearAllPages () {
      this.pdf.clearAllPages()
      this.closeOpenMoreIcon()
    },
    resetZoom () {
      this.pdf.resetZoom()
      this.closeOpenMoreIcon()
    },
    changeFontSize (value) {
      this.fontSize = value
      this.pdf.setFontSize(value)
    },
    changeStrokeWidth (value) {
      this.strokeWidth = value / 2 || 1
      this.pdf.setBrushSize(this.strokeWidth)
    },
    closeStyleBox () {
      this.styleBox = false
    },
    zoomIn () {
      this.pdf.zoomIn(this.zoomValue)
    },
    async loadAnnotationOfVersion (versionId) {
      this.versionMenu = false
      this.moreIcon = false
      this.activeAnnotationRevision = versionId
      await this.pdf.destroy('pdf-container')
      this.pdf = null
      this.setup(versionId)
    },
    getCustomListData () {
      GetTaskStatuses().then((res) => {
        this.customList = {}
        res.custom_list_values.forEach((list) => {
          if (list.name === 'DONE') {
            this.customList.done = list.id
          }
          if (list.name === 'IN PROGRESS') {
            this.customList.progress = list.id
          }
          if (list.name === 'TO DO') {
            this.customList.todo = list.id
          }
        })
      })
    },
    updateStatus (status) {
      this.pdf.updateStatus(status)
    },
    showStatusBased (status) {
      if (status === this.customList.todo) {
        this.pdf.showAllannoatations()
      } else {
        this.pdf.showannotationBasedOnStatus(status)
      }
    },
    handleKeydown (e) {
      if (e.keyCode === 46) { // Delete key
        this.deleteSelectedObject()
      }
    }
  },
  async mounted () {
    this.setup()
    this.getCustomListData()
  },
  watch: {
    docId () {
      this.pdf.docId = this.fileId
    },
    zoomValue () {
      this.zoomIn()
    }
  },
  computed: {
    ...mapGetters([
      'collaborator',
      'isOnProjectLevel',
      'isProjectViewer',
      'isTenantViewer'
    ]),
    ...mapGetters('document', ['getIconsUrlMap']),
    viewOnly2 () {
      if (this.state === 1 || this.state === 4 || this.fileId !== this.activeAnnotationRevision) {
        return true
      }
      if (this.isOnProjectLevel) {
        return this.view_only || this.isProjectViewer
      } else {
        return this.view_only || this.isTenantViewer
      }
    }
  },
  created () {
    this.document.addEventListener('keydown', this.handleKeydown)
  },
  destroyed () {
    this.pdf.removeAllEventListeners()
    document.removeEventListener('keydown', this.handleKeydown)
    window.loader = null
  }
}
</script>
<style scoped lang="scss">
.pdfviewContainer {
  height: 100%;
  position: relative;
  overflow:auto;
  &-statusbox {
position: fixed;
top:10px;
right:10px;
    width: 18rem;
    min-height: fit-content;
    z-index:7
  }
}

.canvas,
.canvas-container {
  -webkit-box-shadow: 10px 10px 5px 0px rgba(0, 0, 0, 0.75);
  -moz-box-shadow: 10px 10px 5px 0px rgba(0, 0, 0, 0.75);
  box-shadow: 10px 10px 5px 0px rgba(0, 0, 0, 0.75);
}

.toolbar {
  width: 100%;
  background-color: rgb(243, 244, 245);
  height: 50px;
  // position: fixed;
  top: 0;
  left: 0;
  z-index: 5;
  position: sticky;
  padding-inline: 10px;
}

#pdf-container {
  // margin-top:59.55px;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  background-color: rgb(0, 0, 0, 0.8);
  box-shadow: rgba(252, 251, 251, 0.16) 0px 3px 6px,
    rgba(0, 0, 0, 0.23) 0px 3px 6px;
  padding:5px;
}

button:focus {
  outline: 0;
}

.toolbar .tool {
  color: black;
  padding-left: 10px;
}

.toolbar .tool select,
.toolbar .tool input {
  width: auto;
  height: auto !important;
  padding: 0;
}

.toolbar .tool input {
  width: 50px;
}

.toolbar .tool .color-tool {
  height: 15px;
  width: 15px;
  border-radius: 25px;
  border: 0;
  cursor: pointer;
  display: inline-block;
}

.toolbar .tool .color-tool.active {
  -webkit-box-shadow: 3px 4px 5px 0px rgba(0, 0, 0, 0.75);
  -moz-box-shadow: 3px 4px 5px 0px rgba(0, 0, 0, 0.75);
  box-shadow: 3px 4px 5px 0px rgba(0, 0, 0, 0.75);
}

.toolbar .tool:nth-last-child(1) {
  float: right;
}

.toolbar .tool .tool-button {
  color: black;
  border: 1px solid #00000061;
  border-radius: 3px;
  cursor: pointer;
}

.toolbar .tool .tool-button:hover,
.toolbar .tool .tool-button.active {
  background-color:var(--brand-color);
  border-color: rgb(82, 86, 89);
}

.toolbar {
  display: flex;
  justify-content: center;
  align-items: center;
  &-active-color {
    transform: scale(1.2);
    background-color: rgb(82, 86, 89);
    border-color: rgb(82, 86, 89);
    box-shadow: 3px 4px 5px 0px rgba(0, 0, 0, 0.75);
  }
  &-box{
    flex-wrap: wrap;
  }
  &-color-tool {
    height: 15px;
    width: 15px;
    border-radius: 25px;
    border: 0;
    cursor: pointer;
    display: inline-block;
  }
  &-activetool {
    -webkit-box-shadow: 3px 4px 5px 0px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 3px 4px 5px 0px rgba(0, 0, 0, 0.2);
    box-shadow: 3px 4px 5px 0px rgba(0, 0, 0, 0.2);
    background-color:var(--brand-color);
  }
  & .tool-button {
    &-fixedimg {
      border: 1px solid rgb(82, 86, 89);
      color: black;
       border-radius: 3px;
      cursor: pointer;
      position: relative;
      &-list {
        position: absolute;
        top: calc(100% + 5px);
        left: 0;
        transform: translateX(-25%);
        width: 200%;
        background-color: white;
        border-radius: 3px;
        overflow-y: auto;
        & > li:hover {
          background-color: var(--brand-color);
          border-color: rgb(82, 86, 89);
        }
        & > li {
          padding: 5px 2px;
        }
      }
    }
    &-select{
      border-radius: 3px;
    padding: 2.5px;
    background-color: inherit;
    }
  }
  & .tool {
    &-brush-size {
      position: relative;
      & .tool-button {
        padding-inline: 5px;
      }
    }
    &-drag-box {
      position: absolute;
      top: 50px;
      padding: 10px;
      left: 0px;
      background: rgb(248, 247, 247);
      border-radius: 8px;
      border: 1px solid #0000003d;
      &-sroke-size {
        background-color: black;
        border-radius: 100%;
        width: 1px;
        height: 1px;
        right:20px;
      }
      &-font-size {
        right:20px;
        font-size: 10px;
        color: black;
      }
      &-container {
        width: 18rem;
      }
      p {
        text-align: left;
        width: 18rem;
      }
    }
  }
}
.openHeight {
  max-height: 300px;
  border: 1px solid black;
  transition: max-height 1s ease, border 1s ease;
  transition: border 1s ease;
}
.closedHeight {
  max-height: 0px;
  border: 0px solid;
  transition: max-height 0.4s ease, border 0.4s ease;
}
.moreIcon {
  position: relative;
  &-icon {
  }
  &-list {
    position: absolute;
    background-color: white;
    border-radius: 5px;
    right: 0;
    width: fit-content;
    text-wrap: nowrap;
    height: 0;
    overflow: hidden;
    border: 0;
    opacity: 0;
    transition: height  .6s ease-in-out;
    transition: opacity  .4s ease-in-out;
    & li {
      padding: 5px 8px;
      cursor: pointer;
      text-align: left;
    }
    & li:hover {
      background-color: var(--brand-color);
    }
    & > li:active {
      // transform: scale(0.95);
    }
    &-expanded{
      height: 11rem;
      overflow: visible;
      opacity: 1;
      border: 1px solid rgb(0, 0, 0, 0.2);
    }
  }
  &-version{
    position: relative;
& > div {
  transition:  width .8s ease-in-out;
}
    &-menu{
    // display: none;
    width: 0;
    position: absolute;
    opacity: 0;
    right: 100%;
    top: 0px;
    overflow: hidden;
    }
    &-menuActive{
      position: absolute;
      display: block;
      opacity: 1;
      background-color: white;
    width: auto;
    right: 100%;
    top: 0px;
    border: 1px solid rgb(0, 0, 0, 0.2);
    border-radius: 5px 0 5px 5px;
    -webkit-box-shadow: 3px 4px 5px 0px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 3px 4px 5px 0px rgba(0, 0, 0, 0.2);
    box-shadow: 3px 4px 5px 0px rgba(0, 0, 0, 0.2);
    }
  }
}
.disable-div {
  cursor: not-allowed;
  pointer-events: none !important;
  opacity: 0.3;
}
.canvas-container canvas {
  cursor: pointer;
}

.switch {
  position: relative;
  height: 20px;
  // width: 180px;
  // margin: 20px auto;
  border: 1px solid rgba(0, 0, 0, 0.3803921569);
  background:inherit;
  border-radius: 20px;
  &-label {
  position: relative;
  z-index: 2;
  float: left;
  width:59.55px;
  // line-height: 32px;
  font-size: 12px;
  color: #676a6c;
  text-align: center;
  cursor: pointer;
}
&-input {
  display: none;
}
&-selector {
  position: absolute;
  z-index: 1;
  top: 0px;
  left: 0px;
  bottom:0px;
  display: block;
  width:59.55px;
  border-radius: 20px;
  background-color: #1ab394;
  transition: all 0.8s cubic-bezier(0.18, 0.89, 0.32, 1.28);
}
}

.switch-input:checked + .switch-label {
font-weight: 500;
  transition: 0.15s ease-out;
  transition-property: color, text-shadow;
}

.switch-input:checked + .switch-label-y ~ .switch-selector {
  transform: translateX(0%);
  background-color: #d4dddbd8;
}
.switch-input:checked + .switch-label-i ~ .switch-selector {
  transform: translateX(100%);
  background-color: #fcd927;
}
.switch-input:checked + .switch-label-n ~ .switch-selector {
  transform: translateX(200%);
  background-color: #5deb3d;
}
</style>
