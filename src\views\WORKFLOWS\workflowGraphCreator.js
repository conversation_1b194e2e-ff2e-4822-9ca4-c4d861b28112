export class WorkflowGraph {
  constructor () {
    this.nodes = new Map() // Stores all nodes
    this.adjacencyList = new Map() // Adjacency list for edges
  }

  addNode (nodeId) {
    if (!this.nodes.has(nodeId)) {
      this.nodes.set(nodeId, { id: nodeId })
      this.adjacencyList.set(nodeId, [])
    }
  }

  addEdge (sourceId, targetId) {
    if (!this.nodes.has(sourceId)) this.addNode(sourceId)
    if (!this.nodes.has(targetId)) this.addNode(targetId)
    this.adjacencyList.get(sourceId).push(targetId)
  }

  findStartingNodes () {
    const startingNodes = []
    const hasIncoming = new Set()

    // Mark all nodes that have incoming edges
    for (const [source, targets] of this.adjacencyList) {
      console.log(source)

      for (const target of targets) {
        hasIncoming.add(target)
      }
    }

    // Nodes not in hasIncoming are starting nodes
    for (const nodeId of this.nodes.keys()) {
      if (!hasIncoming.has(nodeId)) {
        startingNodes.push(nodeId)
      }
    }

    return startingNodes
  }

  findEndingNodes () {
    const endingNodes = []

    // Nodes with no outgoing edges
    for (const [nodeId, edges] of this.adjacencyList) {
      if (edges.length === 0) {
        endingNodes.push(nodeId)
      }
    }

    // Also include nodes not present in adjacency list at all
    for (const nodeId of this.nodes.keys()) {
      if (!this.adjacencyList.has(nodeId)) {
        endingNodes.push(nodeId)
      }
    }

    return [...new Set(endingNodes)] // Remove duplicates
  }

  // Alternative implementation using in-degree/out-degree calculation
  calculateNodeDegrees () {
    const inDegree = new Map()
    const outDegree = new Map()

    // Initialize all degrees to 0
    for (const nodeId of this.nodes.keys()) {
      inDegree.set(nodeId, 0)
      outDegree.set(nodeId, 0)
    }

    // Calculate degrees
    for (const [source, targets] of this.adjacencyList) {
      outDegree.set(source, targets.length)
      for (const target of targets) {
        inDegree.set(target, inDegree.get(target) + 1)
      }
    }

    return { inDegree, outDegree }
  }

  findStartAndEndNodes () {
    const { inDegree, outDegree } = this.calculateNodeDegrees()

    const startingNodes = []
    const endingNodes = []

    for (const [nodeId, degree] of inDegree) {
      if (degree === 0) startingNodes.push(nodeId)
    }

    for (const [nodeId, degree] of outDegree) {
      if (degree === 0) endingNodes.push(nodeId)
    }

    return {
      startingNodes,
      endingNodes
    }
  }
}
