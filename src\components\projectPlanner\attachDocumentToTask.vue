<template>
    <div class="container">
        <div class="grid-1 mt-2">
          <div class="document-attach">
            <div v-if="attachedDocs.length > 0 " class="weight-500 document-attach-view imp " ><span v-for="file in attachedDocs" :key="file.id">
              <span v-show="file.flag!=='deleted'" class="document-attach-list" v-overflow-tooltip>  {{ file.core_document.doc_name }}</span></span>
            </div>
            <span v-tooltip="' Link Documents '" class="document-attach-edit"  @click="documentSelectDialog = true">
                <img src="~@/assets/images/icons/file-icon.svg" width="10px" height="10px" alt="" >
            </span>
      </div>
      </div>
      <div class="gap-1 flex-end document-attach-btn">
          <button
            class="btn btn-black"
            @click="$emit('close')"
            type="button"
          >
            CANCEL
          </button>
          <button
            class="btn"
            type="button"
            @click="saveTaskDocument"
          >
            SAVE
          </button>
        </div>
        <modal
        :open="documentSelectDialog"
    @close="documentSelectDialog = false"
    :closeOnOutsideClick="true"
    title=""
      >
      <document-selector @close="documentSelectDialog = false" :restrictUpload="true" @files-selected="handleFileSelection" v-if="documentSelectDialog"  :linkedDocs="attachedDocs" :uploadDocButton="uploadDocButton"/>
    </modal>
    </div>
  </template>
<script>
import { attchTaskDocs, GetAllTaskDocs, removeTaskDocuments } from '@/api'
import Modal from '../common/modal.vue'
import DocumentSelector from '../document/documentSelectorDialog.vue'
import { success } from '../../plugins/notification'

export default {
  components: { DocumentSelector, Modal },
  props: {
    task: {
      type: Object,
      default: () => ({})
    },
    selectedId: {
      type: String
    }
  },
  data () {
    return {
      documentSelectDialog: false,
      attachedDocs: [],
      uploadDocButton: true
    }
  },
  computed: {

  },
  mounted () {
    const task = JSON.parse(JSON.stringify(this.$props.task))
    if (!task.task_docs) {
      GetAllTaskDocs(task.id).then((res) => {
        if (res.task_document_association) {
          this.attachedDocs = res.task_document_association.map((element) => {
            return element
          })
        }
      })
    } else {
      this.attachedDocs = this.task.task_docs
    }
  },
  methods: {
    handleFileSelection (selectedFileData) {
      // the selectedfiles data is comming from modal (contains  all the datat related to selected docs)
      this.documentSelectDialog = false
      this.attachedDocs = selectedFileData
    },
    async saveTaskDocument () {
      const addnewAttachments = []
      this.attachedDocs.forEach((element) => {
        if (element.flag === 'new') {
          addnewAttachments.push({ task_id: this.selectedId, document_id: element.id })
        } else if (element.flag === 'deleted') {
          removeTaskDocuments(element.document_id, this.selectedId)
        }
      })
      addnewAttachments.length > 0 && await attchTaskDocs(addnewAttachments)
      // this is filtering the data that passed to material master table after updation
      // if any doc get delelted ,  here it  removes it
      success('Document has been updated successfully!')
      this.$emit('close')
      this.attachedDocs = this.attachedDocs.filter((element) => {
        if (element.flag) {
          if (element.flag !== 'deleted') {
            return element
          }
        } else {
          return element
        }
      })
      this.documentSelectDialog = false
    }
  }
}

</script>
<style lang="scss" scoped>
.container {
  padding: 17px 17px 17px 17px;
  width: 400px;
  // min-height: 200px;
  max-height: 400px;
  overflow-y: scroll;
}
.document-attach{
    display: flex;
    align-items: center;
    justify-content: start;
    flex-wrap: wrap;
    width: 100%;
    // top:15px;
    // height: auto;
    padding: 0.6rem;
    border-radius: 4px;
    // border: 2px solid rgba(var(--brand-rgb),0.4);
    cursor: pointer;
    &-view{
      display: flex;
      flex-wrap: wrap;
      gap: 14px;

    }
    &-edit{
      border: 2px solid rgba(41, 39, 39, 0.2);
      border-radius: 3px;
      padding: 6px 6px;
      width: 25px;
      display: flex;
      justify-content: center;
      // margin-left: 8px;
      margin-top: 8px;
    }
   &-btn{
    padding: 8px;
   }
    &-list{
      border: 2px solid rgba(var(--brand-rgb));
      border-radius: 3px;
      background-color: var(--brand-color);
      padding: 5px 5px 5px 5px ;
    width: 100px;
    text-overflow: ellipsis;
    overflow-y: auto;
    white-space: nowrap;
    cursor:context-menu;
    }
    span{
      cursor: pointer;
      user-select: none;
      background-color: transparent;
    }
  }
</style>
