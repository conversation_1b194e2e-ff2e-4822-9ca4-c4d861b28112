<template>
  <div class="item-compare-row">
    <div
      class="bom-detail-row bom-detail-body"
      :class="{
        'item-compare-row--add': diffItem.isAdded,
        'item-compare-row--remove': diffItem.isDeleted,
        'item-compare-row--update': diffItem.isUpdated,
      }"
    >
      <div>
        <span
          v-if="diffItem.updateItems?.associated_bom"
          v-tooltip="diffItem.updateItems?.associated_bom"
          class="updated-item"
        >
          {{ bomItem.material_product_code?.product_code || "--" }}
        </span>
        <span v-else>
          {{ bomItem.material_product_code?.product_code || "--" }}
        </span>
      </div>
      <div v-overflow-tooltip>{{ bomItem.custom_material_id || "--" }}</div>
      <div v-overflow-tooltip>{{ bomItem.plm_material_id || "--" }}</div>
      <div v-overflow-tooltip>{{ bomItem.erp_material_id || "--" }}</div>
      <div v-overflow-tooltip>{{ bomItem.material_unit_details.name || "--" }}</div>
      <div v-overflow-tooltip>
        {{ bomItem.material_description || "--" }}
      </div>
      <div>
        <span
          v-if="diffItem.updateItems?.unit_size"
          v-tooltip="diffItem.updateItems?.unit_size"
          class="updated-item"
        >
          {{ bomItem.unit_size || "--" }}
        </span>
        <span v-else>
          {{ bomItem.unit_size || "--" }}
        </span>
      </div>
      <div>
        <span
          v-if="diffItem.updateItems?.quantity"
          v-tooltip="diffItem.updateItems?.quantity"
          class="updated-item"
        >
          {{ bomItem.quantity || "--" }}
        </span>
        <span v-else>
          {{ bomItem.quantity || "--" }}
        </span>
      </div>
      <div v-overflow-tooltip>
          {{ bomItem.material_name || "--" }}
      </div>
      <div v-overflow-tooltip>{{ bomItem.lead_time || "--" }}</div>
      <div v-overflow-tooltip>
        <span v-if="bomItem.type === 1">
          {{ bomItem.material_group_details === null ? "--" : bomItem.material_group_details.name }}
        </span>
        <span v-else>{{ bomItem.resource_group_details === null ? "--" : bomItem.resource_group_details.name }}</span>
      </div>
      <div v-overflow-tooltip>{{ bomItem.unit_cost || "--"}}</div>
      <div v-overflow-tooltip>{{ bomItem.unit_sale_price || "--" }}</div>
      <div></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CompareSummery',
  props: {
    diffItem: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    bomItem () {
      if (this.diffItem.isDeleted) {
        return this.diffItem.left
      } else {
        return this.diffItem.right
      }
    }
  }
}
</script>

<style lang="scss" scoped >
.item-compare-row {
  &--add {
    background-color: #d2ffd6 !important;
  }
  &--remove {
    background-color: #fdd0d0 !important;
  }
  &--update {
    background-color: #fdfaea !important;
    & .updated-item {
      background-color: #f9e79f !important;
      padding: 4px;
      cursor: pointer;
    }
  }
}
</style>
