<template>
  <div class="progressbar">
    <div class="progressbar-percent">
      <svg>
        <circle cx="30" cy="30" r="28"></circle>
        <circle ref="progressCircle" cx="30" cy="30" r="28" :style="{ '--progressbar': (176 * percentage) / 100 +'px','--progresscolor':colorCode }"></circle>
      </svg>
      <div class="progressbar-number">
        <h3>{{value}}</h3>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProgressBar',
  props: {
    percentage: {
      type: Number,
      default: 0
    },
    value: {
      type: Number
    }
  },
  mounted () {
    this.animateProgress()
  },
  computed: {
    colorCode () {
      const colorNames = ['red', 'orangered', 'orange', 'gold', 'yellow', 'lime', 'limegreen', 'green', 'forestgreen', 'darkgreen']
      const index = Math.round(this.percentage / 10)
      return colorNames[index - 1]
    }
  },
  methods: {
    animateProgress () {
      const offset = 176 * (100 - this.percentage) / 100
      this.$refs.progressCircle.style.strokeDashoffset = offset + 'px'
    }
  },
  watch: {
    progress () {
      this.animateProgress()
    }
  }
}
</script>

<style lang="scss">
.progressbar {
  &-percent {
    position: relative;
  }
  & svg {
    position: relative;
    width: 65px;
    height: 65px;
    transform: rotate(-90deg);
    & circle {
      width: 100%;
      height: 100%;
      fill: none;
      stroke: #f0f0f0;
      stroke-width: 5;
      stroke-linecap: round;
      &:last-of-type {
        stroke-dasharray: 176px;
        stroke-dashoffset: var(--progress);
        stroke: #3498db;
        transition: stroke-dashoffset 1s ease; // Add transition for smooth animation
      }
    }
  }
  &-number {
    position: absolute;
    top: 50%;
    left: 47%;
    transform: translate(-50%, -50%);
    & h3 {
      font-weight: 200;
      font-size: 1.5rem;
      & span {
        font-size: 1rem;
      }
    }
  }
}
.progressbar:nth-child(1) svg circle:last-of-type {
  stroke: var(--progresscolor);
}
</style>
