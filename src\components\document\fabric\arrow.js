export const fabric = require('fabric').fabric

fabric.LineArrow = fabric.util.createClass(fabric.Line, {
  type: 'lineArrow',

  initialize (element, options = {}) {
    this.callSuper('initialize', element, options)
  },

  toObject () {
    return fabric.util.object.extend(this.callSuper('toObject'))
  },

  _render (ctx) {
    this.callSuper('_render', ctx)

    if (this.width === 0 || this.height === 0 || !this.visible) return

    ctx.save()

    const xDiff = this.x2 - this.x1
    const yDiff = this.y2 - this.y1
    const angle = Math.atan2(yDiff, xDiff)
    ctx.translate((this.x2 - this.x1) / 2, (this.y2 - this.y1) / 2)
    ctx.rotate(angle)
    ctx.beginPath()
    ctx.moveTo(10, 0)
    ctx.lineTo(-20, 15)
    ctx.lineTo(-20, -15)
    ctx.closePath()
    ctx.fillStyle = this.stroke
    ctx.fill()

    ctx.restore()
  }
})

fabric.LineArrow.fromObject = (object, callback) => {
  callback && callback(new fabric.LineArrow([object.x1, object.y1, object.x2, object.y2], object))
}

fabric.LineArrow.async = true

export class Arrow {
  constructor (canvas, color, activeTool, callback) {
    this.canvas = canvas
    this.className = 'Arrow'
    this.isDrawing = false
    this.color = color
    this.callback = callback
    this.active_tool = activeTool
    this.bindEvents()
  }

  bindEvents () {
    this.onMouseDown = this.onMouseDown.bind(this)
    this.onMouseMove = this.onMouseMove.bind(this)
    this.onMouseUp = this.onMouseUp.bind(this)
    this.onObjectMoving = this.onObjectMoving.bind(this)

    this.canvas.on('mouse:down', this.onMouseDown)
    this.canvas.on('mouse:move', this.onMouseMove)
    this.canvas.on('mouse:up', this.onMouseUp)
    this.canvas.on('object:moving', this.onObjectMoving)
  }

  unbindEvents () {
    this.canvas.off('mouse:down', this.onMouseDown)
    this.canvas.off('mouse:move', this.onMouseMove)
    this.canvas.off('mouse:up', this.onMouseUp)
    this.canvas.off('object:moving', this.onObjectMoving)
    this.isDrawing = false
  }

  onMouseDown (o) {
    if (this.active_tool !== 3) {
      this.disable()
      this.unbindEvents()
      return
    }
    this.enable()
    const pointer = this.canvas.getPointer(o.e)
    const points = [pointer.x, pointer.y, pointer.x, pointer.y]
    const line = new fabric.LineArrow(points, {
      strokeWidth: 5,
      fill: this.color || 'red',
      stroke: this.color || 'red',
      originX: 'center',
      originY: 'center',
      hasBorders: false,
      hasControls: true,
      selectable: true
    })

    this.canvas.add(line).setActiveObject(line)
  }

  onMouseMove (o) {
    if (!this.isEnable()) {
      return
    }

    const pointer = this.canvas.getPointer(o.e)
    const activeObj = this.canvas.getActiveObject()
    activeObj.set({
      x2: pointer.x,
      y2: pointer.y
    })
    activeObj.setCoords()
    this.canvas.renderAll()
  }

  onMouseUp (o) {
    this.disable()
    this.unbindEvents()
    this.active_tool = null
    if (this.callback) this.callback()
  }

  onObjectMoving (o) {
    this.disable()
  }

  isEnable () {
    return this.isDrawing
  }

  enable () {
    this.isDrawing = true
  }

  disable () {
    this.isDrawing = false
  }
}
