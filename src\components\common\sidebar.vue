<template>
      <div class="sidebar bg-brand column v-center">
        <!-- <div class="dashboard" :class="{'active-page':currentPath===''}">

          <img
          @click="$router.push('/')"
          width="40px"
          src="~@/assets/images/brand-logo.png"
          alt=""
          class="mt-5 pointer"
          />
        </div> -->
        <div v-if="!(isBeaconAdmin || tenantType === 2)" >
          <router-link
          to="/form"
          class="sidebar-btn column v-center "
          :class="{'active-page':currentPath==='form'}"
          >
            <img width="20px" src="~@/assets/images/form-icon.png" alt="" />
            <span class="">Forms</span>
          </router-link>
          <div v-if="showButtons">
            <router-link
            v-if="isOnProjectLevel"
            to="/project"
            class="sidebar-btn column v-center "
            :class="{'active-page':currentPath==='project'}"
          >
            <img width="20px" src="~@/assets/images/projects-icon.png" alt="" />
            <span class="">Project</span>
          </router-link>
          <router-link
            v-else
            to="/projects"
            class="sidebar-btn column v-center "
            :class="{'active-page':currentPath==='projects'}"
          >
            <img width="20px" src="~@/assets/images/projects-icon.png" alt="" />
            <span class="">Projects</span>
          </router-link>
          </div>
          <router-link
          @click="handleDocumentsLinkClick"
            to="/document-view"
            class="sidebar-btn column v-center "
            :class="{'active-page':currentPath==='document-view'}"
          >
            <img width="20px" src="~@/assets/images/documents-icon.png" alt="" />
            <span class="">Documents</span>
          </router-link>
         <router-link
            :to="`/materialmaster/${1}`"
            class="sidebar-btn column v-center "
            :class="{'active-page':currentPath==='materialmaster'}"
          >
            <img
              width="20px"
              src="~@/assets/images/material-master-icon.png"
              alt=""
            />
            <span class="">Item Master</span>
          </router-link>
          <router-link
            v-if="isOnProjectLevel"
            to="/bom/project"
            class="sidebar-btn column v-center "
            :class="{'active-page':currentPath==='bom'}"
          >
            <img width="20px" src="~@/assets/images/bom-icon.png" alt="" />
            <span class="">BOM</span>
          </router-link>
          <router-link
            v-else to="/bom/product"
            class="sidebar-btn column v-center"
            :class="{'active-page':currentPath==='bom'}"
            >
            <img width="20px" src="~@/assets/images/bom-icon.png" alt="" />
            <span class="">BOM</span>
          </router-link>
          <router-link
            to="/project-planner"
            class="sidebar-btn column v-center "
            :class="{'active-page':currentPath==='project-planner'}"
            >
            <img
              width="20px"
              src="~@/assets/images/project-planner-icon.png"
              alt=""
            />
            <span class="">Project Planner</span>
          </router-link>
          <router-link
              to="/timesheet/update"
              class="sidebar-btn column v-center "
              :class="{'active-page':currentPath==='timesheet'}"
            >
              <img width="20px" src="~@/assets/images/time-sheet.svg" alt="" />
              <span class="">Time sheet</span>
            </router-link>
            <router-link
              to="/trendsandforecast/material-forecast"
              class="sidebar-btn column v-center "
              v-if="showButtons"
              :class="{'active-page':currentPath==='trendsandforecast'}"
            >
              <img width="20px" src="~@/assets/images/icons/forecast.svg" alt="" />
              <span class="">Trends and Forecast</span>
            </router-link>
          <router-link
              to="/settings"
              class="sidebar-btn column v-center "
              v-if="showSettingButton"
              :class="{'active-page':currentPath==='settings'}"
            >
              <img width="20px" src="~@/assets/images/settings.svg" alt="" />
              <span class="">Settings</span>
            </router-link>
          <router-link
              :to='insightsEndpoint'
              class="sidebar-btn column v-center "
              :class="{'active-page':currentPath==='insights'}"
            >
              <img width="20px" src="~@/assets/images/icons/insights-icon.svg" alt="" />
              <span class="">Insights</span>
            </router-link>
            <router-link
              to="/beacon-dashboard"
              class="sidebar-btn column v-center "
              :class="{'active-page':currentPath==='beacon-dashboard'}"
              v-if="showBeaconDashboard && showButtons"
            >
              <img width="20px" src="~@/assets/images/icons/beacon_dasboard.svg" alt="" />
              <span class="">Beacon Dasboard</span>
            </router-link>
            <router-link
              to="/workflow-dashboard"
              class="sidebar-btn column v-center "
              :class="{'active-page':currentPath==='workflow-dashboard'}"
            >
              <img width="20px" src="~@/assets/images/workflow-dashboard.svg" alt="" />
              <span class="">Workflow Dasboard</span>
            </router-link>
        </div>
      </div>
  </template>

<script>
import { mapGetters } from 'vuex'
import config from '@/config'
export default {
  name: 'SidebarComponent',
  data () {
    return {
      currentPath: '',
      location: window.location.hostname

    }
  },
  computed: {
    ...mapGetters([
      'isBeaconAdmin',
      'isOnProjectLevel',
      'user',
      'collaborator',
      'tenantType',
      'openTenantId'
    ]),
    insightsEndpoint () {
      if (this.collaborator) {
        return '/insights/product/personal'
      } else if (this.isOnProjectLevel) {
        return '/insights/project'
      } else {
        return '/insights/product'
      }
    },
    showSettingButton () {
      return (
        (this.user.tenantLevelRole === 'ADMIN' || this.user.tenantLevelRole === 'EDITOR' ||
          this.user.tenantLevelRole === 'COLLABORATOR' || this.user.projectLevelRole === 'COLLABORATOR') && !this.collaborator
      )
    },
    showButtons () {
      return (
        !this.collaborator
      )
    },
    showBeaconDashboard () {
      if ((this.location === 'stg.beacon-dtx.com' && this.openTenantId === config.BEACON_DASHBOARD.STAGING_TENANT_ID) || this.location === 'dev.beacon-dtx.com' || this.location === 'localhost') {
        return true
      } else {
        return false
      }
    }
  },
  methods: {
    handleDocumentsLinkClick () {
      // Dispatch the action to fetch the project list
      this.$store.dispatch('fetchProjectList')
    }
  },
  created () {
    this.currentPath = this.$route.path.split('/')[1].toLowerCase()
  },
  watch: {
    '$route.name' () {
      this.currentPath = this.$route.path.split('/')[1].toLowerCase()
      if (this.collaborator) {
        if (this.currentPath === 'document-view') {
          this.$store.dispatch('fetchProjectList')
        } else if (this.currentPath === 'bom') {
          this.$store.dispatch('fetchProjectList')
        } else if (this.currentPath === 'form') {
          this.$store.dispatch('fetchProjectList')
        } else if (this.currentPath === 'insights') {
          this.$store.dispatch('fetchProjectList')
        }
      }
    },
    currentPath () {
    }
  }
}
</script>

  <style lang="scss" scoped>
  .active-page{
    background-color: rgba(var(--brand-rgb), 0.4);
  }
  .layout {
    .sidebar {
      height: calc(100vh - 60px);
      width: 80px;
      background-color: rgba(255, 240, 217, 0.6);
      overflow-y: auto;
      -ms-overflow-style: none;
      scrollbar-width: none;
      .dashboard{
        width: 100%;
        display: flex;
        justify-content: center;
        padding-bottom: 10px;
      }

      .dashboard:hover:not(.active-page){
        cursor: pointer;
        background-color: rgba(255, 240, 217, 1);
      }

      &-btn {
        font-size: 10px;
        font-weight: 500;
        padding: 10px 20px;
        color: var(--text-color);
        text-align: center;
        &:hover:not(.active-page){
          background-color: rgba(255, 240, 217, 1);
        }
        span {
          padding-top: 6px;
        }
      }
    }
    .sidebar::-webkit-scrollbar{
      display: none;
    }
  }
  </style>
