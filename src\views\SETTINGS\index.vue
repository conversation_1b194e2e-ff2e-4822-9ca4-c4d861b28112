<template>
  <div class="tenant-level-settings">
    <div class="tenant-bar v-center space-between px-3">
      <h1 class="weight-500 xxl">Tenant Level Settings</h1>
    </div>
    <div class="tenant-container">
      <div class="tenant-settings-bar">
        <tenant-settings-list/>
      </div>
      <div class="tenant-content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
import tenantSettingsList from '../../components/settings/tenantList.vue'
export default {
  name: 'Settings',
  data () {
    return {
      loading: false
    }
  },
  components: {
    tenantSettingsList
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.tenant {
  height: 100%;
  &-level-settings {
    height: 100%;
  }
  &-bar {
    height: 60px;
    margin: -12px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  &-container {
    height: calc(100% - 60px);
    overflow: auto;
    display: flex;
    padding-top: 10px;
    & > div {
      height: 100%;
    }
  }
  &-settings-bar{
    width: 200px;
  }
  &-settings-list{
    width: 100%;
  }
  &-content {
    width: calc(100% - 200px);
    padding-left: 10px;
  }
}
</style>
