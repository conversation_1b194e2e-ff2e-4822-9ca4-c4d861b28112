<template>
    <div class="storage-locations fh">
      <div class="storage-locations-bar v-center space-between px-3">
        <h3 class="weight-500 xl">Storage Locations</h3>
        <div class="v-center">
          <div class="input-group search m mx-3">
            <input
              v-model="searchKeyword"
              @input="updateOnSearch"
              type="text"
              placeholder="Search by name"
            />
          </div>
          <button class="btn pointer" @click="openCreateLocationModal" v-if="isTenantAdmin">
            + Add Storage Location
          </button>
        </div>
      </div>
      <div class="storage-locations-container">
        <div class="fh center" v-if="loading">
          <loading-circle />
        </div>
        <div
          class="storage-locations-no-form fh center"
          v-if="!loading && locations.length === 0"
        >
          No Storage Locations
        </div>
        <div class="copy-dtx-table" v-if="!loading && locations.length">
    <table >
        <thead>
      <tr class="m">
        <th>Name</th>
        <th>City</th>
        <th>State</th>
        <th>Address </th>
        <th>Created On</th>
        <th>Created By</th>
        <th v-if="isTenantAdmin">Action</th>
      </tr>
    </thead>
      <tbody>
        <tr v-for="(item, index) in locations" :key="item.id">
        <td class="elipsis-text " v-overflow-tooltip>{{ item.name }}</td>
        <td class="elipsis-text " v-overflow-tooltip>{{ item.city }}</td>
            <td >
                {{ item.state }}
           </td>
        <td class="elipsis-text " v-overflow-tooltip>{{ item.address_line_1 + ' ' + item.address_line_2 }}</td>
        <td class="elipsis-text " v-overflow-tooltip>{{ new Date(item.created_on).toLocaleDateString('en-US') }}</td>
        <td class="elipsis-text " v-overflow-tooltip>{{ item?.created_by_user?.first_name + ' ' + item?.created_by_user?.last_name }}</td>
       <td  v-if="isTenantAdmin" class="action-column">
        <div class="action-icons">
            <img @click="openEditModal(index)" v-tooltip="'Edit Storage Location'" class="pointer" src="~@/assets/images/pencil.svg" alt="" />
            <img @click="openDeleteModal(index)" width="16"  v-tooltip="'Delete Storage Location'" class="pointer" src="~@/assets/images/trash-2.svg" alt="" />
          </div>
             </td>
      </tr>
      </tbody>
    </table>
    <div class="pagination-footer space-between">
            <pagination2
       v-if="totalCount>10"
          :length="totalCount"
          :pageNumber="pageNumber"
          :perPage="perPage"
            @selectPage="selectPage"
          class="mt-3 mx-2"
        />
          <span class="pagination-footer-total mt-4"  v-if="locations.length > 0">Total Storage Location : &nbsp; <b> {{ totalCount }}</b></span>
        </div>
  </div>
      </div>
      <modal :open="openCreateModal"
       @close="closeCreateLocationModal"
        :closeOnOutsideClick="true"
        title="Create Storage Location"

        >
          <create-storage-location
   v-if="openCreateModal"
          :open="openCreateModal"
           @close= "closeCreateLocationModal"
           @save-and-close="saveAndClose"
           @update="getLocations"
          />
      </modal>
      <modal :open="editModalObject.open"
       @close="closeEditModal"
        :closeOnOutsideClick="true"
        title="Update Storage Location"
        >
          <create-storage-location
           v-if="editModalObject.selectedIndex !== -1"
             :open="editModalObject.open"
           :forEdit="true"
           :locationObj="locations[editModalObject.selectedIndex]"
           @close= "closeEditModal"
           @update-and-close="updateAndClose"
          />

      </modal>
    </div>
  </template>

<script>
import { getStorageLocations, updateStorageLocations } from '@/api'
import { mapGetters } from 'vuex'
import ConfirmationDialog from '@/plugins/confirmationDialog'
import loadingCircle from '../../components/common/loadingCircle.vue'
import modal from '../../components/common/modal.vue'
import CreateStorageLocation from '../../components/settings/StorageLocations/createStorageLocation.vue'
import pagination2 from '@/components/common/pagination2.vue'
import { debounce } from '@/utils/debounce'

export default {
  components: { loadingCircle, modal, CreateStorageLocation, pagination2 },
  name: 'storage-locations',
  data: () => ({
    locations: [],
    loading: false,
    openCreateModal: false,
    editModalObject: {
      selectedIndex: -1,
      open: false
    },
    deleteModalObject: {
      selectedIndex: -1,
      open: false
    },
    pageNumber: 1,
    perPage: 8,
    totalCount: 0,
    searchKeyword: '',
    updateOnSearch: null

  }),
  computed: {
    ...mapGetters(['user']),
    isTenantAdmin () {
      return this.user.tenantLevelRole === 'ADMIN'
    }
  },
  methods: {
    selectPage (page) {
      this.pageNumber = page
      this.getLocations()
    },
    openCreateLocationModal () {
      this.openCreateModal = true
    },
    closeCreateLocationModal () {
      this.openCreateModal = false
    },
    openEditModal (index) {
      this.editModalObject.selectedIndex = index
      this.editModalObject.open = true
    },
    closeEditModal () {
      this.editModalObject.selectedIndex = -1
      this.editModalObject.open = false
    },
    openDeleteModal (index) {
      ConfirmationDialog(
          `Are you sure you want to delete the storage location ${this.locations[index].name}?`,
          async (res) => {
            if (res) {
              try {
                this.loading = true
                await updateStorageLocations(this.locations[index].id, { deleted: true })
                this.$notify.success('Successfully deleted the storage location')
                this.locations.splice(index, 1)
                this.totalCount -= 1
              } catch (err) {
                this.$notify.alert(err?.message ?? 'Something went wrong')
              } finally {
                this.loading = false
              }
            }
          }
      )
    },
    async getLocations () {
      try {
        this.loading = true
        const res = await getStorageLocations((this.pageNumber - 1) * this.perPage, this.perPage, this.searchKeyword)
        this.locations = res.address_locations
        this.totalCount = res.address_locations_aggregate.aggregate.count
      } catch (err) {
        this.$notify.alert(err?.message ?? 'Something went wrong')
      } finally {
        this.loading = false
      }
    },
    saveAndClose (data) {
      this.locations.push(data)
      this.openCreateModal = false
    },
    updateAndClose (data) {
      const updateObj = {
        ...this.locations[this.editModalObject.selectedIndex],
        ...data
      }
      this.$set(this.locations, this.editModalObject.selectedIndex, updateObj)
      this.closeEditModal()
    }
  },
  created () {
    this.updateOnSearch = debounce(() => {
      this.pageNumber = 1
      this.getLocations()
    }, 500)
    this.getLocations()
  }
}
</script>

  <style lang="scss" scoped>
  .storage-locations {
    &-bar {
      height: 60px;
      padding: 16px;
      margin-top: -12px;
      background: var(--bg-color);
      border-bottom: var(--border);
    }
    &-container {
      height: calc(100% - 60px);
    }
    &-group {
      & > div {
        width: 100%;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
        & > div {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
      &-header {
        background-color: var(--brand-color);
        &-item {
          padding: 4px 10px;
          font-size: 14px;
          font-weight: 600;
          color: var(--text-color);
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
      &-items {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        max-height: 2rem;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        & > div {
          padding: 4px 10px;
          font-size: 14px;
          color: var(--text-color);
        }
      }
      &-item{
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        cursor: pointer;
      }
    }
  }
  </style>
