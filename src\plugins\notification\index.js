
import './notification.scss'
const GetParentAlert = function () {
  const container = document.getElementById('notificationContainer')
  if (container === null) {
    const parentDiv = document.createElement('div')
    parentDiv.setAttribute('id', 'notificationContainer')
    parentDiv.setAttribute('class', 'notificationContainer')
    document.body.appendChild(parentDiv)
    return parentDiv
  } else {
    return container
  }
}

const OpenNotification = function (config) {
  var parent = GetParentAlert()

  const notificationDiv = document.createElement('div')
  notificationDiv.setAttribute('class', 'notification')

  const notificationFlag = document.createElement('div')
  notificationFlag.className = `notification-flag notification-${config.type}`

  const title = document.createElement('div')
  title.setAttribute('class', 'notification-title')
  title.innerText = config.title || ''
  notificationFlag.append(title)

  const text = document.createElement('div')
  text.setAttribute('class', 'notification-text')
  text.innerHTML = config.text || ''
  notificationFlag.append(text)

  const cross = document.createElement('div')
  cross.setAttribute('class', 'notification-close')
  cross.innerHTML = '&#10005;'
  notificationFlag.append(cross)

  notificationDiv.append(notificationFlag)
  const timeout = config.timeout || 5000
  parent.append(notificationDiv)

  const closeNotification = function () {
    if (typeof config.callback === 'function') {
      config.callback()
    }
    notificationFlag.removeAttribute('class', 'notification-flag')
    notificationFlag.className = `notification-flag-out notification-${config.type}`
    setTimeout(function () { notificationDiv.remove() }, 900)
  }
  cross.addEventListener('click', closeNotification)
  setTimeout(closeNotification, timeout - 1000)
}

export const alert = (e) => {
  OpenNotification({
    type: 'danger',
    title: e
  })
}
export const success = (e) => {
  OpenNotification({
    type: 'success',
    title: e
  })
}
export const warning = (e) => {
  OpenNotification({
    type: 'warning',
    title: e
  })
}

export const notification = OpenNotification

export default {
  install (Vue) {
    Vue.prototype.$notify = {}
    Vue.prototype.$notify.notification = OpenNotification
    Vue.prototype.$notify.alert = alert
    Vue.prototype.$notify.success = success
    Vue.prototype.$notify.warning = warning
  }
}
