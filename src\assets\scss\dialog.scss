.dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: center;
    margin-bottom: 15px;
  }
  &-close {
    position: absolute;
    top: 15px;
    right: 12px;
    font-size: 40px;
    color: rgba(59, 59, 59, 0.4666666667);
    cursor: pointer;
    transition: opacity 0.2s;
  }
  &-close:hover {
    opacity: 0.6;
  }
  &-container {
    background: var(--bg-color);
    min-width: 40%;
    max-width:60%;
    padding: 30px 20px 20px 20px;
    filter: drop-shadow(0 0 2px var(--brand-color));
    border-radius: 8px;
  }
  &-select {
    margin: 0 0 0 10px;
    font-size: 12px;
    background-color: var(--brand-light-color);
    line-height: 1;
    border: 1px solid var(--brand-color);
    border-radius: 4px;
    padding: 4px 12px;
  }
  &-title {
    font-size: 18px;
    font-weight: 600;
  }
  &-message {
    font-size: 16px;
    margin-bottom: 20px;
  }
  &-buttons {
    display: flex;
    justify-content: flex-end;
    button {
      margin-left: 10px;
    }
  }
}