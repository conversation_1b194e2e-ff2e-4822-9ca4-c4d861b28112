<template>
  <div v-if="showKanban">
    <div id="toolbar">
    </div>
    <div id="kanban">
    </div>
    <div class="drawer" :class="filterDrawer ? 'open' : 'close'">
      <div class="filters">
           <div class="space-between">
             <h3 class="weight-500">Filters</h3>
             <span class="pointer underline" @click="clearFilters">Clear All</span>
           </div>
          <div class="my-5 form">
            <label>Task</label>
            <div class="input-group add-user__search">
              <input type="text" placeholder="Task name" v-model="taskNameFilter"/>
            </div>
            <label>Due Date</label>
            <div class="flex s mb-2">
           <button class="btn mr-2" @click="setDate('thisWeek')">This Week</button>
           <button class="btn mr-2" @click="setDate('nextWeek')">Next Week</button>
           <button class="btn" @click="setDate('next30')">Next 30 days</button>
            </div>
            <div class="flex mb-5">
             <input type="date" class="mr-2"  v-model="dateFilter.from" @change="[dateFilter.upto=dateFilter.from]">
             <input type="date"  v-model="dateFilter.upto" :min="dateFilter.from">
            </div>
            <div>
              <label class="key">Tag</label>
              <filterTag
                :tags="getTags"
                @new-tag="addNewTag"
                @update-tags="updateTags"
                :type="2"
                title="Add tags to filter"
              />
            </div>
              <label>
                Assignee
              </label>

              <div class="input-group add-user__search" ref="addUserBody">
                <div>
            <input
              type="text"
              v-model="searchKeyword"
              @click="openPopup = true"
              placeholder="Add assignees to filter"
            />
          </div>
          <div class="add-user__list" v-if="openPopup">
          <div class="add-user__list-no-result" v-if="!nonAddedUsers?.length">
            No users found
          </div>
          <div
            class="add-user__list-item"
            v-for="user in nonAddedUsers"
            :key="user.associated_user.id"
            tabindex="0"
            @click="updateSelectedUsers(user.associated_user)"
          >
            <div class="add-user__list-item__avatar">
              <avatar :user="user.associated_user" size="24px" />
            </div>
            <div class="add-user__list-item__name">
              {{ user.associated_user.first_name }}
              {{ user.associated_user.last_name }}
            </div>
            <div class="add-user__list-item__action"></div>
          </div>
        </div>
              </div>
              <div class="flex add-user__selected" v-if="assigneeFilter?.length">
            <div
              class="tags"
              v-for="(user, index) in assigneeFilter"
              :key="user.id"
            >
              <div class="add-user__list-item__avatar">
                <avatar :user="user" size="24px" />
              </div>
              <div class="add-user__list-item__name">
                {{ user.first_name }}
                {{ user.last_name }}
              </div>
              <img
                class="pointer ml-1"
                @click="removeAssignee(index)"
                src="~@/assets/images/icons/close-icon.svg"
                width="16px"
                alt=""
              />
            </div>
          </div>
          </div>
         <div>
         </div>
          <div class="flex-end s">
            <button class="btn btn-black-outline mx-2" @click="closeFilter">Cancel</button>
            <button class="btn" @click="confirmFilters">Apply</button>
          </div>
      </div>
    </div>
    <modal title="Edit task" :open = "editModalObject.open" @close = "closeEditForm" v-if="showEditDetails">
      <task-edit-form
      v-if="editModalObject.open"
      :task="editModalObject.selectedTask"
      @close="closeEditForm"
      @delete-task="handleDeleteTask"
      @update-and-close="updateAndCloseModal"
      @openTaskDocument="openDocs"
      view="board"
    />
    </modal>
    <modal
    v-else-if="showViewDetails"
    :open = "editModalObject.open"
      @close="closeEditForm"
      :closeOnOutsideClick="true"
      title="Task Details"
    >
    <task-view-form
    v-if="editModalObject.open"
    :task="editModalObject.selectedTask"
    view="board"/>
    </modal>
    <modal title="Add Assignee" v-if="!isExternalCollaborator" :open="addAssigneeModalObject.open" @close = "addAssigneeModalObject.open=false">
      <add-assignee-body
      v-if="addAssigneeModalObject.open"
      @close="addAssigneeModalObject.open=false"
      @update-and-close="updateAndCloseAddAssigneeModal"
      :task="addAssigneeModalObject.selectedTask"/>
    </modal>
    <modal title="Add Bom To Task" v-if="!isExternalCollaborator" :open="addBomToTaskObject.open" @close = "addBomToTaskObject.open=false" :scroll="false">
      <add-bom-to-task
      v-if="addBomToTaskObject.open"
      @close="addBomToTaskObject.open=false"
      @bom-add-and-close="updateAndCloseAddBomToTaskModal"
      @bom-update-and-close="updateAndCloseEditBomToTaskModal"
      @bom-delete-and-close="updateAndCloseDeleteBomToTaskModal"
      :task="addBomToTaskObject.selectedTask"/>
    </modal>
    <modal :title="showDependenciesObject.type" :open="showDependenciesObject.open" @close ="closeDependencyModal">
      <dependencies-list v-if="showDependenciesObject.selectedTask"
      :task="showDependenciesObject.selectedTask"
      :type="showDependenciesObject.type"/>
    </modal>
    <taskComment
      v-if="openCommentModal"
      @closeComment="closeCommentModal"
      :cardId="cardId"
      :closeOnOutsideClick="true"
    />
    <modal
    v-if="!isExternalCollaborator"
    :open="addDocumentToTaskObject.open"
    @close="addDocumentToTaskObject.open = false"
    :closeOnOutsideClick="true"
    title="Attach Document"
    >
<attach-document-to-task @close="addDocumentToTaskObject.open = false"  v-if="addDocumentToTaskObject.open" :task="addDocumentToTaskObject.selectedTask"  :selectedId="addDocumentToTaskObject.selectedId"/>
  </modal>
  <modal title="Parent Task" :open="tasksMap.open" @close ="tasksMap.open = false">
      <parent-task-list v-if="tasksMap.selectedTasksMap"
      :tasks="tasksMap.selectedTasksMap"/>
    </modal>
    <view-attached-docs
    v-if="openDocView"
    :fileDetails="docsforview"
    @close="closeDocs"
  ></view-attached-docs>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import { kanban } from '../../assets/kanban/kanban'
import { getData } from '../../assets/kanban/kanbanConfig'
import AddBomToTask from './addBomToTask.vue'
import avatar from '../common/avatar.vue'
import viewAttachedDocs from './viewAttachedDocs.vue'
import {
  UpdateTask,
  GetTaskStatuses,
  deleteAssignees,
  addNewAssignees,
  AddTaskTags,
  DeleteTaskTags,
  AddBomToTaskMutation,
  DeleteBomTaskMutation,
  UpdateBomTaskMutation,
  attchTaskDocs,
  removeTaskDocuments,
  deleteTasks
} from '@/api'
import Loader from '@/plugins/loader'
import { alert, success } from '../../plugins/notification'
import Modal from '../common/modal.vue'
import TaskEditForm from './taskEditForm.vue'
import { KanbanTree } from '@/utils/kanbanHelper'
import filterTag from '../../components/common/filterTag.vue'
import taskComment from './taskComment.vue'
import AddAssigneeBody from './addAssignee.vue'
import dependenciesList from './dependenciesList.vue'
import { addTaskComment, addTaskComments } from '../../api/apis/comments'
import TaskViewForm from './taskViewForm.vue'
import AttachDocumentToTask from './attachDocumentToTask.vue'
import ParentTaskList from './parentTaskList.vue'
const { rows, cardShape, filter, cardTemplate } = getData()
const { Kanban, Toolbar, template } = kanban

let board

export default {
  props: {
    tasks: {
      type: Object,
      default () {
        return { data: [], links: [] }
      }
    },
    filters: {
      type: Object,
      default () {
        return {
          tags: [],
          from: '',
          upto: ''
        }
      }
    }
  },
  components: { avatar, Modal, TaskEditForm, filterTag, taskComment, AddAssigneeBody, AddBomToTask, dependenciesList, TaskViewForm, AttachDocumentToTask, ParentTaskList, viewAttachedDocs },
  data () {
    return {
      task_id: null,
      showKanban: true,
      kanbanTree: new KanbanTree(),
      elementsWithCustomEventListeners: [],
      cards: [],
      taskStatuses: [],
      filterDrawer: false,
      searchKeyword: '',
      showDependenciesObject: {
        open: false,
        selectedTask: null,
        type: null
      },
      openPopup: false,
      dateFilter: {
        from: '',
        upto: ''
      },
      addAssigneeModal: false,
      editModalObject: {
        open: false,
        selectedId: -1,
        selectedTask: null
      },
      addAssigneeModalObject: {
        open: false,
        selectedId: -1,
        selectedTask: null
      },
      addBomToTaskObject: {
        open: false,
        selectedId: -1,
        selectedTask: null
      },
      addDocumentToTaskObject: {
        open: false,
        selectedId: -1,
        selectedTask: null
      },
      tasksMap: {
        open: false,
        selectedTasksMapId: {},
        selectedTasksMap: null
      },
      tagsFilter: [],
      assigneeFilter: [],
      taskNameFilter: '',
      openCommentModal: false,
      cardId: '',
      moveCards: false,
      calendarLists: [],
      calendarHolidays: '',
      calendarWorkHours: '',
      workingHours: '',
      projectId: null,
      reWork: false,
      isViewer: false,
      openDocView: false,
      docsforview: []
    }
  },
  methods: {
    closeDocs () {
      this.openDocView = false
      this.docsforview = []
    },
    openDocs (docs) {
      this.openDocView = true
      this.docsforview.push(docs.core_document)
    },
    handleDeleteTask (id) {
      deleteTasks([id]).then(() => {
        board.deleteCard({ id })
        this.closeEditForm()
      }).catch(() => {
        alert('Failed to delete Task')
      })
    },
    closeEditForm () {
      this.editModalObject.open = false
      this.$router.replace({ query: { view: 'board' } })
      this.destroyEventListeners()
      this.initEventListeners()
    },
    closeDependencyModal () {
      this.showDependenciesObject = {
        open: false,
        selectedTask: null,
        type: null
      }
    },
    openEditModal (id, card) {
      const task = JSON.parse(JSON.stringify(card))
      task.text = task.label
      this.editModalObject.selectedId = id
      this.editModalObject.selectedTask = task
      this.editModalObject.open = true
      this.$router.replace({ query: { view: 'board', selectedTaskId: id } })
    },
    closeEditModal () {
      this.editModalObject.open = false
      this.editModalObject.selectedId = -1
      this.editModalObject.selectedTask = null
    },
    async updateAndCloseAddAssigneeModal (taskData) {
      const loader = new Loader()
      try {
        loader.show()
        let promiseArr = []
        const id = this.addAssigneeModalObject.selectedId
        const taskAssignees = []
        const newAssignees = []
        const deletedAssignees = []
        const newTaskComments = []
        taskData.task_assignees.forEach((item) => {
          if (item.status !== 'deleted') {
            taskAssignees.push({
              first_name: item.first_name,
              last_name: item.last_name,
              id: item.id
            })
          }
        })
        taskData.task_assignees.forEach(element => {
          if (element?.status === 'new') {
            const assigneeObj = { task_id: id, user_id: element.id }
            if (element.tenantId) {
              assigneeObj.target_tenant_id = element.tenantId
            }
            newAssignees.push(assigneeObj)
          } else if (element?.status === 'deleted') {
            deletedAssignees.push({ user_id: { _eq: element.id }, task_id: { _eq: id } })
          }
        })
        if (newAssignees?.length) {
          promiseArr = [...promiseArr, addNewAssignees(newAssignees)]
        }
        if (deletedAssignees?.length) {
          promiseArr = [...promiseArr, deleteAssignees({ _or: deletedAssignees })]
        }
        newAssignees?.length && newTaskComments.push({
          comment: `${newAssignees.length} Task assignees have been updated`,
          parent_id: null,
          task_id: id
        })
        // Add comment for deleted assignees
        deletedAssignees?.length && newTaskComments.push({
          comment: `${deletedAssignees.length} Task assignees have been removed`,
          parent_id: null,
          task_id: id
        })
        await addTaskComments(newTaskComments)
        Promise.all(promiseArr).then(() => {
          board.updateCard({
            id,
            card: {
              task_assignees: taskAssignees
            }
          })
          success('Task Assignee updated successfully')
        }).catch((err) => {
          console.log(err)
          alert('Unable to update task assignees')
        }).finally(() => {
          this.addAssigneeModalObject.open = false
          this.destroyEventListeners()
          this.initEventListeners()
        })
      } catch (err) {
        console.log(err)
        alert('Something went wrong')
      } finally {
        loader.hide()
      }
    },
    updateAndCloseAddBomToTaskModal (data) {
      AddBomToTaskMutation(data.selectedBom[0].id, data.taskId, data.selectedBom[0].quantity).then((res) => {
        if (res.insert_task_material_association_one) {
          success('Bom added to Task successfully')
          const task = board.getCard(data.taskId)
          task.attached_bom = {
            associationId: res?.insert_task_material_association_one?.id,
            id: data.selectedBom[0].id,
            name: data.selectedBom[0].name,
            quantity: data.selectedBom[0].quantity
          }
          board.updateCard(task)
          this.addBomToTaskObject.open = false
        }
      }).catch(err => {
        if (err.message.includes('Uniqueness violation')) {
          alert('This Bom is already associated to this task')
        }
      })
    },
    updateAndCloseDeleteBomToTaskModal (data) {
      DeleteBomTaskMutation(data.associationId).then((res) => {
        if (res) {
          success('The Bom has been deleted from the task')
          const task = board.getCard(data.taskId)
          task.attached_bom = null
          board.updateCard(task)
          this.addBomToTaskObject.open = false
        }
      }).catch(() => {
        alert('Unable to delete bom association')
      })
    },
    async updateAndCloseEditBomToTaskModal (data) {
      try {
        let updatedAttachedBom = null
        if (data.updatesArray) {
          for (const item of data.updatesArray) {
            if (item.tag === 'deleted') {
              await DeleteBomTaskMutation(item.associationId)
              updatedAttachedBom = 'deleted'
            } else if (item.tag === 'new') {
              const res = await AddBomToTaskMutation(item.id, this.addBomToTaskObject.selectedId, item.quantity)
              updatedAttachedBom = {
                associationId: res?.insert_task_material_association_one?.id,
                id: item.id,
                name: item.name,
                quantity: item.quantity
              }
            } else if (item.tag === 'updated') {
              await UpdateBomTaskMutation(item.associationId, item.quantity)
              updatedAttachedBom = {
                associationId: item.associationId,
                id: item.id,
                name: item.name,
                quantity: item.quantity
              }
            }
          }
          const updationObject = {}
          if (updatedAttachedBom) {
            if (updatedAttachedBom === 'deleted') {
              updationObject.attached_bom = null
            } else {
              updationObject.attached_bom = updatedAttachedBom
            }
          }
          board.updateCard({
            id: this.addBomToTaskObject.selectedId,
            card: updationObject
          })
        }
        success('Bom updated to Task successfully')
      } catch (err) {
        console.log(err)
        alert('Something went wrong')
      } finally {
        this.addBomToTaskObject.open = false
      }
    },
    async updateAndCloseModal (taskData, taskDocs) {
      const actualDates = new Date()
      const loader = new Loader()
      try {
        loader.show()
        const id = this.editModalObject.selectedId
        const taskAssignees = []
        taskData.task_assignees.forEach((item) => {
          if (item.status !== 'deleted') {
            taskAssignees.push({
              first_name: item.first_name,
              last_name: item.last_name,
              id: item.id
            })
          }
        })
        const updateData = {
          planned_end_date: taskData?.end_date,
          planned_start_date: taskData?.start_date,
          name: taskData?.name,
          type: taskData?.type,
          description: taskData?.description,
          duration: taskData?.duration,
          progress: Math.round(taskData?.progress)
        }
        if (!taskData.actualStartDate && taskData.progress > 0) {
          updateData.actual_start_date = actualDates
        }
        if (taskData.progress === 100) {
          updateData.actual_end_date = actualDates
        }
        // const taskTags = []
        let promiseArr = []
        const newAssignees = []
        const deletedAssignees = []
        const newTaskComments = []

        // updating attached boms
        let updatedAttachedBom = null
        if (taskData.attachedBom) {
          for (const item of taskData.attachedBom) {
            if (item.tag === 'deleted') {
              await DeleteBomTaskMutation(item.associationId)
              updatedAttachedBom = 'deleted'
            } else if (item.tag === 'new') {
              const res = await AddBomToTaskMutation(item.id, id, item.quantity)
              updatedAttachedBom = {
                associationId: res?.insert_task_material_association_one?.id,
                id: item.id,
                name: item.name,
                quantity: item.quantity
              }
            } else if (item.tag === 'updated') {
              await UpdateBomTaskMutation(item.associationId, item.quantity)
              updatedAttachedBom = {
                associationId: item.associationId,
                id: item.id,
                name: item.name,
                quantity: item.quantity
              }
            }
          }
        }
        taskData.task_assignees.forEach(element => {
          if (element?.status === 'new') {
            const assigneeObj = { task_id: id, user_id: element.id }
            if (element.tenantId) {
              assigneeObj.target_tenant_id = element.tenantId
            }
            newAssignees.push(assigneeObj)
          } else if (element?.status === 'deleted') {
            deletedAssignees.push({ user_id: { _eq: element.id }, task_id: { _eq: id } })
          }
        })
        if (newAssignees?.length) {
          promiseArr = [...promiseArr, addNewAssignees(newAssignees)]
        }
        if (deletedAssignees?.length) {
          promiseArr = [...promiseArr, deleteAssignees({ _or: deletedAssignees })]
        }
        await Promise.all(promiseArr)
        if (newAssignees?.length > 0) {
          const taskString = `${newAssignees?.length} Task assignees has been updated`
          newTaskComments.push({
            comment: taskString,
            parent_id: null,
            task_id: id
          })
        }
        if (deletedAssignees?.length > 0) {
          const taskString = `${deletedAssignees?.length} Task assignees has been removed`
          newTaskComments.push({
            comment: taskString,
            parent_id: null,
            task_id: id
          })
        }
        await UpdateTask(id, updateData)
        const newTasks = this.editModalObject.selectedTask
        if (newTasks.progress !== updateData.progress) {
          this.moveCards = true
          const isSwimlaneChanged = this.editModalObject.selectedTask.column !== taskData.status
          this.handleSwimlaneChange(id, taskData.status, board, updateData.progress, isSwimlaneChanged)
        }
        if (taskData.tags) {
          const taskTags = []
          for (const tagArray of taskData.tags) {
            for (const tag of tagArray) {
              taskTags.push({
                tag_id: tag.id,
                task_id: id
              })
            }
          }
          // here we are deleteing all tags associated with  given material is deleted and then new tags will attached

          await DeleteTaskTags(id) // here the id is the id of task tags
          taskTags.length > 0 && await AddTaskTags(taskTags)
        }
        this.attachTaskDocument(taskDocs, id)
        let color
        if (new Date().toISOString() > updateData?.planned_end_date && updateData.progress !== 100) {
          color = '#FF0000'
        } else {
          color = '#FFC975'
        }
        const updationObject = {
          label: updateData?.name,
          planned_start_date: updateData?.planned_start_date,
          planned_end_date: updateData?.planned_end_date,
          description: updateData?.description,
          progress: Math.round(updateData?.progress),
          task_assignees: taskAssignees,
          duration: updateData?.duration,
          color,
          tag_tasks: taskData.tags.flat(1),
          start_date: taskData?.start_date,
          end_date: taskData?.end_date
        }
        if (updatedAttachedBom) {
          if (updatedAttachedBom === 'deleted') {
            updationObject.attached_bom = null
          } else {
            updationObject.attached_bom = updatedAttachedBom
          }
        }
        board.updateCard({
          id,
          card: updationObject
        })
        this.destroyEventListeners()
        this.initEventListeners()
        if (newTasks.label !== updateData.name || newTasks.description !== updateData.description) {
          const newString = 'Task details were modified'
          newTaskComments.push({
            comment: newString,
            parent_id: null,
            task_id: id
          })
        }
        const plannedStartDate = new Date(newTasks.planned_start_date).toISOString().substr(0, 10)
        const plannedEndDate = new Date(newTasks.planned_end_date).toISOString().substr(0, 10)
        if (plannedStartDate !== updateData.planned_start_date || plannedEndDate !== updateData.planned_end_date || newTasks.duration !== updateData.duration) {
          const dateString = 'Task dates/schedule were modified'
          newTaskComments.push({
            comment: dateString,
            parent_id: null,
            task_id: id
          })
        }
        await addTaskComments(newTaskComments)
        this.closeEditForm()
        success('Task updated successfully')
      } catch (err) {
        alert('Something went wrong')
      } finally {
        loader.hide()
      }
    },
    handleOutsideClick (e) {
      if (!this?.$refs?.addUserBody?.contains(e.target)) {
        this.openPopup = false
      }
    },
    confirmFilters () {
      this.tagsFilter = this.getTags
      this.$emit('addFilter', this.dateFilter, this.assigneeFilter, this.tagsFilter, this.taskNameFilter)
      this.filterDrawer = false
    },
    clearFilters () {
      this.assigneeFilter = []
      this.dateFilter.from = ''
      this.dateFilter.upto = ''
      this.tagsFilter = []
      this.taskNameFilter = ''
      this.$emit('addFilter', this.dateFilter, this.assigneeFilter, this.tagsFilter, this.taskNameFilter)
    },
    setDate (dur) {
      const today = new Date()
      const currentDay = today.getDay()
      if (dur === 'thisWeek') {
        const startOfWeek = new Date(today)
        startOfWeek.setDate(today.getDate() - currentDay) // Set start date to the beginning of the week
        const endOfWeek = new Date(startOfWeek)
        endOfWeek.setDate(startOfWeek.getDate() + 7) // Set end date to the beginning of the next week
        this.dateFilter.from = startOfWeek.toLocaleDateString('en-CA')
        this.dateFilter.upto = endOfWeek.toLocaleDateString('en-CA')
      } else if (dur === 'nextWeek') {
        const startOfNextWeek = new Date(today) // Clone today's date object
        startOfNextWeek.setDate(today.getDate() + (7 - currentDay)) // Set start date to the beginning of the next week
        const endOfNextWeek = new Date(startOfNextWeek)
        endOfNextWeek.setDate(startOfNextWeek.getDate() + 7) // Set end date to the beginning of the week after the next
        this.dateFilter.from = startOfNextWeek.toLocaleDateString('en-CA')
        this.dateFilter.upto = endOfNextWeek.toLocaleDateString('en-CA')
      } else if (dur === 'next30') {
        const startOfNext30Days = new Date(today) // Clone today's date object
        const endOfNext30Days = new Date(today) // Clone today's date object
        endOfNext30Days.setDate(today.getDate() + 30)
        this.dateFilter.from = startOfNext30Days.toLocaleDateString('en-CA')
        this.dateFilter.upto = endOfNext30Days.toLocaleDateString('en-CA')
      }
    },
    openFilterDrawer () {
      this.dateFilter.from = this.$props.filters.from ? new Date(this.$props.filters.from).toLocaleDateString('en-CA') : ''
      this.dateFilter.upto = this.$props.filters.upto ? new Date(this.$props.filters.upto).toLocaleDateString('en-CA') : ''
      this.assigneeFilter = [...this.$props.filters.assigneeFilter]
      this.taskNameFilter = this.$props.filters.taskNameFilter
      this.tagsFilter = JSON.parse(JSON.stringify(this.$props.filters.tagsFilter))
      this.clearAllTags()
      for (const tag of this.tagsFilter) {
        this.addNewTag(tag)
      }
      this.filterDrawer = true
    },
    closeFilter () {
      this.dateFilter.from = ''
      this.dateFilter.upto = ''
      this.assigneeFilter = []
      this.filterDrawer = false
    },
    findTaskStatusById (id) {
      const status = this.taskStatuses.find((item) => item.id === id)
      return status.label
    },
    findTaskIdByName (name) {
      const status = this.taskStatuses.find((item) => item.label === name)
      return status.id
    },
    // autoComputeParentProgress (id, board) {
    //   const progress = this.kanbanTree.autoComputeProgress(id)
    //   const updateData = { progress }
    //   if (progress === 100) {
    //     const status = this.findTaskIdByName('DONE')
    //     updateData.status = status
    //   }
    //   if (progress === 0) {
    //     const status = this.findTaskIdByName('TO DO')
    //     updateData.status = status
    //   }
    //   console.log('auto compute progress', updateData.progress)
    //   UpdateTask(id, updateData).then((res) => {
    //     board.updateCard({
    //       id,
    //       card: { progress }
    //     })
    //     if (updateData.status) {
    //       board.moveCard({
    //         id,
    //         columnId: updateData.status
    //       })
    //     }
    //     if (res.parent_task_id) {
    //       this.autoComputeParentProgress(res.parent_task_id)
    //     }
    //   })
    // },
    handleSwimlaneChange (id, columnId, board, updatedProgress, isSwimlaneChanged = false) {
      const actualDates = new Date()
      const loader = new Loader()
      loader.show()
      const status = this.findTaskStatusById(columnId)
      const updateData = { status: columnId }
      if (status === 'TO DO') {
        updateData.progress = 0
      } else if (status === 'IN PROGRESS') {
        if (this.moveCards) {
          updateData.progress = updatedProgress
          this.moveCards = false
        } else {
          updateData.progress = 1
        }
      } else if (status === 'DONE') {
        updateData.progress = 100
      }
      // updated the actual start Date
      const card = board.getCard(id)
      if (!card.actualStartDate && updateData.progress > 0) {
        updateData.actual_start_date = actualDates
      }
      if (status === 'DONE') {
        updateData.actual_end_date = actualDates
      }
      UpdateTask(id, updateData).then((res) => {
        board.updateCard({
          id,
          card: updateData
        })
        let progressUpdated = ''
        if (isSwimlaneChanged) {
          progressUpdated = `Task status modified to ${status} and progress modified to ${updateData.progress} %`
        } else {
          progressUpdated = `Task progress modified to ${updateData.progress} %`
        }
        addTaskComment('task', id, progressUpdated, null)
      }).catch((err) => {
        console.log(err)
        alert('Failed to update')
      }).finally(() => {
        this.destroyEventListeners()
        this.initEventListeners()
        loader.hide()
      })
    },
    kanbanInit () {
      const loader = new Loader()
      loader.show()
      GetTaskStatuses().then((res) => {
        const columns = res.custom_list_values?.map((item) => {
          return {
            label: item.name,
            id: item.id
          }
        })
        this.taskStatuses = columns
        // if (!customKanbanTree) {
        //   customKanbanTree = new TreeBuilder({
        //     idKey: 'id',
        //     parentFieldKey: 'parent',
        //     items: this.$props.tasks.data,
        //     ordered: false
        //   })
        //   customKanbanTree.computeCost()
        // }
        const tasks = JSON.parse(JSON.stringify(this.$props.tasks.data))
        this.cards = tasks.filter((item) => {
          this.tasksMap.selectedTasksMapId[item.id] = item
          return (item.type === 1)
        }).map((item) => {
          let color
          if (new Date().toISOString() > item?.planned_end_date && item.progress !== 100) {
            color = '#FF0000'
          } else {
            color = '#FFC975'
          }
          const taskAssignees = item?.task_assignees.map(item => {
            return item.assignee
          })
          const users = item.task_assignees.map((item) => item.user_id)
          const cardObj = {
            id: item.id,
            label: item?.text,
            start_date: item?.start_date,
            end_date: item?.planned_end_date,
            planned_start_date: item?.start_date,
            projected_start_date: item?.projected_start_date,
            projected_end_date: item?.projected_end_date,
            planned_end_date: item?.planned_end_date,
            progress: item?.progress,
            description: item?.description,
            parent: item?.parent,
            users,
            cost: item?.cost ?? 0,
            is_critical: item?.is_critical,
            task_assignees: taskAssignees,
            parent_task: item?.parent_core_task?.text,
            color,
            tag_tasks: item.tag_tasks,
            type: 'task',
            column: item.status,
            duration: item.duration,
            comments: item?.core_comments?.length > 0 ? item?.core_comments : [{}],
            successors: item?.src_task_links,
            predecessors: item?.target_task_links,
            is_viewer: this.isViewer,
            status: item.status,
            actualStartDate: item.actual_start_date,
            actualEndDate: item.actual_end_date
          }
          cardObj.attached_bom = item.task_material_associations[0]?.target_bom ? {
            id: item.task_material_associations[0]?.target_bom?.id,
            name: item.task_material_associations[0]?.target_bom?.name,
            associationId: item.task_material_associations[0]?.id,
            quantity: item.task_material_associations[0]?.metadata?.quantity,
            versionId: item.task_material_associations[0]?.target_bom?.bom_versions[0].id
          } : null
          return cardObj
        })
        cardShape.users = {
          show: true,
          values: this.projectUsers
        }
        board = new Kanban('#kanban', {
          columns,
          rows,
          readonly: {
            add: false
          },
          cards: this.cards,
          rowKey: 'type',
          cardShape,
          cardTemplate: template(card => cardTemplate(card))
        })

        board.setSort({
          by: 'start_date',
          dir: 'asc'
        })
        board.api.intercept('start-drag-card', (obj) => {
          const task = board.getCard(obj.id)
          const userId = []
          task.task_assignees.map((item) => {
            userId.push(item.id)
          })
          if ((userId.includes(this.user.userId) || this.user.projectLevelRole === 'ADMIN')) {
            return true
          } else {
            return false
          }
        })
        board.api.on('move-card', (obj) => {
          this.handleSwimlaneChange(obj.id, obj.columnId, board)
        })
        board.api.on('select-card', (obj) => {
          return false
        })
        // eslint-disable-next-line no-new
        new Toolbar('#toolbar',
          {
            api: board.api,
            items: [
              'search',
              'spacer',
              filter
            ]
          })
        this.initEventListeners()
      }).catch((err) => {
        console.log(err)
        alert('Failed to fetch')
      }).finally(() => {
        loader.hide()
      })
    },
    updateSelectedUsers (user) {
      this.assigneeFilter.push(user)
      this.openPopup = false
      this.searchKeyword = ''
    },
    removeAssignee (index) {
      this.assigneeFilter.splice(index, 1)
    },
    async attachTaskDocument (taskDocs, taskId) {
      const addnewAttachments = []
      taskDocs.forEach((element) => {
        if (element.flag === 'new') {
          addnewAttachments.push({ task_id: taskId, document_id: element.id })
        } else if (element.flag === 'deleted') {
          removeTaskDocuments(element.id, taskId)
        }
      })
      addnewAttachments?.length > 0 && await attchTaskDocs(addnewAttachments)
      // this is filtering the data that passed to material master table after updation
      // if any doc get delelted ,  here it  removes it
      taskDocs = taskDocs.filter((element) => {
        if (element.flag) {
          if (element.flag !== 'deleted') {
            return element
          }
        } else {
          return element
        }
      })
    },
    ...mapMutations('tag', ['addNewTag', 'updateTags', 'clearAllTags']),
    addTooltipEventListener () {
      document.addEventListener('DOMContentLoaded', function () {
        setTimeout(function () {
          var tooltips = document.querySelectorAll('.tooltip')

          tooltips.forEach(function (tooltip) {
            tooltip.querySelector('.tooltiptext')
          })
        }, 100)
      })
    },
    onClickAttachDocument (e) {
      e.stopPropagation()
      const findCardElement = (element) => {
        if (element.classList.contains('attach-document-btn')) {
          return element
        } else {
          return findCardElement(element.parentElement)
        }
      }
      const card = findCardElement(e.target)
      const cardId = card.getAttribute('data-menu-id')
      this.cardId = cardId
      const cardDetails = board.getCard(cardId)
      const task = JSON.parse(JSON.stringify(cardDetails))
      this.addDocumentToTaskObject.selectedId = cardDetails.id
      this.addDocumentToTaskObject.selectedTask = task
      this.addDocumentToTaskObject.open = true
    },
    onClickComment (e) {
      e.stopPropagation()
      this.closeEditModal()
      const findCardElement = (element) => {
        if (element.classList.contains('wx-card')) {
          return element
        } else {
          return findCardElement(element.parentElement)
        }
      }
      const card = findCardElement(e.target)
      const cardId = card.getAttribute('data-id')
      this.cardId = cardId
      this.openCommentModal = true
    },
    onClickAddAssignee (e) {
      e.stopPropagation()
      this.closeEditModal()
      const findCardElement = (element) => {
        if (element.classList.contains('wx-card')) {
          return element
        } else {
          return findCardElement(element.parentElement)
        }
      }
      const card = findCardElement(e.target)
      const cardId = card.getAttribute('data-id')
      this.cardId = cardId
      const cardDetails = board.getCard(cardId)
      const task = JSON.parse(JSON.stringify(cardDetails))
      task.text = task.label
      this.addAssigneeModalObject.selectedId = cardDetails.id
      this.addAssigneeModalObject.selectedTask = task
      this.addAssigneeModalObject.open = true
    },
    onClickAddBom (e) {
      e.stopPropagation()
      const findCardElement = (element) => {
        if (element.classList.contains('kanban-add-bom-btn')) {
          return element
        } else {
          return findCardElement(element.parentElement)
        }
      }
      const card = findCardElement(e.target)
      const cardId = card.getAttribute('data-menu-id')
      this.cardId = cardId
      const cardDetails = board.getCard(cardId)
      const task = JSON.parse(JSON.stringify(cardDetails))
      task.text = task.label
      this.addBomToTaskObject.selectedId = cardDetails.id
      this.addBomToTaskObject.selectedTask = task
      this.addBomToTaskObject.open = true
    },
    onClickShowSuccessor (e) {
      e.stopPropagation()
      this.closeEditModal()
      const findCardElement = (element) => {
        if (element.classList.contains('wx-card')) {
          return element
        } else {
          return findCardElement(element.parentElement)
        }
      }
      const card = findCardElement(e.target)
      const cardId = card.getAttribute('data-id')
      this.cardId = cardId
      const cardDetails = board.getCard(cardId)
      const task = JSON.parse(JSON.stringify(cardDetails))
      this.showDependenciesObject.selectedTask = task
      this.showDependenciesObject.type = 'Successors'
      this.showDependenciesObject.open = true
    },
    onClickShowPredecessor (e) {
      e.stopPropagation()
      this.closeEditModal()
      const findCardElement = (element) => {
        if (element.classList.contains('wx-card')) {
          return element
        } else {
          return findCardElement(element.parentElement)
        }
      }
      const card = findCardElement(e.target)
      const cardId = card.getAttribute('data-id')
      this.cardId = cardId
      const cardDetails = board.getCard(cardId)
      const task = JSON.parse(JSON.stringify(cardDetails))
      this.showDependenciesObject.selectedTask = task
      this.showDependenciesObject.type = 'Predecessors'
      this.showDependenciesObject.open = true
    },
    onClickShowTaskModal (e) {
      e.stopPropagation()
      const findCardElement = (element) => {
        if (element.classList.contains('wx-card')) {
          return element
        } else {
          return findCardElement(element.parentElement)
        }
      }
      const card = findCardElement(e.target)
      const cardId = card.getAttribute('data-id')
      const cardDetails = board.getCard(cardId)
      const task = JSON.parse(JSON.stringify(cardDetails))
      this.openEditModal(cardId, task)
    },
    onClickShowParentTask (e) {
      e.stopPropagation()
      const findCardElement = (element) => {
        if (element.classList.contains('kanban-parent-task-btn')) {
          return element
        } else {
          return findCardElement(element.parentElement)
        }
      }
      const card = findCardElement(e.target)
      const cardId = card.getAttribute('data-menu-id')
      this.cardId = cardId
      const cardDetails = board.getCard(cardId)
      const task = JSON.parse(JSON.stringify(cardDetails))
      const parentTasks = []

      const pushParentTasks = (parentId) => {
        const parentTask = this.tasksMap.selectedTasksMapId[parentId]
        parentTasks.push(parentTask)
        if (parentTask.parent) {
          pushParentTasks(parentTask.parent)
        }
      }

      pushParentTasks(task.parent)
      this.tasksMap.selectedTasksMap = parentTasks
      this.tasksMap.open = true
    },
    closeCommentModal () {
      this.openCommentModal = false
    },
    addAllEventListeners () {
      const kanbanFilterEle = {
        element: document.querySelector('#kanban-filter'),
        type: 'mousedown',
        handler: this.openFilterDrawer
      }
      kanbanFilterEle.element.addEventListener(kanbanFilterEle.type, kanbanFilterEle.handler)
      this.elementsWithCustomEventListeners.push(kanbanFilterEle)

      const kanbanCommentElements = []
      document.querySelectorAll('.kanban-comments').forEach((item) => {
        const ele = { element: item, type: 'mousedown', handler: this.onClickComment }
        ele.element.addEventListener(ele.type, ele.handler)
        kanbanCommentElements.push(ele)
      })
      this.elementsWithCustomEventListeners.push(kanbanCommentElements)

      const kanbanAddAssigneeElements = []
      document.querySelectorAll('.kanban-add-assignee').forEach((item) => {
        const ele = { element: item, type: 'mousedown', handler: this.onClickAddAssignee }
        ele.element.addEventListener(ele.type, ele.handler)
        kanbanAddAssigneeElements.push(ele)
      })
      this.elementsWithCustomEventListeners.push(kanbanAddAssigneeElements)

      const kanbanShowSuccessorElements = []
      document.querySelectorAll('.kanban-show-successor').forEach((item) => {
        const ele = { element: item, type: 'mousedown', handler: this.onClickShowSuccessor }
        ele.element.addEventListener(ele.type, ele.handler)
        kanbanShowSuccessorElements.push(ele)
      })
      this.elementsWithCustomEventListeners.push(kanbanShowSuccessorElements)

      const kanbanShowPredecessorElements = []
      document.querySelectorAll('.kanban-show-predecessor').forEach((item) => {
        const ele = { element: item, type: 'mousedown', handler: this.onClickShowPredecessor }
        ele.element.addEventListener(ele.type, ele.handler)
        kanbanShowPredecessorElements.push(ele)
      })
      this.elementsWithCustomEventListeners.push(kanbanShowPredecessorElements)

      const attachDocumentElements = []
      document.querySelectorAll('.attach-document-btn').forEach((item) => {
        const ele = { element: item, type: 'mousedown', handler: this.onClickAttachDocument }
        ele.element.addEventListener(ele.type, ele.handler)
        attachDocumentElements.push(ele)
      })
      this.elementsWithCustomEventListeners.push(attachDocumentElements)

      const wxCardElements = []
      document.querySelectorAll('.wx-card').forEach((item) => {
        const ele = { element: item, type: 'dblclick', handler: this.onClickShowTaskModal }
        ele.element.addEventListener(ele.type, ele.handler)
        wxCardElements.push(ele)
      })
      this.elementsWithCustomEventListeners.push(wxCardElements)

      const kanbanParentTaskElements = []
      document.querySelectorAll('.kanban-parent-task-btn').forEach((item) => {
        const ele = { element: item, type: 'mousedown', handler: this.onClickShowParentTask }
        ele.element.addEventListener(ele.type, ele.handler)
        kanbanParentTaskElements.push(ele)
      })
      this.elementsWithCustomEventListeners.push(kanbanParentTaskElements)

      const kanbanAddBomElements = []
      document.querySelectorAll('.kanban-add-bom-btn').forEach((item) => {
        const ele = { element: item, type: 'mousedown', handler: this.onClickAddBom }
        ele.element.addEventListener(ele.type, ele.handler)
        kanbanAddBomElements.push(ele)
      })
      this.elementsWithCustomEventListeners.push(kanbanAddBomElements)
    },
    initEventListeners () {
      this.$el.addEventListener('click', this.handleOutsideClick)
      this.addAllEventListeners()
    },
    removeAllEventListeners (elementsWithCustomEventListeners) {
      elementsWithCustomEventListeners.forEach(value => {
        if (Array.isArray(value)) {
          this.removeAllEventListeners(value)
          return
        }
        value.element.removeEventListener(value.type, value.handler)
      })
    },
    destroyEventListeners () {
      this.removeAllEventListeners(this.elementsWithCustomEventListeners)
      this.$el.removeEventListener('click', this.handleOutsideClick)
    }
  },
  mounted () {
    this.task_id = this.$route?.params?.task_id || this.$route?.query?.selectedTaskId || this.$route?.query?.selectedtaskid || 0
    this.isViewer = this.user.projectLevelRole === 'VIEWER' || this.isExternalCollaborator
    this.kanbanTree.arrayToTree(this.$props.tasks.data)
    this.kanbanInit()
    if (this.task_id) {
      const card = this.cards.find(card => card.id === this.task_id)
      this.openEditModal(this.task_id, card)
    }
  },
  beforeDestroy () {
    this.destroyEventListeners()
  },
  computed: {
    ...mapGetters(['tenantUsersList', 'currentProject', 'user', 'openProjectId', 'collaborator', 'isExternalCollaborator']),
    ...mapGetters('tag', ['getTags']),
    projectUsers () {
      let projectUsersList = []
      if (!this.collaborator) {
        projectUsersList = this.tenantUsersList
          .filter((user) => {
            return (
            this.currentProject?.project_user_associations.find(
              (item) =>
                item.user_id === user.associated_user.id && item.status === 1
            )
            )
          }).map((user) => {
            return {
              id: user.associated_user.id,
              label: user.associated_user.first_name + ' ' + user.associated_user.last_name
            }
          })
      }
      return projectUsersList
    },
    nonAddedUsers () {
      let nonAddedUsersList = []
      if (!this.collaborator) {
        nonAddedUsersList = this.tenantUsersList
          .filter((user) => {
            return (
              this.currentProject.project_user_associations.find(
                (item) =>
                  item.user_id === user.associated_user.id && item.status === 1 && item.role_id !== 4
              ) &&
            !this.assigneeFilter.find(
              (item) => item?.id === user.associated_user.id
            )
            )
          })
          .filter((user) => {
            return (
              user.associated_user.first_name
                .toLowerCase()
                .includes(this.searchKeyword.toLowerCase()) ||
            user.associated_user.last_name
              .toLowerCase()
              .includes(this.searchKeyword.toLowerCase())
            )
          })
      }
      return nonAddedUsersList
    },
    showEditDetails () {
      return (
        (this.user.projectLevelRole === 'ADMIN' ||
          this.user.projectLevelRole === 'EDITOR' ||
          this.user.projectLevelRole === 'COLLABORATOR' || this.isExternalCollaborator)
      )
    },
    showViewDetails () {
      return (
        (this.user.projectLevelRole === 'VIEWER')
      )
    }
  }
}

</script>

<style lang="scss" scoped>

  .form {
    height: 63vh;
  }

.tags {
  align-items: center;
  display: flex;
  background-color: var(--brand-color);
  border-radius: 0.3rem;
    padding: 0.6rem;
    font-size: small;
}

  .filters{
    width: 300px;
    padding: 2rem;

    h3{
      font-size: 1.5rem;
    }
    .s {
    font-size: 0.85rem;
    }
  }

  .add-user {
  &__body {
    position: relative;
  }
  &__search {
    margin-bottom: 1rem;
    input {
      width: 81.5%;
    border-radius: 0.285em;
    font-size: 1em;
    padding: 0.85rem;
    /* border-radius: 0.25rem; */
    margin-right: 10px;
    }
  }
  &__selected {
    gap: 2px;
    flex-wrap: wrap;
  }
  &__list {
    width: 66%;
    max-height: 20rem;
    overflow-y: auto;
    position: absolute;
    background: white;
    left: 29px;
    right: 0px;
    z-index: 1;
    top: 322px;
    padding: 10px;
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    &-no-result {
      padding: 1rem;
      text-align: center;
    }
    &-item {
      display: flex;
      align-items: center;
      padding: 0.5rem 0;
      border-bottom: 1px solid #e8e8e8;
      cursor: pointer;
      &:hover {
        background: #f8f8f8;
      }
      &__avatar {
        margin-right: 1rem;
      }
      &__name {
        flex: 1;
        white-space: nowrap;
      }
      &__action {
        &__btn {
          padding: 0.25rem;
          border: 1px solid #e8e8e8;
          border-radius: 0.25rem;
          background: #fff;
          cursor: pointer;
          i {
            font-size: 1.25rem;
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
  @import '../../assets/kanban/kanban.css';
  @import "@/assets/scss/sizeVars.scss";

.display_none {
  display: none;
}

.svelte-nejz1p {
  display: none;
}
/* Add this CSS to your stylesheet */
.myCard {
  min-height: auto;
  display: flex;
  flex-direction: column;
  padding: 10px;
}

.card-header {
  align-content: start;
}

.status-color {
  width: 4px; /* Adjust the width as needed */
  height: 100%;
  background: blue; /* Color of the progress bar */
}

.label {
  font-weight: 500;
  font-size: 15px;
  display: flex;
  justify-content: space-between;
  align-content: center;

  svg {
    fill: rgb(205, 42, 42);
  }
}

.assignees {
  display: flex;
  flex-wrap: wrap;
  margin-top: 5px;
}

.assignee {
  background-color: #eee; /* Assignee background color */
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 5px;
  margin-bottom: 5px;
}

/* Style for the task assignee circle container */
.assignee-circles-container {
  min-height: 30px;
  display: flex; /* Use flexbox to arrange circles horizontally */
  align-items: center; /* Center the circles vertically */
}

.assignee-circle {
  width: 30px; /* Set the width and height to create a circle */
  height: 30px;
  color: #fff; /* Set the text color to white */
  border-radius: 50%; /* Make it a circle using border-radius */
  text-align: center; /* Center the text horizontally */
  line-height: 30px; /* Center the text vertically */
  font-weight: bold; /* Make the text bold */
  margin-right: -7.5px; /* Add a small margin to create a gap */
}

.date-icon-container {
  display: flex;
  gap: 3px;
}

.custom-card-shortcuts {
  display:flex;
  justify-content:end;
  padding: 1rem;
  margin-top: 6px;
  svg {
    &:hover {
      fill: var(--brand-color);
    }
  }
  &--add-assignee-icon{
    right: 5px;
  }
  &--add-bom-icon{
    margin-right: 8px;
    top: 20px;
  }
  &--icons{
  margin-left: 8px;
  }
   &--add-document-icon{
     margin-right: 13px;
     top: 20px;
  }
}
.custom-shortcuts {
  display:flex;
  margin-top: 6px;
  &--get-parent-icon{
    //  font-size: 23px;
     margin-left: 30px;
  }
}

.menu-icon {
  margin-top: 10px;
  cursor: pointer;
}
.progress {
  height: 4px; /* Set the height of the progress bar */
  background-color: #ccc; /* Set the background color of the progress bar container */
  margin-top: 10px; /* Adjust the top margin as needed */
}

.progress-bar {
  height: 100%; /* Set the height to fill the container vertically */
  width: 0; /* Initially, the width is 0% */
  background-color: blue; /* Set the progress bar color */
}

  .wx-mark.svelte-ms9wpd.svelte-ms9wpd {
      display: none;
  }

  .wx-controls-wrapper.svelte-1ppa004.svelte-1ppa004 {
    display: none;
  }

  .wx-sidebar {
    display: none;
  }

  .wx-menu.svelte-ms9wpd.svelte-ms9wpd {
    display: none;
  }

  .wx-controls-wrapper.svelte-pinuaq.svelte-pinuaq {
    display: none;
  }

  .wx-mark.svelte-13iqu94 {
    display: none;
  }

  .wx-menu.svelte-1tqohog {
    display: none;
  }

  .wx-clickable.svelte-p36u8h {
    display: none;
  }

  .menu.svelte-9lfyly {
    display: none !important;
  }

  .wx-controls-wrapper.svelte-1s6vr1a.svelte-1s6vr1a {
    display: none !important;
  }

  .menu.svelte-16cp59w {
    display: none !important;
  }

  #kanban {
    height: 71vh;
    @media screen and (min-width: $xxl) {
    height: 81vh;
    }
  }

  #kanban-filter{
  cursor: pointer;
  padding: 2px;
   &:hover {
    background-color:rgba(0, 0, 0, 0.07);
  }
 }
 .tooltip {
  cursor: pointer;
  position: relative;
  display: inline-block;
  color: brown;
  font-style: revert-layer
}

.tooltip .tooltiptext {
  width: 200px;
  max-width: 100px;
  background-color: #fff;
  color: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(1px);
  backdrop-filter: blur(1px);
  text-align: center;
  border-radius: 6px;
  transition: opacity 0.5s, visibility 0.5s;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  position: absolute;
  z-index: 1;
  border: 1px solid #999694;
}

.tooltip:hover .tooltiptext {
  opacity: 30;
  visibility: visible;
}
</style>
