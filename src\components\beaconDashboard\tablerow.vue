<template>
  <div class="beaconsales-topselling-table-content">
    <div>{{ index + 1 }} </div>
    <div>{{ product.name }} </div>
    <div class="beaconsales-topselling-table-progress h-center v-center">
        <progress-bar :rawColor="colors[index].rawColor" :rawbg="colors[(index+1)%10].rawbg" :value="Math.round(product.percentage)"/>
    </div>
    <div class="v-center h-center">
      <span class="beaconsales-topselling-table-content-button v-center h-center" :style="{'--buttonraw':colors[index].rawColor,'--buttonbg':colors[index].rawbg}" >{{ product.percentage }}%</span>
    </div>
  </div>
</template>
<script>
import progressBar from '@/components/beaconDashboard/progressbar.vue'
export default {
  name: 'DashboradTableRow',
  components: {
    progressBar
  },
  props: {
    index: {
      type: Number,
      default: 0
    },
    product: {
      type: Object,
      default: () => ({})
    }
  },
  data: function () {
    return ({
      colors: {
        0: {
          rawColor: 'rgb(107, 107, 71)',
          rawbg: 'rgba(107, 107, 71, 0.1)'
        },
        1: {
          rawColor: 'rgb(230, 172, 0)',
          rawbg: 'rgba(230, 172, 0,0.1)'
        },
        2: {
          rawColor: 'rgb(230, 0, 0)',
          rawbg: 'rgb(255, 128, 128,.3)'
        },
        3: {
          rawColor: 'rgb(230, 115, 0)',
          rawbg: 'rgba(230, 115, 0,0.1)'
        },
        4: {
          rawColor: 'rgb(0, 230, 115)',
          rawbg: 'rgba(0, 230, 115,0.1)'
        },
        5: {
          rawColor: 'rgb(0, 57, 230)',
          rawbg: 'rgba(0, 57, 230,0.1)'
        },
        6: {
          rawColor: 'rgb(230, 0, 230)',
          rawbg: 'rgba(230, 0, 230,0.1)'
        },
        7: {
          rawColor: 'rgb(0, 230, 230)',
          rawbg: 'rgba(0, 230, 230,0.1)'
        },
        8: {
          rawColor: 'rgb(172, 115, 57)',
          rawbg: 'rgba(172, 115, 57,0.1)'
        },
        9: {
          rawColor: 'rgb(153, 51, 255)',
          rawbg: 'rgba(153, 51, 255,0.1)'
        }
      }
    })
  }
}
</script>
<style lang="scss" scoped>
.beaconsales-topselling-table{
        &-content {
          display: grid;
          grid-template-columns: 1fr 4fr 4fr 1fr;
          border-bottom: 1px solid rgb(132, 130, 130,0.2);
          padding: 1rem;
          width: 100%;
          & > div {
            text-align: center;
          }
          &-button{
            all: unset;
            padding-block:3px;
            border:.5px groove var(--buttonraw);
            background-color: var(--buttonbg);
            border-radius: 8px;
            display:flex;
            justify-content: center;
            align-items: center;
            color: var(--buttonraw);
            width: 4rem;
        }
        }
        &-progress{
            width: 100%;
            padding: 4px;
        }

}
</style>
