<template>
  <div id="chart" >
<h2 class="m-2 mx-3">{{ label }} </h2>
    <div v-if="showFilter" class="toolbar chart-buttonGroup">
      <button
        id="day "
        @click="updateData('day')"
        :class="{ active: selection === 'day' }"
      >
        1D
      </button>
      <button
        id="month "
        @click="updateData('month')"
        :class="{ active: selection === 'month' }"
      >
        1M
      </button>

      <button
        id="6months"
        @click="updateData('6months')"
        :class="{ active: selection === '6months' }"
      >
        6M
      </button>

      <button
        id="year"
        @click="updateData('year')"
        :class="{ active: selection === 'year' }"
      >
        1Y
      </button>
    </div>

    <div id="chart-timeline" >
      <apexchart
        height="500"
        ref="chart"
        :options="chartOptions"
        :series="series"
        :key="tempKey"
      ></apexchart>
    </div>
  </div>
</template>
<script>
export default {
  name: 'burnoutchart',
  props: {
    graphData: {
      type: Array,
      default: () => [[], []]
    },
    xaxis: {
      type: Array,
      default: () => []
    },
    timeLine: {
      type: String,
      default: 'year'
    },
    curve: {
      type: String,
      default: 'straight'
    },
    marker: {
      type: Boolean,
      default: false
    },
    chartType: {
      type: String,
      default: 'line'
    },
    label: {
      type: String
    },
    showFilter: Boolean,
    graphcolors: {
      type: Array
    }
  },
  components: {},
  data: function () {
    return {
      series: [],
      chartOptions: {
        chart: {
          id: 'area-datetime',
          type: this.chartType,
          height: 500,
          zoom: {
            autoScaleYaxis: true
          }
        },
        dataLabels: {
          enabled: false
        },
        markers: {
          size: this.markers ? 5 : 0,
          style: 'hollow'
        },
        stroke: {
          curve: this.curve
        },
        xaxis: {
          type: 'datetime',
          // min: new Date('01 Mar 2012').getTime(),
          tickAmount: 6
        },
        tooltip: {
          x: {
            format: 'MMM yyyy',
            labels: {
              format: 'MMMM',
              formatter: function (value, timestamp, opts) {
                return opts.dateFormatter(new Date(timestamp)).format('MMM')
              },
              datetimeUTC: true,
              datetimeFormatter: {
                year: 'yyyy',
                month: "MMM 'yy",
                day: 'MMM'
              }
            }
          }
        },
        fill: {
          type: this.chartType === 'area' ? 'gradient' : 'origin',
          gradient: {
            shadeIntensity: 1,
            opacityFrom: 0.7,
            opacityTo: 0.9,
            stops: [0, 100]
          }
        },
        colors: this.graphcolors
      },
      selection: this.timeLine,
      tempKey: 0
    }
  },
  computed: {
    chartFillStyleComputed () {
      switch (this.chartType) {
      case 'area':
        return ({
          type: 'gradient',
          gradient: {
            shadeIntensity: 1,
            opacityFrom: 0.7,
            opacityTo: 0.9,
            stops: [0, 100]
          }
        })
      default:
        return null
      }
    }
  },
  methods: {
    updateData (timeline) {
      this.selection = timeline
      this.$emit('timelineUpdate', timeline)
    },
    createGraphSeriesData () {
      const seriesTemp = this.graphData.map((data) => {
        return {
          name: data.name,
          data: data.data || []
        }
      })
      this.series = seriesTemp
    }
  },
  watch: {
    graphData: {
      handler: function (newValue, oldValue) {
        this.tempKey++
        this.series = []
        this.createGraphSeriesData()
      },
      deep: true // This option is necessary to watch changes deeply inside the object/array
    }
  },
  mounted () {
    this.series = []
    this.createGraphSeriesData()
  }
}
</script>
<style lang="scss" scoped>
.chart {
  &-buttonGroup {
    border: 1px solid black;
    width: fit-content;
    // border-radius: 6px;
    & button {
      padding:.5rem 1.5rem;
      background-color: white;
      border: none;
      // border-radius: 6px;
      cursor: pointer;
    }
    & :nth-child(n):not(:last-child) {
      border-right: 1px solid;
      border-radius: 0;
    }
  }
}
.active{
  background-color: var(--brand-color) !important;
}
#chart-timeline{
  width: 100%;
  height: 500px;
}
</style>
