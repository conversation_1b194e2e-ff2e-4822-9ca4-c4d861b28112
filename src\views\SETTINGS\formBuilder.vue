<template>
  <div class="form-builder" >
    <div class="form-builder-nav">
      <h2 class="weight-500 xxxl v-center">
        <router-link to="/settings/copy-forms">
          <img src="~@/assets/images/icons/arrow-back.svg" class="mt-1" width="30px" alt="">
        </router-link>
        Create Your Form Template Here</h2>
    </div>
    <div class="form-builder-elements">
      <form-elements />
    </div>
    <div class="form-builder-playground">
      <form-playground/>
    </div>
    <div class="form-builder-config">
      <form-config />
    </div>
  </div>
</template>

<script>
import FormConfig from '../../components/form/formConfig.vue'
import formElements from '../../components/form/formElements.vue'
import FormPlayground from '../../components/form/formPlayground.vue'
export default {
  components: { formElements, FormPlayground, FormConfig },
  name: 'FormBuilder',
  data () {
    return {
    }
  },
  methods: {
  },
  computed: {},
  created () {
    this.$store.dispatch('form/getFormTypeList')
    this.$store.dispatch('form/getFormFields')
    this.$store.dispatch('form/resetFormTemplate')
  }
}
</script>

<style lang="scss" scoped >
.form-builder {
  height: 100%;
  display: grid;
  grid-template-columns: 210px 1fr 300px;
  grid-template-rows: auto 1fr;
  grid-template-areas:
    "nav nav nav"
    "elements playground config";
  grid-gap: 10px;
  .form-builder-nav {
    grid-area: nav;
    background-color: var(--bg-color);
    margin: -12px;
    padding: 16px;
    margin-bottom: 0;
  }
  .form-builder-elements {
    grid-area: elements;
    background-color: rgba(var(--brand-rgb), 0.2);
    height: 100%;
    overflow-y: auto;
  }
  .form-builder-playground {
    grid-area: playground;
    background-color: var(--bg-color);
    height: 100%;
    overflow-y: auto;
  }
  .form-builder-config {
    grid-area: config;
    background-color: rgba(var(--brand-rgb), 0.2);
    height: 100%;
    overflow-y: auto;
  }
}
</style>
