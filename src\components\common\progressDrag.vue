<template>
    <div id="audio-progress" ref="audioProgress" class="audio-progress">
      <div
        id="draggable-point"
        ref="draggablePoint"
        class="draggable-point"
        @mousedown="startDrag"
        @mouseup="stopDrag"
      >
    <div  v-if="isDragging" class="notify"> <slot ></slot></div>
    </div>
      <div
        id="audio-progress-bar"
        ref="audioProgressBar"
        class="audio-progress-bar"
      ></div>
    </div>
  </template>
<script>
export default {
  props: {
    activeValue: {
      type: Number,
      default: 20
    }

  },
  data () {
    return {
      isDragging: false,
      startX: 0
    }
  },
  methods: {
    startDrag (event) {
      this.isDragging = true
      this.startX = event.clientX - this.$refs.draggablePoint.offsetLeft
      document.addEventListener('mousemove', this.onDrag)
      document.addEventListener('mouseup', this.stopDrag)
    },
    onDrag (event) {
      if (!this.isDragging) return

      const audioProgress = this.$refs.audioProgress
      const draggablePoint = this.$refs.draggablePoint
      const audioProgressBar = this.$refs.audioProgressBar

      const newX = event.clientX - this.startX
      const minLeft = 0
      const maxLeft = (audioProgress.clientWidth - draggablePoint.clientWidth) + 12
      if (newX >= minLeft && newX <= maxLeft) {
        draggablePoint.style.left = newX + 'px'
        const xPos = (100 * newX) / audioProgress.clientWidth + '%'
        audioProgressBar.style.width = xPos
        const value = Math.round((newX / maxLeft) * 100)
        this.$emit('dragValue', value)
      }
    },
    stopDrag () {
      if (this.isDragging) {
        this.isDragging = false
        document.removeEventListener('mousemove', this.onDrag)
        document.removeEventListener('mouseup', this.stopDrag)
      }
    },
    updateInitialProgress () {
      const audioProgress = this.$refs.audioProgress
      const draggablePoint = this.$refs.draggablePoint
      const audioProgressBar = this.$refs.audioProgressBar

      const maxLeft = audioProgress.clientWidth - draggablePoint.clientWidth
      const newX = (this.activeValue / 100) * maxLeft
      draggablePoint.style.left = newX + 'px'
      const xPos = (100 * newX) / audioProgress.clientWidth + '%'
      audioProgressBar.style.width = xPos
    }
  },
  beforeDestroy () {
    document.removeEventListener('mousemove', this.onDrag)
    document.removeEventListener('mouseup', this.stopDrag)
  },
  mounted () {
    this.updateInitialProgress()
  }
}
</script>
  <style lang="scss" scoped>
  .audio-progress {
    width: 100%;
    height: 12px;
    position: relative;
    background-color: #eee;
    border: 1px solid #a69d9d82;
    border-radius: 5px;
    margin-block: 10px;
  }
  .draggable-point {
    width: 12px;
    height: 12px;
    position: absolute;
    background-color: #333;
    cursor: pointer;
    transform: translateX(-50%);
    z-index: 2;
    border-radius: 100%;
    position: relative;
  }
  .audio-progress-bar {
    height: 12px;
    position: absolute;
    top: 0;
    left: 20;
    background-color:var(--brand-color);
    border-radius: 3px;
  }
  .notify{
    position: absolute;
    bottom:13px;
    transform: translateX(calc(-50% + 7px));
    background-color: rgb(247, 247, 247);
    border-radius: 100%;
    padding: 5px;
text-shadow: 0 1px 0 #ccc,
               0 2px 0 #c9c9c9,
               0 3px 0 #bbb,
               0 4px 0 #b9b9b9,
               0 5px 0 #aaa,
               0 6px 1px rgba(0,0,0,.1),
               0 0 5px rgba(0,0,0,.1),
               0 1px 3px rgba(0,0,0,.3),
               0 3px 5px rgba(0,0,0,.2),
               0 5px 10px rgba(0,0,0,.25),
               0 10px 10px rgba(0,0,0,.2),
               0 20px 20px rgba(0,0,0,.15);
  }
  </style>
