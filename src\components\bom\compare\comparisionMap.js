export const generateComparisonMap = (leftBomItems, rightBomItems) => {
  const compareRows = []
  const leftMaterialIds = leftBomItems.map(item => item.material_id)
  const rightMaterialIds = rightBomItems.map(item => item.material_id)
  rightMaterialIds.forEach((materialId, index) => {
    const leftIndex = leftMaterialIds.indexOf(materialId)
    if (leftIndex === -1) {
      compareRows.push({
        left: null,
        right: rightBomItems[index],
        isAdded: true,
        existing: false,
        isDeleted: false,
        isUpdated: false,
        message: [`A new item has been added to the BOM. with material id ${rightBomItems[index].custom_material_id}`],
        updateItems: {
          associated_bom: false,
          quantity: false,
          total_price: false,
          unit_size: false
        }
      })
    } else {
      compareRows.push({
        left: leftBomItems[leftIndex],
        right: rightBomItems[index],
        isAdded: false,
        existing: true,
        isDeleted: false,
        isUpdated: false,
        message: [],
        updateItems: {
          associated_bom: false,
          quantity: false,
          total_price: false,
          unit_size: false
        }
      })
    }
  })
  leftMaterialIds.forEach((materialId, index) => {
    const rightIndex = rightMaterialIds.indexOf(materialId)
    if (rightIndex === -1) {
      compareRows.push({
        left: leftBomItems[index],
        right: null,
        isAdded: false,
        existing: false,
        isDeleted: true,
        isUpdated: false,
        message: [`An item has been deleted form the BOM. with material id ${leftBomItems[index].custom_material_id}`],
        updateItems: {
          associated_bom: false,
          quantity: false,
          total_price: false,
          unit_size: false
        }
      })
    }
  })
  compareRows.forEach(row => {
    if (row.existing) {
      const left = row.left
      const right = row.right
      if (left.quantity !== right.quantity) {
        row.isUpdated = true
        row.message.push(`Quantity has been changed form ${left.quantity} to ${right.quantity} for ${right.custom_material_id}`)
        row.updateItems.quantity = `Quantity has been changed form ${left.quantity} to ${right.quantity}`
      }
      if (left.total_price !== right.total_price) {
        row.isUpdated = true
        row.message.push(`Total Price has been changed form ${left.total_price} to ${right.total_price} for ${right.custom_material_id}`)
        row.updateItems.total_price = `Total Price has been changed form ${left.total_price} to ${right.total_price}`
      }
      if (left.unit_size !== right.unit_size) {
        row.isUpdated = true
        row.message.push(`Unit size has been changed form ${left.unit_size} to ${right.unit_size} for ${right.custom_material_id}`)
        row.updateItems.unit_size = `Unit size has been changed form ${left.unit_size} to ${right.unit_size}`
      }
      if (left.associated_bom_id !== right.associated_bom_id) {
        row.isUpdated = true
        row.message.push(`For product ${right.material_product_code?.product_code} associated BOM has been changed form ${left.associated_bom_name || 'none'} to ${right.associated_bom_name || 'none'}`)
        row.updateItems.associated_bom = `Associated BOM has been changed form ${left.associated_bom_name || 'none'} to ${right.associated_bom_name || 'none'}`
      } else if (left.associated_bom_version_id !== right.associated_bom_version_id) {
        row.isUpdated = true
        row.message.push(`For product ${right.material_product_code?.product_code} associated BOM Version has been changed form ${left.associated_bom_version || 'none'} to ${right.associated_bom_version || 'none'}`)
        row.updateItems.associated_bom = `Associated BOM Version has been changed form ${left.associated_bom_version || 'none'} to ${right.associated_bom_version || 'none'}`
      }
    }
  })
  return compareRows
}
