<template>
    <div>
    <div>
      <!-- <div class="file-name-card" @click="$emit('file-selected',file)"> -->
      <div class="file-name-card" @click="fecthVersionData(file)">
        <!-- <img src="~@/assets/images/icons/file-icon.svg" width="20px" alt=""> -->
        <span class="weight-500 l elipsis-text " v-overflow-tooltip>
          {{ file.doc_name }}
        </span>
      </div>
    </div>
  </div>
  </template>

<script>
import META_CONFIG from '@/config'
import { mapGetters } from 'vuex'
export default {
  name: 'fileCard',
  props: {
    fileSelect: {
      type: Boolean,
      default: false
    },
    selectedFile: {
      type: Object,
      default: () => ({})
    },
    file: {
      type: Object,
      default: () => ({})
    },
    parentFolder: {
      type: Object,
      default: () => ({})
    },
    activeFileId: {
      type: String,
      default: null
    }
  },
  data: () => ({
    downloadingUrl: '',
    META_CONFIG,
    versionList: []
  }),
  computed: {
    ...mapGetters(['isTenantAdmin']),
    ...mapGetters(['user', 'getUserById']),
    canUpload () {
      return (
        this.file.state === 2 ||
          (this.file.state === 3 && this.file.checked_out_by === this.user.userId)
      )
    },
    showDropBox (id) {
      return (
        this.activeFieldId === id
      )
    }
  },
  methods: {
    fecthVersionData (file) {
      this.$emit('addToAttachedList', file)
    }

  },
  mounted () {
    if (!this.fileSelect) {
      this.downloadFile()
    }
  }
}
</script>

  <style lang="scss" scoped >
  .file-card {
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
    border-radius: 4px;
    position: relative;
    &--image {
      width: 100%;
      height: 140px;
      background: #dbdbdb;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        object-position: center;
      }
    }
    &-footer {
      .icon-btn {
        width: 30px;
        height: 30px;
        margin-right: 10px;
        border-radius: 50%;
        padding: 5px;
        cursor: pointer;
        &:hover {
          background: #e3e3e3;
        }
      }
      img {
        width: 100%;
      }
    }
    &-action {
      &--btn {
        width: 20px;
        position: absolute;
        top: 10px;
        right: 10px;
        cursor: pointer;
      }
      &:hover {
        .file-card-options {
          display: block;
        }
      }
    }
    &-options {
      position: absolute;
      top: 40px;
      right: 0;
      width: 140px;
      background-color: var(--white);
      border-radius: 5px;
      padding: 10px 2px;
      filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.3));
      z-index: 1;
      display: none;
      &:before {
        content: "";
        position: absolute;
        top: -10px;
        right: 10px;
        width: 0;
        height: 0;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-bottom: 10px solid var(--white);
      }
    }
    &-option {
      display: flex;
      align-items: center;
      padding: 5px 8px;
      cursor: pointer;
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
      &:hover {
        background-color: rgba(0, 0, 0, 0.1);
      }
      img {
        width: 20px;
        margin-right: 8px;
      }
      span {
        font-size: 12px;
        font-weight: 400;
        line-height: 1;
        color: var(--text-color);
      }
    }
    [data-enable="true"] {
      opacity: 1;
      cursor: pointer;
      pointer-events: auto;
    }
    .chip {
      background: var(--brand-color);
      border-radius: 4px;
      padding: 5px 5px;
      font-size: 10px;
      margin-left: 6px;
      line-height: 1;
    }

  }
  .file-name-card{
    background-color: var(--bg-color);
    display: flex;
    align-items: center;
    gap:10px;
    padding: 10px 10px;
    border-radius: 4px;
    cursor: pointer;
    user-select: none;
    &:hover{
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
    }
  }

  </style>
