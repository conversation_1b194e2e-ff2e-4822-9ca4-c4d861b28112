<template>
  <table class="company-table">
    <thead v-if="showHeader">
      <tr class="m">
        <th></th>
        <th>Logo</th>
        <th>Company</th>
        <th>Contact No</th>
        <th>Email</th>
        <th>Industry Vertical</th>
        <th>URL</th>
        <th v-if="beaconAdmin">Type</th>
        <th v-show="(isTenantAdmin | beaconAdmin) && !isOnProjectLevel">Action</th>
      </tr>
    </thead>
    <tbody v-if="!loading">
      <tr class="s" :class="{'deactivatedRow':row.status===2 || row.associationStatus === 2}" v-for="(row,index) in displayData" :key="row.id">
        <td>
          <img src="~@/assets/images/active-icon.svg" alt="" />
        </td>
        <td>
          <img v-if="row.company_logo_blob_key" :src="row.company_logo_blob_key" height="40" width="40" alt=""/>
        </td>
        <td>{{ row.company_name || '--' }}</td>
        <td>{{ row.company_phone || '--' }}</td>
        <td>{{ row.company_email || '--' }}</td>
        <td>{{ row.industry_vertical_value.name || '--' }}</td>
        <td v-overflow-tooltip>
          <div>
            <a :href="row.company_url" target="_blank">{{ row.company_url }}</a>
          </div>
        </td>
        <td v-if="beaconAdmin">{{ row.tenant_type_details.type || '--' }}</td>
        <td v-show="(isTenantAdmin || beaconAdmin) && !isOnProjectLevel" class="action">
          <span v-tooltip="'Deactivate tenant'" class="btn pointer" v-if="row.status===1 && beaconAdmin" @click="updateTenantStatus(index, 2)">
            <img src="~@/assets/images/delete-icon.svg" alt="" width="20" height="20"/>
          </span>
          <button  v-tooltip="'Deactivate tenant'" class="btn pointer" v-show="row.associationStatus===1 && row.status === 1 && !beaconAdmin" @click="deactivateCollaboratingTenant(row, index)">
            <img src="@/assets/images/deactivate_user-icon.svg" alt="" height="20"/>
          </button >
          <button  v-tooltip="'activate tenant'" class="btn pointer mr-2" v-show="row.associationStatus===2 && !beaconAdmin" @click="activateCollaboratingTenant(row, index)">
            <img src="~@/assets/images/icons/undo-icon.svg" alt="" height="20"/>
          </button >
          <button v-tooltip="'Re Invite tenant'" class="btn" v-if="row.status===4" @click="tenantReInvite(row.id)">
            <img src="~@/assets/images/icons/invite.svg" alt="" />
          </button>
          <button v-tooltip="'Activate tenant'" class="btn pointer" v-else-if="row.status!==1 && beaconAdmin" @click="updateTenantStatus(index, 1)">
            <img src="~@/assets/images/icons/undo-icon.svg" alt="" width="20" height="20"/>
            </button>
              <button v-tooltip="'Edit Company Details'" class="btn pointer ml-3 action" @click="EditCompany(row.id)">
                <img src="~@/assets/images/edit-icon.svg" alt="" width="20" height="20" />
              </button>
        </td>
      </tr>
    </tbody>
    <tbody v-else>
      <tr>
        <td colspan="6">
          <div class="center">
            <loading-circle />
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</template>

<script>
import LoadingCircle from './loadingCircle.vue'
import { alert, success } from '@/plugins/notification'
import ConfirmationDialog from '@/plugins/confirmationDialog'
import { ReInviteTenant, UpdateTenantStatusBeaconAdmin, updateCollaboratingTenantStatus } from '@/api'
import { mapGetters, mapMutations } from 'vuex'
export default {
  components: { LoadingCircle },
  name: 'company-table',
  props: {
    companyList: {
      type: Array,
      default: () => []
    },
    pageNumber: {
      type: [Number, String],
      default: 1
    },
    perPage: {
      type: [Number, String],
      default: 10
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    beaconAdmin: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      showTooltip: false
    }
  },
  methods: {
    EditCompany (rowId) {
      this.$router.push(`/edit-user/${rowId}`)
    },
    ...mapMutations(['setTenentList', 'setChildTenantList']),
    async updateTenantStatus (index, status) {
      ConfirmationDialog(`Are you sure you want to ${status === 1 ? 'activate' : 'deactivate'} the tenant ${this.companyList[index].company_name}`, async (res) => {
        if (res) {
          try {
            await UpdateTenantStatusBeaconAdmin(this.companyList[index].id, status)
            this.$notify.success('Successfully updated the tenant status')
            const tenantList = JSON.parse(JSON.stringify(this.companyList))
            tenantList[index].status = status
            this.setTenentList(tenantList)
          } catch (err) {
            this.$notify.alert(err?.message ?? 'Something went wrong')
          }
        }
      })
    },
    deactivateCollaboratingTenant (tenant, index) {
      ConfirmationDialog(`Are you sure you want to deactivate the tenant ${tenant.company_name}`, (res) => {
        if (res) {
          updateCollaboratingTenantStatus(tenant.id, 2).then((res) => {
            tenant.associationStatus = 2
            const tenantList = JSON.parse(JSON.stringify(this.companyList))
            tenantList[((this.pageNumber - 1) * this.perPage) + index].status = 2
          }).catch((err) => {
            console.log(err)
            alert('Unable to deactivate tenant')
          })
        }
      })
    },
    activateCollaboratingTenant (tenant, index) {
      ConfirmationDialog(`Are you sure you want to activate the tenant ${tenant.company_name}`, (res) => {
        if (res) {
          updateCollaboratingTenantStatus(tenant.id, 1).then((res) => {
            tenant.associationStatus = 1
            const tenantList = JSON.parse(JSON.stringify(this.companyList))
            tenantList[((this.pageNumber - 1) * this.perPage) + index].status = 1
          }).catch(() => {
            alert('Unable to activate tenant')
          })
        }
      })
    },
    tenantReInvite (tenantId) {
      const body = {
        tenant_id: tenantId
      }
      ReInviteTenant(body).then(data => {
        if (data.message === 'Invitation Resent') {
          success('Tenant Re-invited successfully')
        } else {
          alert('There was an error, re-inviting the Tenant.')
        }
      }).catch(() => {
        alert('There was an error, re-inviting the Tenant. Please try again')
      })
    }
  },
  computed: {
    ...mapGetters(['isTenantAdmin', 'isOnProjectLevel']),
    ...mapGetters(['tenantList', 'childTenantsList', 'tenantType']),

    ...mapGetters(['user']),
    displayData () {
      return this.companyList.slice(
        (this.pageNumber - 1) * this.perPage,
        this.pageNumber * this.perPage
      )
    }
  }
}
</script>

<style lang="scss" scoped >
.company-table {
  width: 100%;
  font-size: 16px;
  border-collapse: collapse;
  position: relative;
  .deactivatedRow{
    color:var(--alert);
  }
  th {
    background: var(--brand-color);
    font-weight: 500;
    padding: 12px 4px;
    position: sticky;
    top: 0;
    max-width: 180px; /* Set your desired max width for each column */
    overflow: hidden; /* Prevents content from overflowing */
    text-overflow: ellipsis; /* Adds ellipsis (...) for overflowing text */
    white-space: nowrap;
  }

  .btn {
    padding: 0.5em 0.8em;
  }

  td {
    text-align: center; /* Center cell text horizontally */
    vertical-align: middle; /* Center cell text vertically */
    border-bottom: 1px solid var(--brand-color);
    max-width: 180px; /* Set your desired max width for each column */
    overflow: hidden; /* Prevents content from overflowing */
    text-overflow: ellipsis; /* Adds ellipsis (...) for overflowing text */
    white-space: nowrap;
  }

  th,
  td {
    // text-align: left;
    padding: 8px 4px;

    &:nth-child(1) {
      width: 50px;

      &>img {
        width: 24px;
      }
    }
  }
  .action {
    align-items: center;
  }
  td:last-child {
    text-align: center; /* Center all content horizontally in the last column */
    // display: flex;
    justify-content: center; /* Center content horizontally using flexbox */
    align-items: center; /* Center content vertically using flexbox */
  }
}</style>
