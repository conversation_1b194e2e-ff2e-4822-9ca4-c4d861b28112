<template>
  <div class="time-view fh column">
    <div class="time-view-header v-center space-between px-3">
      <div class="flex v-center">
        <img
        class="mr-3 pointer"
        src="~@/assets/images/icons/arrow-back.svg"
        width="25px"
        alt=""
        @click="goBack"
      />
        <h1 class="weight-500 xxl">Team wise</h1>
      </div>
        <button
          class="btn btn-brand mr-3 pointer v-center"
          @click="$router.push('reports')"
        >
          See Reports
        </button>
    </div>
    <div class="time-view-bar flex space-between">
      <div class="flex mt-4 time-view-bar-add">
        <div class="flex v-center">
        Date Range
        <span class="time-view-bar-week ml-4 mr-1 v-center">
          <input
            type="week"
            class=""
            @change="handleSelelctedDate"
            :value="date.selectedYear + '-W' + date.selectedWeek" />
          <img
            width="20px"
            class="time-view-bar-calender"
            src="~@/assets/images/calender-icon.svg"
            alt=""
        /></span>
        <button
          class="time-view-bar-next mr-3 v-center"
          @click="selectNextWeek(-1)"
        >
          <img
            width="8px"
            class="input"
            src="~@/assets/images/left-arrow-icon.svg"
            alt=""
          />
        </button>
        <span class="v-center flex"
          >{{ date.startDate }} {{ " - " }} {{ date.endDate }}</span
        >
        <button
          class="time-view-bar-next mx-3 v-center"
          @click="selectNextWeek(1)"
        >
          <img width="8px" src="~@/assets/images/right-arrow-icon.svg" alt="" />
        </button>
        <div class="filter-box">
          <single-select-drop-down
            label="Projects"
            :options="projects"
            :current="selectedProject"
            @selected="getSelectedProject"
          />
          <multiselect-dropdown
            label="Users"
            :options="associatedUsers"
            @selected="addSelectedUsers"
          />
          <button
            class="btn btn-brand mr-3 pointer"
            :disabled="checkApplyButton"
            @click="getUserWiseData"
          >
            Apply
          </button>
        </div>
      </div>
        <button
        class="btn btn-black mr-5 "
        v-if="(user.tenantLevelRole === 'ADMIN' || this.projects.length > 0 ) && !currentWeekCheck"
        :disabled="!projectsData"
        @click="approvefilteredData"
      >
        Approve
      </button>
      </div>
    </div>
      <time-sheet-project-wise
      :date="date"
      @removeEditMode="removeEditMode"
      :lastWeekDates="lastWeekDates"
      :selectedProjectId="selectedProject.id"
      :projectsData="projectsData"
      :userList="userList"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { GetReportsData, GetAssociatedUsersList, approveTimesheet, getProjectExchangeToken } from '@/api'
import { numberToTimeFormat } from '@/utils/timeSheetHelper'
import Loader from '@/plugins/loader'
import singleSelectDropDown from '../../components/common/singleSelectDropDown.vue'
import MultiselectDropdown from '../../components/common/multiselectDropdown.vue'
import { getProjectUserAssociation } from '../../api/apis/userFlow'
import { alert, success } from '@/plugins/notification'
import timeSheetProjectWise from '../../components/timeSheet/timeSheetProjectwise.vue'
import {
  startAndEndDateFinder,
  getMonthInString,
  getDayToString,
  getWeekNumber,
  getOrderdDayNumbers
} from '@/utils/date'
export default {
  name: 'update',
  components: {
    MultiselectDropdown,
    timeSheetProjectWise,
    singleSelectDropDown
  },
  data: () => ({
    open: false,
    projects: [],
    selectedProject: {},
    selectedUserIds: [],
    projectsData: null,
    date: {
      selectedWeek: null,
      selectedYear: null,
      startDate: null,
      formatedDates: [], // it has all  the dates in iso format from mon to sun
      weekDays: []
    },
    editMode: false,
    lastWeekDates: [],
    associatedUsers: [],
    userList: {}
  }),
  computed: {
    ...mapGetters(['user', 'getUserById']),
    ...mapGetters(['tenantUsersList', 'tenantProjectList']),
    checkApplyButton () {
      if (
        this.selectedProject.id ||
        (this.selectedProject.id && this.selectedUserIds.length > 0)
      ) {
        return false
      } else return true
    },
    currentWeekCheck () {
      return this.date.selectedWeek === getWeekNumber(new Date())
    }
  },
  methods: {
    handleSelelctedDate (e) {
      // e.target.value ==2023-W28 will come like this
      const [year, week] = e.target.value.split('-W')
      this.getNewDateFormats(week, year)
    },
    getNewDateFormats (week, year) {
      this.date.selectedWeek = week
      this.date.selectedYear = year
      this.date.formatedDates = startAndEndDateFinder(week, year)
      this.date.startDate =
        getMonthInString(this.date.formatedDates[0].getMonth()) +
        ' ' +
        this.date.formatedDates[0].getDate()
      this.date.endDate =
        getMonthInString(this.date.formatedDates[6].getMonth()) +
        ' ' +
        this.date.formatedDates[6].getDate()
      this.date.weekDays = []
      this.date.formatedDates.forEach((element) => {
        this.date.weekDays.push(
          getDayToString(element.getDay()) + ' - ' + element.getDate()
        )
      })
    },
    selectNextWeek (changer) {
      let currentDate = null
      if (changer === 1) {
        currentDate = new Date(this.date.formatedDates[6])
      } else {
        currentDate = new Date(this.date.formatedDates[0])
      }
      currentDate.setDate(currentDate.getDate() + changer)
      this.getNewDateFormats(
        getWeekNumber(currentDate),
        currentDate.getFullYear()
      )
      if (this.selectedProject.id) {
        this.getUserWiseData()
      }
    },
    openEditMode () {
      this.editMode = true
    },
    removeEditMode () {
      this.editMode = false
    },
    getProjectsWithAdminRole () {
      if (this.user.tenantLevelRole === 'ADMIN') {
        this.tenantProjectList.forEach((item) => {
          this.projects.push({
            id: item?.id,
            name: item?.name
          })
        })
      } else {
        getProjectUserAssociation(this.user.userId).then((response) => {
          response.project_user_association.forEach((element) => {
            if (element.associated_project) {
              this.projects.push({
                id: element?.associated_project?.id,
                name: element?.associated_project?.name
              })
            }
          })
        })
      }
    },
    getSelectedProject (project) {
      this.selectedProject = project
      this.associatedUsers = []
      this.getUsersList(project.id)
    },
    getUsersList (projectId) {
      GetAssociatedUsersList(projectId).then((res) => {
        this.associatedUsers = res.project_user_association.map((user) => {
          return {
            label:
              user.associated_user.first_name +
              ' ' +
              user.associated_user.last_name,
            value: user.associated_user.id
          }
        })
      })
    },
    addSelectedUsers (users) {
      this.selectedUserIds = users
    },
    getUserWiseData () {
      const loader = new Loader()
      this.projectsData = {}
      try {
        loader.show()
        const filters = {
          from: [this.date?.formatedDates[0] || 0],
          upto: [this.date?.formatedDates[6] || 0],
          projectIds: [this.selectedProject.id],
          userIds: [...(this.selectedUserIds || undefined)]
        }
        GetReportsData(filters).then((res) => {
          this.projectsData = {}
          res.message.users.forEach((element) => {
            const day = new Date(element.entry_date).getDay()
            const dayIndex = getOrderdDayNumbers(day)
            if (!Object.hasOwn(this.projectsData, element.user_id)) {
              this.projectsData[element.user_id] = new Array(7).fill({
                hours: '00:00'
              })
              this.userList[element.user_id] = element.user_name
            }
            this.projectsData[element.user_id][dayIndex] = {
              user_id: element.user_id,
              hours: numberToTimeFormat(element.hours),
              date: new Date(element.entry_date)
            }
          })
          loader.hide()
        })
      } catch (error) {
        console.log(error)
        loader.hide()
        alert('something went wrong')
      }
    },
    async approvefilteredData () {
      const loader = new Loader()
      loader.show()
      const { message } = await getProjectExchangeToken(this.selectedProject.id)
      approveTimesheet(this.selectedProject.id, this.date?.formatedDates[0], this.date?.formatedDates[6], this.selectedUserIds.length > 0 ? this.selectedUserIds : [], message)
        .then(res => {
          loader.hide()
          if (res.approve_timesheet.message === 'Timesheet approved successfully') {
            success('Timesheet approved successfully')
          } else {
            alert(res.approve_timesheet.message || 'Somehing went wrong')
          }
        })
        .catch((res) => {
          loader.hide()
          alert(res.approve_timesheet.message || 'Somehing went wrong')
        })
    },
    goBack () {
      this.$router.go(-1)
    }
  },

  mounted () {
    this.getProjectsWithAdminRole()
  },
  created () {
    this.getNewDateFormats(getWeekNumber(new Date()), new Date().getFullYear())
  },
  watch: {
    'date.selectedWeek' () {
      if (this.selectedProject?.id) {
        this.getUserWiseData()
      }
    }
  }
}
</script>
<style lang="scss" s MaterialMasterTablecoped>
.time-view {
  height: calc(100% - 60px);
  &-header {
    height: 60px;
    margin: -12px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  &-bar {
    width: 100%;

    &-add {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
    &-next {
      border: 1px solid #d9d9d9;
      padding: 4px;
      background-color: #d9d9d9;
      border-radius: 2px;
    }
    &-next:active {
      background-color: var(--brand-color);
    }
    &-week {
      position: relative;
      & input {
        position: absolute;
        width: 20px;
        height: 20px;
        padding: 0;
        border: none;
        opacity: 0;
      }
    }
    & select {
      margin: 0 10px;
      min-width: 100px;
      background-color: var(--brand-light-color);
      line-height: 1;
      border: 1px solid var(--brand-color);
      border-radius: 4px;
      padding: 4px 12px;
    }
    & option {
      background-color: white;
      border-radius: 0px;
      padding: 5px;
    }
  }
}
.filter-box {
  display: flex;
  gap: 15px;
}
.no-data-box{
}
</style>
