image: node:16

pipelines:
  branches:
    develop:
      - step:
          name: Build And Deploy to Dev
          script:
            - npm install
            - npm install webpack -g
            - npm run build
            - sed -i 's|http://deb.debian.org/debian|http://archive.debian.org/debian|g' /etc/apt/sources.list
            - sed -i '/security.debian.org/d' /etc/apt/sources.list
            - echo 'Acquire::Check-Valid-Until "false";' > /etc/apt/apt.conf.d/99no-check-valid-until
            - apt-get update && apt-get install -y python3 python3-pip curl unzip
            - pip3 install --upgrade awscli
            - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
            - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
            - aws configure set default.region $AWS_DEFAULT_REGION
            - aws s3 rm s3://beacon-dev-ui --recursive
            - pipe: atlassian/aws-s3-deploy:0.4.3
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                S3_BUCKET: "beacon-dev-ui"
                LOCAL_PATH: "./dist"
                S3_PATH: "/"
                ACL: "public-read"
            - aws cloudfront create-invalidation --distribution-id E1BA1I9G2BWN05 --paths "/*"
    staging:
      - step:
          name: Build And Deploy to Staging
          script:
            - npm install
            - npm install webpack -g
            - npm run build
            - sed -i 's|http://deb.debian.org/debian|http://archive.debian.org/debian|g' /etc/apt/sources.list
            - sed -i '/security.debian.org/d' /etc/apt/sources.list
            - echo 'Acquire::Check-Valid-Until "false";' > /etc/apt/apt.conf.d/99no-check-valid-until
            - apt-get update && apt-get install -y python3 python3-pip curl unzip
            - pip3 install --upgrade awscli
            - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
            - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
            - aws configure set default.region $AWS_DEFAULT_REGION
            - aws s3 rm s3://beacon-stg-ui --recursive
            - pipe: atlassian/aws-s3-deploy:0.4.3
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                S3_BUCKET: "beacon-stg-ui"
                LOCAL_PATH: "./dist"
                S3_PATH: "/"
                ACL: "public-read"
            - aws cloudfront create-invalidation --distribution-id E3PYUUMHBXLUY4 --paths "/*"
