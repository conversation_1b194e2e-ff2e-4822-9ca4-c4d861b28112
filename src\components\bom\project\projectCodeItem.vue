<template>
  <div>
    <div class="project-code-item v-center">
      <div
        class="project-code-item__name v-center s elipsis-text"
        v-overflow-tooltip
      >
        {{ productCodeName }}
        {{ associatedBomId ? `(${associatedBomName})` : "" }}
      </div>
      <div
        class="toggle-button center"
        @click="openProductCode"
        :class="{
          active: associatedBomId,
          rotate: open,
        }"
      >
        <img src="~@/assets/images/down-arrow-icon.svg" alt="" />
      </div>
    </div>
    <div class="pl-2" v-if="open">
      <div v-if="loading" class="p-3 center s">
        <loading-circle />
      </div>
      <div v-if="!loading && associatedProductCode.length === 0" class="project-code-item center">
        <div class="project-code-item__name v-center s elipsis-text">
          No child product code
        </div>
      </div>
      <template v-else>
        <project-code-item
          v-for="childProductCode in associatedProductCode"
          :key="childProductCode.id"
          :product-object="childProductCode"
        />
      </template>
    </div>
  </div>
</template>

<script>
import { GetAllChildProductForProjectBom } from '@/api'
import loadingCircle from '../../common/loadingCircle.vue'
export default {
  components: { loadingCircle },
  name: 'ProjectCodeItem',
  props: {
    productObject: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      open: false,
      associatedProductCode: [],
      loading: false
    }
  },
  computed: {
    productCode () {
      return this.productObject.product_code
    },
    productCodeName () {
      return this.productObject.product_code_name
    },
    associatedBomId () {
      return this.productObject.associated_bom_id
    },
    associatedBomName () {
      return this.productObject.associated_bom_name
    },
    associatedBomVersionId () {
      return this.productObject.associated_bom_version_id
    },
    associatedBomVersionName () {
      return this.productObject.associated_bom_version_name
    }
  },
  methods: {
    openProductCode () {
      this.open = !this.open
      if (this.open) {
        this.getAssociatedProductCode()
      }
    },
    getAssociatedProductCode () {
      this.loading = true
      GetAllChildProductForProjectBom(this.associatedBomVersionId).then((res) => {
        this.loading = false
        this.associatedProductCode = res.bom_versions_by_pk.bom_items.map((item) => {
          return {
            product_code_name:
              item?.core_material?.material_product_code?.product_code,
            product_code: item?.core_material?.material_product_code?.id,
            associated_bom_id: item?.associated_productcode_bom?.id,
            associated_bom_name: item?.associated_productcode_bom?.name,
            associated_bom_version_id: item?.associated_bom_version?.id,
            associated_bom_version_name: item?.associated_bom_version?.version_no
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.project-code-item {
  border: 1px solid var(--brand-color);
  background-color: rgba(var(--brand-rgb), 0.2);
  margin: 6px 6px;
  padding-left: 6px;
  cursor: pointer;
  &.selected {
    background-color: rgba(var(--brand-rgb), 0.6);
  }
  &__name {
    width: calc(100% - 30px);
    height: 30px;
  }
  & .toggle-button {
    height: 30px;
    width: 30px;
    background-color: rgba(var(--brand-rgb), 1);
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.5;
    &.active {
      opacity: 1;
      cursor: pointer;
      pointer-events: all;
    }
    &.rotate {
      img {
        transform: rotate(0deg);
      }
    }
    & img {
      height: 16px;
      width: 16px;
      transform: rotate(-90deg);
    }
  }
}
</style>
