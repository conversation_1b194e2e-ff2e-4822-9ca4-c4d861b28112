import { GQL } from '../graphQl'

export const getinsightsDataApiQuery = () => GQL`query getinsightsDataApiQuery( $tenantId: uuid, $now:timestamptz){
  core_tasks(
    where: {parent_task_id: {_is_null: true}, tenant_id: {_eq: $tenantId}, core_project: {deleted: {_eq: false}}}
  ) {
    cost
    name
    progress
    duration
    planned_start_date
    planned_end_date
    spi
    core_project {
      id
      active_forms: core_forms(where: {due_date: {_gte: $now}}) {
        id
      }
      overdue_forms: core_forms(where: {due_date: {_lt:$now }}) {
        id
      }
      total_tasks: core_tasks_aggregate(where: {type: {_eq: 1}}) {
        aggregate {
          count(columns: id)
        }
      }
      completed_tasks: core_tasks_aggregate(
        where: {type: {_eq: 1}, progress: {_eq: 100}}
      ) {
        aggregate {
          count(columns: id)
        }
      }
      total_milestones: core_tasks_aggregate(where: {type: {_eq: 3}}) {
        aggregate {
          count(columns: id)
        }
      }
      completed_milestones: core_tasks_aggregate(
        where: {type: {_eq: 3}, progress: {_eq: 100}}
      ) {
        aggregate {
          count(columns: id)
        }
      }
    }
  }
  }`

export const getinsightsDataApiForProjectAdmin = () => GQL`query getinsightsDataApiForProjectQuery($tenantId: uuid, $projectId: uuid) {
    core_tasks(
      where: {parent_task_id: {_is_null: true}, tenant_id: {_eq: $tenantId}, core_project: {deleted: {_eq: false}, id: {_eq: $projectId}}}
    ) {
      cost
      name
      progress
      duration
      planned_start_date
      planned_end_date
      spi
      core_project {
        total_tasks: core_tasks_aggregate(where: {type: {_eq: 2}}) {
          aggregate {
            count(columns: id)
          }
        }
        completed_tasks: core_tasks_aggregate(
          where: {type: {_eq: 2}, progress: {_eq: 100}}
        ) {
          aggregate {
            count(columns: id)
          }
        }
        total_milestones: core_tasks_aggregate(where: {type: {_eq: 3}}) {
          aggregate {
            count(columns: id)
          }
        }
        completed_milestones: core_tasks_aggregate(
          where: {type: {_eq: 3}, progress: {_eq: 100}}
        ) {
          aggregate {
            count(columns: id)
          }
        }
      }
    }
  }
  `
export const getinsightsDataApiForProject = () => GQL`query getinsightsDataApiForProjectQuery($tenantId: uuid, $projectId: uuid, $userId: uuid) {
    core_tasks(
      where: {parent_task_id: {_is_null: true}, tenant_id: {_eq: $tenantId}, core_project: {deleted: {_eq: false}, id: {_eq: $projectId}}}
    ) {
      cost
      name
      progress
      duration
      planned_start_date
      planned_end_date
      spi
      core_project {
        total_tasks: core_tasks_aggregate(
          where: {type: {_eq: 2}, task_assignees: {user_id: {_eq: $userId}}}
        ) {
          aggregate {
            count(columns: id)
          }
        }
        completed_tasks: core_tasks_aggregate(
          where: {type: {_eq: 2}, progress: {_eq: 100}, task_assignees: {user_id: {_eq: $userId}}}
        ) {
          aggregate {
            count(columns: id)
          }
        }
        total_milestones: core_tasks_aggregate(where: {type: {_eq: 3}}) {
          aggregate {
            count(columns: id)
          }
        }
        completed_milestones: core_tasks_aggregate(
          where: {type: {_eq: 3}, progress: {_eq: 100}}
        ) {
          aggregate {
            count(columns: id)
          }
        }
      }
    }
  }
  `

export const ResourceChartDataQuery = () => GQL`query ResourceChartDataQuery ($projectId: uuid) {
  custom_list_values(where: {custom_list: {name: {_eq: "Resource State"}}}) {
    name
    core_material_master_by_resource_state_aggregate(
      where: {bom_material_items: {bom_version: {active: {_eq: true}, core_bom: {associated_links_by_target_id: {source_feature_id:{_eq: 4}  project_id: {_eq: $projectId }}}}}}
    ) {
      aggregate {
        count(columns: id)
      }
    }
  }
}`

export const getParentLevelTask = () => GQL`query GetParentTaskDetailsQuery {
  core_tasks(where: {parent_task_id:{_is_null: true}}) {
    name
    id
  }
}`

export const getChildrenTasks = () => GQL`query GetChildrenTaskQuery ($id: uuid) {
  core_tasks(where: {parent_task_id: {_eq: $id}}) {
    name
    id
    planned_start_date
    planned_end_date
    project_id
  }
}`
export const getTaskDatabyuserIdQuery = () => GQL`query getTaskDatabyuserIdQuery( $overDueConditions: core_tasks_bool_exp, $runningConditions: core_tasks_bool_exp, $upcomingConditions: core_tasks_bool_exp ) {
  overdue: core_tasks(
    where: $overDueConditions
    order_by: {planned_start_date: asc}
  ) {
    planned_end_date
    planned_start_date
    project_id
    progress
    name
    id
    status
    task_status {
      name

    }
    core_project {
      id
      name

    }
  }
  running: core_tasks(
    where: $runningConditions
  ) {
    planned_end_date
    planned_start_date
    project_id
    progress
    name
    id
    status
    task_status {
      name

    }
    core_project {
      id
      name

    }
  }
  upcomming: core_tasks(
    where: $upcomingConditions
  ) {
    planned_end_date
    planned_start_date
    project_id
    progress
    name
    id
    status
    task_status {
      name

    }
    core_project {
      id
      name

    }
  }
}
`

export const getTemplateIdWithUserIdQuery = () => GQL`query getTemplateIdWithUserIdQuery($userId: uuid) {
  template_fields(where: {visibility: {_eq: true}, template_version: {active: {_eq: true}, core_forms: {forms_user_lists: {user_id: {_eq: $userId}}}}}) {
    caption
    field_id
    template_version{
    template_id
    }
    form_field {
      caption
      key
    }
  }
}`

export const getFormDatabyuserIdQuery = () => GQL`query getFormDatabyuserIdQuery ($conditions: core_forms_bool_exp, $field_id: [uuid]){
  core_forms (where: $conditions) {
    id
    due_date
    project_id
    sequence_value
    core_project {
      name
    }
    schedule_impact
    cost_impact
    template_version {
      template_id
       core_form_template {
        name
      }
    }
      forms_metadata_by_id(
      where: {field_id: {_in: $field_id}}
    ) {
      time_value
      point_value
      int_value
      date_value
      bool_value
      string_value
      field_id
    }
    forms_user_lists(
      where: {field_id: {_in: $field_id}}
    ) {
      field_id
      user_id
    }
          forms_company_lists(
      where: {field_id: {_in: $field_id}}
    ) {
      field_id
      company_name
    }
    forms_material_lists(
      where: {field_id: {_in: $field_id}}
    ) {
      field_id
      core_material {
        material_name
      }
    }
    forms_attachments (
      where: {deleted: {_eq: false}, field_id: {_in: $field_id}}
    ) {
      field_id
      core_document {
       doc_name
      }
      core_attachment {
        file_name
      }
    }
    forms_config_lists (where: {field_id: {_in: $field_id}}) {
    field_id
    custom_list_value
    }
    forms_user_lists(
      where: {field_id: {_in: $field_id}}
    ) {
      field_id
      user_id
      core_user{
        id
        first_name
        last_name
      }
    }
    created_by_user {
      first_name
      last_name
    }
    created_on
    status
  }
}`
