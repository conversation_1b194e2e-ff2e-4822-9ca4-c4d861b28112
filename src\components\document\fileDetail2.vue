<template>
  <div class="file-detail">
    <div class="file-detail-content">
      <div v-if="loading" class="fh center">
        <loading-circle />
      </div>
      <div v-else class="fh">
        <div class="file-detail-header v-center space-between">
          <div class="title">{{ getRootFile.doc_name }}</div>
          <div class="v-center">
            <div class="icon-btn" @click="$emit('close')">
              <img src="~@/assets/images/close.png" alt="" width="18px" />
            </div>
          </div>
        </div>
        <div class="file-detail-container">
          <div class="file-detail-selected">
            <div class="file-detail-selected--view">
              <img
                v-if="blobKeyURlMapping[getSelectedFile.blob_key]"
                :src="blobKeyURlMapping[getSelectedFile.blob_key]"
                alt=""
              />
            </div>
            <div class="file-detail-selected--info">
              <div class="file-detail-selected--info--title elipsis-text">
                {{ getSelectedFile.doc_name }}
              </div>
              <div class="file-detail-selected--info--description">
                {{ getSelectedFile.description }}
              </div>
              <div class="file-detail-selected--info--date">
                <b class="weight-500" >Uploaded At: &nbsp;&nbsp;</b>{{ getSelectedFile.created_on | duration }}
              </div>
            </div>
          </div>
          <div class="file-detail-all">
            <div
              v-for="(item, index) in revisionList"
              :key="item.id"
              class="file-detail-all--item"
              :class="{ active: index === selectedIndex }"
              @click="selectedIndex = index"
            >
              <div class="file-detail-all--item-img">
                <img
                  v-if="blobKeyURlMapping[item.blob_key]"
                  :src="blobKeyURlMapping[item.blob_key]"
                  alt=""
                />
              </div>
              <div class="file-detail-all--item-title elipsis-text">
                {{ item.doc_name }}
              </div>
              <div class="file-detail-all--item-date elipsis-text">
                {{ item.created_on | duration }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAllRevisionDocuments, generateS3DownloadingUrl } from '@/api'
import loadingCircle from '../common/loadingCircle.vue'
import { Duration } from '@/utils/date'
export default {
  components: { loadingCircle },
  name: 'fileDetail',
  filters: {
    duration (value) {
      return Duration(value)
    }
  },
  props: {
    fileId: {
      type: [String, Number],
      default: ''
    }
  },
  data () {
    return {
      revisionList: [],
      loading: false,
      blobKeyURlMapping: {},
      selectedIndex: 0
    }
  },
  computed: {
    getRootFile () {
      return this.revisionList.find((item) => item.version_of === null)
    },
    getSelectedFile () {
      return this.revisionList[this.selectedIndex]
    }
  },
  methods: {
    getRevisionList () {
      this.loading = true
      getAllRevisionDocuments(this.fileId)
        .then((res) => {
          this.revisionList = res.core_documents
          this.loadS3Url(res.core_documents)
        })
        .finally(() => {
          this.loading = false
        })
    },
    loadS3Url (items) {
      const S3Objects = items.map((item) => {
        return {
          fileName: encodeURIComponent(item.doc_name),
          S3Key: item.blob_key
        }
      })
      generateS3DownloadingUrl({
        S3Objects
      }).then((res) => {
        const obj = {}
        res.url.forEach((item) => {
          obj[item.S3Key] = item.url
        })
        this.blobKeyURlMapping = obj
      })
    },
    selectRevision (index) {
      this.selectedIndex = index
    }
  },
  created () {
    this.getRevisionList()
  }
}
</script>

<style lang="scss" scoped >
.file-detail {
  position: fixed;
  bottom: 0;
  right: 0;
  top: 0;
  left: 0;
  z-index: 100;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(2px);
  animation: slideIn 0.3s ease-in-out;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }
    100% {
      transform: translateX(0);
    }
  }
  &-content {
    width: 700px;
    height: 80vh;
    border-radius: 4px;
    background-color: var(--bg-color);
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  }
  &-header {
    height: 40px;
    background-color: var(--brand-color);
    display: flex;
    align-items: center;
    padding: 0 12px;
    .close {
      cursor: pointer;
      margin-right: 12px;
    }
    .title {
      font-size: 18px;
      font-weight: 500;
    }
  }
  &-container {
    height: calc(100% - 40px);
    overflow-y: auto;
    padding: 12px;
    position: relative;
  }
  &-selected {
    position: sticky;
    top: -12px;
    &--view {
      height: 40vh;
      background: #dbdbdb;
      margin: -12px;
      margin-bottom: 0;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        object-position: center;
      }
    }
    &--info {
      padding: 10px;
      background-color: var(--bg-color);
      border-radius: 4px;
      .file-detail-selected--info--title {
        font-size: 18px;
        font-weight: 500;
      }
      .file-detail-selected--info--date {
        font-size: 12px;
        color: var(--text-color);
      }
      .file-detail-selected--info--description {
        font-size: 14px;
        color: var(--text-color-1);
      }
    }
  }
  &-all {
    margin-top: 12px;
    width: 100%;
    overflow: auto;
    display: flex;
    padding: 20px;
    &--item {
      cursor: pointer;
      border-radius: 4px;
      background-color: var(--bg-color);
      margin-right: 12px;
      width: 140px;
      flex: 0 0 140px;
      box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.2);
      &-img {
        width: 100%;
        height: 100px;
        background-color: #dbdbdb;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center;
        }
      }
      &-title {
        font-size: 12px;
        font-weight: 500;
        color: var(--text-color);
        padding: 4px 10px;
      }
      &-date {
        font-size: 10px;
        color: var(--text-color-1);
        padding: 0 10px;
        padding-bottom: 10px;
      }
      &.active {
        border: 1px solid var(--brand-color);
      }
    }
  }
}
</style>
