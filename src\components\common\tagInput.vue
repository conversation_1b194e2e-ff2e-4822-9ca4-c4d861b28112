<template>
  <div>
    <div class="filter">
      <div class="searchInput input-group relative mt-1">
        <input type="text" :placeholder="title" v-click-outside="hideSuggestion" @focus="onInputFocus"
          @keyup="showSuggestion" v-model.trim="value">
        <div class="resultBox absolute" v-if="suggestion">
          <ul v-if="suggestions.length > 0">
            <li v-for="element in suggestions" :key="element.id" class="pointer" @click='addTag(element)'>
              {{ element.name }}
            </li>
            <span v-if="hasTags">
              <h5>Or</h5>
              Select
            <span @click="showRootTags" class="tag-modal-link">Another Parent Tag</span>
            </span>
          </ul>
          <span v-else-if="loading">Loading ... </span>
          <span v-else>
            No Tag Found
            <span @click="createTag" class="tag-modal-link">Click here</span> to create new Tag!
            <h5>Or</h5>
            Select
            <span @click="showRootTags" class="tag-modal-link">Another Parent Tag</span>
          </span>
        </div>
      </div>
      <div class="tags mx-1 mt-1" v-for="tag in tags" :key="tag.id">
        <span>{{ tag.name }}</span>
        <img class="pointer" @click="removeTag(tag.id)" src="~@/assets/images/icons/close-icon.svg" width="16px" alt="" />
      </div>
    </div>
    <modal :open="openTagModal" @close="openTagModal = false" :closeOnOutsideClick="true" title="Create New TAG">
      <div class="create-new-input s p-4">
        <div class="input-group imp">
          <label class="key">TAG Name:</label>
          <input v-model="value" @input="validateInput" class="searchInput input-group relative mt-1" :isEmpty="true" type="text" placeholder="Enter TAG Name" />
          <p v-if="showError" class="error-text">This field is required.</p>
          <p v-else class="error-test-default">No error</p>
        </div>
        <div class="flex flex-end py-3 l">
          <button @click="saveTag" :disabled="EmptyInputError" class="btn">SAVE</button>
          <button @click="cancelTag" class="btn btn-black mr-3">CANCEL</button>
        </div>
      </div>
    </modal>
  </div>
</template>

<script>
import { SearchParentTags, SearchChildrenTags, CheckChildTags, InsertTags, CheckRootTags } from '@/api'
import { TagTrie } from '@/utils/tagsHelper'
import { debounce } from '@/utils/debounce'
import Modal from './modal.vue'
import { success, alert } from '@/plugins/notification'
export default {
  name: 'tagInput',
  props: {
    tags: {
      type: Array,
      default () { return [] }
    },
    title: {
      type: String,
      default () { return 'Search Tags' }
    },
    type: {
      type: Number
    }
  },
  data () {
    return {
      value: '',
      loading: false,
      tagTrie: new TagTrie(),
      showSuggestion: null,
      suggestion: false,
      suggestions: [],
      openTagModal: false,
      showError: false,
      selectedTags: []
    }
  },
  components: {
    Modal
  },
  computed: {
    EmptyInputError () {
      return this.value.trim() === ''
    },
    hasTags () {
      return this.tags?.length > 0
    }
  },
  methods: {
    addTag (tag) {
      if (tag) {
        this.tagTrie.insertTag({ name: tag.name, id: tag.id, parentId: tag.parent_id })
        this.$emit('new-tag', tag)
        this.suggestions = []
        this.value = ''
      }
    },
    hideSuggestion () {
      this.suggestion = false
    },
    onInputFocus () {
      this.suggestion = true
      this.getData()
    },
    getData () {
      this.loading = true
      if (!this.$props.tags.length) {
        SearchParentTags(this.value, this.$props.type)
          .then((res) => {
            this.suggestions = res.tag
          }
          )
          .catch((err) =>
            console.log(err)
          ).finally(() => {
            this.loading = false
          })
        return
      }
      SearchChildrenTags(this.$props.tags.at(-1).id, this.value, this.$props.type)
        .then((res) => {
          this.suggestions = res.tag
        }
        )
        .catch((err) =>
          console.log(err)
        ).finally(() => {
          this.loading = false
        })
    },
    removeTag (id) {
      this.tagTrie.deleteTagById(id)
      this.$emit('update-tags', this.tagTrie.getTagArray())
    },
    createTag () {
      this.openTagModal = true
    },
    saveTag () {
      if (this.$props.tags?.at(-1)?.id === undefined) {
        this.getTagDataForRoot()
      } else {
        this.getTagDataForChild()
      }
      this.value = ''
    },
    getTagDataForRoot () {
      const value = this.value.toUpperCase()
      CheckRootTags(value, this.$props.type).then((data) => {
        if (data.tag.length === 0) {
          InsertTags(value, this.$props.tags?.at(-1)?.id, this.$props.type)
            .then((data) => {
              this.tagTrie.insertTag({ name: data.insert_tag_one.name, id: data.insert_tag_one.id, parentId: data.insert_tag_one.parent_id })
              this.$emit('new-tag', { name: data.insert_tag_one.name, id: data.insert_tag_one.id, parentId: data.insert_tag_one.parent_id })
              success('New Tag Created successfully')
              this.suggestions = []
              this.value = ''
              this.openTagModal = false
            })
        } else {
          alert('Already exist! Failed to createa new one.')
        }
      })
    },
    getTagDataForChild () {
      const value = this.value.toUpperCase()
      CheckChildTags(value, this.$props.tags?.at(-1)?.id, this.$props.type).then((data) => {
        if (data.tag.length === 0) {
          this.$props.tags?.at(-1)?.id && InsertTags(value, this.$props.tags?.at(-1)?.id, this.$props.type)
            .then((data) => {
              this.tagTrie.insertTag({ name: data.insert_tag_one.name, id: data.insert_tag_one.id, parentId: data.insert_tag_one.parent_id })
              this.$emit('new-tag', { name: data.insert_tag_one.name, id: data.insert_tag_one.id, parentId: data.insert_tag_one.parent_id })
              success('New Tag Created successfully')
              this.suggestions = []
              this.value = ''
              this.openTagModal = false
            })
        } else {
          alert('Already exist! Failed to createa new one.')
        }
      })
    },
    cancelTag () {
      this.value = ''
      this.openTagModal = false
    },
    validateInput () {
      this.showError = this.value.trim() === ''
    },
    showRootTags () {
      const selectedTag = this.$props.tags.map(item => {
        return {
          id: item.id,
          name: item.name
        }
      })
      this.$emit('tag-selected', selectedTag)
    },
    keyPress (e) {
      if (!this.openTagModal) {} else if (e instanceof KeyboardEvent && e.code === 'Enter') {
        this.saveTag()
      }
    }
  },
  beforeDestroy () {
    this.$emit('update-tags', [])
    document.body.removeEventListener('keydown', this.keyPress)
  },
  created () {
    this.showSuggestion = debounce(this.getData, 300)
    document.body.addEventListener('keydown', this.keyPress)
  }
}
</script>

<style scoped lang="scss">
.filter {
  margin-bottom: 0.8rem;
  display: flex;
  flex-wrap: wrap;

  input {
    padding: 0.85em;
    border: none;
  }

  span {
    margin-right: 3px;
  }
}

.tags {
  display: flex;
  background-color: var(--brand-color);
  padding: 0.6rem;
  font-size: small;
  border-radius: 0.3rem;
}

.searchInput {
  border-radius: 5px;
  border: 1px solid rgba(9, 8, 8, 0.9);
  border-radius: 4px;
}

.searchInput:focus {
  box-shadow: 0 0 0 1px var(--brand-color-1)
}

.resultBox {
  overflow: scroll;
  max-height: 12rem;
  padding: 2px 8px;
  opacity: 1;
  pointer-events: auto;
  z-index: 1000;
  background-color: var(--bg-color);
  width: 100%;
  box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;

  li {
    list-style: none;
    padding: 8px 12px;
    width: 100%;
    border-radius: 3px;
  }

  li:last-child {
    padding-bottom: 3px;
  }

  li:hover {
    background: #efefef;
  }

}

.tag-modal-link {
  color: blue;
  text-decoration: underline;
  cursor: pointer;
}

.create-new-input {
  width: 260px;
  max-height: 700px;
  overflow: auto;
  padding: 10px;
}

.btn {
  margin-right: 12px;
}

.error-text {
  color: red;
  padding-top: 12px;
}
.error-test-default{
  color: red;
  padding-top: 12px;
  visibility: hidden;
}

</style>
