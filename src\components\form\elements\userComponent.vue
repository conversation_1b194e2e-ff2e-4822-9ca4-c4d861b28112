<template>
  <div>
    <div :class="{
      'form-input': true,
      'form-input--required': data.required,
    }">
      <label>{{ data.caption }}:
        <img v-if="data.visibility" class="mx-1" width="15" src="~@/assets/images/eye.svg" alt="view" />
      </label>
    </div>
    <div :class="{ 'form-location--grid-2': isFormCreatingPath }">
      <div :class="{
        'form-input': true,
        'form-input--required': data.required,
      }">
        <div v-if="isFormCreatingPath">
          <select :disabled="viewOnly" v-model="companyValue" @change="emitCompanyChange"
            :style="{ color: companyValue === 'none' ? 'grey' : '' }">
            <option :value="openTenantDetails.id" selected>
              {{ openTenantDetails.company_name }}
            </option>
            <optgroup label="Child Tenants" v-if="childTenantsList.length">
              <option v-for="childTenant in filterChildTenant" :key="childTenant.id"
                :value="childTenant.target_tenant.id" :disabled="childTenant.status === 2 || childTenant.status === 3">
                {{ childTenant.target_tenant.company_name }}
              </option>
            </optgroup>
          </select>
        </div>
      </div>
      <div :class="{
        'form-input': true,
        'form-input--required': data.required,
      }">
        <div v-if="getSelectedUser" class="form-user-bedge">
          <div>
            <div class="flex">
              <div class="form-user-bedge__name">
                {{ getSelectedUser.first_name }} {{ getSelectedUser.last_name }}
              </div>
              &nbsp;
              {{
                getSelectedUser?.status === 4
                  ? "(Invitation not accepted yet)"
                  : null
              }}
            </div>

            <div class="form-user-bedge__email">
              {{ getSelectedUser.email }}
            </div>
          </div>

          <div v-if="!viewOnly" class="form-user-bedge__action" @click="removeUser">
            <img src="~@/assets/images/delete-gray-icon.svg" width="20px" />
          </div>
        </div>
        <input v-else type="text" :style="{ height: '40px' }" placeholder="Select a User" :disabled="viewOnly"
          v-model="searchKeyword" @focus="open = true" />
        <div class="form-input--options" v-if="open">
          <div v-if="companyValue === 'none'" class="form-input--option">
            <div class="form-input--option__name">Select a Tenant First</div>
          </div>
          <div v-else-if="getUserList.length === 0" class="form-input--option">
            <div class="form-input--option__name">No users found</div>
          </div>
          <div class="form-input--option" v-for="user in getUserList" :key="user.id" @click="selectUser(user)">
            <div class="form-input--option__name">
              {{ user.first_name }} {{ user.last_name }}
            </div>
            <div class="form-input--option__email">{{ user.email }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getTenantUsersListwithInvitedUsers, GetUserListByPojIds } from '@/api'
export default {
  name: 'CompanyComponent',
  props: {
    mode: {
      type: String,
      default: 'TEMPLATE'
    },
    data: {
      type: Object,
      default: () => {}
    },
    value: {
      type: Array,
      default: () => []
    },
    viewOnly: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      searchKeyword: '',
      open: false,
      componentValue: [],
      initialValue: [],
      insert: [],
      delete: [],
      companyValue: 'none',
      userList: []
    }
  },
  computed: {
    ...mapGetters('form', ['usersList']),
    ...mapGetters('form', ['companyList']),
    ...mapGetters(['childTenantsList', 'isOnProjectLevel', 'openTenantDetails', 'openTenantId', 'openProjectId', 'isOnProjectLevel']),
    currentTenant () {
      return this.tenantList.find(tenant => tenant.id === this.openTenantId)
    },
    filterChildTenant () {
      return this.childTenantsList.filter(
        (item) => item.target_tenant.status === 1
      )
    },
    getSelectedUser () {
      return this.usersList.data.find(
        (user) => user.id === this.componentValue?.[0]?.user_id
      )
    },
    getUserList () {
      if (!Array.isArray(this.userList)) {
        return []
      }
      let usersList
      if (this.searchKeyword) {
        usersList = this.userList
          .map((item) => {
            return {
              id: item.associated_user.id,
              first_name: item.associated_user.first_name,
              last_name: item.associated_user.last_name
            }
          })
          .filter((item) =>
            `${item.first_name} ${item.last_name}`
              .replace(' ', '')
              .toLowerCase()
              .includes(this.searchKeyword.toLowerCase().replace(' ', ''))
          )
      } else {
        usersList = this.userList.map((item) => {
          return {
            id: item.associated_user.id,
            first_name: item.associated_user.first_name,
            last_name: item.associated_user.last_name
          }
        })
      }
      return usersList
    },
    isFormCreatingPath () {
      return (
        this.$route.path.includes('/createform/') ||
        this.$route.path.includes('/editform/')
      )
    }
  },
  methods: {
    allUsers () {
      if (this.companyValue !== 'none') {
        if ((this.companyValue === this.openTenantId) && this.isOnProjectLevel) {
          GetUserListByPojIds([this.openProjectId], [1, 4]).then((res) => {
            this.userList = res.project_user_association
          })
        } else {
          getTenantUsersListwithInvitedUsers(this.companyValue, this.isOnProjectLevel).then((res) => {
            this.userList = res.tenant_user_association
          })
        }
      }
    },
    emitCompanyChange () {
      this.componentValue = []
      if (this.initialValue.length) {
        this.delete = [{ userId: this.initialValue }]
      }
      this.emitChange()
    },
    emitChange () {
      const value = {}
      if (this.insert.length) {
        value.insert = this.insert
      }
      if (this.delete.length) {
        value.delete = this.delete
      }
      this.$emit('customChange', {
        field_id: this.$props.data.field_id,
        value
      })
    },
    removeUser () {
      this.componentValue = []
      if (this.initialValue.length) {
        this.delete = [{ userId: this.initialValue }]
      }
      this.emitChange()
    },
    selectUser (user) {
      this.componentValue = [
        { user_id: user.id, target_tenant_id: this.companyValue }
      ]
      if (this.initialValue[0] !== this.componentValue[0]) {
        this.insert = [
          { user_id: user.id, target_tenant_id: this.companyValue }
        ]
      }
      this.emitChange()
      this.open = false
    },
    closeDropdown (event) {
      if (this.$el.contains(event.target)) {
        return
      }
      this.open = false
    },
    setValue () {
      this.componentValue = this.value.map((item) => item)
      this.value.map((item) => {
        this.companyValue = item.target_tenant_id
      })
    }
  },
  watch: {
    value () {
      this.setValue()
    },
    companyValue: 'allUsers'
  },
  created () {
    this.$store.dispatch('form/getUserList')
    window.addEventListener('click', this.closeDropdown)
    this.setValue()
    if (this.companyValue === 'none') {
      this.companyValue = this.openTenantDetails.id
    }
    this.initialValue = this.value.map((item) => item.user_id)
  },
  destroyed () {
    window.removeEventListener('click', this.closeDropdown)
  }
}
</script>

<style lang="scss" scoped>
select{
  height: 40px;
  optgroup{
    color: var(--black)
  }
  option{
    color: var(--black)
  }
}
.form-user-bedge {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f5f5f5;
  padding-top: 5px;
  padding-bottom: 5px;
  border-radius: 5px;
  height: 40px;
  .form-user-bedge__name {
    font-size: 12px;
    font-weight: 600;
    color: #000000;
  }
  .form-user-bedge__email {
    font-size: 12px;
    color: #000000;
  }
  .form-user-bedge__action {
    margin-left: 10px;
    cursor: pointer;
  }
}
.form-location {
  font-size: 12px;
  &--label {
    margin-bottom: 0.5rem;
    font-size: 1.3em;
  }
  &--grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 1rem;
  }
}
</style>
