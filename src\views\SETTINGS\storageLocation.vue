<template>
  <div class="storage-locations fh">
    <div class="storage-locations-bar v-center space-between px-3">
      <h3 class="weight-500 xl">Storage Locations</h3>
      <div class="v-center">
        <button class="btn btn-black pointer" @click="openCreateLocationModal" v-if="isTenantAdmin">
          Add New Storage Location
        </button>
      </div>
    </div>
    <div class="storage-locations-container">
      <div class="fh center" v-if="loading">
        <loading-circle />
      </div>
      <div
        class="storage-locations-no-form fh center"
        v-if="!loading && locations.length === 0"
      >
        No Storage Locations
      </div>
      <div
        class="fh mt-2"
        v-if="!loading && locations.length"
      >
        <div class="storage-locations-group">
          <div class="storage-locations-group-header">
            <div class="storage-locations-group-header-item" v-overflow-tooltip>
              Name
            </div>
            <div class="storage-locations-group-header-item" v-overflow-tooltip>
              City
            </div>
            <div class="storage-locations-group-header-item" v-overflow-tooltip>
              State
            </div>
            <div class="storage-locations-group-header-item" v-overflow-tooltip>
              Address
            </div>
            <div class="storage-locations-group-header-item" v-overflow-tooltip>
              Created On
            </div>
            <div class="storage-locations-group-header-item" v-overflow-tooltip>
              Created By
            </div>
            <div class="storage-locations-group-header-item" v-overflow-tooltip v-if="isTenantAdmin">
              Action
            </div>
          </div>
        </div>
        <div
            class="storage-locations-group-items"
            v-for="(item, index) in locations"
            :key="item.id"
          >
            <div class="storage-locations-group-item" v-overflow-tooltip>
              {{ item.name }}
            </div>
            <div class="storage-locations-group-item" v-overflow-tooltip>
              {{ item.city }}
            </div>
            <div class="storage-locations-group-item" v-overflow-tooltip>
              {{ item.state }}
            </div>
            <div class="storage-locations-group-item elipsis-text" v-overflow-tooltip>
              {{ item.address_line_1 + ' ' + item.address_line_2 }}
            </div>
            <div class="storage-locations-group-item" v-overflow-tooltip>
              {{ new Date(item.created_on).toLocaleDateString('en-US') }}
            </div>
            <div class="storage-locations-group-item" v-overflow-tooltip>
              {{ item?.created_by_user?.first_name + ' ' + item?.created_by_user?.last_name }}
            </div>
            <div  v-overflow-tooltip v-if="isTenantAdmin">
              <img @click="openEditModal(index)" v-tooltip="'Edit Storage Location'" class="pointer" src="~@/assets/images/edit-icon.svg" alt="" />
              <img @click="openDeleteModal(index)" width="16"  v-tooltip="'Delete Storage Location'" class="pointer" src="~@/assets/images/delete-icon.svg" alt="" />

            </div>
          </div>
      </div>
    </div>
    <modal :open="openCreateModal"
     @close="closeCreateLocationModal"
      :closeOnOutsideClick="true"
      title="Create Storage Location"

      >
        <create-storage-location
 v-if="openCreateModal"
        :open="openCreateModal"
         @close= "closeCreateLocationModal"
         @save-and-close="saveAndClose"
         @update="getLocations"
        />
    </modal>
    <modal :open="editModalObject.open"
     @close="closeEditModal"
      :closeOnOutsideClick="true"
      title="Update Storage Location"
      >
        <create-storage-location
         v-if="editModalObject.selectedIndex !== -1"
           :open="editModalObject.open"
         :forEdit="true"
         :locationObj="locations[editModalObject.selectedIndex]"
         @close= "closeEditModal"
         @update-and-close="updateAndClose"
        />

    </modal>
  </div>
</template>

<script>
import { getStorageLocations, updateStorageLocations } from '@/api'
import { mapGetters } from 'vuex'
import ConfirmationDialog from '@/plugins/confirmationDialog'
import loadingCircle from '../../components/common/loadingCircle.vue'
import modal from '../../components/common/modal.vue'
import CreateStorageLocation from '../../components/settings/StorageLocations/createStorageLocation.vue'

export default {
  components: { loadingCircle, modal, CreateStorageLocation },
  name: 'storage-locations',
  data: () => ({
    locations: [],
    loading: false,
    openCreateModal: false,
    editModalObject: {
      selectedIndex: -1,
      open: false
    },
    deleteModalObject: {
      selectedIndex: -1,
      open: false
    }
  }),
  computed: {
    ...mapGetters(['user']),
    isTenantAdmin () {
      return this.user.tenantLevelRole === 'ADMIN'
    }
  },
  methods: {
    openCreateLocationModal () {
      this.openCreateModal = true
    },
    closeCreateLocationModal () {
      this.openCreateModal = false
    },
    openEditModal (index) {
      this.editModalObject.selectedIndex = index
      this.editModalObject.open = true
    },
    closeEditModal () {
      this.editModalObject.selectedIndex = -1
      this.editModalObject.open = false
    },
    openDeleteModal (index) {
      ConfirmationDialog(
        `Are you sure you want to delete the storage location ${this.locations[index].name}?`,
        async (res) => {
          if (res) {
            try {
              this.loading = true
              await updateStorageLocations(this.locations[index].id, { deleted: true })
              this.$notify.success('Successfully deleted the storage location')
              this.locations.splice(index, 1)
            } catch (err) {
              this.$notify.alert(err?.message ?? 'Something went wrong')
            } finally {
              this.loading = false
            }
          }
        }
      )
    },
    async getLocations () {
      try {
        this.loading = true
        const res = await getStorageLocations()
        this.locations = res.address_locations
      } catch (err) {
        this.$notify.alert(err?.message ?? 'Something went wrong')
      } finally {
        this.loading = false
      }
    },
    saveAndClose (data) {
      this.locations.push(data)
      this.openCreateModal = false
    },
    updateAndClose (data) {
      const updateObj = {
        ...this.locations[this.editModalObject.selectedIndex],
        ...data
      }
      this.$set(this.locations, this.editModalObject.selectedIndex, updateObj)
      this.closeEditModal()
    }
  },
  created () {
    this.getLocations()
  }
}
</script>

<style lang="scss" scoped>
.storage-locations {
  &-bar {
    height: 60px;
    padding: 16px;
    margin-top: -12px;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  &-container {
    height: calc(100% - 60px);
  }
  &-group {
    & > div {
      width: 100%;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
      & > div {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
    &-header {
      background-color: var(--brand-color);
      &-item {
        padding: 4px 10px;
        font-size: 14px;
        font-weight: 600;
        color: var(--text-color);
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
    &-items {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      max-height: 2rem;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      & > div {
        padding: 4px 10px;
        font-size: 14px;
        color: var(--text-color);
      }
    }
    &-item{
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      cursor: pointer;
    }
  }
}
</style>
