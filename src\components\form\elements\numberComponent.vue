<template>
  <div :class="{
    'form-input': true,
    'form-input--required': data.required
    }" >
      <label>{{data.caption}}:
        <img
      v-if="data.visibility"
      class="mx-1"
      width="15"
      src="~@/assets/images/eye.svg"
      alt="view"
      />
      </label>
      <input :disabled="viewOnly" v-model.number="componentValue" type="number" @keydown="changeNumber" @change="emitChange">
  </div>
</template>

<script>
import { restrictKeys } from '@/utils/validations'

export default {
  name: 'numberComponent',
  props: {
    mode: {
      type: String,
      default: 'TEMPLATE'
    },
    data: {
      type: Object,
      default: () => {}
    },
    value: {
      type: Number
      // default: null
    },
    viewOnly: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      componentValue: null
    }
  },
  watch: {
    value (val) {
      this.componentValue = +val
    }
  },
  created () {
    this.componentValue = +this.value
  },
  methods: {
    changeNumber (event) {
      if (['e', 'E', '+', '-'].includes(event.key)) {
        restrictKeys(event)
      }
    },
    emitChange () {
      this.$emit('customChange', {
        field_id: this.$props.data.field_id,
        value: this.componentValue
      })
    }
  }
}
</script>

<style lang="scss" scoped >

</style>
