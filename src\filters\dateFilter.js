const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

export const timeStampToDateTime = (timestamp) => {
  if (!timestamp) return '--'
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  return `${day.toString().padStart(2, 0)}/${month.toString().padStart(2, 0)}/${year} ${hour > 12 ? (hour - 12).toString().padStart(2, 0) : hour.toString().padStart(2, 0)}:${minute.toString().padStart(2, 0)} ${hour > 12 ? 'PM' : 'AM'}`
}

export const dayMonthYear = (timestamp) => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${day}/${month}/${year}`
}

export const timeStampToDate = (timestamp, format = 'dayMonthYear') => {
  if (!timestamp) return '--'
  switch (format) {
  case 'dayMonthYear':
    return dayMonthYear(timestamp)
  case 'monthDayYear':
    return monthDayYear(timestamp)
  default:
    return dayMonthYear(timestamp)
  }
}

export const monthDayYear = (timestamp) => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = date.getMonth()
  const day = date.getDate()
  return `${months[month]} ${day}, ${year}`
}
