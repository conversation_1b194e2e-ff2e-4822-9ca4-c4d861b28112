
<template>
  <div v-if="loading" class="loader center s">
    <loading-circle />
  </div>
  <div class="reports" v-else>
    <div class="reports-header v-center px-3">
      <img
        class="mr-3 pointer"
        src="~@/assets/images/icons/arrow-back.svg"
        width="30px"
        alt=""
        @click="goBack"
      />
      <h1 class="weight-500 xxl">Reports</h1>
    </div>
    <div class="reports-filters">
      <div>Filters</div>
      <div class="space-between">
        <div class="flex gap-1">
          <multiselect-dropdown
            label="Projects"
            :options="projects"
            @selected="setProjectFilters"
            :initiallySelected="appliedFilters.projectIds"
          />
          <multiselect-dropdown
          v-if="adminProjetcts.length > 0"
            label="Users"
            :options="associatedUsers"
            @selected="setUserFilters"
            :initially-selected="appliedFilters.userIds"
          />
          <div class="dropdown"  @mouseleave="emitSelected">
    <button class="dropdown-toggle"  @click="showDropdown = !showDropdown">
      Tags
    </button>
    <div class="dropdown-content" v-show="showDropdown">
      <input
        type="text"
        v-model="search"
        placeholder="Search..."
      />
      <filter-multi-select-drop-down
      :options="tags"
        :initially-selected="appliedFilters.tags"
        :search="search"
        @selected-partial="collectChildSelections"
        @selected="handleParentSelected"/>
      </div>
    </div>
          <!-- <multiselect-dropdown
            label="Tags"
            :options="tags"
            @selected="setTagFilters"
            :initially-selected="appliedFilters.tags"
          /> -->
          <div class="date-range">
            <span>From:</span>
            <input
              type="date"
              class="ml-3"
              v-model="filters.from"
              @change="[applyButton = false,filters.upto=filters.from]"
            />
            <span class="ml-3">Upto:</span>
            <input
              type="date"
              class="ml-3"
              v-model="filters.upto"
              :min="filters.from"
              @change="applyButton = false"
            />
          </div>
          <button class="btn" :disabled="applyButton" @click="applyFilters">
            Apply
          </button>
          <button class="btn" @click="clearFilter">
            Clear
          </button>
        </div>

        <div>
        </div>
      </div>
    </div>
    <div class="flex lineCharts">
      <apexchart
        class="lineCharts-single"
        type="line"
        height="350"
        :options="chartOptions"
        :series="projectSeries"
      />
      <apexchart
        class="lineCharts-single"
        type="line"
        height="350"
        :options="userChartOptions"
        :series="userSeries"
      />
    </div>
    <div id="chart" class="h-center mt-9">
      <apexchart
        type="pie"
        width="600"
        :options="piechartOptions"
        :series="pieChartSeries"
      ></apexchart>
      <apexchart
      class="lineCharts-single"
        type="line"
        height="350"
        :options="reworkChartOptions"
        :series="reworkSeries"
      >
      </apexchart>
    </div>
  </div>
</template>

<script>
/* eslint-disable no-unmodified-loop-condition */
import MultiselectDropdown from '../../components/common/multiselectDropdown.vue'
import { mapGetters } from 'vuex'
import { GetReportsData, GetAllTaskTags, getProjectUserExceptViewer, GetUserListByPojIds } from '@/api'
import loadingCircle from '../../components/common/loadingCircle.vue'
import { alert } from '../../plugins/notification'
import filterMultiSelectDropDown from '@/components/common/filterMultiSelectDropDown.vue'
export default {
  name: 'timeSheetReports',
  components: {
    MultiselectDropdown,
    loadingCircle,
    filterMultiSelectDropDown
  },
  methods: {
    emitSelected () {
      if (this.parentSelectedIds.length || this.allSelectedIds.length) {
        const merged = new Set([...this.parentSelectedIds, ...this.allSelectedIds])
        const finalIds = Array.from(merged)
        this.setTagFilters(finalIds)
      }
      this.showDropdown = false
    },
    applyFilters () {
      this.loading = true
      this.getChartData(this.filters)
    },
    clearFilter () {
      this.appliedFilters = {
        projectIds: [],
        userIds: [],
        tags: []
      }
      this.filters = {
        projectIds: [],
        userIds: [],
        tags: []
      }
      this.setInitialDates()
      this.getChartData(this.filters)
    },
    goBack () {
      this.$router.go(-1)
    },
    setProjectFilters (projectIds) {
      this.filters.projectIds = projectIds
      this.applyButton = false
      this.associatedUsers = []
      const adminProjectIds = []
      projectIds.forEach((id) => {
        this.adminProjetcts.forEach((project) => {
          if (project.id === id) {
            adminProjectIds.push(id)
          }
        })
      })
      this.associatedUsers = []
      adminProjectIds.length && this.getUsersList(adminProjectIds)
    },
    setUserFilters (userIds) {
      this.filters.userIds = userIds
      this.applyButton = false
    },
    handleParentSelected (selectedIds) {
      this.parentSelectedIds = selectedIds
    },
    collectChildSelections (partialIds) {
      const merged = new Set([...this.allSelectedIds, ...partialIds])
      this.allSelectedIds = Array.from(merged)
    },
    setTagFilters (tags) {
      this.filters.tags = tags
      this.applyButton = false
    },
    getOneMonthBeforeDate (currentDate) {
      const oneMonthBefore = new Date(currentDate)
      oneMonthBefore.setMonth(oneMonthBefore.getMonth() - 1)
      return this.formatDate(oneMonthBefore)
    },
    generateDatesInRange (startDate, endDate) {
      const dates = {}
      const options = {
        year: '2-digit',
        month: '2-digit',
        day: '2-digit'
      }
      const currentDate = new Date(startDate)
      let i = 0
      while (currentDate <= endDate) {
        const date = currentDate.toLocaleString('en-GB', options)
        dates[new Date(currentDate).setUTCHours(0, 0, 0, 0)] = i
        this.userChartOptions.xaxis.categories.push(date)
        this.chartOptions.xaxis.categories.push(date)
        this.reworkChartOptions.xaxis.categories.push(date)
        currentDate.setDate(currentDate.getDate() + 1)
        i++
      }
      return dates
    },
    formatDate (date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    setInitialDates () {
      const currentDate = new Date()
      const currentFormattedDate = this.formatDate(currentDate)
      const oneMonthBeforeFormattedDate =
        this.getOneMonthBeforeDate(currentDate)
      this.filters.from = oneMonthBeforeFormattedDate
      this.filters.upto = currentFormattedDate
    },
    getChartData (filters) {
      this.loading = true
      GetReportsData(filters)
        .then((res) => {
          const projectData = {}
          const userData = {}
          const projectsReworkData = {}
          const hoursPerUser = {}
          this.chartOptions.xaxis.categories = []
          this.userChartOptions.xaxis.categories = []
          this.reworkChartOptions.xaxis.categories = []
          this.userSeries = []
          this.projectSeries = []
          this.pieChartSeries = []
          this.reworkSeries = []
          this.piechartOptions.labels = []
          const dates = this.generateDatesInRange(new Date(filters.from), new Date(filters.upto))
          const numberOfDates = Object.keys(dates).length
          // Changing structure of the data from the backend to a structure to use in chart.
          res.message.projects.forEach((item) => {
            if (projectData[item.project_name]) {
              projectData[item.project_name][dates[new Date(item.entry_date).setUTCHours(0, 0, 0, 0)]] =
            Math.round(item.hours)
            } else {
              projectData[item.project_name] = new Array(numberOfDates).fill(0)
              projectData[item.project_name][dates[new Date(item.entry_date).setUTCHours(0, 0, 0, 0)]] =
            Math.round(item.hours)
            }
          })
          res.message.projectsRework.forEach((item) => {
            if (projectsReworkData[item.project_name]) {
              projectsReworkData[item.project_name][dates[new Date(item.entry_date).setUTCHours(0, 0, 0, 0)]] =
            Math.round(item.hours)
            } else {
              projectsReworkData[item.project_name] = new Array(numberOfDates).fill(0)
              projectsReworkData[item.project_name][dates[new Date(item.entry_date).setUTCHours(0, 0, 0, 0)]] =
            Math.round(item.hours)
            }
          })
          res.message.users.forEach((item) => {
            if (userData[item.user_name]) {
              userData[item.user_name][dates[new Date(item.entry_date).setUTCHours(0, 0, 0, 0)]] = Math.round(
                item.hours
              )
            } else {
              userData[item.user_name] = new Array(numberOfDates).fill(0)
              userData[item.user_name][dates[new Date(item.entry_date).setUTCHours(0, 0, 0, 0)]] = Math.round(
                item.hours
              )
            }
            if (hoursPerUser[item.user_name]) {
              hoursPerUser[item.user_name] =
                hoursPerUser[item.user_name] + item.hours
            } else {
              hoursPerUser[item.user_name] = item.hours
            }
          })
          for (const [key, value] of Object.entries(projectData)) {
            this.projectSeries.push({
              name: key,
              data: value
            })
          }
          for (const [key, value] of Object.entries(projectsReworkData)) {
            this.reworkSeries.push({
              name: key,
              data: value
            })
          }
          for (const [key, value] of Object.entries(userData)) {
            this.userSeries.push({
              name: key,
              data: value
            })
          }
          for (const [key, value] of Object.entries(hoursPerUser)) {
            this.pieChartSeries.push(value)
            this.piechartOptions.labels.push(key)
          }
          if (filters.projectIds) { this.appliedFilters.projectIds = filters.projectIds }
          if (filters.userIds) this.appliedFilters.userIds = filters.userIds
          if (filters.tags) this.appliedFilters.tags = filters.tags
          this.applyButton = true
        })
        .catch((err) => {
          console.log(err)
          alert('Failed to fetch')
        })
        .finally(() => {
          this.loading = false
        }).catch((err) => {
          console.log(err)
          alert('Failed to fetch')
        })
    },
    getProjectsWithNotViewer () {
      // this function is to avoid projects those have given user is not associated  as viewer
      getProjectUserExceptViewer(this.user.userId).then((res) => {
        this.projects = res?.project_user_association.map((obj) => {
          return {
            value: obj?.associated_project?.id,
            label: obj?.associated_project?.name
          }
        })
      }
      )
    },
    getUsersList (projectIds) {
      this.associatedUsers = [] // TO reset users list based on selected project
      const userIdForTemp = []
      GetUserListByPojIds(projectIds).then((res) => {
        this.associatedUsers = res.project_user_association.filter((user) => {
          // to remove the duplication need to check whether the user Data is already added or not
          // since there is no project id while fetching project_associated users initilly need to this one
          if (!userIdForTemp.includes(user.associated_user.id)) {
            userIdForTemp.push(user.associated_user.id)
            return true
          }
        }).map((filteredUser) => {
          return {
            label:
            filteredUser.associated_user.first_name +
              ' ' +
              filteredUser.associated_user.last_name,
            value: filteredUser.associated_user.id
          }
        })
      }).catch((err) => {
        console.log(err)
      })
    }
  },
  data () {
    return {
      applyButton: true,
      allSelectedIds: [],
      parentSelectedIds: [],
      filters: {
        from: '',
        upto: '',
        projectIds: [],
        userIds: [],
        tags: []
      },
      appliedFilters: {
        projectIds: [],
        userIds: [],
        tags: []
      },
      associatedUsers: [],
      loading: true,
      projects: [],
      tags: [],
      projectSeries: [],
      userSeries: [],
      reworkSeries: [],
      pieChartSeries: [],
      showDropdown: false,
      search: '',
      piechartOptions: {
        chart: {
          width: 380,
          type: 'pie'
        },
        labels: [],
        responsive: [
          {
            breakpoint: 480,
            options: {
              chart: {
                width: 200
              },
              legend: {
                position: 'bottom'
              }
            }
          }
        ]
      },
      chartOptions: {
        chart: {
          height: 350,
          type: 'line',
          zoom: {
            enabled: false
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'straight'
        },
        title: {
          text: 'Time spent for each project',
          align: 'left'
        },
        grid: {
          row: {
            colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
            opacity: 0.5
          }
        },
        xaxis: {
          categories: []
        }
      },
      userChartOptions: {
        chart: {
          height: 350,
          type: 'line',
          zoom: {
            enabled: false
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'straight'
        },
        title: {
          text: 'Time spend by each users',
          align: 'left'
        },
        grid: {
          row: {
            colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
            opacity: 0.5
          }
        },
        xaxis: {
          categories: []
        }
      },
      reworkChartOptions: {
        chart: {
          height: 350,
          type: 'line',
          zoom: {
            enabled: false
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'straight'
        },
        title: {
          text: 'Time spend by each reworks',
          align: 'left'
        },
        grid: {
          row: {
            colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
            opacity: 0.5
          }
        },
        xaxis: {
          categories: []
        }
      }
    }
  },
  computed: {
    ...mapGetters(['user', 'getUserById', 'adminProjetcts']),
    ...mapGetters(['tenantUsersList', 'tenantProjectList'])
  },
  mounted () {
    GetAllTaskTags().then(res => {
      res.tag.forEach((item) => {
        if (item.parent_id === null) {
          this.tags.push({
            value: item?.id,
            label: item?.name,
            isExpanded: false
          })
        }
      })
    })
    this.getProjectsWithNotViewer()
    this.setInitialDates()
    this.getChartData(this.filters)
    // this.tenantUsersList.forEach((item) => {
    //   if (item.status === 1) {
    //     this.users.push({
    //       value: item?.associated_user?.id,
    //       label:
    //       item?.associated_user?.first_name +
    //       ' ' +
    //       item?.associated_user?.last_name
    //     })
    //   }
    // })
  }
}
</script>

  <style lang="scss">
.reports {
  position: relative;
  &-header {
    height: 60px;
    margin: -12px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  &-filters {
    margin: 4px;
    margin-top: 1rem;
    padding: 10px;
    border: var(--border);
    position: sticky;
    top: 0px;
    z-index: 3;
    background-color: white;
  }
}
.lineCharts {
  margin-top: 4rem;
  &-single {
    width: 50%;
  }
}

.date-range {
  input {
    height: 100%;
    border: .7px solid var(--brand-color);
    border-radius: 5px;
    padding-inline: 4px;
    background: var(--brand-light-color);
  }
}
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  background-color: var(--brand-light-color);
  border: 1px solid var(--brand-color);
  padding: 8px 12px;
  cursor: pointer;
  width: 200px;
  border-radius: 0.3em;
}

.dropdown-content {
  position: absolute;
  background-color: #fff;
  border: 1px solid #ccc;
  box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
  padding: 8px;
  z-index: 2;
  width: 200px;
  max-height: 365px;
  overflow: scroll;
  & input{
    padding: 5px;
    border-radius: 4px;
    border-color: rgb(30, 27, 27,.3);
    border-style: solid;
  }
  & input:focus{
    border-color: rgb(30, 27, 27,.7);
    border-style: solid;
  }
}

.dropdown-content label {
  margin-bottom: 5px;
}
.loader {
  padding: 18rem;
}
</style>
