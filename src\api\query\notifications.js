import { GQL } from '../graphQl'

export const liveNotification = () => GQL`subscription myNotifications($offsetPage: Int!, $limits: Int!, $tenantId: uuid!, $userId: uuid!) {
  core_notifications(where: {_or: [{tenant_id: {_eq: $tenantId}}, {_and: {tenant_id: {_is_null: true}, sent_to: {_eq: $userId}}}]}, offset: $offsetPage, limit: $limits, order_by: {sent_on: desc}) {
    metadata
    id
    action
    feature
    read_status
    document_id
    form_id
    project_id
    task_id
    sent_by_user {
      id
      first_name
      last_name
    }
    core_project {
      name
    }
    sent_on
    task_id
    core_form {
      template_version {
        id
      }
    }
     #core_document {
      #doc_name
     #id
    #}
    material_id
    core_material_master {
      id
      material_name
      custom_material_id
    }
    core_task {
      name
      id
    }
    product_code {
      id
      product_code
    }
    core_bom {
      id
      project_id
      product_code
      name
      bom_versions {
        id
      }
    }
    workflow_instance_id
    workflow_step_id
  }
}
`

export const changeReadStatus = () => GQL`mutation changeNotification($id: uuid!) {
  update_core_notifications_by_pk(_set: {read_status: true}, pk_columns: {id: $id}) {
    bom_id
  }
}`
