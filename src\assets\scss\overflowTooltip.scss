
.overflow-tooltip {
  // css for top tooltip
  position: fixed;
  transform: translate(-50%, calc(-100% - 10px));
  background: #ffffff;
  color: #000000;
  font-size: 10px;
  max-width: 200px;
  padding: 4px 8px;
  border-radius: 4px;
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
  z-index: 1000;
  word-wrap: break-word;
  // add bottom notches
  &::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #ffffff;
  }
}
.tooltip-container-top {
  position: fixed;
  background: #ffffff;
  color: #000000;
  font-size: 10px;
  max-width: 200px;
  padding: 4px 8px;
  border-radius: 4px;
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
  z-index: 1000;
  // add bottom notches
  &::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #ffffff;
  }
}
.tooltip-container-left {
  position: fixed;
  background: #ffffff;
  color: #000000;
  font-size: 10px;
  max-width: 200px;
  padding: 4px 8px;
  border-radius: 4px;
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
  z-index: 1000;
  // add bottom notches
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 100%;
    transform: rotate(-90deg) translateX(50%);
    border: 5px solid transparent;
    border-top-color: #ffffff;
  }
}
.tooltip-container-bottom {
  position: fixed;
  background: #ffffff;
  color: #000000;
  font-size: 10px;
  max-width: 200px;
  padding: 4px 8px;
  border-radius: 4px;
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
  z-index: 1000;
  word-wrap: break-word;

  // Add an upward-pointing notch
  &::before {
    content: '';
    position: absolute;
    top: calc(0%) ;
    left: 50%;
    transform: translateX(-50%) translateY(-100%);
    border: 5px solid transparent;
    border-bottom-color: #ffffff;
  }
}
