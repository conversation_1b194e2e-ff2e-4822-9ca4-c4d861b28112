<template>
<div class="dependencies-container">
    <div v-if="taskData.dependencies.length">
        <div class="myCard card" v-for="task in taskData.dependencies" :key="task[key].id">
              <div class="card-header">
                  <div class="label mb-3">
                  <div>{{ task[key].name }}</div>
                   <div v-if="task[key].is_critical"> <img src="~@/assets/images/icons/critical-icon.svg" alt="" /> </div>
                   </div>
                  <div class="mb-3">
                <div class="date-icon-container mb-1">
                    <img src="~@/assets/images/icons/date-icon.svg" alt="" />
                  <span class="date-text">Planned Date:
                    {{ task[key].planned_start_date ? formatDate(task[key].planned_start_date): 'Date not set'}}
                    - {{ task[key].planned_end_date ? formatDate(task[key].planned_end_date): 'Date not set'}}
                </span>
                </div>
                <div class="date-icon-container">
                    <img src="~@/assets/images/icons/date-icon.svg" alt="" />
            <span class="date-text">Projected Date:
                {{ task[key].projected_start_date ? formatDate(task[key].projected_start_date): 'Date not set'}}
                - {{ task[key].projected_end_date ? formatDate(task[key].projected_end_date): 'Date not set'}}
            </span>
          </div>
                </div>
                </div>
                <div class="assignee-circles-container">
                    <div class="assignee-circle" v-for="(assigneee, index) in task[key].task_assignees" :key="index"
                    :style="{'background-color': assigneeColors[index % assigneeColors.length] }">{{ assigneee.assignee.first_name.charAt(0).toUpperCase() }}</div>
                </div>
                <div class="progress">
                  <div class="progress-bar" :style="{'width': task[key]?.progress+ '%', 'background-color': 'blue'}"></div>
                  <div class="progress-label">{{task[key]?.progress}}%</div>
                </div>
            </div>
    </div>
    <div v-else class="empty">
 There are no {{ type.toLowerCase() }} to this task
    </div>
    </div>
</template>

<script>

export default {
  props: {
    task: {
      type: Object,
      default: () => ({})
    },
    type: {
      type: String
    }
  },
  data () {
    return {
      key: '',
      taskData: {
        dependencies: []
      },
      assigneeColors: ['#007bff', '#ff5722', '#4caf50', '#f44336', '#9c27b0']
    }
  },
  mounted () {
    const task = JSON.parse(JSON.stringify(this.$props.task))
    if (this.$props.type === 'Successors') {
      this.key = 'target_task'
      this.taskData.dependencies = task?.successors ?? []
    } else {
      this.key = 'src_task'
      this.taskData.dependencies = task?.predecessors ?? []
    }
  },
  methods: {
    formatDate (dateString) {
      const options = { month: 'short', day: '2-digit' }
      return new Date(dateString).toLocaleDateString(undefined, options)
    }
  },
  unmounted () {
    this.taskData.dependencies = []
    this.key = ''
  }
}
</script>

<style lang="scss" scoped>

.dependencies-container {
    overflow-x: scroll;
    min-height: 249px;
    max-height: 475px;
    width: 400px;
    padding: 23px;
}

.card {
    padding: 25px;
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    margin-bottom: 15px;
}

.empty {
    line-height: 230px;
    text-align: center;
}

</style>
