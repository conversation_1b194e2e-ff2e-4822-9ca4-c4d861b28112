<template>
  <div class="list-detail">
    <div>
    <calendar-table
    @update="update"
    :selectedCalendar="calendar_details"
    @edit="editCalendar"
    />
    <div class="list-detail-table dtx-table">
        <table>
          <thead>
            <tr>
              <th class="col-sl"></th>
              <th class="col-name">Holiday Name</th>
              <th class="col-name">Holiday Date</th>
              <th class="col-action" v-if="isTenantAdmin" >Action</th>
              <th v-else></th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(item, index) in holidays"
              :key="item.id"
            >
              <td class="col-sl">{{ index + 1 }}</td>
              <td class="col-name">
                <input
                  v-if="selectToEdit === item.id"
                  v-model="edited.name"
                  type="text"
                />
                <p v-else>{{ item.name }}</p>
              </td>
              <td class="col-date">
                <input
                  v-if="selectToEdit === item.id"
                  v-model="edited.date"
                  type="date"
                />
                <p v-else>{{ item.date }}</p>
              </td>
              <td class="col-action" v-if="selectToEdit !== item.id && isTenantAdmin">
                <img
                :class="{ 'disabled': addNew }"
                  @click="edit(item)"
                  src="~@/assets/images/edit-icon.svg"
                  alt=""
                />
                <img
                :class="{ 'disabled': addNew }"
                  @click="deleteHoliday(index, item)"
                  src="~@/assets/images/delete-icon.svg"
                  alt=""
                />
              </td>
              <td class="col-action" v-else-if="selectToEdit === item.id && isTenantAdmin">
                <img
                  @click="saveEdit(index)"
                  src="~@/assets/images/icons/save-icon.svg"
                  alt=""
                  :disabled="disableSaveButton"
                  class="mr-1"
                />
                <img
                  style="width: 18px;"
                  @click="cancelEdit(item)"
                  src="~@/assets/images/close.png"
                  alt=""
                />
              </td>
              <td v-else ></td>
            </tr>
            <tr v-if="addNew">
              <td class="col-sl">
                {{ (this.holidays.length || 0) + 1 }}
              </td>
              <td class="col-name">
                <input v-model="newName" type="text" placeholder="Add new holiday" />
              </td>
              <td data-validation="holidayDate" class="col-date">
                <input v-model="newDate" type="date" />
              </td>
              <td class="col-action" v-if="isTenantAdmin">
                <img
                  @click="saveHoliday"
                  :disabled="disableSaveButton"
                  src="~@/assets/images/icons/save-icon.svg"
                  alt=""
                  class="mr-1"
                />
                <img
                  style="width: 18px;"
                  @click="cancelAddNew"
                  src="~@/assets/images/close.png"
                  alt=""
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="flex flex-end mt-1 s" v-if="isTenantAdmin">
        <button class="btn" v-if="!addNew && selectToEdit === null" @click="addNew = true">+ Add New Holiday</button>
      </div>
    </div>
    <modal
    title="Edit Calendar"
    :open="isOpen"
    @close="isOpen = false"
    :close-on-outside-click="true"
    >
      <edit-calendar
      v-if="isOpen"
      :editCalendar="editedData"
      @update="update"
      @cancel="isOpen = false"
      />
    </modal>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { emptyString } from '@/helper/formValidation'
import { AddHolidayInCalendar, DeleteCalendarById, UpdateHoliday } from '@/api'
import ConfirmationDialog from '@/plugins/confirmationDialog'
import Modal from '../../../components/common/modal.vue'
import EditCalendar from './editCalendar.vue'
import CalendarTable from './calendarTable.vue'
import { alert, success } from '../../../plugins/notification'
export default {
  components: { CalendarTable, Modal, EditCalendar },
  name: 'CalendarListDetails',
  props: {
    calendar_details: {
      type: Object
    },
    holidays: {
      type: Array
    }
  },
  data () {
    return {
      disableSaveButton: false,
      addNew: false,
      newHoliday: {
        name: '',
        date: ''
      },
      editedHolidays: [],
      key: null,
      addHoliday: false,
      isOpen: false,
      loading: false,
      newName: '',
      newDate: null,
      selectToEdit: null,
      edited: null,
      editedData: {}
    }
  },
  computed: {
    ...mapGetters([
      'isOnProjectLevel', 'user'
    ]),
    isTenantAdmin () {
      return this.user.tenantLevelRole === 'ADMIN'
    }
  },
  methods: {
    isOnlyWhiteSpaces (str) {
      const regex = /^\s*$/
      return regex.test(str)
    },
    cancelAddNew () {
      this.addNew = false
      this.newName = ''
      this.newDate = ''
    },
    cancelDelete () {
      this.newHoliday.date = ''
      this.newHoliday.name = ''
      this.addHoliday = false
    },
    edit (item) {
      this.edited = { ...item }
      this.selectToEdit = item.id
    },
    cancelEdit () {
      this.selectToEdit = null
      this.editName = ''
      this.holidays = [...this.holidays]
    },
    deleteHoliday (key, value) {
      ConfirmationDialog(`Are you sure you want to delete the Holiday ${this.calendar_details?.calendar_holidays[key].name} ?`, (res) => {
        if (res) {
          DeleteCalendarById(value.id, this.isOnProjectLevel).then(res => {
            if (res) {
              success('Holiday Deleted successfully')
              this.$emit('update')
            } else {
              alert('Something went wrong.Please try again')
            }
          }).catch(err => {
            alert(`${err.message}`)
          })
        }
      }, '', '', 'Delete Holiday')
    },
    editCalendar (item) {
      this.isOpen = true
      this.editedData = item
    },
    update () {
      this.isOpen = false
      this.$emit('update')
    },
    saveEdit () {
      if (this.disableSaveButton) return
      this.disableSaveButton = true
      if (this.isOnlyWhiteSpaces(this.edited.name)) {
        this.disableSaveButton = false
        return alert('Holiday name is required')
      }
      if (!emptyString(this.edited.date, 'Holiday Date', 'holidayDate')) {
        this.disableSaveButton = false
        return
      }
      const name = this.edited.name
      const date = this.edited.date
      let isExisting = false
      if (this.holidays) {
        this.holidays.forEach(holiday => {
          if (holiday.name === name && holiday.id !== this.edited.id) {
            alert('This holiday name is already used')
            isExisting = true
          } else if (holiday.date === date && holiday.id !== this.edited.id) {
            isExisting = true
            alert('This Date is already used')
          }
        })
      }
      if (!isExisting) {
        const data = {
          name,
          date
        }
        UpdateHoliday(data, this.edited.id, this.isOnProjectLevel).then(res => {
          if (res) {
            this.$emit('update')
            this.selectToEdit = null
            success('Holiday updated successfully')
          } else {
            alert('Something went wrong.Please try again')
          }
        }).catch(err => {
          const key = this.holidays.length - 1
          this.holidays.splice(key, 1)
          alert(`${err.message}`)
        }).finally(() => {
          this.disableSaveButton = false
        })
      } else {
        this.disableSaveButton = false
      }
    },
    saveHoliday () {
      if (this.disableSaveButton) return
      this.disableSaveButton = true
      if (this.isOnlyWhiteSpaces(this.newName)) {
        this.disableSaveButton = false
        return alert('Holiday name is required')
      }
      if (!emptyString(this.newDate, 'Holiday Date', 'holidayDate')) {
        this.disableSaveButton = false
        return
      }
      const name = this.newName
      const date = this.newDate
      let isExisting = false
      if (this.holidays) {
        this.holidays.forEach(holiday => {
          if (holiday.name === name) {
            alert('This holiday name is already used')
            isExisting = true
          } else if (holiday.date === date) {
            isExisting = true
            alert('This Date is already used')
          }
        })
      }
      if (!isExisting) {
        const data = {
          calendar_id: this.calendar_details.id,
          name: this.newName,
          date: this.newDate
        }
        // if (this.holidays !== 5) {
        //   return
        // }
        AddHolidayInCalendar(data, this.sOnProjectLevel).then(res => {
          if (res) {
            this.cancelAddNew()
            this.$emit('update')
            success('Holiday added successfully')
          } else {
            alert('Something went wrong.Please try again')
          }
        }).catch(err => {
          const key = this.holidays.length - 1
          this.holidays.splice(key, 1)
          alert(`${err.message}`)
        }).finally(() => {
          this.disableSaveButton = false
        })
      } else {
        this.disableSaveButton = false
      }
    }
  }
}
</script>

<style lang="scss" scoped >
.disabled {
    opacity: 0.5;
    box-shadow: none;
    cursor: not-allowed;
    pointer-events: none;
}
.holiday-table {
width: 100%;
margin: 0 auto;
font-size: 16px;
border-collapse: collapse;
position: relative;

th {
  min-width: 100px;
  background: var(--brand-color);
  font-weight: 500;
  padding: 12px 4px;
  position: sticky;
  top: 0;
}

td {
  border-bottom: 1px solid var(--brand-color);
}

th,
td {
  text-align: center;
  padding: 10px 5px;

  &:nth-child(1) {
    // width: 50px;
  }
}
}
.list-detail {
  height: 100%;
  overflow: auto;
  &-bar {
    border-bottom: var(--border);
  }
  &-table {
    .col-sl {
      width: 50px;
    }
    .col-action {
      width: 60px;
      img {
        width: 20px;
        cursor: pointer;
      }
    }
    .col-name {
      input {
        width: 100%;
        border: 1px solid var(--brand-color);
        padding: 4px 5px;
        border-radius: 4px;
        box-shadow: 0 0 3px var(--brand-color);
      }
    }
  }
}
</style>
