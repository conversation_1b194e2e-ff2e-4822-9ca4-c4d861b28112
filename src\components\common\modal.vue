<template>
  <div class="modal" v-show="isOpen" >
    <div class="modal-body">
      <div class="modal-header">
        <h3>{{ title }}</h3>
        <div class="modal-close" @click="$emit('close')">&times;</div>
      </div>
      <div class="modal-content" :class="{ 'modal-scroll': scroll }">
        <slot/>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Modal',
  props: {
    open: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    closeOnOutsideClick: {
      type: Boolean,
      default: false
    },
    scroll: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    open () {
      this.isOpen = this.open
    }
  },
  data () {
    return {
      isOpen: false
    }
  },
  methods: {
    chieckOutsideClick (e) {
      if (e.target.classList.contains('modal')) {
        this.$emit('close')
      }
    },
    closeOnEscapeKey (event) {
      if (event.key === 'Escape' && this.isOpen) {
        this.$emit('close')
      }
    }
  },
  mounted () {
    document.addEventListener('keydown', this.closeOnEscapeKey)
    if (this.closeOnOutsideClick) {
      this.$el.addEventListener('mousedown', this.chieckOutsideClick)
    }
  },
  beforeDestroy () {
    document.removeEventListener('keydown', this.closeOnEscapeKey)
    this.$el.remove('click', this.chieckOutsideClick)
  }
}
</script>

<style lang="scss" scoped >
.modal {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 4;
  background-color: rgba(0,0,0,0.4);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  &-body {
    position: relative;
    filter: drop-shadow(0 0 2px var(--brand-color));
    border-radius: 8px;
    animation: popIn 0.1s ease-in;
    background: var(--bg-color);
    padding: 30px 20px 20px 20px;
  }
  &-close {
    position: absolute;
    top: 8px;
    right: 12px;
    font-size: 40px;
    color: rgba(59, 59, 59, 0.4666666667);
    cursor: pointer;
    transition: opacity 0.2s;
  }
  &-close:hover {
    opacity: 0.6;
  }
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 12px;
    text-align: center;
  }
  &-header h3 {
    margin: 0;
    padding-right: 6rem;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color-1);
  }
  &-content {
    padding: 10px;
    background-color: var(--bg-color);
    max-height: 85vh;
    &-scroll {
      overflow-y: auto;
    }
  }
  @keyframes popIn {
    0% {
      transform: scale(0);
    }
    100% {
      transform: scale(1);
    }
  }
}
</style>
