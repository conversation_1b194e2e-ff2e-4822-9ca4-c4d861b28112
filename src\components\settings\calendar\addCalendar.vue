<template>
  <div class="calendar-settings">
    <div class="box flex-column">
      <div class="working-hours">
        <div class="input-group imp">
            <div class="input-group-row mb-3">
              <label class="mb-3">Calendar Name</label>
              <div data-validation="calendarName" class="input-field">
                <input class="input-name" v-model="calendarName" type="text" placeholder="Calendar Name">
              </div>
            </div>
            <div class="input-group-row mb-3">
              <label class="mb-3">Description</label>
              <div data-validation="description" class="input-field">
                <input class="input-description" v-model="description" type="text" placeholder="Description">
              </div>
            </div>
          </div>
        <div class="input-group imp mb-3" v-for="(day, index) in days" :key="index">
          <div class="input-group-row">
            <label class="mb-3">{{ day.name }}</label>
            <div class="input-field ml-3">
              <input :disabled="day.weekendOff" class="input-start" v-model="day.start" type="time" @change="findWorkingHours(index)"/>

              <span class="mx-2">-</span>
              <input :disabled="day.weekendOff" class="input-end" v-model="day.end" type="time" :min="day.start" step="1800" @change="findWorkingHours(index)" />
          <!-- Add checkbox for Saturday and Sunday -->
          <template v-if="day.dayId === 6 || day.dayId === 0">
            <input class="input-weekend-off" type="checkbox" v-model="day.weekendOff"
            @change="updateWorkingHours(day)" />
            <label class="ml-3">Weekend Off</label>
          </template>
            </div>
          </div>
        </div>
        <div class="input-group imp" data-validation="selectDay">
            <label class="mb-3">Start day of the Week</label>
            <select class="select-day" v-model="selectedDay">
              <option v-for="day in daysOfWeek" :key="day.name" :value="day.name">{{ day.name }}</option>
            </select>
          </div>
        <button class="btn mt-4" @click="submitCalendar">Submit</button>
      </div>
    </div>
  </div>
</template>

<script>
import { emptyString } from '@/helper/formValidation'
import { timeStringToDecimal } from '@/utils/timeSheetHelper'
import { AddNewCalendar, IfExistingCalendar } from '@/api'
import { alert, success } from '@/plugins/notification'
export default {
  data () {
    return {
      calendarName: '',
      description: '',
      days: [
        { dayId: 1, name: 'Monday', start: '10:00', end: '18:00', working_hours: 8 },
        { dayId: 2, name: 'Tuesday', start: '10:00', end: '18:00', working_hours: 8 },
        { dayId: 3, name: 'Wednesday', start: '10:00', end: '18:00', working_hours: 8 },
        { dayId: 4, name: 'Thursday', start: '10:00', end: '18:00', working_hours: 8 },
        { dayId: 5, name: 'Friday', start: '10:00', end: '18:00', working_hours: 8 },
        { dayId: 6, name: 'Saturday', start: '10:00', end: '18:00', working_hours: 8, weekendOff: false },
        { dayId: 0, name: 'Sunday', start: '10:00', end: '18:00', working_hours: 8, weekendOff: false }
      ],
      daysOfWeek: [
        { name: 'Monday' },
        { name: 'Tuesday' },
        { name: 'Wednesday' },
        { name: 'Thursday' },
        { name: 'Friday' },
        { name: 'Saturday' },
        { name: 'Sunday' }
      ],
      selectedDay: ''
    }
  },
  computed: {
    daysColumn1 () {
      return this.days.slice(0, 3)
    },
    daysColumn2 () {
      return this.days.slice(3, 6)
    }
  },
  methods: {
    findWorkingHours (index) {
      this.days[index].working_hours = Math.abs(timeStringToDecimal(this.days[index].end) - timeStringToDecimal(this.days[index].start))
    },
    updateWorkingHours (day) {
      if (day.weekendOff) {
        day.start = null
        day.end = null
        day.working_hours = 0
      } else {
        day.start = '10:00'
        day.end = '18:00'
        day.working_hours = 8
      }
    },
    submitCalendar () {
      if (!emptyString(this.calendarName, 'Calendar Name', 'calendarName')) {
        return
      }
      if (!emptyString(this.description, 'Description', 'description')) {
        return
      }
      if (!emptyString(this.selectedDay, 'Start Day', 'selectDay')) {
        return
      }
      const dateData = JSON.stringify({ days: this.days, selectedDay: this.selectedDay })
      const body = {
        name: this.calendarName,
        work_time_hours: dateData,
        work_time_days: JSON.stringify({}),
        description: this.description
      }
      IfExistingCalendar(this.calendarName).then(res => {
        if (res.core_project_calendar.length !== 0) {
          alert('Calendar Name already Exists')
          return
        }
        AddNewCalendar(body).then(res => {
          this.calendarName = ''
          this.description = ''
          this.days.forEach((day) => {
            day.start = '00:00'
            day.end = '00:00'
          })
          success('Calendar added successfully')
          this.$emit('update')
        })
          .catch((error) => {
            alert('Something went wrong. Try Again')
            alert(error.message)
          })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.input-group-row {
  display: flex;
  align-items: center;
}

.input-field {
  display: flex;
  align-items: center;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mx-2 {
  margin: 0 0.5rem;
}

.input-start,
.input-end {
  min-width: 100px; // Adjust as needed
}
</style>
