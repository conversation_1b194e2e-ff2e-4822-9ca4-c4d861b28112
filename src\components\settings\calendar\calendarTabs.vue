<template>
  <div>
    <div class="tabs">
      <div @click="selectedTab = 'Tab1'" :class="{ 'active btn center heading': selectedTab === 'Tab1' }">Add Calendar</div>
    </div>

    <div class="tab-content">
      <div v-if="selectedTab === 'Tab1'">
        <add-calendar
        @update="update"
        />
      </div>
    </div>
  </div>
</template>

<script>
import AddCalendar from './addCalendar.vue'

export default {
  components: {
    AddCalendar
  },
  props: {
    showCalendar: {
      type: Boolean,
      required: true
    }
  },
  data () {
    return {
      selectedTab: 'Tab1'
    }
  },
  methods: {
    update () {
      this.$emit('update')
    }
  }
}
</script>

<style>
.heading {
  width: 100%;
  text-align: center;
  background-color: var(--brand-color);
  height: 40px;
}
.tabs {
  display: flex;
}

.tabs button {

  border: none;
  margin-right: 10px;
  padding: 10px 20px;
  cursor: pointer;
}

.tabs button.active {
  font-weight: bold;
}

.tab-content {
  margin-top: 20px;
}

.calendar-settings {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 10px;
}
</style>
