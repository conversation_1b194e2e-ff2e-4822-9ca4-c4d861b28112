<template>
    <div>
      <div class="filter" >
        <div class="searchInput input-group relative mt-1"  >
          <input type="text" :disabled="timeLineEdit" :placeholder="title" v-click-outside="hideSuggestion"  @focus="onInputFocus"
            @keyup="showSuggestion" v-model.trim="value">
          <div class="resultBox absolute" v-if="suggestion">
            <ul v-if="suggestions.length > 0">
              <li v-for="element in suggestions" :key="element.id" class="pointer" @click='addTag(element)'>
                {{ element.name }}
              </li>
              <li>Select <span @click="showRootTags" class="tag-modal-link">Another Parent Tag</span></li>
            </ul>
            <span v-else-if="loading">Loading ... </span>
            <span v-else>
              No Tag Found
              <h5>Or</h5>
              Select
              <span @click="showRootTags" class="tag-modal-link">Another Parent Tag</span>
            </span>
          </div>
        </div>
        <!-- <div class="tags mx-1 mt-1" v-for="tag in tags" :key="tag.id">
          <span>{{ tag.name }}</span>
          <img class="pointer" @click="removeTag(tag.id)" src="~@/assets/images/icons/close-icon.svg" width="16px" alt="" />
        </div> -->
      </div>
    </div>
  </template>

<script>
import { SearchParentTags, SearchChildrenTags } from '@/api'
// import { TagTrie } from '@/utils/tagsHelper'
import { debounce } from '@/utils/debounce'
import { alert } from '@/plugins/notification'
export default {
  name: 'tagInput-edit-material',
  props: {
    tags: {
      type: Array,
      default () { return [] }
    },
    title: {
      type: String,
      default () { return 'Search Tags' }
    },
    type: {
      type: Number
    },
    lastParentId: {
      type: String,
      default: null
    },
    tagGroupData: {
      type: Object,
      default () { return {} }
    },
    timeLineEdit: { type: Boolean, default: false }
  },
  data () {
    return {
      value: '',
      loading: false,
      showSuggestion: null,
      suggestion: false,
      suggestions: [],
      openTagModal: false,
      showError: false,
      selectedTags: []
    }
  },
  computed: {
    EmptyInputError () {
      return this.value.trim() === ''
    },
    hasTags () {
      return this.tags?.length > 0
    }
  },
  methods: {
    addTag (tag) {
      if (tag) {
        this.$emit('addNewTag', tag)
        this.suggestions = []
        this.value = ''
        if (tag.parent_id === null) {
          this.$emit('parentLevelSelected', tag.id)
        }
      }
    },
    hideSuggestion () {
      this.suggestion = false
    },
    onInputFocus () {
      this.suggestion = true
      this.getData()
    },
    getData () {
      this.loading = true
      if (!this.lastParentId) {
        SearchParentTags(this.value, this.$props.type)
          .then((res) => {
            this.suggestions = res.tag
          }
          )
          .catch((err) =>
            console.log(err)
          ).finally(() => {
            this.loading = false
          })
        return
      }
      SearchChildrenTags(this.lastParentId, this.value, this.$props.type)
        .then((res) => {
          this.suggestions = res.tag
        }
        )
        .catch((err) =>
          console.log(err)
        ).finally(() => {
          this.loading = false
        })
    },
    validateInput () {
      this.showError = this.value.trim() === ''
    },
    showRootTags (event) {
      event.stopPropagation()
      this.suggestion = true
      this.loading = true
      SearchParentTags(this.value, this.$props.type)
        .then((res) => {
          this.suggestions = res.tag.filter(item => {
            if (!this.tagGroupData?.firstLevelParents?.includes(item.id)) {
              return {
                id: item.id,
                name: item.name
              }
            }
          })
          if (this.suggestions.length <= 0) {
            alert('all the parent level tags alredy attached')
          }
        })
        .catch((err) =>
          console.log(err)
        ).finally(() => {
          this.loading = false
        })
      this.$emit('parentLevelSelected', null)
    }
  },
  beforeDestroy () {
    this.$emit('update-tags', [])
  },
  created () {
    this.showSuggestion = debounce(this.getData, 300)
  }
}
</script>

  <style scoped lang="scss">
  .filter {
    margin-bottom: 0.8rem;
    display: flex;
    flex-wrap: wrap;

    input {
      padding: 0.85em;
      border: none;
    }

    span {
      margin-right: 3px;
    }
  }

  .tags {
    display: flex;
    background-color: var(--brand-color);
    padding: 0.6rem;
    font-size: small;
    border-radius: 0.3rem;
  }

  .searchInput {
    border-radius: 5px;
    border: 1px solid rgba(9, 8, 8, 0.9);
    border-radius: 4px;
  }

  .searchInput:focus {
    box-shadow: 0 0 0 1px var(--brand-color-1)
  }

  .resultBox {
    overflow: scroll;
    max-height: 12rem;
    padding: 2px 8px;
    opacity: 1;
    pointer-events: auto;
    z-index: 1000;
    background-color: var(--bg-color);
    width: 100%;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;

    li {
      list-style: none;
      padding: 8px 12px;
      width: 100%;
      border-radius: 3px;
    }

    li:last-child {
      padding-bottom: 3px;
    }

    li:hover {
      background: #efefef;
    }

  }

  .tag-modal-link {
    color: blue;
    text-decoration: underline;
    cursor: pointer;
  }

  .create-new-input {
    width: 260px;
    max-height: 700px;
    overflow: auto;
    padding: 10px;
  }

  .btn {
    margin-right: 12px;
  }

  .error-text {
    color: red;
    padding-top: 12px;
  }
  .error-test-default{
    color: red;
    padding-top: 12px;
    visibility: hidden;
  }

  </style>
