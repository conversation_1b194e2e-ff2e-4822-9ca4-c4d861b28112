.flex {
  display: flex;
}

.flex-grow {
  flex-grow: 1;
}

.wrap {
  flex-wrap: wrap;
}

.block {
  display: block;
}

.none {
  display: none;
}

.flex-end {
  display: flex;
  justify-content: flex-end;
}

.space-between {
  display: flex;
  justify-content: space-between;
}

.space-even {
  display: flex;
  justify-content: space-evenly;
}

.space-around {
  display: flex;
  justify-content: space-around;
}

.h-center {
  display: flex;
  justify-content: center;
}

.v-center {
  display: flex;
  align-items: center;
}

.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.column {
  flex-direction: column;
}

.gap-1{
 gap: 1rem
}

.h-100p {
  height: 100%;
}
.fw {
  width: 100%;
}
.fh {
  height: 100%;
}
.gap-1{
  gap: 1rem
}