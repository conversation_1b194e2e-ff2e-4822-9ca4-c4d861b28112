import { GQL } from '../graphQl'

export const getTimeLineDataQuery = () => GQL`query getTimeLineDataQuery($conditions: core_tasks_bool_exp, $skipResourceFetching : Boolean!) {
    core_tasks(where: $conditions) {
      id
      text:name
      slack
      description
      planned_start_date
      planned_end_date
      is_critical
      projected_end_date
      projected_start_date
      progress
      task_material_associations : associated_links_by_source_id(where:{target_feature_id:{_eq: 2}}) {
        metadata
        target_bom {
          id
          name
          bom_versions(where:{active: {_eq: true}}){
            total_cost
          }
          bom_items (
            where: {bom_version: {active: {_eq: true}}, core_material: {type: {_eq: 2}}}
          ) @skip (if: $skipResourceFetching) {
            core_material {
              id
              material_name
            }
          }
        }
      }
      core_project {
        id
        name
      }
      task_assignees {
        user_id
        assignee {
          first_name
          last_name
          id
        }
      }
      duration
      is_critical
      type
      level
      status
      parent_core_task{
        id
        name
      }
      tag_tasks {
        tag {
          id
          name
          parent_id
        }
      }
    }
  }`
export const updateTimeLineMutationQuery = () => GQL`mutation updateTimeLineMutationQuery(
  $taskId: uuid, 
  $startDate: timestamptz, 
  $endDate: timestamptz,
   $deletedAssignees: [Int!]) {
  update_core_tasks(
    where: {id: {_eq: $taskId}}
    _set: {planned_start_date: $startDate, planned_end_date: $endDate}
  ) {
    affected_rows
  }
   delete_task_assignee(
    where: {task_id: {_eq: $taskId},user_id: {_in: $deletedAssignees}}
  ) {
    affected_rows
  }
}`

export const getProjectsUserAsViewerQuery = () => GQL`query getProjectsUserAsViewerQuery($userId: uuid) {
  project_user_association(
    where: {role_id: {_eq: 4}, status: {_eq: 1}, user_id: {_eq: $userId}}
  ) {
    associated_project {
      id
      name
    }
  }
}`
export const addTaskAssigneesQuery = () => GQL`mutation addTaskAssigneesQuery($newAssigneeArray: [task_assignee_insert_input!]!) {
  insert_task_assignee(objects: $newAssigneeArray) {
    affected_rows
  }
}`

export const getCalenderDataByidsQuery = () => GQL`query getCalenderDataByidsQuery($projectIds: [uuid!]) {
  core_project_calendar(where: {project_id: {_in: $projectIds}}) {
    id
    name
    project_id
    working_hours
    working_hours_end
    working_hours_start
    calendar_working_days(where: {deleted: {_eq: false}}) {
      day_name
      work_day
      id
    }
    calendar_holidays {
      date
      calendar_id
    }
  }
}`

export const getProjectAssocResourcesQuery = () => GQL`query getProjectAssocResourcesQuery($coreBomConditions: core_bom_bool_exp) {
  core_material_master(
    where: {type: {_eq: 2}, bom_material_items: {bom_version: {active: {_eq: true}, core_bom: $coreBomConditions}}}
  ) {
    material_name
    id
    custom_material_id
  }
}`
