<template>
  <div class="beacon-admin">
    <div class="beacon-admin-nav py-2 v-center space-between">
      <h1>Tenant Dashboard</h1>
      <div class="s">
      </div>
    </div>
    <div class="beacon-admin-kpi py-3 flex">
      <!-- <div class="beacon-admin-kpi-blue p-4">
        <div class="beacon-admin-kpi-header">Total Projects</div>
        <div class="beacon-admin-kpi-value mt-3">100</div>
      </div> -->
      <div class="beacon-admin-kpi-blue mx-4 p-4">
        <div class="beacon-admin-kpi-header">Total Tenant</div>
        <div class="beacon-admin-kpi-value mt-3">{{ tenantList.length }}</div>
      </div>
      <div class="beacon-admin-kpi-brand p-4">
        <img src="~@/assets/images/bg-2.png" alt="" />
        <div class="beacon-admin-kpi-header">Activity</div>
        <div class="beacon-admin-kpi-value mt-3">
          Here Projects and Tenant Activity will come in graph format
        </div>
      </div>
    </div>
    <div class="beacon-admin-bar v-center space-between my-3">
      <h3 class="weight-500 xl">Company List</h3>
      <div class="v-center">
        <div class="input-group search">
          <input v-model="searchKeyword" type="text"  @input="performSearch(searchKeyword)" placeholder="Search by Comapny Name, GSTIN" />
        </div>
        <router-link to="/invite-tenant" >
          <button class="btn ml-3">Invite Tenant</button>
        </router-link>
      </div>
    </div>
     <copy-company-table
        :companyList="getSearchResult"
        :perPage="perPage"
        :pageNumber="pageNumber"
        :beaconAdmin= "true"
        :showHeader="true"
      />
    <pagination
      :length="getSearchResult.length"
      :perPage="perPage"
      :pageNumber="pageNumber"
      @selectPage="selectPage"
      class="mt-3 mx-2"
    />
  </div>
</template>

<script>
import { arraySearch } from '@/utils/array'
import copyCompanyTable from '@/components/common/copyCompanyTable.vue'
import Pagination from '../../components/common/pagination2.vue'
import { mapGetters } from 'vuex'
import { generateS3DownloadingUrl } from '@/api'

export default {
  components: { Pagination, copyCompanyTable },
  name: 'BeaconAdmin',
  data () {
    return {
      searchKeyword: '',
      pageNumber: 1,
      perPage: 10
    }
  },
  computed: {
    ...mapGetters(['tenantList']),
    getSearchResult () {
      return arraySearch(this.tenantList, this.searchKeyword,
        { fieldsToSearch: ['company_name', 'GSTIN'] })
    }
  },
  methods: {
    performSearch (keyword) {
      this.searchKeyword = keyword // Update search keyword
      this.pageNumber = 1 // Reset page number when searching
    },
    selectPage (pageNumber) {
      this.pageNumber = pageNumber
    },
    loadCompanyLogo () {
      const S3Objects = []
      this.tenantList.forEach((item) => {
        if (item.company_logo_blob_key !== null) {
          S3Objects.push({
            fileName: item.company_name,
            S3Key: item.company_logo_blob_key
          })
        }
      })
      generateS3DownloadingUrl({
        S3Objects: S3Objects
      }).then((res) => {
        const logoMap = {}
        for (const thumbnail of res.url) {
          logoMap[thumbnail.S3Key] = thumbnail.url
        }
        this.tenantList.forEach((company) => {
          const blobkey = company.company_logo_blob_key
          const url = logoMap[blobkey]
          if (url) {
            company.company_logo_blob_key = url
          }
        })
      }).catch((err) => {
        console.log(err)
      })
    }
  },
  mounted () {
    this.loadCompanyLogo()
  },
  watched: {
    tenantList () {
      this.loadCompanyLogo()
    }
  }
}
</script>

<style lang="scss" scoped >
.beacon-admin {
  height: 100%;
  &-nav {
    border-bottom: var(--border);
    h1 {
      font-weight: 500;
    }
  }
  &-kpi {
    align-items: stretch;
    color: var(--white);
    font-weight: 600;
    border-bottom: var(--border);
    &-blue {
      height: 180px;
      width: 225px;
      background-image: url("~@/assets/images/bg-1.png");
      border-radius: 6px;
    }
    &-brand {
      position: relative;
      flex-grow: 1;
      border-radius: 6px;
      background: linear-gradient(
        317.26deg,
        #dfa600 -26.68%,
        #ffc467 42.21%,
        #e8892a 90.37%
      );
      & img {
        position: absolute;
        bottom: 10px;
        right: 10px;
        height: 160px;
      }
      & .beacon-admin-kpi-value {
        font-size: 20px;
      }
    }
    &-header {
      font-size: 24px;
    }
    &-value {
      font-size: 64px;
    }
  }
}
</style>
