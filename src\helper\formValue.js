export const metaTypeIdMap = {
  1: 'string_value',
  2: 'string_value',
  3: 'int_value',
  7: 'date_value',
  8: 'time_value',
  12: 'bool_value',
  13: 'point_value'
}
export const GetFormValueMap = (form) => {
  const formValueMap = {}
  const formMetaData = form.forms_metadata_by_id || []
  const formAttachments = form.forms_attachments || []
  const formConfigLists = form.forms_config_lists || {}
  const formUserList = form.forms_user_lists || []
  const formCompanyList = form.forms_company_lists || []
  const bomData = form.forms_bom_lists
  const formTagsList = form.forms_tag_list || []
  const formContactList = form.forms_contact_list || []
  const formDocumentList = form.forms_document_list || []

  formMetaData.forEach((meta) => {
    const metaTypeId = meta.field_type_id
    const metaType = metaTypeIdMap[metaTypeId]
    const metaValue = meta[metaType]
    const metaFieldId = meta.field_id
    formValueMap[metaFieldId] = metaValue
  })

  // For attachments, we need to group them by field_id
  const attachmentFieldIds = formAttachments.map((attachment) => attachment.field_id)
  const attachmentFieldIdsSet = new Set(attachmentFieldIds)

  attachmentFieldIdsSet.forEach((fieldId) => {
    const attachmentValues = formAttachments.filter((attachment) => attachment.field_id === fieldId)
    formValueMap[fieldId] = attachmentValues
  })
  // for BOM List
  const bomFieldIds = bomData.map((bom) => bom.field_id)
  const setBomfieldIds = new Set(bomFieldIds)
  setBomfieldIds.forEach((fieldId) => {
    const bomvalues = bomData.filter((item) => item.field_id === fieldId)
    formValueMap[fieldId] = bomvalues
  })
  // For Custom List Value
  formConfigLists.forEach((config) => {
    const configFieldId = config.field_id
    const configValue = [config.custom_list_value_id]
    formValueMap[configFieldId] = configValue
  })

  // for Company List
  formCompanyList.forEach((company) => {
    const companyFieldId = company.field_id
    const companyValue = company.company_id
    formValueMap[companyFieldId] = companyValue
  })

  // for tag list
  formTagsList.forEach((tag) => {
    if (formValueMap[tag.field_id]) {
      formValueMap[tag.field_id].push(tag)
    } else {
      formValueMap[tag.field_id] = [tag]
    }
  })

  // For Users List
  const userFieldIds = formUserList.map((user) => user.field_id)
  const userFieldIdsSet = new Set(userFieldIds)

  userFieldIdsSet.forEach((fieldId) => {
    const userValues = formUserList.filter((user) => user.field_id === fieldId)
    formValueMap[fieldId] = userValues
  })

  // Form Materials List
  const formMaterials = form.forms_material_lists || []
  const formMaterialsFieldIds = formMaterials.map((material) => material.field_id)
  const formMaterialsFieldIdsSet = new Set(formMaterialsFieldIds)

  formMaterialsFieldIdsSet.forEach((fieldId) => {
    const materialValues = formMaterials.filter((material) => material.field_id === fieldId)
    formValueMap[fieldId] = materialValues
  })

  formContactList.forEach((contact) => {
    if (formValueMap[contact.field_id]) {
      formValueMap[contact.field_id].push(contact)
    } else {
      formValueMap[contact.field_id] = [contact]
    }
  })

  formDocumentList.forEach((document) => {
    if (formValueMap[document.field_id]) {
      formValueMap[document.field_id].push(document)
    } else {
      formValueMap[document.field_id] = [document]
    }
  })

  return formValueMap
}
