<template>
    <div class="user-groups fh">
      <div class="user-groups-bar v-center space-between px-5">
        <div class="flex v-center space-between">
          <img src="~@/assets/images/icons/arrow-back.svg" class="mr-3 pointer" width="30px" height="20px" alt="" @click="$router.go(-1)"/>
          <h1 class="weight-500 xl py-3 user-group-name elipsis-text" v-overflow-tooltip="userGroup.name">
            {{ userGroup.name }}
          </h1>
        </div>
      </div>
      <div class="fh center" v-if="loading">
        <loading-circle />
        </div>
      <div class="user-groups-container px-5">
        <div class="user-groups-description mt-3">
            <label for="description" class="l">Description:</label>
          <textarea
            disabled
            v-model="userGroup.description"
            placeholder="Description"
            class="form-control"
            rows="3"
          ></textarea>
          <div class="dtx-table mt-3">
            <h3 class="mb-3"> Members </h3>
        <table>
          <thead>
            <tr>
              <th>No.</th>
              <th>Name</th>
              <th>Email</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(row, index) in  userGroup.core_user_group_members" :key="row.id">
                <td>{{ index + 1 }}</td>
                <td>{{ row.core_user.first_name + ' ' + row.core_user.last_name }}</td>
                <td>{{ row.core_user.email }}</td>
            </tr>
          </tbody>
        </table>
        <!-- <div class="pagination-footer space-between">
          <pagination class="mt-4" :length="totalCount" :pageNumber="pageNumber" :perPage="perPage"
            @selectPage="selectPage" v-if="totalCount>10"/>
          <span class="pagination-footer-total mt-4"  v-if="filteredUserGroups.length > 0">Total User Groups : &nbsp; <b> {{ totalCount }}</b></span>
        </div> -->
       </div>
        </div>
      </div>
    </div>
  </template>

<script>
import { GetUserGroupDetail } from '@/api'
import { alert } from '@/plugins/notification'
import LoadingCircle from '@/components/common/loadingCircle.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'user-groups-detail',
  components: {
    LoadingCircle
  },
  data: () => ({
    loading: false,
    userGroup: {
      name: '',
      description: '',
      core_user_group_members: []
    }
  }),
  computed: {
    ...mapGetters(['user', 'isOnProjectLevel']),
    showEditButton () {
      return (
        this.user.tenantLevelRole === 'ADMIN'
      )
    }
  },
  mounted () {
    this.id = this.$route.params.id
    this.loading = true
    GetUserGroupDetail(this.id).then((res) => {
      this.userGroup = res.core_user_group_by_pk
    }).catch(() => {
      alert('Unable to fetch user group details')
    }).finally(() => {
      this.loading = false
    })
  }
}
</script>

  <style lang="scss" scoped>
  .user-group-name {
    max-width: 90%;
  }
  textarea {
    width: 100%;
    height: 50px;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 10px;
    font-size: 16px;
    font-family: Arial, sans-serif;
    outline: none;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease-in-out;
    resize: none;
  }

  textarea:focus {
    border-color: var(--bg-color);
    box-shadow: 2px 2px 10px var(--bg-color);
  }

  .user-groups {
    &-bar {
      height: 60px;
      background: var(--bg-color);
      border-bottom: var(--border);
    }
  }
  </style>
