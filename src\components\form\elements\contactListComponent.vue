<template>
  <div>
    <label
      >{{ data.caption }}:
    </label>
    <div class="copy-dtx-table contact-list-table">
      <table>
        <thead>
          <tr>
            <th>Name</th>
            <th>Email</th>
            <th>Phone</th>
            <th>Department</th>
            <th>Designation</th>
          </tr>
        </thead>
        <tbody class = "">
          <tr v-for="(item, index) in value" :key="index">
            <td>{{ item.name }}</td>
            <td>{{ item.email ?? '--' }} </td>
            <td>{{ item.phone ?? '--'}} </td>
            <td>{{ item.department_custom_list_value ? item.department_custom_list_value.name : '--' }}</td>
            <td>{{ item.description ?? '--' }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'contactListComponent',
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    value: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style lang="scss" scoped>

.contact-list-table {
    max-height: 50vh;
    overflow-y: auto;
}
</style>
