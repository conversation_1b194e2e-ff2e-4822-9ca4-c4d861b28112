<template>
  <section>
  <div>
    <!-- loading circle -->
  </div>
  <div class="dropdown" @mouseleave="emitSelected">
    <button class="dropdown-toggle" :style="{backgroundColor:background, height:btnHeight}" @click="showDropdown = !showDropdown">
      {{ label }}
    </button>
    <div class="dropdown-content" v-show="showDropdown && !disabled">
      <input
        type="text"
        v-model="search"
        placeholder="Search..."
        @input="filterOptions"
      />
      <label class="flex v-center gap-1 mt-3">
        <input class="" type="checkbox" @click="selectAll" /> <span>Select All</span>
      </label>
      <label v-for="(option, index) in options" :key="index" class=" flex v-center gap-1">
        <input
          type="checkbox"
          class="option"
          :value="option.value"
          :data-label="option.label"
          v-model="selectedOptions"
        />
       <div class="option-label elipsis-text" v-overflow-tooltip> {{ option.label }}</div>
      </label>
    </div>
    <div v-if="selectedOptions.length" class="selectedCount">
      {{ selectedOptions.length }}
    </div>
  </div>
</section>
</template>

<script>
export default {
  name: 'multiSelectDropdown',
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    initiallySelected: Array,
    options: Array,
    label: {
      type: String,
      default: 'Select Options'
    },
    background: {
      type: String,
      default: 'var(--brand-light-color)'
    },
    btnHeight: {
      type: String,
      default: '40px'
    }

  },
  data () {
    return {
      search: '',
      selectedOptions: [],
      showDropdown: false
    }
  },
  mounted () {
    if (this.initiallySelected) {
      this.selectedOptions = this.initiallySelected
    } else { this.selectedOptions = [] }
  },
  methods: {
    emitSelected () {
      if (this.showDropdown) { // showdropdown is there means to prevent false emiting of data
        this.$emit('selected', this.selectedOptions)
      }
      this.showDropdown = false
    },
    filterOptions () {
      const filter = this.search.toUpperCase()
      const options = this.$el.querySelectorAll('.option')
      options.forEach((option) => {
        const text = option.dataset.label
        option.parentElement.style.display =
          text.toUpperCase().indexOf(filter) > -1 ? 'flex' : 'none'
      })
    },
    selectAll () {
      const selectAllCheckbox = this.$el.querySelector(
        ".dropdown-content input[type='checkbox']:first-child"
      )
      this.selectedOptions = selectAllCheckbox.checked
        ? this.options.map((opt) => opt.value)
        : []
    }
  },
  watch: {
    selectedOptions () {
      const selectAllCheckbox = this.$el.querySelector(
        ".dropdown-content input[type='checkbox']:first-child"
      )
      selectAllCheckbox.checked =
        this.selectedOptions?.length === this.options?.length
    },
    options () {
      // this.selectedOptions = []
    },
    initiallySelected (newVal, oldVal) {
      this.selectedOptions = [...newVal]
      this.search = ''
    },
    showDropdown () {
      if (this.showDropdown && this.search) {
        this.filterOptions()
      }
    }
  }
}
</script>

  <style lang="scss" scoped>
/* Add your CSS styles here */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  background-color: var(--brand-light-color);
  border: 1px solid var(--brand-color);
  // padding: 8px 12px;
  cursor: pointer;
  width: 200px;
  border-radius: 0.3em;
}

.dropdown-content {
  position: absolute;
  background-color: #fff;
  border: 1px solid #ccc;
  box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
  padding: 8px;
  z-index: 2;
  width: 200px;
  max-height: 365px;
  overflow: scroll;
  & input{
    padding: 5px;
    border-radius: 4px;
    border-color: rgb(30, 27, 27,.3);
    border-style: solid;
  }
  & input:focus{
    border-color: rgb(30, 27, 27,.7);
    border-style: solid;
  }
}

.dropdown-content label {
  margin-bottom: 5px;
}

.selectedCount {
  border-radius: 50%;
  position: absolute;
  top: -4px;
  padding: 3px 10px;
  background: var(--brand-color);
  right: -9px;
}

#searchInput {
  width: 100%;
  padding: 8px;
  margin-bottom: 8px;
}
.option-label{
flex-grow: 1;
}
</style>
