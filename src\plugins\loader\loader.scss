.asw-loader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--black);
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
  backdrop-filter: blur(10px);
  transition: opacity 0.3s ease-in-out;

  .three-quarter-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid var(--brand-color);
    border-top: 5px solid transparent;
    border-radius: 50%;
    animation: spin .8s linear 0s infinite;
  }


@keyframes spin {
  from {
    transform: rotate(0);
  }
  to{
    transform: rotate(359deg);
  }
}
}