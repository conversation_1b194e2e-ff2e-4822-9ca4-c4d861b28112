<template>
       <div
      ref="schedulerContainer"
      id="schedulerContainer"
      class="schedulerContainer"
      style="width: 100%"
    ></div>
</template>
<script>
import '@/assets/scheduler/dhtmlxscheduler'

let scheduler
export default {
  name: 'TimeLineView',
  props: {
    primaryColumnData: { type: Object, required: true, default: () => {} }, // users list in hashmap
    timeLineData: { type: Array, required: true, default: () => [] },
    currentDate: { type: Date },
    viewModeParent: { type: String, default: 'weekly' },
    view: {
      type: String,
      default: 'users'
    }
  },
  data () {
    return {
      groupColors: {
        1: '#20B2AA', // LightSeaGreen
        2: '#008B8B', // DarkCyan
        3: '#2E8B57', // SeaGreen
        4: '#00CED1', // DarkTurquoise
        5: '#008000', // Green
        6: '#ADFF2F', // GreenYellow
        7: '#5F9EA0', // CadetBlue
        8: '#9ACD32', // YellowGreen
        9: '#40E0D0', // Turquoise
        10: '#48D1CC', // MediumTurquoise
        11: '#66CDAA', // MediumAquaMarine
        12: '#7FFF00', // Chartreuse
        13: '#00FF7F', // SpringGreen
        14: '#D2691E', // Chocolate
        15: '#00FA9A', // MediumSpringGreen
        16: '#CD853F', // Peru
        17: '#3CB371', // MediumSeaGreen
        18: '#008080', // Teal
        19: '#556B2F', // DarkOliveGreen
        20: '#A0522D', // Sienna
        21: '#00FF00', // Lime
        22: '#228B22', // ForestGreen
        23: '#808000', // Olive
        24: '#008000', // Green
        25: '#8FBC8B', // DarkSeaGreen
        26: '#008B8B', // DarkCyan
        27: '#32CD32', // LimeGreen
        28: '#20B2AA', // LightSeaGreen
        29: '#006400', // DarkGreen
        30: '#CD853F' // Peru
      },
      viewMode: this.viewModeParent,
      primaryColumnList: Object.values(this.primaryColumnData)
    }
  },
  mounted () {
    this.initScheduler(this.primaryColumnList, this.timeLineData, this.currentDate)
  },
  computed: {
  },
  methods: {

    initScheduler (userData, taskData, date = new Date()) {
      if (scheduler) {
        scheduler.destructor()
        scheduler = null
      }
      scheduler = window.Scheduler.getSchedulerInstance()
      scheduler.config.readonly = true // it is make the gant is readonly
      scheduler.plugins({
        timeline: true,
        tooltip: true,
        minical: true,
        drag_between: false
      })

      scheduler.attachEvent('onLoad', (id, ev, render) => {
      })
      // this is configuration for tooltip for task
      scheduler.config.className = 'dhtmlXTooltip tooltip'
      scheduler.config.timeout_to_display = 50
      scheduler.config.timeout_to_hide = 50
      scheduler.config.delta_x = 15
      scheduler.config.delta_y = -20
      scheduler.config.date_format = '%d-%m-%Y'

      const format = scheduler.date.date_to_str('%d-%m-%Y')
      scheduler.templates.tooltip_text = function (start, end, event) {
        start = new Date(new Date(start).setUTCHours(0, 0, 0, 0))
        end = new Date(new Date(end).setUTCHours(0, 0, 0, 0))
        return '<b>Task name</b> ' + event.text + '<br/><b>Start date:</b> ' +
    format(start) + '<br/><b>End date:</b> ' + format(end) + '<br/><b>Project Name:</b> ' + event.project_name + '</br/><b>Parent Task: </b>' + event.parentTask
      }
      // window.addEventListener('DOMContentLoaded', function () {
      scheduler.locale.labels.timeline_tab = 'Timeline'
      scheduler.locale.labels.section_custom = 'Section'
      scheduler.config.details_on_create = true
      scheduler.config.details_on_dblclick = true
      var sections = userData
      const monthlyClass = this.viewMode === 'monthly' ? 'activeButton' : null
      const weeklyClass = this.viewMode === 'weekly' ? 'activeButton' : null
      const configHeaderArray = [
        // 'day',
        // 'week',
        { html: 'Monthly', click: () => { this.changeMode('monthly') }, css: monthlyClass },
        { html: 'Weekly', click: () => { this.changeMode('weekly') }, css: weeklyClass },
        {
          view: 'minicalendar',
          click: function () {
            if (scheduler.isCalendarVisible()) {
              scheduler.destroyCalendar()
            } else {
              scheduler.renderCalendar({
                position: this,
                date: scheduler.getState().date,
                navigation: true,
                handler: function (date, calendar) {
                  scheduler.setCurrentView(date)
                  scheduler.destroyCalendar()
                }
              })
            }
          }
        },
        'prev',
        { view: 'date', css: 'date_box' },
        'next',
        'spacer'
      ]
      if (this.$props.view === 'users') {
        configHeaderArray.push({ html: 'Edit', click: () => { this.activateEditMode() }, css: 'edit-button' })
      }
      scheduler.config.header = configHeaderArray
      // create a timeline in the scheduler
      if (this.viewMode === 'monthly') {
        scheduler.createTimelineView({
          name: 'timeline',
          render: 'bar',
          month: 12,
          folder_dy: 20,
          x_unit: 'month',
          x_date: '%F',
          x_step: 1,
          x_size: 12,
          x_start: 0,
          x_length: 12,
          y_unit: sections,
          y_property: 'section_id'
        })
      } else {
        scheduler.createTimelineView({
          name: 'timeline',
          render: 'bar',
          days: 7,
          folder_dy: 20,
          x_unit: 'day',
          x_date: '%D %j %F',
          x_step: 1,
          x_size: 7,
          x_start: 0,
          x_length: 7,
          y_unit: sections,
          y_property: 'section_id',
          round_position: true // To make an event occupy the entire cell width, no matter how long this event lasts, use the round_position parameter

        })
      }

      // modal
      scheduler.config.lightbox.sections = [
        { name: 'description', height: 50, map_to: 'text', type: 'textarea', focus: true },
        { name: 'custom', height: 30, type: 'select', options: sections, map_to: 'section_id' },
        { name: 'time', height: 72, type: 'time', map_to: 'auto' }
      ]
      // to setting the color based on project
      scheduler.attachEvent('onEventLoading', (ev) => {
        ev.color = ev.projectColor
        ev.textColor = 'white'
        return true
      })

      scheduler.init('schedulerContainer', new Date(date), 'timeline')
      scheduler.parse(taskData) // task data
    },
    activateEditMode () {
      this.$emit('editModeOn', { date: scheduler._date, viewMode: this.viewMode })
    },
    changeMode (mode) {
      const date = scheduler._date
      this.viewMode = mode
      scheduler.destructor()
      this.initScheduler(this.primaryColumnList, this.timeLineData, date)
    }
  },
  watch: {
    timeLineData () {
      this.initScheduler(this.primaryColumnList, this.timeLineData, scheduler._date)
    }
  }
}
</script>
<style lang="scss" scoped>
@import "../../assets/scheduler/dhtmlxscheduler.css";
.dhx_cal_tab.active {
  background-color: var(--brand-light-color);
  border: 1px solid var(--brand-color);
  text-shadow: none;
}
.dhx_cal_navline {
  z-index: 2;
}
.main {
  overflow: hidden !important;
}
.dhx_cal_container {
  overflow: hidden;
}
.date-range {
  margin: 0;
  input {
    height: 100%;
    border: 0.7px solid var(--brand-color);
    border-radius: 5px;
    padding-inline: 4px;
    background: var(--brand-light-color);
  }
}
.edit-button {
  margin: 0 3rem !important;
  background-color: rgb(38 32 32);
  color: white;
  border-radius: 5px;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}
.date_box{
flex-grow: 0 !important;
margin-inline: 10px;
}
.dhx_cal_prev_button{
margin-left:10px
}
.schedulerContainer{
      height: calc(100vh - 260px);
    }
</style>
