class TagNode {
  constructor ({ name = null, parentId = null, id = null }) {
    this.name = name
    this.parentId = parentId
    this.isEndOfTag = true
    this.id = id
    this.children = {}
  }
}

export class TagTrie {
  constructor () {
    this.root = new TagNode({})
    this._tagArray = []
    this.tagArray_material = {
      tagArray: [],
      firstLevelParents: []
    }
  }

  insertTag (tag, level = this.root) {
    if (level.children[tag.id] === undefined && tag.parentId === level.id) {
      level.children[tag.id] = new TagNode(tag)
      level.isEndOfTag = false
      this._tagArray.push(tag)
      return
    }
    for (const child of Object.values(level.children)) {
      this.insertTag(tag, child)
    }
  }

  _generateTagArray (level = this.root) {
    for (const child of Object.values(level.children)) {
      this._tagArray.push({
        name: child.name,
        parentId: child.parentId,
        id: child.id
      })
      this._generateTagArray(child)
    }
    return this.root
  }

  _generateTreeFromUnorderedList (tags) {
    const mappedArr = new Map()

    // Build a hash table and map items to objects
    for (const tag of tags) {
      // to ignore in case of duplicates
      if (!mappedArr.has(tag.id)) {
        mappedArr.set(tag.id, new TagNode(tag))
      }
    }

    // looping over the hashmap
    for (var [id, value] of mappedArr) {
      const parentId = value.parentId // getting the parent id of the item

      /* If the element is not at the root level,
         add it to its parent array of children */
      if (parentId) {
        const parentItem = mappedArr.get(parentId)
        parentItem.isEndOfTag = false
        value.isEndOfTag = true
        parentItem.children[id] = value
      // eslint-disable-next-line brace-style
      }

      // If the element is at the root level, directly add to the tree
      else {
        this.root.children[id] = value
        this.root.isEndOfTag = false
      }
    }
  }

  deleteTagById (id, current = this.root) {
    for (const child of Object.values(current.children)) {
      if (child.id === id) {
        delete current.children[child.id]
        this._tagArray = []
        this._generateTagArray()
        return
      }
      this.deleteTagById(id, child)
    }
  }

  deleteTagById2 (id, current = this.root) {
    for (const child of Object.values(current.children)) {
      if (child.id === id) {
        delete current.children[child.id]
        this._tagArray = []
        this._generateTagArray()
        return
      }
      this.deleteTagById2(id, child)
    }
  }

  clearAll () {
    this.root.children = {}
    this._tagArray = []
  }

  getTagArray () {
    return JSON.parse(JSON.stringify(this._tagArray))
  }

  groupTagInArray () {
    this._dfs(this.root)
    return JSON.parse(JSON.stringify(this.tagArray_material))
  }

  _dfs (node) {
    if (!node) {
      return
    }
    this.tagArray_material.tagArray = []
    let childArray = []
    if (node.parentId === null && node.id === null) {
      this._dfs(node.children)
    } else {
      for (const child of Object.values(node)) {
        tagDataIntoArray(this, child)
        this.tagArray_material.tagArray.push(JSON.parse(JSON.stringify(childArray)))
        childArray = []
      }
    }

    function tagDataIntoArray (context, tagData) {
      if (tagData.parentId === null) {
        context.tagArray_material.firstLevelParents.push(tagData.id)
      }
      childArray.push({
        name: tagData.name,
        parentId: tagData.parentId,
        id: tagData.id,
        isEndOfTag: tagData.isEndOfTag
      })
      const child = Object.values(tagData.children)[0]
      if (child) {
        tagDataIntoArray(context, child)
      }
    }
  }
}
// export const tagTrie = new TagTrie()
