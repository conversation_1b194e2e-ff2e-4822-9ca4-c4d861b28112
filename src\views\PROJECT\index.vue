<template>
  <div class="project">
    <div class="project-bar v-center space-between">
      <h2 class="xxxl weight-500">{{ currentProject.name }}</h2>
      <div class="v-center">
        <!-- <button class="btn btn-black" @click="openAddUserDrawer" v-if="isTenantAdminOrCollaborator">
          Add User
        </button> -->
        <button class="btn btn-black mx-2" @click="editProject" v-if="isTenantAdminOrCollaborator">
          Edit Project
        </button>
      </div>
    </div>
    <div class="project-row">
      <div class="weight-500">Longitude:</div>
      <div>{{ getLocationObject.longitude || "--" }}</div>
    </div>
    <div class="project-row">
      <div class="weight-500">Latitude:</div>
      <div>{{ getLocationObject.latitude || "--" }}</div>
    </div>
    <div class="project-row">
      <div class="weight-500">Created On:</div>
      <div>{{ currentProject.created_on | timeStampToDateTime }}</div>
    </div>
    <div class="project-row">
      <div class="weight-500">Created By:</div>
      <div>{{ getUserName(currentProject.created_by) }}</div>
    </div>
    <div class="project-row">
      <div class="weight-500">Updated On:</div>
      <div>{{ currentProject.updated_on | timeStampToDateTime }}</div>
    </div>
    <div class="project-row">
      <div class="weight-500">Updated By:</div>
      <div>{{ getUserName(currentProject.updated_by) }}</div>
    </div>
    <div class="project-row">
      <div class="weight-500">Users:</div>
    </div>
    <div class="project-user-table">
      <div class="project-user-table__header">
        <div class="project-user-table__header__item">Name</div>
        <div class="project-user-table__header__item">Email</div>
        <div class="project-user-table__header__item">Role</div>
        <!-- <div class="project-user-table__header__item" v-if="isTenantAdminOrCollaborator">Action</div> -->
      </div>
      <div class="project-user-table__body">
        <template v-for="(user, index) in currentProject.project_user_associations">
          <div
            class="project-user-table__body__row"
            :key="user.id"
            v-if="user.status !== 3"
          >
            <div class="project-user-table__body__row__item v-center">
              <avatar size="24px" :id="user.user_id" class="mr-2" />
              {{ user.associated_user.first_name + ' ' + user.associated_user.last_name}}
            </div>
            <div class="project-user-table__body__row__item">
             {{ user.associated_user.email }}
            </div>
            <div v-if="userEditObject.editIndex === index" class="project-user-table__body__row__item">
              <select v-model="userEditObject.userRole">
                <option v-for="(role, key) in roleMap" :key="key" :value="key">{{ role }}</option>
              </select>
            </div>
            <div v-else class="project-user-table__body__row__item">
              {{ roleMap[user.role_id] }}
            </div>
            <!-- <div class="project-user-table__body__row__item" v-if="isTenantAdminOrCollaborator && !user.system_added">
              <img v-if="userEditObject.editIndex === index" v-tooltip="'Save'" src="~@/assets/images/icons/save-icon.svg" @click="updateUserRole" >
              <img v-else v-tooltip="'Edit User Role'" src="~@/assets/images/edit-icon.svg" alt="" @click="editUserRole(user, index)">
              <img v-tooltip="'Remove User'" src="~@/assets/images/delete-icon.svg" v-if="isTenantAdmin" @click="deleteUser(user)" alt="">
            </div> -->
          </div>
        </template>
      </div>
    </div>
    <div class="drawer" :class="drawer ? 'open' : 'close'">
      <project-form
        ref="projectForm"
        :isUpdate="true"
        @cancel="closeDrawer"
        @update="updateProject"
        :drawer="drawer ? true : false"
        :closeOnOutsideClick="true"
        :buttonDisabled="buttonDisabled"
      ></project-form>
    </div>
    <div
      v-if="currentProject.id"
      class="drawer"
      :class="addUserDrawer ? 'open' : 'close'"
    >
      <add-user-to-project
        :projectId="currentProject.id"
        @close="addUserDrawer = false"
      ></add-user-to-project>
    </div>
  </div>
</template>

<script>
import {
  GetCurrentProjectData,
  UpdateProjectById,
  DeleteUserFromProjectData,
  UpdateUserRoleForProjectData
} from '@/api'
import { mapGetters } from 'vuex'
import { timeStampToDateTime } from '@/filters/dateFilter'
import Avatar from '../../components/common/avatar.vue'
import ProjectForm from '../../components/manage/projectForm.vue'
import AddUserToProject from '../../components/manage/addUserToProject.vue'
import Config from '@/config.js'
import ConfirmationDialog from '@/plugins/confirmationDialog'
import { alert } from '@/plugins/notification'

export default {
  name: 'Project',
  filters: {
    timeStampToDateTime
  },
  components: { ProjectForm, AddUserToProject, Avatar },
  data () {
    return {
      drawer: false,
      addUserDrawer: false,
      roleMap: Config.userRole,
      userEditObject: {
        editIndex: -1,
        userId: '',
        userRole: ''
      },
      buttonDisabled: false // this for disabling update button  of project updation
    }
  },
  computed: {
    ...mapGetters(['currentProject', 'getUserById']),
    ...mapGetters(['user', 'getUserById']),
    getLocationObject () {
      const location = this.currentProject.location
        ? this.currentProject.location
          .replace('(', '')
          .replace(')', '')
          .split(',')
        : []
      return {
        latitude: location[0] || '',
        longitude: location[1] || ''
      }
    },
    isTenantAdminOrCollaborator () {
      return (
        this.user.tenantLevelRole === 'ADMIN' || this.user.projectLevelRole === 'ADMIN')
    },
    isTenantAdmin () {
      return (
        this.user.tenantLevelRole === 'ADMIN' || this.user.projectLevelRole === 'ADMIN')
    }
  },
  methods: {
    editUserRole (user, index) {
      this.userEditObject = {
        editIndex: index,
        userId: user.user_id,
        userRole: user.role_id
      }
    },
    updateUserRole () {
      const { userId, userRole } = this.userEditObject
      UpdateUserRoleForProjectData({
        user_id: userId,
        role_id: userRole
      })
        .then((res) => {
          this.getCurrentProject()
          this.$notify.success('User role updated successfully')
          this.userEditObject = {
            editIndex: -1,
            userId: '',
            userRole: ''
          }
        })
        .catch(() => {
          this.$notify.alert('Something went wrong')
        })
    },
    deleteUser (user) {
      ConfirmationDialog(
        'Are you sure you want to remove this user?',
        (res) => {
          if (res) {
            DeleteUserFromProjectData({
              user_id: user.user_id
            })
              .then((res) => {
                this.getCurrentProject()
                this.$notify.success('User removed successfully')
              })
              .catch(() => {
                this.$notify.alert('Something went wrong')
              })
          }
        }
      )
    },
    openAddUserDrawer () {
      this.addUserDrawer = true
    },
    getUserName (id) {
      const user = this.getUserById(id)
      return user
        ? `${user.associated_user?.first_name || ''} ${
            user.associated_user?.last_name || ''
          }`
        : '--'
    },
    openDrawer () {
      this.drawer = true
    },
    closeDrawer () {
      this.drawer = false
    },
    editProject () {
      this.$refs.projectForm.name = this.currentProject.name
      this.$refs.projectForm.latitude = this.getLocationObject.latitude
      this.$refs.projectForm.longitude = this.getLocationObject.longitude
      this.$refs.projectForm.selectedTenant = this.currentProject?.company
      this.$refs.projectForm.startDate = this.currentProject?.planned_start_date
      this.$refs.projectForm.endDate = this.currentProject?.planned_end_date
      this.$refs.projectForm.cost = this.currentProject?.project_cost
      this.$refs.projectForm.revenue = this.currentProject?.project_revenue
      this.$refs.projectForm.address = this.currentProject?.address?.address
      this.$refs.projectForm.state = this.currentProject?.address?.state
      this.$refs.projectForm.city = this.currentProject?.address?.city
      this.$refs.projectForm.pincode = this.currentProject?.address?.pincode
      this.openDrawer()
    },
    updateProject (obj) {
      this.buttonDisabled = true
      const body = {
        name: obj.name,
        planned_end_date: obj?.planned_end_date,
        planned_start_date: obj?.planned_start_date,
        project_cost: obj?.project_cost,
        project_revenue: obj.project_revenue,
        address: obj.address,
        location:
          obj.latitude.length && obj.longitude.length
            ? `(${obj.latitude}, ${obj.longitude})`
            : null
      }
      UpdateProjectById({ id: this.currentProject.id, data: body }).then((res) => {
        this.closeDrawer()
        this.getCurrentProject()
        this.$store.dispatch('fetchProjectList')
        this.$notify.success('Project Updated Successfully')
        this.buttonDisabled = false
      }).catch(() => {
        alert('Project updation failed!')
        this.buttonDisabled = false
      })
    },
    getCurrentProject () {
      GetCurrentProjectData().then((res) => {
        this.$store.commit('setCurrentProject', res?.core_projects?.[0])
      })
    }
  }
}
</script>

<style lang="scss" scoped >
.project {
  margin: -12px;
  height: 100%;
  &-bar {
    background-color: var(--bg-color);
    padding: 20px;
  }
  &-row {
    display: grid;
    grid-template-columns: 120px 1fr;
    grid-gap: 20px;
    margin: 10px 0;
    font-size: 16px;
    padding: 0 20px;
  }
  &-user-table {
    margin: 20px 0;
    &__header {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 80px;
      margin: 0 20px;
      font-size: 14px;
      font-weight: 500;
      border-bottom: var(--border);
      background-color: var(--brand-color);
      &__item {
        padding: 8px;
      }
    }
    &__body {
      &__row {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 80px;
        margin: 0 20px;
        font-size: 13px;
        border-bottom: var(--border);
        &__item {
          padding: 8px;
          & > img {
            cursor: pointer;
            width: 20px;
            margin-right: 4px;
          }
        }
      }
    }
  }
}
</style>
