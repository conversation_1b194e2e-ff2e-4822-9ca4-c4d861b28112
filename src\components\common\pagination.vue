<template>
  <div class="pagination mt-4" v-if="length">
    <button :disabled="pageNumber === 1" @click="$emit('selectPage', 1)">
      &#60;&#60;
    </button>
    <button
      :disabled="pageNumber === 1"
      @click="$emit('selectPage', pageNumber - 1)"
    >
      &#60;
    </button>
    <div v-for="num in numberArray" :key="num.id">
      <span v-if="num === -1"> ... </span>
      <button
        :class="{
          active: num === pageNumber,
        }"
        v-else
        @click="$emit('selectPage', num)"
      >
        {{ num }}
      </button>
    </div>
    <button
      :disabled="pageNumber === totalPage"
      @click="$emit('selectPage', pageNumber + 1)"
    >
      &#62;
    </button>
    <button
      :disabled="pageNumber === totalPage"
      @click="$emit('selectPage', totalPage)"
    >
      &#62;&#62;
    </button>
  </div>
</template>

<script>
export default {
  name: 'Pagination',
  props: {
    length: {
      type: Number,
      default: 0
    },
    pageNumber: {
      type: [Number, String],
      default: 1
    },
    perPage: {
      type: [Number, String],
      default: 10
    }
  },
  computed: {
    totalPage () {
      return (
        parseInt(this.length / this.perPage) +
        (this.length % this.perPage > 0 ? 1 : 0)
      )
    },
    numberArray () {
      const totalButtonCount = this.totalPage
      if (totalButtonCount <= 3) {
        return [...Array(totalButtonCount).keys()].map((i) => i + 1)
      }
      if (this.pageNumber === 1) {
        return [1, 2, -1, totalButtonCount]
      }
      if (this.pageNumber === 2) {
        return [1, 2, 3, -1, totalButtonCount]
      }
      if (this.pageNumber === totalButtonCount) {
        return [1, -1, totalButtonCount - 1, totalButtonCount]
      }
      if (this.pageNumber === totalButtonCount - 1) {
        return [
          1,
          -1,
          totalButtonCount - 2,
          totalButtonCount - 1,
          totalButtonCount
        ]
      }
      return [
        -1,
        this.pageNumber - 1,
        this.pageNumber,
        this.pageNumber + 1,
        -1
      ]
    }
  }
}
</script>

<style scoped lang="scss" >
.pagination {
  display: flex;
  background: #ffc56761;
  padding: 0.5em 0.5em 0.5em;
  border: 1px solid var(--brand-color);
  border-radius: 0.2em;
  width: fit-content;
  & button {
    cursor: pointer;
    background: transparent;
    border: none;
    margin: 0 6px;
    padding: 0;
    font-weight: 500;
    &.active {
      text-decoration: underline;
      font-weight: 600;
    }
  }
  & > button {
    margin: 0 3px;
  }
}
</style>
