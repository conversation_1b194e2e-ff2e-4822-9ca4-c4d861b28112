<template>
  <div class="workflow">
    <div class="workflow-bar v-center space-between px-3">
      <h1 class="weight-500 xxl">Workflow</h1>
    </div>
    <div class="workflow-activity-bar v-center space-between px-3">
      <div class="flex v-center space-between">
        <img
          src="~@/assets/images/icons/arrow-back.svg"
          class="mr-2 workflow-activity-bar-backButton"
          width="30px"
          alt=""
          @click="$router.go(-1)"
        />
        <h2 class="weight-500 xl" v-if="!editMode"><div class="workflow-heading elipsis-text" v-overflow-tooltip>{{ templateName }}</div></h2>
        <input
          v-else
          type="text"
          class="workflow-activity-bar-template-name"
          placeholder="Template name"
          v-model="templateName"
          @input="changeColor"
          maxlength="50"
          ref="templateNameInput"
        />
        <select
          v-if="templateId"
          name=""
          v-model="selectedVersionId"
          class="workflow-version-selector mx-2"
        >
          <option
            v-for="version in versionList"
            :key="version.id"
            :value="version.id"
            :selected="version.active"
            :disabled="
              (selectedVersionId === 'draft' && version.id !== 'draft') ||
              (!version.draft &&
                selectedVersionId === draftVersionId &&
                editMode)
            "
          >
            Version - {{ version.version_no }}
            {{ version.id === "draft" ? " (Checkout)" : null }}
            {{ version.active ? " (Active)" : null }}
            {{ version.draft ? " (Draft)" : null }}
          </option>
        </select>
        <div class="mx-5"       data-validation="feature" v-if="showSelectFeature">
          <span>Select Feature : </span>
          <select
            name=""
            v-model="selectedFeatureId"

            class="workflow-version-selector mx-2"
            ref="selectFeature"
            @change="changeFeature"
          >
            <option
              v-for="feature in Object.keys(config.CORE_FEATURES)"
              :key="config.CORE_FEATURES[feature]"
              :value="config.CORE_FEATURES[feature]"
              :selected="config.CORE_FEATURES[feature] === selectedFeatureId"
            >
              {{ feature }}
            </option>
          </select>
        </div>
        <div class="mx-5 v-center" v-else>
          <!-- <span >Selected Feature : </span> -->
          <span class="workflow-version-selector">{{
            config.CORE_FEATURES_INVERTED[selectedFeatureId]
          }}</span>
        </div>
      </div>

      <div v-if="editPermission">
        <button
          class="btn btn-black mx-1"
          v-if="!editMode"
          @click="enableEditMode"
        >
          Edit
        </button>
        <button class="btn btn-black mx-1" v-else @click="cancelEditMode">
        Cancel
      </button>
      <button class="btn mx-1" v-if="editMode" :disable="disableSaveButton" @click="saveWorkflow(false)">
        Save
      </button>
      <button
        class="btn mx-1"
        v-if="editMode && templateId"
        :disable="disableSaveButton"
        @click="saveWorkflow(true)"
      >
        Save as Draft
      </button>
      </div>
    </div>
    <div class="workflow-designer">
      <div id="stencil" v-show="editMode"></div>
      <div id="graph-container"></div>
    </div>
    <modal
      @close="modalOpen = false"
      :open="modalOpen"
      :title="
        selectedElement?.shape === 'node-rect'
          ? 'Update step features'
          : 'Update name'
      "
      :closeOnOutsideClick="true"
    >
<edit-wf-modal-component
:data="editData"
:shape="selectedElement?.shape"
:allFormsTemplate="allFormsTemplate"
:userGroups=userGroups
:showSelectFormTemplate="showSelectFormTemplate"
@save="updateEdgeName"
@cancel="modalOpen = false"
v-if="modalOpen"
/>
  </modal>
    <alert-modal
      :open="warningModal.status"
      :closeOnOutsideClick="true"
      @close="closeWarningModal"
    >
      <div class="workflow-warning-box">
        <img
          src="@/assets/images/icons/alertErrorWhite.svg"
          width="100"
          alt=""
        />
        <h3>Validation Error</h3>
       <ul>
        <li v-for="(message, index) in warningModal.errorMessage" :key="index">
  {{ index + 1 }}. {{ message }}
</li>

       </ul>
        <button
          class="workflow-warning-box-close"
          @click="closeWarningModal"
        >
          Close
        </button>
      </div>
    </alert-modal>

    <modal
      @close="detailModal = false"
      :open="detailModal"
      :title="editData.newLabel"
      :closeOnOutsideClick="true"
    >
      <div v-if="detailModal">
        <step-detail-modal
          :stepData="JSON.parse(JSON.stringify(this.selectedElement))"
          :instanceId="instanceId"
        />
      </div>
    </modal>
  </div>
</template>
<script>
import { Graph, Shape } from '@antv/x6'
import { Stencil } from '@antv/x6-plugin-stencil'
import { Transform } from '@antv/x6-plugin-transform'
import { Selection } from '@antv/x6-plugin-selection'
import { Snapline } from '@antv/x6-plugin-snapline'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Clipboard } from '@antv/x6-plugin-clipboard'
import { History } from '@antv/x6-plugin-history'
import Modal from '@/components/common/modal.vue'
import AlertModal from '@/components/common/alertModal.vue'
import approvedIcon from '../../assets/images/icons/stageIcon.svg'
import durationIcon from '../../assets/images/icons/duration.svg'
import PlannedDateIcon from '../../assets/images/icons/plannedDates.svg'
import editIcon from '../../assets/images/pencil.svg'
import deleteIcon from '../../assets/images/delete-gray-icon.svg'
import ActualDateIcon from '../../assets/images/icons/endDates.svg'
import {
  CreateWorkflowSteps,
  CreateWorkflowTemplateWIthPhases,
  CreateWorkflowTransition,
  GetAllWorkflowByVersionById,
  getFormTemplates,
  getTransitionData,
  GetUserGroupsWithoutLimit,
  GetWorkFlowDataByTemplateId,
  GetWorkflowTypes,
  updateWorkFlowTemplate,
  UpdateWorkflowTemplateName
} from '@/api'
import config from '@/config'
import { alert, success } from '@/plugins/notification'
import Loader from '@/plugins/loader'
import { mapGetters } from 'vuex'
import { timeStampToDate } from '@/filters/dateFilter'
import stepDetailModal from '@/components/workflows/stepDetailModal.vue'
import editWfModalComponent from '@/components/workflows/editWfModalComponent.vue'
import { emptyString } from '../../helper/formValidation'
let graph = null
// let ctrlPressed = false
// let sytemMovement = false
export default {
  name: 'WorkflowDesigner',
  components: { Modal, AlertModal, stepDetailModal, editWfModalComponent },

  data: () => ({
    window: window,
    modalOpen: false,
    selectedElement: null,
    heading: null,
    department: null,
    listMap: {},
    versionList: [],
    activeVersionId: null,
    templateName: '',
    warningModal: {},
    editMode: false,
    templateId: null,
    selectedVersionId: null,
    draftVersionId: null,
    userGroups: [],
    editData: {},
    nameChanged: false,
    config: config,
    selectedFeatureId: null,
    instanceId: null,
    workFlowData: {},
    detailModal: false,
    disableSaveButton: true,
    wfMap: {},
    allFormsTemplate: []
  }),
  computed: {
    ...mapGetters(['user', 'isOnProjectLevel']),
    workFlowType () {
      return this.$route.params.type
    },
    editPermission () {
      if (this.user.tenantLevelRole !== 'ADMIN') {
        return false
      } else {
        return (
          ((this.selectedVersionId === this.activeVersionId &&
          !this.draftVersionId) ||
          this.selectedVersionId === 'draft' ||
          this.selectedVersionId === this.draftVersionId) &&
        !this.instanceId
        )
      }
    },
    showEditButton () {
      return true
    },
    showVersions () {
      return true
    },
    showSelectFeature () {
      return !(this.templateId || this.instanceId)
    },
    showSelectFormTemplate () {
      return this.selectedElement?.shape === 'node-rect' && this.wfMap[this.selectedElement.id]?.source.size === 0
    }
  },
  mounted () {
    this.getTypeFromCustomList()
    const templateId = this.$route?.query?.tid
    if (templateId) {
      // loading from settings
      this.getTemplateData(templateId)
    } else if (this.activeVersionId && this.instanceId) {
      // based on workflow instance
      this.initGraphView()
    } else {
      // create mode
      this.getAllFormTempalates()
      this.editMode = true
      this.initGraph()
    }
  },
  created () {
    this.selectedVersionId = this.$route?.query?.version ?? null
    this.activeVersionId = this.$route?.query?.version ?? null
    this.instanceId = this.$route?.query?.wfinstance ?? null
    this.getUserGroup()
    if (!['FULL', 'PARTIAL', 'NORMAL'].includes(this.workFlowType)) {
      this.$router.push('/settings/workflows/1')
    }
  },
  methods: {
    initGraph () {
      graph = new Graph({
        container: this.window.document.getElementById('graph-container'),
        grid: true,
        autoResize: true,
        panning: {
          enabled: true,
          modifiers: 'ctrl'
        },
        width: '100%',
        height: '100%',
        onToolItemCreated ({ name, cell, tool }) {
          if (name === 'vertices') {
            const options = tool.options
            if (options && options.index % 2 === 1) {
              tool.setAttrs({ fill: 'red' })
            }
          }
        },
        interacting: {
          edgeMovable: true, // Allow edges to be dragged
          nodeMovable: false
        },
        mousewheel: {
          enabled: true,
          zoomAtMousePosition: true,
          modifiers: 'ctrl',
          minScale: 0.5,
          maxScale: 3
        },
        connecting: {
          router: 'normal', // manhattan
          allowMulti: true,
          connector: {
            name: 'rounded',
            args: {
              radius: 8
            }
          },
          anchor: 'center',
          connectionPoint: 'anchor',
          allowBlank: false,
          snap: {
            radius: 20
          },
          createEdge () {
            return new Shape.Edge({
              attrs: {
                line: {
                  stroke: '#A2B1C3',
                  strokeWidth: 2,
                  targetMarker: {
                    name: 'block',
                    width: 12,
                    height: 8
                  }
                }
              },
              vertices: [],
              tools: {
                name: 'vertices',
                args: {
                  snapRadius: 20,
                  attrs: {
                    fill: '#444'
                  }
                }
              },
              sourceAnchor: { name: 'right' },
              targetAnchor: { name: 'left' },
              labels: [
                {
                  markup: [
                    {
                      tagName: 'rect',
                      selector: 'labelBody',
                      attrs: {
                        cursor: 'pointer', // Add pointer cursor
                        'pointer-events': 'visible',
                        event: 'none'
                      }
                    },
                    {
                      tagName: 'text',
                      selector: 'label'
                    }
                  ],
                  attrs: {
                    label: {
                      text: 'transition',
                      fill: '#000',
                      fontSize: 12,
                      fontFamily: 'Arial, sans-serif',
                      textAnchor: 'middle',
                      pointerEvents: 'visible',
                      zIndex: 2
                    },
                    labelBody: {
                      ref: 'label',
                      fill: 'white',
                      stroke: '#666',
                      strokeWidth: 0.5,
                      rx: 3,
                      ry: 3,
                      padding: 5, // Padding around text
                      refWidth: '140%', // Extra width (adjust as needed)
                      refHeight: '140%', // Extra height (adjust as needed)
                      refX: '-20%', // Center the background
                      refY: '-20%'
                    }
                  }

                }
              ],
              zIndex: 1
            })
          }
          // validateConnection ({
          //   sourceCell,
          //   targetCell,
          //   sourceMagnet,
          //   targetMagnet
          // }) {
          //   if (
          //     sourceMagnet?.getAttribute('port') === 'port-right' &&
          //     targetMagnet?.getAttribute('port') === 'port-left'
          //   ) {
          //     return true // Allow connection
          //   }
          //   return false // Block connection
          // }
        },
        highlighting: {
          magnetAdsorbed: {
            name: 'stroke',
            args: {
              attrs: {
                fill: '#5F95FF',
                stroke: '#5F95FF'
              }
            }
          }
        },
        translating: {
          restrict: true
        }
      })
      // #endregion
      graph
        .use(
          new Selection({
            enabled: true,
            rubberband: true,
            showNodeSelectionBox: true,
            multiple: false,
            filter: ['node-rect']
          })
        )
        .use(
          new Transform({
            resizing: {
              enabled: true,
              orthogonal: true
            },
            rotating: false
          })
        )
        .use(
          new Snapline({
            enabled: true,
            filter: ['node-rect']
          })
        )
        .use(new Keyboard())
        .use(new Clipboard())
        .use(new History())

      const stencil = new Stencil({
        title: 'Phase and Step ',
        target: graph,
        stencilGraphWidth: 200,
        stencilGraphHeight: 180,
        // collapsable: true,
        groups: [
          {
            title: 'Phases',
            name: 'group1'
          }
        ],
        layoutOptions: {
          columns: 1,
          columnWidth: 80,
          rowHeight: 90,
          center: true,
          marginX: 30
        }
      })
      this.window.document
        .getElementById('stencil')
        .appendChild(stencil.container)
      const ports = {
        groups: {
          right: {
            position: 'right',
            attrs: {
              circle: {
                r: 6,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'visible'
                }
              }
            },
            maxConnections: -1
          },
          left: {
            position: 'left',
            attrs: {
              circle: {
                r: 6,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'visible'
                }
              }
            },
            maxConnections: -1
          },
          top: {
            position: 'top',
            attrs: {
              circle: {
                r: 6,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'visible'
                }
              }
            },
            maxConnections: -1
          },
          bottom: {
            position: 'bottom',
            attrs: {
              circle: {
                r: 6,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'visible'
                }
              }
            },
            maxConnections: -1
          }
        },
        items: [
          {
            id: 'port-right',
            group: 'right'
          },
          {
            id: 'port-left',
            group: 'left'
          },
          {
            id: 'port-top',
            group: 'top'
          },
          {
            id: 'port-bottom',
            group: 'bottom'
          }
        ]
      }
      Graph.registerNode(
        'node-rect',
        {
          inherit: 'rect',
          width: 90,
          height: 60,
          zIndex: 2,
          // movable: true,
          attrs: {
            body: {
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: 'rgba(95,149,255,0.2)',
              refWidth: 1,
              refHeight: 1,
              rx: '5',
              ry: '5'
            },
            image: {
              'xlink:href': approvedIcon,
              width: 16,
              height: 16,
              x: 12,
              y: 12
            },
            durationIcon: {
              'xlink:href': durationIcon,
              width: 16,
              height: 16,
              x: 12,
              y: 35
            },
            duration: {
              text: '0 hours',
              refX: 40,
              refY: 44,
              fill: '#6b6a6a',
              fontSize: 12,
              'text-anchor': 'start'
            },
            text: {
              text: 'Heading',
              refX: 40,
              refY: 21,
              fill: 'rgba(0,0,0)',
              fontSize: 14,
              'text-anchor': 'start'
            },
            flagBox: {
              width: 0, // 40
              height: 0, // 15
              stroke: 'rgb(27, 142, 27',
              fill: 'rgb(27, 142, 27,.1)',
              strokeWidth: 0.5,
              x: 100,
              y: 12,
              rx: 5,
              ry: 5,
              class: 'flagBox-start-box'
            },
            flagText: {
              text: '',
              // fill: 'rgb(27, 142, 27',
              fontSize: 10,
              fontWeight: 'bold',
              zIndex: 2
            }
            // title: {
            //   text: 'content',
            //   refX: 40,
            //   refY: 38,
            //   fontSize: 12,
            //   fill: 'rgba(0,0,0,0.6)',
            //   'text-anchor': 'start'
            // }
          },
          markup: [
            {
              tagName: 'rect',
              selector: 'body'
            },
            {
              tagName: 'image',
              selector: 'image'
            },
            {
              tagName: 'image',
              selector: 'durationIcon'
            },
            {
              tagName: 'text',
              selector: 'duration'
            },
            {
              tagName: 'text',
              selector: 'title'
            },
            {
              tagName: 'text',
              selector: 'flagText'
            },
            {
              tagName: 'rect',
              selector: 'flagBox'
            }
          ],
          // nodeMovable: true,
          ports: { ...ports }
        },
        true
      )
      const nodesArray = []
      if (
        this.workFlowType === config.WORKFLOW_TYPE.STEPS_WITH_PHASES ||
        this.workFlowType === config.WORKFLOW_TYPE.FULL
      ) {
        Graph.registerNode(
          'phase-rect',
          {
            inherit: 'rect',
            width: 140,
            height: 60,
            zIndex: 1,
            markup: [
              {
                tagName: 'text',
                selector: 'text'
              },
              {
                tagName: 'rect',
                selector: 'name-box'
              },
              {
                tagName: 'rect',
                selector: 'body'
              }
            ],
            attrs: {
              body: {
                strokeWidth: 1,
                fill: 'rgba(245, 240, 108,.3)',
                stroke: '#ffe7ba'
              },
              'name-box': {
                width: 140,
                height: 20,
                fill: 'transparent',
                stroke: '#096dd9',
                strokeWidth: 1,
                x: 0,
                y: 0
              },
              text: {
                // text: 'phase',
                fill: 'black',
                fontSize: 12,
                fontWeight: 'bold',
                refY: 10,
                textAnchor: 'middle',
                zIndex: 2
              }
            }
          },
          true
        )
      }
      if (
        this.workFlowType === config.WORKFLOW_TYPE.STEPS_WITH_PHASES ||
        this.workFlowType === config.WORKFLOW_TYPE.FULL
      ) {
        const r2 = graph.createNode({
          shape: 'phase-rect',
          label: 'Phase'
        })
        nodesArray.push(r2)
      }
      const r1 = graph.createNode({
        shape: 'node-rect',
        label: 'Title'
      })
      nodesArray.push(r1)
      stencil.load([...nodesArray], 'group1')
      this.addEventListeners(graph)
      if (this.templateId) {
        this.loadWorkFlowData(graph, ports)
      }
    },
    initGraphView () {
      graph = new Graph({
        container: this.window.document.getElementById('graph-container'),
        grid: true,
        autoResize: true,
        width: '100%',
        height: '100%',
        panning: {
          enabled: true,
          modifiers: 'ctrl'
        },
        interacting: {
          nodeMovable: false,
          edgeMovable: false,
          arrowheadMovable: false,
          vertexMovable: false,
          vertexAddable: false,
          vertexDeletable: false,
          edgeLabelMovable: false,
          useEdgeTools: false,
          useNodeTools: false
        },
        mousewheel: {
          enabled: true,
          zoomAtMousePosition: true,
          modifiers: 'ctrl',
          minScale: 0.5,
          maxScale: 3
        },
        translating: {
          restrict: true
        }
      })
      const ports = {
        groups: {
          right: {
            position: 'right',
            attrs: {
              circle: {
                r: 0,
                magnet: false,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'visible'
                }
              }
            },
            maxConnections: -1
          },
          left: {
            position: 'left',
            attrs: {
              circle: {
                r: 0,
                magnet: false,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'visible'
                }
              }
            },
            maxConnections: -1
          },
          top: {
            position: 'top',
            attrs: {
              circle: {
                r: 0,
                magnet: false,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'visible'
                }
              }
            },
            maxConnections: -1
          },
          bottom: {
            position: 'bottom',
            attrs: {
              circle: {
                r: 0,
                magnet: false,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'visible'
                }
              }
            },
            maxConnections: -1
          }
        },
        items: [
          {
            id: 'port-right',
            group: 'right'
          },
          {
            id: 'port-left',
            group: 'left'
          },
          {
            id: 'port-top',
            group: 'top'
          },
          {
            id: 'port-bottom',
            group: 'bottom'
          }
        ]
      }

      Graph.registerNode(
        'node-rect',
        {
          inherit: 'rect',
          width: 140,
          height: 80,
          zIndex: 2,
          // movable: true,
          attrs: {
            body: {
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: 'rgba(95,149,255,0.2)',
              refWidth: 1,
              refHeight: 1,
              rx: '5',
              ry: '5'
            },
            image: {
              'xlink:href': approvedIcon,
              width: 16,
              height: 16,
              x: 8,
              y: 12
            },
            durationIcon: {
              'xlink:href': durationIcon,
              width: 14,
              height: 14,
              x: 8,
              y: 31
            },
            plannedDateIcon: {
              'xlink:href': PlannedDateIcon,
              width: 14,
              height: 14,
              x: 8,
              y: 49
            },
            actualDateIcon: {
              'xlink:href': ActualDateIcon,
              width: 14,
              height: 14,
              x: 8,
              y: 65
            },
            duration: {
              text: '0 hours',
              refX: 30,
              refY: 33,
              fill: '#6b6a6a',
              fontSize: 10,
              'text-anchor': 'start'
            },
            text: {
              text: 'Heading',
              refX: 30,
              refY: 20,
              fill: 'rgba(0,0,0)',
              fontSize: 12,
              fontWeight: 'bold',
              'text-anchor': 'start'
            },
            blink: {
              r: 0,
              fill: 'green',
              strokeWidth: 1,
              x: -10,
              y: -10,
              class: 'blink'
            },
            flagBox: {
              width: 0, // 40
              height: 0, // 15
              stroke: 'rgb(27, 142, 27',
              fill: 'rgb(27, 142, 27,.1)',
              strokeWidth: 0.5,
              x: 100,
              y: 12,
              rx: 5,
              ry: 5,
              class: 'flagBox-start-box'
            },
            flagText: {
              // text: 'Start',
              fill: 'rgb(27, 142, 27)',
              fontSize: 10,
              fontWeight: 'bold',
              zIndex: 2
            },
            PlanendDateText: {
              text: '-- - --',
              refX: 30,
              refY: 50,
              fill: '#6b6a6a',
              fontSize: 10
            },
            ActualDateText: {
              text: '-- - --',
              refX: 30,
              refY: 68,
              fill: '#6b6a6a',
              fontSize: 10
            }
            // title: {
            //   text: 'content',
            //   refX: 40,
            //   refY: 38,
            //   fontSize: 12,
            //   fill: 'rgba(0,0,0,0.6)',
            //   'text-anchor': 'start'
            // }
          },
          markup: [
            {
              tagName: 'rect',
              selector: 'body'
            },
            {
              tagName: 'image',
              selector: 'image'
            },
            {
              tagName: 'image',
              selector: 'plannedDateIcon'
            },
            {
              tagName: 'image',
              selector: 'actualDateIcon'
            },
            {
              tagName: 'text',
              selector: 'title'
            },
            {
              tagName: 'text',
              selector: 'text'
            },
            {
              tagName: 'text',
              selector: 'PlanendDateText'
            },
            {
              tagName: 'text',
              selector: 'ActualDateText'
            },
            {
              tagName: 'text',
              selector: 'flagText'
            },
            {
              tagName: 'rect',
              selector: 'flagBox'
            },
            {
              tagName: 'image',
              selector: 'durationIcon'
            },

            {
              tagName: 'text',
              selector: 'duration'
            },
            {
              tagName: 'circle',
              selector: 'blink'
            }
          ]
        },
        true
      )
      Graph.registerNode(
        'phase-rect',
        {
          inherit: 'rect',
          width: 140,
          height: 60,
          zIndex: 1,
          markup: [
            {
              tagName: 'text',
              selector: 'text'
            },
            {
              tagName: 'rect',
              selector: 'name-box'
            },
            {
              tagName: 'rect',
              selector: 'body'
            }
          ],
          attrs: {
            body: {
              strokeWidth: 1,
              fill: 'rgba(245, 240, 108,.3)',
              stroke: '#ffe7ba'
            },
            resizeHandles: {
              display: 'none' // Hide resize handles
            },
            'name-box': {
              width: 140,
              height: 20,
              fill: 'transparent',
              stroke: '#096dd9',
              strokeWidth: 1,
              x: 0,
              y: 0
            },
            text: {
              // text: 'phase',
              fill: 'black',
              fontSize: 12,
              fontWeight: 'bold',
              refY: 10,
              textAnchor: 'middle',
              zIndex: 2
            }
          }
        },
        true
      )
      if (this.templateId || this.instanceId) {
        this.loadWorkFlowData(graph, ports)
        this.instanceId && this.addEventListnersViewMode()
      }
    },
    addEventListeners (graph) {
      const self = this
      graph.on('node:mouseenter', ({ node, options }) => {
      })
      graph.on('node:added', ({ node, options }) => {
        if (node.shape === 'node-rect') {
          // edge has 2
          if (node.zIndex < 2) {
            node.setZIndex(2) // Set a custom z-index value
          }
          node.resize(175, 85)
          const allPhaseNodes = graph.getNodes().filter((n) => {
            return n.shape === 'node-rect'
          })
          let lastSequence = 0
          let lastPhaseNode = null
          if (allPhaseNodes.length > 1) {
            lastPhaseNode = allPhaseNodes.at(-2)
            lastSequence = lastPhaseNode?.prop('sequence') ?? 1
          }
          node.prop('sequence', lastSequence + 1)

          if (this.workFlowType === config.WORKFLOW_TYPE.ONLY_STEPS) {
            return
          }
          const parent = graph
            .getNodes()
            .find(
              (n) =>
                n.shape === 'phase-rect' &&
                n.getBBox().containsPoint(node.getPosition())
            )
          if (parent) {
            parent.addChild(node)
          } else {
            graph.removeCell(node.id)
          }
        }
        // Check if the node was added via a drop from the stencil.
        if (node.shape === 'phase-rect') {
          if (node) {
            node.setZIndex(1) // Set a custom z-index value
            // adding sequence for getting the order
            const allPhaseNodes = graph.getNodes().filter((n) => {
              return n.shape === 'phase-rect'
            })
            let lastSequence = 0
            let lastPhaseNode = null
            if (allPhaseNodes.length > 1) {
              lastPhaseNode = allPhaseNodes.at(-2)
              lastSequence = lastPhaseNode.prop('sequence')
            }
            node.prop('sequence', lastSequence + 1)

            node.setAttrs({
              'name-box': {
                width: 300,
                height: 30,
                stroke: 'rgb(0,0,0,.7)',
                strokeWidth: 2,
                x: 0,
                y: 0
              },
              text: {
                fill: 'black',
                fontSize: 14,
                fontWeight: 'bold',
                refY: 15,
                textAnchor: 'middle',
                zIndex: 2
              }
            })

            // Position the new node near the previous one
            node.resize(300, 1000)
            if (lastPhaseNode?.prop('sequence')) {
              const nexX =
                lastPhaseNode?.position().x + lastPhaseNode?.size().width
              node.position(nexX, 10) // Position it properly
            } else {
              node.position(20, 10)
            }

            //   storing initial position to reset if dragged
            node.prop('originSize', node.size())
          }
        } // it will add the initial position  for both node-rect and phase-rect
        node.prop('originPosition', node.getPosition())
      })
      graph.on('node:removed', ({ node, e }) => {
        if (node.shape === 'phase-rect') {
          // sytemMovement = true
          // Recalculate and reposition all phase-rect node
          const deletedNodeWidth = node.size().width
          const phaseRectNodes = graph
            .getNodes()
            .filter((n) => n.shape === 'phase-rect')

          // Reposition each phase-rect node sequentially
          phaseRectNodes.forEach((phaseRect, index) => {
            const sequence = phaseRect.prop('sequence')
            if (sequence > node.prop('sequence')) {
              const initialPosition = phaseRect.getPosition()
              const newX = initialPosition.x - deletedNodeWidth
              const currentPosition = phaseRect.getPosition()

              phaseRect.position(newX, currentPosition.y)
              // Move all child node-rect nodes relative to the parent's new position
              const childNodes = phaseRect.getChildren()
              if (childNodes && childNodes.length > 0) {
                childNodes.forEach((child) => {
                  if (child.shape === 'node-rect') {
                    const childPosition = child.getPosition()
                    const offsetX = childPosition.x - currentPosition.x // Calculate the child's offset from the parent
                    const newChildX = newX + offsetX // New x-position for the child
                    child.position(newChildX, childPosition.y) // Move the child
                    child.prop('originPosition', {
                      x: newChildX,
                      y: childPosition.y
                    })
                  }
                })
              }
              // Update the origin position for the phase-rect node
              phaseRect.prop('originSize', phaseRect.size())
              phaseRect.prop('originPosition', phaseRect.getPosition())
            }
          })
          e.stopPropagation()
        } else if (node.shape === 'node-rect') {
          delete this.wfMap[node.id]
          for (const item in this.wfMap) {
            if (this.wfMap[item]?.source.size === 0 && this.wfMap[item]?.target.size === 0) {
              const node = graph.getCellById(item)
              node && node.setAttrs({
                flagBox: { width: 0, height: 0 },
                flagText: {
                  text: '',
                  refY: 19.5,
                  fill: 'transparent'
                }
              })
              delete this.wfMap[item]
            }
          }
        }
      })

      graph.on('node:change:position', ({ node, options }) => {
        // condition to prevent dragging of parent node
        if (options.skipParentHandler) {
          return
        }

        if (node.shape === 'phase-rect') {
          return
        }
        // Get the current parent of the node-rect
        const parent = node.getParent()
        // Ensure the node-rect has a parent and the parent is a phase-rect
        if (parent && parent.shape === 'phase-rect') {
          const parentBBox = parent.getBBox() // Get the parent's bounding box
          const childBBox = node.getBBox() // Get the child's bounding box

          // Calculate the allowed position for the child node
          let newX = childBBox.x
          let newY = childBBox.y

          // Restrict movement horizontally
          if (childBBox.x < parentBBox.x) {
            newX = parentBBox.x // Move child to the left boundary of the parent
          } else if (
            childBBox.x + childBBox.width >
            parentBBox.x + parentBBox.width
          ) {
            newX = parentBBox.x + parentBBox.width - childBBox.width // Move child to the right boundary of the parent
          }

          // Restrict movement vertically
          if (childBBox.y < parentBBox.y) {
            newY = parentBBox.y // Move child to the top boundary of the parent
          } else if (
            childBBox.y + childBBox.height >
            parentBBox.y + parentBBox.height
          ) {
            newY = parentBBox.y + parentBBox.height - childBBox.height // Move child to the bottom boundary of the parent
          }

          // If the position has changed, update the child node's position
          if (newX !== childBBox.x || newY !== childBBox.y) {
            node.position(newX, newY, { skipParentHandler: true }) // Skip the parent handler to avoid infinite loops
          }
        } else {
          // If the node-rect is not inside any phase-rect, find the nearest phase-rect and add it as a child
          const nearestPhaseRect = graph
            .getNodes()
            .find(
              (n) =>
                n.shape === 'phase-rect' &&
                n.getBBox().containsPoint(node.getPosition())
            )

          if (nearestPhaseRect) {
            nearestPhaseRect.addChild(node)
          }
        }
      })
      graph.on('edge:mouseenter', ({ edge }) => {
        console.log('Edge mouse enter', edge.zIndex)
        edge.addTools('vertices', 'onhover') // Show vertices tool on hover of the edge line
        edge.attr('line/filter', {
          name: 'dropShadow',
          args: {
            dx: 2,
            dy: 2,
            blur: 4,
            color: '#A2B1C3' // Shadow color
          }
        })
        edge.addTools([
          {
            name: 'button-remove',
            args: {
              distance: 50,
              markup: [ // Override default markup
                {
                  tagName: 'circle',
                  selector: 'button',
                  attrs: {
                    r: 8,
                    stroke: '#fe854f',
                    'stroke-width': 1,
                    fill: 'white',
                    cursor: 'pointer'
                  }
                },
                {
                  tagName: 'use', // Direct SVG path for edit icon
                  selector: 'icon',
                  attrs: {
                    'xlink:href': deleteIcon, // ID from your SVG sprite
                    width: 10,
                    height: 10,
                    x: -5, // Center horizontally
                    y: -5, // Center vertically
                    fill: '#fe854f'
                  }
                }
              ]
            }
          },
          {
            name: 'button',
            args: {
              markup: [
                {
                  tagName: 'circle',
                  selector: 'button',
                  attrs: {
                    r: 8,
                    stroke: '#6e6d6d',
                    'stroke-width': 1,
                    fill: 'white',
                    cursor: 'pointer'
                  }
                },
                {
                  tagName: 'use', // Reference external SVG
                  selector: 'icon',
                  attrs: {
                    'xlink:href': editIcon, // ID from your SVG sprite
                    width: 10,
                    height: 10,
                    x: -5, // Center horizontally
                    y: -5, // Center vertically
                    fill: '#fe854f'
                  }
                }
              ],
              distance: -30,
              onClick ({ view, cell }) {
                self.editData.newLabel = cell?.getLabelAt(0)?.attrs?.label?.text
                self.selectedElement = cell
                self.modalOpen = true
              }
            }
          }
        ])
      })
      graph.on('node:mousemove', ({ node }) => {
        if (node.shape === 'phase-rect') {
          const selectedNode = graph.isSelectionEmpty()
          !selectedNode && graph.cleanSelection()
        }
      })
      // graph.on('node:click', ({ node }) => {
      //   graph.use(
      //     new Transform({
      //       resizing: {
      //         enabled: true,
      //         // Only allow right-side resize
      //         handles: ['right'] // Disables left, top, bottom, etc.
      //       },
      //       rotating: false // Disable rotation if needed
      //     })
      //   )
      // })
      graph.on('node:resize', ({ node, x, y, view }) => {})

      graph.on('node:resized', ({ node, x, y, view }) => {
        graph.getNodes().forEach((cell) => {
          cell.prop('originPosition', cell.getPosition())
          cell.prop('originSize', cell.size())
        })
        const width = node.store.data.size.width
        if (this.wfMap[node.id].target.size === 0) {
          node.setAttrs({
            flagBox: {
              x: width - 45
            },
            flagText: {
              x: width - 76
            }
          })
        } else if (this.wfMap[node.id].source.size === 0) {
          node.setAttrs({
            flagBox: { x: width - 45 },
            flagText: {
              x: width - 79
            }
          })
        }
      })
      graph.on('node:resizing', ({ node, x, y, view }) => {
        if (node.shape === 'node-rect') {
          return
        }
        if (node.shape === 'phase-rect') {
          const size = node.size()
          const initialSize = node.prop('originSize')
          if (initialSize.height !== size.height) {
            return
          }
        }
        const phaseRectNodes = graph
          .getNodes()
          .filter((n) => n.shape === 'phase-rect')
        const distanceMoved =
          x - (node.getPosition().x + node.prop('originSize').width)
        const nodeSequnce = node.prop('sequence')
        node.setAttrs({
          'name-box': { width: node.size().width }
        })
        for (const ele of phaseRectNodes) {
          const eleSequence = ele.prop('sequence')

          if (eleSequence <= nodeSequnce) {
            continue
          } else {
            const oldPosition = ele.prop('originPosition')
            const newX = oldPosition.x + distanceMoved
            ele.position(newX, oldPosition.y, { relative: true })
            const childNodes = ele.getChildren()
            if (childNodes && childNodes.length > 0) {
              childNodes.forEach((child) => {
                if (child.shape === 'node-rect') {
                  const childPosition = child.getPosition()
                  const newChildX =
                    child.prop('originPosition').x + distanceMoved // New x-position for the child
                  child.position(newChildX, childPosition.y) // Move the child
                }
              })
            }
          }
        }

        // if (node.shape === 'phase-rect') {
        //   // Reset the size to the original size
        //   node.resize(options.previous.width, options.previous.height, {
        //     skipParentHandler: true
        //   })
        // }
      })
      graph.on('node:mouseup', ({ node }) => {
        // if (node.shape === 'phase-rect') {
        //   node.prop('nodeMovable', false)
        // }
      })
      graph.on('edge:dblclick', ({ edge }) => {
        // Prompt the user for a new label
        this.editData.newLabel = edge?.getLabelAt(0)?.attrs?.label?.text
        this.selectedElement = edge
        this.modalOpen = true
      })
      graph.on('node:dblclick', ({ node }) => {
        this.editData.newLabel = node.getAttrs()?.text?.text
        this.editData.duration = node.prop('duration')
        this.editData.userGroup = node.prop('userGroup')
        this.editData.escalation = node.prop('escalation')
        this.editData.formTemplate = node.prop('formTemplate')
        this.editData.docUploadTime = node.prop('docUploadTime')
        this.editData.uploadMandatory = Boolean(node.prop('uploadMandatory'))
        this.selectedElement = node
        this.modalOpen = true
        // this.heading = node?.getAttrs()?.title?.text
      })

      graph.on('edge:mouseleave', ({ edge }) => {
        edge.attr({
          line: {
            filter: null
            // stroke: '#A2B1C3', // Reset color
            // strokeWidth: 2 // Reset width
          }
        })
        edge.removeTools()
      })
      graph.bindKey(['meta+c', 'ctrl+c'], () => {
        const cells = graph.getSelectedCells()
        if (cells.length) {
          graph.copy(cells)
        }
        return false
      })
      graph.bindKey(['meta+x', 'ctrl+x'], () => {
        const cells = graph.getSelectedCells()
        if (cells.length) {
          graph.cut(cells)
        }
        return false
      })
      graph.bindKey(['meta+v', 'ctrl+v'], () => {
        if (!graph.isClipboardEmpty()) {
          const cells = graph.paste({ offset: 32 })
          graph.cleanSelection()
          graph.select(cells)
        }
        return false
      })
      graph.on('node:contextmenu', ({ node }) => {
        graph.cleanSelection()
      })
      // undo redo
      graph.bindKey(['meta+z', 'ctrl+z'], () => {
        if (graph.canUndo()) {
          graph.undo()
        }
        return false
      })
      graph.bindKey(['meta+shift+z', 'ctrl+shift+z'], () => {
        if (graph.canRedo()) {
          graph.redo()
        }
        return false
      })

      // select all
      graph.bindKey(['meta+a', 'ctrl+a'], () => {
        const nodes = graph.getNodes()
        if (nodes) {
          graph.select(nodes)
        }
      })

      // delete
      graph.bindKey('backspace', () => {
        const cells = graph.getSelectedCells()
        if (cells.length) {
          graph.removeCells(cells)
        }
      })
      graph.bindKey('delete', () => {
        const cells = graph.getSelectedCells()
        if (cells.length) {
          graph.removeCells(cells)
        }
      })

      // zoom
      graph.bindKey(['ctrl+1', 'meta+1'], () => {
        const zoom = graph.zoom()
        if (zoom < 1.5) {
          graph.zoom(0.1)
        }
      })
      graph.bindKey(['ctrl+2', 'meta+2'], () => {
        const zoom = graph.zoom()
        if (zoom > 0.5) {
          graph.zoom(-0.1)
        }
      })
      graph.on('edge:connected', ({ isNew, edge }) => {
        const source = edge.getSourceCell()
        const target = edge.getTargetCell()
        this.createNodeMap(edge.id, source.id, target.id)

        source.setAttrs({ body: { fill: 'rgba(95,149,255,0.2)' } })
        target.setAttrs({ body: { fill: 'rgba(95,149,255,0.2)' } })
      })
      graph.on('edge:removed', ({ edge }) => {
        const sourceId = edge.getSourceCellId()?.id
        const targetId = edge.getTargetCellId()?.id
        this.createNodeMap(edge.id, sourceId, targetId, true)
      })
      graph.on('node:mouseenter', ({ node }) => {
        node.addTools({
          name: 'button-remove',
          args: {
            x: 0,
            and: 0,
            offset: { x: 10, y: 10 },
            markup: [ // Override default markup
              {
                tagName: 'circle',
                selector: 'button',
                attrs: {
                  r: 8,
                  stroke: '#fe854f',
                  'stroke-width': 1,
                  fill: 'white',
                  cursor: 'pointer'
                }
              },
              {
                tagName: 'use', // Direct SVG path for edit icon
                selector: 'icon',
                attrs: {
                  'xlink:href': deleteIcon, // ID from your SVG sprite
                  width: 10,
                  height: 10,
                  x: -5, // Center horizontally
                  y: -5, // Center vertically
                  fill: '#fe854f'
                }
              }
            ]
          }
        })
      })
      graph.on('node:mouseleave', ({ node }) => {
        node.removeTools()
      })
      // graph.on('node:selected', ({ node }) => {
      //   console.log('Node selected:', node.shape)
      // })
    },
    // this function will gwt executed only while showing the workflow progress (along with workflow instance)
    addEventListnersViewMode () {
      graph.on('node:dblclick', ({ node }) => {
        if (node.shape !== 'node-rect') {
          return
        }
        this.selectedElement = node
        this.detailModal = true
        this.editData.newLabel = node.getAttrs()?.text?.text
        this.editData.duration = node.prop('duration')
        this.editData.userGroup = node.prop('userGroup')
        this.editData.escalation = node.prop('escalation')
        this.editData.formTemplate = node.prop('formTemplate')
      })
    },
    updateEdgeName (data) {
      if (this.selectedElement && data.newLabel) {
        if (data.newLabel.length > 30) {
          data.newLabel = data.newLabel.slice(0, 30)
        }
        if (this.selectedElement.isNode()) {
          // Rename Node
          this.selectedElement.setAttrs({
            text: { text: data.newLabel, fill: 'rgba(0,0,0)' }
          })
          if (this.selectedElement.shape === 'node-rect') {
            this.selectedElement.prop('duration', data.duration)
            this.selectedElement.prop('escalation', data.escalation)
            this.selectedElement.prop('formTemplate', data.formTemplate)
            this.selectedElement.prop('uploadMandatory', data.uploadMandatory)
            this.selectedElement.prop('docUploadTime', data.docUploadTime)
            this.selectedElement.setAttrs({
              duration: {
                text: (data.duration ?? 0) + ' hours',
                fill: '#6b6a6a'
              }
            })
            this.selectedElement.prop('userGroup', data.userGroup)
            this.adjustSizeBasedOnLabel(this.selectedElement)
          }
        } else if (this.selectedElement.isEdge()) {
          // Rename Edge
          this.selectedElement.setLabels([
            {
              markup: [
                {
                  tagName: 'rect', // Background rectangle
                  selector: 'labelBody'
                },
                {
                  tagName: 'text', // Text label
                  selector: 'label'
                }
              ],

              attrs: {
                label: {
                  text: data.newLabel,
                  fill: '#000',
                  backgroundColor: 'transparent',
                  fontSize: 12,
                  fontFamily: 'Arial, sans-serif',
                  textAnchor: 'middle'
                },
                labelBody: {
                  ref: 'label', // Wrap around the text
                  fill: 'white', // Background color
                  stroke: '#666', // Border color
                  strokeWidth: 0.5, // Border width
                  rx: 3, // Border radius
                  ry: 3,
                  padding: 5, // Padding around text
                  refWidth: '140%', // Extra width (adjust as needed)
                  refHeight: '140%', // Extra height (adjust as needed)
                  refX: '-20%', // Center the background
                  refY: '-20%'
                }
              }

            }
          ])
        }
      }
      this.modalOpen = false
    },
    adjustSizeBasedOnLabel (node) {
      // Adjust node size based on text length
      const textWidth = this.getTextWidth(node.getAttrs().text.text)
      let width = textWidth + 100
      //  this is to prevent the node from  shriking below the minimum width
      if (width < 175) {
        width = 175
      }
      node.resize(width, node.getSize().height)

      if (this.wfMap[node.id]?.target.size === 0) {
        node.setAttrs({
          flagBox: {
            x: width - 45
          },
          flagText: {
            x: width - 76
          }
        })
      } else if (this.wfMap[node.id]?.source.size === 0) {
        node.setAttrs({
          flagBox: { x: width - 45 },
          flagText: {
            x: width - 79
          }
        })
      }
    },
    async saveWorkflow (draft = false) {
      this.disableSaveButton = true
      const loader = new Loader()
      try {
        const data = graph.toJSON()
        if (data.cells.length <= 0) {
          alert(
            'There is nothing to save. Please add at least one item before saving.'
          )
          return
        }
        this.warningModal.errorMessage = this.validateNodesandEdges(data.cells)
        if (this.warningModal.errorMessage.length > 0) {
          // alert(
          //   'Invalid names detected. Please review the text highlighted in red.'
          // )
          this.warningModal.status = true
          return
        }

        loader.show()
        // type id is from custom list
        const type = this.listMap[this.workFlowType]
        if (!this.templateId) {
          const wfMap = {}
          const templateData = {
            name: this.templateName,
            type,
            feature_id: this.selectedFeatureId,
            workflow_versions: { data: { workflow_stages: { data: [] } } }
          }
          // phase map are required to create steps  based on its parent phase (ids are given from BE)
          const nodesIdMap = new Map()
          const phaseData = []
          const nodeCells = []
          const edgeCells = []
          for (const item of data.cells) {
            if (item.shape === 'node-rect') {
              wfMap[item.id] = { source: new Set(), target: new Set() }
            }
          }
          for (const item of data.cells) {
            if (item.shape === 'phase-rect') {
              const obj = {
                name: item.attrs.text.text,
                sequence: item.sequence,
                type: config.NODE_TYPE.PHASE,
                start_coordinates: `(${item.position.x},${item.position.y})`,
                end_coordinates: `(${item.position.x + item.size.width},${
                  item.position.y + item.size.height
                })`
              }
              nodesIdMap.set(
                `${obj.name}-${obj.start_coordinates}-${obj.end_coordinates}`,
                item.id
              )
              phaseData.push(obj)
            } else if (item.shape === 'node-rect') {
              nodeCells.push(item)
              nodesIdMap.set(
                `${
                  item.attrs.text.text
                }-${`(${item.position.x},${item.position.y})`}-${`(${
                  item.position.x + item.size.width
                },${item.position.y + item.size.height})`}`,
                item.id
              )
            } else if (item.shape === 'edge') {
              edgeCells.push(item)
              wfMap[item.source.cell].source.add(item.id)
              wfMap[item.target.cell].target.add(item.id)
            }
          }
          templateData.workflow_versions.data.workflow_stages.data = phaseData
          let templateVersionId = {}
          const parentIdMap = {}

          const templatePHaseData = await CreateWorkflowTemplateWIthPhases(
            templateData
          )
          // const templateId = templatePHaseData.insert_workflow_templates_one.id
          templateVersionId =
            templatePHaseData.insert_workflow_templates_one.workflow_versions[0]
              .id
          const templateId = templatePHaseData.insert_workflow_templates_one.id
          const workFlowStages =
            templatePHaseData.insert_workflow_templates_one.workflow_versions[0]
              .workflow_stages
          for (const phase of workFlowStages) {
            const originalId = nodesIdMap.get(
              `${phase.name}-${phase.start_coordinates}-${phase.end_coordinates}`
            )
            if (originalId) {
              parentIdMap[originalId] = phase.id
            }
          }
          const nodeData = []
          for (const node of nodeCells) {
            const obj = {
              template_version_id: templateVersionId,
              sequence: node.sequence,
              start_coordinates: `(${node.position.x},${node.position.y})`,
              end_coordinates: `(${node.position.x + node.size.width},${
                node.position.y + node.size.height
              })`,
              // here need to add phase id from db
              phase_id: parentIdMap[node?.parent] ?? undefined,
              name: node.attrs.text.text,
              // description: node.attrs.title.text,
              duration: parseInt(node.duration) ?? 1,
              department_id: node.userGroup,
              type: config.NODE_TYPE.STEP,
              // nodes with zero destination of transitions will be treated as -- start
              // nodes with zero source of transitions will be treated as  --- end
              end_step: wfMap[node.id].source.size === 0,
              start_step: wfMap[node.id].target.size === 0,
              escalation_threshold: parseInt(node.escalation)
            }
            if (obj.end_step && node?.formTemplate?.id) {
              obj.trigger_actions = {
                data: [
                  {
                    form_template_id: node?.formTemplate?.id,
                    workflow_template_id: templateId,
                    workflow_version_id: templateVersionId,
                    trigger_action: 'CREATE'
                  }
                ]
              }
            }
            if (node.docUploadTime === 'on_start' || node.docUploadTime === 'on_end') {
              if (obj.trigger_actions?.data?.length > 0) {
                obj.trigger_actions.data.push([
                  {
                    workflow_template_id: templateId,
                    workflow_version_id: templateVersionId,
                    trigger_action: 'FILE_UPLOAD',
                    mandatory: node.uploadMandatory ?? undefined, // if upload mandatory is not set then it will be null,
                    trigger_timing: node.docUploadTime ?? undefined // if doc upload time is not set then it will be null
                  }
                ])
              } else {
                obj.trigger_actions = {
                  data: [
                    {
                    // form_template_id: node?.formTemplate?.id,
                      workflow_template_id: templateId,
                      workflow_version_id: templateVersionId,
                      trigger_action: 'FILE_UPLOAD',
                      mandatory: node.uploadMandatory ?? undefined, // if upload mandatory is not set then it will be null,
                      trigger_timing: node.docUploadTime ?? undefined // if doc upload time is not set then it will be null
                    }
                  ]
                }
              }
            }
            nodeData.push(obj)
          }
          const stepsData = await CreateWorkflowSteps(nodeData)
          const stepsIdMap = {}
          for (const steps of stepsData.insert_workflow_stages.returning) {
            const originalId = nodesIdMap.get(
              `${steps.name}-${steps.start_coordinates}-${steps.end_coordinates}`
            )
            if (originalId) {
              stepsIdMap[originalId] = steps.id
            }
          }
          const edgeData = []
          for (const edge of edgeCells) {
            const obj = {
              line_vertices: JSON.stringify({
                vertices: edge.vertices,
                sourcePort: edge.source.port,
                targetPort: edge.target.port
              }),
              name: edge.labels?.[0].attrs.label.text,
              prev_step: stepsIdMap[edge.source.cell],
              next_step: stepsIdMap[edge.target.cell],
              template_version_id: templateVersionId
            }
            edgeData.push(obj)
          }
          await CreateWorkflowTransition(edgeData)
          success('Workflow created successfully')
          this.$router.push('/settings/copy-workflows/1')
        } else {
          if (this.nameChanged) {
            await UpdateWorkflowTemplateName(this.templateId, this.templateName)
          }
          this.updateWorkFlow(data, type, draft)
        }
        loader.hide()
      } catch (error) {
        loader.hide()
        const errorMessage = error?.message || error?.toString?.() || ''
        if (errorMessage.includes('Uniqueness')) {
          alert('Duplicate template name')
        }
        if (error && Array.isArray(error)) {
          for (const err of error) {
            if (err.message.includes('Uniqueness violation')) {
              alert('Duplicate template name')
              this.$refs.templateNameInput.style.border = '2px solid red'
              this.$refs.templateNameInput.style.borderRadius = '5px'
            }
            return
          }
          alert('something went wrong')
        }
        console.log(error)
      } finally {
        loader.hide()
        this.disableSaveButton = false
      }
    },
    async updateWorkFlow (data, type, draft) {
      const loader = new Loader()
      loader.show()
      const wfMap = {}
      try {
        const childParentMap = new Map()
        for (const item of data.cells) {
          if (item.shape === 'phase-rect') {
            for (const child of item?.children ?? []) {
              childParentMap.set(child, item.id)
            }
          }
          if (item.shape === 'node-rect') {
            wfMap[item.id] = { source: new Set(), target: new Set() }
          }
        }
        for (const item of data.cells) {
          if (item.shape === 'edge') {
            wfMap[item.source.cell].source.add(item.id)
            wfMap[item.target.cell].target.add(item.id)
          }
        }
        const updates = { stages: [], transitions: [] }
        for (const item of data.cells) {
          if (item.shape === 'phase-rect' || item.shape === 'node-rect') {
            const obj = {
              id: item.id,
              name: item.attrs.text.text,
              duration: parseInt(item?.duration) ?? 1,
              department_id: item?.userGroup,
              sequence: item.sequence,
              type:
                item.shape === 'node-rect'
                  ? config.NODE_TYPE.STEP
                  : config.NODE_TYPE.PHASE,
              phase_id: childParentMap.get(item.id) ?? null, // for phase-rect there is no parent
              start_coordinates: {
                x: item.position.x,
                y: item.position.y
              },
              end_coordinates: {
                x: item.position.x + item.size.width,
                y: item.position.y + item.size.height
              }
            }
            obj.end_step = false
            obj.start_step = false
            if (item.shape === 'node-rect') {
              obj.end_step = wfMap[item.id].source.size === 0
              obj.start_step = wfMap[item.id].target.size === 0
              obj.escalation_threshold = parseInt(item.escalation)
            }
            if (obj.end_step && item.formTemplate?.id) {
              if (updates.actions?.length > 0) {
                updates.actions.push({
                  form_template_id: item.formTemplate?.id,
                  trigger_action: 'CREATE',
                  workflow_step_id: item.id

                })
              } else {
                updates.actions = [{
                  form_template_id: item.formTemplate?.id,
                  trigger_action: 'CREATE',
                  workflow_step_id: item.id
                }]
              }
            }
            if (item.docUploadTime === 'on_start' || item.docUploadTime === 'on_end') {
              if (updates.actions?.length > 0) {
                updates.actions.push({
                  trigger_action: 'FILE_UPLOAD',
                  workflow_step_id: item.id,
                  mandatory: item.uploadMandatory ?? undefined, // if upload mandatory is not set then it will be null,
                  trigger_timing: item.docUploadTime ?? undefined // if doc upload time is not set then it will be null
                })
              } else {
                updates.actions = [{
                  trigger_action: 'FILE_UPLOAD',
                  workflow_step_id: item.id,
                  mandatory: item.uploadMandatory ?? undefined, // if upload mandatory is not set then it will be null,
                  trigger_timing: item.docUploadTime ?? undefined // if doc upload time is not set then it will be null

                }]
              }
            }
            updates.stages.push(obj)
          } else if (item.shape === 'edge') {
            updates.transitions.push({
              id: item.id,
              name: item.labels?.[0].attrs.label.text,
              prev_step: item.source.cell,
              next_step: item.target.cell,
              line_vertices: JSON.stringify({
                vertices: item.vertices,
                sourcePort: item.source.port,
                targetPort: item.target.port
              }),
              blocking: false
            })
          }
        }
        updateWorkFlowTemplate(draft, this.templateId, updates)
          .then((res) => {
            loader.hide()
            success('Workflow updated successfully')
            this.$router.push('/settings/copy-workflows/1')
          })
          .catch((err) => {
            loader.hide()
            if (err?.message.includes('No changes have been made')) {
              if (this.nameChanged) {
                success('Workflow name updated successfully')
                this.$router.push('/settings/copy-workflows/1')
              } else {
                alert(err.message)
              }
            } else {
              alert('Something went wrong')
            }
          })
      } catch (error) {
        alert('Something went wrong')
        loader.hide()
        console.log(error)
      }
    },
    async loadWorkFlowData (graph, ports) {
      const loader = new Loader()
      try {
        loader.show()
        this.workFlowData = await GetAllWorkflowByVersionById(
          this.selectedVersionId === 'draft'
            ? this.activeVersionId
            : this.selectedVersionId
        )
        this.selectedFeatureId =
          this.workFlowData.workflow_versions_by_pk.workflow_template.feature_id
        this.templateName =
          this.workFlowData?.workflow_versions_by_pk.workflow_template?.name
        const itemArray = []
        // const itemIdIndex = {}
        const parentChildMap = {}
        this.wfMap = {}
        for (const item of this.workFlowData.workflow_versions_by_pk
          .workflow_stages) {
          if (item.type === config.NODE_TYPE.STEP) {
            // end step and start calculation--start
            this.wfMap[item.id] = { source: new Set(), target: new Set() }
            // end step and start calculation--end

            if (parentChildMap[item.phase_id]) {
              parentChildMap[item.phase_id].push(item.id)
            } else {
              parentChildMap[item.phase_id] = [item.id]
            }
          }
        }
        for (const item of this.workFlowData.workflow_versions_by_pk
          .workflow_stages) {
          const cordinateAndSize = this.formateCordinates(
            item.start_coordinates,
            item.end_coordinates
          )
          const obj = {
            id: item.id,
            duration: parseInt(item.duration) ?? 1,
            escalation: item.escalation_threshold,
            userGroup: item.department_id,
            departmentName: item.core_user_group?.name ?? null, // this only for showing name while loading  instance view modal
            shape:
              item.type === config.NODE_TYPE.STEP ? 'node-rect' : 'phase-rect',
            x: cordinateAndSize.x,
            y: cordinateAndSize.y,
            originSize: {
              width: cordinateAndSize.width,
              height: cordinateAndSize.height
            },
            originPosition: {
              x: cordinateAndSize.x,
              y: cordinateAndSize.y
            },
            sequence: item.sequence ?? 1,
            width: cordinateAndSize.width < 175 ? 175 : cordinateAndSize.width,
            height: cordinateAndSize.height < 85 ? 85 : cordinateAndSize.height,
            label: item.name,
            // title: 'Heading',
            //  ports: item.ports,
            ports: item.type === config.NODE_TYPE.STEP ? { ...ports } : [],
            zIndex: item.type === config.NODE_TYPE.STEP ? 2 : 1,
            visible: true,
            children:
              item.type === config.NODE_TYPE.STEP
                ? []
                : parentChildMap[item.id]
          }

          if (item.type === config.NODE_TYPE.STEP) {
            obj.formTemplate = {}
            obj.docUploadTime = null
            obj.uploadMandatory = null
            if (item?.trigger_actions?.[0]?.trigger_action === 'FILE_UPLOAD') {
              obj.docUploadTime = this.instanceId ? null : item?.trigger_actions?.[0]?.trigger_timing
              obj.uploadMandatory = this.instanceId ? null : item?.trigger_actions?.[0]?.mandatory
            }
            if (item?.trigger_actions?.[0]?.trigger_action === 'CREATE') {
              obj.formTemplate = this.instanceId ? null : { name: item?.trigger_actions?.[0]?.core_form_template?.name, id: item?.trigger_actions?.[0]?.form_template_id }
            }
            obj.width =
              cordinateAndSize.width < 175 ? 175 : cordinateAndSize.width
            obj.height =
              cordinateAndSize.height < 85 ? 85 : cordinateAndSize.height
            obj.attrs = {
              body: {
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: 'rgba(95,149,255,0.2)',
                refWidth: 1,
                refHeight: 1
              },
              // durationIcon: {
              //   'xlink:href': durationIcon,
              //   width: 16,
              //   height: 16,
              //   x: 12,
              //   y: 35
              // },
              duration: {
                text: item.duration + ' hours'
              }
              // text: {
              //   text: 'Heading',
              //   refX: 40,
              //   refY: 20,
              //   fill: 'rgba(0,0,0)',
              //   fontSize: 13,
              //   'text-anchor': 'start'
              // }
              // title: {
              //   text: item.name,
              //   refX: 40,
              //   refY: 38,
              //   fontSize: 12,
              //   fill: 'rgba(0,0,0,0.6)',http://localhost:8080/timesheet/update
              //   'text-anchor': 'start'
              // }
            }
          } else {
            obj.width = cordinateAndSize.width
            obj.height = cordinateAndSize.height
            obj.attrs = {
              ...item.attrs,
              'name-box': {
                width: cordinateAndSize.width,
                height: 30,
                stroke: 'rgb(0,0,0,.7)',
                strokeWidth: 2,
                x: 0,
                y: 0
              },
              text: {
                // text: 'phase',
                fill: 'black',
                fontSize: 14,
                fontWeight: 'bold',
                refY: 15,
                textAnchor: 'middle',
                zIndex: 2
              }
            }
          }
          itemArray.push(obj)
          // itemIdIndex[item.id] = itemArray.length - 1
        }
        for (const item of this.workFlowData.workflow_versions_by_pk
          .workflow_transitions) {
          // start and end step calculation--start
          this.wfMap[item.prev_step].source.add(item.id)
          this.wfMap[item.next_step].target.add(item.id)
          // end step and start calculation--end
          const verticesData = JSON.parse(item.line_vertices)
          itemArray.push({
            id: item.id,
            shape: 'edge',
            attrs: {
              line: {
                stroke: '#A2B1C3',
                strokeWidth: 1,
                targetMarker: {
                  name: 'block',
                  width: 12,
                  height: 8
                }
              }
            },
            source: {
              cell: item.prev_step,
              port: verticesData.sourcePort
            },
            target: {
              cell: item.next_step,
              port: verticesData.targetPort
            },
            labels: [
              {
                markup: [
                  {
                    tagName: 'rect', // Background rectangle
                    selector: 'labelBody'
                  },
                  {
                    tagName: 'text', // Text label
                    selector: 'label'
                  }
                ],
                attrs: {
                  label: {
                    text: item.name ?? 'transition',
                    fill: '#000',
                    fontSize: 12,
                    fontFamily: 'Arial, sans-serif',
                    textAnchor: 'middle',
                    pointerEvents: 'visible',
                    zIndex: 4
                  },
                  labelBody: {
                    ref: 'label', // Wrap around the text
                    fill: 'white', // Background color
                    stroke: '#666', // Border color
                    strokeWidth: 0.5, // Border width
                    rx: 3, // Border radius
                    ry: 3,
                    padding: 5, // Padding around text
                    refWidth: '140%', // Extra width (adjust as needed)
                    refHeight: '140%', // Extra height (adjust as needed)
                    refX: '-20%', // Center the background
                    refY: '-20%'
                  }
                }
              }
            ],
            zIndex: 1,
            vertices: verticesData.vertices
          })
        }

        if (itemArray?.length > 0) {
          graph.fromJSON(itemArray)
          // calling transition data to show progress of workflow
          if (this.instanceId) {
            this.loadTransitionData()
          }
          for (const nodeId in this.wfMap) {
            if (this.wfMap[nodeId].source.size === 0) {
              const node = graph.getCellById(nodeId)
              const nodeSize = node.size()
              // for adjusting the size of node based on text length --start
              const textWidth = this.getTextWidth(node.getAttrs().text.text)
              const expectedWidth = textWidth + 100
              if (expectedWidth > nodeSize.width) {
                node.resize(expectedWidth, nodeSize.height)
              }
              // for adjusting the size of node based on text length --end
              node.setAttrs({
                flagBox: { width: 40, height: 15, x: nodeSize.width - 45 },
                flagText: {
                  text: 'Finish',
                  fill: 'rgb(27, 142, 27)',
                  refY: this.editMode ? 19.5 : 15,
                  x: nodeSize.width - (this.editMode ? 79 : 40)
                }
              })
            }
            if (this.wfMap[nodeId].target.size === 0) {
              const node = graph.getCellById(nodeId)
              const nodeSize = node.size()
              // for adjusting the size of node based on text length --start
              // const expectedWidth = (node.getAttrs().text.text.length * 10) + 65
              const textWidth = this.getTextWidth(node.getAttrs().text.text)
              const expectedWidth = textWidth + 100
              if (expectedWidth > nodeSize.width) {
                node.resize(expectedWidth, nodeSize.height)
              }
              // for adjusting the size of node based on text length --end
              node.setAttrs({
                flagBox: {
                  width: 40,
                  height: 15,
                  x: nodeSize.width - 45,
                  stroke: 'rgba(59, 45, 207, 1)',
                  fill: 'rgba(59, 45, 207, .1)'
                },
                flagText: {
                  text: 'Start',
                  fill: 'rgba(59, 45, 207, 1)',
                  refY: this.editMode ? 19.5 : 15,
                  x: nodeSize.width - (this.editMode ? 76 : 38)
                }
              })
            }
          }
        }

        loader.hide()
      } catch (error) {
        console.log(error)
        loader.hide()
      }
    },
    formateCordinates (coordinatesStrt, coordinatesEnd) {
      const startMatches = coordinatesStrt.match(/\(([\d.]+),\s*([\d.]+)\)/)
      const x = parseInt(startMatches[1], 10)
      const y = parseInt(startMatches[2], 10)
      const endMatches = coordinatesEnd.match(/\(([\d.]+),\s*([\d.]+)\)/)
      const endX = parseInt(endMatches[1], 10)
      const endY = parseInt(endMatches[2], 10)

      return {
        x,
        y,
        width: endX - x,
        height: endY - y
      }
    },
    getTypeFromCustomList () {
      GetWorkflowTypes().then((res) => {
        res.custom_list_values.forEach((value) => {
          this.listMap[value.name] = value.id
        })
      })
    },
    getTemplateData (templateId) {
      GetWorkFlowDataByTemplateId(templateId).then((res) => {
        this.templateId = res.workflow_templates_by_pk.id
        this.versionList = res.workflow_templates_by_pk.workflow_versions
          .filter((version) => {
            if (this.user.userId !== version.created_by && version.draft) {
            } else {
              return version
            }
          })
          .map((item) => {
            if (item.active) {
              this.activeVersionId = item.id
              this.selectedVersionId = item.id
            }
            if (item.draft) {
              this.draftVersionId = item.id
            }
            return item
          })
      })
    },
    changeColor () {
      this.nameChanged = true
      if (this.templateName) {
        this.$refs.templateNameInput.style.border =
          '1px solid rgba(0, 0, 0, 0.7)'
      } else {
        this.$refs.templateNameInput.style.border = '2px solid red'
        this.$refs.templateNameInput.style.borderRadius = '5px'
      }
    },
    validateNodesandEdges (data) {
      const errorFlag = {}
      const uniquePhaseNames = new Map()
      let minDocupload = false
      // this is validation for template name
      if (!this.templateName) {
        this.$refs.templateNameInput.style.border = '2px solid red'
        this.$refs.templateNameInput.style.borderRadius = '5px'
        errorFlag.templateName = true
      }
      // this is validation for feature selection
      if (!emptyString(this.selectedFeatureId ?? '', 'Feature', 'feature', false)) {
        errorFlag.feature = true
      }
      for (const item of data) {
        if (item.shape === 'phase-rect') {
          if (item.attrs.text.text === 'Phase' || !item.attrs.text.text) {
            const node = graph.getCellById(item.id)
            node.setAttrs({
              text: { fill: 'red' }
            })
            !item.attrs.text.text && node.setAttrs({ text: { text: 'Phase' } })
            errorFlag.phaseName = true
          } else {
            if (uniquePhaseNames.has(item.attrs.text.text)) {
              const node = graph.getCellById(item.id)
              const duplicatenNode = graph.getCellById(
                uniquePhaseNames.get(item.attrs.text.text)
              )
              node.setAttrs({
                text: { fill: 'red' }
              })
              duplicatenNode.setAttrs({
                text: { fill: 'red' }
              })
              errorFlag.duplicatePhase = true
            } else {
              uniquePhaseNames.set(item.attrs.text.text, item.id)
            }
          }
        } else if (item.shape === 'node-rect') {
          const node = graph.getCellById(item.id)
          // checing wheather the  step is isolated or not --------start -----------
          if (node) {
            const edges = graph.getConnectedEdges(node) ?? []
            if (edges.length === 0) {
              node.setAttrs({ body: { fill: 'rgba(255,0,0,.3)' } })
              errorFlag.isolated = true
            }
          }
          // checing wheather the  step is isolated or not --------end -----------

          if (item.attrs.text.text === 'Title' || !item.attrs.text.text) {
            node.setAttrs({
              text: { fill: 'red' }
            })
            errorFlag.nodeName = true
          }
          if (!item.duration) {
            node.setAttrs({
              duration: { fill: 'red' }
            })
            errorFlag.duration = true
          }
          // this is condtion checks only if the seelcted feature is documents and none of the steps has doc upload trigger action

          if (this.selectedFeatureId === config.CORE_FEATURES.DOCUMENTS && item.docUploadTime) {
            minDocupload = true
          }
        } else if (item.shape === 'edge') {
          if (
            item.labels?.[0].attrs.label.text === 'transition' ||
            !item.labels?.[0].attrs.label.text
          ) {
            const edge = graph.getCellById(item.id)
            edge.setLabels([
              {
                markup: [
                  {
                    tagName: 'rect', // Background rectangle
                    selector: 'labelBody'
                  },
                  {
                    tagName: 'text', // Text label
                    selector: 'label'
                  }
                ],

                attrs: {
                  label: {
                    text: item.labels?.[0].attrs.label.text,
                    fill: 'red',
                    backgroundColor: 'white',
                    fontSize: 12,
                    fontFamily: 'Arial, sans-serif',
                    textAnchor: 'middle'
                  },
                  labelBody: {
                    ref: 'label', // Wrap around the text
                    fill: 'white', // Background color
                    stroke: 'red', // Border color
                    strokeWidth: 0.5, // Border width
                    rx: 3, // Border radius
                    ry: 3,
                    padding: 5, // Padding around text
                    refWidth: '140%', // Extra width (adjust as needed)
                    refHeight: '140%', // Extra height (adjust as needed)
                    refX: '-20%', // Center the background
                    refY: '-20%'
                  }

                }
              }
            ])
            errorFlag.edgeName = true
          }
        }
      }
      // this is condtion checks only if the seelcted feature is documents and none of the steps has doc upload trigger action
      if (this.selectedFeatureId === config.CORE_FEATURES.DOCUMENTS && !minDocupload) {
        errorFlag.trigger = true
      }
      const errorMessages = []
      // Populate error messages for all error flags
      if (errorFlag.edgeName) {
        errorMessages.push('Edge labels cannot be empty or "transition"')
      }
      if (errorFlag.templateName) {
        errorMessages.push('Should have proper template name')
      }
      if (errorFlag.phaseName) {
        errorMessages.push('Phase names cannot be empty or "Phase"')
      }
      if (errorFlag.nodeName) {
        errorMessages.push('Step names cannot be empty or "Title"')
      }
      if (errorFlag.feature) {
        errorMessages.push('Feature selection missing')
      }
      if (errorFlag.duplicatePhase) {
        errorMessages.push('Duplicate phase names found')
      }
      if (errorFlag.isolated) {
        errorMessages.push('Isolated steps found (steps with no connections)')
      }
      if (errorFlag.duration) {
        errorMessages.push('Step durations cannot be empty')
      }
      if (errorFlag.trigger) {
        errorMessages.push('Select file upload trigger action for at least one step')
      }
      return errorMessages
    },
    enableEditMode () {
      if (!this.editPermission) {
        return
      }
      if (this.selectedVersionId !== this.draftVersionId) {
        this.versionList.push({
          id: 'draft',
          active: false,
          version_no: this.versionList.length + 1
        })
        this.selectedVersionId = 'draft'
      }
      this.editMode = true
      this.getAllFormTempalates()
      graph.dispose()
      this.initGraph()
    },
    cancelEditMode () {
      if (!this.templateId) {
        this.$router.go(-1)
        return
      }
      if (!this.draftVersionId) {
        this.versionList.pop()
        this.selectedVersionId = this.versionList.at(-1).id
      }
      graph.dispose()
      this.editMode = false
      this.initGraphView()
    },
    getUserGroup () {
      GetUserGroupsWithoutLimit().then((res) => {
        this.userGroups = res.core_user_group
      })
    },
    changeFeature () {
      // this.$ref.selectFeature.style.border = '1px solid rgba(0, 0, 0, 0.7)'
    },
    loadTransitionData () {
      getTransitionData(this.instanceId)
        .then((res) => {
          const instanceStepMap = {}
          for (const step of res.workflow_instance_steps) {
            instanceStepMap[step.step_id] = step
          }
          for (const history of res.transition_history) {
            const node = graph.getCellById(history.step_id)
            const edge = graph.getCellById(history.action_taken)
            if (history.end_date === null) {
              node.setAttrs({ blink: { r: 5 } })
            }
            node &&
              node.setAttrs({
                body: { fill: 'rgba(235, 148, 72, 0.5)', strokeWidth: 2 },
                ActualDateText: {
                  text: `${timeStampToDate(
                    history.start_date
                  )} - ${timeStampToDate(history.end_date)}`
                }
              })
            if (edge) {
              edge.setAttrs({
                line: { stroke: 'rgba(235, 148, 72, 0.8)', strokeWidth: 3 }
              })
              edge.zIndex = 2
            }
          }
          for (const step of this.workFlowData?.workflow_versions_by_pk
            ?.workflow_stages) {
            if (step.type === config.NODE_TYPE.STEP) {
              const node = graph.getCellById(step.id)
              node.setAttrs({
                PlanendDateText: {
                  text: `${timeStampToDate(
                    instanceStepMap[step.id]?.planned_start_date
                  )} - ${timeStampToDate(
                    instanceStepMap[step.id]?.planned_end_date
                  )}`
                }
              })
            }
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    getTextWidth (text, fontSize = '12px') {
      const span = document.createElement('span')
      span.style.cssText = `
    position: absolute;
    visibility: hidden;
    font-size: ${fontSize};
    white-space: nowrap;
  `
      span.textContent = text
      document.body.appendChild(span)
      const width = span.offsetWidth
      document.body.removeChild(span)
      return width
    },
    createNodeMap (id, sourceNodeId, targetNodeId, remove = false) {
      if (!remove) {
        if (this.wfMap[sourceNodeId]) {
          this.wfMap[sourceNodeId].source.add(id)
        } else {
          this.wfMap[sourceNodeId] = {
            source: new Set([id]),
            target: new Set()
          }
        }
        if (this.wfMap[targetNodeId]) {
          this.wfMap[targetNodeId].target.add(id)
        } else {
          this.wfMap[targetNodeId] = {
            source: new Set(),
            target: new Set([id])
          }
        }
      } else {
        for (const item in this.wfMap) {
          this.wfMap[item] && this.wfMap[item].source.delete(id)
          this.wfMap[item] && this.wfMap[item].target.delete(id)
        }
      }
      for (const item in this.wfMap) {
        const node = graph.getCellById(item)
        if (!node) {
          continue
        }
        if (this.wfMap[item]?.source?.size === 0) {
          const nodeSize = node.size()
          node.setAttrs({
            flagBox: { width: 40, height: 15, x: nodeSize.width - 45 },
            flagText: {
              text: 'Finish',
              refY: 19.5,
              fill: 'rgb(27, 142, 27)',
              x: nodeSize.width - 79
            }
          })
          continue
        } else {
          node && node.setAttrs({
            flagBox: { width: 0, height: 0 },
            flagText: {
              text: '',
              refY: 19.5,
              fill: 'transparent'
            }
          })
        }
        if (this.wfMap[item].target.size === 0) {
          const nodeSize = node.size()
          // for adjusting the size of node based on text length --start
          // const expectedWidth = (node.getAttrs().text.text.length * 10) + 65
          const textWidth = this.getTextWidth(node.getAttrs().text.text)
          const expectedWidth = textWidth + 100
          if (expectedWidth > nodeSize.width) {
            node.resize(expectedWidth, nodeSize.height)
          }
          node.setAttrs({
            flagBox: {
              width: 40,
              height: 15,
              x: nodeSize.width - 45,
              stroke: 'rgba(59, 45, 207, 1)',
              fill: 'rgba(59, 45, 207, .1)'
            },
            flagText: {
              text: 'Start',
              fill: 'rgba(59, 45, 207, 1)',
              refY: 20,
              x: nodeSize.width - 76
            }
          })
          continue
        } else {
          node.setAttrs({
            flagBox: { width: 0, height: 0 },
            flagText: {
              text: '',
              refY: 19.5,
              fill: 'transparent'
            }
          })
        }
      }
    },
    getAllFormTempalates () {
      const wfTemplateId = this.$route.query.tid
      getFormTemplates()
        .then((res) => {
          this.allFormsTemplate = res.core_form_templates
            .filter(item => item.workflow_template_id !== wfTemplateId)
            .map((item) => {
              return {
                workflow_id: item.workflow_template_id,
                id: item.id,
                name: item.name
              }
            })
        })
        .catch((err) => {
          console.log(err)
        })
    },
    closeWarningModal () {
      this.warningModal = { status: false, errorMessage: [] }
    }

  },
  watch: {
    selectedVersionId () {
      // if selectedVersionId is draft then no need to fecth the data  if selected version id is the draft versionid only need to get data if you are not in edit mode
      if (
        this.selectedVersionId === 'draft' ||
        (this.selectedVersionId === this.draftVersionId && this.editMode)
      ) {
        return
      }
      graph && graph.dispose()
      this.initGraphView()
    }
  }
}
</script>
<style lang="scss">
.workflow {
  width: 100%;
  height: 100%;
  &-heading{
    max-width: 40vw;
  }
  &-bar {
    height: 60px;
    // margin: -12px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  &-designer {
    width: 100%;
    height: calc(100% - 60px);
    display: flex;
    border: 1px solid #dfe3e8;
  }
  &-version-selector {
    margin: 0 0 0 20px;
    font-size: 12px;
    background-color: var(--brand-light-color);
    line-height: 1;
    border: 1px solid var(--brand-color);
    border-radius: 4px;
    padding: 4px 12px;
  }
  &-activity-bar {
    padding: 10px;
    &-template-name {
      padding: 5px;
      border: none;
      border-bottom: solid;
      border-width: 0.5px;
      font-size: 1.28rem;
      border-color: rgba(0, 0, 0, 0.3);
      width: 20rem;
      &:focus {
        outline: none;
        border: solid 0.5px rgba(0, 0, 0, 0.7);
        border-radius: 5px;
      }
      &::placeholder {
        font-size: 12px;
        font-style: italic;
      }
    }
    &-backButton {
      height: 20px;
      margin-right: 2rem;
    }
  }
  &-warning-box {
    border-radius: 10px;
    background-color: rgb(249, 73, 73, 0.9);
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 350px;
    animation: slide-in-fwd-center 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)
      alternate both;
    & > h3 {
      color: white;
      font-size: 1.5rem;
      font-weight: larger;
      margin-top: 1rem;
    }
    & > ul {
      color: white;
      font-size: 12px;
      margin-top: 2rem;
      text-wrap: wrap;
    }
    &-close {
      border: none;
      margin-top: 1.5rem;
      color: white;
      padding: 0.5rem 2rem;
      border-radius: 30px;
      background: transparent;
      border: 1px solid white;
      &:active {
        scale: 0.95;
      }
    }
  }
}
.modalcontainer {
  padding: 17px 17px 17px 17px;
  width: 400px;
  height: fit-content;
}
#container {
  display: flex;
  border: 1px solid #dfe3e8;
}
#stencil {
  width: 180px;
  height: 100%;
  position: relative;
  border-right: 1px solid #dfe3e8;
}
#graph-container {
  width: calc(100% - 180px);
  height: 100%;
}
.x6-widget-stencil {
  background-color: #fff;
}
.x6-widget-stencil-title {
  background-color: #fff;
}
.x6-widget-stencil-group-title {
  background-color: #fff !important;
}
.x6-widget-transform {
  margin: -1px 0 0 -1px;
  padding: 0px;
  border: 1px solid #239edd;
}
.x6-widget-transform > div {
  border: 1px solid #239edd;
  &:nth-child(1) {
    display: none;
  }
  &:nth-child(2) {
    display: none;
  }
  &:nth-child(3) {
    display: none;
  }
  &:nth-child(5) {
    display: none;
  }
  &:nth-child(7) {
    display: none;
  }
  &:nth-child(8) {
    display: none;
  }
  &:nth-child(3) {
    display: none;
  }
}
.x6-widget-transform > div:hover {
  background-color: #3dafe4;
}
.x6-widget-transform-active-handle {
  background-color: #3dafe4;
}
.x6-widget-transform-resize {
  border-radius: 0.1;
}
.x6-widget-selection-inner {
  border: 1px solid #e41212;
}
.x6-widget-selection-box {
  opacity: 1;
}
.x6-widget-transform > rect:not([data-direction="right"]) {
  display: none !important;
}
.flagBox-start-box {
  background-color: rgb(27, 142, 27);
  border-radius: 50%;
}

@keyframes slide-in-fwd-center {
  0% {
    transform: translateZ(-1400px);
    opacity: 0;
  }
  100% {
    transform: translateZ(0);
    opacity: 1;
  }
}
.blink {
  -webkit-animation: scale-in-center 1s cubic-bezier(0.25, 0.46, 0.45, 0.94)
    infinite;
  animation: scale-in-center 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite
    alternate;
}

@-webkit-keyframes scale-in-center {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
    opacity: 0.6;
  }
  100% {
    -webkit-transform: scale(1.3);
    transform: scale(1.3);
    opacity: 0.6;
  }
}
@keyframes scale-in-center {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(1.3);
    transform: scale(1.3);
    opacity: 0.6;
  }
}

// checkbox starts
 .checkbox-wrapper {
    --borderColor: #48c;
    --borderWidth: .1em;
  }

  .checkbox-wrapper label {
    display: block;
    max-width: 100%;
    margin: 0 auto;
  }

  .checkbox-wrapper input[type=checkbox] {
    -webkit-appearance: none;
    appearance: none;
    vertical-align: middle;
    background: #fff;
    font-size: 1.8em;
    border-radius: 0.125em;
    display: inline-block;
    border: var(--borderWidth) solid var(--borderColor);
    width: .8em;
    height: .8em !important;
    position: relative;
  }
  .checkbox-wrapper input[type=checkbox]:before,
  .checkbox-wrapper input[type=checkbox]:after {
    content: "";
    position: absolute;
    background: var(--borderColor);
    width: calc(var(--borderWidth) * 3);
    height: var(--borderWidth);
    top: 50%;
    left: 10%;
    transform-origin: left center;
  }
  .checkbox-wrapper input[type=checkbox]:before {
    transform: rotate(45deg) translate(calc(var(--borderWidth) / -2), calc(var(--borderWidth) / -2)) scaleX(0);
    transition: transform 200ms ease-in 200ms;
  }
  .checkbox-wrapper input[type=checkbox]:after {
    width: calc(var(--borderWidth) * 5);
    transform: rotate(-45deg) translateY(calc(var(--borderWidth) * 2)) scaleX(0);
    transform-origin: left center;
    transition: transform 200ms ease-in;
  }
  .checkbox-wrapper input[type=checkbox]:checked:before {
    transform: rotate(45deg) translate(calc(var(--borderWidth) / -2), calc(var(--borderWidth) / -2)) scaleX(1);
    transition: transform 200ms ease-in;
  }
  .checkbox-wrapper input[type=checkbox]:checked:after {
    width: calc(var(--borderWidth) * 5);
    transform: rotate(-45deg) translateY(calc(var(--borderWidth) * 2)) scaleX(1);
    transition: transform 200ms ease-out 200ms;
  }
  .checkbox-wrapper input[type=checkbox]:focus {
    outline: calc(var(--borderWidth) / 2) dotted rgba(0, 0, 0, 0.25);
  }
  .grid-3{
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  align-items: center;

}

// checkbox ends
</style>
