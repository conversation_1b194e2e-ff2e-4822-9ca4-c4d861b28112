<template>
  <div
    :class="{
      'form-input': true,
      'form-input--required': upload ? false : data.required,
      'form-document': true,
    }"
  >
<!-- Wrapper for caption + line + icons -->
<div>
  <!-- If upload is true -->
  <div v-if="upload" class="caption-line-wrap">
    <div class="caption-line-text">
      <label class="label">{{ data.caption }}
        <!-- <img v-if="data.visibility" class="mx-1" width="15" src="~@/assets/images/eye.svg" alt="view" /> -->
      </label>
      <input ref="docsInput" type="file" :id="uid" @click="resetInputValue" @change="onChange" />
      <div class="line"></div>
    </div>
      <p class="next-action-desc">
        Please upload document to sunbsequent documents
      </p>
    <!-- <div class="icon-row">
      <img
        width="25px"
        src="~@/assets/images/icon-system.png"
        alt="Add Document"
        v-tooltip="'Add Documents from Local.'"
        @click="openFolderDialog"
        v-if="mode !== 'TEMPLATE' && !isExternalCollaborator && mode !== 'VIEW'"
      />
      <img
        width="25px"
        src="~@/assets/images/icons-cloud.png"
        alt="Add Document"
        v-tooltip="'Add Documents from DTX.'"
        @click="openDocumentDialog"
        v-if="mode !== 'TEMPLATE' && !isExternalCollaborator && mode !== 'VIEW'"
      />
    </div> -->
  </div>

  <!-- Default layout when upload is false -->
<div v-else class="v-center space-between">
      <label>{{ data.caption }}
        <img
      v-if="data.visibility"
      class="mx-1"
      width="15"
      src="~@/assets/images/eye.svg"
      alt="view"
      />
      </label>
      <input ref="docsInput" type="file" :id="uid" @click="resetInputValue" @change="onChange" />
      <div class="flex gap-1 mr-1">
<img width="25px" src="~@/assets/images/icon-system.png" alt="Add Document" v-tooltip="'Add Documents from Local.'"
      @click="openFolderDialog"
      v-if="mode !== 'TEMPLATE' && !isExternalCollaborator && mode !== 'VIEW'" />
      <img width="25px" src="~@/assets/images/icons-cloud.png" alt="Add Document" v-tooltip="'Add Documents from DTX.'"
      @click="openDocumentDialog"
      v-if="mode !== 'TEMPLATE' && !isExternalCollaborator && mode !== 'VIEW'" />
      </div>
    </div>
</div>

    <div class="form-document--document">
      <div v-if="!attachedDocs.length" class="form-document--document__no-document">
        No document added.
      </div>
      <div class="copy-dtx-table" v-else>
      <table>
        <thead>
          <tr>
            <th>File Name</th>
            <!-- <th>Current Version</th> -->
            <th v-if="upload && showUpload">Upload</th>
            <th v-if="upload">Revisions</th>
          </tr>
        </thead>
        <tbody class = "">
          <tr v-for="(item, index) in attachedDocs" :key="index">
            <td>{{ item.doc_name }}</td>
            <!-- <td>Path</td> -->
            <td v-if="upload && showUpload">
              <div class="pointer" v-if="upload">
                 <span v-if="item.uploadStatus === 'uploading'" class="loader-small"></span>
                <img
                  v-else
                  :title="'Upload'"
                  @click="triggerFilePicker(index)"
                  src="~@/assets/images/icons/upload-icon.svg"
                  alt="Upload"
                />
                <!-- Hidden input -->
                <input
                  type="file"
                  class="hidden"
                  ref="fileInputs"
                  @change="onFilePicked($event, item, index)"
                />
              </div>
              <div v-else>
                <img
                  src="~@/assets/images/icons/upload-icon.svg"
                  alt="Upload"
                />
              </div>
            </td>
            <td v-if="upload">
             <img class="pointer" :title="'View Revisions'" @click="viewRevisions(item, index)" width="15" src="~@/assets/images/eye.svg" alt="view" />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
      <div class="center p-6" v-if="Loading" >
            <loading-circle/>
          </div>
    </div>
    <modal
      :open="revisionUpload"
      @close="revisionUpload = false"
      :closeOnOutsideClick="true"
      title="Revision Upload"
    >
       <div class="copy-dtx-table">
        <table class="">
          <thead>
            <tr>
              <th>File Name</th>
              <th>Revision</th>
              <th>Step Name</th>
              <th>Uploaded By</th>
              <th>Uploaded At</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(file, index) in currentRevisions.slice().reverse()" :key="index">
              <td class="elipsis-text" v-overflow-tooltip >{{ file.doc_name }}</td>
              <td>{{ file.version_no }}</td>
              <td>{{ file.step_name }}</td>
              <td>{{ file.uploaded_by || '--' }}</td>
              <td>{{ file.created_on | genericFormatDate }}</td>
            </tr>
            <tr v-if="!currentRevisions.length">
              <td colspan="5" class="no-data">No files available</td>
            </tr>
          </tbody>
        </table>
      </div>
    </modal>
    <modal
    :open="documentSelectDialog"
    @close="documentSelectDialog = false"
    :closeOnOutsideClick="true"
    title=""
    >
    <document-selector @close="documentSelectDialog = false" @files-selected="handleFileSelection" :restrictUpload="true" v-if="documentSelectDialog" :linkedDocs="dtxSelectedFile"/>
  </modal>
    <modal
    :open="folderSelectDialog"
    @close="folderSelectDialog = false"
    :closeOnOutsideClick="true"
    title=""
    >
    <folder-selector @close="folderSelectDialog = false" v-if="folderSelectDialog" @folder-selected="handleFolderSelection"/>
    </modal>
  <view-attached v-if="detailObject.open" :fileId="detailObject.fileId" :fileDetails="fileDetails" :view_only="detailObject.view_only" @close="closeDetail"/>
  </div>
</template>

<script>
import { warning, alert, success } from '../../../plugins/notification'
import { generateS3SubmittingUrl, generateS3DownloadingUrlSingle, checkDocExists } from '@/api'
import { mapGetters } from 'vuex'
import Modal from '../../common/modal.vue'
import DocumentSelector from '../../document/documentSelectorDialog.vue'
import FolderSelector from '../../document/folderSelectorDialog.vue'
import ViewAttached from '../../projectPlanner/viewAttachedDocs.vue'
import LoadingCircle from '../../common/loadingCircle.vue'
import { genericFormatDate } from '@/utils/date'
export default {
  name: 'documentComponent',
  components: { DocumentSelector, Modal, ViewAttached, LoadingCircle, FolderSelector },
  filters: {
    genericFormatDate
  },
  props: {
    showUpload: {
      type: Boolean,
      default: false
    },
    stepName: {
    },
    upload: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'TEMPLATE'
    },
    data: {
      type: Object,
      default: () => {}
    },
    value: {
      type: Array,
      default: () => ([])
    },
    viewOnly: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      currentRevisions: [],
      files: [],
      newFiles: [],
      existingFiles: [],
      revisionUpload: false,
      componentValue: [],
      delete: [],
      insert: [],
      documentSelectDialog: false,
      attachedDocs: [],
      selecctedFolder: null,
      detailObject: {
        open: false,
        fileId: '',
        view_only: false
      },
      fileDetails: [],
      dtxSelectedFile: [],
      Loading: false,
      initialValue: [],
      folderSelectDialog: false
    }
  },
  computed: {
    ...mapGetters(['isOnProjectLevel']),
    ...mapGetters(['openTenantId']),
    ...mapGetters(['isExternalCollaborator']),
    uid () {
      return this._uid
    }
  },
  methods: {
    viewRevisions (item) {
      let relatedRevisions = []

      if (item.attachments && item.attachments.length) {
        relatedRevisions = [...item.attachments]
      } else {
        relatedRevisions = [
          ...this.existingFiles.filter(f => f.version_of === item.id),
          ...this.newFiles.filter(f => f.version_of === item.id)
        ]
      }
      relatedRevisions.sort((a, b) => new Date(a.created_on || 0) - new Date(b.created_on || 0))
      this.currentRevisions = relatedRevisions
      this.revisionUpload = true
    },

    formatFileSize (size) {
      const i = size === 0 ? 0 : Math.floor(Math.log(size) / Math.log(1024))
      return (
        (size / Math.pow(1024, i)).toFixed(2) * 1 +
    ' ' +
    ['B', 'KB', 'MB', 'GB', 'TB'][i]
      )
    },

    triggerFilePicker (index) {
      this.$refs.fileInputs[index].click()
    },

    async onFilePicked (event, item, index) {
      const selectedFiles = event.target.files
      const defaultTenantId = ''
      if (!selectedFiles.length) return

      const file = selectedFiles[0]
      const fileType = file.name.split('.').pop()
      const formattedSize = this.formatFileSize(file.size)
      const uploadedExt = file.name.split('.').pop().toLowerCase()

      const existingExt = item.doc_name
        ? item.doc_name.split('.').pop().toLowerCase()
        : ''

      if (uploadedExt !== existingExt) {
        alert(`Only .${existingExt} files are allowed for this document.`)
        return
      }

      this.$set(this.attachedDocs, index, {
        ...item,
        uploading: true,
        uploadedFileName: file.name,
        version_of: item.id,
        uploadedFileSize: formattedSize,
        uploadStatus: 'uploading'
      })

      try {
        // Step 1: Get signed URL
        const { url } = await generateS3SubmittingUrl({
          tenantId: defaultTenantId,
          feature: 'form',
          featureId: 'formDocument',
          fileName: file.name
        })

        // Step 2: Upload to S3
        await fetch(url, {
          method: 'PUT',
          body: file,
          headers: {
            'Content-Type': file.type
          }
        })

        // Step 3: Extract blob key
        const blobKey = url.split('?')[0].split('.com/').pop()
        // const versionNo = (this.existingFiles.length + this.newFiles.length + 1).toString()
        const existingRevisionCount =
  [
    ...this.existingFiles.filter(f => f.version_of === item.id),
    ...this.newFiles.filter(f => f.version_of === item.id)
    // Remove item.attachments from here
  ].length

        const versionNo = (existingRevisionCount + 1).toString()

        const uploadedFile = {
          doc_ext: fileType,
          doc_name: file.name,
          doc_size: file.size,
          folder_id: item?.folder_id || '',
          version_of: item.id,
          blob_key: blobKey,
          version_no: versionNo,
          created_on: Date.now(),
          step_name: this.stepName
        }
        this.newFiles.push(uploadedFile)

        if (!this.attachedDocs[index].attachments) {
          this.$set(this.attachedDocs[index], 'attachments', [])
        }
        if (!this.attachedDocs[index].attachments) {
          this.$set(this.attachedDocs[index], 'attachments', [])
        }
        this.attachedDocs[index].attachments.push(uploadedFile)
        this.$set(this.attachedDocs, index, {
          ...this.attachedDocs[index],
          blob_key: blobKey,
          file_name: file.name,
          file_ext: fileType,
          file_size: file.size,
          uploadStatus: 'uploaded'
        })
        success('file uploaded successfully')
        this.$emit('filesUploaded', this.newFiles)
      } catch (err) {
        console.error('Upload failed:', err)
        this.$set(this.attachedDocs, index, {
          ...item,
          uploadStatus: 'failed'
        })
      } finally {
        event.target.value = null // reset input for re-upload if needed
      }
    },

    resetInputValue () {
      this.$refs.docsInput.value = null
    },
    triggerFileInput () {
      this.$refs.docsInput.click()
    },
    openDocumentDialog () {
      this.documentSelectDialog = true
    },
    openFolderDialog () {
      this.folderSelectDialog = true
    },
    handleFolderSelection (selectedFolder) {
      this.folderSelectDialog = false
      this.selecctedFolder = selectedFolder
      this.triggerFileInput()
    },
    handleFileSelection (selectedFileData) {
      this.documentSelectDialog = false
      this.Loading = true
      this.dtxSelectedFile = selectedFileData
      selectedFileData.forEach((item) => {
        const documentId = item.core_document.id
        const isExisting = this.attachedDocs.some(doc => doc.id === documentId)
        if (!isExisting && (item.flag === 'new' || item.flag === 'existing')) {
          this.attachedDocs.push(item)
          this.componentValue.push({ document_id: documentId })
          const indexInInitialArray = this.initialValue.findIndex((val) => val === documentId)
          if (indexInInitialArray === -1) {
            this.insert.push({ document_id: documentId })
          } else {
            const indexInDeleteArray = this.delete.findIndex((val) => val.id === documentId)
            if (indexInDeleteArray !== -1) {
              this.delete.splice(indexInDeleteArray, 1)
            }
          }
        } else if (item.flag === 'deleted') {
          this.attachedDocs = this.attachedDocs.filter(doc => doc.id !== documentId)
          this.componentValue = this.componentValue.filter(doc => doc.document_id !== documentId)
          const indexInInitialArray = this.initialValue.findIndex((val) => val === documentId)
          if (indexInInitialArray !== -1) {
            this.delete.push({ localAttachment: false, id: this.initialValue[indexInInitialArray] })
          } else {
            const indexInInsertArray = this.insert.findIndex((doc) => doc.document_id === documentId)
            if (indexInInsertArray !== -1) {
              this.insert.splice(indexInInsertArray, 1)
            }
          }
        }
        this.emitChanges()
      })
      this.Loading = false
    },
    emitChanges () {
      const value = {}
      if (this.insert.length) {
        value.insert = this.insert
      }
      if (this.delete.length) {
        value.delete = this.delete
      }
      this.$emit('customChange', {
        field_id: this.$props.data.field_id,
        value
      })
    },
    getFileSize (bytes, decimals = 2) {
      if (!+bytes) return '0 Bytes'

      const k = 1024
      const dm = decimals < 0 ? 0 : decimals
      const sizes = [
        'Bytes',
        'KiB',
        'MiB',
        'GiB',
        'TiB',
        'PiB',
        'EiB',
        'ZiB',
        'YiB'
      ]

      const i = Math.floor(Math.log(bytes) / Math.log(k))

      return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`
    },
    async onChange (e) {
      if (this.mode === 'TEMPLATE') {
        warning('Document upload is not allowed on template mode.')
        return
      }
      const file = e.target.files[0]
      const fileType = file.name.split('.').pop()
      const fileObject = {
        doc_name: file.name,
        doc_ext: fileType,
        doc_size: file.size,
        fileData: file,
        folder_id: this.selecctedFolder ? this.selecctedFolder.id : null
      }
      const res = await checkDocExists(file.name, this.selecctedFolder.id, this.isOnProjectLevel)
      const exists = !!res?.core_documents?.[0]?.id
      if (exists) {
        alert('This file name already exists in this folder!')
        return 0
      }
      if (this.attachedDocs.some(f => f.doc_name === fileObject.doc_name)) {
        alert('This file name has already exist.')
        return
      }
      this.Loading = true
      this.componentValue.push(fileObject)
      const fileReader = new FileReader()
      fileReader.readAsDataURL(file)
      fileReader.onload = async (e) => {
        const { url } = await generateS3SubmittingUrl({
          tenantId: this.openTenantId,
          feature: 'form',
          featureId: 'formDocument',
          fileName: fileObject.doc_name
        })
        await fetch(url, {
          method: 'PUT',
          body: fileObject.fileData,
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        const blobkey = url.split('?')[0].split('.com/').pop()
        fileObject.blob_key = blobkey
        fileObject.fileData = undefined
        this.insert.push(fileObject)
        this.attachedDocs.push({ doc_name: file.name, doc_ext: fileType, doc_size: file.size, blob_key: fileObject.blob_key })
        this.emitChanges()
        this.Loading = false
        this.$refs.docsInput.values = ''
      }
    },
    remove (index) {
      const removeDocs = this.attachedDocs[index]
      if (this.attachedDocs[index].status === 'NONE') {
        if (this.attachedDocs[index].id && this.attachedDocs[index].attachments_reference_key) {
          this.delete.push({ localAttachment: true, id: this.attachedDocs[index].id })
        } else {
          this.delete.push({ localAttachment: false, id: this.attachedDocs[index].id })
        }
      }
      this.componentValue = this.componentValue.filter(doc => {
        return doc.document_id !== removeDocs.id
      })
      this.insert = this.insert.filter(doc => {
        return doc.document_id !== removeDocs.id
      })
      this.dtxSelectedFile = this.dtxSelectedFile.filter(doc => {
        return doc.core_document.id !== removeDocs.id
      })
      this.attachedDocs.splice(index, 1)
      this.emitChanges()
    },
    download (docs) {
      docs.loading = true
      generateS3DownloadingUrlSingle({
        fileName: encodeURIComponent(docs.doc_name),
        S3Key: docs.blob_key
      }).then((resp) => {
        fetch(resp.url)
          .then((res) => {
            return res.blob()
          })
          .then((res) => {
            const blob = new Blob([res])
            const link = document.createElement('a')
            link.href = URL.createObjectURL(blob)
            link.download = docs.doc_name
            link.click()
            URL.revokeObjectURL(link.href)
            docs.loading = false
          })
          .catch((err) => {
            alert(err)
            docs.loading = false
          })
      })
    },
    setValue () {
      const tempValue = []
      this.value.forEach(item => {
        if (item.core_document) {
          tempValue.push({
            version_of: item.version_of,
            doc_name: item.core_document.doc_name,
            id: item.core_document?.id,
            folder_id: item.core_document?.parent_id,
            step_name: item.transition_history?.current_workflow_step?.name,
            created_on: item.created_on,
            doc_ext: item.doc_ext,
            doc_size: item.core_document.doc_size,
            blob_key: item.core_document.blob_key
          })
        } else {
          tempValue.push({
            version_of: item.version_of,
            doc_name: item.doc_name,
            folder_id: item.core_document?.parent_id,
            doc_ext: item.doc_ext,
            id: item.core_document?.id,
            step_name: item.transition_history?.current_workflow_step?.name,
            created_on: item.created_on,
            doc_size: item.doc_size,
            blob_key: item.blob_key
          })
        }
      })
      this.initalValue = tempValue
      this.componentValue = tempValue
      this.attachedDocs = tempValue.filter(doc => !doc.version_of)
      const mainDocs = tempValue.filter(item => item.id)
      const matchingFiles = []

      mainDocs.forEach(mainDoc => {
        const relatedVersions = tempValue
          .filter(item => item.version_of === mainDoc.id)
          .sort((a, b) => new Date(a.created_on || 0) - new Date(b.created_on || 0)) // oldest first

        const attachmentsMap = new Map()
        const dedupedAttachments = []
        relatedVersions.forEach(item => {
          const key = item.blob_key || item.doc_name
          if (!attachmentsMap.has(key)) {
            attachmentsMap.set(key, true)
            dedupedAttachments.push(item)
          }
        })
        const formattedAttachments = dedupedAttachments.map((item, idx) => ({
          doc_ext: item.core_document ? item?.core_document?.doc_ext : item?.doc_ext,
          doc_name: item.doc_name || '',
          doc_size: item.doc_size || '',
          folder_id: item.folder_id || '',
          description: item.description || '',
          version_of: item.version_of || '',
          blob_key: item.blob_key || '',
          created_on: item.created_on,
          version_no: idx + 1,
          step_name: item.step_name || ''
        }))
        matchingFiles.push(...formattedAttachments)
        const docIndex = this.attachedDocs.findIndex(doc => doc.id === mainDoc.id)
        if (docIndex !== -1) {
          this.$set(this.attachedDocs[docIndex], 'attachments', formattedAttachments)
        }
      })
      this.existingFiles = matchingFiles
    },
    closeDetail () {
      this.detailObject = {
        open: false,
        fileId: '',
        view_only: false
      }
      this.fileDetails = []
    },
    openDocs (fileData) {
      this.detailObject = {
        open: true,
        fileId: fileData.doc_id || fileData.id,
        view_only: false
      }
      this.fileDetails.push(fileData)
    }
  },
  created () {
    this.setValue()
  }
}
</script>

<style lang="scss" scoped >
.loader-small {
  width: 16px;
  height: 16px;
  border: 2px solid #ccc;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  display: inline-block;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.view {
  font-size: 13px;
}
.view:hover {
  color: blue;
  text-decoration: underline;
}
.label {
  // font-weight: 500;
  font-size: 12px;
  white-space: nowrap;
}
.next-action-desc {
  font-size: 11px;
  color: #666;
  margin: 0 0 8px 0;
}
.caption-line-wrap {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.caption-line-text {
  display: flex;
  align-items: center;
  gap: 10px;
}

.caption-line-text label {
  font-weight: 500;
  white-space: nowrap;
}

.caption-line-text .line {
  flex-grow: 1;
  height: 1px;
  background-color: #ccc;
}

.icon-row {
  display: flex;
  margin-left: auto;
  gap: 10px;
  padding-left: 2px;
}

.form-document {
  input[type="file"] {
    display: none;
  }
  &--label {
    background-color: var(--brand-color);
    font-size: 12px;
    padding: 4px 10px;
    border-radius: 4px;
    box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
  }
  &--document {
    position: relative;
    display: inline-block;
    width: 100%;
    // margin-top: 10px;
    border: 1px solid rgb(179, 179, 179);
    // min-height: 60px;
    // max-height: 150px;
    overflow-y: auto;
    // border-radius: 4px;
    &__no-document {
      position: absolute;
      top: 50%;
      left: 50%;
      // transform: translate(-50%, -50%);
      color: rgb(179, 179, 179);
    }
    &__item {
      padding: 4px 10px;
      margin: 4px;
      background-color: rgba(var(--brand-rgb), 0.2);
      display: flex;
      align-items: center;
      font-size: 12px;
      &:hover {
        box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.25);
      }
      &--name {
        flex-grow: 1;
        & > div {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
        & small {
          opacity: 0.7;
        }
      }
      .loading-icon {
        animation: rotate 0.8s linear infinite;
      }
    }
  }
}
.file-table-wrapper {
  overflow-x: auto;
}

.file-table {
  width: 100%;
  border-collapse: collapse;
}

.file-table th,
.file-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.file-table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.no-data {
  text-align: center;
  color: #888;
  padding: 12px;
}
</style>
