<template>
  <div
    v-if="!id"
    class="avatar text center"
    :style="getStyle"
    @click="$emit('click')"
  >
    <img
      v-if="validImg"
      :src="img"
    />
    <div v-else-if="exceededCount"> +{{ exceededCount }}</div>
    <div v-else>{{ userSortName }}</div>
  </div>
  <div
    v-else
    class="avatar text center"
    :style="userAvatartById.style"
    :title="userAvatartById.fullName"
  >
    <div >{{ userAvatartById.sortName }}</div>
  </div>
</template>

<script>
import { stringToHexCode } from '@/utils/color.js'
import { mapGetters } from 'vuex'
export default {
  name: 'Avatar',
  props: {
    id: {
      type: [Number, String],
      default: 0
    },
    user: {
      type: Object,
      default: () => ({})
    },
    img: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: '1.5em'
    },
    exceededCount: {
      type: Number,
      default: 0
    }
  },
  computed: {
    ...mapGetters(['getUserById']),
    validImg () {
      return this.img || false
    },
    getStyle () {
      return {
        fontSize: this.size || '16px',
        height: this.size || '16px',
        width: this.size || '16px',
        backgroundColor: stringToHexCode(this.user?.first_name + ' ' + this.user?.last_name)
      }
    },
    userSortName () {
      return ((this.user?.first_name[0] || '') + (this.user?.last_name[0] || '')).toUpperCase()
    },
    userAvatartById () {
      const user = this.getUserById(this.id)
      const userDetail = user?.associated_user
      return {
        img: userDetail?.avatar,
        isValid: userDetail,
        sortName: ((userDetail?.first_name[0] || '') + (userDetail?.last_name[0] || '')).toUpperCase(),
        fullName: userDetail?.first_name + ' ' + userDetail?.last_name,
        style: {
          fontSize: this.size || '16px',
          height: this.size || '16px',
          width: this.size || '16px',
          backgroundColor: stringToHexCode(userDetail?.first_name + ' ' + userDetail?.last_name)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.avatar {
  border-radius: 50%;
  cursor: pointer;
  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }
  div {
    font-size: 0.4em;
    letter-spacing: 1px;
  }
}
</style>
