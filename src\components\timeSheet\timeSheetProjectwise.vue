<template>
    <div class="time-sheet column" :key="componentKey">
      <div class="time-sheet-table--container flex-grow mt-3">
        <table >
          <thead>
            <tr>
                <th>Users</th>
              <th v-for="day in date.weekDays" :key="day" >{{ day }}</th>
            </tr>
          </thead>
          <tbody v-if="checkForTimeSheetData">
            <tr v-for="(value,prop) in projectsData" :key="prop-1000" >
              <td>{{ userList[prop] }}</td>
              <td  v-for="(entry,index) in value" :key="index">
                <div class="flex v-center input-box">
                  <div class=" input-box-inner v-center ">
                    <span  class="v-center input-box-inner-span  " type="datetime">{{ entry.hours }}</span>
                    <!-- <img  src="~@/assets/images/eye.svg" alt="" class="input-box-eye" @click="openTimeSheetEditModal(prop,index,entry)"> -->
                  </div>
               Hrs
                </div>
                 </td>
            </tr>
           <tr>
           </tr>
          </tbody>
          <tbody v-else>
            <tr>
              <td colspan="8">
                <div  class="mt-4 h-center v-center"> <i>no time sheet data is available</i></div>
              </td>
            </tr>
          </tbody>

        </table>

  </div>

  <modal
      :open="timeSheetModalData.open"
      @close="timeSheetModalData.open = false"
      :closeOnOutsideClick="true"
      title="Edit time sheet"
    >
   <!-- <time-sheet-edit-modal
   :timeSheetModalData="timeSheetModalData"
   @close="timeSheetModalData.open = false"
    @addMoreTimeData="addMoreTimeData"
   /> -->
    </modal>
</div>
  </template>

<script>
import { convertToTimeFormat, dateFormodalConverter } from '@/utils/timeSheetHelper'
import modal from '../common/modal.vue'
// import timeSheetEditModal from './timeSheetEditModal.vue'
// import { alert, success } from '@/plugins/notification'
import { mapGetters } from 'vuex'
export default {
  components: {
    modal
    // timeSheetEditModal
  },
  name: 'timeSheetTable',
  props: {
    date: {
      type: Object,
      default: () => ({ weekDays: [], formatedDates: [] })
    },
    lastWeekDates: {
      type: Array,
      default: () => ([])
    },
    selectedProjectId: {
      type: String
    },
    projectsData: {
      type: Object,
      default: () => ({})
    },
    userList: {
      type: Object,
      default: () => ({})
    }

  },
  data: () => ({
    timeSheetData: {},
    timeSheetModalData: {
      open: false,
      prop: null,
      index: null
    },
    selectProjectBox: false,
    searchKeyWord_poj: '',
    startDate: '',
    endDate: '',
    selectedDate: null,
    availableProjects: [],
    open: false,
    projectNames: {},
    componentKey: 0,
    filterProjectsData: []
  }),
  computed: {
    ...mapGetters(['user', 'getUserById']),
    getUserId () {
      return (this.user.userId)
    },
    checkForTimeSheetData () {
      if (this.projectsData) {
        if (Object?.keys(this.projectsData)?.length > 0) return true
        else return false
      } else return false
    }
  },
  methods: {
    validateEnteredValue (prop, index, e) {
      const newtime = convertToTimeFormat(String(String(e.target.value)))
      this.projectsData[prop][index].duration = newtime
      if (this.projectsData[prop][index].id) {
        this.projectsData[prop][index].change = 'update'
      } else {
        this.projectsData[prop][index].change = 'new'
      }
      this.forceRender()
    },
    forceRender () {
      this.componentKey += 1
    },
    openTimeSheetEditModal (prop, index, entry) {
      this.timeSheetModalData = {
        open: true,
        prop: prop,
        index: index,
        selectedDate: dateFormodalConverter(this.date.formatedDates[index]),
        selectedProject: {
          id: prop,
          name: this.projectNames[prop]
        },
        entry: entry
      }
    },
    addMoreTimeData (data) {
      this.timeSheetModalData.open = false
      this.projectsData[data.timeSheetModalData.prop][data.timeSheetModalData.index] = data.entryData

      // this.forceRender()
    }

  },
  mounted () { },
  watch: {}
}
</script>

  <style lang="scss" scoped>

  .time-sheet {
    height: calc(100% - 147px);
    margin-top: 10px;
   &-table{
    &--container {
      height: calc(100%);
      overflow: auto;
      table {
        width: 100%;
        position: relative;
        border-collapse: collapse;
        th {
          position: sticky;
          top: -1px;
          font-weight: 500;
          background-color: var(--brand-color);
        }
        th,
        td {
          text-align: left;
          font-size: 12px;
          padding: 8px 4px;
        }
        td {
         & span{
          width: 70px;
          border: none;
        }
       & .input-box{
        gap: 4px;
        &-inner{
          font-size: 13px;
          display: flex;
          justify-content: space-around;
          border:1px solid #e5e5e5;
          padding:6px;
          position: relative;
          img {
        position: absolute;
        max-height: 70%;
        max-width: 70%;
        object-fit: contain;
        opacity:.6;
        top:3px;
        right: 4px;
        display:none;
      }
      &:hover > img{
        display: block;
      }
        }
        }
      }
        tr:nth-child(odd) {
          background-color: rgba(var(--brand-rgb), 0.05);
          border: 1px solid var(--brand-color);
        }
      }
    }
  }
}
.form-input{
  width: 200px;
  z-index:2;
  &--options
  {
  width: 200px;
}
}
  </style>
