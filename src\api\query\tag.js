import { GQL } from '../graphQl'

// for Attaching Tags to Material Master, Task

export const getChildrenTags = () => GQL`query get_children_tags($value: String!, $parent_id: uuid, $typeId: smallint) {
  tag(
    where: {name: {_iregex: $value}, parent_id: {_eq: $parent_id}, tag_type: {id: {_eq: $typeId}}}
  ) {
    parent_id
    name
    id
  }
}`

export const getParentTags = () => GQL`query get_parent_tags($value: String!, $typeId: smallint) {
  tag(
    where: {name: {_iregex: $value}, parent_id: {_is_null: true}, tag_type: {id: {_eq: $typeId}}}
  ) {
    parent_id
    name
    id
  }
}`

export const insertTags = () => GQL`mutation insert_tags_for_task_and_material_master($tagName: String!, $parentId: uuid, $typeId: smallint) {
  insert_tag_one(object: {name: $tagName, parent_id: $parentId, type: $typeId}) {
    id
    name
    parent_id
  }
}`

export const checkChildTags = () => GQL`query checkChildTagsQuery($tagName: String!, $parentId: uuid!, $typeId: smallint) {
  tag(
    where: {name: {_eq: $tagName}, parent_id: {_eq: $parentId}, tag_type: {id: {_eq: $typeId}}}
  ) {
    id
    name
    parent_id
  }
}`

export const checkRootTags = () => GQL`query rootTag($tagName: String!, $typeId: smallint) {
  tag(
    where: {parent_id: {_is_null: true}, name: {_eq: $tagName}, tag_type: {id: {_eq: $typeId}}}
  ) {
    id
    name
  }
}`

//  for Material Master

export const addMaterialTags = () => GQL`mutation insert_material_tag($tag_materials:[tag_material_insert_input!]!) {
insert_tag_material(objects:$tag_materials) {
affected_rows
}}`

export const getMaterialTags = () => GQL`query tag_material(
  $tags:[Int!]!
){
  tag_material(where: {tag_id: {_in: $tags}}) {
    material_id
  }
}
`

export const deleteMaterialTags = () => GQL`mutation deleteTagMaterial( $materialId: uuid!) {
  delete_tag_material(
    where: {material_id: {_eq: $materialId}}
  ) {
    affected_rows
  }
}`

export const CreateTag = () => GQL`mutation CreateTagMutation($tagName: String!, $parentId: uuid, $type: smallint!) {
  insert_tag_one(object: {name: $tagName, parent_id: $parentId, type: $type}) {
    id
    name
    parent_id
  }
}`

export const checkTag = () => GQL`query MyQuery($tagName: String!, $parentId: uuid!, $type: smallint!) {
  tag(
    where: {name: {_eq: $tagName}, parent_id: {_eq: $parentId}, tag_type: {id: {_eq: $type}}}
  ) {
    id
    name
    parent_id
  }
}`

export const rootTag = () => GQL`query rootTag($tagName: String!, $type: smallint!) {
  tag(
    where: {parent_id: {_is_null: true}, name: {_eq: $tagName}, tag_type: {id: {_eq: $type}}}
  ) {
    id
    name
  }
}`

export const displayRootTag = () => GQL`query root_material_tag {
  tag(where: {parent_id: {_is_null: true}, tag_type: {name: {_eq: "Material"}}}) {
    name
    id
  }
}
`
export const displayChildTag = () => GQL`query child_material_tag($parent_id: uuid) {
  tag(where: {parent_id: {_eq: $parent_id}, tag_type: {name: {_eq: "Material"}}}) {
    id
    name
  }
}`

export const updateTagName = () => GQL`mutation updating_material_tag_name($id: uuid, $name: String, $type: smallint!) {
  update_tag(
    where: {id: {_eq: $id}, tag_type: {id: {_eq: $type}}}
    _set: {name: $name}
  ) {
    affected_rows
  }
}
`
//  Task Tags

export const addTaskTags = () => GQL`mutation insert_task_tags($tag_tasks: [tag_task_insert_input!] = {}) {
  insert_tag_task(objects: $tag_tasks) {
    affected_rows
  }
}
`
export const getTaskTag = () => GQL`query tag_task($tags: [uuid!]!) {
  tag_task(where: {tag_id: {_in: $tags}}) {
    task_id
  }
}`

export const deleteTaskTag = () => GQL`mutation DeleteTagTask($id: uuid!) {
  delete_tag_task(where: {task_id: {_eq: $id}}) {
    affected_rows
  }
}`
export const deleteTaskTagsArray = () => GQL`mutation DeleteTagTaskMany($ids: [uuid!]!) {
  delete_tag_task(where: {task_id: {_in: $ids}}) {
    affected_rows
  }
}`
export const createTaskTag = () => GQL`mutation insert_task_tag($taskTagName: String!, $parentId: uuid) {
  insert_tag_one(object: {name: $taskTagName, parent_id: $parentId, type: "2"}) {
    id
    name
    parent_id
  }
}`

export const taskParentTag = () => GQL`query task_root_tag {
  tag(where: {parent_id: {_is_null: true}, tag_type: {name: {_eq: "Task"}}}) {
    name
    id
  }
}
`
export const taskChildTag = () => GQL`query task_child_tag($parent_id: uuid) {
  tag(where: {parent_id: {_eq: $parent_id}, tag_type: {name: {_eq: "Task"}}}) {
    id
    name
    parent_id
  }
}`

export const taskRootTag = () => GQL`query rootTag($tagName: String!) {
  tag(
    where: {parent_id: {_is_null: true}, name: {_eq: $tagName}, tag_type: {name: {_eq: "Task"}}}
  ) {
    id
    name
  }
}`

export const updateTaskTagName = () => GQL`mutation updating_task_tag_name($id: uuid, $name: String) {
  update_tag(
    where: {id: {_eq: $id}, tag_type: {name: {_eq: "Task"}}}
    _set: {name: $name}
  ) {
    affected_rows
  }
}`

export const checkTaskTag = () => GQL`query checkTaskTag($tagName: String!, $parentId: uuid!) {
  tag(
    where: {name: {_eq: $tagName}, parent_id: {_eq: $parentId}, tag_type: {name: {_eq: "Task"}}}
  ) {
    id
    name
    parent_id
  }
}`

export const GetAllTaskTagsQuery = () => GQL`query task_root_tag {
  tag(where: {tag_type: {name: {_eq: "Task"}}}) {
    name
    id
    parent_id
    __typename
  }
}`
export const GetAllResourceTagsQuery = () => GQL`query task_root_tag {
  tag(where: {tag_type: {name: {_eq: "Material"}}}) {
    name
    id
    __typename
  }
}`

export const GetRootLevelTagsQuery = () => GQL`query GetRootLevelTagsQuery ($type: smallint!) {
  tag (where: {parent_id : {_is_null: true}, tag_type: {id: {_eq: $type}}}) {
    name
    id
    parent_id
    tag_type {
      id
    }
  }
}`

export const GetChildLevelTagsQuery = () => GQL`query GetChildLevelTagsQuery ($type: smallint!, $parentId: uuid) {
  tag (where: {parent_id : {_eq: $parentId}, tag_type: {id: {_eq: $type}}}) {
    name
    id
    parent_id
    tag_type {
      id
    }
  }
}`
