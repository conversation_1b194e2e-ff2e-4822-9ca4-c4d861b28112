<template>
  <div class="form-playground">
    <nav class="v-center space-between bg-brand py-1 px-3">
      <input type="text" ref="formNameInput" v-model="formData.formName" :editonly="isEdit"/>
      <div class="flex">
        <button class="btn btn-black mx-2" @click="previewForm = true" v-if="formTemplateBody.length">
          Preview
        </button>
        <router-link to="/settings/copy-forms">
        <button class="btn btn-black mr-2 pointer">Cancel</button>
        </router-link>
        <button v-if="isEdit" @click="updateForm" class="btn btn-black pointer">Update</button>
        <button v-else class="btn btn-black pointer" @click="saveForm">Save</button>
      </div>
    </nav>
    <div class="form-playground-container">
      <div class="form-input grid-3">
        <div>
          <label>Form Type</label>
          <select v-model="formData.formType" @change="changeFormType" :disabled="isEdit">
            <option v-for="type in filteredFormTypeList" :key="type.id" :value="type.id">
              {{ type.name }}
            </option>
          </select>
        </div>
        <div>
          <label :class="{ required :formData.formType === 14}">Workflow Template</label>
          <div>
  <custom-dropdown :list="workflowTempaltesFiltered" :selected="formData.selectedWorkflow" @select="(WFT)=>formData.selectedWorkflow={id:WFT.id, name:WFT.name, data: WFT}" @toggle="WFTSearchKeyword = ''" @deselect="()=>formData.selectedWorkflow={}" class="form-template-container-wft">
  <template #before-items>
    <div class="fh center py-4" v-if="WFTLoading && workflowTempaltes.length === 0"  >
          <loading-circle />
        </div>
    <div class="fh center py-4" v-else-if="!WFTLoading && workflowTempaltes.length === 0"  >
         No Workflow Templates
        </div>
    <div class="fh center p-3 form-template-container-wft-search-box" v-else  >
        <input type="text p-2" class="form-template-container-wft-input"  v-model="WFTSearchKeyword" placeholder="Search"/>
        </div>
  </template>
  </custom-dropdown>
</div>
  </div>
    <div>
      <label>Sequence Template</label>
      <select v-model="formData.sequence_template_id">
        <option
          v-for="template in formSequenceTemplates"
          :key="template.id"
          :value="template.id"
        >
          {{ template.name }}
        </option>
      </select>
    </div>
      </div>
      <div class="form-playground-autogenerated">
        <template  v-for="(ele, index) in formTemplateBody">
          <div :key="index" v-if="ele.autogenerated && (ele.field_name !== 'Project Id' && ele.field_name !== 'project_id')" >
            <span class="label">{{ele.field_name}}:</span>
            <span class="value" >--</span>
          </div>
        </template>
      </div>
      <template v-for="(ele, index) in formTemplateBody">
        <drop
          v-if="!ele.autogenerated"
          :key="index"
          @Drop="onDrop"
          @click="selectToEdit(index)"
          class="form-playground-drop-element pointer"
          :class="{
            'form-playground-drop-element--selected':
              index === selectedFormElementToEdit,
          }"
        >
          <component
          @click="selectToEdit(index)"
            :viewOnly="true"
            :is="ele.key + '_COMPONENT'"
            :data="ele"
            mode="TEMPLATE"
          />
        </drop>
      </template>
      <div class="form-playground-builder">
        <drop @Drop="onDrop" class="form-playground-drop">
          <div class="form-playground-drop-text l weight-500">Drag and drop form elements here</div>
        </drop>
      </div>
    </div>
    <modal
      :open="previewForm"
      title="Preview"
      :closeOnOutsideClick="true"
      @close="previewForm = false"
      mode="TEMPLATE"
    >
      <div class="form-playground-preview">
        <form-preview
          :formName="formData.formName"
          :formType="getFormType.name"
          :formTemplateBody="formTemplateBody"
        />
      </div>
    </modal>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { createForm, getFullWorkFLowTemplates, updateForm, GetTemplateData } from '@/api'
import { alert, success } from '@/plugins/notification'
import Modal from '../common/modal.vue'
import FormPreview from './formPreview.vue'
import Drop from '@/components/common/drop.vue'
import config from '@/config'

import dateComponent from '@/components/form/elements/dateComponent.vue'
import longTextComponent from '@/components/form/elements/longTextComponent.vue'
import numberComponent from '@/components/form/elements/numberComponent.vue'
import switchComponent from '@/components/form/elements/switchComponent.vue'
import textComponent from '@/components/form/elements/textComponent.vue'
import companyComponent from '@/components/form/elements/companyComponent.vue'
import timeComponent from '@/components/form/elements/timeComponent.vue'
import tagsComponent from '@/components/form/elements/tagsComponent.vue'
import configurationList from './elements/configurationList.vue'
import fileComponent from './elements/fileComponent.vue'
import userComponent from './elements/userComponent.vue'
import multiUserComponent from './elements/multiUserComponent.vue'
import materialComponent from './elements/materialComponent.vue'
import locationComponent from './elements/locationComponent.vue'
import productCodeComponent from './elements/productCodeComponent.vue'
import bomComponent from './elements/bomComponent.vue'
import documentsComponent from './elements/documentsComponent.vue'
import customDropdown from '@/components/common/customDropdown.vue'
import loadingCircle from '@/components/common/loadingCircle.vue'

import Loader from '@/plugins/loader'
export default {
  name: 'FormPlayground',
  components: {
    Drop,
    Modal,
    FormPreview,
    customDropdown,
    loadingCircle,

    BOOLEAN_COMPONENT: switchComponent,
    NUMBER_COMPONENT: numberComponent,
    TEXT_COMPONENT: textComponent,
    LONG_TEXT_COMPONENT: longTextComponent,
    DATE_COMPONENT: dateComponent,
    TIME_COMPONENT: timeComponent,
    COMPANY_COMPONENT: companyComponent,
    CONFIGURATION_LIST_COMPONENT: configurationList,
    ATTACHMENT_COMPONENT: fileComponent,
    USER_COMPONENT: userComponent,
    MULTI_USER_COMPONENT: multiUserComponent,
    MATERIALS_COMPONENT: materialComponent,
    LOCATION_COMPONENT: locationComponent,
    PRODUCT_CODE_COMPONENT: productCodeComponent,
    BOM_COMPONENT: bomComponent,
    TAGS_COMPONENT: tagsComponent,
    DOCUMENTS_COMPONENT: documentsComponent
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      formSequenceTemplates: [],
      editFormName: true,
      previewForm: false,
      formData: {
        formName: 'Template Name',
        formType: '',
        selectedWorkflow: {},
        sequence_template_id: null
      },
      verify: true,
      config: config,
      workflowTempaltes: [],
      WFTSearchKeyword: '',
      WFTLoading: false
    }
  },
  computed: {
    ...mapGetters('form', [
      'formFields',
      'formTypeList',
      'formTemplateBody',
      'selectedFormElementToEdit',
      'preventElementSelection',
      'formTemplateName',
      'formTemplateType',
      'linkedWFTemplate',
      'linkedFormSequenceId'
    ]),
    filteredFormTypeList () {
      return this.formTypeList.filter(type =>
        type.id !== this.config.STANDARD_MATERIAL_FORM.form_type &&
        type.id !== this.config.STANDARD_BOM_FORM.form_type
      )
    },
    getFormType () {
      return (
        this.formTypeList.find(
          (type) => type.id === this.formData.formType
        ) || { name: '' }
      )
    },
    workflowTempaltesFiltered () {
      let templates = this.workflowTempaltes
      if (this.WFTSearchKeyword?.toLowerCase()) {
        templates = templates.filter(t =>
          t.name.toLowerCase().includes(this.WFTSearchKeyword?.toLowerCase())
        )
      }
      if (this.formData.formType === this.config.DOCUMENT_REVISIONING_FORM.form_type) {
        templates = templates.filter(t => t.feature_id === this.config.CORE_FEATURES.DOCUMENTS)
      } else {
        templates = templates.filter(t => t.feature_id !== this.config.CORE_FEATURES.DOCUMENTS)
      }
      return templates
    }
  },
  methods: {
    changeFormType () {
      const formDefaultFields = (
        this.formTypeList.find((type) => type.id === this.formData.formType)
          .default_fields || []
      ).map((type) => {
        const key = this.formFields.find(
          (field) => field.id === type.field_type_id
        ).key
        return {
          ...type,
          key
        }
      })
      this.$store.commit(
        'form/setFormTemplateType',
        this.formData.formType
      )
      this.$store.dispatch('form/setSelectedFormElementToEdit', null)
      this.$store.dispatch('form/removeTemplateElementFormTemplateBody')
      this.$store.dispatch(
        'form/addTemplateElementToTemplateBody',
        formDefaultFields
      )
    },
    selectToEdit (index) {
      if (this.preventElementSelection) {
        alert('You have to add a default custom list value to Configuration List')
        return
      }
      this.$store.dispatch('form/setSelectedFormElementToEdit', index)
    },
    onDrop (event) {
      const elementData = event.data
      const data = {
        key: elementData.key,
        field_name: elementData.caption,
        field_type_id: elementData.id,
        caption: elementData.caption,
        autogenerated: false,
        fixed: false,
        required: false,
        custom_list_id: null
      }
      this.$store.dispatch('form/addElementToFormTemplateBody', data)
    },
    openFormNameEdit () {
      this.editFormName = true
      this.$nextTick(() => {
        this.$refs.formNameInput.focus()
      })
    },
    updateForm () {
      this.verify = true
      if (!this.formData.formType) {
        alert('Please select form type')
        return
      }
      if (!this.formData.formName) {
        alert('Please enter form name')
        return
      }
      if (!this.formData.selectedWorkflow?.id && this.formData.formType === 14) {
        alert('Please select a workflow template')
        return
      }
      const toKebabCase = (str) =>
        str &&
        str
          .match(
            /[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g
          )
          .map((x) => x.toLowerCase())
          .join('_')
      if (this.formData.formName === 'Template Name' || this.formData.formName === null || this.formData.formName === '') {
        alert('check the form template Name')
        return
      }
      let visibilityCount = 0
      const formNameSet = new Set()
      let duplicateFieldNames = false
      const body = this.formTemplateBody.map((element, index) => {
        if (formNameSet.has(element.caption)) {
          duplicateFieldNames = true
          this.verify = false
        } else {
          formNameSet.add(element.caption)
        }
        if (element.visibility) {
          visibilityCount++
        }
        if (element.key === 'CONFIGURATION_LIST') {
          if (!element.custom_list_id) {
            alert('You have to add a default custom list value to Configuration List ')
            this.verify = false
          }
        }
        return ({
          field_id: element.field_id ?? crypto.randomUUID(),
          field_name: toKebabCase(element.caption),
          field_type_id: element.field_type_id,
          caption: element.caption,
          required: element.required,
          custom_list_id: element.custom_list_id,
          autogenerated: element.autogenerated,
          fixed: element.fixed,
          visibility: element.visibility ?? false
        })
      })
      if (duplicateFieldNames) {
        alert('Template contains duplicate field names')
      }
      if (visibilityCount > 2) {
        alert('You can only select two fields to show in table')
        this.verify = false
        return
      } else if (visibilityCount < 2) {
        alert('You have to select two fields to show in table')
        this.verify = false
        return
      }
      if (this.verify) {
        const loader = new Loader()
        loader.show()
        const templateId = this.$route.params.formId
        updateForm(templateId, body, this.formData.formName, this.formData.selectedWorkflow.id, this.formData.sequence_template_id)
          .then(() => {
            success('Form updated successfully')
            this.$router.push('/settings/copy-forms')
            this.verify = null
          })
          .catch(() => {
            alert('Error updating form')
            this.verify = null
          }).finally(() => {
            loader.hide()
          })
      }
    },
    async getTemplatesForWFL () {
      try {
        this.WFTLoading = true
        this.workflowTempaltes = []
        const res = await getFullWorkFLowTemplates()
        this.workflowTempaltes = res.workflow_templates
        if (res) {
          GetTemplateData().then(res => {
            const templates = res?.core_sequence_id_template || []

            this.formSequenceTemplates = templates.filter(
              template => template.core_feature?.id === 5
            )
          })
        }
        this.WFTLoading = false
      } catch (error) {
        alert('Error fetching  form templates')
        this.WFTLoading = false
        console.error('Error fetching templates', error)
      }
    },
    saveForm () {
      this.verify = true
      if (!this.formData.formType) {
        alert('Please select form type')
        return
      }
      if (!this.formData.formName) {
        alert('Please enter form name')
        return
      }
      if (!this.formData.selectedWorkflow.id && this.formData.formType === 14) {
        alert('Please select a workflow template')
        return
      }
      const toKebabCase = (str) =>
        str &&
        str
          .match(
            /[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g
          )
          .map((x) => x.toLowerCase())
          .join('_')
      if (this.formData.formName === 'Template Name' || this.formData.formName === null || this.formData.formName === '') {
        alert('check the form template Name')
        return
      }
      let visibilityCount = 0
      const formNameSet = new Set()
      let duplicateFieldNames = false
      const body = this.formTemplateBody.map((element, index) => {
        if (formNameSet.has(element.caption)) {
          duplicateFieldNames = true
          this.verify = false
        } else {
          formNameSet.add(element.caption)
        }
        if (element.visibility) {
          visibilityCount++
        }
        if (element.key === 'CONFIGURATION_LIST') {
          if (!element.custom_list_id) {
            alert('You have to add a default custom list value to Configuration List ')
            this.verify = false
          }
        }
        return ({
          field_name: toKebabCase(element.caption),
          field_type_id: element.field_type_id,
          caption: element.caption,
          required: element.required,
          custom_list_id: element.custom_list_id,
          autogenerated: element.autogenerated,
          fixed: element.fixed,
          visibility: element.visibility
        })
      })
      if (duplicateFieldNames) {
        alert('Template contains duplicate field names')
        this.verify = false
      }
      if (visibilityCount > 2) {
        alert('You can only select two fields to show in table')
        this.verify = false
        return
      } else if (visibilityCount < 2) {
        alert('You have to select two fields to show in table')
        this.verify = false
        return
      }
      if (this.verify) {
        const loader = new Loader()
        loader.show()
        createForm(body, this.formData.formType, this.formData.formName, this.formData.selectedWorkflow.id, this.formData.sequence_template_id)
          .then(() => {
            this.verify = null
            let successMessage = 'Form created successfully'
            if (this.formData?.selectedWorkflow?.data?.system_generated && this.formData?.selectedWorkflow?.data?.feature_id === config.CORE_FEATURES.DOCUMENTS) {
              successMessage += ' with the default document workflow template'
            }
            success(successMessage)
            this.$router.push('/settings/copy-forms')
          })
          .catch(() => {
            this.verify = null
            alert('Error saving form')
            loader.hide()
          }).finally(() => {
            loader.hide()
          })
      }
    }
  },
  created () {
    if (this.$props.isEdit) {
      this.formData = {
        formName: this.formTemplateName,
        formType: this.formTemplateType,
        selectedWorkflow: this.linkedWFTemplate,
        sequence_template_id: this.linkedFormSequenceId.id
      }
    }
    this.getTemplatesForWFL()
  }
}
</script>

<style lang="scss" scoped >
.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem; /* adjust as per your current spacing, e.g., 16px or 1.5rem */
  align-items: start;
}
.form-playground {
  height: 100%;
  position: relative;
  nav {
    position: sticky;
    top: 0;
    z-index: 1;
  }
  input {
    border: 1px solid #3B3B3B77;
    height: 30px;
    display: block;
    border-radius: 0.285em;
    font-size: 1em;
    padding: 0.85em;
    &::placeholder {
      color: var(--text-color);
      opacity: 0.15;
    }
    &:focus {
      box-shadow: 0 0 0 1px var(--brand-color-1);
    }
  }
  &-builder {
    background-color: #f5f5f5;
    position: sticky;
    bottom: 0px;
    z-index: 1;
  }
  &-drop {
    padding: 20px;
    text-align: center;
    font-size: 20px;
    border: 1px dashed #ccc;
    border-radius: 5px;
    // margin: 0 auto;
    margin-top: 20px;
    &.onoverclass {
      background-color: #ccc;
    }
  }
  &-drop-element {
    padding: 10px;
    border-radius: 5px;
    &:hover {
      border: 1px dashed #4cb5ff;
    }
    &--selected {
      border: 1px solid #4cb5ff !important;
    }
  }
  &-container {
    height: calc(100% - 50px);
    padding: 20px;
  }
  &-preview {
    width: 500px;
    max-height: 70vh;
    overflow-y: auto;
  }
  &-autogenerated {
    background-color: rgba(var(--brand-rgb), 0.2);
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    grid-gap: 10px;
    & .label {
      font-size: 14px;
      color: var(--brand-color-1);
      font-weight: 500;
    }
    & .value {
      margin-left: 6px;
      font-size: 16px;
      color: var(--text-color);
    }
  }
  .required {
    &::after {
        content: '*';
        color: var(--brand-color-1);
        font-size: 1.2em;
        margin-left: 0.1em;
   }
  }
}
</style>
