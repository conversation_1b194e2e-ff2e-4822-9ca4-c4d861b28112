<template>
  <div class="bom-list-container">
  <div class="empty-bom-list">
    <div v-if="loadingBomList && !bomList.length" style="padding-left: 33rem; padding-top: 20rem">
    <loading-circle/>
    </div>
    <div v-else-if="obsoleteProductCode">
      <h3 >
        Obsolete Product Code!
      </h3>
    </div>
    <div v-else-if="!loadingBomList && !bomList.length" style="padding-left: 33rem; padding-top: 20rem">
      <h3>
          No bom found
        </h3>
    </div>
    <div v-else class="bom-item" >
    <template>
      <div class="empty-bom-list__header s  ">
     <div class="space-between p-2 v-center">
      <h2 class="weight-500 l">BOM List</h2>
     </div>
    </div>
      <div
      v-for="bom in getBomList"
        :key="bom.name"
        @click="openBomDetail(bom)">
        <div class="product-code-item v-center"  :class="{
        selected: bom.id == selectedBomId
      }">
      <div v-overflow-tooltip class="product-code-item__name v-center s elipsis-text "  >{{bom.name }}
      </div>
      <div>
        <img
          v-if="bom.state === 2"
          src="~@/assets/images/icons/checkin-icon.svg"
          class="icon"
          v-tooltip="'Bom is checked in'"
        />
        <img
        v-else-if="bom.state === 3"
         class="icon"
          src="~@/assets/images/icons/checkout-icon.svg"
          v-tooltip="'Bom is checked out'"
        />
        <img
          v-else-if="bom.state === 1"
           class="icon"
          src="~@/assets/images/icons/locked-icon.svg"
          v-tooltip="'Bom is locked'"
        />
        <img
        v-else-if="bom.state === OBSOLETE"
         class="icon"
          src="~@/assets/images/icons/obsolete-icon.svg"
          v-tooltip="'Bom is Obsolete'"
        />
      </div>
      </div>
    </div>
    </template>
  </div>
</div>
<div  class="bom-detail-view">
<router-view />
</div>
</div>
</template>

<script>
import { mapGetters } from 'vuex'
import loadingCircle from '../../../components/common/loadingCircle.vue'
import Config from '@/config'
export default {
  components: { loadingCircle },
  name: 'productBomList',
  data () {
    return {
      loadingBomList: false,
      cid: null,
      OBSOLETE: Config.BOM_STATE_MAP.OBSOLETE
    }
  },
  watch: {
    selectedProductCode () {
      this.setBaseData()
    },
    '$route.query.cid': function (newCid) {
      this.cid = newCid
    }
  },
  computed: {
    ...mapGetters('productBom', ['bomList', 'selectedProductCode', 'productCodeTree', 'bomProdSearchKeyword', 'selectedBomId']),
    ...mapGetters(['user']),

    createBomDisable () {
      if (this.user.tenantLevelRole === 'ADMIN' ||
            this.user.tenantLevelRole === 'COLLABORATOR') { return false } else return true
    },
    getBomList () {
      return this.bomList
        .filter((bom) => {
          if (!this.bomProdSearchKeyword || (this.bomProdSearchKeyword && bom.name.toLowerCase().includes(this.bomProdSearchKeyword))) {
            if (this.user.tenantLevelRole !== 'ADMIN') {
              if (bom.state === 3 && bom.checked_out_by) {
                if (bom.checked_out_by !== this.user.userId && bom.bom_versions.length <= 1) {
                  return false
                }
              }
            }
            return true
          }
        })
        .map((bom) => {
          return {
            ...bom,
            stateValue: Config.STATE_MAP[bom.state]
          }
        })
    },
    obsoleteProductCode () {
      return this.productCodeTree.items[this.cid]?.active === false && !this.bomList.length
    },
    obsoleteProductAssociatedBom () {
      return this.productCodeTree.items[this.cid]?.active === false
    }
  },
  methods: {
    bomStatus (bom) {},
    openBomDetail (bom) {
      this.$router.push(
        `/bom/product/${this.selectedProductCode}/bom/${bom?.id}${
          bom.bom_versions?.[0]?.id
            ? `?bomVersionId=${bom.bom_versions?.[0]?.id}`
            : ''
        }`
      )
    },
    setBaseData () {
      this.loadingBomList = true
      this.$store.dispatch('productBom/getAllBomList', () => {
        this.loadingBomList = false
      })
    }
  },
  created () {
    this.setBaseData()
    this.cid = this.$route.query.cid
  }
}
</script>

<style lang="scss" scoped >
.bom-item{
  min-width: 20rem;
  max-width: 22rem;
  height:100%;
  overFlow: auto;
  background-color: var(--bg-color);
  border: 1px solid var(--brand-color);
  @media screen and (max-width: 1366px) {
    min-width: 15rem;
    max-width: 15rem;
  }
}
.product-code-item {
  border: 1px solid var(--brand-color);
  background-color: rgba(var(--brand-rgb), 0.2);
  margin: 6px 6px;
  padding-left: 6px;
  cursor: pointer;
  &.selected {
    background-color: rgba(var(--brand-rgb), 0.6);
  }
  &__name {
    height: 30px;
    width: 20rem;
  }
  & .icon {
    width: 20px;
  }
}
.empty-bom-list {
  &__header {
    position: sticky;
    top: 0;
    z-index: 1;
    background-color: var(--brand-color);
    display: flex;
    flex-direction: column;

  }

  h3 {
    font-size: 20px;
    font-weight: 500;
  }
}
.red-border {
  border: 1px solid red;
}
.bom-list-container{
  height:100%;
  display: flex;
  transition: max-height 0.5s ease;
  .bom-detail-view{
    flex: 1;
    overflow-x: auto;
    overflow-y: auto;
    padding-left: 5px;
  }
}
</style>
