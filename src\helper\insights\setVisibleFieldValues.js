const setVisibleFieldValues = (formData, templateId, templateVisibleFieldsMap, isProject) => {
  let visibleFieldValue1 = '--'
  let tooltip1 = ''
  let visibleFieldValue2 = '--'
  let tooltip2 = ''
  if (templateVisibleFieldsMap[templateId]) {
    const visibleField = templateVisibleFieldsMap[templateId][0]
    switch (visibleField.key) {
    case 'BOOLEAN' : {
      for (const element of formData.forms_metadata_by_id) {
        if (element.field_id === visibleField.id) {
          visibleFieldValue1 = element.bool_value
          break
        }
      }
      break
    }
    case 'NUMBER' : {
      for (const element of formData.forms_metadata_by_id) {
        if (element.field_id === visibleField.id) {
          visibleFieldValue1 = element.int_value
          break
        }
      }
      break
    }
    case 'TEXT' : {
      for (const element of formData.forms_metadata_by_id) {
        if (element.field_id === visibleField.id) {
          visibleFieldValue1 = element.string_value
          break
        }
      }
      break
    }
    case 'LONG_TEXT': {
      for (const element of formData.forms_metadata_by_id) {
        if (element.field_id === visibleField.id) {
          visibleFieldValue1 = element.string_value
          break
        }
      }
      break
    }
    case 'DATE' : {
      for (const element of formData.forms_metadata_by_id) {
        if (element.field_id === visibleField.id) {
          visibleFieldValue1 = element.date_value
          break
        }
      }
      break
    }
    case 'TIME' : {
      for (const element of formData.forms_metadata_by_id) {
        if (element.field_id === visibleField.id) {
          visibleFieldValue1 = element.time_value
          break
        }
      }
      break
    }
    case 'CONFIGURATION_LIST' : {
      for (const element of formData.forms_config_lists) {
        if (element.field_id === visibleField.id) {
          visibleFieldValue1 = element.custom_list_value
          break
        }
      }
      break
    }
    case 'ATTACHMENT' : {
      let count = 0
      for (const attachment of formData.forms_attachments) {
        const fileName = attachment?.core_attachment?.file_name ?? attachment?.core_document?.doc_name
        if (attachment.field_id === visibleField.id) {
          if (count === 0) {
            visibleFieldValue1 = fileName
          } else if (count === 1) {
            visibleFieldValue1 += `, ${fileName}`
          } else {
            if (tooltip1?.length) {
              tooltip1 += `, ${fileName}`
            } else {
              tooltip1 = fileName
            }
          }
          count++
        }
      }
      if (count > 2) {
        visibleFieldValue1 += ` & ${ count - 2 } more`
      }
      break
    }
    case 'COMPANY': {
      for (const company of formData.forms_company_lists) {
        if (company.field_id === visibleField.id) {
          visibleFieldValue1 = company.company_name.replace(/^"|"$/g, '')
          break
        }
      }
      break
    }
    case 'USER' : {
      for (const user of formData.forms_user_lists) {
        if (user.field_id === visibleField.id) {
          visibleFieldValue1 = user.core_user.first_name + ' ' + user.core_user.last_name
          break
        }
      }
      break
    }
    case 'MULTI_USER' : {
      let count = 0
      for (const user of formData.forms_user_lists) {
        if (user.field_id === visibleField.id) {
          if (count === 0) {
            visibleFieldValue1 = user.core_user.first_name + ' ' + user.core_user.last_name
          } else if (count === 1) {
            visibleFieldValue1 += `, ${user.core_user.first_name + ' ' + user.core_user.last_name}`
          } else {
            if (tooltip1?.length) {
              tooltip1 += `, ${user.core_user.first_name + ' ' + user.core_user.last_name}`
            } else {
              tooltip1 = user.core_user.first_name + ' ' + user.core_user.last_name
            }
          }
          count++
        }
      }
      if (count > 2) {
        visibleFieldValue1 += ` & ${ count - 2 } others`
      }
      break
    }
    case 'MATERIALS': {
      let count = 0
      for (const material of formData.forms_material_lists) {
        if (material.field_id === visibleField.id) {
          if (count === 0) {
            visibleFieldValue1 = material.core_material.material_name
          } else if (count === 1) {
            visibleFieldValue1 += `, ${material.core_material.material_name}`
          } else {
            if (tooltip1?.length) {
              tooltip1 += `, ${material.core_material.material_name}`
            } else {
              tooltip1 = material.core_material.material_name
            }
          }
          count++
        }
      }
      if (count > 2) {
        visibleFieldValue1 += ` & ${ count - 2 } others`
      }
      break
    }
    case 'LOCATION' : {
      for (const element of formData.forms_metadata_by_id) {
        if (element.field_id === visibleField.id) {
          visibleFieldValue1 = element.point_value
          break
        }
      }
      break
    }
    case 'BOM' : {
      if (isProject) {
        let count = 0
        for (const material of formData.forms_bom_lists) {
          if (material.field_id === visibleField.id) {
            if (count === 0) {
              visibleFieldValue1 = material.core_bom.name
            } else if (count === 1) {
              visibleFieldValue1 += `, ${material.core_bom.name}`
            } else {
              if (tooltip2?.length) {
                tooltip2 += `, ${material.core_bom.name}`
              } else {
                tooltip2 = material.core_bom.name
              }
            }
            count++
          }
        }
        if (count > 2) {
          visibleFieldValue1 += ` & ${ count - 2 } others`
        }
      } else {
        visibleFieldValue1 = null
      }
      break
    }
    case 'TAGS' : {
      let count = 0
      if (formData.forms_tag_list) {
        for (const tags of formData.forms_tag_list) {
          if (tags.field_id === visibleField.id) {
            if (count === 0) {
              visibleFieldValue1 = tags.tag.name
            } else if (count === 1) {
              visibleFieldValue1 += `, ${tags.tag.name}`
            } else {
              if (tooltip1?.length) {
                tooltip1 += `, ${tags.tag.name}`
              } else {
                tooltip1 = tags.tag.name
              }
            }
            count++
          }
        }
      }
      if (count > 2) {
        visibleFieldValue1 += ` & ${ count - 2 } others`
      }
      break
    }
    }
    const visibleField2 = templateVisibleFieldsMap[templateId][1]
    switch (visibleField2.key) {
    case 'BOOLEAN' : {
      for (const element of formData.forms_metadata_by_id) {
        if (element.field_id === visibleField2.id) {
          visibleFieldValue2 = element.bool_value
          break
        }
      }
      break
    }
    case 'NUMBER' : {
      for (const element of formData.forms_metadata_by_id) {
        if (element.field_id === visibleField2.id) {
          visibleFieldValue2 = element.int_value
          break
        }
      }
      break
    }
    case 'TEXT' : {
      for (const element of formData.forms_metadata_by_id) {
        if (element.field_id === visibleField2.id) {
          visibleFieldValue2 = element.string_value
          break
        }
      }
      break
    }
    case 'LONG_TEXT': {
      for (const element of formData.forms_metadata_by_id) {
        if (element.field_id === visibleField2.id) {
          visibleFieldValue2 = element.string_value
          break
        }
      }
      break
    }
    case 'DATE' : {
      for (const element of formData.forms_metadata_by_id) {
        if (element.field_id === visibleField2.id) {
          visibleFieldValue2 = element.date_value
          break
        }
      }
      break
    }
    case 'TIME' : {
      for (const element of formData.forms_metadata_by_id) {
        if (element.field_id === visibleField2.id) {
          visibleFieldValue2 = element.time_value
          break
        }
      }
      break
    }
    case 'CONFIGURATION_LIST' : {
      for (const element of formData.forms_config_lists) {
        if (element.field_id === visibleField2.id) {
          visibleFieldValue2 = element.custom_list_value
          break
        }
      }
      break
    }
    case 'ATTACHMENT' : {
      let count = 0
      for (const attachment of formData.forms_attachments) {
        console.log(attachment)
        const fileName = attachment?.core_attachment?.file_name ?? attachment?.core_document?.doc_name
        if (attachment.field_id === visibleField2.id) {
          if (count === 0) {
            visibleFieldValue2 = fileName
          } else if (count === 1) {
            visibleFieldValue2 += `, ${fileName}`
          } else {
            if (tooltip2?.length) {
              tooltip2 += `, ${fileName}`
            } else {
              tooltip2 = fileName
            }
          }
          count++
        }
      }
      if (count > 2) {
        visibleFieldValue2 += ` & ${ count - 2 } more`
      }
      break
    }
    case 'COMPANY': {
      for (const company of formData.forms_company_lists) {
        if (company.field_id === visibleField2.id) {
          visibleFieldValue2 = company.company_name.replace(/^"|"$/g, '')
          break
        }
      }
      break
    }
    case 'USER' : {
      for (const user of formData.forms_user_lists) {
        if (user.field_id === visibleField2.id) {
          visibleFieldValue2 = user.core_user.first_name + ' ' + user.core_user.last_name
          break
        }
      }
      break
    }
    case 'MULTI_USER' : {
      let count = 0
      for (const user of formData.forms_user_lists) {
        if (user.field_id === visibleField2.id) {
          if (count === 0) {
            visibleFieldValue2 = user.core_user.first_name + ' ' + user.core_user.last_name
          } else if (count === 1) {
            visibleFieldValue2 += `, ${user.core_user.first_name + ' ' + user.core_user.last_name}`
          } else {
            if (tooltip2?.length) {
              tooltip2 += `, ${user.core_user.first_name + ' ' + user.core_user.last_name}`
            } else {
              tooltip2 = user.core_user.first_name + ' ' + user.core_user.last_name
            }
          }
          count++
        }
      }
      if (count > 2) {
        visibleFieldValue2 += ` & ${ count - 2 } others`
      }
      break
    }
    case 'MATERIALS': {
      let count = 0
      for (const material of formData.forms_material_lists) {
        if (material.field_id === visibleField2.id) {
          if (count === 0) {
            visibleFieldValue2 = material.core_material.material_name
          } else if (count === 1) {
            visibleFieldValue2 += `, ${material.core_material.material_name}`
          } else {
            if (tooltip2?.length) {
              tooltip2 += `, ${material.core_material.material_name}`
            } else {
              tooltip2 = material.core_material.material_name
            }
          }
          count++
        }
      }
      if (count > 2) {
        visibleFieldValue2 += ` & ${ count - 2 } others`
      }
      break
    }
    case 'LOCATION' : {
      for (const element of formData.forms_metadata_by_id) {
        if (element.field_id === visibleField2.id) {
          visibleFieldValue2 = element.point_value
          break
        }
      }
      break
    }
    case 'BOM' : {
      if (isProject) {
        let count = 0
        for (const material of formData.forms_bom_lists) {
          if (material.field_id === visibleField2.id) {
            if (count === 0) {
              visibleFieldValue2 = material.core_bom.name
            } else if (count === 1) {
              visibleFieldValue2 += `, ${material.core_bom.name}`
            } else {
              if (tooltip2?.length) {
                tooltip2 += `, ${material.core_bom.name}`
              } else {
                tooltip2 = material.core_bom.name
              }
            }
            count++
          }
        }
        if (count > 2) {
          visibleFieldValue2 += ` & ${ count - 2 } others`
        }
      } else {
        visibleFieldValue2 = null
      }
      break
    }
    case 'TAGS' : {
      let count = 0
      if (formData.forms_tag_list) {
        for (const tags of formData.forms_tag_list) {
          if (tags.field_id === visibleField2.id) {
            if (count === 0) {
              visibleFieldValue2 = tags.tag.name
            } else if (count === 1) {
              visibleFieldValue2 += `, ${tags.tag.name}`
            } else {
              if (tooltip2.length) {
                tooltip2 += `, ${tags.tag.name}`
              } else {
                tooltip2 = tags.tag.name
              }
            }
            count++
          }
        }
      }
      if (count > 2) {
        visibleFieldValue2 += ` & ${ count - 2 } others`
      }
      break
    }
    }
  }
  formData.customVisibleField1 = {
    value: visibleFieldValue1,
    tooltip: tooltip1
  }
  formData.customVisibleField2 = {
    value: visibleFieldValue2,
    tooltip: tooltip2
  }
}

export default setVisibleFieldValues
