.notificationContainer {
  position: fixed;
  width: 400px;
  right: 0px;
  top: 10px;
  z-index: 99999;
  font-size: 14px;
  .notification {
    color: #ffffff;
    &-success {
      background-color: var(--success);
    }
    &-danger {
      background-color: var(--alert);
    }
    &-warning {
      background-color: var(--warning);
    }
    &-light {
      background-color: var(--white);
      color: var(--black);
    }
    &-dark {
      background-color: var(--black);
      color: var(--white);
    }

    &-flag {
      margin: 5px;
      padding: 10px 20px;
      float: right;
      border-radius: 0.5rem;
      position: relative;
      opacity: 1;
      animation: openNotification 1s;
      width: 100%;
    }
    &-flag-out {
      margin: 5px;
      padding: 10px 20px;
      float: right;
      border-radius: 0.5em;
      width: 100%;
      transform-origin: right;
      animation: closeNotification 1s;
    }
    &-close {
      color: #ffffff;
      width: fit-content;
      position: absolute;
      top: 2px;
      right: 5px;
      font-size: 1.25em;
      cursor: pointer;
    }
    &-title {
      font-weight: bolder;
      font-size: 1em;
    }
    &-text {
      font-weight: bold;
      margin-top: 5px;
      font-size: 0.8em;
    }
  }
}

@keyframes openNotification {
  from {
    transform-origin: right;

    transform: scaleX(0);
  }
  to {
    transform-origin: right;

    transform: scaleX(1);
  }
}

@keyframes closeNotification {
  to {
    transform-origin: right;

    transform: scaleX(0);

  }
  from {
    transform-origin: right;

    transform: scaleX(1);

  }
}
