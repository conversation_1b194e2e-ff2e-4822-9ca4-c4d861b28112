<template>
  <router-view v-if="!loadding" />
  <loading v-else />
</template>
<script>
import loading from './components/common/loading.vue'
import { mapGetters } from 'vuex'
import config from './config'
export default {
  components: { loading },
  name: 'Main',
  data () {
    return {
      loadding: true,
      unProtectedRoutes: ['/login', '/signup', 'forgot-password']
    }
  },
  created () {
    const queryString = window.location.search
    const urlParams = new URLSearchParams(queryString)
    const tenantId = urlParams.get('tenantId')
    const targetTenantId = urlParams.get('targetTenantId')
    const projectId = urlParams.get('projectId')
    const redirectFrom = urlParams.get('redirectFrom') ?? 'email'
    const path = window.location.pathname
    if (tenantId) {
      localStorage.setItem(config.localstorageKeys.LAST_OPENED_TENANT, tenantId)
      localStorage.setItem(config.localstorageKeys.REDIRECT_FROM, redirectFrom)
      localStorage.setItem(config.localstorageKeys.LAST_PATH, path)
      if (targetTenantId) {
        localStorage.setItem(config.localstorageKeys.TARGET_TENANT_ID, targetTenantId)
      }
      if (projectId) {
        if (targetTenantId) {
          this.$store.commit('setProjectIdForCollaborator', projectId)
        } else {
          localStorage.setItem(config.localstorageKeys.LAST_OPENED_PROJECT, projectId)
        }
      }
    }
    const authToken = localStorage.getItem(config.localstorageKeys.AUTH)
    if (!(this.unProtectedRoutes.includes(path)) && authToken) {
      this.loadding = true
      this.$store.dispatch('setup', () => {
        if (this.unProtectedRoutes.includes(path)) {
          this.$router.replace('/')
        }
        localStorage.removeItem(config.localstorageKeys.REDIRECT_FROM)
        this.loadding = false
      })
    } else {
      this.loadding = false
    }
  },
  computed: {
    ...mapGetters([
      'collaborator'
    ])
  }
}
</script>
<style lang="scss">
</style>
