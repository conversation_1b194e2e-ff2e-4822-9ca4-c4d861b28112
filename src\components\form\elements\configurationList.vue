<template>
  <div :class="{
    'form-input': true,
    'form-input--required': data.required
    }" >
      <label>{{data.caption}}:
        <img
      v-if="data.visibility"
      class="mx-1"
      width="15"
      src="~@/assets/images/eye.svg"
      alt="view"
      />
      </label>
      <select :disabled="viewOnly" v-model="componentValue[0]"  @change="emitChange">
        <option v-for="item in list" :key="item.id" :value="item.id">
          {{item.name}}
        </option>
      </select>
  </div>
</template>

<script>
// import { GetCustomList } from '@/api'
import { mapGetters } from 'vuex'
export default {
  name: 'dateComponent',
  props: {
    mode: {
      type: String,
      default: 'TEMPLATE'
    },
    data: {
      type: Object,
      default: () => {}
    },
    value: {
      type: Array,
      default: () => ([])
    },
    viewOnly: {
      type: <PERSON>olean,
      default: false
    }
  },
  data () {
    return {
      listId: null,
      componentValue: [],
      currentValue: []
    }
  },
  watch: {
    value (val) {
      this.componentValue = val
    },
    data: {
      handler (val) {
        this.setListId()
      },
      deep: true
    }
  },
  methods: {
    setListId () {
      this.listId = this.data.custom_list_id || null
    },
    emitChange (e) {
      this.componentValue = [+e.target.value]
      let value = null
      if (this.componentValue[0] !== this.currentValue[0]) {
        value = this.componentValue
      }
      this.$emit('customChange', {
        field_id: this.$props.data.field_id,
        value
      })
    }
  },
  created () {
    this.setListId()
    this.componentValue = Array.isArray(this.value) ? this.value : []
    this.currentValue = JSON.parse(JSON.stringify(Array.isArray(this.value) ? this.value : []))
  },
  computed: {
    ...mapGetters('form', ['customListMap']),
    list () {
      return this.customListMap[this.listId] ?? []
    }
  }
}
</script>

<style>

</style>
