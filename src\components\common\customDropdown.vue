<template>
  <div id="dropdown" @mouseleave="visible = false">
    <div class="custom-select pointer" :class="{ compact }">
      <div class="selector">
        <div class="select-label" @click="toggle()">
          <div class=" elipsis-text mr-4" v-overflow-tooltip>
            {{ selected?.name ?? 'Select' }}
          </div>
        </div>
        <div class="arrow" :class="{ expanded: visible }" @click="toggle()"></div>

        <div
          class="dropdown"
          :class="[
            { hidden: !visible, visible },
            openUp ? 'dropdown-up' : 'dropdown-down'
          ]"
          :style="dropdownStyle"
        >
          <div class="dropdown-content" :class="{ 'upward': openUp }" @mouseleave="(e)=>e.stopPropagation()">
            <!-- When opening downward, slot (search) comes first -->
            <div v-if="!openUp" class="slot-container">
              <slot name="before-items"></slot>
            </div>

            <ul>
              <li
                :class="{ current: item.id === selected?.id }"
                v-for="item in filteredList"
                :key="item?.id"
                @click="select(item)">
                <div class="elipsis-text" v-overflow-tooltip>{{ item?.name }}</div>
                <div class="xs elipsis-text" v-if="showDescription" v-overflow-tooltip>({{ item?.description }})</div>
              </li>
            </ul>

            <div v-if="openUp" class="slot-container">
              <slot name="before-items"></slot>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomDropdown',
  props: {
    compact: {
      type: Boolean,
      default: false
    },
    list: {
      default: () => [],
      type: Array
    },
    selected: {
      default: () => ({ id: -1, name: 'Select' }),
      type: Object
    },
    searchText: {
      default: '',
      type: String
    },
    showDescription: {
      default: false,
      type: Boolean
    }
  },
  data () {
    return {
      visible: false,
      openUp: false,
      dropdownStyle: {}
    }
  },
  computed: {
    filteredList () {
      if (!this.searchText) return this.list
      if (this.showDescription) {
        return this.list.filter(item => {
          return (
            item.name.toLowerCase().includes(this.searchText.toLowerCase()) ||
            item.description.toLowerCase().includes(this.searchText.toLowerCase())
          )
        })
      } else {
        return this.list.filter(item => {
          return item.name.toLowerCase().includes(this.searchText.toLowerCase())
        })
      }
    }
  },
  methods: {
    toggle () {
      this.visible = !this.visible
      if (this.visible) {
        this.checkDropdownDirection()
      } else {
        this.dropdownStyle = {} // Clean up
      }
      this.$emit('toggle')
    },
    select (item) {
      if (item.id === this.selected?.id) {
        this.deselect()
        return
      }
      this.$emit('select', item)
      this.toggle()
    },
    deselect () {
      this.$emit('deselect')
      this.toggle()
    },
    checkDropdownDirection () {
      this.$nextTick(() => {
        const dropdown = this.$el.querySelector('.dropdown')
        const trigger = this.$el.querySelector('.select-label')
        if (!dropdown || !trigger) return

        const triggerRect = trigger.getBoundingClientRect()
        const dropdownHeight = dropdown.offsetHeight
        const spaceBelow = window.innerHeight - triggerRect.bottom
        const spaceAbove = triggerRect.top

        if (dropdownHeight > spaceBelow && spaceAbove > dropdownHeight) {
          // open upward using fixed position
          this.openUp = true
          this.dropdownStyle = {
            position: 'fixed',
            top: `${triggerRect.top - dropdownHeight}px`,
            width: `${triggerRect.width}px`,
            zIndex: 9999,
            overflow: 'visible',
            background: ' var(--bg-color)',
            border: '1px solid gainsboro'
          }
        } else {
          // open downward with default style
          this.openUp = false
          this.dropdownStyle = {
            position: 'absolute',
            top: '100%',
            left: 0,
            width: '100%',
            zIndex: 999
          }
        }
      })
    }
  },
  watch: {
    visible () {
      this.$emit('toggle', this.visible)
    }
  }
}
</script>

<style lang="scss">
.custom-select {
  width: 100%;

  .selector {
    position: relative;

    .arrow {
      position: absolute;
      right: 10px;
      top: 35%;
      width: 0;
      height: 0;
      border-left: 7px solid transparent;
      border-right: 7px solid transparent;
      border-top: 10px solid #888;
      transform: rotateZ(0deg) translateY(0px);
      transition-duration: 0.3s;
      transition-timing-function: cubic-bezier(.59, 1.39, .37, 1.01);
    }

    .expanded {
      transform: rotateZ(180deg) translateY(2px);
    }

    .select-label {
      display: block;
      border: 1px solid rgba(59, 59, 59, 0.4666666667);
      border-radius: 0.285em;
      font-size: 1em;
      padding: 0.68em;
      background-color: transparent;
    }

    .dropdown {
      position: absolute;
      z-index: 999;
      width: 100%;
      background: var(--bg-color);
      border: 1px solid gainsboro;
      overflow: hidden;
      max-height: 16rem;

      &.dropdown-down {
        top: 100%;
      }

      &.dropdown-up {
        bottom: 0px;
        top: auto;
      }

      .dropdown-content {
        display: flex;
        flex-direction: column;
        max-height: 16rem;
        overflow: hidden;

        ul {
          overflow: auto;
          flex: 1;
          margin: 0;
          padding: 0;
          list-style: none;

          li {
            text-align: left;
            line-height: normal;
            padding: 8px;
            color: black;

            &:hover {
              cursor: pointer;
              color: white;
              background: var(--brand-color-1);
            }

            &.current {
              background: var(--brand-color);
            }
          }
        }

        .slot-container {
          border-top: 1px solid #e0e0e0;
          background: var(--bg-color);
        }
      }
    }

    .hidden {
      visibility: hidden;
    }

    .visible {
      visibility: visible;
    }
  }
}
.custom-select.compact {
  width: 6rem;
  height: fit-content;
  .selector {
    .select-label {
      font-size: 0.75em;         // Smaller text
      padding: 0.25em 0.4em;     // Less space inside the box
      line-height: 1.2;          // Tighter vertical height
    }
    .arrow {
      right: 5px;
      top: 40%;
      border-left: 3px solid transparent;
      border-right: 3px solid transparent;
      border-top: 5px solid #888; // Slightly smaller arrow
    }
  }
  .dropdown {
    max-height: 10rem;
    .dropdown-content {
      max-height: 10rem;
      ul li {
        padding: 4px 6px;
        font-size: 0.75em;       // Smaller option text
        line-height: 1.2;
      }
    }
  }
}

</style>
