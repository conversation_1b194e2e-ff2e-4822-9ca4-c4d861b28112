<template>
  <div :class="{
    'form-input--checkbox': true,
    'form-input--required': data.required,
    'v-center': true
    }" >
      <label>{{data?.caption}}:
        <img
      v-if="data.visibility"
      class="mx-1"
      width="15"
      src="~@/assets/images/eye.svg"
      alt="view"
      />
      </label>
      <div class="v-center" v-if="viewOnly">
        <span for="" class="ml-3 form-input--checkbox-label">Yes</span>
        <img  v-if="componentValue===true" src="~@/assets/images/icons/checked-icon.svg" class="ml-1"  alt=""  />
        <img  v-else src="~@/assets/images/icons/unchecked-icon.svg" class="ml-1" alt=""  />
       <span for="" class="ml-2 form-input--checkbox-label">No</span>
       <img  v-if="componentValue===false" src="~@/assets/images/icons/checked-icon.svg" class="ml-1" alt=""  />
        <img  v-else src="~@/assets/images/icons/unchecked-icon.svg" class="ml-1" alt=""  />
         </div>
      <div class="v-center" v-else>
        <span for="" class="ml-3 form-input--checkbox-label">Yes</span>
       <input class="ml-1 boolean"  :name="data.caption" :disabled="viewOnly" :checked="componentValue===true" type="radio" @change="addBoolenSelected(true)">
       <span for="" class="ml-2 form-input--checkbox-label">No</span>
      <input class="ml-1 boolean " :name="data.caption" :disabled="viewOnly" :checked="componentValue===false" @change="addBoolenSelected(false)" type="radio">
      </div>

  </div>
</template>

<script>
export default {
  name: 'textComponent',
  props: {
    mode: {
      type: String,
      default: 'TEMPLATE'
    },
    data: {
      type: Object,
      default: () => {}
    },
    value: {
      type: Boolean,
      default: null
    },
    viewOnly: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      componentValue: ''
    }
  },
  methods: {
    addBoolenSelected (boolean) {
      this.componentValue = boolean
      this.$emit('customChange', {
        field_id: this.$props.data.field_id,
        value: this.componentValue
      })
    }
  },
  watch: {
    value (val) {
      this.componentValue = val
    }
  },
  created () {
    this.componentValue = this.value
  }
}
</script>

<style lang="scss" scoped >
.form-input--checkbox {
  font-size: 12px;
  margin-bottom: 1em;
  & label {
    font-size: 1.3em;
    // margin-bottom: 0.3em;    // to make the alignment in correct line
    margin-right: 0.4em;
  }
  &-label{
    font-size: 13px;
  }
}

.boolean{
  border: 2px solid #ccc;
  border-radius: 50%;
  width: 15px;
  height: 15px;
  background-color: green;
}
</style>
