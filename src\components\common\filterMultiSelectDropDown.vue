<template>
    <section>
    <div>
      <!-- loading circle -->
    </div>
    <div @mouseleave="emitSelected">
      <div>
        <div v-for="(option, index) in filteredOptions" :key="option.value">
          <div class=" flex v-center gap-1" >
          <img
         @click="toggleExpand(option, index)"
          v-if="!option.isExpanded"
          src="~@/assets/images/chevron-right.svg"
          alt=""
        />
        <img v-else @click="toggleExpand(option, index)"
        src="~@/assets/images/chevron-down.svg"
        alt=""/>
        <label class=" flex v-center gap-1" >
          <input
            type="checkbox"
            class="option"
            :value="option.value"
            :checked="selectedOptions.includes(option.value)"
  @change="onCheckboxChange(option, $event.target.checked)"
          />
         <div class="option-label elipsis-text" v-overflow-tooltip> {{ option.label }}</div>
        </label> </div>
         <div v-show="option.isExpanded" v-if="option.children && option.children.length" class="child-options">
      <!-- Pass option.children back into the same rendering block -->
      <filter-multi-select-drop-down
      :options="option.children"
      :search="search"
      v-model="selectedOptions"
      @fetch-child="fetchChildTags"
      :initially-selected="initiallySelected"
      @selected-partial="$emit('selected-partial', $event)"
       @selected="$emit('selected', $event)"
      />
    </div>
  </div>
      </div>
      <div v-if="selectedOptions.length" class="selectedCount">
        {{ selectedOptions.length }}
      </div>
    </div>
  </section>
  </template>

<script>
import { displayingTaskChildTag } from '@/api'
export default {
  name: 'filterMultiSelectDropDown',
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    initiallySelected: Array,
    options: Array,
    search: String,
    label: {
      type: String,
      default: 'Select Options'
    }
  },
  data () {
    return {
      selectedOptions: [],
      showDropdown: false,
      localOptions: [],
      showChildTags: false
    }
  },
  mounted () {
    this.localOptions = JSON.parse(JSON.stringify(this.options || []))
    this.selectedOptions = (this.initiallySelected || [])
    this.resolveInitialSelections()
  },
  computed: {
    filteredOptions () {
      if (!this.search) return this.localOptions
      const term = this.search.toLowerCase()
      const filterRecursive = (options) => {
        return options
          .map(option => {
            const match = option.label.toLowerCase().includes(term)
            const children = option.children ? filterRecursive(option.children) : []

            if (match || children.length) {
              return { ...option, children }
            }
            return null
          })
          .filter(Boolean)
      }
      return filterRecursive(this.localOptions)
    }
  },
  methods: {
    onCheckboxChange (option, checked) {
      const value = option.value

      if (checked) {
        // Add current option
        if (!this.selectedOptions.includes(value)) {
          this.selectedOptions.push(value)
        }
      } else {
        // Remove current option
        this.selectedOptions = this.selectedOptions.filter(id => id !== value)
      }

      // Emit updated selection
      this.emitSelected()
    },
    async toggleExpand (option, index) {
      if (!option.children || option.children.length === 0) {
        await this.fetchChildTags(option)
      }
      this.localOptions[index].isExpanded = !this.localOptions[index].isExpanded
    },
    async fetchChildTags (parentTag) {
      const target = this.findTagById(parentTag.value, this.localOptions)
      if (!target || (target.children && target.children.length)) return

      const res = await displayingTaskChildTag(parentTag.value)
      const children = res.tag.map(tag => ({
        value: tag.id,
        label: tag.name,
        parent_id: tag.parent_id,
        children: [],
        isExpanded: false
      }))
      this.$set(target, 'children', children)
    },
    findTagById (id, options = this.localOptions) {
      for (const opt of options) {
        if (opt.value === id) return opt
        if (opt.children?.length) {
          const found = this.findTagById(id, opt.children)
          if (found) return found
        }
      }
      return null
    },
    async resolveInitialSelections () {
      for (const id of this.initiallySelected || []) {
        await this.ensureVisible(id, this.localOptions)
      }
    },
    async ensureVisible (id, options) {
      for (const option of options) {
        if (option.value === id) return true

        if (option.children?.length) {
          const found = await this.ensureVisible(id, option.children)
          if (found) {
            option.isExpanded = true
            return true
          }
        }

        if (!option.children || option.children.length === 0) {
          await this.fetchChildTags(option)
          if (option.children?.length) {
            const found = await this.ensureVisible(id, option.children)
            if (found) {
              option.isExpanded = true
              return true
            }
          }
        }
      }
      return false
    },
    emitSelected () {
      const allIds = this.getSelectedIds(this.localOptions)
      const isRoot = !this.$parent || this.$parent.$options.name !== 'filterMultiSelectDropDown'
      this.$emit(isRoot ? 'selected' : 'selected-partial', allIds)
    },
    getSelectedIds (options) {
      let ids = []
      for (const option of options) {
        if (this.selectedOptions.includes(option.value)) {
          ids.push(option.value)
        }
        if (option.children?.length) {
          ids = ids.concat(this.getSelectedIds(option.children))
        }
      }
      return ids
    },
    filterOptions () {
      // Filtering is handled by computed property now
    }
  },
  watch: {
    search () {
      this.filterOptions()
    },
    value: {
      handler (val) {
        this.selectedOptions = val
      },
      deep: true
    },
    selectedOptions (val) {
      this.$emit('input', val)
    }
  }
}
</script>

    <style lang="scss" scoped>
  /* Add your CSS styles here */
  .dropdown {
    position: relative;
    display: inline-block;
  }

  .dropdown-toggle {
    background-color: var(--brand-light-color);
    border: 1px solid var(--brand-color);
    padding: 8px 12px;
    cursor: pointer;
    width: 200px;
    border-radius: 0.3em;
  }

  .dropdown-content {
    position: absolute;
    background-color: #fff;
    border: 1px solid #ccc;
    box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
    padding: 8px;
    z-index: 2;
    width: 200px;
    max-height: 365px;
    overflow: scroll;
    & input{
      padding: 5px;
      border-radius: 4px;
      border-color: rgb(30, 27, 27,.3);
      border-style: solid;
    }
    & input:focus{
      border-color: rgb(30, 27, 27,.7);
      border-style: solid;
    }
  }

  .dropdown-content label {
    margin-bottom: 5px;
  }

  .selectedCount {
    border-radius: 50%;
    position: absolute;
    top: -4px;
    padding: 3px 10px;
    background: var(--brand-color);
    right: -9px;
  }

  #searchInput {
    width: 100%;
    padding: 8px;
    margin-bottom: 8px;
  }
  .option-label{
  flex-grow: 1;
  }
  .child-options {
  padding-left: 20px;
  border-left: 1px dashed #ccc;
  margin-top: 5px;
}
  </style>
