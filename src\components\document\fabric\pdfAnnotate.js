
import {
  // deleteAllAnnotation,
  generateS3SubmittingUrl
} from '@/api'
import { fabric, Arrow } from './arrow'
import { jsPDF as JsPDF } from 'jspdf'
import confirmationDialog from '@/plugins/confirmationDialog'
import addImage from '../../../assets/images/icons/annotation/addImage.svg'
import store from '../../../store'
import { alert } from '@/plugins/notification'
const pdfjsLib = require('pdfjs-dist/es5/build/pdf.js')
const user = store.getters.user
const isOnProjectLevel = store.getters.isOnProjectLevel
let keyDownHandler = null

if (typeof window !== 'undefined' && 'Worker' in window) {
  // eslint-disable-next-line import/no-webpack-loader-syntax
  const PdfjsWorker = require('worker-loader!pdfjs-dist/es5/build/pdf.worker.js')
  pdfjsLib.GlobalWorkerOptions.workerPort = new PdfjsWorker()
}
export const PDFAnnotate = function (containerId, url, options = {}, savedDataObj, loader, viewOnly) {
  this.number_of_pages = 0
  this.pages_rendered = 0
  this.selectedObjects = null
  this.active_tool = 0 // 1 - Free hand, 2 - Text, 3 - Arrow, 4 - Rectangle
  this.fabricObjects = []
  this.fabricObjectsData = []
  this.color = 'black'
  this.borderColor = 'red'
  this.borderSize = 2
  this.font_size = 16
  this.active_canvas = 0
  this.containerId = containerId
  this.url = url
  this.pageImageCompression = options.pageImageCompression
    ? options.pageImageCompression.toUpperCase()
    : 'NONE'
  this.textBoxText = 'Sample Text'
  this.format = null
  this.orientation = null
  this.eventListnerArray = []
  this.origX = null
  this.origY = null
  this.docId = null
  this.savedDataObj = savedDataObj
  this.arrowInstance = []
  this.iconObject = {}
  this.isDragging = false
  this.isZooming = false
  this.lastPosX = null
  this.lastPosY = null
  this.loader = loader
  this.eventHandlers = []
  this.deletedIds = []
  this.viewOnly = viewOnly
  this.loadedImage = {}
  const inst = this

  const loadingTask = pdfjsLib.getDocument(this.url)
  loadingTask.promise.then(
    function (pdf) {
      const scale = options.scale ? options.scale : 1.3
      inst.number_of_pages = pdf.numPages

      for (let i = 1; i <= pdf.numPages; i++) {
        pdf.getPage(i).then(function (page) {
          if (typeof inst.format === 'undefined' ||
              typeof inst.orientation === 'undefined') {
            const originalViewport = page.getViewport({ scale: 1 })
            inst.format = [originalViewport.width, originalViewport.height]
            inst.orientation =
                originalViewport.width > originalViewport.height
                  ? 'landscape'
                  : 'portrait'
          }

          const viewport = page.getViewport({ scale: scale })
          const canvas = document.createElement('canvas')
          document.getElementById(inst.containerId).appendChild(canvas)
          canvas.className = 'pdf-canvas'
          canvas.height = viewport.height
          canvas.width = viewport.width
          const context = canvas.getContext('2d')

          const renderContext = {
            canvasContext: context,
            viewport: viewport
          }
          const renderTask = page.render(renderContext)
          renderTask.promise.then(function () {
            document.querySelectorAll('.pdf-canvas').forEach((el, index) => {
              el.id = `page-${index + 1}-canvas`
            })
            inst.pages_rendered++
            if (inst.pages_rendered === inst.number_of_pages) inst.initFabric()
          })
        })
      }
    },
    function (reason) {
      console.error(reason)
      window.loader.hide()
    }
  )

  this.initFabric = function () {
    const inst = this
    const canvases = document.querySelectorAll(`#${this.containerId} canvas`)
    canvases.forEach(function (el, index) {
      const background = el.toDataURL('image/png')
      const fabricObj = new fabric.Canvas(el.id, {
        // defaultCursor: `url(" ${'cell'} "), auto`,
        //
        // canvas.defaultCursor = `url(" ${cursorUrl} "), auto`;
        // canvas.hoverCursor = `url(" ${cursorUrl} "), auto`;
        // canvas.moveCursor = `url(" ${cursorUrl} "), auto`;
        freeDrawingBrush: {
          width: 1,
          color: inst.color
        }
      })
      inst.fabricObjects.push(fabricObj)
      if (typeof options.onPageUpdated === 'function') {
        fabricObj.on('object:added', function () {
          const oldValue = Object.assign({}, inst.fabricObjectsData[index])
          inst.fabricObjectsData[index] = fabricObj.toJSON()
          options.onPageUpdated(
            index + 1,
            oldValue,
            inst.fabricObjectsData[index]
          )
        })
      }
      fabricObj.setBackgroundImage(
        background,
        fabricObj.renderAll.bind(fabricObj)
      )
      let isDrawing = false
      let rect
      if (inst.viewOnly) {
        if (index === canvases.length - 1 && typeof options.ready === 'function') {
          inst.loadFromJSON()
          options.ready()
        }
        return
      }
      fabricObj.on('mouse:down:before', function (event) {
        inst.active_canvas = index
        if (inst.active_tool !== 0) {
          fabricObj.discardActiveObject()
          fabricObj.getObjects().forEach(obj => {
            obj.set({
              selectable: false,
              evented: false
            })
          })
        }
      })
      fabricObj.on('mouse:wheel', function (opt) {
        if (!inst.isZooming) {
          return
        }
        // Get the delta (how much the wheel moved)
        var delta = opt.e.deltaY

        // Calculate the new zoom level
        var zoom = fabricObj.getZoom()
        zoom *= 0.999 ** delta

        // Clamp zoom between min and max values
        if (zoom > 20) zoom = 20
        if (zoom < 0.01) zoom = 0.01

        // Determine if zooming in or out
        var zoomPoint = { x: opt.e.offsetX, y: opt.e.offsetY }

        // If zooming out, zoom based on the canvas center
        if (delta > 0) {
          zoomPoint = {
            x: fabricObj.width / 2,
            y: fabricObj.height / 2
          }
        }

        // Zoom to the point
        fabricObj.zoomToPoint(zoomPoint, zoom)

        // Prevent the default action and stop the event from propagating further
        opt.e.preventDefault()
        opt.e.stopPropagation()

        // Get the current viewport transform (for panning)
        var vpt = this.viewportTransform

        // Adjust viewport transform to ensure the canvas doesn't go out of bounds
        if (zoom < 1) {
          vpt[4] = Math.min(0, Math.max(fabricObj.getWidth() * (1 - zoom), vpt[4]))
          vpt[5] = Math.min(0, Math.max(fabricObj.getHeight() * (1 - zoom), vpt[5]))
        } else {
          if (vpt[4] >= 0) {
            vpt[4] = 0
          } else if (vpt[4] < fabricObj.getWidth() - fabricObj.getWidth() * zoom) {
            vpt[4] = fabricObj.getWidth() - fabricObj.getWidth() * zoom
          }
          if (vpt[5] >= 0) {
            vpt[5] = 0
          } else if (vpt[5] < fabricObj.getHeight() - fabricObj.getHeight() * zoom) {
            vpt[5] = fabricObj.getHeight() - fabricObj.getHeight() * zoom
          }
        }

        // Render the canvas with the updated transform
        fabricObj.requestRenderAll()
      })

      fabricObj.on('mouse:down', function (event) {
        // tracking mouseMovement only for pan -- start

        const evt = event.e
        if (evt.ctrlKey === true) {
          this.isDragging = true
          this.selection = false
          this.lastPosX = evt.clientX
          this.lastPosY = evt.clientY
        }

        // tracking mouseMovement only for pan --  end

        if (inst.iconObject?.url) {
          const pointer = fabricObj.getPointer(event.e)
          inst.addImageToGivenPosition(pointer.x, pointer.y)
        }
        if (inst.loadedImage?.fileName) {
          const pointer = fabricObj.getPointer(event.e)
          inst.loadImage(pointer.x, pointer.y)
        }
        if (inst.active_tool === 2) { return }
        if (inst.active_tool === 4) {
          // fabricObj.deactivateAll().renderAll();
          isDrawing = true
          const pointer = fabricObj.getPointer(event.e)
          this.origX = pointer.x
          this.origY = pointer.y

          rect = new fabric.Rect({
            left: this.origX,
            top: this.origY,
            width: 0,
            height: 0,
            fill: 'transparent',
            stroke: inst.color,
            strokeWidth: inst.borderSize
          })
          fabricObj.add(rect)
        }
      })

      fabricObj.on('mouse:move', function (event) {
        inst.active_canvas = index
        // pan movement track starts
        if (this.isDragging) {
          var e = event.e
          var vpt = this.viewportTransform
          vpt[4] += e.clientX - this.lastPosX
          vpt[5] += e.clientY - this.lastPosY
          this.requestRenderAll()
          this.lastPosX = e.clientX
          this.lastPosY = e.clientY
        }
        // pan movement track ends

        if (inst.active_tool === 2) {
          event.e.preventDefault()
          event.e.stopPropagation()
          return
        }
        if (!isDrawing) return
        const pointer = fabricObj.getPointer(event.e)

        if (this.origX > pointer.x) {
          rect.set({ left: Math.abs(pointer.x) })
        }
        if (this.origY > pointer.y) {
          rect.set({ top: Math.abs(pointer.y) })
        }
        rect.set({ width: Math.abs(this.origX - pointer.x) })
        rect.set({ height: Math.abs(this.origY - pointer.y) })

        fabricObj.renderAll()
      })

      fabricObj.on('mouse:up', function () {
        isDrawing = false
        this.isDragging = false
        if (inst.active_tool === 3) {
          inst.disableArrowInstances()
        }
        fabricObj.getObjects().forEach(obj => {
          obj.set({
            selectable: true,
            evented: true
          })
        })
        fabricObj.renderAll()

        if (inst.active_tool === 4 || inst.active_tool === 3) {
          inst.active_tool = 0
        }
      })
      fabricObj.on('selection:updated', function (obj) {
        inst.selectedObjects = []
        if (obj.selected.length > 1) return
        inst.selectedObjects = obj.selected.map((item) => {
          return {
            type: item.type,
            id: item.id,
            updatedBy: item.updatedBy,
            createdBy: item.createdBy,
            createdOn: item.createdOn,
            docId: item.docId,
            status: item.status
          }
        }
        )
      })
      fabricObj.on('selection:created', function (obj) {
        inst.selectedObjects = []
        if (obj.selected.length > 1) return
        inst.selectedObjects = obj.selected.map((item) => {
          return {
            type: item.type,
            id: item.id,
            updatedBy: item.updatedBy,
            createdBy: item.createdBy,
            createdOn: item.createdOn,
            docId: item.docId,
            status: item.status
          }
        }
        )
      })
      // fabricObj.on('before:selection:cleared', function () {
      //   inst.selectedObjects = []
      // })
      fabricObj.on('selection:cleared', function (obj) {
        inst.selectedObjects = []
      })
      document.addEventListener('keydown', keyDownHandler, true)
      keyDownHandler = function (event) {
        if (event.ctrlKey === true) {
          inst.isZooming = true
        } else if (event.code === 'Escape' && inst.selectedObjects?.length > 0) {
          event.stopPropagation()
          fabricObj.discardActiveObject()
          inst.selectedObjects = []
        }
      }
      document.onkeyup = (event) => {
        if (event.key === 'Control') {
          inst.isZooming = false
        }
      }

      fabricObj.upperCanvasEl.addEventListener('click', handleCanvasClick)
      function handleCanvasClick (event) {
        inst.fabricClickHandler(event, fabricObj)
      }
      inst.eventHandlers.push({ fabricObj, handleCanvasClick })
      fabricObj.on('after:render', function () {
        inst.fabricObjectsData[index] = fabricObj.toJSON()
        fabricObj.off('after:render')
      })

      if (index === canvases.length - 1 && typeof options.ready === 'function') {
        inst.loadFromJSON()
        options.ready()
      }
    })
  }
  this.fabricClickHandler = function (event, fabricObj) {
    const inst = this
    let toolObj
    if (inst.active_tool === 2) {
      toolObj = new fabric.Textbox(inst.textBoxText, {
        left: event.clientX - fabricObj.upperCanvasEl.getBoundingClientRect().left,
        top: event.clientY - fabricObj.upperCanvasEl.getBoundingClientRect().top,
        fill: inst.color,
        fontSize: inst.font_size,
        width: 200,
        height: 100,
        selectable: true,
        hasBorders: true,
        editingBorderColor: 'blue',
        padding: 5
      })
    }

    if (toolObj) {
      fabricObj.add(toolObj)
      fabricObj.setActiveObject(toolObj) // Select the new text object
      toolObj.enterEditing()
      toolObj.hiddenTextarea.value = ''
      toolObj.hiddenTextarea.focus()
      toolObj.selectionStart = 0
      toolObj.selectionEnd = 11
      this.active_tool = 0
    }
  }
}
PDFAnnotate.prototype.enableSelector = function () {
  const inst = this
  inst.disableArrowInstances()
  inst.active_tool = 0
  if (inst.fabricObjects.length > 0) {
    inst.fabricObjects.forEach((fabricObj) => {
      fabricObj.isDrawingMode = false
    })
  }
}
PDFAnnotate.prototype.enablePencil = function () {
  const inst = this
  inst.disableArrowInstances()
  inst.active_tool = 1
  if (inst.fabricObjects.length > 0) {
    inst.fabricObjects.forEach((fabricObj) => {
      fabricObj.isDrawingMode = true
      fabricObj.setCursor('cell')
    })
  }
}
PDFAnnotate.prototype.enableAddText = function (text) {
  const inst = this
  inst.active_tool = 2
  inst.disableArrowInstances()
  if (typeof text === 'string') {
    inst.textBoxText = text
  }
  if (inst.fabricObjects.length > 0) {
    inst.fabricObjects.forEach((fabricObj) => {
      fabricObj.isDrawingMode = false
    })
  }
}
PDFAnnotate.prototype.enableRectangle = function () {
  const inst = this
  inst.disableArrowInstances()
  inst.active_tool = 4
  inst.fabricObjects.forEach((fabricObj) => {
    fabricObj.isDrawingMode = false
  })
}
PDFAnnotate.prototype.enableAddArrow = function (setSelectorTool = null) {
  const inst = this
  inst.active_tool = 3
  if (inst.fabricObjects.length > 0) {
    inst.fabricObjects.forEach((fabricObj) => {
      fabricObj.isDrawingMode = false
      // eslint-disable-next-line no-new
      const arrowInstance = new Arrow(fabricObj, this.color, inst.active_tool, () => {})
      this.arrowInstance.push(arrowInstance)
    })
  }
}
PDFAnnotate.prototype.addImageToCanvas = function (imageObjectUrl = null, id, path, initialWidth = 100, initialHeight = 100) {
  const inst = this
  inst.disableArrowInstances()
  const cursorUrl = addImage
  inst.fabricObjects.forEach((fabricObj) => {
    fabricObj.defaultCursor = `url("${cursorUrl}"), auto`
    fabricObj.renderAll()
  })
  inst.enableSelector()
  const fabricObj = inst.fabricObjects[inst.active_canvas]

  if (!fabricObj) return
  if (imageObjectUrl) {
    this.iconObject = { url: imageObjectUrl, id }
  } else {
    const inputElement = document.createElement('input')
    inputElement.type = 'file'
    inputElement.accept = '.jpg,.jpeg,.png,.PNG,.JPG,.JPEG'
    inputElement.id = 'inputElement'
    inputElement.onchange = async (e) => {
      // this.loader.show()
      inst.loadedImage.file = e.target.files[0]
      inst.loadedImage.fileName = inst.loadedImage.file?.name?.split('.')?.shift()
      inst.loadedImage.fileExtension = inst.loadedImage.file?.name?.split('.')?.pop()
    }
    inputElement.onblur = async (e) => {
      this.loader.hide()
    }
    document.body.appendChild(inputElement)
    inputElement.click()
  }
}
PDFAnnotate.prototype.addImageToFabric = function (image, blobkey = null, left, top) {
  const inst = this
  const fabricObj = inst.fabricObjects[inst.active_canvas]
  if (!fabricObj) return
  // Get the current viewport transformation matrix
  // Create the fabric image and set its properties
  const fabricImage = new fabric.Image(image, {
    left: left - 200 / 2,
    top: top - 200 / 2,
    scaleX: 200 / image.width,
    scaleY: 200 / image.height,
    assetId: null,
    blobKey: blobkey
  })
  fabricObj.add(fabricImage)
  fabricObj.renderAll()
  this.loader.hide()
}
PDFAnnotate.prototype.loadImage = function (left, top) {
  const inst = this
  if (!inst.loadedImage) return
  this.loader.show()
  const reader = new FileReader()
  reader.onload = async (e) => {
    document.getElementById('inputElement').remove()
    const fileBase64 = e.target.result

    const image = new Image()
    image.onload = async function () {
      try {
        const blobkey = await inst.uploadImageToS3(inst.loadedImage.fileName, inst.loadedImage.fileExtension, inst.loadedImage.file)
        this.loadedImage = image
        this.loadedBlob = blobkey
        inst.addImageToFabric(image, blobkey, left, top)
        inst.loadedImage = {}
        inst.fabricObjects.forEach((fabricObj) => {
          fabricObj.defaultCursor = null
          fabricObj.renderAll()
        })
      } catch (error) {
        this.loader.hide()
        console.error('Error uploading image to S3:', error)
      }
    }
    image.src = fileBase64
  }
  reader.readAsDataURL(inst.loadedImage.file)
}
PDFAnnotate.prototype.deleteSelectedObject = function () {
  const inst = this
  const activeCanvas = inst.fabricObjects[inst.active_canvas]
  const selectdObjects = activeCanvas.getActiveObjects()
  if (selectdObjects.length) {
    confirmationDialog('Are you sure to delete?', async function (confirm) {
      if (!confirm) return
      const ids = selectdObjects.filter(obj => obj.id && (obj.createdBy.id === user.userId || (user.tenantLevelRole === 'ADMIN') || (isOnProjectLevel && user.projectLevelRole === 'ADMIN'))).map(obj => obj.id)
      inst.deletedIds = [...inst.deletedIds, ...ids]
      // ids.length > 0 && await deleteAnnoatationElements(ids)
      selectdObjects.forEach((obj) => {
        //! obj.createdBy?.id , if created id is not there which means its newly created by the active user
        if (!obj.createdBy?.id || obj.createdBy?.id === user.userId || (user.tenantLevelRole === 'ADMIN') || (isOnProjectLevel && user.projectLevelRole === 'ADMIN')) { activeCanvas.remove(obj) }
      })
    })
  }
}
PDFAnnotate.prototype.savePdf = function (fileName) {
  const inst = this
  const format = inst.format || 'a4'
  const orientation = inst.orientation || 'portrait'
  if (!inst.fabricObjects.length) return
  const doc = new JsPDF({ format, orientation })
  if (typeof fileName === 'undefined') {
    fileName = `${new Date().getTime()}.pdf`
  }

  inst.fabricObjects.forEach(function (fabricObj, index) {
    if (index !== 0) {
      doc.addPage(format, orientation)
      doc.setPage(index + 1)
    }
    doc.addImage(
      fabricObj.toDataURL({
        format: 'png'
      }),
      inst.pageImageCompression === 'NONE' ? 'PNG' : 'JPEG',
      0,
      0,
      doc.internal.pageSize.getWidth(),
      doc.internal.pageSize.getHeight(),
        `page-${index + 1}`,
        ['FAST', 'MEDIUM', 'SLOW'].indexOf(inst.pageImageCompression) >= 0
          ? inst.pageImageCompression
          : undefined
    )
    if (index === inst.fabricObjects.length - 1) {
      doc.save(fileName)
    }
  })
}
PDFAnnotate.prototype.setBrushSize = function (size) {
  const inst = this
  inst.fabricObjects.forEach((fabricObj) => {
    fabricObj.freeDrawingBrush.width = parseInt(size, 10) || 1
  })
  const selectdObject = inst.fabricObjects[inst.active_canvas].getActiveObject()
  switch (selectdObject.type) {
  case 'path' : {
    selectdObject.set({ strokeWidth: size })
    inst.fabricObjects[inst.active_canvas].renderAll()
    break
  }
  case 'rect' : {
    selectdObject.set({ strokeWidth: size })
    inst.fabricObjects[inst.active_canvas].renderAll()
    break
  }
  case 'lineArrow' : {
    selectdObject.set({ strokeWidth: size })
    inst.fabricObjects[inst.active_canvas].renderAll()
    break
  }
  // case 'textbox' : {
  //   selectdObject.set({ fill: color })
  //   inst.fabricObjects[inst.active_canvas].renderAll()
  //   break
  // }
  default :
    break
  }
}
PDFAnnotate.prototype.setColor = function (color) {
  const inst = this
  inst.color = color
  if (inst.active_tool === 1) {
    this.fabricObjects.forEach(fabricObj => {
      fabricObj.freeDrawingBrush.color = color
    })
  }

  const selectdObject = inst.fabricObjects[inst.active_canvas].getActiveObject()
  switch (selectdObject.type) {
  case 'path' : {
    selectdObject.set({ stroke: color })
    inst.fabricObjects[inst.active_canvas].renderAll()
    break
  }
  case 'rect' : {
    selectdObject.set({ stroke: color })
    inst.fabricObjects[inst.active_canvas].renderAll()
    break
  }
  case 'lineArrow' : {
    selectdObject.set({ stroke: color })
    inst.fabricObjects[inst.active_canvas].renderAll()
    break
  }
  case 'textbox' : {
    selectdObject.set({ fill: color })
    inst.fabricObjects[inst.active_canvas].renderAll()
    break
  }
  default :
    break
  }
  inst.fabricObjects.forEach((fabricObj) => {
    fabricObj.freeDrawingBrush.color = color
  })
}
PDFAnnotate.prototype.setBorderColor = function (color) {
  const inst = this
  inst.borderColor = color
}
PDFAnnotate.prototype.setFontSize = function (size) {
  const inst = this
  this.font_size = size
  const selectdObject = inst.fabricObjects[inst.active_canvas].getActiveObject()

  if (selectdObject.type === 'textbox') {
    selectdObject.set({ fontSize: size })
    inst.fabricObjects[inst.active_canvas].renderAll()
  }
}
PDFAnnotate.prototype.setBorderSize = function (size) {
  this.borderSize = size
}
PDFAnnotate.prototype.clearActivePage = function () {
  const inst = this
  const fabricObj = inst.fabricObjects[inst.active_canvas]
  const bg = fabricObj.backgroundImage
  confirmationDialog(' Are you sure to clear the page', function (confirm) {
    if (!confirm) return
    const ids = fabricObj._objects.filter(obj => obj.id && (obj.createdBy.id === user.userId || (user.tenantLevelRole === 'ADMIN') || (isOnProjectLevel && user.projectLevelRole === 'ADMIN'))).map(obj => obj.id)
    inst.deletedIds = [...inst.deletedIds, ...ids]
    // ids.length > 0 && await deleteAnnoatationElements(ids)
    if (!((user.tenantLevelRole === 'ADMIN') || (isOnProjectLevel && user.projectLevelRole === 'ADMIN'))) {
      fabricObj._objects.forEach((obj) => {
        if (obj.createdBy.id === user.userId || (user.tenantLevelRole === 'ADMIN') || (isOnProjectLevel && user.projectLevelRole === 'ADMIN')) { fabricObj.remove(obj) }
      })
    } else {
      fabricObj.clear()
    }
    fabricObj.setBackgroundImage(bg, fabricObj.renderAll.bind(fabricObj))
  })
}
PDFAnnotate.prototype.serializePdf = function (callback) {
  const inst = this
  const pageAnnotations = []
  inst.fabricObjects.forEach(function (fabricObject) {
    fabricObject.clone(function (fabricObjectCopy) {
      fabricObjectCopy.setBackgroundImage(null)
      fabricObjectCopy.setBackgroundColor('')
      pageAnnotations.push(fabricObjectCopy)
      if (pageAnnotations.length === inst.fabricObjects.length) {
        const data = {
          pageSetup: {
            format: inst.format,
            orientation: inst.orientation
          },
          pages: pageAnnotations
        }
        callback(JSON.stringify(data))
      }
    })
  })
}
PDFAnnotate.prototype.setDefaultTextForTextBox = function (text) {
  const inst = this
  if (typeof text === 'string') {
    inst.textBoxText = text
  }
}
PDFAnnotate.prototype.saveInBE = function (annotationCall, fileId, status) {
  const inst = this
  const pageAnnotations = []
  const updateObj = []
  const newObj = []
  // Serialize each fabric canvas to JSON
  inst.fabricObjects.forEach(function (fabricObject, index) {
    fabricObject.clone(function (fabricObjectCopy) {
      pageAnnotations.push(fabricObjectCopy.toJSON())
      fabricObject.getObjects().forEach(obj => {
        let modifiedObj = null
        switch (obj.type) {
        case 'lineArrow': {
          modifiedObj = {
            document_id: fileId,
            page_no: index,
            angle: obj.angle,
            bg_color: obj.backgroundColor,
            crop_x: obj.cropX,
            crop_y: obj.cropY,
            // cross_origin: obj.crossOrigin,
            char_spacing: obj.charSpacing,
            fill_rule: obj.fillRule,
            flip_x: obj.flipX,
            flip_y: obj.flipY,
            // globalCompositeOperation: obj.globalCompositeOperation,
            height: obj.height,
            left: obj.left,
            opacity: obj.opacity,
            origin_x: obj.originX,
            origin_y: obj.originY,
            paint_first: obj.paintFirst,
            scale_x: obj.scaleX,
            scale_y: obj.scaleY,
            // shadow: obj.shadow,
            skew_x: obj.skewX,
            skew_y: obj.skewY,
            stroke: obj.stroke,
            stroke_dash_offset: obj.strokeDashOffset,
            stroke_line_cap: obj.strokeLineCap,
            stroke_line_join: obj.strokeLineJoin,
            stroke_miter_limit: obj.strokeMiterLimit,
            stroke_uniform: obj.strokeUniform,
            stroke_width: obj.strokeWidth,
            top: obj.top,
            type: 3,
            // version: obj.version,
            // visible: obj.visible,
            width: obj.width,
            x1: obj.x1,
            x2: obj.x2,
            y1: obj.y1,
            y2: obj.y2,
            status: obj.status ?? status.todo
          }
          break }
        case 'rect': {
          modifiedObj = {
            document_id: fileId,
            page_no: index,
            angle: obj.angle,
            bg_color: obj.backgroundColor,
            fill: obj.fill,
            fill_rule: obj.fillRule,
            flip_x: obj.flipX,
            flip_y: obj.flipY,
            // globalCompositeOperation: obj.globalCompositeOperation,
            height: obj.height,
            left: obj.left,
            opacity: obj.opacity,
            origin_x: obj.originX,
            origin_y: obj.originY,
            paint_first: obj.paintFirst,
            rx: obj.rx,
            ry: obj.ry,
            scale_x: obj.scaleX,
            scale_y: obj.scaleY,
            // shadow: obj.shadow,
            skew_x: obj.skewX,
            skew_y: obj.skewY,
            stroke: obj.stroke,
            stroke_dash_offset: obj.strokeDashOffset,
            stroke_line_cap: obj.strokeLineCap,
            stroke_line_join: obj.strokeLineJoin,
            stroke_miter_limit: obj.strokeMiterLimit,
            stroke_uniform: obj.strokeUniform,
            stroke_width: obj.strokeWidth,
            top: obj.top,
            type: 2,
            // version: obj.version,
            // visible: obj.visible,
            width: obj.width,
            status: obj.status ?? status.todo
          }
          break }
        case 'textbox': {
          modifiedObj = {
            document_id: fileId,
            page_no: index,
            angle: obj.angle,
            bg_color: obj.backgroundColor,
            char_spacing: obj.charSpacing,
            fill: obj.fill,
            fill_rule: obj.fillRule,
            flip_x: obj.flipX,
            flip_y: obj.flipY,
            // fontFamily
            font_size: obj.fontSize,
            // fontStyle
            // fontWeight
            // globalCompositeOperation: obj.globalCompositeOperation,
            height: obj.height,
            left: obj.left,
            line_height: obj.lineHeight,
            // linethrough
            min_width: obj.minWidth,
            opacity: obj.opacity,
            origin_x: obj.originX,
            origin_y: obj.originY,
            // overline
            paint_first: obj.paintFirst,
            path_align: obj.pathAlign,
            path_side: obj.pathSide,
            // pathStartOffset
            scale_x: obj.scaleX,
            scale_y: obj.scaleY,
            // shadow: obj.shadow,
            skew_x: obj.skewX,
            skew_y: obj.skewY,
            // splitByGrapheme
            stroke: obj.stroke,
            // strokeDashArray
            stroke_dash_offset: obj.strokeDashOffset,
            stroke_line_cap: obj.strokeLineCap,
            stroke_line_join: obj.strokeLineJoin,
            stroke_miter_limit: obj.strokeMiterLimit,
            stroke_uniform: obj.strokeUniform,
            stroke_width: obj.strokeWidth,
            // styles
            text: obj.text,
            text_align: obj.textAlign,
            // textBackgroundColor
            top: obj.top,
            type: 1,
            // version: obj.version,
            // visible: obj.visible,
            width: obj.width,
            status: obj.status ?? status.todo
          }
          break }
        case 'path': {
          modifiedObj = {
            document_id: fileId,
            page_no: index,
            angle: obj.angle,
            bg_color: obj.backgroundColor,
            fill: obj.fill,
            fill_rule: obj.fillRule,
            flip_x: obj.flipX,
            flip_y: obj.flipY,
            // globalCompositeOperation: obj.globalCompositeOperation,
            height: obj.height,
            left: obj.left,
            opacity: obj.opacity,
            origin_x: obj.originX,
            origin_y: obj.originY,
            paint_first: obj.paintFirst,
            metadata: JSON.stringify(obj.path),
            scale_x: obj.scaleX,
            scale_y: obj.scaleY,
            // shadow: obj.shadow,
            skew_x: obj.skewX,
            skew_y: obj.skewY,
            stroke: obj.stroke,
            // strokeDashArray
            stroke_dash_offset: obj.strokeDashOffset,
            stroke_line_cap: obj.strokeLineCap,
            stroke_line_join: obj.strokeLineJoin,
            stroke_miter_limit: obj.strokeMiterLimit,
            stroke_uniform: obj.strokeUniform,
            stroke_width: obj.strokeWidth,
            top: obj.top,
            type: 4,
            // version: obj.version,
            // visible: obj.visible,
            width: obj.width,
            status: obj.status ?? status.todo
          }
          break }
        case 'image': {
          modifiedObj = {
            document_id: fileId,
            blob_key: obj.blobKey ?? undefined,
            page_no: index,
            angle: obj.angle,
            bg_color: obj.backgroundColor,
            crop_x: obj.cropX,
            crop_y: obj.cropY,
            fill: obj.fill,
            fill_rule: obj.fillRule,
            flip_x: obj.flipX,
            flip_y: obj.flipY,
            // globalCompositeOperation: obj.globalCompositeOperation,
            height: obj.height,
            left: obj.left,
            opacity: obj.opacity,
            origin_x: obj.originX,
            origin_y: obj.originY,
            paint_first: obj.paintFirst,
            metadata: JSON.stringify(obj.assetId) ?? undefined,
            scale_x: obj.scaleX,
            scale_y: obj.scaleY,
            // shadow: obj.shadow,
            skew_x: obj.skewX,
            skew_y: obj.skewY,
            stroke: obj.stroke,
            // strokeDashArray
            stroke_dash_offset: obj.strokeDashOffset,
            stroke_line_cap: obj.strokeLineCap,
            stroke_line_join: obj.strokeLineJoin,
            stroke_miter_limit: obj.strokeMiterLimit,
            stroke_uniform: obj.strokeUniform,
            stroke_width: obj.strokeWidth,
            top: obj.top,
            type: 5,
            // version: obj.version,
            // visible: obj.visible,
            width: obj.width,
            status: obj.status ?? status.todo
          }
          break
        }
        default:
          break
        }
        // this is track for updated annotations and  newly created annoatation
        // need to check wheter to update all objects if its not tocuched
        if (modifiedObj) {
          if (obj.id) {
            delete modifiedObj.document_id // there is no permission to update doc id
            updateObj.push({
              where: { id: { _eq: obj.id } },
              _set: modifiedObj
            })
          } else {
            newObj.push(modifiedObj)
          }
        }
      })

      // If all pages are processed, send the data to the backend
      if (pageAnnotations.length === inst.fabricObjects.length) {
        annotationCall(updateObj, newObj, inst.deletedIds)
      }
    })
  })
}
PDFAnnotate.prototype.loadFromJSON = function () {
  const inst = this
  const pages = []
  for (let i = 0; i < this.fabricObjects.length; i++) {
    if (Object.hasOwn(inst.savedDataObj.annotationobj, i)) {
      pages.push({ version: '5.3.0', objects: inst.savedDataObj.annotationobj[i] ?? [] })
    } else {
      pages.push({ version: '5.3.0', objects: [] })
    }
  }
  inst.fabricObjects.forEach((fabricObj, index) => {
    if (pages.length > index) {
      fabricObj.loadFromJSON(pages[index], function () {
        inst.fabricObjectsData[index] = fabricObj.toJSON(['id', 'blobKey', 'assetId'])
        fabricObj.renderAll()
      })
    }
  })
  inst.fabricObjects.forEach((fabricObj, index) => {
    if (inst.savedDataObj.imageObj[index]) {
      inst.savedDataObj.imageObj[index].forEach((obj) => {
        const fabricObj = inst.fabricObjects[obj.pageNo]
        if (fabricObj) {
          const image = new Image()
          image.onload = function () {
            fabricObj.add(new fabric.Image(image, {
              left: obj.left,
              top: obj.top,
              scaleX: obj.scaleX,
              scaleY: obj.scaleY,
              assetId: obj.assetId ?? undefined,
              blobKey: obj.blobKey || undefined,
              id: obj.id,
              selectable: obj.selectable,
              updatedBy: obj.updatedBy,
              createdBy: obj.createdBy,
              createdOn: obj.createdOn,
              status: obj.status,
              docId: obj.docId
            }))
          }
          image.src = obj.src
        }
      })
    }
  })
  // if (inst.viewOnly) {
  //   inst.fabricObjects.forEach((fabricObj, index) => {
  //     if (pages.length > index) {
  //       fabricObj.getObjects().forEach(obj => {
  //         obj.set({ selectable: false })
  //       })
  //     }
  //   })
  // }
}
PDFAnnotate.prototype.hideAllAnnotations = function () {
  const inst = this
  inst.fabricObjects.forEach((fabricObj) => {
    fabricObj.getObjects().forEach((obj) => {
      obj.set({ visible: false })
    })
    fabricObj.renderAll()
  })
}
PDFAnnotate.prototype.showAllAnnotations = function () {
  const inst = this
  inst.fabricObjects.forEach((fabricObj) => {
    fabricObj.getObjects().forEach((obj) => {
      obj.set({ visible: true })
    })
    fabricObj.renderAll()
  })
}
PDFAnnotate.prototype.disableArrowInstances = function () {
  const inst = this
  inst.arrowInstance.forEach((instance) => {
    instance && instance.unbindEvents()
  })
  inst.arrowInstance = []
}
PDFAnnotate.prototype.addImageToGivenPosition = function (left, top) {
  const inst = this
  const fabricObj = inst.fabricObjects[inst.active_canvas]
  if (fabricObj) {
    if (inst.iconObject.url) {
      const image = new Image()
      const assetId = inst.iconObject.id
      image.onload = function () {
        fabricObj.add(new fabric.Image(image, {
          left: left - 100 / 2,
          top: top - 100 / 2,
          scaleX: 100 / image.width,
          scaleY: 100 / image.height,
          assetId: assetId
        }))
      }
      image.src = inst.iconObject.url
      inst.iconObject = { }
      // image.assetId = inst.iconObject.id
      inst.fabricObjects.forEach((fabricObj) => {
        fabricObj.defaultCursor = null
        fabricObj.renderAll()
      })
      fabricObj.renderAll()
    }
  }
}
PDFAnnotate.prototype.uploadImageToS3 = function (fileName, fileExtension, fileData) {
  return new Promise((resolve, reject) => {
    const inst = this
    try {
      generateS3SubmittingUrl({
        tenantId: localStorage.getItem('tenantId'),
        feature: 'annotationImg',
        featureId: inst.docId || 'annnotationImg',
        fileName: fileName + Date.now() + '.' + fileExtension
      }).then(async ({ url }) => {
        await fetch(url, {
          method: 'PUT',
          body: fileData,
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        const blobkey = url.split('?')[0].split('.com/').pop()
        resolve(blobkey)
      })
    } catch (error) {
      console.log(error)
      reject(error)
    }
  })
}
PDFAnnotate.prototype.resetZoom = function () {
  const inst = this
  inst.fabricObjects.forEach(fabricObj => {
    // fabricObj.clear()
    fabricObj.setViewportTransform([1, 0, 0, 1, 0, 0]) // Reset zoom
    fabricObj.renderAll()
  })
}
PDFAnnotate.prototype.clearAllPages = function () {
  const inst = this
  confirmationDialog('Are you sure to clear all pages?', function (confirm) {
    if (!confirm) return
    inst.fabricObjects.forEach((fabricObj) => {
      const bg = fabricObj.backgroundImage

      const ids = fabricObj._objects.filter(obj => obj.id && (obj.createdBy.id === user.userId || (user.tenantLevelRole === 'ADMIN') || (isOnProjectLevel && user.projectLevelRole === 'ADMIN'))).map(obj => obj.id)
      inst.deletedIds = [...inst.deletedIds, ...ids]
      // ids.length > 0 && await deleteAnnoatationElements(ids)
      if (!((user.tenantLevelRole === 'ADMIN') || (isOnProjectLevel && user.projectLevelRole === 'ADMIN'))) {
        fabricObj._objects.forEach((obj) => {
          if (obj.createdBy.id === user.userId || (user.tenantLevelRole === 'ADMIN') || (isOnProjectLevel && user.projectLevelRole === 'ADMIN')) { fabricObj.remove(obj) }
        })
      } else {
        fabricObj.clear()
      }
      fabricObj.setBackgroundImage(bg, fabricObj.renderAll.bind(fabricObj))
    })
  })
}
PDFAnnotate.prototype.removeAllEventListeners = function () {
  const inst = this
  // Remove event listeners from fabric objects
  inst.fabricObjects.forEach((fabricObj) => {
    // Remove all 'mouse:down', 'mouse:down:before', 'mouse:move', 'mouse:up', 'mouse:wheel', and 'after:render' event listeners
    fabricObj.off('mouse:down')
    fabricObj.off('mouse:down:before')
    fabricObj.off('mouse:move')
    fabricObj.off('mouse:up')
    fabricObj.off('mouse:wheel')
    fabricObj.off('after:render')
    fabricObj.off('selection:updated')
    fabricObj.off('selection:created')
    fabricObj.off('selection:cleared')
    fabricObj.off('before:selection:cleared')

    // Set isDrawingMode to false to disable drawing
    fabricObj.isDrawingMode = false
  })

  // Remove event listeners from the document
  document.onkeydown = null
  document.onkeyup = null
  document.body.removeEventListener('keydown', keyDownHandler)

  // Remove event listeners from arrow instances
  inst.arrowInstance.forEach((arrow) => {
    arrow.unbindEvents()
  })
  this.removeEventListners()
  // Clear the arrow instances array
  inst.arrowInstance = []
}
PDFAnnotate.prototype.removeEventListners = function (text) {
  const inst = this
  inst.eventHandlers.forEach(({ fabricObj, handleCanvasClick }) => {
    fabricObj.upperCanvasEl.removeEventListener('click', handleCanvasClick)
  })
  inst.eventHandlers = []
}
PDFAnnotate.prototype.zoomIn = function (value) {
  if (value <= 0.01) {
    return
  }
  const inst = this
  inst.fabricObjects.forEach((fabricObj) => {
    const zoomCenterX = fabricObj.width / 2
    const zoomCenterY = fabricObj.height / 2

    fabricObj.zoomToPoint({ x: zoomCenterX, y: zoomCenterY }, value)

    const vpt = fabricObj.viewportTransform
    vpt[4] = (fabricObj.width - fabricObj.width * value) / 2
    vpt[5] = (fabricObj.height - fabricObj.height * value) / 2

    fabricObj.requestRenderAll()
  })
}
PDFAnnotate.prototype.destroy = function (containerId) {
  this.removeAllEventListeners()

  this.fabricObjects.forEach(fabricObj => {
    fabricObj.clear() // Clear the canvas
    fabricObj.dispose() // Dispose of the canvas to remove event listeners
  })

  // Remove all canvas elements from the DOM
  const container = document.getElementById(containerId)
  const children = document.querySelectorAll('.canvas-container')
  const children2 = document.querySelectorAll('.pdf-canvas')
  if (children.length > 0) {
    for (const child of children) { container.removeChild(child) }
  }
  if (children2.length > 0) {
    for (const child of children2) { container.removeChild(child) }
  }
  // Reset properties to their initial values
  this.number_of_pages = 0
  this.pages_rendered = 0
  this.selectedObjects = null
  this.active_tool = 0
  this.fabricObjects = []
  this.fabricObjectsData = []
  this.color = 'black'
  this.borderColor = 'red'
  this.borderSize = 2
  this.font_size = 16
  this.active_canvas = 0
  this.format = null
  this.orientation = null
  this.eventListnerArray = []
  this.origX = null
  this.origY = null
  this.docId = null
  this.arrowInstance = []
  this.iconObject = {}
  this.isDragging = false
  this.isZooming = false
  this.lastPosX = null
  this.lastPosY = null
  this.eventHandlers = []
  this.deletedIds = []

  // Optionally, remove references to external resources
  this.url = null
  this.savedDataObj = null
  this.loader = null
}
PDFAnnotate.prototype.updateStatus = function (status) {
  const inst = this
  const activeCanvas = inst.fabricObjects[inst.active_canvas]
  const selectdObjects = activeCanvas.getActiveObjects()
  if (selectdObjects.length !== 1) {
    alert('Select one annotation at time')
    return
  }
  selectdObjects[0].status = status
  activeCanvas.renderAll()
}
PDFAnnotate.prototype.showAllannoatations = function (status) {
  const inst = this
  inst.fabricObjects.forEach((fabricObj) => {
    fabricObj.getObjects().forEach((obj) => {
      obj.set({ visible: true })
    })
    fabricObj.renderAll()
  })
}
PDFAnnotate.prototype.showAllannoatations = function () {
  const inst = this
  inst.fabricObjects.forEach((fabricObj) => {
    fabricObj.getObjects().forEach((obj) => {
      obj.set({ visible: true })
    })
    fabricObj.discardActiveObject()
    fabricObj.requestRenderAll()
  })
}
PDFAnnotate.prototype.showannotationBasedOnStatus = function (status) {
  const inst = this
  inst.fabricObjects.forEach((fabricObj) => {
    fabricObj.getObjects().forEach((obj) => {
      if (status === obj.status) {
        obj.set({ visible: true })
      } else {
        obj.set({ visible: false })
      }
    })
    fabricObj.discardActiveObject()
    fabricObj.requestRenderAll()
  })
}
