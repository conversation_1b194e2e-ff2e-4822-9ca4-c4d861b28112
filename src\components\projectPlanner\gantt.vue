<template>
  <div>
    <div ref="ganttContainer" id="gantt-here"></div>
    <modal
      :open="showForm"
      @close="showForm = false"
      :closeOnOutsideClick="true"
      title="Task Details"
    >
    <task-view-form
    v-if="showForm"
    :task="selectedTask"
    @openDocumentView="openDocs"
    view="chart"/>
    <div>
    </div>
  </modal>
  <view-attached-docs
    v-if="openDocView"
    :fileDetails="docsforview"
    @close="closeDocs"
  ></view-attached-docs>
  </div>
</template>

<script>
import { Gantt } from '../../assets/gantt/dhtmlxgantt'
import { mapGetters } from 'vuex'
import { UpdateTask } from '@/api'
import TaskViewForm from './taskViewForm.vue'
import Modal from '../common/modal.vue'
import { debounce } from '@/utils/debounce'
import Loader from '@/plugins/loader'
import { getGanttTypeFromNumber } from '@/helper/gantt/getGanttType'
import viewAttachedDocs from './viewAttachedDocs.vue'

/**
 * Gantt Instance
 * @type {import('../../assets/gantt/dhtmlxgantt').GanttStatic}
 */
let gantt

export default {
  components: { Modal, TaskViewForm, viewAttachedDocs },
  props: {
    zoomLevel: {
      type: String,
      default () {
        return 'Weeks'
      }
    },
    tasks: {
      type: Object,
      default () {
        return { data: [], links: [] }
      }
    },
    criticalPath: {
      type: Boolean,
      required: true
    },
    showSlack: {
      type: Boolean,
      required: true
    },
    baseline: {
      default () {
        return null
      }
    },
    projectedPlan: {
      type: Boolean,
      required: true
    },
    calendar: {
      type: Object,
      required: true
    },
    clearSelectedBaseLine: {
      type: Boolean,
      required: true
    }
  },
  data () {
    return {
      task_id: null,
      baselineData: null,
      mappedHoliday: [],
      workTimeHours: [],
      selectedTask: null,
      showForm: false,
      baselineLayerId: '',
      projectedPlanLayerId: '',
      showSlackLayerId: '',
      openDocView: false,
      docsforview: []
    }
  },
  computed: {
    ...mapGetters(['isProjectAdmin'])
  },
  methods: {
    // adding baseline display
    drawBaseline (task) {
      const baselineTask = this.baselineData[task.id]
      if (baselineTask?.planned_end_date && baselineTask?.planned_start_date) {
        baselineTask.planned_end_date = gantt.date.parseDate(new Date(baselineTask.planned_end_date), gantt.config.date_format)
        baselineTask.planned_start_date = gantt.date.parseDate(new Date(baselineTask.planned_start_date), gantt.config.date_format)
        const sizes = gantt.getTaskPosition(task, baselineTask.planned_start_date, baselineTask.planned_end_date)
        const el = document.createElement('div')
        el.className = 'baseline'
        el.style.left = sizes.left - 6 + 'px'
        el.style.width = sizes.width + 10 + 'px'
        el.style.height = sizes.height + 6 + 'px'
        el.style.top = sizes.top + 1 + 'px'
        return el
      }
      return false
    },
    drawSlack (task) {
      const slack = gantt.getFreeSlack(task)
      if (!slack) {
        return null
      }
      var slackStart = new Date(task.end_date)
      var slackEnd = gantt.calculateEndDate(slackStart, slack)
      var sizes = gantt.getTaskPosition(task, slackStart, slackEnd)
      var el = document.createElement('div')
      el.className = 'slack'
      el.style.left = sizes.left + 'px'
      el.style.top = sizes.top + 1 + 'px'
      el.style.width = sizes.width + 'px'
      el.style.height = sizes.height - 1 + 'px'
      return el
    },
    drawProjectedPlan (task) {
      try {
        if (task.projected_end_date) {
          const projectedEndDate = gantt.date.parseDate(new Date(task.projected_end_date), gantt.config.date_format)
          let startDate
          if (task.projected_start_date) {
            startDate = gantt.date.parseDate(new Date(task.projected_start_date), gantt.config.date_format)
          } else {
            startDate = task.start_date
          }
          if (+new Date(startDate) - +gantt.getState().min_date <= 200000000) {
            gantt.config.start_date = gantt.date.add(new Date(startDate), -3, 'day')
            setTimeout(() => {
              gantt.render()
            })
          }
          if (+projectedEndDate - +gantt.getState().max_date >= -200000000) {
            gantt.config.end_date = gantt.date.add(projectedEndDate, 3, 'day')
            setTimeout(() => {
              gantt.render()
            })
          }
          const sizes = gantt.getTaskPosition(task, startDate, projectedEndDate)
          const el = document.createElement('div')
          if (task.end_date < projectedEndDate) {
            el.className = 'projected-alert'
          } else {
            el.className = 'baseline'
          }
          el.style.left = sizes.left - 6 + 'px'
          el.style.width = sizes.width + 10 + 'px'
          el.style.height = sizes.height + 6 + 'px'
          el.style.top = sizes.top + 1 + 'px'
          return el
        }
        return false
      } catch (err) {
        console.log(err)
      }
    },
    ganttInit () {
      // if gantt exists destroy the instance
      if (gantt) {
        gantt.clearAll()
        gantt.destructor()
        gantt = undefined
      }
      // getting the new gantt instance
      gantt = Gantt.getGanttInstance()

      // setting gantt date format
      gantt.config.date_format = '%Y-%m-%d %H:%i:%s'
      gantt.templates.parse_date = (date) => {
        return new Date(date)
      }
      gantt.config.server_utc = true
      // config for columns
      gantt.config.columns = [
        {
          name: 'order_index',
          label: 'No.',
          align: 'center',
          width: 40,
          min_width: 40,
          resize: true
        },
        { name: 'text', tree: true, min_width: 200, resize: true, align: 'left' },
        {
          name: 'start_date',
          label: 'Start date',
          align: 'center',
          width: 100,
          min_width: 100,
          resize: true
        },
        {
          name: 'end_date',
          label: 'End date',
          align: 'center',
          width: 100,
          min_width: 100,
          resize: true
        },
        {
          name: 'duration',
          label: 'Duration(Hrs)',
          align: 'center',
          resize: true,
          width: 100,
          min_width: 100
        },
        {
          name: 'progress',
          label: 'Progress',
          align: 'center',
          resize: true,
          width: 70,
          template: (task) => {
            return Math.round(task.progress > 1 ? task.progress : task.progress * 100) + '%'
          }
        }
      ]

      // gantt work time hours
      gantt.config.work_time = true

      const customCalendar = {
        id: 'global',
        worktime: {
          hours: this.calendar.workTimeHours,
          days: this.calendar.workTimeDays
        }
      }

      // Add the custom calendar to the Gantt chart
      gantt.addCalendar(customCalendar)

      const calendar = gantt.getCalendar('global')
      // setting up holidays
      for (const holiday of this.calendar?.holidays) {
        calendar.setWorkTime({ date: new Date(holiday.date), hours: false })
      }

      // setting config for draggable config columns
      gantt.config.grid_elastic_columns = true
      gantt.config.highlight_critical_path = this.$props.criticalPath
      gantt.config.layout = {
        css: 'gantt_container',
        cols: [
          {
            width: 400,
            maxWidth: 800,
            // adding horizontal scrollbar to the grid via the scrollX attribute
            rows: [
              { view: 'grid', scrollX: 'gridScroll', scrollable: true, scrollY: 'scrollVer' },
              { view: 'scrollbar', id: 'gridScroll' }
            ]
          },
          { resizer: true, width: 1 },
          {
            rows: [
              { view: 'timeline', scrollX: 'scrollHor', scrollable: true, scrollY: 'scrollVer' },
              { view: 'scrollbar', id: 'scrollHor' }
            ]
          },
          { view: 'scrollbar', id: 'scrollVer' }
        ]
      }

      // for styling weekend days
      gantt.templates.timeline_cell_class = (task, date) => {
        if (this.$props.zoomLevel === 'Days' || this.$props.zoomLevel === 'Weeks') {
          if (!calendar.isWorkTime({ date, unit: 'day' })) {
            return 'weekend'
          }
        }
      }
      // making tasks with children bold
      gantt.templates.grid_row_class = function (start, end, task) {
        return gantt.hasChild(task.id) ? 'weight-600' : ''
      }
      gantt.config.duration_unit = 'hour'
      gantt.config.skip_off_time = false
      gantt.config.min_column_width = 50
      gantt.config.auto_types = true
      gantt.config.fit_tasks = true

      // enabling toolips plugins
      gantt.plugins({
        tooltip: true,
        critical_path: true
      })

      // disabling resize of gantt
      gantt.config.drag_resize = false
      gantt.config.details_on_dblclick = true
      gantt.config.drag_links = false
      gantt.config.drag_move = false
      gantt.config.drag_progress = false
      gantt.config.open_tree_initially = true
      gantt.templates.task_class = function (start, end, task) {
        return 'no_progress'
      }
      // listening to gantt ready to do things after the gantt has been mounted
      gantt.attachEvent('onGanttReady', function () {
        // creating custom tooltip for the gantt task bars
        gantt.ext.tooltips.tooltipFor({
          selector: '.gantt_task_line',
          html: function (event, domElement) {
            const id = event.target?.parentNode?.attributes?.task_id?.nodeValue
            if (id) {
              const task = gantt.getTask(id)
              const taskAssignees = []
              for (const assignee of task?.task_assignees) {
                if (assignee?.status !== 'deleted') {
                  taskAssignees.push('<br>' + assignee?.first_name + ' ' + assignee?.last_name)
                }
              }
              if (task.type === 'milestone') {
                return `<b>Milestone:</b>  ${task?.text}  <br/>
                <b>Date: </b> ${task?.start_date.toLocaleDateString('en-US')}<br/>
                `
              } else {
                return `<b>Task:</b>  ${task?.text}  <br/>
                <b>Duration:</b>  ${task?.duration} hours <br/>
                <b>Progress:</b>  ${Math.round(task?.progress * 100)} % <br/>
                <b>Start Date: </b> ${new Date(task?.start_date).toLocaleDateString('en-GB')}<br/>
                <b>End Date: </b> ${new Date(task?.end_date).toLocaleDateString('en-GB')}<br/>
                <b>Projected Start Date: </b> ${task.projected_start_date ? new Date(task.projected_start_date).toLocaleDateString('en-GB') : '--'}<br/>
                <b>Projected End Date: </b> ${task.projected_end_date ? new Date(task.projected_end_date).toLocaleDateString('en-GB') : '--'}<br/>
                <b>Cost: </b> ${task?.cost ?? '--' }<br/>
                <b>Spi: </b> ${task?.spi ?? '--' }<br/>
                ${task.critical_progress ? `<b>Critical Progress: </b> ${task?.critical_progress}<br/>` : '' }
                <b>Slack: </b> ${task?.slack ? task?.slack + ' hrs' : '--'}<br/>
                <b>Assigned To:</b>  ${taskAssignees} <br/>
                <b>Actual Start Date: </b> ${task.actual_start_date ? new Date(new Date(task.actual_start_date).setUTCHours(0, 0, 0, 0)).toLocaleDateString('en-GB') : '--'}<br/>
                <b>Actual End Date: </b> ${task.actual_end_date ? new Date(new Date(task.actual_end_date).setUTCHours(0, 0, 0, 0)).toLocaleDateString('en-GB') : '--'}<br/>
                `
              }
            }
          }
        })

        // disabling tooltip for task in left section of gantt
        gantt.ext.tooltips.tooltipFor({
          selector: '.gantt_row',
          html: function () {
            return null
          }
        })

        // custom tooltip for gantt links
        gantt.ext.tooltips.tooltipFor({
          selector: '.gantt_task_link',
          html: function (event, domElement) {
            const targetLinkId =
              domElement?.closest('.gantt_task_link')?.dataset.linkId
            if (gantt.isLinkExists(targetLinkId)) {
              const link = gantt.getLink(targetLinkId)
              const source = gantt.getTask(link.source)
              const target = gantt.getTask(link.target)
              return `Link from <b>${source.text}</b> to <b>${target.text}</b>`
            }
          }
        })
      })
      // diabling gantt link edit
      gantt.attachEvent('onLinkDblClick', () => {
        return false
      })
      // setting gantt zoom events
      gantt.ext.zoom.init({
        levels: [
          {
            name: 'Days',
            min_column_width: 90,
            scale_height: 60,
            scales: [
              { unit: 'month', step: 1, format: '%F, %Y' },
              { unit: 'day', step: 1, format: '%d %M' }
            ]
          },
          {
            name: 'Weeks',
            min_column_width: 120,
            scale_height: 60,
            scales: [
              { unit: 'month', step: 1, format: '%F, %Y' },
              {
                unit: 'week',
                step: 1,
                format: function (date) {
                  const dateToStr = gantt.date.date_to_str('%d %M')
                  const endDate = gantt.date.add(gantt.date.add(date, 1, 'week'), -1, 'day')
                  return dateToStr(date) + ' - ' + dateToStr(endDate)
                }
              }
            ]
          },
          {
            name: 'quarter',
            min_column_width: 90,
            scale_height: 60,
            scales: [
              {
                unit: 'quarter',
                step: 1,
                format: function quarterLabel (date) {
                  const month = date.getMonth()
                  let qNum

                  if (month >= 9) {
                    qNum = 4
                  } else if (month >= 6) {
                    qNum = 3
                  } else if (month >= 3) {
                    qNum = 2
                  } else {
                    qNum = 1
                  }

                  return date.getFullYear() + ' ' + 'Q' + qNum
                }
              },
              { unit: 'month', step: 1, format: '%M' }
            ]
          },
          {
            name: 'Months',
            min_column_width: 90,
            scale_height: 60,
            scales: [
              { unit: 'year', step: 1, format: '%Y' },
              { unit: 'month', step: 1, format: '%F' }
            ]
          },
          {
            name: 'year',
            min_column_width: 90,
            scale_height: 60,
            scales: [
              { unit: 'year', step: 1, format: '%Y' }
            ]
          }
        ]
      })

      // setting zoom level based on the value passed from props
      gantt.ext.zoom.setLevel(this.$props.zoomLevel)

      // attaching new function to showLightbox for showing custom dialog
      gantt.showLightbox = (id) => {
        this.selectedTask = JSON.parse(JSON.stringify(gantt.getTask(id)))
        this.selectedTask.hasChild = gantt.hasChild(id)
        gantt.ext.tooltips.tooltip.hide()
        this.showForm = true
      }

      // diabling gantt link edit
      gantt.attachEvent('onLinkDblClick', () => {
        return false
      })

      // listening to progress change in order to update the progress of the parent task
      gantt.attachEvent('onTaskDrag', (id, mode, task, original) => {
        if (mode === 'progress') {
          if (original.actual_start_date === null) {
            task.actual_start_date = new Date()
          }
          if (task.progress === 1) {
            task.actual_end_date = new Date()
          }
          if (task.parent !== 0) {
            this.updateParentProgress(id)
          }
        }
      })

      // listening to parse event which would be called while gantt is parsing the tasks
      gantt.attachEvent('onParse', function () {
        // changing the gantt start_date and end_date before the gantt is rendered
        const range = gantt.getSubtaskDates()
        const scaleUnit = gantt.getState().scale_unit
        // setting gantt start_date and end_date for the timeline
        if (range.start_date && range.end_date) {
          gantt.config.start_date = gantt.calculateEndDate(range.start_date, -7, scaleUnit)
          gantt.config.end_date = gantt.calculateEndDate(range.end_date, 7, scaleUnit)
        }

        gantt.sort((a, b) => {
          const aOrderIndex = a.order_index?.split('.')
          const bOrderIndex = b.order_index?.split('.')
          const smallOrderIndex = aOrderIndex?.length < bOrderIndex?.length ? aOrderIndex : bOrderIndex
          for (let i = 0; i < smallOrderIndex?.length; i++) {
            if (parseInt(aOrderIndex[i]) > parseInt(bOrderIndex[i])) {
              return 1
            } else if (parseInt(aOrderIndex[i]) < parseInt(bOrderIndex[i])) {
              return -1
            }
          }
        })
        gantt.batchUpdate(() => {
          gantt.eachTask((task) => {
            task.progress =
            task.progress > 0 ? task.progress / 100 : task.progress
            task.type = getGanttTypeFromNumber(task.type)
            const hasChild = gantt.hasChild(task.id)
            task.task_assignees = task.task_assignees?.map(data => (
              {
                id: data.user_id,
                first_name: data.assignee.first_name,
                last_name: data.assignee.last_name,
                status: null
              }))
            // if (hasChild) {
            //   task.color = '#3CB371'
            // }
            // // if gantt does not have any child render it as task type, else dhtmlx would not render the task
            if (task.type === 'project' && !hasChild) {
              task.type = 'task'
              task.color = '#3CB371'
            }
            gantt.updateTask(task.id, task)
          })
        })
      })
      gantt.init(this.$refs.ganttContainer)
      gantt.config.deepcopy_on_parse = true
      if (this.$props.tasks.data.length === 1) {
        this.$props.tasks.data[0].end_date = this.$props.tasks.data[0].planned_end_date
      }
      gantt.parse(this.$props.tasks)
    },
    changeGanttZoom (value) {
      // optimize this to avoid rerender if same zoom level is specified again
      gantt.ext.zoom.setLevel(value)
    },
    updateParentProgress (id) {
      const updateTaskArr = []
      const taskDetails = gantt.getTask(id)
      updateTaskArr.push({ id: taskDetails.id, progress: Math.round(taskDetails.progress * 100) })
      gantt.batchUpdate(() => {
        gantt.eachParent((task) => {
          const childTaskIds = gantt.getChildren(task.id)
          let completedDuration = 0
          let totalCriticalDuration = 0
          let progress = 0
          let totalWeightage = 0
          const parentTaskDuration = task.duration
          for (const childTaskId of childTaskIds) {
            const childTask = gantt.getTask(childTaskId)
            const weightage = childTask.duration / parentTaskDuration
            totalWeightage += weightage
            progress += weightage * childTask.progress
            if (childTask.is_critical) {
              totalCriticalDuration += childTask.duration
              completedDuration += childTask.duration * childTask.progress * 0.01
            }
          }
          task.progress = progress / totalWeightage
          task.critical_progress = (completedDuration / totalCriticalDuration) * 100 ?? 0
          if (task.progress === 1) {
            task.actual_end_date = new Date()
          }
          updateTaskArr.push({ id: task.id, progress: Math.round(task.progress * 100), actual_end_date: task.actual_end_date, actual_start_date: task.actual_start_date })
        }, id)
      })

      debounce(() => this.updateProgressToServer(updateTaskArr), 500, this)()
    },
    async updateProgressToServer (tasks) {
      const loader = new Loader()
      try {
        loader.show()
        await Promise.all(tasks.map(task => {
          const id = task.id
          task.id = undefined
          return UpdateTask(id, task)
        }
        ))
        this.$notify.success('Updated the progress successfully')
      } catch (err) {
        this.$notify.alert(err?.message ?? 'Something went wrong')
      } finally {
        loader.hide()
      }
    },
    closeDocs () {
      this.openDocView = false
      this.docsforview = []
    },
    openDocs (docs) {
      this.openDocView = true
      this.docsforview.push(docs.core_document)
    }
  },
  watch: {
    tasks () {
      this.ganttInit()
    },
    zoomLevel (newZoom, oldZoom) {
      if (newZoom !== oldZoom) {
        this.changeGanttZoom(newZoom)
      }
    },
    criticalPath (newCriticalPath) {
      gantt.config.highlight_critical_path = newCriticalPath
      gantt.render()
    },
    baseline () {
      if (this.$props.baseline) {
        this.baselineData = {}
        this.$props.baseline.tasks.forEach((item) => {
          this.baselineData[item.id] = {
            planned_end_date: item?.planned_end_date,
            planned_start_date: item?.planned_start_date
          }
        })
        this.baselineLayerId = gantt.addTaskLayer(this.drawBaseline)
        gantt.render()
      }
    },
    clearSelectedBaseLine (value) {
      if (value) {
        if (this.baselineLayerId) {
          gantt.removeTaskLayer(this.baselineLayerId)
          this.baselineLayerId = ''
          this.baselineData = {}
          gantt.render()
        }
      }
    },
    showSlack (value) {
      if (value) {
        if (this.baselineLayerId) {
          gantt.removeTaskLayer(this.baselineLayerId)
          this.baselineLayerId = ''
          this.baselineData = {}
        }
        if (this.projectedPlanLayerId) {
          gantt.removeTaskLayer(this.projectedPlanLayerId)
          this.projectedPlanLayerId = ''
        }
        this.showSlackLayerId = gantt.addTaskLayer(this.drawSlack)
        gantt.render()
      } else {
        if (this.showSlackLayerId) {
          gantt.removeTaskLayer(this.showSlackLayerId)
          this.showSlackLayerId = ''
        }
      }
    },
    projectedPlan (value) {
      if (value) {
        if (this.baselineLayerId) {
          gantt.removeTaskLayer(this.baselineLayerId)
          this.baselineLayerId = ''
        }
        this.projectedPlanLayerId = gantt.addTaskLayer(this.drawProjectedPlan)
        gantt.render()
      } else {
        if (this.projectedPlanLayerId) {
          gantt.removeTaskLayer(this.projectedPlanLayerId)
          this.projectedPlanLayerId = ''
        }
      }
    }
  },
  mounted () {
    window.gantt = undefined
    window.Gantt = undefined
    this.task_id = this.$route?.params?.task_id
    if (this.task_id) {
      this.ganttInit()
    } else this.ganttInit()
  },
  beforeDestroy () {
    // clearing all gantt cache
    gantt.clearAll()
    gantt.destructor()
    gantt = undefined
  }
}
</script>

<style lang="scss">
@import "../../assets/gantt/dhtmlxgantt.css";
@import "@/assets/scss/sizeVars.scss";

#gantt-here {
  height: 70vh;
  @media screen and (min-width: $xxl) {
    height: 80vh;
  }
  .gantt_task_bg {
    .weekend {
      background-color: var(--gray);
    }
  }
  .gantt_grid_scale .gantt_grid_head_cell,
  .gantt_task .gantt_task_scale .gantt_scale_cell {
    font-weight: 550;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.7);
  }

  // to remove the level 0 add button
  .gantt_grid_head_cell.gantt_grid_head_add.gantt_last_cell {
    display: none;
  }
  .gantt_project .gantt_task_progress_drag {
     visibility: hidden;
      }
}
.no_progress .gantt_task_progress_drag{
        display: none !important;
    }
.baseline {
        position: absolute;
        z-index: 0;
        opacity: 0.3;
        height: 12px;
        background: #6dadf0;
        border: 1px solid #4ac1f8;
    }

.projected-alert {
  position: absolute;
        z-index: 0;
        opacity: 0.3;
        height: 12px;
        background: #f55105;
        border: 1px solid #f55105;
}

.slack {
position: absolute;
border-radius: 0;
opacity: 0.7;
border: none;
border-right: 1px solid #b6b6b6;
margin-left: -2px;
background: #b6b6b6;
background: repeating-linear-gradient(45deg, #FFFFFF,
#FFFFFF 5px,
#b6b6b6 5px,
#b6b6b6 10px);
}

</style>
