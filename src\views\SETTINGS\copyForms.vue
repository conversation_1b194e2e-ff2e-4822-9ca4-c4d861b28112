<template>
    <div class="form-template fh">
      <div class="form-template-bar v-center space-between px-3 py-3">
        <h1 class="weight-500 xxl">
          Form Template
        </h1>
        <div class="v-center">
          <div>
            <label for="">Sort By</label>
            <select v-model="selectedSort" class="sort" name="" id="">
              <option value="1">Latest</option>
              <option value="2">Oldest</option>
              <option value="3">Sort: A to Z</option>
              <option value="4">Sort: Z to A</option>
            </select>
          </div>
          <div class="input-group search m mx-3">
            <input
              v-model="searchKeyword"
              @input="onSearchInput"
              type="text"
              placeholder="Search by name"
            />
          </div>
          <button v-if="showLInkToWorkFlows" class="btn btn-black mr-2" @click="enableWorkflowLink">Link Workflows</button>
          <button v-if="showLinkTemplateButton" class="btn mr-2 btn-black " @click="workflowLinkEdit=false">Cancel</button>
          <button v-if="showLinkTemplateButton" class="btn mr-2" @click="linkWorkFlow">Save</button>
          <router-link to="/form-builder" class="btn flex"  v-if="user.tenantLevelRole === 'ADMIN'">
            <button v-if="createNewTemplate" class="btn">+ Add Template</button>
          </router-link>
        </div>
      </div>
      <div class="form-template-container">
        <div class="fh center" v-if="loading">
          <loading-circle />
        </div>
        <div
          class="form-template-no-form fh center"
          v-if="!loading && templates.length === 0"
        >
          No Form Templates
        </div>
        <div
          :class="{
            fh: true,
            'mt-2': true,
            'grid-2': selectedTemplate.open,
          }"
          style="align-items: flex-start"
          v-if="!loading && templates.length"
        >
        <div class="copy-dtx-table"
        :style="{
    overflowY: templates.length < 7 ? 'visible' : 'auto'
  }">
    <table >
        <thead>
      <tr class="m">
        <th>Name</th>
        <th>Type</th>
        <th>Created By</th>
        <th>Created At </th>
        <th>Workflow Template</th>
        <th  v-if="showFormAction">Action</th>
      </tr>
    </thead>
      <tbody>
        <tr  v-for="item in templates" :key="item.id">
        <td v-overflow-tooltip class="elipsis-text ">{{ item.name }}</td>
        <td v-overflow-tooltip class="elipsis-text ">{{ item.core_form_type.name }}</td>
            <td >
                {{ getFullName(item.template_created_by) }}
           </td>
        <td>{{ item.created_on | genericFormatDate }}</td>
        <td>
<div v-if="workflowLinkEdit">
  <custom-dropdown :list="workflowTempaltesFiltered" :selected="item.workFlowLinkTemp" @select="(WFT)=>handleWrokflowSelection(WFT,item)" @toggle="toggleDropDown(item)" @deselect="()=>handleWrokflowSelection({},item)" class="form-template-container-wft">

  <template #before-items>
    <div class="fh center py-4" v-if="WFTLoading && workflowTempaltes.length === 0"  >
          <loading-circle />
        </div>
    <div class="fh center py-4" v-else-if="!WFTLoading && workflowTempaltes.length === 0"  >
         No Workflow Templates
        </div>
    <div class="fh center p-3 form-template-container-wft-search-box" v-else  >
        <input type="text p-2" class="form-template-container-wft-input"  v-model="WFTSearchKeyword" placeholder="Search"/>
        </div>
  </template>

  </custom-dropdown>
</div>
<div v-else  v-overflow-tooltip class="w-100 elipsis-text ">
{{item.workflow_template?.name ?? '--'}}
</div>

        </td>
       <td v-if="showFormAction" class="action-column">
        <div class="action-icons">
            <img
              v-if="user.tenantLevelRole === 'ADMIN'"
              width="24"
              v-tooltip="`Edit Template`"
              class="pointer"
              src="~@/assets/images/pencil.svg"
              @click="
                      $router.push(
                        `/form-editor/${item.id}`
                      )
              "
              alt="" />
              <img
              class="ml-2"
                width="24"
                v-tooltip="`Preview Template`"
                src="~@/assets/images/icons/preview-icon.svg"
                alt=""
                @click="openPreview(item.id)"
              />
          </div>
       </td>
      </tr>
      </tbody>
    </table>
    <div class="space-between">
      <pagination2 :length="totalcount" :pageNumber="pageNumber" :perPage="perPage"
            @selectPage="selectPage"
          class="mt-3 mx-2"
        />
        <span class="mt-5">Total form templates : &nbsp; <b class="mr-5"> {{ totalcount }}</b></span>
    </div>
  </div>
          <div class="form-template-preview fh" v-if="selectedTemplate.open">
            <div class="form-template-preview-header v-center space-between">
              <h3>Preview</h3>
              <div class="v-center">
                <select @change="changeVersion" v-model="selectedTemplate.version">
                <option
                  v-for="version in selectedTemplate.data"
                  :key="version.id"
                  :value="version.id"
                >
                  V-{{ version.version_id }} ({{
                    version.active ? "Latest" : ""
                  }})
                </option>
              </select>
              <img class="ml-4" @click="closePreview" width="18px" src="~@/assets/images/close.png" alt="">
              </div>
            </div>
            <div class="form-template-preview-body">
              <form-preview
                :formName="selectedTemplate.name"
                :formTemplateBody="getSelectedVersionItems"
                :formType="selectedTemplate.type"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>

<script>
import { mapGetters } from 'vuex'
import { genericFormatDate } from '@/utils/date'
import { getFormTemplateswithPagination, getDetailFormTemplate, getFullWorkFLowTemplates, formWorkFlowLinkingApi } from '@/api'
import loadingCircle from '../../components/common/loadingCircle.vue'
import { timeStampToDateTime } from '@/filters/dateFilter.js'
import FormPreview from '../../components/form/formPreview.vue'
import Config from '@/config'
import customDropdown from '@/components/common/customDropdown.vue'
import { alert, success } from '@/plugins/notification'
import Pagination2 from '@/components/common/pagination2.vue'
import { debounce } from '@/utils/debounce'
import { previousPath } from '@/router'
export default {
  components: { loadingCircle, FormPreview, customDropdown, Pagination2 },
  name: 'form-template',
  filters: {
    timeStampToDateTime,
    genericFormatDate
  },
  data: () => ({
    selectedSort: '1',
    perPage: 10,
    pageNumber: 1,
    totalcount: 0,
    updateOnSearch: null,
    templates: [],
    loading: false,
    searchKeyword: '',
    selectedTemplate: {
      id: 0,
      open: false,
      loading: false,
      data: [],
      name: '',
      type: '',
      version: 0
    },
    Config: Config,
    workflowLinkEdit: false,
    workflowTempaltes: [],
    workflowLinkMap: {},
    WFTLoading: false,
    WFTSearchKeyword: '',
    formType: null
  }),
  computed: {
    ...mapGetters(['user', 'getUserById']),
    ...mapGetters('form', ['searchKeywordStore']),
    getSelectedVersionItems () {
      const version = this.selectedTemplate.data.find(
        (item) => item.id === this.selectedTemplate.version
      ) ?? { template_fields: [] }
      return version.template_fields
    },
    createNewTemplate () {
      return (
        (this.user.tenantLevelRole === 'ADMIN' ||
            this.user.tenantLevelRole === 'COLLABORATOR') && !this.workflowLinkEdit
      )
    },
    showLInkToWorkFlows () {
      return (
        this.user.tenantLevelRole === 'ADMIN' && !this.workflowLinkEdit
      )
    },
    showFormAction () {
      return (
        (this.user.tenantLevelRole === 'ADMIN' ||
            this.user.tenantLevelRole === 'COLLABORATOR' ||
            this.user.tenantLevelRole === 'EDITOR') && !this.workflowLinkEdit
      )
    },
    showLinkTemplateButton () {
      return (
        (this.user.tenantLevelRole === 'ADMIN' ||
            this.user.tenantLevelRole === 'COLLABORATOR' ||
            this.user.tenantLevelRole === 'EDITOR') && this.workflowLinkEdit
      )
    },
    workflowTempaltesFiltered () {
      let templates = this.workflowTempaltes
      if (this.WFTSearchKeyword?.toLowerCase()) {
        templates = templates.filter(t =>
          t.name.toLowerCase().includes(this.WFTSearchKeyword?.toLowerCase())
        )
      }
      if (this.formType === this.Config.DOCUMENT_REVISIONING_FORM.form_type) {
        templates = templates.filter(t => t.feature_id === this.Config.CORE_FEATURES.DOCUMENTS)
      } else {
        templates = templates.filter(t => t.feature_id !== this.Config.CORE_FEATURES.DOCUMENTS)
      }
      return templates
    }
  },
  methods: {
    toggleDropDown (template) {
      this.WFTSearchKeyword = ''
      this.formType = template.form_type
    },
    onSearchInput () {
      this.$store.dispatch('form/saveSearchKeyword', this.searchKeyword)
      this.updateOnSearch()
    },
    async getTemplates () {
      this.loading = true
      try {
        // true -- // this arguement true is for to avoid standard Material form
        const res = await getFormTemplateswithPagination((this.pageNumber - 1) * this.perPage, this.perPage, true, this.searchKeyword, this.selectedSort)
        this.templates = res.core_form_templates
        this.totalcount = res.core_form_templates_aggregate.aggregate.count
      } catch (err) {
        alert('Unable to fetch form templates')
        console.log(err)
      } finally {
        this.loading = false
      }
    },
    closePreview () {
      this.selectedTemplate = {
        id: 0,
        open: false,
        loading: false,
        data: [],
        name: '',
        type: '',
        version: 0
      }
    },
    getFullName (user) {
      return `${user.first_name} ${user.last_name}`
    },
    openPreview (id) {
      this.selectedTemplate.id = id
      this.selectedTemplate.open = true
      getDetailFormTemplate(id).then((res) => {
        const template = res.core_form_templates_by_pk
        this.selectedTemplate.name = template.name
        this.selectedTemplate.type = template.core_form_type.name
        this.selectedTemplate.data = template.template_versions.map((item) => ({
          id: item.id,
          version_id: item.version_id,
          active: item.active,
          template_fields: item.template_fields.map((field) => ({
            ...field,
            key: field.form_field.key
          }))
        }))
        this.selectedTemplate.version =
            template.template_versions?.find((item) => item.active)?.id ?? 0
      })
    },
    enableWorkflowLink () {
      this.templates = this.templates.map((item) => ({
        ...item,
        workFlowLinkTemp: { flag: null, ...item.workflow_template }
      }))
      this.getTemplatesForWFL()
      this.workflowLinkEdit = true
    },
    async getTemplatesForWFL () {
      try {
        this.WFTLoading = true
        const res2 = await getFullWorkFLowTemplates()
        this.workflowTempaltes = res2.workflow_templates
        this.WFTLoading = false
      } catch (error) {
        alert('Error fetching  form templates')
        console.error('Error fetching templates', error)
      }
    },
    handleWrokflowSelection (item, formTemp) {
      if ((item.id === formTemp.workflow_template?.id) && item.id) {
        formTemp.workFlowLinkTemp = { flag: null, id: item.id ?? -1, name: item.name ?? null }
        return
      }
      formTemp.workFlowLinkTemp = { flag: item ? 'update' : null, id: item.id ?? -1, name: item.name ?? null }
    },
    async linkWorkFlow () {
      this.loading = true
      const updatesObjList = []

      for (const item of this.templates) {
        if (item.workFlowLinkTemp.id === -1 || !item.workFlowLinkTemp.id) {
          item.workFlowLinkTemp.id = null
        }
        if (item.workFlowLinkTemp.flag === 'update') {
          updatesObjList.push({
            _set: {
              workflow_template_id: item.workFlowLinkTemp.id
            },
            where: {
              id: {
                _eq: item.id
              }
            }

          })
        }
      }
      if (updatesObjList.length === 0) {
        this.loading = false
        alert('No changes made')
        return
      } else {
        try {
          await formWorkFlowLinkingApi(updatesObjList)
          success('Workflow templates linked successfully')
        } catch (error) {
          console.log(error)
          alert('Error linking workflow templates')
          this.loading = false
          return
        }
      }
      this.workflowLinkEdit = false
      this.getTemplates()
    },
    selectPage (page) {
      this.pageNumber = page
      this.getTemplates()
    }
  },
  watch: {
    selectedSort (value) {
      if (value === '0') return 0
      else return this.getTemplates()
    }
  },
  created () {
    if (
      !previousPath.includes('/copy-forms') &&
      !previousPath.includes('/form-editor')
    ) {
      this.$store.dispatch('form/removeSearchKeyword')
    }
    this.searchKeyword = this.searchKeywordStore
    this.updateOnSearch = debounce(() => {
      this.pageNumber = 1
      this.getTemplates()
    }, 500)
    this.getTemplates()
  }
}
</script>

  <style lang="scss" scoped >
      .sort {
    height: 2em;
    width: 10rem;
    margin: 0 5px 0 5px;
    // margin-left: 2px;
    font-size: 14px;
    background-color: var(--brand-light-color);
    line-height: 1;
    border: 1px solid var(--brand-color);
    border-radius: 4px;
    padding: 4px 12px;
    }
  .form-template {
    &-bar {
      height: 60px;
      margin: -12px;
      margin-bottom: 0;
      background: var(--bg-color);
      border-bottom: var(--border);
    }
    &-container {
      height: calc(100% - 60px);
      &-wft{
        &-search{
        padding:5px 10px;
        background: white;
    position: sticky;
    top: 0;
    z-index: 10;
      }
    &-input{
        width: 100%;
        padding: 5px 10px;
        border-radius: 4px;
        border: 1px solid rgba(0, 0, 0, 0.3);
        background-color: transparent;
      }
    }
    }
    &-no-form {
      font-size: 20px;
      color: var(--text-color);
    }
    &-group {
      overflow: auto;
      position: relative;
      & > div {
        & > div {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          & > div {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
        }
      }
      &-header {
        background-color: var(--brand-color);
        position: sticky;
        top: 0;
        &-item {
          padding: 4px 10px;
          font-size: 14px;
          font-weight: 600;
          color: var(--text-color);
        }
      }
      &-row {
        display: grid;
        grid-template-columns: 2fr 3fr 1fr 1fr 80px;
      }
      &-item {
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        & > div {
          padding: 4px 10px;
          font-size: 14px;
          color: var(--text-color);
        }
      }
    }
    &-preview {
      &-header {
        padding: 5px 10px;
        background: var(--brand-color);
        & > h3 {
          font-size: 14px;
          font-weight: 600;
          color: var(--text-color);
        }
        & select {
          font-size: 10px;
          background-color: var(--brand-light-color);
          line-height: 1;
          border: 1px solid rgba(0, 0, 0, 0.3);
          border-radius: 4px;
          padding: 2px 6px;
        }
      }
      &-body {
        height: calc(100% - 40px);
        padding: 10px;
        background: var(--bg-color);
        overflow-y: auto;
      }
    }
  }
  </style>
