/* eslint-disable standard/no-callback-literal */
// create a conferim dialog
// @param {string} message - the message to display
// @param {function} callback - the callback to call when the user clicks ok
// @param {string} okText - the text to display on the ok button
// @param {string} cancelText - the text to display on the cancel button
// @param {string} title - the title of the dialog

export default function (message, callback, okText, cancelText, title, message2 = '', showDropdown = false, dropdownOptions = []) {
  const dialog = document.createElement('div')
  dialog.className = 'dialog'

  let dropdownHtml = ''

  if (showDropdown && dropdownOptions.length > 0) {
    dropdownHtml = `
      <div class="dialog-dropdown">
        <select class="dialog-select">
          ${dropdownOptions.map(option => `<option value="${option.value}">${option.label}</option>`).join('')}
        </select>
      </div>`
  }

  dialog.innerHTML = `
    <div class="dialog-container">
      <div class="dialog-header">
      <div class="dialog-title">${title || 'Confirm'}</div>
      <div class="dialog-close">&times;</div>
      </div>
      <div class="dialog-message">${message}</div>
      <div class="dialog-message">${message2}</div>
      ${dropdownHtml}
      <div class="dialog-buttons">
      <button class="btn btn-black dialog-cancel">${cancelText || 'Cancel'}</button>
        <button class="btn dialog-ok">${okText || 'OK'}</button>
      </div>
    </div>
  `

  document.body.appendChild(dialog)

  const selectElement = dialog.querySelector('.dialog-select')

  dialog.querySelector('.dialog-ok').addEventListener('click', () => {
    const selectedValue = selectElement ? selectElement.value : null
    dialog.remove()
    callback && callback(true, selectedValue) // Pass the selected value back
  })

  dialog.querySelector('.dialog-cancel').addEventListener('click', () => {
    dialog.remove()
    callback && callback(false, null) // No value for cancellation
  })

  dialog.querySelector('.dialog-close').addEventListener('click', () => {
    dialog.remove()
    callback && callback(false, null) // No value for cancellation
  })
}
