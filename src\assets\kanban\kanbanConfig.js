export function getData () {

    function formatDate(dateString) {
        const options = { month: 'short', day: '2-digit' };
        return new Date(dateString).toLocaleDateString(undefined, options);
      }

  const cardShape = {
    label: true,
    progress: true,
    task_assignees: true,
    parent_task: true,
    votes: true,
    start_date: true,
    end_date: true,
    projected_start_date: true,
    projected_end_date: true,
    comments: true,
    priority: {
      show: true,
      values: [
        { id: 1, color: '#FF5252', label: 'high', value: 1 },
        { id: 2, color: '#FFC975', label: 'medium', value: 2 },
        { id: 3, color: '#65D3B3', label: 'low', value: 3 }
      ]
    },
    color: true,
    menu: true,
    cover: true,
    attached: false
  }


  function cardTemplate({ cardFields, selected, dragging, cardShape }) {
    const {
      label,
      color,
      start_date,
      end_date,
      projected_start_date,
      projected_end_date,
      progress,
      task_assignees,
      parent_task,
      is_critical,
      votes,
      comments,
      priority,
      is_viewer
    } = cardFields;

// Sample array of colors for assignee circles
const assigneeColors = ['#007bff', '#ff5722', '#4caf50', '#f44336', '#9c27b0'];

// Iterate through task assignees and generate assignee circles
const taskAssigneesHtml = task_assignees.map((assignee, index) => {
  const assigneeInitials = assignee.first_name.charAt(0).toUpperCase();
  const assigneeColor = assigneeColors[index % assigneeColors.length]; // Cycle through colors

  return `<div class="assignee-circle" style="background-color:${assigneeColor};">${assigneeInitials}</div>`;
}).join('');

// Wrap the assignee circles in a container
const assigneeCirclesContainer = `<div class="assignee-circles-container">${taskAssigneesHtml}</div>`;
    const formattedStartDate = formatDate(start_date);
    const formattedEndDate = formatDate(end_date);
  const formattedProjectedStartDate = projected_start_date ? formatDate(projected_start_date) : 'Date not set';
  const formattedProjectedEndDate = projected_end_date ? formatDate(projected_end_date) : 'Date not set';
    
    const dateIcon = `
      <img src="https://www.svgrepo.com/show/35457/calendar-symbol.svg" class="date-icon" alt="Calendar Icon"style="height: 15px; width: 15px; vertical-align: middle;" >
    `;

    const criticalIcon = `<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="m40-120 440-760 440 760H40Zm138-80h604L480-720 178-200Zm302-40q17 0 28.5-11.5T520-280q0-17-11.5-28.5T480-320q-17 0-28.5 11.5T440-280q0 17 11.5 28.5T480-240Zm-40-120h80v-200h-80v200Zm40-100Z"/></svg>`

    const successorIcon = `<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M647-440H160v-80h487L423-744l57-56 320 320-320 320-57-56 224-224Z"/></svg>`
   const predecessorIcon = `<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="m313-440 224 224-57 56-320-320 320-320 57 56-224 224h487v80H313Z"/></svg>`
   const documentIcon = `<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M240-80q-50 0-85-35t-35-85v-120h120v-560l60 60 60-60 60 60 60-60 60 60 60-60 60 60 60-60 60 60 60-60v680q0 50-35 85t-85 35H240Zm480-80q17 0 28.5-11.5T760-200v-560H320v440h360v120q0 17 11.5 28.5T720-160ZM360-600v-80h240v80H360Zm0 120v-80h240v80H360Zm320-120q-17 0-28.5-11.5T640-640q0-17 11.5-28.5T680-680q17 0 28.5 11.5T720-640q0 17-11.5 28.5T680-600Zm0 120q-17 0-28.5-11.5T640-520q0-17 11.5-28.5T680-560q17 0 28.5 11.5T720-520q0 17-11.5 28.5T680-480ZM240-160h360v-80H200v40q0 17 11.5 28.5T240-160Zm-40 0v-80 80Z"/></svg>`

    const projected_date = `<div class="date-icon-container">
    ${dateIcon}
    <span class="date-text">Projected Date: ${formattedProjectedStartDate} - ${formattedProjectedEndDate}</span>
  </div>`
  
    const cardMenu = `
      <div
        class="menu-icon"
        data-menu-id=${cardFields.id}
        data-ignore-selection="true">
          <i class="wxi-dots-v"></i>
      </div>
    `;
  
    const progressBarWidth = `${progress}%`;
    let progressBarHtml = '';
  
      progressBarHtml = `
        <div class="progress">
          <div class="progress-bar" style="width:${progressBarWidth}; background-color: blue;"></div>
          <div class="progress-label">${progress}%</div>
        </div>
      `;
  
    return `
    <div class="myCard ${selected ? 'selected' : ''}" style="border-top: 5px solid ${color};">
      <div class="card-header">
          <div class="label mb-3">
          <div class="elipsis-text">${label}</div> 
           <div class="${is_critical ? '' : 'display_none'}"> ${criticalIcon}</div>
           </div>
          <div class="mb-3">
        <div class="date-icon-container mb-1">
          ${dateIcon}
          <span class="date-text">Planned Date: ${formattedStartDate} - ${formattedEndDate}</span>
        </div>
        ${projected_date}
        </div>
        </div>
        ${assigneeCirclesContainer}
         <div class="${ (is_viewer || !parent_task) ?'display_none' : ''} kanban-parent-task-btn" data-menu-id=${cardFields.id}>
        <span class="tooltip">${parent_task} <span class="tooltiptext">Parent tasks.</span> </span>
        </div>
      ${progressBarHtml}
      <div class="custom-card-shortcuts"> 
      <div class="${ is_viewer ?'display_none' : ''} attach-document-btn custom-card-shortcuts--add-document-icon" data-menu-id=${cardFields.id}>
      <span class="tooltip"> ${documentIcon}  <span class="tooltiptext">Attach documents.</span> </span>
      </div>
      <div class="${ is_viewer ?'display_none' : ''} kanban-add-bom-btn custom-card-shortcuts--add-bom-icon" data-menu-id=${cardFields.id}>
      <span class="tooltip"> <svg version="1.0" xmlns="http://www.w3.org/2000/svg"
      width="24" height="24" viewBox="0 0 20.000000 20.000000"
      preserveAspectRatio="xMidYMid meet">
       <g transform="translate(0.000000,20.000000) scale(0.100000,-0.100000)" stroke="none">
      <path d="M60 175 c0 -16 -6 -25 -15 -25 -9 0 -15 -9 -15 -25 0 -16 -6 -25 -15
      -25 -11 0 -15 -12 -15 -50 l0 -50 50 0 c38 0 50 4 50 15 0 9 9 15 25 15 16 0
      25 6 25 15 0 9 9 15 25 15 24 0 25 2 25 70 l0 70 -70 0 c-68 0 -70 -1 -70 -25z
      m120 -45 c0 -38 -4 -50 -15 -50 -10 0 -15 11 -15 35 0 33 -2 35 -35 35 -24 0
      -35 5 -35 15 0 11 12 15 50 15 l50 0 0 -50z m-50 -40 c0 -29 -4 -40 -15 -40
      -9 0 -15 9 -15 25 0 20 -5 25 -25 25 -16 0 -25 6 -25 15 0 11 11 15 40 15 l40
      0 0 -40z m-50 -40 c0 -27 -3 -30 -30 -30 -27 0 -30 3 -30 30 0 27 3 30 30 30
      27 0 30 -3 30 -30z"/>
      </g> <span class="tooltiptext">Attach BOM.</span> </span>
      </svg> </div>
      <div class="kanban-show-predecessor custom-card-shortcuts--icons">
          <span class="tooltip">  ${predecessorIcon}   <span class="tooltiptext">predecessors.</span> </span>
      </div>
      <div class="kanban-show-successor custom-card-shortcuts--icons">
      <span class="tooltip">${successorIcon}<span class="tooltiptext">successors.</span> </span>
      </div>
      <div class="${ is_viewer ?'display_none' : ''} kanban-add-assignee custom-card-shortcuts--icons" data-menu-id=${cardFields.id}>
      <span class="tooltip">
      <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M720-400v-120H600v-80h120v-120h80v120h120v80H800v120h-80Zm-360-80q-66 0-113-47t-47-113q0-66 47-113t113-47q66 0 113 47t47 113q0 66-47 113t-113 47ZM40-160v-112q0-34 17.5-62.5T104-378q62-31 126-46.5T360-440q66 0 130 15.5T616-378q29 15 46.5 43.5T680-272v112H40Zm80-80h480v-32q0-11-5.5-20T580-306q-54-27-109-40.5T360-360q-56 0-111 13.5T140-306q-9 5-14.5 14t-5.5 20v32Zm240-320q33 0 56.5-23.5T440-640q0-33-23.5-56.5T360-720q-33 0-56.5 23.5T280-640q0 33 23.5 56.5T360-560Zm0-80Zm0 400Z"/></svg>
      <span class="tooltiptext">Add Assignee.</span> </span>
      </div>
      <div class="kanban-comments custom-card-shortcuts--icons" data-menu-id=${cardFields.id}>
      <span class="tooltip">
      <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M280-240q-17 0-28.5-11.5T240-280v-80h520v-360h80q17 0 28.5 11.5T880-680v600L720-240H280ZM80-280v-560q0-17 11.5-28.5T120-880h520q17 0 28.5 11.5T680-840v360q0 17-11.5 28.5T640-440H240L80-280Zm520-240v-280H160v280h440Zm-440 0v-280 280Z"/></svg>
      <span class="tooltiptext">view comments.</span> </span>
      </div>
      </div>
    `;
  }
  
  
  

  const columns = [
    {
      label: 'Todo',
      id: 'todo'
    },
    {
      label: 'In progress',
      id: 'inprogress'
    },
    {
      label: 'Done',
      id: 'done'
    }
  ]

  const rows = [
    {
      label: 'Task',
      id: 'task'
    }
  ]

  const groupData = [
    { id: 'column', label: 'Column', columns },
    {
      id: 'sprint',
      label: 'Sprint',
      columns: [
        { id: '1.0', label: '1.0' },
        { id: '1.1', label: '1.1' },
        { id: '1.2', label: '1.2' }
      ]
    },
    {
      id: 'type',
      label: 'Type',
      columns: [
        { id: 'feature', label: 'Feature' },
        { id: 'task', label: 'Task' }
      ]
    },
    {
      id: 'priority',
      label: 'Priority',
      columns: [
        { id: 1, label: 'High' },
        { id: 2, label: 'Medium' },
        { id: 3, label: 'Low' }
      ]
    }
  ]

  const links = [
    {
      id: 1,
      masterId: 1,
      slaveId: 3,
      relation: 'parent'
    },
    {
      id: 3,
      masterId: 1,
      slaveId: 3,
      relation: 'duplicate'
    },
    {
      id: 4,
      masterId: 2,
      slaveId: 1,
      relation: 'relatesTo'
    }
  ]

  const sort = { // custom sort control
    type: 'sort',
    options: [
      {
        text: 'Label (A-Z)',
        by: 'label',
        dir: 'asc'
      },
      {
        text: 'Label (Z-A)',
        by: 'label',
        dir: 'desc'
      },
      {
        text: 'Start Date',
        by:'start_date'
      }
    ]
  }

  const filter = () => {
    return '<div id="kanban-filter" @click="openFilterDrawer"><svg xmlns="http://www.w3.org/2000/svg" height="20" viewBox="0 -960 960 960" width="20"><path d="M440-160q-17 0-28.5-11.5T400-200v-240L161-745q-14-17-4-36t31-19h584q21 0 31 19t-4 36L560-440v240q0 17-11.5 28.5T520-160h-80Zm40-276 240-304H240l240 304Zm0 0Z"/></svg></div>'
  }

  return {
    rows,
    columns,
    cardShape,
    groupData,
    links,
    filter,
    cardTemplate
  }
}
