<template>
  <div class="file-detail">
    <div v-if="loading" class="fh fw center">
      <loading-circle />
    </div>
    <div v-else class="fh fw">
      <div class="file-detail-header v-center space-between">
        <div>
          <div class="title v-center">
          {{ getSelectedFile.doc_name }} {{ `(version ${getRevisionNumber})` }}
        </div>
        </div>

        <img
          class="close"
          src="~@/assets/images/icons/close-icon.svg"
          @click="$emit('close')"
          alt=""
          width="24px"
        />
      </div>
      <div class="flex">
        <div
          :style="{
            width: openComment ? 'calc(100% - 400px)' : '100%',
          }"
        >
          <div class="file-detail-image-container p-3 h-center" :style="{
            height: fullwindow ? 'calc(100vh - 80px)':'',
          }">
            <div v-if="fileTypes1.includes(getSelectedFile.doc_ext)" style="width: 100%; height: 100%; position: relative;">
            <iframe :src="`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(
                blobKeyURlMapping[
                  getSelectedFile.inherited_from_doc_id
                    ? getSelectedFile.inherited_from_document?.blob_key
                    : getSelectedFile.blob_key
                ]
              )}&embedded=true`" width="100%" height="100%" frameborder="0" scrolling="no" seamless=""></iframe>
            <div @click="viewOnly" v-if="collaborator && view_only || viewOnly2" class="no-icon" style="width: 40px; cursor: pointer; height: 40px; position: absolute; opacity: 0; right: 12px; top: 12px;">&nbsp;</div>
        </div>
            <pdfjs v-else-if="getSelectedFile.doc_ext==='pdf' && pdfUrl" :pdfUrl="pdfUrl" :fileId="getSelectedFile.id" :viewOnly="collaborator && view_only" :state="state" :key="getSelectedFile.id" :zoomValue="zoomValue" @close="$emit('close')" :revisionList="[...revisionList].reverse()" />
            <div class="file-detail-image-container-csvbox" v-else-if="getSelectedFile.doc_ext === 'csv'">
              <table>
                <thead>
                  <tr>
                    <th v-for="(header, index) in csv.headers" :key="index">
                      {{ header }}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(row, rowIndex) in csv.rows" :key="rowIndex">
                    <td v-for="(item, itemIndex) in row" :key="itemIndex">
                      {{ item }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <img
            v-else-if="fileTypes3.includes(getSelectedFile?.doc_ext)"
              :src="
                blobKeyURlMapping[
                  getSelectedFile.inherited_from_doc_id
                    ? getSelectedFile.inherited_from_document?.blob_key
                    : getSelectedFile.blob_key
                ]
              "
              alt=""
            />
            <img
              v-else
              src="~@/assets/images/no_file_icon.svg"
              alt=""
            />
          </div>
          <div>

          </div>
          <div   :style="{
            height: fullwindow ? '0':'',
          }" class="file-detail-corusol">
          <div v-if="fullwindow"></div>
<div v-else class="v-center h-center">
            <div
              v-for="(item, index) in revisionList"
              :key="item.id"
              class="file-detail-corusol--item"
              :class="{ active: index === selectedIndex }"
              @click="selectedIndex = index"
            >
              <div class="file-detail-corusol--item-img">
                <img
                  v-if="
                    blobKeyThumbURlMapping[
                      item?.inherited_from_doc_id
                        ? item?.inherited_from_document?.thumbnail_blob_key
                        : item.thumbnail_blob_key
                    ]
                  "
                  :src="
                    blobKeyThumbURlMapping[
                      item?.inherited_from_doc_id
                        ? item?.inherited_from_document?.thumbnail_blob_key
                        : item.thumbnail_blob_key
                    ]
                  "
                  alt=""
                  height="100%"
                />
                <img
                  v-else
                  src="~@/assets/images/no_file_icon.svg"
                  alt=""
                  height="100%"
                  />
              </div>
              <div class="file-detail-corusol--item-title elipsis-text">
                {{ item.doc_name }}
              </div>
              <div class="file-detail-corusol--item-date elipsis-text">
                {{ item.created_on | duration }}
              </div>
            </div>
            </div>

          </div>
        </div>
        <div
          class="file-detail-comment"
          :style="{
            width: openComment ? '400px' : '0px',
          }"
        >
          <comment-panel
            v-if="openComment"
            :versionDocId="getRootFile"
            :document="getSelectedFile"
            @closeComment="openComment = false"
          />
        </div>
      </div>
    </div>
    <div class="comment-btn-box">
      <button  v-if="getSelectedFile.doc_ext === 'pdf'" class="commet-btn" @click="zoomValue=zoomValue + 0.2">
        <img src="~@/assets/images/icons/annotation/zoomin.svg" alt="" />
      </button>
      <button  v-if="getSelectedFile.doc_ext === 'pdf'" class="commet-btn" @click="doZoomOut">
        <img src="~@/assets/images/icons/annotation/zoomout.svg" alt="" />
      </button>
      <button class="commet-btn" @click="fullwindow=!fullwindow">
        <img src="~@/assets/images/icons/annotation/expandorminimize.svg" alt="" />
      </button>
      <button class="commet-btn" v-if="!openComment" @click="openComment = true">
      <img src="~@/assets/images/icons/comment-icon.svg" alt="" />
    </button>
    </div>
  </div>
</template>

<script>
import pdfjs from './pdfjs.vue'
// import pdfjs from './annotationLayer.vue'
import { getAllRevisionDocuments, generateS3DownloadingUrl } from '@/api'
import { mapGetters } from 'vuex'
import loadingCircle from '../common/loadingCircle.vue'
import { Duration } from '@/utils/date'
import CommentPanel from './commentPanel.vue'
import { warning } from '@/plugins/notification'
import { parseCSV } from '@/utils/dataHandler'
export default {
  components: { loadingCircle, CommentPanel, pdfjs },
  name: 'fileDetail',
  filters: {
    duration (value) {
      return Duration(value)
    }
  },
  props: {
    view_only: {
      type: Boolean
    },
    fileId: {
      type: [String, Number],
      default: ''
    },
    state: {
      type: [String, Number],
      default: null
    }
  },
  data () {
    return {
      view: false,
      encodeURIComponent: encodeURIComponent,
      revisionList: [],
      loading: false,
      blobKeyURlMapping: {},
      selectedIndex: 0,
      openComment: false,
      fileTypes1: ['odt', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
      fileTypes2: ['pdf', 'html', 'gif', 'mp4', 'webm', 'ogg', 'mp3', 'wav'],
      fileTypes3: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp', 'svg', 'ico'],
      blobKeyThumbURlMapping: {},
      csv: {
        headers: [],
        rows: []
      },
      pdfUrl: null,
      fullwindow: true,
      zoomValue: 1
    }
  },
  computed: {
    ...mapGetters(['collaborator', 'isOnProjectLevel', 'isProjectViewer', 'isTenantViewer']),
    getRootFile () {
      return this.revisionList.find((item) => item.version_of === null)
    },
    getRevisionNumber () {
      // Compute revision number based on the index and length of revisionList
      return this.revisionList.length - this.selectedIndex
    },
    getSelectedFile () {
      if (this.revisionList[this.selectedIndex].doc_ext === 'csv') {
        this.loadCSV(this.revisionList[this.selectedIndex])
      } else if (this.revisionList[this.selectedIndex].doc_ext === 'pdf') {
        this.loadPDF(this.revisionList[this.selectedIndex])
      }
      return this.revisionList[this.selectedIndex]
    },
    viewOnly2 () {
      if (this.isOnProjectLevel) {
        return this.view_only || this.isProjectViewer
      } else {
        return this.view_only || this.isTenantViewer
      }
    }
  },
  methods: {
    viewOnly () {
      warning('This file is only for viewing!')
    },
    getRevisionList () {
      this.loading = true
      getAllRevisionDocuments(this.fileId)
        .then((res) => {
          this.revisionList = res.core_documents
          this.loadS3Url(res.core_documents)
        })
        .finally(() => {
          this.loading = false
        })
    },
    loadS3Url (items) {
      const S3Objects = items.map((item) => {
        return {
          fileName: encodeURIComponent(item?.inherited_from_doc_id
            ? item?.inherited_from_document?.doc_name
            : item.doc_name),
          S3Key: item?.inherited_from_doc_id
            ? item?.inherited_from_document?.blob_key
            : item.blob_key
        }
      })
      const S3ThumbNailObjects = []
      for (const item of items) {
        // some times thumbnail blob key comes as null , which will lead to the failure of thumbanil url fecth
        if (item.thumbnail_blob_key || item?.inherited_from_document?.thumbnail_blob_key) {
          S3ThumbNailObjects.push({
            fileName: encodeURIComponent(item?.inherited_from_doc_id
              ? item?.inherited_from_document?.doc_name
              : item.doc_name),
            S3Key: item?.inherited_from_doc_id
              ? item?.inherited_from_document?.thumbnail_blob_key
              : item.thumbnail_blob_key
          })
        }
      }
      generateS3DownloadingUrl({
        S3Objects
      }).then((res) => {
        const obj = {}
        res.url.forEach((item) => {
          obj[
            item?.inherited_from_doc_id
              ? item?.inherited_from_doc_id.S3Key
              : item.S3Key
          ] = item.url
        })
        this.blobKeyURlMapping = obj
      })
      generateS3DownloadingUrl({
        S3Objects: S3ThumbNailObjects
      }).then((res) => {
        const obj = {}
        res.url.forEach((item) => {
          obj[
            item?.inherited_from_doc_id
              ? item?.inherited_from_doc_id.S3Key
              : item.S3Key
          ] = item.url
        })
        this.blobKeyThumbURlMapping = obj
      })
    },
    selectRevision (index) {
      this.selectedIndex = index
    },
    async loadCSV (selectedFile) {
      const url =
        this.blobKeyURlMapping[
          selectedFile.inherited_from_doc_id
            ? selectedFile.inherited_from_document?.blob_key
            : selectedFile.blob_key
        ]
      if (!url) {
        return
      }
      try {
        const response = await fetch(url)
        const data = await response.text()
        this.handleCSV(data)
      } catch (error) {
        console.error('Error loading CSV:', error)
      }
    },
    handleCSV (csvData) {
      const { headers, rows } = parseCSV(csvData)
      this.csv.headers = headers
      this.csv.rows = rows
    },
    async loadPDF (selectedFile) {
      const url =
        this.blobKeyURlMapping[
          selectedFile.inherited_from_doc_id
            ? selectedFile.inherited_from_document?.blob_key
            : selectedFile.blob_key
        ]
      if (!url) {
        return
      }
      try {
        const response = await fetch(url)
        const data = await response.blob()
        this.pdfUrl = URL.createObjectURL(data.slice(0, data.size, 'application/pdf'))
      } catch (error) {
        console.error('Error loading CSV:', error)
      }
    },
    doZoomOut () {
      this.zoomValue = this.zoomValue <= 0.01 ? this.zoomValue : this.zoomValue - 0.2
    },
    keyPress (e) {
      if (e instanceof KeyboardEvent && e.code === 'Escape') {
        this.$emit('close')
      }
    }
  },
  // watch: {
  // },
  created () {
    this.getRevisionList()
    document.body.addEventListener('keydown', this.keyPress)
  },
  destroyed () {
    document.body.removeEventListener('keydown', this.keyPress)
  }
}
</script>

<style lang="scss" scoped>
.file-detail {
  position: fixed;
  bottom: 0;
  right: 0;
  top: 0;
  left: 0;
  z-index: 100;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(2px);
  animation: slideIn 0.3s ease-in-out;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }
    100% {
      transform: translateX(0);
    }
  }
  &-header {
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0 12px;
    .close {
      cursor: pointer;
      &:hover {
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
      }
    }
    .title {
      font-size: 18px;
      font-weight: 500;
      color: var(--white);
    }
  }
  &-image-container {
    width: 100%;
    height: calc(100vh - 80px - 22vh);
    border-radius: 8px;
    transition: height 1s ease ;
    img {
      max-height: 100%;
      max-width: 100%;
      object-fit: contain;
    }
   &-csvbox{
height:100%;
background-color: rgb(255, 255, 255);
width: 100%;
overflow: auto;
padding: 5px;
table{
  border-collapse: collapse;
}
 th,td {
  padding: 5px;
  border: 1px solid;
}
    }
  }
  &-corusol {
    border-radius: 8px;
    width: calc(100% - 100px);
    padding: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow-x: auto;
    &::-webkit-scrollbar {
      height: 5px;
    }
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    &::-webkit-scrollbar-thumb {
      background: var(--brand-color);
    }
    &::-webkit-scrollbar-thumb:hover {
      background: var(--brand-color-1);
    }
    &--item {
      height: 20vh;
      min-width: 18vh;
      max-width: 18vh;
      border-radius: 1vh;
      margin-right: 1.5vh;
      cursor: pointer;
      position: relative;
      transition: transform 0.3s ease-in-out;
      &:hover {
        .file-detail-corusol--item-title,
        .file-detail-corusol--item-date {
          display: block;
        }
      }
      &.active {
        border: 2px solid var(--brand-color);
        transform: scale(1.1);
      }
      &-img {
        height: 100%;
        width: 100%;
        border-radius: 1vh;
        overflow: hidden;
        img {
          height: 100%;
          width: 100%;
          object-fit: cover;
        }
      }
      &-title {
        position: absolute;
        display: none;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 4px 8px;
        background-color: rgba(0, 0, 0, 0.5);
        color: var(--white);
        font-size: 14px;
        font-weight: 500;
        border-bottom-left-radius: 1vh;
        border-bottom-right-radius: 1vh;
      }
      &-date {
        position: absolute;
        display: none;
        top: 0;
        left: 0;
        right: 0;
        padding: 4px 8px;
        background-color: rgba(0, 0, 0, 0.5);
        color: var(--white);
        font-size: 14px;
        font-weight: 500;
        border-top-left-radius: 1vh;
        border-top-right-radius: 1vh;
      }
    }
  }
  &-comment {
    transition: width 0.3s ease-in-out;
    border-radius: 4px;
    height: 90vh;
    background-color: var(--white);
  }
  .comment-btn-box {
    padding-inline: 5px;
    position: absolute;
    bottom: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    & .commet-btn{
    margin-block: 5px;
    background: var(--white);
    cursor: pointer;
    border-radius: 50%;
    height: 35px;
    width: 35px;
    border: none;
    box-shadow: 4px 4px 4px 0 rgba(0, 0, 0, 0.25);
    & img {
      width: 20px;
      height: 20px;
      color: var(--brand-color);
    }
    &:hover {
      background: var(--brand-color);
      & img {
        filter: invert(1);
      }
    }
    &:active {
      box-shadow: inset 4px 4px 4px 0 rgba(0, 0, 0, 0.25);
    }
  }
  }
}
.file-detail-corusol{
  transition: height 1s ease ;
}
</style>
