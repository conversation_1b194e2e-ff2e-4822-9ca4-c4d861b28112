import { router } from '@/router'
import { GetAllChildProductCodeByBomVersion } from '@/api'

const clone = (obj) => {
  return JSON.parse(JSON.stringify(obj))
}

const getProductCode = () => {
  const { params } = router.currentRoute
  return params.productCode ? +params.productCode : null
}

const getBomId = () => {
  const { params } = router.currentRoute
  return params.bomId ? +params.bomId : null
}

const getBomVersionId = () => {
  const { query } = router.currentRoute
  return query.bomVersionId ? +query.bomVersionId : null
}

const getTreeElement = (cid, list) => {
  for (let i = 0; i < list.length; i++) {
    if (list[i].cid === cid) {
      return list[i]
    } else if (list[i].childrens.length) {
      const result = getTreeElement(cid, list[i].childrens)
      if (result) {
        return result
      }
    }
  }
}

export const initProductTree = (productTreeData) => {
  const productTree = {
    items: {},
    tree: []
  }
  const pc = getProductCode()
  productTreeData.forEach((item, index) => {
    const cid = `cid-${index}`
    productTree.items[cid] = {
      product_code_name: item.product_code_name,
      product_code: item.product_code,
      selected: item.product_code === pc,
      active: item.active,
      canExpand: item.product_code === pc && getBomId(),
      path: `/bom/product/${item.product_code}`,
      childrens: {},
      open: false,
      loading: false,
      associatedBomId: null,
      associatedBomName: null,
      associatedBomVersion: null,
      associatedBomVersionId: null,
      cid,
      parent: null
    }
    productTree.tree.push({
      cid,
      childrens: []
    })
  })
  return productTree
}

export const bomVersionIdChanged = (productTree) => {
  const bomId = getBomId()
  const pc = getProductCode()
  Object.keys(productTree.items).forEach((key) => {
    const node = productTree.items[key]
    node.canExpand = !!node.associatedBomId || (node.product_code === pc && bomId)
  })
  return clone(productTree)
}

export const goto = (cid, productTree) => {
  const node = productTree.items[cid]
  // deselect all
  Object.keys(productTree.items).forEach((key) => {
    productTree.items[key].selected = false
    if (node.parent === null) {
      productTree.items[key].open = false
    }
  })
  node.selected = true
  router.push({
    path: node.path,
    query: {
      cid
    }
  })
  return [productTree, node]
}

export const expand = (cid, productTree, cb) => {
  const node = productTree.items[cid]
  if (!node.canExpand) {
    return productTree
  }
  if (node.open) {
    node.open = false
    return productTree
  }
  node.open = true
  node.loading = true
  const treeElement = getTreeElement(cid, productTree.tree)
  const bomVersionId = node.associatedBomVersionId || getBomVersionId()
  GetAllChildProductCodeByBomVersion(bomVersionId).then((res) => {
    const item = res.bom_items
    const childrens = []

    item.forEach((item, index) => {
      const cid = `${node.cid}-${index}`
      const obj = {
        product_code_name: item.core_material?.material_product_code?.product_code,
        product_code: item.core_material?.material_product_code?.id,
        selected: false,
        canExpand: item.associated_bom_version?.id,
        path: '',
        open: false,
        loading: false,
        associatedBomId: item.associated_bom_version?.core_bom?.id || null,
        associatedBomName: item.associated_bom_version?.core_bom?.name || null,
        associatedBomVersion: item.associated_bom_version?.version_no || null,
        associatedBomVersionId: item.associated_bom_version?.id || null,
        cid,
        parent: node.cid
      }
      if (obj.associatedBomId && obj.associatedBomVersionId) {
        obj.path = `/bom/product/${obj.product_code}/bom/${obj.associatedBomId}?bomVersionId=${obj.associatedBomVersionId}`
      } else {
        obj.path = `/bom/product/${obj.product_code}`
      }
      productTree.items[cid] = obj
      childrens.push({
        cid,
        childrens: []
      })
    })
    treeElement.childrens = childrens
    node.loading = false
    cb && cb(clone(productTree))
  })
  return clone(productTree)
}
