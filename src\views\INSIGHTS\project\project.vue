<template>
  <div>
    <div class="insights fh p-3">
      <!--container  -->
      <div class="insights-container">
        <!-- loop start here  -->
        <div class="heading mt-5">
          <h1>{{ dashBoardData[0]?.name }}</h1>
        </div>
        <div class="insights-container-single_project " v-for="(project, index) in dashBoardData" :key="index">
          <div class="insights-container-projectbox-1 v-center h-center">
            <div class="grid-2x2 insights-container-projectbox-1-box1">
              <div class="v-center h-center column insights-container-databox">
                <p class="mb-2">Project Value</p>
                <h3>₹ {{ project.cost }}</h3>
              </div>
              <div class="v-center h-center column insights-container-databox">
                <p class="mb-2" >Earned Value</p>
                <h3>₹ {{ (project.progress * project.cost) / 100 }}</h3>
              </div>
              <div class="v-center h-center column insights-container-databox">
                <p class="mb-2">Project Duration</p>
                <h3 class="mb-2">{{ project.duration }}{{ project.duration===1?' Day':' Days'}}</h3>
                <p>
                  {{ project.formattedStartDate }} to
                  {{ project.formattedEndDate }}
                </p>
              </div>
              <div class="v-center h-center column insights-container-databox">
                <p class="mb-2" >Elapsed Time</p>
                <h3>{{ project.elapsedTime }} {{ project.elapsedTime===1?' Day':' Days'}}</h3>
              </div>
            </div>
          </div>
          <div class="insights-container-projectbox-2 v-center h-center">
            <div class="grid-3x2">
              <!-- Box 1 -->
              <div class="v-center h-center column insights-container-databox">
                <progressbar :percentage="project.progress" :value="project.progress">
                </progressbar>
                <p>Progress</p>
              </div>

              <!-- Box 2 -->
              <div class="v-center h-center column insights-container-databox">
                <progressbar :percentage="(project.completedMilestones / project.totalMileStones) *
            100
            " :value="project.completedMilestones">
                </progressbar>
                <p>Of {{ project.totalMileStones }} milestones</p>
              </div>

              <!-- Box 3 -->
              <div class="v-center h-center column insights-container-databox">
                <div class="container2">
                <formsPieChartVue :active-projects="taskData" :label="'Tasks'" @goInside="goToKanban" />
                </div>
              </div>
              <!-- Box 4 -->
              <div class="v-center h-center column insights-container-databox">
                <p class="mb-2" >SPI value</p>
                <h3>{{ project.spi }}</h3>
              </div>

              <!-- Box 5 -->
              <div class="v-center h-center column insights-container-databox p-3">
                <div v-if="!formTemplateActive.length">
                  <label>No Active Forms</label>
                </div>
                <div v-else class="v-center h-center column insights">
                  <formsPieChartVue :active-projects="formTemplateActive" :label="'Total Active Forms'"
                    @goInside="goToSelectedView" />
                </div>
              </div>

              <!-- Box 6 -->
              <div class="v-center h-center column insights-container-databox">
                <div v-if="!formTemplateOverDue.length">
                  <label>No Over Due Dates</label>
                </div>
                <div v-else class="container2" >
                    <!-- <div class=""> -->
                  <formsPieChartVue :active-projects="formTemplateOverDue" :label="'Total Over Due Forms'"
                    @goInside="goToSelectedView" />
                    <!-- </div> -->
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- loop end here  -->

        <div class="milestone  box-shadow my-5">
          <h2 class="my-3 pt-5">Milestone Timeline</h2>
          <milestone-time-line-view :usersData="{
            1: {
              key: this.currentProject.id,
              label: this.currentProject.name,
            },
          }" :timeLineData="timeLineData" />
        </div>
      </div>
      <section>
        <div class="insights-container-graphbox">
          <div class="box-shadow">
          <burnout-chart :graphData="[plannedBurnOut, actualBurnOut]" :chartType="'area'"
            @timelineUpdate="timelineUpdateBurnOut" @expand="expand='actualvsplan'" :label="'Burn Up Chart'" :showFilter="true" />
        </div>
        <div class="box-shadow">
          <burnout-chart :graphData="[plannedBudgetOverview, actualBudgetOverview]" :chartType="'area'"
            @timelineUpdate="timelineUpdateBudget" :label="'Budget Overview'" :showFilter="true" />
          </div>
        </div>
      </section>
      <section>
        <div class="insights-container-graphbox mt-5 mb-5">
          <div :class="{'box-shadow':true,
                    'expanded': expanded==='actualvsplan'
                  }">
          <label v-if="expanded === ''" class="texts" for="">Resource Production Actual Vs Planned</label>
          <div class="flex extend">
          <multiselect-dropdown
          label="Resource"
          :options="resources"
          @selected="setResourceFilters"
          :initially-selected="filter.resourceIds"
          />
          <multiselect-dropdown
          class="ml-3 mr-3"
          label="Resource Group"
          :options="list_of_resource_group"
          @selected="setResourceGroupFilter"
          :initially-selected="filter.ResourceGroup"
          />
          <div class="tags">
            <single-select-drop-down
              class=""
              label="Tags"
              :options="tags"
              :current="filter.tags"
              @selected="setTagFilters"
            />
          </div>
          <button class="btn btn-black pointer ml-3" @click="clearAll" >Clear all</button>
          <button class="btn ml-3" @click="applyFilters" >
            Apply
          </button>
        </div>
          <category-wise-chart
          :key="materialNameAxis[0]"
          v-if="materialNameAxis.length > 0"
          :graphSeries="resourceSeries"
          :xaxisCategories="materialNameAxis"
          :curve="'smooth'"
          :marker="false"
          :chartType="'bar'"
          :label="null"
          :type="'horizontal'"
          @expand="expanded='actualvsplan'"
          @collapse="expanded=''"
        />
        <label v-else class="no-data-label" for="">No Data Available</label>
      </div>
        <div class="box-shadow">
        <div>
          <div class="v-center h-center column insights-container-graphbox">
            <label class="texts" for="">Resource Status</label>
                <formsPieChartVue v-if="resourceStateData.length > 0 && !empty" :active-projects="resourceStateData" :label="null"
                    @goInside="goToSelectedView" />
                    <label v-else class="no-data-label" for="">No Data Available</label>
              </div>
        </div>
        </div>

      </div>
    </section>
    </div>
  </div>
</template>

<script>
import progressbar from '@/components/common/progressbarCircle.vue'
import multiselectDropdown from '@/components/common/multiselectDropdown.vue'
import singleSelectDropDown from '@/components/common/singleSelectDropDown.vue'

import {
  // getinsightsDataApi,
  getDashBoardFormData,
  getParentLevelTasks,
  GetChildrenTasks,
  getProjectUserExceptViewer,
  getinsightsDataApiForProject,
  getBurnoutchartDataApi,
  getbudgetOverViewChartApi,
  getTaskStatusAgg,
  getResourceChartData,
  getResourceActualvsPlanApi,
  GetProjectAssocResources,
  GetAllResourceTags,
  ResourceGroup,
  getAllTasks,
  getAllCalendarData
} from '@/api'
import { getDuration, getDateAgo } from '../insightsHelper.js'
import { mapGetters } from 'vuex'
import Loader from '@/plugins/loader'
import formsPieChartVue from '@/components/common/charts/formsPieChart.vue'
import burnoutChart from '@/components/insights/burnoutChart.vue'
import MilestoneTimeLineView from '../../../components/insights/milestones/milestoneTimelineView'
import CategoryWiseChart from '@/components/common/charts/categorywisechart.vue'
import { getGanttTypeFromString } from '@/helper/gantt/getGanttType.js'
import { alert } from '@/plugins/notification'

export default {
  name: 'insights',
  components: {
    singleSelectDropDown,
    burnoutChart,
    progressbar,
    formsPieChartVue,
    MilestoneTimeLineView,
    CategoryWiseChart,
    multiselectDropdown
  },
  data: function () {
    return {
      list_of_resource_group: [],
      empty: false,
      empty_2: 0,
      filter: {
        resourceIds: [],
        tags: {},
        ResourceGroup: []
      },
      appliedFilters: {
        resourceIds: [],
        tags: {},
        ResourceGroup: []
      },
      timeLineData: [],
      resourceData: [],
      usersData: {},
      tags: [],
      dashBoardData: [],
      projectIds: [],
      formTemplateActive: [], // Example data, you should replace these with actual data
      formTemplateOverDue: [],

      plannedBurnOut: { data: [] },
      actualBurnOut: { data: [] },
      plannedBudgetOverview: { data: [] },
      actualBudgetOverview: { data: [] },
      taskData: [],
      loading: false,
      materialNameAxis: [], // frr resource output graph,
      resourceSeries: [],
      resourceStateData: [],
      resource: false,
      resources: [],
      expanded: ''
    }
  },
  computed: {
    ...mapGetters(['user', 'adminProjetcts', 'currentProject']),
    showGraph () {
      return (
        this.actualBurnOut.data.length > 0 &&
        this.plannedBurnOut.data.length > 0
      )
    }
  },
  methods: {
    listOfMaterialGroup () {
      ResourceGroup('Resource Group').then((res) => {
        this.list_of_resource_group = res.custom_list_values.map((item) => {
          return {
            label: item.name,
            value: item.id
          }
        })
      })
    },
    getTaskTags () {
      GetAllResourceTags().then(res => {
        res.tag.forEach((item) => {
          this.tags.push({
            id: item?.id,
            name: item?.name
          })
        })
      })
    },
    applyFilters () {
      this.appliedFilters = this.filter
      this.getResourceActualvsPlan()
    },
    clearAll () {
      this.filter.resourceIds = []
      this.filter.tags = {}
      this.filter.ResourceGroup = []
      this.applyFilters()
    },
    setResourceFilters (resourceIds) {
      this.filter.resourceIds = resourceIds
    },
    setTagFilters (tags) {
      this.filter.tags = tags
    },
    setResourceGroupFilter (resourceGroup) {
      this.filter.ResourceGroup = resourceGroup
    },
    async getTaskAggData () {
      const data = await getTaskStatusAgg()
      this.taskData = data?.message?.map((task) => {
        return {
          count: task.count || 0,
          template_name: task.status
        }
      })
    },
    goToKanban () {
      this.$router.push('/project-planner?view=board')
    },
    goToSelectedView (path) {
      this.$router.push(path)
    },
    goToEditMode (data) {
      this.currentDate = new Date(data.date)
      this.viewModeParent = data.viewMode
      this.editMode = true
    },
    async getinsightsData () {
      const loader = new Loader()
      loader.show()
      try {
        const projectId = localStorage.getItem('projectId')
        this.resource = true
        getResourceChartData(projectId).then(res => {
          res.custom_list_values.forEach(value => {
            this.resourceStateData.push({
              template_name: value.name,
              count: value.core_material_master_by_resource_state_aggregate.aggregate.count
            })
          })
          this.empty = this.resourceStateData.every(item => item.count === 0)
        })
        this.getResourceList()
        this.listOfMaterialGroup()
        this.getTaskAggData()
        this.getTaskTags()
        this.usersData = {
          1: { key: this.currentProject.id, label: this.currentProject.name }
        }
        getParentLevelTasks().then((res) => {
          GetChildrenTasks(res.core_tasks[0].id).then((res) => {
            for (const task of res.core_tasks) {
              this.timeLineData.push({
                start_date: task.planned_start_date,
                end_date: task.planned_end_date,
                text: task?.name,
                section_id: this.currentProject.id,
                project_name: this.currentProject.name,
                projectId: this.currentProject.id,
                taskId: task.id,
                readonly: true,
                color: 'red'
              })
            }
          })
        })
        const associatedProjects = await getProjectUserExceptViewer(
          this.user.userId
        )
        this.projectIds = associatedProjects?.project_user_association.map(
          (obj) => {
            return obj.associated_project.id
          }
        )
        // const projectId = parseInt(localStorage.getItem('projectId'))
        const formData = await getDashBoardFormData(projectId, true)
        const formDataMapOverDue = {}
        const formDataMapActive = {}
        for (const overDue of formData.message.overdue) {
          if (Object.hasOwn(formDataMapOverDue, overDue.project_name)) {
            formDataMapOverDue[overDue.project_name] += parseInt(
              overDue?.count
            )
            this.formTemplateOverDue.push(overDue)
          } else {
            formDataMapOverDue[overDue.project_name] = parseInt(overDue?.count)
            this.formTemplateOverDue.push(overDue)
          }
        }
        for (const active of formData.message.active) {
          if (Object.hasOwn(formDataMapOverDue, active?.project_name)) {
            formDataMapActive[active.project_name] += parseInt(active.count)
            this.formTemplateActive.push(active)
          } else {
            formDataMapActive[active.project_name] = parseInt(active.count)
            this.formTemplateActive.push(active)
          }
        }
        const tenantId = localStorage.getItem('tenantId')
        getinsightsDataApiForProject(tenantId, projectId, this.user.userId)
          .then((res) => {
            this.dashBoardData = []
            for (const project of res.core_tasks) {
              const projectData = { ...project }
              projectData.duration = getDuration(
                project.planned_start_date,
                project.planned_end_date
              )
              projectData.elapsedTime = getDuration(
                project.planned_start_date,
                (parseInt(project.progress) < 100 || project.progress === null) ? new Date() : project.planned_end_date
              )
              projectData.formattedStartDate = new Date(
                project.planned_start_date
              )
                .toLocaleDateString('en-US')
                .replaceAll('/', '-')
              projectData.formattedEndDate = new Date(project.planned_end_date)
                .toLocaleDateString('en-US')
                .replaceAll('/', '-')
              projectData.totalMileStones =
                project?.core_project?.total_milestones?.aggregate?.count || 0
              projectData.totalTasks =
                project?.core_project?.total_tasks?.aggregate?.count || 0
              projectData.completedMilestones =
                project?.core_project?.completed_milestones?.aggregate?.count ||
                0
              projectData.completedTasks =
                project?.core_project?.completed_tasks?.aggregate?.count || 0
              projectData.overDueForms = formDataMapOverDue[project.name] ?? 0
              projectData.activeForms = formDataMapActive[project.name] ?? 0
              this.dashBoardData.push(projectData)
            }
            loader.hide()
          })
          .catch((err) => {
            console.log(err)
            loader.hide()
          })
      } catch (error) {
        loader.hide()
      }
    },
    getResourceList () {
      const projectId = localStorage.getItem('projectId')
      GetProjectAssocResources([projectId]).then((res) => {
        this.resources = res.core_material_master.map((resource) => {
          return {
            label: resource.material_name,
            value: resource.id
          }
        })
      }).catch(() => {
        alert('Unable to fetch')
      })
    },
    getBurnoutchartDataActual (duration = 'year') {
      const startDate = getDateAgo(duration)
      const currentDate = new Date().toLocaleDateString('en-US')
      // project ids should be passed as an array
      getBurnoutchartDataApi(
        [this.currentProject.id],
        startDate,
        currentDate
      ).then((res) => {
        if (res.message.actual) {
          for (const chartData of res.message.actual) {
            this.actualBurnOut.data.push([
              new Date(chartData.date).getTime(),
              chartData.cumulative_duration
            ])
          }
        }
        this.actualBurnOut.name = 'Actual'
        this.plannedBurnOut.name = 'Planned'
      })
    },
    async fetchBurnoutChartDataPlanned (durationFilter) {
      try {
        const startDate = getDateAgo(durationFilter)
        const endDate = new Date().toLocaleDateString('en-US')

        // Fetch task and calendar data
        const [{ core_tasks: taskData }, { core_project_calendar: [calendarData] }] = await Promise.all([getAllTasks({ startDate, endDate, projectId: this.currentProject.id, type: getGanttTypeFromString('task') }),
          getAllCalendarData(this.currentProject.id)])

        const plannedDateDurationMap = new Map() // Map to store cumulative durations by date
        const perDayWorkingHours = calendarData.working_hours
        const holidays = new Set(
          (calendarData.calendar_holidays || []).map(holiday => holiday.date)
        )
        const workingDays = new Set(
          calendarData?.calendar_working_days
            .filter(day => !day.deleted)
            .map(day => day.work_day)
        )

        // Helper to check if a date is a valid working day
        const isWorkingDay = date => {
          const dayIndex = date.getUTCDay()
          const dateStr = date.toISOString().split('T')[0]
          return workingDays.has(dayIndex) && !holidays.has(dateStr) // Exclude holidays
        }

        // Process each task
        for (const task of taskData) {
          const plannedStartDate = new Date(task.planned_start_date)
          const plannedEndDate = new Date(task.planned_end_date)

          // Distribute the task duration proportionally across valid working days
          let currentDate = new Date(plannedStartDate) // Initialize currentDate
          let workDoneTillDate = 0
          while (currentDate <= plannedEndDate) {
            if (isWorkingDay(currentDate)) {
              const formattedDate = currentDate.toISOString().split('T')[0]
              const currentDuration = plannedDateDurationMap.get(formattedDate) || 0
              let workingHoursForTheDay = task.duration < perDayWorkingHours ? task.duration : perDayWorkingHours
              if (workingHoursForTheDay + workDoneTillDate > task.duration) {
                workingHoursForTheDay = task.duration - workDoneTillDate
              }
              workDoneTillDate += workingHoursForTheDay
              plannedDateDurationMap.set(
                formattedDate,
                Math.round(currentDuration + workingHoursForTheDay)
              )
            }
            // Move to the next day
            currentDate = new Date(currentDate.setUTCDate(currentDate.getUTCDate() + 1))
          }
        }
        // sorting the keys
        const dateKeys = [...plannedDateDurationMap.keys()].sort()

        for (let i = 0; i < dateKeys.length; i++) {
          let duration = plannedDateDurationMap.get(dateKeys[i])
          if (i !== 0) {
            duration += plannedDateDurationMap.get(dateKeys[i - 1])
            plannedDateDurationMap.set(dateKeys[i], duration)
          }
          this.plannedBurnOut.data.push([
            new Date(dateKeys[i]).getTime(), // Convert date to timestamp
            duration
          ])
        }
      } catch (error) {
        console.error('Error fetching burnout chart data:', error)
      }
    },

    timelineUpdateBurnOut (timeLine) {
      this.actualBurnOut.data = []
      this.plannedBurnOut.data = []
      this.getBurnoutchartDataActual(timeLine)
      this.fetchBurnoutChartDataPlanned(timeLine)
    },
    timelineUpdateBudget (timeLine) {
      this.plannedBudgetOverview.data = []
      this.actualBudgetOverview.data = []
      this.getbudgetOverViewChart(timeLine)
    },
    getbudgetOverViewChart (duration = 'year') {
      const startDate = getDateAgo(duration)
      const currentDate = new Date().toLocaleDateString('en-US')
      // project ids should be passed as an array
      getbudgetOverViewChartApi(
        [this.currentProject.id],
        startDate,
        currentDate
      ).then((res) => {
        for (const chartData of res.message.planned) {
          this.plannedBudgetOverview.data.push([
            new Date(chartData.date).getTime(),
            chartData.cumulative_cost
          ])
        }
        for (const chartData of res.message.actual) {
          this.actualBudgetOverview.data.push([
            new Date(chartData.date).getTime(),
            chartData.cumulative_cost
          ])
        }
        this.actualBudgetOverview.name = 'Actual'
        this.plannedBudgetOverview.name = 'Planned'
      })
    },
    getResourceActualvsPlan () {
      getResourceActualvsPlanApi(this.appliedFilters).then((res) => {
        const categoryArray = []
        this.materialNameAxis = []
        this.resourceSeries = []
        res.message.actual.forEach((rsrc) => {
          categoryArray.push(rsrc.custom_material_id)
        })
        res.message.planned.forEach((rsrc) => {
          const index = categoryArray.findIndex((material) => material === rsrc.custom_material_id)
          if (index === -1) {
            categoryArray.push(rsrc.custom_material_id)
          }
        })
        const actualResource = { data: new Array(categoryArray.length).fill(0), name: 'Actual' }
        const plannedResource = { data: new Array(categoryArray.length).fill(0), name: 'Planned' }
        res.message.actual.forEach((rsrc) => {
          const index = categoryArray.findIndex((material) => material === rsrc.custom_material_id)
          if (index !== -1) {
            actualResource.data[index] = Number(rsrc.quantity).toFixed(2)
          }
        })
        res.message.planned.forEach((rsrc) => {
          const index = categoryArray.findIndex((material) => material === rsrc.custom_material_id)
          if (index !== -1) {
            plannedResource.data[index] = Number(rsrc.sum).toFixed(2)
          }
        })
        this.materialNameAxis = categoryArray
        this.resourceSeries = [plannedResource, actualResource]
      })
    },
    goInside (path) {
      this.$router.push(path)
    }
  },
  mounted () {
    try {
      this.getinsightsData()
    } catch (err) {
      console.log(err, 'this is the error')
    }
    this.getBurnoutchartDataActual()
    this.getbudgetOverViewChart()
    this.getResourceActualvsPlan()
    this.fetchBurnoutChartDataPlanned()
  }
}
</script>

<style lang="scss" scoped>
.tags {
  width: 90%;
}
.no-data-label {
  display: flex; // Ensures the text stays centered
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: large;
  font-weight: bolder;
  width: 100%;
  padding: 1rem;
  min-height: 550px; // Added to give enough space to the label
  // background-color: rgba(169, 169, 169, 0.2);
  border-radius: 8px;
  word-wrap: break-word;
}
.container2 {
  width: 100%;
  height: 100%;
}
.insights {
  overflow: hidden;
  max-width: 100%;

  &-bar {
    height: 60px;
    margin: -12px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }

  &-container {
    height: 90%;
    max-width: 100%;

    // overflow-y: auto;
    // padding: 12px;
    .milestone {
      overflow-y: scroll;
      animation: myAnim 0.6s ease-in 0s 1 normal forwards;
      padding-bottom: 10px;
    }

    .heading {
      padding-left: 30px;
    }

    &-single_project {
      display: grid;
      grid-template-columns: 40% 60%;
      // grid-column-gap: 12px;
      grid-gap: 20px;
      margin-top: 1rem;
      height: 90%;
    }

    &-projectbox-1 {
      // border: 1px groove rgb(194, 188, 188, 0.4);
      // border-radius: 6px;
      height: 100%;
      animation: myAnim 0.6s ease-in 0s 1 normal forwards;
      font-size: 1.3rem;

      .grid-2x2 {
        display: grid;
        grid-template-rows: repeat(2, 1fr);
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 0; // Remove the gap

        .insights-container-databox:nth-child(2),
        .insights-container-databox:nth-child(3) {
          background-color: rgba(169,
              169,
              169,
              0.2); // Apply grey background to box 2 and 3
        }
      }

      &-box1 {
        width: 100%;
        height: 100%;
      }
    }

    &-projectbox-2 {
      // border: 1px groove rgb(194, 188, 188, 0.4);
      // border-radius: 6px;
      height: 100%;
      width: 100%;
      animation: myAnim 0.6s ease-in 0s 1 normal forwards;
      font-size: 1.3rem;

      .grid-3x2 {
        height: 100%;
        width: 100%;
        display: grid;
        grid-template-rows: repeat(2, 1fr);
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 0; // Remove the gap

        .insights-container-databox:nth-child(even) {
          background-color: rgba(169,
              169,
              169,
              0.2); // Apply grey background to even boxes
        }
      }
    }

    &-projectlist {
      display: flex;
      row-gap: 1rem;
      flex-wrap: wrap;
    }

    &-databox {
      // box-shadow: rgba(0, 0, 0, 0.08) 0px 4px 12px;
      // border-radius: 6px;
      padding: 1rem;
      // border: 1px groove rgb(194, 188, 188, 0.4);
      height: 100%;
      width: 100%;
      animation: myAnim 0.6s ease-in 0s 1 normal forwards;

      & p {
        font-size: smaller;
        font-weight: 200;
        margin-top: 0.3rem;
      }
    }

    &-progressbox {
      position: relative;
      height: 100%;

      &-inner {
        height: 7rem;
        width: 7rem;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        position: absolute;
        border-radius: 100%;
      }

      &-outer {
        height: 6rem;
        width: 6rem;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        position: absolute;
        border-radius: 100%;
        background-color: rgb(169, 209, 25);
        z-index: 2;
      }
    }

    &-graphbox {
      width: 100%;
      display: flex;
      justify-content: center;
      gap: 20px;

      &>div {
        width: 100%;
      }
    }
  }
}

.box-shadow {
  animation: myAnim 0.6s ease-in 0s 1 normal forwards;
  box-shadow: rgba(149, 157, 165, 0.2) 0px 0px 16px;
  border-radius: 1rem;
  background-color: white;
  padding: 1rem;
}
.extend {
  padding: 10px;
}
.texts {
  font-size: large;
  font-weight: bold;
}
.expanded{
  position: absolute;
  inset:0px;
  // width: 100%;
  // max-width: 100%;
  // min-height: 200px;
  // display: flex;
  // flex-direction: column;
  background-color: rgb(0, 0, 0, .7);
  padding: 5rem;
  border-radius: 0;
  box-shadow: none;
  z-index: 5;
  animation: myAnim 0.6s ease-in 0s 1 normal forwards;
  & div{
    background-color: white;
    // width: 100%;
    // max-width: 800px; // Added to prevent it from becoming too wide on large screens
    // padding: 1rem;
    // text-align: center;
  }
}

@keyframes myAnim {
  0% {
    opacity: 0;
    transform: scale(0.6);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@media screen and (max-width: 700px) {
  .insights-container-graphbox {
    display: flex;
    flex-direction: column;
  }
}
</style>
