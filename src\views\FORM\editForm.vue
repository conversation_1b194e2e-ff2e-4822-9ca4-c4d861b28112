<template>
  <div class="view-form fh">
    <div class="fh center" v-if="loadEditForm">
      <loading-circle />
    </div>
    <div v-else class="fh">
      <div class="view-form--nav v-center space-between">
        <h2 class="weight-500 xxl v-center">
          <img
            src="~@/assets/images/icons/arrow-back.svg"
            width="30"
            @click="goBack"
          />{{ formTemplateData.name }}
        </h2>
        <div class="v-center flex-end py-3">
            <button class="btn btn-black mx-2" @click="goBack">Cancel</button>
            <button class="btn mx-2" @click="UpdateForm(config.FORM_STATE_MAP.DRAFT)" v-if="currentStatus===config.FORM_STATE_MAP.DRAFT">Save as Draft</button>
            <!-- this button will update form as open state -->
            <button class="btn mx-2" @click="UpdateForm(config.FORM_STATE_MAP.OPEN)" :disabled="stepWithNoUsers.length > 0" v-if="currentStatus===config.FORM_STATE_MAP.DRAFT" >Submit Form</button>
            <button class="btn mx-2" @click="UpdateForm(currentStatus)"  v-if="currentStatus===config.FORM_STATE_MAP.OPEN || currentStatus===config.FORM_STATE_MAP.REOPENED" >Update</button>
            <!-- // only if no attached workfow is null then only we can close the form -->
            <button class="btn mx-2" @click="UpdateForm(config.FORM_STATE_MAP.CLOSED)"  v-if="(currentStatus===config.FORM_STATE_MAP.OPEN || currentStatus===config.FORM_STATE_MAP.REOPENED) && !workflowId" >Close Form</button>
            <button class="btn mx-2" @click="UpdateForm(config.FORM_STATE_MAP.REOPENED)"  v-if="currentStatus===config.FORM_STATE_MAP.CLOSED" >Reopen Form</button>
            <!-- <button class="btn mx-2" @click="UpdateForm(config.FORM_STATE_MAP.REOPENED)"  v-if="workflowData.workflow_stage?.end_step && config.FORM_STATE_MAP.REOPENED" >Update </button> -->
             <!-- rubiya is working on enable new revision in reopened forms  -->
        </div>
      </div>
      <div class="view-form--maincontainer">
      <div class="view-form--container">
        <div class="fh center" v-if="loading">
      <loading-circle />
    </div>
        <div v-else class="view-form--elements">
          <div class="view-form-autogenerated">
              <div v-if="formData.sequence_value">
                <span class="label">Form ID:</span>
                <span class="value">
                  {{ formData.sequence_value }}
                  </span>
                  </div>
            <template v-for="(ele, index) in templateField">
              <div class="span-div" :key="index" v-if="ele.autogenerated &&  ele.field_name !== 'project_id'">
                <span class="label">{{ ele.field_name }}:</span>
                <span class="value">{{getValuesFromForm(ele.field_name)}}</span>
              </div>
            </template>
          </div>
          <div :class="{
    'dateComponent':true,
    'form-input': true,
    'form-input--required': true
    }" >
        <label>Due Date:</label>
        <input v-model="convertDueDate" type="date" :min="currentDate" @change="changeInDueDate = true"/>
    </div>
            <template v-for="(ele, index) in templateField">
            <component
              v-if="!ele.autogenerated && (ele.form_field.key !== 'BOM' || isOnProjectLevel) && ele.form_field.key !== 'DOCUMENTS'"
              :ref="ele.field_id"
              :key="index"
              :is="ele.form_field.key + '_COMPONENT'"
              :data="ele"
              mode="EDIT"
              :value="formValueData[ele.field_id]"
              :viewOnly="isExternalCollaborator && ['ATTACHMENT_COMPONENT', 'USER_COMPONENT', 'BOM_COMPONENT', 'MULTI_USER_COMPONENT', 'COMPANY_COMPONENT'].includes(ele.form_field.key + '_COMPONENT')"
              @customChange="handleInputChange"
            />
          </template>
        </div>
      </div>
      <div class="view-form--sideBar">
        <div class="tab-headers">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        :class="['tab-header', { active: selectedTab === tab.id }]"
        @click="selectedTab = tab.id"
      >
        {{ tab.name }}
      </div>
    </div>
    <div v-if="selectedTab ===  config.FORM_TAB_STATUS.REVISION_HISTORY || selectedTab ===  config.FORM_TAB_STATUS.HISTORY" class="tab-body-parent">
      <version-history :rootFormId="formData.root_parent_id" @selectedVersion="getFormData" :formId="formData.id" :viewOnly="true" :selectedTab="selectedTab"/>
    </div>
    <div v-if="selectedTab ===  config.FORM_TAB_STATUS.COMMENTS">
      <form-comment-panel
        :form="formData"
        :disabled="true"
        @closeComment="openComment = false"
        :closeOnOutsideClick = true
      />
    </div>
        <div v-if="wftInstaceId && selectedTab === config.FORM_TAB_STATUS.WORKFLOW">
    <div class="activeStep mb-5">
      <!-- Top Row -->
      <div class="step-header space-between v-center">
        <div class="v-center">
          <div class="step-name">{{ workflowData.workflow_stage?.name }}</div>
          <div class="mb-1 ml-2">
            <div
            :class="{
              'v-center workflowStatus status-tag': true,
              'not-started': workflowData.state_value.name === 'NOT_STARTED',
              'ongoing': workflowData.state_value.name === 'ONGOING',
              'completed': workflowData.state_value.name === 'COMPLETED'
            }"
          >
            {{ workflowData.state_value?.name | statusfomat }}
          </div>
          </div>
        </div>
        <router-link
          :to="`/workflows/${workflowData?.workflow_version?.workflow_template.type_value?.name}/?version=${workflowData?.workflow_version?.id}&wfinstance=${wftInstaceId}`"
          target="_blank"
        >
          <button class="btn btn-2 v-center" v-tooltip="'View Workflow'">
            <img
              src="~@/assets/images/icons/workflows.svg"
              width="18px"
              height="18px"
              alt="View Workflow"
            />
          </button>
        </router-link>
        <div v-if="workflowData?.workflow_stage?.end_step">
          <button class="btn btn-black" @click="UpdateForm(config.FORM_STATE_MAP.CLOSED, true)" >Finish WF</button>
        </div>
      </div>
      <!-- Dept Row -->
      <div style="margin-top: -18px;" class="flex v-venter">
        <div class="dept">Department : </div>
        <div class="date-value ml-1"> {{ workflowData.workflow_stage?.core_user_group?.name ?? '--' }}</div>
        <br>
      </div>
      <div class="border"></div>
      <!-- Date/Duration Row -->
      <div class="date-row space-between">
        <div class="date-block">
          <div class="xs label">Start Date</div>
          <div class="flex v-center">
            <div class="date-detail mr-1">Expected : </div>
            <div class="date-value" >{{ workflowData.workflow_instance_steps?.[0]?.planned_start_date | genericFormatDate }}</div>
          </div>
          <div class="flex v-center">
            <div class="date-detail">Actual : </div>
            <div class="date-value"> {{ new Date() | genericFormatDate }}</div>
          </div>
        </div>

        <div class="duration-block">
          <div class="label-2">Duration</div>
          <div class="duration-value">{{ workflowData.workflow_stage?.duration }} hours</div>
        </div>

              <div class="date-block">
                <div class="xs label">End Date</div>
                <div class="v-center">
                  <div class="date-detail">Expected : </div>
                  <div class="date-value"> {{ workflowData.workflow_instance_steps?.[0]?.planned_end_date | genericFormatDate }}</div>
                </div>
                <div class="flex v-center">
                  <div class="date-detail">Actual : </div>
                  <div class="date-value"> {{ new Date() | genericFormatDate }}</div>
                </div>
             </div>
            </div>
            <!-- Document revision upload -->
            <div>
          <div>
          <component
            :upload="true"
            :showUpload="this.workflowData?.workflow_stage?.trigger_actions && ['on_start', 'on_end'].includes(this.workflowData?.workflow_stage?.trigger_actions[0]?.trigger_timing)"
            :stepName="workflowData?.workflow_stage?.name"
            v-if="documentField"
            :ref="documentField.field_id"
            :is="documentField.form_field.key + '_COMPONENT'"
            :data="documentField"
            mode="EDIT"
            :value="formValueData[documentField.field_id]"
            :viewOnly="isExternalCollaborator && ['ATTACHMENT_COMPONENT', 'USER_COMPONENT', 'BOM_COMPONENT', 'MULTI_USER_COMPONENT', 'COMPANY_COMPONENT'].includes(documentField.form_field.key + '_COMPONENT')"
            @customChange="handleInputChange"
            @filesUploaded="revisionFilesUploaded"
          />
          </div>
          </div>
         <!-- Upload Section -->
        <div v-if="formTemplateData.core_form_type.id !== 14 && (this.workflowData?.workflow_stage?.trigger_actions && this.workflowData?.workflow_stage?.trigger_actions[0]?.trigger_timing === 'on_start')" >
          <div class="upload-header">
          <label class="label">Upload</label>
          <div class="next-action-line"></div>
          </div>
        <div class="upload-row">
            <div class="upload-box">
            <div class="label-2 weight-500" >File Upload</div>
          <img src="~@/assets/images/icons/upload-icon.svg" class="upload-icon" />
          <button @click="triggerFilePicker" class="btn btn-2">Browse</button>
          <input
          ref="fileInput"
          type="file"
          multiple
          @change="onFilePicked"
          style="display: none"
          />
        </div>
        <!-- Right Uploaded Files List -->
    <div class="uploaded-files-box">
      <div class="uploaded-header">Uploaded Files</div>
      <div v-if="existingFiles.length || newFiles.length" class="uploaded-list">
        <div
          v-for="(file, index) in [...existingFiles, ...newFiles]"
          :key="index"
          class="uploaded-file"
        >
          <div class="file-name">{{ file?.file_name }}</div>
          <div class="file-status">
            <span v-if="file?.status === 'uploading'" class="loader"></span>
            <span v-else-if="file?.status === 'failed'" class="error-text">failed</span>
            <span style="font-size: 10px;" v-else>{{ file?.size }}</span>
          </div>
        </div>
      </div>
        <div v-else class="no-files">
      No Files Attatched
    </div>
    </div>
      </div>
        </div>
    <!-- Next Action Row -->
  <div class="next-action" v-if="!workflowData.workflow_stage?.end_step">
  <!-- Header with Next Action label and horizontal line -->
  <div class="next-action-header">
    <span class="label">Next Action</span>
    <div class="next-action-line"></div>
  </div>
  <!-- Paragraph for notes -->
  <p class="next-action-desc mb-2">
    Please select the appropriate transition for the next step in the workflow.
  </p>

  <!-- Dropdown -->
  <!-- <select v-model="selectedNextAction" @change="nextAction" class="next-action-select">
    <option
      v-for="action in workflowData.workflow_stage?.next_transition"
      :key="action.id"
      :value="action.id"
    >
      {{ action?.name }}
    </option>
  </select> -->
  <customDropdown
  :list="workflowData.workflow_stage?.next_transition || []"
  :selected="selectedNextActionObj"
  :compact="true"
  @select="onNextActionSelect"
/>
</div>

<!-- user selection startrs -->
 <div v-if="!isExternalCollaborator && selectedNextAction">
 <user-selection-for-wf
        :selectedStartingStep="nextStepData"
        @handleUserSelection="handleUserSelection"
      />
</div>
<!-- user selection ends -->
    <!-- Active Step Row -->
<div class="next-step" v-if="selectedNextAction">
  <!-- Name -->
  <div class="flex w-100 v-center">
    <div class="dept">Step Name : </div>
    <h3 class="weight-500 m"> {{ nextStepData?.name ?? "--" }}</h3>
  </div>

  <!-- Next Action / Department and Duration -->
  <div class="flex space-between w-100 my-2">
    <div class="flex">
      <div class="date-detail">
        Department : </div>
      <span class="date-value">{{ nextStepData.core_user_group?.name ?? "--" }}</span>
    </div>
    <div class="flex">
      <div class="date-detail text-right">
        Duration : </div>
       <span class="date-value">{{ nextStepData.duration ?? "--" }} hours</span>
    </div>
  </div>
</div>

    </div>
    </div>
    <div v-else-if="showWorkFlowStepSelection">
<workflow-starting-stp-selection
:wftStartStages="wftStartStages"
:selectedStartingStep="selectedStartingStep"
:templateId="workflowId"
:wfTypeName="wfTypeName"
@selectStartingStep="addSelectedStep"
@removeSelection="()=>selectedStartingStep=null"
/>
    </div>
  </div>
  </div>
  </div>
  </div>
</template>

<script>
import { getDetailFormTemplate, GetFormDataByFormId, EditForm, workflowDataBasedOnInstanceId, updateWorkFlowActionApi, getNextStepWithTransitionId, generateS3SubmittingUrl, getGivenCustomListsData } from '@/api'
import { mapGetters } from 'vuex'
import { timeStampToDate } from '@/filters/dateFilter'
import { GetFormValueMap } from '@/helper/formValue.js'
import { alert, success } from '@/plugins/notification'
import { genericFormatDate } from '@/utils/date'
import timeComponent from '@/components/form/elements/timeComponent.vue'
import configurationList from '@/components/form/elements/configurationList.vue'
import fileComponent from '@/components/form/elements/fileComponent.vue'
import dateComponent from '@/components/form/elements/dateComponent.vue'
import longTextComponent from '@/components/form/elements/longTextComponent.vue'
import numberComponent from '@/components/form/elements/numberComponent.vue'
import switchComponent from '@/components/form/elements/switchComponent.vue'
import textComponent from '@/components/form/elements/textComponent.vue'
import companyComponent from '@/components/form/elements/companyComponent.vue'
import userComponent from '@/components/form/elements/userComponent.vue'
import multiUserComponent from '@/components/form/elements/multiUserComponent.vue'
import materialComponent from '@/components/form/elements/materialComponent.vue'
import locationComponent from '@/components/form/elements/locationComponent.vue'
import LoadingCircle from '../../components/common/loadingCircle.vue'
import productCodeComponent from '@/components/form/elements/productCodeComponent.vue'
import FormCommentPanel from '../../components/form/formCommentPanel.vue'
import bomComponent from '@/components/form/elements/bomComponent.vue'
import tagsComponent from '@/components/form/elements/tagsComponent.vue'
import contactListComponent from '@/components/form/elements/contactListComponent.vue'
import customDropdown from '@/components/common/customDropdown.vue'
import documentsComponent from '@/components/form/elements/documentsComponent.vue'
import userSelectionForWf from '@/components/form/userSelectionForWf.vue'
import Loader from '@/plugins/loader'
import versionHistory from './versionHistory.vue'
import config from '@/config'
import confirmationDialog from '@/plugins/confirmationDialog'
import { getWorkflowStepWithUsers } from '@/api/apis/workflows'
import workflowStartingStpSelection from '@/components/form/workflowStartingStpSelection.vue'
import errorBorder from '@/helper/formValidation/errorBorder'
export default {
  name: 'EditFormComponent',
  components: {
    BOOLEAN_COMPONENT: switchComponent,
    NUMBER_COMPONENT: numberComponent,
    TEXT_COMPONENT: textComponent,
    LONG_TEXT_COMPONENT: longTextComponent,
    DATE_COMPONENT: dateComponent,
    TIME_COMPONENT: timeComponent,
    CONFIGURATION_LIST_COMPONENT: configurationList,
    ATTACHMENT_COMPONENT: fileComponent,
    COMPANY_COMPONENT: companyComponent,
    USER_COMPONENT: userComponent,
    MULTI_USER_COMPONENT: multiUserComponent,
    MATERIALS_COMPONENT: materialComponent,
    LOCATION_COMPONENT: locationComponent,
    PRODUCT_CODE_COMPONENT: productCodeComponent,
    BOM_COMPONENT: bomComponent,
    TAGS_COMPONENT: tagsComponent,
    CONTACT_LIST_COMPONENT: contactListComponent,
    DOCUMENTS_COMPONENT: documentsComponent,
    LoadingCircle,
    FormCommentPanel,
    versionHistory,
    workflowStartingStpSelection,
    customDropdown,
    userSelectionForWf
  },
  filters: {
    timeStampToDate,
    genericFormatDate,
    statusfomat (value) {
      if (!value) return ''
      return value.replace(/_/g, ' ')
    }
  },
  data () {
    return {
      formSequenceTemplate: {},
      folderId: null,
      fileSize: 0,
      files: [],
      existingFiles: [],
      newFiles: [],
      currentDate: new Date().toLocaleDateString('en-CA'),
      validationErrors: [],
      templateId: '',
      templateName: '',
      formTemplateData: {},
      formData: {},
      currentStatus: '',
      formValueData: {},
      loading: false,
      openComment: false,
      convertDueDate: '',
      changeInDueDate: false,
      metadataPayloadValueMap: {},
      config: config,
      workflowId: null,
      workflowData: {},
      wftInstaceId: null,
      selectedNextAction: null,
      nextStepData: {},
      selectedTab: 1,
      loadEditForm: false,
      stepWithNoUsers: {},
      workFlowVersionId: null,
      tabs: [
        {
          id: 1,
          name: 'WorkFlow'
        },
        {
          id: 2,
          name: 'Comments'
        },
        {
          id: 3,
          name: 'Version History'
        },
        {
          id: 4,
          name: 'History'
        }
      ],
      wftStartStages: [],
      selectedStartingStep: null,
      wfTypeName: '',
      wFstepUsers: [] // thisis to store the users selected for the next step
    }
  },
  computed: {
    ...mapGetters(['isOnProjectLevel', 'tenantUsersList', 'currentProject', 'tenantList']),
    ...mapGetters(['isExternalCollaborator']),
    formVersion () {
      return this.formTemplateData?.template_versions?.find(
        (item) => item.id === this.formData.template_version_id
      )
    },
    documentField () {
      return this.templateField.find(
        (ele) => ele.form_field.key === 'DOCUMENTS'
      )
    },
    selectedNextActionObj () {
      return (
      this.workflowData.workflow_stage?.next_transition?.find(
        a => a.id === this.selectedNextAction
      ) || { id: -1, name: 'Select' }
      )
    },
    templateField () {
      return this.formVersion?.template_fields
    },
    showWorkFlowStepSelection () {
      return this.workflowId && !this.wftInstaceId && this.currentStatus === config.FORM_STATE_MAP.DRAFT && this.selectedTab === config.FORM_TAB_STATUS.WORKFLOW
    }
  },
  methods: {
    revisionFilesUploaded (data) {
      // data[0].folder_id = this.folderId
      this.newFiles = data
    },
    onNextActionSelect (item) {
      this.selectedNextAction = item.id
      this.nextAction() // Call your original method
    },
    triggerFilePicker () {
      this.$refs.fileInput.click()
    },
    async onFilePicked (event) {
      const selectedFiles = event.target.files
      const defaultTenantId = '' // Replace or map from Vuex if needed

      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i]
        const fileType = file.name.split('.').pop()
        const formattedSize = this.formatFileSize(file.size)

        // Temporary file object with uploading status
        const fileEntry = {
          file_name: file.name,
          file_type: fileType,
          file_size: file.size,
          status: 'uploading'
        }

        // Add temporarily while uploading
        this.newFiles.push(fileEntry)

        try {
          // Step 1: Get signed URL
          const { url } = await generateS3SubmittingUrl({
            tenantId: defaultTenantId,
            feature: 'form', // adjust as needed
            featureId: 'formDocument', // adjust as needed
            fileName: file.name
          })

          // Step 2: Upload to S3
          await fetch(url, {
            method: 'PUT',
            body: file,
            headers: {
              'Content-Type': file.type
            }
          })

          // Step 3: Extract blob key
          const blobKey = url.split('?')[0].split('.com/').pop()

          // Step 4: Replace entry with final attachmentObj
          this.$set(this.newFiles, this.newFiles.length - 1, {
            blob_key: blobKey,
            file_name: file.name,
            file_type: fileType,
            file_size: file.size,
            size: formattedSize, // Optional if used in UI
            status: 'uploaded'
          })
        } catch (err) {
          console.error('Upload failed:', err)
          // Mark status as failed
          this.$set(this.files, this.files.length - 1, {
            ...fileEntry,
            status: 'failed'
          })
        }
      }

      // Clear input so same file can be selected again
      this.$refs.fileInput.value = ''
    },
    formatFileSize (size) {
      const i = size === 0 ? 0 : Math.floor(Math.log(size) / Math.log(1024))
      return (
        (size / Math.pow(1024, i)).toFixed(2) * 1 +
    ' ' +
    ['B', 'KB', 'MB', 'GB', 'TB'][i]
      )
    },
    validateInputData (formInput, form) {
      if (formInput?.form_field?.key === 'USER') {
        if (formInput.required && (!form[formInput.field_id][0].componentValue || !form[formInput.field_id][0].componentValue.length)) {
          this.validationErrors.push(`${formInput.caption} is a mandatory field`)
        }
      }
      if (formInput?.form_field?.key === 'BOOLEAN') {
        // we are comparing the value to null instead of checking if it is a falsy value cause the boolean component value will false in one case and it is valid value
        if (formInput.required && form[formInput.field_id][0].componentValue === null) {
          this.validationErrors.push(`${formInput.caption} is a mandatory field`)
        }
        return
      } else if (formInput?.form_field?.key === 'MATERIALS') {
        if (formInput.required && form[formInput.field_id][0].componentValue.length < 1) {
          this.validationErrors.push(`${formInput.caption} is a mandatory field`)
        }
      }
      if (formInput?.required && !form[formInput.field_id][0].componentValue) {
        this.validationErrors.push(`${formInput.caption} is a mandatory field`)
      }
    },
    handleInputChange (data) {
      this.metadataPayloadValueMap[data.field_id] = data.value
    },
    getValuesFromForm (key) {
      switch (key) {
      case 'created_on':
        if (this.formData.created_on) {
          return timeStampToDate(this.formData.created_on)
        }
        return '--'
      case 'created_by': {
        if (this.formData.created_by) {
          return this.formData.created_by_user.first_name + ' ' + this.formData.created_by_user.last_name
        }
        return '--'
      }
      case 'updated_on':
        if (this.formData.updated_on) {
          return timeStampToDate(this.formData.updated_on)
        }
        return '--'
      case 'updated_by': {
        if (this.formData.updated_by) {
          return this.formData.updated_by_user.first_name + ' ' + this.formData.updated_by_user.last_name
        }
        return '--'
      }
      }
    },
    goBack () {
      this.$router.go(-1)
    },
    getFormTemplateData () {
      this.loading = true
      this.loadEditForm = true
      getDetailFormTemplate(this.templateId).then(async (res) => {
        this.formTemplateData = res.core_form_templates_by_pk
        this.formSequenceTemplate = this.formTemplateData.core_sequence_id_template
        this.loading = false
        this.loadEditForm = false
        this.workflowId = res.core_form_templates_by_pk.workflow_template_id
        this.workFlowVersionId = res.core_form_templates_by_pk.workflow_template?.workflow_versions[0].id
        this.wfTypeName = res.core_form_templates_by_pk.workflow_template?.type_value?.name
        const customListData = this.findUsedCustomListIds(res.core_form_templates_by_pk.template_versions)
        if (customListData.size > 0) {
          await this.getGivenCustomList([...customListData])
        }
        if (this.formData.status === config.FORM_STATE_MAP.DRAFT) {
          this.checkUsersAvailabilityWithWorkFlow()
          this.wftStartStages = res.core_form_templates_by_pk?.workflow_template?.workflow_versions[0]?.workflow_stages
          if (this.wftStartStages?.length === 1) {
            this.selectedStartingStep = this.wftStartStages[0]
          }
        }
        // this.workflowData = res.core_form_templates_by_pk.workflow_template
      }).catch(() => {
        alert('Failed to fetch form template data')
      })
    },
    async UpdateForm (status, closeWFTform = false) {
      let error = false
      if (this.workflowData?.workflow_stage?.trigger_actions && this.workflowData?.workflow_stage?.trigger_actions[0]?.trigger_timing === 'on_start' && this.workflowData?.workflow_stage?.trigger_actions[0]?.mandatory === true && this.selectedNextAction) {
        if (![...this.newFiles, ...this.existingFiles].length && this.workflowData?.workflow_stage?.trigger_actions?.[0]?.mandatory === true) {
          alert('File upload mandatory before next action!')
          error = true
        }
      }
      const isUploading = this.newFiles.some(file => file.status === 'uploading')
      if (isUploading) {
        alert('Please wait for all files to finish uploading before saving.')
        error = true
      }
      if (this.selectedNextAction && !this.nextStepData.core_user_group?.name && !this.wFstepUsers?.length) {
        errorBorder('multiSelectinput', true)
        alert('Please select users for the next step')
        error = true
      }
      if (error) return
      // if (this.workflowData)
      // closeWFTform is used to close the form if there is a workflow template associated with it
      try {
        const form = this.$refs
        this.validationErrors = []
        if (parseInt(status) !== config.FORM_STATE_MAP.DRAFT) {
          Object.keys(form).forEach((key) => {
            if (form[key] !== undefined) {
              this.validateInputData(form[key][0]?.data, form)
            }
          })
        } else {
          if (!Object.keys(form).length) {
            this.validationErrors.push('Please fill atleast one field')
          }
        }
        if (this.validationErrors.length > 0) {
          this.validationErrors.forEach(error => alert(error))
          return
        }
        const metadataPayload = []
        for (const [key, value] of Object.entries(this.metadataPayloadValueMap)) {
          metadataPayload.push({ field_id: key, value })
        }
        const payload = {
          formId: this.$route.params.formId,
          metadataPayload
        }
        if (this.changeInDueDate) {
          if (!this.convertDueDate) {
            alert('Please select due date')
          }
          payload.dueDate = new Date(this.convertDueDate).toISOString()
        }
        if (parseInt(status) !== this.currentStatus) {
          payload.status = status
        }
        if (closeWFTform === true) delete payload.status
        if (status !== config.FORM_STATE_MAP.DRAFT && this.currentStatus === config.FORM_STATE_MAP.DRAFT && this.workflowId && !this.selectedStartingStep && !this.wftInstaceId) {
          alert('Please select a starting step for the workflow')
          return
        }
        // this action only expected to be happened when the form is in draft state
        if (!this.wftInstaceId && this.selectedStartingStep && this.currentStatus === config.FORM_STATE_MAP.DRAFT) {
          payload.stepId = this.selectedStartingStep.id
        }

        const loader = new Loader()
        loader.show()
        await EditForm(payload).then(async (res) => {
          loader.hide()
          if (res?.message?.message === 'Form Updated Successfully') {
          // if next transition is selected then only need to  update the workflow action
            if (this.selectedNextAction || closeWFTform || this.newFiles.length) {
            // closeWFTform is used to close the form if there is a workflow template associated with it
              const res = await this.updateWorkFlowAction(closeWFTform)
              if (res?.message?.form_id) {
                const templateId = this.workflowData.workflow_stage.trigger_actions?.[0]?.core_form_template.id
                const templateName = this.workflowData.workflow_stage.trigger_actions?.[0]?.core_form_template.name
                this.$router.push(`/form/editform/${templateId}/${templateName}/${res?.message?.form_id}`)
                this.getFormData()
                return
              }
            }
            success(res?.message?.message ?? 'Successfully edited form')
            this.$router.push(`/form/viewform/${this.templateId}/${this.templateName}/${this.$route.params.formId}`)
          } else {
            alert('Failed to update form')
          }
        }).catch(() => {
          loader.hide()
          alert('Failed to update form')
        })
      } catch (error) {
        console.log(error)
      }
    },
    getWorkFlowStepData () {
      if (!this.wftInstaceId) {
        return
      }
      workflowDataBasedOnInstanceId(this.wftInstaceId, this.isExternalCollaborator).then((res) => {
        this.workflowData = res.workflow_instances_by_pk
        this.existingFiles = this.workflowData.workflow_instance_steps[0].core_attachments.map(file => ({
          ...file,
          size: this.formatFileSize(file.file_size),
          status: 'uploaded'
        }))
        this.fileSize = this.files.length
      })
    },
    nextAction () {
      getNextStepWithTransitionId(this.selectedNextAction).then(res => {
        this.nextStepData = { currentTansName: res.workflow_transitions_by_pk.name, ...res.workflow_transitions_by_pk.next_workflow_stage }
      }).catch((err) => {
        console.log(err)
      })
    },
    async updateWorkFlowAction (closeWFTform) {
      this.newFiles.map(file => {
        file.status = undefined
        file.size = undefined
      })
      const body = {
        input: {
          metadata: {
            attachments: this.newFiles
          },
          close_workflow: closeWFTform ? true : undefined,
          history_id: this.workflowData.transition_history[0].id,
          instance_id: this.wftInstaceId,
          action_taken: closeWFTform ? null : this.selectedNextAction,
          next_step_assignees: !this.nextStepData.core_user_group?.name ? this.wFstepUsers : undefined
        }
      }
      return updateWorkFlowActionApi(body)
    },
    getFormData (form) {
      this.templateId = this.$route.params.templateId
      this.templateName = this.$route.params.templateName
      const updatedFormId = form ? form.id : this.$route.params.formId
      this.loading = true
      this.loadEditForm = true
      GetFormDataByFormId(updatedFormId, this.isOnProjectLevel).then(
        (res) => {
          this.formData = res.core_forms_by_pk
          // this.folderId = this.formData?.forms_document_list[0].core_document?.parent_id
          this.currentStatus = res.core_forms_by_pk.status
          this.convertDueDate = new Date(this.formData.due_date).toISOString().split('T')[0]
          this.formValueData = GetFormValueMap(res.core_forms_by_pk)
          this.wftInstaceId = res.core_forms_by_pk?.workflow_instance_id
          this.getWorkFlowStepData(this.wftInstaceId)
          this.getFormTemplateData()
        }
      ).catch((err) => {
        console.log(err)
        alert('Failed to fetch form data')
      })
    },
    checkUsersAvailabilityWithWorkFlow () {
      // const stepUserMap = {}

      getWorkflowStepWithUsers(this.workFlowVersionId).then((res) => {
        const stepUserMapSet = {}
        res.workflow_stages.forEach((stage) => {
          for (const userData of stage.core_user_group?.core_user_group_members ?? []) {
            if (stepUserMapSet[stage.id]) {
              stepUserMapSet[stage.id].add(userData.user_id)
            } else {
              stepUserMapSet[stage.id] = new Set([userData.user_id])
            }
          }
        })
        // checking for the first step with no users, respected stedid are stored
        const stepWithNoUsers = {}
        if (this.isOnProjectLevel) {
          for (const step in stepUserMapSet) {
            let confirmFlag = false
            for (const user of this.currentProject.project_user_associations) {
              if (stepUserMapSet[step].has(user.user_id)) {
                confirmFlag = true
                break
              }
            }
            if (!confirmFlag) {
              stepWithNoUsers[step] = step
            }
          }
        } else {
          for (const step in stepUserMapSet) {
            let confirmFlag = false
            for (const user of this.tenantUsersList) {
              if (stepUserMapSet[step].has(user.associated_user.id)) {
                confirmFlag = true
                break
              }
            }
            if (!confirmFlag) {
              stepWithNoUsers[step] = step
            }
          }
        }
        this.stepWithNoUsers = Object.keys(stepWithNoUsers)
        if (this.stepWithNoUsers?.length > 0) {
          confirmationDialog(
            `${this.stepWithNoUsers?.length} workflow steps have user groups with zero users from this project. Forms will be blocked at these steps until resolved. Are you sure to continue?`,
            (res) => {
              if (res) {
              }
            }
          )
        }
      })
    },
    addSelectedStep (stage) {
      this.selectedStartingStep = stage
    },
    findUsedCustomListIds (templateVersions) {
      const configIdSet = new Set()
      for (const templateVersion of templateVersions) {
        for (const field of templateVersion.template_fields) {
          if (field.field_type_id === config.FORM_TYPE.CONFIGRATION_LIST) {
            configIdSet.add(field.custom_list_id)
          }
        }
      }
      return configIdSet
    },
    async getGivenCustomList (ids) {
      const customListData = await getGivenCustomListsData(ids)
      const customListMap = {}
      for (const customList of customListData.core_custom_list) {
        customListMap[customList.id] = customList.custom_list_values
      }
      this.$store.dispatch('form/saveCustomListMap', customListMap)
    },
    handleUserSelection (data) {
      this.wFstepUsers = data.value.insert
    }

  },
  mounted () {
    // this.getFormTemplateData()
    this.getFormData()
    // GetFormDataByFormId(this.$route.params.formId, this.isOnProjectLevel).then(
    //   (res) => {
    //     this.formData = res.core_forms_by_pk
    //     this.currentStatus = res.core_forms_by_pk.status
    //     this.convertDueDate = new Date(this.formData.due_date).toISOString().split('T')[0]
    //     this.formValueData = GetFormValueMap(res.core_forms_by_pk)
    //     this.wftInstaceId = res.core_forms_by_pk?.workflow_instance_id
    //     this.getWorkFlowStepData(this.wftInstaceId)
    //     this.getFormTemplateData()
    //   }
    // )
  },
  watch: {
    selectedNextAction () {
      const container = this.$refs.userSelectionContainer
      if (container) {
        const select = container?.getElementsByTagName('select')
        select[0].style.padding = ' 0 0.85em'
        select[0].style.height = '25px'
        select[0].style.fontSize = '.8rem'
        const textInput = container.querySelector('input[type="text"]')
        textInput.style.padding = '0 0.85em'
        textInput.style.height = '25px'
        textInput.style.fontSize = '.8rem'
        const badge = container.querySelectorAll('.form-user-bedge') ?? []
        badge.forEach((item) => {
          item.style.fontSize = '.8rem'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped >
.btn-2 {
  font-size: 12px;
  width: fit-content;
  height: fit-content;
}
.view-form {
  background: rgb(255, 196, 103,.05);
  &--nav {
    height: 60px;
    margin: -12px;
    padding: 0 20px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
    & input {
      border: var(--border);
      background-color: transparent;
      border-radius: 4px;
      padding: 4px 8px;
      height: 30px;
      width: 200px;
      outline: none;
      &:focus {
        border-color: var(--brand-color);
      }
    }
  }
  &--container {
    padding: 20px;
    max-width: 700px;
    height: calc(100%);
    overflow-y: auto;
    box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
    margin-top: 10px;
    border-radius: 6px;
    justify-self: flex-start;
    background: white;
  }
  &-autogenerated {
    background-color: rgba(var(--brand-rgb), 0.2);
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between; /* Adjust alignment if needed */
    flex-wrap: wrap; /* Ensure items wrap within the container */
    gap: 10px; /* Space between items */
    .span-div {
      display: flex;
      align-content: center;
      align-items: center;
    }
    & .label {
      font-size: 14px;
      color: var(--brand-color-1);
      font-weight: 500;
    }
    & .value {
      margin-left: 6px;
      font-size: 16px;
      color: var(--text-color);
    }
  }
  &--sideBar {
    margin-top: 10px;
    max-width: 568px;
    height: calc(100%);
    border-radius: 6px;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;

    // // background: white;
    // & .activeStep {
    //   display: flex;
    //   flex-direction: column;
    //   gap: 20px;
    //   box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
    //   padding: 20px;
    //   background: white;
    //   border-radius: 6px;
    //   margin: 2px 4px 4px 4px;
    // }

    & select {
      border: var(--border);
      background-color: transparent;
      border-radius: 4px;
      // padding: 0 6px;
      // height: 20px;
      // width: 100px;
      outline: none;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 15px;
      &:focus {
        border-color: var(--brand-color);
      }
    }
  }
  &--maincontainer{
    display: flex;
    justify-content: center;
    gap: 20px;
    padding: 20px;
    height: calc(100% - 60px);
    justify-items: center;
  }
  &--elements{
    height: -webkit-fill-available;
  }

}
.comment-btn {
  position: fixed;
  bottom: 0;
  right: 0;
  margin: 12px;
  z-index: 9;
  background: var(--white);
  cursor: pointer;
  border-radius: 50%;
  height: 45px;
  width: 45px;
  border: none;
  box-shadow: 4px 4px 4px 0 rgba(0, 0, 0, 0.25);
  & img {
    width: 30px;
    height: 30px;
  }
  &:hover {
    background: var(--brand-color);
    & img {
      filter: invert(1);
    }
  }
  &:active {
    box-shadow: inset 4px 4px 4px 0 rgba(0, 0, 0, 0.25);
  }
}
.form-comment {
  position: fixed;
  bottom: 30px;
  right: -700px;
  top: 130px;
  width: 400px;
  z-index: 9;

  transition: right 0.8s ease-in-out;
  &[arial-data="open"] {
    right: 20px;
  }
}
.activeStep-row{
  display: grid;
  grid-template-columns: 3fr 1fr 4fr;
  align-items: center;
  & .label{

  }
  & .value{

  }
}
.next-step {
}
.status-tag {
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 600;
  display: inline-block;
  min-width: 100px;
  text-align: center;
}
.not-started {
  background-color: rgba(255, 0, 0, 0.1);
  color: purple;
  max-width: 150px;
}

.completed {
  background-color: rgba(0, 128, 0, 0.1);
  color: green;
  max-width: 150px;
}

.ongoing {
  background-color: rgba(255, 165, 0, 0.1);
  color: orange;
  max-width: 150px;
}
.tabs {
  border: 1px solid #ccc;
  border-radius: 6px;
  overflow: hidden;
  font-family: sans-serif;
}

.tab-headers {
  display: flex;
  background-color: #f1f1f1;
  border-bottom: 1px solid #ccc;
}

.tab-header {
  padding: 12px 18px;
  cursor: pointer;
  flex: 1;
  text-align: center;
  transition: background 0.3s;
}

.tab-header:hover {
  background-color: #e0e0e0;
}

.tab-header.active {
  background-color: #fff;
  font-weight: 400;
  border-bottom: 2px solid var(--brand-color);
}
.activeStep {
  padding: 16px 20px 20px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  gap: 10px;
  @media (max-height: 500px) {
    max-height: 455px;
    overflow-y: scroll;
  }
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-name {
  font-size: 28px;
  font-weight: 600;
}

.dept {
  font-size: 12px;
  color: #696969;
  font-weight: 400;
}
.border {
  // box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.5);
  border-top: 1px solid #DCDCDC;
}
.date-row {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.label-2 {
  font-size: 11px;
  color: #696969;
  // font-weight: 500;
  margin-bottom: 4px;
}

.date-block {
  display: flex;
  flex-direction: column;
  font-size: 14px;
}

.date-detail {
  margin-right: 2px;
  font-size: 12px;
  color: #696969;
  margin-bottom: 2px;
}
.date-value {
  font-size: 12px;
}

.duration-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.duration-value {
  // color: #696969;
  font-size: 12px;
  font-weight: 500;
  margin-top: -5px;
}

.upload-row {
  height: 90px;
  display: flex;
  gap: 15px;
  // padding-top: 16px;
  // align-items: flex-start;
  // border-top: 1px solid #ccc;
  // margin-top: 12px;
}

.upload-box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-right: 1px solid #ccc;
  padding-right: 10px;
  width: 25%;
  height: 90px;
  margin-right: 10px;
  text-align: center;
  align-items: center;
}
.upload-icon {
  width: 25px;
  height: 25px;
  object-fit: contain;
  margin-bottom: 8px;
}

.uploaded-files-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.uploaded-header {
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 11px;
  white-space: nowrap;
  color: #696969;
  font-weight: 500;
}

.uploaded-list {
  overflow-y: auto;
  max-height: 120px;
  padding-right: 8px;
}
.no-files {
  font-size: 10px;
// display: flex;
// align-items: center;
// justify-content: center;
height: 120px;
padding-right: 8px;
}

.uploaded-file {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #eee;
  font-size: 14px;
  color: #333;
}

.file-name {
  font-size: 10px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
}

.file-status {
  white-space: nowrap;
  font-size: 13px;
  color: #888;
}

.loader {
  width: 14px;
  height: 14px;
  border: 2px solid #ccc;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
}
.error-text {
  color: red;
  font-size: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.next-action-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.next-action-header .label {
  font-weight: 500;
  font-size: 12px;
  white-space: nowrap;
  margin-right: 12px;
}

.next-action-line {
  flex-grow: 1;
  height: 1px;
  background-color: #ccc;
  margin-top: 1px;
}

.next-action-desc {
  font-size: 11px;
  color: #666;
}

.next-action-select {
  // width: fit-content;
  // padding: 6px 10px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.upload-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.upload-header .label {
  font-weight: 500;
  font-size: 12px;
  white-space: nowrap;
  margin-right: 12px;
}

.upload-line {
  flex-grow: 1;
  height: 1px;
  background-color: #ccc;
  margin-top: 1px;
}

.upload-desc {
  font-size: 11px;
  color: #666;
  margin: 0 0 8px 0;
}

.upload-select {
  // width: fit-content;
  // padding: 6px 10px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.tab-body-parent{
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: -webkit-fill-available;
    overflow: auto;
}

</style>
