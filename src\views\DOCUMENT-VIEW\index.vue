<template>
  <div class="document-view fh">
    <div class="document-view-bar v-center space-between px-3">
      <h1 class="weight-600 xxxl">Document Viewer</h1>
      <div class="v-center">
        <div class="v-center">
          <upload-document
            v-if="parentFolder  && !isTenantViewer && !isOnProjectLevel"
            :folder="parentFolder"
            :is_revision="false"
            :allDocuments="documents"
            :open="uploadDocumentModal"
            @uploaded="getAllDocument"
          >
            <div class="upload-btn">
              Upload Document
            </div>
          </upload-document>
          <div class="upload-btn ml-2 pointer"
          v-if="parentFolder && !isProjectViewer && isOnProjectLevel"
          @click="uploadDocumentModal = true">
              Upload Document
            </div>
          <button v-if="showCreateFolderButton" class="btn btn-black mx-3" @click="openFolderPopup">
            Create Folder
          </button>
        </div>
        <div class="input-group search m">
          <input
            type="text"
            v-model="searchKeyWord"
            placeholder="Search by Folder,File name"
          />
        </div>
      </div>
    </div>
    <div v-if="loading" class="document-view-container center">
      <loading-circle />
    </div>
    <div v-else class="document-view-container">
      <div class="go-back v-center mb-3" :class="!parentFolder ?'disabled':''">
        <div v-if="!collaborator" class="flex v-center" @click="goBack">
          <img
          class="mr-1"
          src="~@/assets/images/icons/arrow-back.svg"
          width="20px"
          />
          <p>Go Back</p>
                    <!-- Breadcrumbs display -->
                    <div v-if="breadcrumbs.length > 0" class="breadcrumbs ml-3">
            <span> / </span>
            <span v-for="(breadcrumb, index) in breadcrumbs" :key="index">
              {{ breadcrumb }}
              <span v-if="index < breadcrumbs.length - 1"> / </span>
            </span>
          </div>
        </div>
      </div>
      <div class="document-view-folder" v-if="!collaborator">
        <p>Folders</p>
        <div v-if="filteredDocuments.length && !collaborator" class="document-view-card-container" >
          <template v-for="doc in filteredDocuments">
              <folder-card
              :key="doc.id"
              v-if="doc.folder"
              :folder="doc"
              @rename="getAllDocument"
              @delete="getAllDocument"
            />
          </template>
        </div>
        <span v-else>No folders were found</span>
      </div>
      <div class="break" />
      <div v-if="parentFolder || collaborator"  class="document-view-folder mt-3">
        <p>Files</p>
        <div v-if="filteredDocuments.length" class="document-view-card-container" >
          <template v-for="doc in filteredDocuments"  >
            <file-card
              :key="doc.id"
              :parentFolder="parentFolder"
              :file="doc"
              :thumbnail_url="getUrl(doc)"
              v-if="!doc.folder"
              @updated-inherited-doc="getAllDocument"
              @delete="getAllDocument"
              @uploaded="getAllDocument"
              @openDetail="openDetail(doc)"
              @updated="getAllDocument"
            />
          </template>
        </div>
        <span v-else>No files were found</span>
      </div>
    </div>
    <modal
      :open="newFolderPopup"
      @close="closeFolderPopup"
      title="Create New Folder"
    >
      <create-folder-form  v-if="newFolderPopup" :buttonDisabled="loading" :open="newFolderPopup" @close="closeFolderPopup" @create="getAllDocument"  :folder="parentFolder"
            :allDocuments="documents"/>
    </modal>
    <modal :open="inheritAndCopyModal.open"
    @close="inheritAndCopyModal.open = false"
    :title="inheritAndCopyModal.title">
    <document-selector :purpose="inheritAndCopyModal.inheritOrCopy"
    v-if="inheritAndCopyModal.open"
    :open="inheritAndCopyModal.open"
    @close="inheritAndCopyModal.open = false"
    @copy-doc="copyDoc"
    @inherit-doc="inheritDoc"/>
  </modal>
    <modal
      :open="uploadDocumentModal"
      @close="uploadDocumentModal = false"
      :closeOnOutsideClick="true"
      title="Upload Document"
    >
    <div class="upload-document-modal">
        <!-- <div class="center">
          <img src="~@/assets/images/icons/import-icon.svg" alt="" />
          <div>Import .CSV</div>
        </div> -->
        <div class="center pointer" @click="openInheritModal">
          <img src="~@/assets/images/icons/inherit-icon.svg" alt="" />
          <div>Inherit From</div>
        </div>
        <div class="center pointer" @click="openCopyModal">
          <img src="~@/assets/images/icons/copy-icon.svg" alt="" />
          <div>Copy From</div>
        </div>
        <div class="center pointer">
          <input type="file" id="upload-docss" ref="docUpload" @click="resetInputValue" @change="onChange">
          <label for="upload-docss">
            <img src="~@/assets/images/icons/add-icon.svg" alt="" />
            <div>Local</div>
          </label>
        </div>
      </div>
    </modal>
    <modal :open="previewPopover" @close="previewPopover = false">
      <upload-document-project
      @uploaded="handleDocumentUpload"
      v-if="previewPopover"
      :open="previewPopover"
      :folder="parentFolder"
      :is_revision="false"
      :file-base64="fileBase64"
      :file-data="fileData"
      :file-description="fileDescription"
      :file-extension="fileExtension"
      :file-name="fileName"
      :file-size="fileSize"
      :file-type="fileType"
      :allDocuments="documents"
      @close="previewPopover = false"
      />
    </modal>
    <file-detail
      v-if="detailObject.open"
      :fileId="detailObject.fileId"
      :view_only="detailObject.view_only"
      :state="detailObject.state"
      @close="closeDetail"
    />
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import Modal from '../../components/common/modal.vue'
import CreateFolderForm from '../../components/document/createFolderForm.vue'
import {
  getAllParentFolders,
  getDocumentsByParentId,
  getDocumentById,
  insertDocument,
  inheritDocument,
  getAllCollabDocument,
  generateS3DownloadingUrl
} from '@/api'
import { success, alert } from '../../plugins/notification'
import LoadingCircle from '../../components/common/loadingCircle.vue'
import FolderCard from '../../components/document/folderCard.vue'
import UploadDocument from '../../components/document/uploadDocument.vue'
import FileCard from '../../components/document/fileCard.vue'
import FileDetail from '../../components/document/fileDetail.vue'
import DocumentSelector from '../../components/document/documentSelectorDialog.vue'
import UploadDocumentProject from '@/components/document/uploadDocumentProject.vue'
export default {
  name: 'DocumentIndex',
  components: {
    Modal,
    CreateFolderForm,
    LoadingCircle,
    FolderCard,
    DocumentSelector,
    UploadDocument,
    UploadDocumentProject,
    FileCard,
    FileDetail
  },
  data: () => ({
    keyToUrlMap: {},
    fileDescription: '',
    fileData: null,
    fileBase64: null,
    fileName: null,
    fileExtension: null,
    fileType: null,
    fileSize: null,
    breadcrumbs: ['Root'],
    searchKeyWord: '',
    newFolderPopup: false,
    documents: [],
    loading: false,
    parentFolder: null,
    previewPopover: false,
    detailObject: {
      open: false,
      fileId: '',
      view_only: false,
      state: null
    },
    uploadDocumentModal: false,
    inheritAndCopyModal: {
      open: false,
      title: '',
      inheritOrCopy: ''
    },
    window: window,
    folderIdData: []
  }),
  computed: {
    ...mapGetters(['user', 'getUserById']),
    ...mapGetters(['isProjectAdmin', 'isTenantAdmin', 'isOnProjectLevel', 'isTenantViewer', 'isProjectViewer', 'collaborator']),
    filteredDocuments () {
      return this.documents.filter((doc) => {
        return (
          doc?.doc_name
            ?.toLowerCase()
            .includes(this.searchKeyWord?.toLowerCase())
        )
      })
    },
    getAllFolders () {
      if (!this.collaborator) {
        return this.documents.filter((doc) => {
          return doc.folder
        })
      } else return []
    },
    getAllFiles () {
      return this.documents.filter((doc) => {
        return !doc.folder
      })
    },
    showCreateFolderButton () {
      if (this.isOnProjectLevel) {
        return this.user.projectLevelRole === 'ADMIN' || this.user.projectLevelRole === 'COLLABORATOR'
      } else {
        return (
          (this.user.tenantLevelRole === 'ADMIN' ||
          this.user.tenantLevelRole === 'COLLABORATOR') && !this.collaborator
        )
      }
    }
  },
  watch: {
    '$route.params.documentId': {
      handler () {
        this.getAllDocument()
      }
    },
    '$store.state.projectIdForCollaborator' () {
      this.getAllDocument()
    }
  },
  methods: {
    ...mapMutations('document', ['clearIconsUrlMap']),
    // this is to reset the file data  in input (file) tag , else  , if we tried to select same file again and again it wont work
    resetInputValue () {
      this.$refs.docUpload.value = null
    },
    getUrl (doc) {
      let S3Key = ''
      if (doc?.inherited_from_doc_id) {
        S3Key = doc?.inherited_from_document?.thumbnail_blob_key
      } else {
        if (doc.associated_versions.length) {
          S3Key = doc.associated_versions[doc.associated_versions.length - 1].thumbnail_blob_key
        } else {
          S3Key = doc.thumbnail_blob_key
        }
      }
      return this.keyToUrlMap[S3Key]
    },
    onChange (e) {
      const file = e.target.files[0]
      this.uploadDocumentModal = false
      if ((this.folder?.blob_key) && file.name?.split('.')[1] !== this.folder?.blob_key?.split('.')[1]) {
        this.openWarning = true
        return
      }
      // this is to  separate file name and extension later while uploading the file name file name and extension will concatenate
      this.fileName = file?.name?.split('.')?.shift()
      this.fileExtension = file?.name?.split('.')?.pop()
      this.fileType = file.type
      this.fileSize = file.size
      this.fileData = file
      const fileReader = new FileReader()
      fileReader.readAsDataURL(file)
      fileReader.onload = (e) => {
        this.fileBase64 = e.target.result
        this.previewPopover = true
      }
    },
    openInheritModal () {
      this.uploadDocumentModal = false
      this.inheritAndCopyModal.open = true
      this.inheritAndCopyModal.inheritOrCopy = 'inherit'
      this.inheritAndCopyModal.title = 'Inherit Document'
    },
    openCopyModal () {
      this.uploadDocumentModal = false
      this.inheritAndCopyModal.open = true
      this.inheritAndCopyModal.inheritOrCopy = 'copy'
      this.inheritAndCopyModal.title = 'Copy Document'
    },
    copyDoc (file) {
      const fileName = file.doc_name + ' ' + '(copy)'
      let thumbnailBlobKey = null
      let blobKey = null
      if (file.associated_versions.length) {
        thumbnailBlobKey = file.associated_versions[0].thumbnail_blob_key
        blobKey = file.associated_versions[0].blob_key
      } else {
        thumbnailBlobKey = file.thumbnail_blob_key
        blobKey = file.blob_key
      }
      insertDocument(
        blobKey,
        file.description,
        fileName,
        file.doc_size,
        file.doc_type,
        file.folder,
        this.parentFolder.id,
        null,
        file.doc_ext, thumbnailBlobKey).then(() => {
        success('Successfully copied document')
        this.getAllDocument()
      }).catch(() => {
        alert('Document already exist.')
      }).finally(() => {
        this.inheritAndCopyModal.open = false
      })
    },
    inheritDoc (file) {
      let id = file.id
      if (file.associated_versions?.length !== 0) {
        id = file.associated_versions[0].id
      }
      inheritDocument(file.doc_ext, id, this.parentFolder.id, file.doc_name).then(() => {
        success('Successfully inherited document')
        this.getAllDocument()
      }).catch(() => {
        alert('This file is already inherited')
      }).finally(() => {
        this.inheritAndCopyModal.open = false
      })
    },
    handleDocumentUpload () {
      this.previewPopover = false
      this.getAllDocument()
    },
    goBack () {
      // after completing the breadcrumb need to re write this logic(first if conditon only )
      if (!this.window.history.state) {
        this.$router.push('/document-view')
        return
      }
      if (this.breadcrumbs.length > 1) {
        this.breadcrumbs.pop()
        this.folderIdData.pop()
      }
      this.$router.go(-1)
    },
    openFolderPopup () {
      this.newFolderPopup = true
    },
    closeFolderPopup () {
      this.newFolderPopup = false
    },
    openDetail (file) {
      this.detailObject = {
        open: true,
        fileId: file.id,
        view_only: file.view_only,
        state: file.state
      }
      this.$router.replace({ query: { selectedDocId: file.id } })
    },
    closeDetail () {
      this.detailObject = {
        open: false,
        fileId: '',
        view_only: false
      }
      this.$router.replace({ query: { } })
    },
    addBreadcrumb (docName) {
    // Add the document name to the breadcrumbs array
      this.breadcrumbs.push(docName)
    },
    getAllDocument (selectedDocId) {
      const thumbnailReqPayload = []
      let apiCall
      const { documentId } = this.$route.params
      this.loading = true
      if (JSON.parse(this.collaborator)) {
        apiCall = getAllCollabDocument()
      } else {
        apiCall = documentId
          ? getDocumentsByParentId(documentId)
          : getAllParentFolders()
      }

      apiCall
        .then((res) => {
          this.documents = res.core_documents
          if (documentId || this.collaborator) {
            for (const doc of this.documents) {
              if (!doc?.thumbnail_blob_key && !doc?.inherited_from_document?.thumbnail_blob_key) {
                continue
              }
              let S3Key = ''
              if (doc?.inherited_from_doc_id) {
                S3Key = doc?.inherited_from_document?.thumbnail_blob_key
              } else {
                if (doc.associated_versions.length) {
                  S3Key = doc.associated_versions[doc.associated_versions.length - 1].thumbnail_blob_key
                } else {
                  S3Key = doc.thumbnail_blob_key
                }
              }
              const s3Object = {
                fileName: encodeURIComponent(doc?.inherited_from_doc_id ? doc?.inherited_from_document?.doc_name : doc.doc_name),
                S3Key
              }
              if (S3Key) {
                thumbnailReqPayload.push(s3Object)
              }
            }
            this.keyToUrlMap = {}
            generateS3DownloadingUrl({ S3Objects: thumbnailReqPayload }).then((res) => {
              for (const s3Res of res.url) {
                if (!this.keyToUrlMap[s3Res.S3Key]) {
                  this.keyToUrlMap[s3Res.S3Key] = s3Res.url
                }
              }
              this.keyToUrlMap = JSON.parse(JSON.stringify(this.keyToUrlMap))
            }).catch(() => {
              alert('Unable to fetch thumbnail')
            })
          }
          if (selectedDocId) {
            const file = this.documents.find(doc => doc.id === selectedDocId)
            this.detailObject = {
              open: true,
              fileId: file.id,
              view_only: file.view_only,
              state: file.state
            }
          }
          if (!this.collaborator) {
            this.getParentFolder()
          }
        }
        ).catch(() => {
          alert('Unable to fetch documents')
        })
        .finally(() => {
          this.loading = false
        })
    },
    getParentFolder () {
      const { documentId } = this.$route.params
      if (documentId) {
        getDocumentById(documentId)
          .then((res) => {
            this.parentFolder = res.core_documents?.[0] || null
            if (!this.folderIdData.includes(res.core_documents[0]?.id)) {
              this.folderIdData.push(res.core_documents?.[0].id)
              this.addBreadcrumb(res.core_documents[0]?.doc_name)
            }
          })
          .catch(() => {
            this.parentFolder = null
          })
      } else {
        this.parentFolder = null
      }
    }
  },
  mounted () {
    const selectedDocId = this.$route.query.selectedDocId ?? this.$route.query.selecteddocid
    this.getAllDocument(selectedDocId)
  },
  destroyed () {
    // this is to clear commons assets which was created while opening annoatation
    this.clearIconsUrlMap()
  }
}
</script>

<style lang="scss" scoped>
.document-view {
  #upload-docss{
    display: none;
  }
  &-image {
    width: 100%;
    height: 125px;
    object-fit: contain;
    object-position: center;
  }
  &-bar {
    height: 60px;
    margin: -12px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  .break {
    margin-left: -12px;
    margin-right: -12px;
  }
  .upload-btn {
    font-size: 1em;
      padding: 0.35em 1.2em;
      border-radius: 0.3em;
      color: var(--black);
      font-weight: 500;
      border: none;
      box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
      background-color: var(--brand-color);
  }
  &-container {
    height: calc(100% - 60px);
    overflow-y: auto;
    padding: 12px;
  }
  &-folder {
    p {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
    }
  }
  &-card-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    grid-gap: 12px;
  }
  .go-back {
    cursor: pointer;
    width: fit-content;
    p {
      font-size: 16px;
      font-weight: 600;
    }
  }
  .disabled{
    cursor: not-allowed;
    opacity: 0.5;
    div {
      pointer-events: none;
      }
    }

    .upload-document {
  &-modal {
    width: 640px;
    padding: 60px 20px;
    margin: -10px;
    background-color: var(--bg-color);
    background: rgba(var(--brand-rgb), 0.3);
    display: flex;
    justify-content: space-around;
    & > div {
      width: 160px;
      height: 160px;
      font-size: 20px;
      background: var(--bg-color);
      border-radius: 8px;
      flex-direction: column;
      &:hover {
        box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
      }
    }
  }
}
}
</style>
