
export const convertToTimeFormat = (input) => {
  const timeRegex = /^(\d+):(\d+)(?::(\d+))?$/
  const valueRegex = /^(\d+(\.\d+)?)([hm]?)?$/

  if (timeRegex.test(input)) {
    const [, hours, minutes, seconds] = input.match(timeRegex)
    if (hours >= 24) {
      return '00:00:00'
    }
    const validHours = parseInt(hours, 10) || 0
    const validMinutes = parseInt(minutes, 10) || 0
    const validSeconds = parseInt(seconds, 10) || 0

    return `${validHours.toString().padStart(2, '0')}:${validMinutes.toString().padStart(2, '0')}:${validSeconds.toString().padStart(2, '0')}`
  } else if (valueRegex.test(input)) {
    const [, value, , unit] = input.match(valueRegex)

    let hours = 0
    let minutes = 0

    if (unit === 'h') {
      hours = Math.floor(parseFloat(value))
      minutes = Math.round((parseFloat(value) - hours) * 60)
    } else if (unit === 'm') {
      minutes = Math.floor(parseFloat(value))
    } else {
      hours = Math.floor(parseFloat(value))
      minutes = Math.round((parseFloat(value) - hours) * 60)
    }

    hours = Math.min(hours, 24)
    minutes = Math.min(minutes, 60)

    if (minutes === 60) {
      hours += 1
      minutes = 0
    }
    if (hours >= 24) {
      return '00:00:00'
    }

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`
  } else {
    return '00:00:00'
  }
}
// this function returns the value of given number into time format with out a restriction at 24 hrs (input =30 output = "30:00:00")
export const convertToTimeFormatWithoutLimit = (input) => {
  const timeRegex = /^(\d+):(\d+)(?::(\d+))?$/
  const valueRegex = /^(\d+(\.\d+)?)([hm]?)?$/

  if (timeRegex.test(input)) {
    const [, hours, minutes, seconds] = input.match(timeRegex)
    const validHours = parseInt(hours, 10) || 0
    const validMinutes = parseInt(minutes, 10) || 0
    const validSeconds = parseInt(seconds, 10) || 0

    return `${validHours.toString().padStart(2, '0')}:${validMinutes.toString().padStart(2, '0')}:${validSeconds.toString().padStart(2, '0')}`
  } else if (valueRegex.test(input)) {
    const [, value, , unit] = input.match(valueRegex)

    let hours = 0
    let minutes = 0

    if (unit === 'h') {
      hours = Math.floor(parseFloat(value))
      minutes = Math.round((parseFloat(value) - hours) * 60)
    } else if (unit === 'm') {
      minutes = Math.floor(parseFloat(value))
    } else {
      hours = Math.floor(parseFloat(value))
      minutes = Math.round((parseFloat(value) - hours) * 60)
    }

    // hours = Math.min(hours, 24)
    minutes = Math.min(minutes, 60)

    if (minutes === 60) {
      hours += 1
      minutes = 0
    }

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
  } else {
    return '00:00'
  }
}
export const timeStringToDecimal = (timeString = '0:0:0') => {
  if (timeString.length === 1) {
    return 0
  }
  const [hours, minutes] = timeString.split(':').map(Number)
  if (hours + minutes / 60 === null || hours + minutes / 60 === undefined) {
    return 0
  } else { return hours + minutes / 60 }
}
// input 6.5 out put 06:30:00

// export const numberToTimeFormat = (hours) => {
//   return new Date(hours * 60 * 60 * 1000).toISOString().substr(11, 8)
// }
// this fuunction also doing same , but above function has limits
export function numberToTimeFormat (floatValue) {
  if (typeof floatValue !== 'number') {
    return '00:00'
  }

  const hours = Math.floor(floatValue)
  const decimalPart = floatValue - hours
  const minutes = Math.floor(decimalPart * 60)
  // const seconds = Math.floor((decimalPart * 60 - minutes) * 60)

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`
}

// input like 'Mon Jul 31 2023 00:00:00 GMT+0530 (India Standard Time)' and out put will be in the format "Mon Jul 31 2023"
export const dateFormodalConverter = (inputDateString) => {
  const inputDate = new Date(inputDateString)
  const dayName = inputDate.toLocaleString('en-us', { weekday: 'short' })
  const day = inputDate.getDate()
  const month = inputDate.toLocaleString('en-us', { month: 'short' })
  const year = inputDate.getFullYear()
  return `${dayName} ${month} ${day} ${year}`
}

export const calculateTotalDuration = (oneEntryData) => {
  let totalDuration = 0
  oneEntryData.forEach(oneObj => {
    totalDuration += timeStringToDecimal(oneObj?.duration)
  })
  if (totalDuration > 23.99) {
    return false
  } else { return numberToTimeFormat(totalDuration) }
}

export const findProjectedEndDate = (newTime, calendarData, entryDate) => {
  newTime = timeStringToDecimal(newTime)
  if (newTime <= 0) {
    return entryDate // date at whcich it enetered
  } else {
    const holidays = []
    const days = {}
    calendarData.holidays.forEach((element) => {
      holidays.push(new Date(element.date).toDateString())
    })
    calendarData.work_time_hours.days.forEach((element) => {
      days[element.dayId] = element.working_hours
    })
    const projectedEndDate = entryDate
    while (newTime >= 0) {
      projectedEndDate.setDate(projectedEndDate.getDate() + 1)
      if (!holidays.includes(projectedEndDate.toDateString())) {
        newTime = newTime - days[projectedEndDate.getDay()]
      } else {
      }
    }
    return projectedEndDate
  }
}
