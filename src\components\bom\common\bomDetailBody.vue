<template>
  <div>
    <div v-if="loadingBomData" class="p-5 h-center">
      <loading-circle />
    </div>
    <template v-else>
      <div v-for="(bomItem, index) in bomItems" :key="index">
        <div
          class="bom-detail-row bom-detail-body"
          :class="{
            open: bomItem.open,
            close: !bomItem.open,
            obsolete: bomItem.Material_status === 2,
          }"
        >
          <div
            v-if="
              bomItem.material_product_code?.product_code &&
              bomItem.associated_bom_version_id
            "
            class="clickable-porduct-code"
            v-tooltip="'Click to view associated BOM'"
            @click="openAssociateBom(bomItem, index)"
          >
            {{ bomItem.material_product_code?.product_code }}
          </div>
          <div
            v-else-if="
              bomItem.material_product_code?.product_code &&
              !bomItem.associated_bom_version_id
            "
            v-tooltip="'No BOM associated'"
          >
            {{ bomItem.material_product_code?.product_code }}
          </div>
          <div v-else>--</div>
          <div v-overflow-tooltip>{{ bomItem.custom_material_id || "--" }}</div>
          <div v-overflow-tooltip v-if="!materialComponent">{{ bomItem.plm_material_id || "--" }}</div>
          <div v-overflow-tooltip v-if="!materialComponent">{{ bomItem.erp_material_id || "--" }}</div>
          <div v-overflow-tooltip>{{ bomItem.material_unit_details?.name || "--" }}</div>
          <div v-overflow-tooltip>
            {{ bomItem.material_description || "--" }}
          </div>
          <div v-overflow-tooltip>{{ bomItem.unit_size || "--" }}</div>
          <div v-overflow-tooltip>{{ bomItem.quantity || "--" }}</div>
          <div v-overflow-tooltip>{{ bomItem.material_name || "--" }}</div>
          <div v-overflow-tooltip v-if="!materialComponent">{{ bomItem.lead_time || "--" }}</div>
          <div v-overflow-tooltip v-if="!materialComponent">{{ bomItem.remarks || "--" }}</div>
          <div v-overflow-tooltip v-if="!materialComponent"><img v-tooltip="'View Standard Material Form'" v-if="bomItem.formId !== null || bomItem.overridden_material_fields !== null" class="pointer mx-1" src="~@/assets/images/icons/open-eye.svg" alt=""  width="12" @click="viewMaterial(bomItem)"/>
            <img v-tooltip="'No Standard Material Form Created'" v-else class="pointer mx-1" src="~@/assets/images/icons/hide-eye.svg" width="12" alt=""></div>
        </div>
        <div v-if="bomItem.open" class="bom-child pl-4">
          <div class="bom-title v-center">
            <div>
              {{ bomItem.associated_bom_name }} (V-{{ bomItem.associated_bom_version }})
              <div class="bom-state-chip" :class="getBomState">
            {{ getBomState }}
            </div>
            </div>
            <div  v-if="!bomItem.associated_bom_version_active">
             <span class="bom-out-of-date">Associated BOM version is out of date</span>
             <router-link  :to="`/bom/compare/${bomItem?.associated_bom_id}?bvid=${bomItem?.associated_bom_version_id}&bvno=${bomItem?.associated_bom_version}&bPvid=${bomVersionId}&mtlid=${bomItem.material_id}&usize=${bomItem.unit_size}&compareAndUpdate=${bomVersionId}&prodCode=${$route.params.productCode}`">
              <button v-if="showEdit" class="bom-update-button">Update</button></router-link>
            </div>
          </div>
          <bom-detail-head />
          <bom-detail-body
            v-if="bomItem.open"
            :bomId="bomItem.associated_bom_id"
            :bomVersionId="bomItem.associated_bom_version_id"
          />
        </div>
      </div>
    </template>
    <Modal
        :open="openDrawer"
        @close="openDrawer = false"
        :title="'Standard Material Form'"
        >
        <div :style="{ width: '550px' }">
          <create-material-form :bomList="true" v-if="openDrawer" :createMaterialValues="standardMaterialData"> </create-material-form>
        </div>
        </Modal>
  </div>
</template>

<script>
import { GetAllBomItemsByBomVersionId, GetAllProjectBomItemsByBomVersionId } from '@/api'
import loadingCircle from '../../common/loadingCircle.vue'
import bomDetailBody from './bomDetailBody.vue'
import BomDetailHead from '../common/bomDetailHead.vue'
import Modal from '../../common/modal.vue'
import createMaterialForm from '../../../views/BOM/bomForm/createMaterialForm.vue'
import { mapGetters } from 'vuex'
import config from '@/config'
export default {
  components: { loadingCircle, bomDetailBody, BomDetailHead, Modal, createMaterialForm },
  name: 'bomDetailBody',
  props: {
    bomId: {
      type: [Number, String],
      default: () => {}
    },
    bomVersionId: {
      type: [Number, String],
      default: () => {}
    },
    showEdit: { type: Boolean, required: true },
    materialComponent: { type: Boolean, default: false }
  },
  data () {
    return {
      loadingBomData: false,
      bomItems: [],
      bomState: null,
      standardMaterialData: null,
      openDrawer: false
    }
  },
  watch: {
    bomVersionId () {
      this.getBomByIdData()
    }
  },
  computed: {
    ...mapGetters(['isOnProjectLevel']),
    getBomState () {
      return config.STATE_MAP[this.bomState]
    },
    getBomDataMethod () {
      return this.isOnProjectLevel ? GetAllProjectBomItemsByBomVersionId : GetAllBomItemsByBomVersionId
    }
  },
  methods: {
    openAssociateBom (bomItem, index) {
      this.bomState = bomItem?.associated_bom_state
      this.bomItems[index].open = !this.bomItems[index].open
    },
    viewMaterial (bomItem) {
      this.openDrawer = !this.openDrawer
      this.standardMaterialData = bomItem
    },
    getBomByIdData () {
      if (!this.bomId) return
      this.loadingBomData = true
      this.getBomDataMethod(this.bomVersionId)
        .then((res) => {
          this.bomItems = res.bom_items.map((item) => {
            return {
              ...(item.core_material || {}),
              material_id: item.material_id,
              quantity: item.quantity,
              total_price: item.total_price,
              unit_size: item.unit_size,
              remarks: item.remarks,
              associated_bom_id: item?.associated_bom_version?.core_bom?.id || null,
              associated_bom_name: item?.associated_bom_version?.core_bom?.name || null,
              associated_bom_state: item?.associated_bom_version?.core_bom?.state || null,
              associated_bom_version: item?.associated_bom_version?.version_no || null,
              associated_bom_version_id: item?.associated_bom_version?.id || null,
              associated_bom_version_active: item?.associated_bom_version?.active || null,
              Material_status: item?.core_material?.status,
              open: false,
              formId: item.core_material.core_form ? item.core_material.core_form.id : null,
              overridden_material_fields: item.overridden_material_fields || null
            }
          })
          this.loadingBomData = false
          this.$emit('bomItemLength', res?.bom_items?.length)
        })
        .catch(() => {
          this.loadingBomData = false
        }).finally(() => {
          this.loadingBomData = false
        })
    }
  },
  created () {
    this.getBomByIdData()
  }
}
</script>

<style>
.bom-state-chip {
  display: inline-block;
    font-size: 12px;
    line-height: 1;
    padding: 6px 12px;
    border-radius: 2em;
    font-weight: 500;
    &.CHECKIN {
      border: 1px solid var(--success);
      color: var(--success);
      background: #00ff002a;
      margin-inline: 1em;
    }
    &.CHECKOUT {
      border: 1px solid var(--warning);
      color: var(--warning);
      background: #ffff002a;
      margin-inline: 1em;
    }
    &.LOCK {
      border: 1px solid var(--alert);
      color: var(--alert);
      background: #ff00002a;
      margin-inline: 1em;
    }
    &.OBSOLETE {
      border: 1px solid var(--alert);
      color: var(--alert);
      background: #ff00002a;
      margin-inline: 1em;
    }
  }
.bom-update-button{
  background: rgba(var(--brand-rgb));
  cursor: pointer;
  padding: 4px 10px 4px 10px;
  border: 1px;
  border-radius: 4px;
  margin-left: 20px;
}
.obsolete{
  color: red;
  border-color: red;
}
</style>
