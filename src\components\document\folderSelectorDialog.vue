<template>
    <div class="document-view">
      <div class="document-view-bar v-center space-between px-3">
        <h1 class="weight-600 xxxl">Select Folder</h1>
        <div class="v-center">
          <div class="v-center" v-if="uploadDocButton">
            <button @click="openFolderPopup" v-if="canCreate" class="btn btn-black mx-3">
              Create Folder
            </button>
          </div>
          <div class="input-group search m">
            <input
              type="text"
              v-model="searchKeyWord"
              placeholder="Search by Folder,File name"
            />
          </div>
        </div>
      </div>
      <div v-if="loading" class="document-view-container center">
        <loading-circle />
      </div>
      <div v-else class="document-view-container">
        <div class="go-back v-center mb-3" :class="!parentFolder ?'disabled':''">
          <div class="flex v-center" @click="goBack">
            <img
            class="mr-1"
            src="~@/assets/images/icons/arrow-back.svg"
            width="20px"
            />
            <p>Go back</p>
          </div>
        </div>
        <div class="document-view-folder">
          <p>Folders</p>
          <div v-if="filteredDocuments.length" class="document-view-card-container">
            <template v-for="folder in filteredDocuments">
              <folder-card
                @push-route="handleFolderSelect(folder)"
                :key="folder.id"
                v-if="folder.folder"
                :useStateRouting="true"
                :folder="folder"
                @rename="getAllDcoument"
                @delete="getAllDcoument"
              />
            </template>
          </div>
          <span v-else>No folders were found</span>
        </div>
      </div>
      <div>
        <div  class="footer">
          <div class="footer-box">
              <div class="footer-box-list" v-if="selectedFolder.id"
               v-overflow-tooltip> {{selectedFolder?.name}}
              </div>
        </div>
        <div class="flex flex-end py-3 l mr-4">
          <button class="btn btn-black mr-3 pointer" @click="closeDocs">CANCEL</button>
      <button v-if="purpose === 'attachment'" class="btn pointer" @click="$emit('folder-selected',selectedFolder)">
        SELECT
      </button>
        </div>
      </div>
      </div>
      <modal
        :open="newFolderPopup"
        @close="closeFolderPopup"
        title="Create New Folder"
      >
        <create-folder-form v-if="newFolderPopup"  :buttonDisabled="loading"   :open="newFolderPopup" @close="closeFolderPopup" @create="getAllDcoument" />
      </modal>
    </div>
  </template>

<script>
import Modal from '../common/modal.vue'
import CreateFolderForm from './createFolderForm.vue'
import {
  getAllParentFolders,
  getDocumentsByParentIdMatNull,
  getDocumentById,
  getDocumentsByParentIdMatNullWithToken,
  getAllParentFoldersWithToken,
  getDocumentByIdWithToken
} from '@/api'
import LoadingCircle from '../common/loadingCircle.vue'
import FolderCard from './folderCard.vue'
import { alert } from '../../plugins/notification'
import { mapGetters } from 'vuex'
export default {
  name: 'documentSelector',
  props: {
    linkedDocs: {
      type: Array,
      default: () => ([])
    },
    purpose: {
      type: String,
      default: 'attachment'
    },
    uploadDocButton: {
      type: Boolean,
      default: true
    },
    // this is coming only from scheduler,
    token: {
      type: String,
      default: null
    },
    // this is coming only from scheduler,
    projectId: {
      type: Number,
      default: null
    },
    materialAttachment: {
      type: Boolean,
      default: false
    }
  },
  components: {
    Modal,
    CreateFolderForm,
    LoadingCircle,
    FolderCard
  },
  data: () => ({
    selectedFolder: {
      id: null,
      name: ''
    },
    routeHistory: [],
    searchKeyWord: '',
    newFolderPopup: false,
    documents: [],
    selectedFile: {},
    loading: false,
    parentFolder: null,
    activeFileId: null,
    documentToBeCopied: null,
    documentToBeInherited: null,
    detailObject: {
      open: false,
      fileId: ''
    },
    attachedList: [],
    disableInheritButton: false,
    disableCopyButton: false,
    initialDocs: []
  }),
  computed: {
    ...mapGetters(['user', 'collaborator', 'isOnProjectLevel']),
    canCreate () {
      if (this.purpose === 'attachment' && !this.collaborator) {
        if (this.isOnProjectLevel) {
          if (this.user.projectLevelRole === 'ADMIN' || this.user.projectLevelRole === 'COLLABORATOR') {
            return true
          }
        } else {
          if (this.user.tenantLevelRole === 'ADMIN' || this.user.tenantLevelRole === 'COLLABORATOR') {
            return true
          }
        }
      }
      return false
    },
    filteredDocuments () {
      return this.documents.filter((doc) => {
        return (
          doc?.doc_name
            ?.toLowerCase()
            .includes(this.searchKeyWord?.toLowerCase()) ||
          doc?.description
            ?.toLowerCase()
            .includes(this.searchKeyWord?.toLowerCase())
        )
      })
    },
    getAllFolders () {
      return this.documents.filter((doc) => {
        return doc.folder && (doc.state === 1 || doc.state === 2 || doc.state === 3)
      })
    },
    getAllFiles () {
      return this.documents.filter((doc) => {
        return !doc.folder && (doc.state === 1 || doc.state === 2 || doc.state === 3)
      })
    },
    latestActiveFileId () {
      return (this.activeFiledId)
    }
  },

  watch: {
    routeHistory: {
      handler () {
        this.getAllDcoument()
      },
      immediate: true
    }
  },
  methods: {
    handleFolderSelect (folder) {
      this.selectedFolder = {
        id: folder.id,
        name: folder.doc_name
      }
      this.routeHistory.push(this.selectedFolder)
    },
    inheritDoc () {
      this.disableInheritButton = true
      this.$emit('inherit-doc', this.documentToBeInherited)
    },
    copyDoc () {
      this.disableCopyButton = true
      this.$emit('copy-doc', this.documentToBeCopied)
    },
    goBack () {
      this.routeHistory.pop()
      if (this.routeHistory.length) {
        this.selectedFolder = {
          id: this.routeHistory[this.routeHistory.length - 1].id,
          name: this.routeHistory[this.routeHistory.length - 1].doc_name
        }
      } else {
        this.selectedFolder = {
          id: null,
          name: ''
        }
      }
    },
    openFolderPopup () {
      this.newFolderPopup = true
    },
    closeFolderPopup () {
      this.newFolderPopup = false
    },
    openDetail (file) {
      this.detailObject = {
        open: true,
        fileId: file.id
      }
    },
    closeDetail () {
      this.detailObject = {
        open: false,
        fileId: ''
      }
    },
    getAllDcoument () {
      const document = this.routeHistory.at(-1)
      const documentId = document?.id ? document.id : null
      this.loading = true
      let apiCall = null
      if (!this.token) {
        apiCall = documentId
          ? getDocumentsByParentIdMatNull(documentId, this.purpose !== 'attachment' ? 'tenant' : null, this.materialAttachment)
          : getAllParentFolders(this.purpose !== 'attachment' ? 'tenant' : null)
      } else {
        apiCall = documentId
          ? getDocumentsByParentIdMatNullWithToken(documentId, this.projectId, this.token)
          : getAllParentFoldersWithToken(this.projectId, this.token)
      }
      apiCall
        .then((res) => {
          this.documents = res.core_documents
        })
        .finally(() => {
          this.loading = false
        })
      this.getParentFolder()
    },
    getParentFolder () {
      const document = this.routeHistory.at(-1)
      const documentId = document?.id ? document.id : null
      if (documentId) {
        const apicall = this.token
          ? getDocumentByIdWithToken(documentId, this.projectId, this.token)
          : getDocumentById(documentId, this.purpose !== 'attachment' ? 'tenant' : null)
        apicall.then((res) => {
          this.parentFolder = res.core_documents?.[0] || null
        })
          .catch((err) => {
            console.log(err)
            this.parentFolder = null
          })
      } else {
        this.parentFolder = null
      }
    },
    // here the file contain full data of selected document
    addToAttachedList (file) {
      if (this.purpose === 'copy') {
        this.documentToBeCopied = file
        return
      }
      if (this.purpose === 'inherit') {
        this.documentToBeInherited = file
        return
      }
      // checking the given selelcted material is added or not
      const duplicate = this.attachedList.filter((element, index) => {
        if (element.core_document?.id === file.id) {
          element.index = index
          return (element)
        }
      })
      if (duplicate?.length > 0) {
        // if the flag is in deleted state then no need to add the doc again
        if (duplicate[0]?.flag === 'deleted') {
          // duplicate[0]?.index shows where the doc data belongs in attachedlist
          this.attachedList[duplicate[0]?.index].flag = 'existing'
          this.$set(this.attachedList, duplicate[0]?.index, this.attachedList[duplicate[0]?.index])
        } else { alert(`${file?.doc_name} has  already attached`) }
      } else {
        file.flag = 'new'
        file.core_document = { ...file }
        this.attachedList.push(file)
      }
    },
    deleteDocAttach (index, doc) {
      this.attachedList[index].flag = 'deleted'
      this.$set(this.attachedList, index, this.attachedList[index])
    },
    closeDocs () {
      this.attachedList.forEach((item) => {
        if (item.flag) {
          delete item.flag
        }
      })
      this.attachedList = [...this.initialDocs]
      this.$emit('close')
    },
    keyPress (e) {
      if (e instanceof KeyboardEvent && e.code === 'Enter') {
        switch (this.purpose) {
        case 'copy':
          this.copyDoc()
          break
        case 'inherit':
          this.inheritDoc()
          break
        default:
          break
        }
      }
    }
  },
  mounted () {
    this.getAllDcoument()
    // this.attched list stores the selelcted docs, for editing linkedDocs carries doc data from edit materail to here
    this.attachedList = [...this.linkedDocs]
    this.initialDocs = [...this.linkedDocs]
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
  }
}
</script>

  <style lang="scss" scoped>
  .document-view {
    min-width: 70vw;
    height: 70vh;
    background-color: var(--white);
    &-image {
      width: 100%;
      height: 125px;
      object-fit: contain;
      object-position: center;
    }
    &-bar {
      background-color: red;
      height: 60px;
      margin-bottom: 0;
      background: var(--bg-color);
      border-bottom: var(--border);
    }
    .break {
      margin-left: -12px;
      margin-right: -12px;
    }
    .upload-btn {
      font-size: 1em;
        padding: 0.35em 1.2em;
        border-radius: 0.3em;
        color: var(--black);
        font-weight: 500;
        border: none;
        box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
        background-color: var(--brand-color);
    }
    &-container {
      height: 52vh;
      overflow-y: auto;
      padding: 12px;
    }
    &-folder {
      p {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
      }
    }
    &-card-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      grid-gap: 12px;
    }
    .go-back {
      cursor: pointer;
      width: fit-content;
      p {
        font-size: 16px;
        font-weight: 600;
      }
    }
    .disabled{
      cursor: not-allowed;
      opacity: 0.5;
      div {
        pointer-events: none;
        }
      }
    .footer{
  &-box{
    display:flex;
    justify-content: end;
    padding-inline:40px ;
    gap:10px;
    flex-wrap: wrap;
    &-list{
      border-radius: 3px 15px 15px 3px;
      background-color: var(--brand-color);
      padding: 5px 25px 5px 5px;
    max-width: 150px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    cursor:context-menu;
    position: relative;
    &-close{
      position: absolute;
      top:5px;
      right:4px;
      cursor: pointer;
    }
    }
  }
    }
  }
  </style>
