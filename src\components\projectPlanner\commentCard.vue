<template>
  <div class="task-comment-card">
    <div class="task-comment-card--header">
      <div class="task-comment-card--header--left">
        <div class="task-comment-card--header--left--img">
          <img src="~@/assets/images/icons/user-icon.svg" alt="" />
        </div>
        <div class="task-comment-card--header--left--name">
          {{userName}}
        </div>
      </div>
      <div class="task-comment-card--header--right">
        <div class="task-comment-card--header--right--date">
          {{ createDate }}
        </div>
        <div class="task-comment-card--header--right--reply" v-if="!view">
          <img src="~@/assets/images/icons/more-icon.svg" alt="" />
          <div class="task-comment-card--header--right--reply-popup">
            <div
              class="task-comment-card--header--right--reply-option"
              :class="{
                inactive: level === 2,
              }"
              @click="openReply"
            >
              Reply
            </div>
            <!-- <div
              class="task-comment-card--header--right--reply-option"
              :class="{
                inactive: isOwner
              }"
              @click="openEditComment"
            >
              Edit
            </div>
            <div
              class="task-comment-card--header--right--reply-option"
              :class="{
                inactive: isOwner
              }"
              @click="deleteComment"
            >
              Delete
            </div> -->
          </div>
        </div>
      </div>
    </div>
    <div class="task-comment-card--body">
      <div
        class="task-comment-card--body--text break-text"
        v-if="!editComment"
        v-html="getHtmlComment"
      />
      <div class="task-comment-card--body--text" v-else>
        <textarea
          class="task-comment-card--body--text-input"
          placeholder="Edit your comment"
          v-model="editCommentText"
        />
        <div class="flex flex-end">
          <button class="btn btn-black mx-2" @click="closeEditComment">
            Cancel
          </button>
          <button class="btn" @click="updateComment">Save</button>
        </div>
      </div>
      <div class="task-comment-card--body-reply" v-if="addReply">
        <textarea
          class="task-comment-card--body-reply--input"
          placeholder="Add a reply"
          v-model="comment"
        />
        <div class="flex flex-end">
          <button class="btn btn-black mx-2" @click="closeReply">Cancel</button>
          <button class="btn" @click="sendComment">Send</button>
        </div>
      </div>
      <div
        class="task-comment-card--body-reply-loading dot-loading"
        v-if="loading"
      ></div>
      <div
        class="task-comment-card--body-reply-count"
        v-if="!loading && commentData.comments_aggregate.aggregate.count"
        @click="expand = !expand"
      >
        {{ commentData.comments_aggregate.aggregate.count }} replies
      </div>
      <div class="task-comment-card--body--reply mt-2" v-if="expand">
        <task-comment-card
          v-for="item in replies"
          :key="item.id"
          :view="view"
          :comment-data="item"
          :level="level - 1"
          @updateComment="getComments"
        />
      </div>
    </div>
  </div>
</template>

<script>
// import { Duration } from '@/utils/date'
import { Duration } from '../../utils/date'
import {
  addTaskComment,
  getAllTaskComments,
  updateComment,
  deleteComment
} from '@/api'
import { mapGetters } from 'vuex'
import ConfirmationDialog from '@/plugins/confirmationDialog'

export default {
  name: 'taskCommentCard',
  filters: {
    duration (value) {
      return Duration(value)
    }
  },
  props: {
    commentData: {
      type: Object,
      default: () => {}
    },
    level: {
      type: Number,
      default: 0
    },
    view: {
      type: Boolean,
      default: false
    },
    showEdit: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      comment: '',
      loading: false,
      replies: [],
      expand: false,
      addReply: false,
      editComment: false,
      editCommentText: '',
      createDate: ''
    }
  },
  computed: {
    ...mapGetters(['user', 'getUserById']),
    isOwner () {
      return this.user.id === this.commentData.created_by
    },
    userName () {
      const user = this.getUserById(this.commentData.created_by)
      const userDetail = user?.associated_user
      return ((userDetail?.first_name || '') + ' ' + (userDetail?.last_name || '')).trim()
    },
    getHtmlComment () {
      const versionLink = this.commentData.bom_version ? `<span style="font-size: 12px;
    color: #1e92ff;
    cursor: pointer">@${
        this.commentData.bom_version.active ? 'Latest' : 'V-' + this.commentData.bom_version.version_no
      }</span>` : ''
      return versionLink + ' ' + this.commentData.comment.replace(/\n/g, '<br />')
    }
  },
  methods: {
    openReply () {
      this.addReply = true
      this.editComment = false
      this.comment = ''
    },
    closeReply () {
      this.addReply = false
      this.editComment = false
      this.comment = ''
    },
    openEditComment () {
      this.editComment = true
      this.addReply = false
      this.editCommentText = this.commentData.comment
    },
    closeEditComment () {
      this.editComment = false
      this.addReply = false
      this.editCommentText = ''
    },
    sendComment () {
      const taskId = this.commentData.task_id
      if (!this.comment.trim()) return
      addTaskComment(
        'task',
        taskId,
        this.comment,
        this.commentData.id,
        null
      ).finally(() => {
        this.closeReply()
        this.closeEditComment()
        this.getComments()
        this.$emit('updateComment')
      })
    },
    updateComment () {
      if (!this.editCommentText) return
      updateComment(this.commentData.id, this.editCommentText).finally(() => {
        this.closeReply()
        this.closeEditComment()
        this.$emit('updateComment')
      })
    },
    deleteComment () {
      ConfirmationDialog(
        'Are you sure you want to delete this comment?',
        (res) => {
          if (res) {
            deleteComment(this.commentData.id).finally(() => {
              this.closeReply()
              this.closeEditComment()
              this.$emit('updateComment')
            })
          }
        }
      )
    },
    getComments () {
      this.loading = true
      const taskId = this.commentData.task_id
      getAllTaskComments(taskId, this.commentData.id)
        .then((res) => {
          this.replies = res.core_comments || []
        })
        .finally(() => {
          this.loading = false
        })
    },
    commentCreatedOn () {
      this.createDate = new Date(this.commentData.created_on).toLocaleString().split(',')[0]
      return this.createDate
    }
  },
  mounted () {
    if (!this.commentData.id) {
      this.getComments()
    }
    if (this.expand) {
      this.getComments()
    }
    this.commentCreatedOn()
  },
  watch: {
    expand (value) {
      if (value) {
        this.getComments()
      }
    }
  }
}
</script>

<style lang="scss" scoped >
.task-comment-card {
  width: 100%;
  border-radius: 4px;
  background-color: #ffffff;
  padding: 10px;
  padding-right: 0;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 10px;
  &:last-child {
    border-bottom: none;
  }
  .task-comment-card--header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    .task-comment-card--header--left {
      display: flex;
      align-items: center;
      flex-grow: 1;
      .task-comment-card--header--left--img {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: #e0e0e0;
        margin-right: 8px;
        padding: 2px;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .task-comment-card--header--left--name {
        width: calc(100% - 32px);
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        font-style: normal;
        font-weight: 600;
        font-size: 14px;
        line-height: 16px;
        color: var(--text-color);
      }
    }
    .task-comment-card--header--right {
      display: flex;
      align-items: center;
      .task-comment-card--header--right--date {
        font-style: normal;
        font-weight: normal;
        font-size: 12px;
        line-height: 14px;
        color: #9e9e9e;
        margin-right: 8px;
      }
      .task-comment-card--header--right--reply {
        width: 24px;
        height: 24px;
        cursor: pointer;
        position: relative;
        &:hover {
          .task-comment-card--header--right--reply-popup {
            display: block;
          }
        }
        &-popup {
          position: absolute;
          top: 100%;
          right: 0;
          background-color: #ffffff;
          box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
          border-radius: 4px;
          padding: 8px;
          z-index: 1;
          display: none;
        }
        &-option {
          width: 100px;
          font-size: 12px;
          line-height: 14px;
          color: #484848;
          padding: 4px;
          cursor: pointer;
          &:hover {
            background-color: #e0e0e0;
          }
          &.inactive {
            color: #9e9e9e;
            cursor: not-allowed;
            pointer-events: none;
          }
        }
      }
    }
  }
  .task-comment-card--body {
    &-reply {
      margin-top: 10px;
      .task-comment-card--body-reply--input {
        font-family: "Poppins", sans-serif;
        width: 100%;
        height: 60px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 8px;
        font-size: 12px;
        color: #000;
        outline: none;
        resize: none;
      }
      & > div > button {
        font-size: 10px;
      }
    }
    .task-comment-card--body--text {
      font-size: 14px;
      line-height: 16px;
      color: var(--text-color);
      &-input {
        font-family: "Poppins", sans-serif;
        width: 100%;
        height: 60px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 8px;
        font-size: 12px;
        color: #000;
        outline: none;
        resize: none;
      }
      & > div > button {
        font-size: 10px;
      }
    }
    &-reply-loading {
      margin-top: 10px;
    }
    &-reply-count {
      font-size: 12px;
      line-height: 14px;
      color: #9e9e9e;
      margin-top: 10px;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
        color: var(--brand-color-1);
      }
    }
    .task-comment-card--body--reply {
      border-left: 1px solid #e0e0e0;
      padding-left: 10px;
    }
  }
  .dot-loading {
    height: 10px;
    width: 10px;
    background-color: var(--white);
    border-radius: 50%;
    animation: load 2s linear infinite;
    @keyframes load {
      0% {
        box-shadow: 14px 0 0 var(--white), 28px 0 0 var(--white),
          42px 0 0 var(--white);
      }
      16.7% {
        box-shadow: 14px 0 0 #e0e0e0, 28px 0 0 var(--white),
          42px 0 0 var(--white);
      }
      33.33% {
        box-shadow: 14px 0 0 #e0e0e0, 28px 0 0 #e0e0e0, 42px 0 0 var(--white);
      }
      50% {
        box-shadow: 14px 0 0 #e0e0e0, 28px 0 0 #e0e0e0, 42px 0 0 #e0e0e0;
      }
      66.7% {
        box-shadow: 14px 0 0 #e0e0e0, 28px 0 0 #e0e0e0, 42px 0 0 var(--white);
      }
      83.33% {
        box-shadow: 14px 0 0 #e0e0e0, 28px 0 0 var(--white),
          42px 0 0 var(--white);
      }
      100% {
        box-shadow: 14px 0 0 var(--white), 28px 0 0 var(--white),
          42px 0 0 var(--white);
      }
    }
  }
}
</style>
