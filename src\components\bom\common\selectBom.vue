<template>
  <div class="select-bom">
    <div v-if="bomListLoading" class="select-bom-container">
      <loading-circle />
    </div>
    <div
      v-else-if="bomList.length === 0"
      class="select-bom-container no-bom-found"
    >
      <span class="text-muted">No BOM found</span>
    </div>
    <div v-else class="select-bom-container">
      <div
        :class="{
          selected: bom.id === tempSelectBom?.id,
          disable: bom.state !== 2,
        }"
        class="select-bom-option"
        v-for="bom in bomList"
        :key="bom.id"
        @click="selectedBomTemp(bom)"
        v-overflow-tooltip
        :title="bom.state === 2 ? '' : 'BOM is not approved yet'"
      >
        <img
          v-if="bom.state === 2"
          src="~@/assets/images/icons/checkin-icon.svg"
          class="select-bom-option--icon"
          v-tooltip="'Bom is checked in'"
        />
        <img
          v-if="bom.state === 3"
          src="~@/assets/images/icons/checkout-icon.svg"
          class="select-bom-option--icon"
          v-tooltip="'Bom is checked out'"
        />
        <img
          v-if="bom.state === LOCK"
          src="~@/assets/images/icons/locked-icon.svg"
          class="select-bom-option--icon"
          v-tooltip="'Bom is locked'"
        />
        {{ bom.name }}
      </div>
    </div>
    <div class="v-center m select-bom-buttonBox" v-if="showCloseButton">
      <div class="select-bom-vesrion v-center mt-3" v-overflow-tooltip>
        <span class="" v-if="tempSelectBom?.id">{{ tempSelectBom?.name }}</span>
        <span class="ml-5" v-for="bomversion in selectedBomVersions" :key="bomversion.id">V-{{ bomversion.version_no
            }}{{ bomversion.active ? " (Latest)" : ""
            }}</span>
        <!-- <select
          name=""
          id=""
          v-model="tempSelectBom.versionId"
          @change="selectCurrentVersion"
          v-if="tempSelectBom?.id"
        >
          <option
            v-for="bomversion in selectedBomVersions"
            :key="bomversion.id"
            :value="bomversion.id"
            :disabled="bomversion?.state !== 2"
          >
            V-{{ bomversion.version_no
            }}{{ bomversion.active ? " (Latest)" : ""
            }}{{ bomversion.released ? " (Relesed)" : "" }}
          </option>
        </select> -->
      </div>
      <div>
        <button
          class="btn btn-black mt-3"
          @click="removeBom"
          v-if="showRemoveAssBom"
        >
          Remove
        </button>
        <button
          v-else-if="cyclicDepChecking"
          class="btn mt-3 loeading-button "
          :class="{ 'loading': cyclicDepChecking }"
          :disabled="cyclicDepChecking || cyclicDependency "
        >
       checking cyclic dependency <span class="spinner"></span>
        </button>
        <button
          v-else
          class="btn mt-3"
          :class="{ 'loading': cyclicDepChecking }"
          :disabled="!tempSelectBom.versionId || !tempSelectBom.id || cyclicDependency"
          @click="selectBom"
        >
          Select
        </button>
        <button class="btn btn-black mt-3 ml-5" @click="$emit('close')">
          Close
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { GetAllBomList, GetAllBomVersionListByBomId, getBomDatawithAssociatedBoms } from '@/api'
import loadingCircle from '../../common/loadingCircle.vue'
import { alert } from '@/plugins/notification'
import { mapGetters } from 'vuex'
import Config from '@/config'
export default {
  components: { loadingCircle },
  props: {
    product_code: {
      type: [String, Number],
      default: null
    },
    selectedBomId: {
      type: [String, Number],
      default: null
    },
    showCloseButton: {
      type: Boolean,
      default: false
    },
    selectedBomVersionId: {
      type: [String, Number],
      default: null
    },
    parentProdCode: {
      type: [String, Number],
      default: null
    }
  },
  data () {
    return {
      bomList: [],
      bomListLoading: false,
      selectedBom: null,
      tempSelectBom: {
        versionId: null
      },
      selectedBomVersions: null, // array of versions of selelcted bom
      stateForRemoval: true, // initially value  is true which means  which selects current version for removal else set the active version
      cyclicDependency: false,
      cyclicDepChecking: false,
      LOCK: Config.BOM_STATE_MAP.LOCK
    }
  },
  methods: {
    selectedBomTemp (bom) {
      this.tempSelectBom = bom
      this.getAllBomversions()
    },
    selectBom () {
      try {
        const bom = this.tempSelectBom
        if (this.selectedBomVersionId === bom?.versionId) {
          this.selectedBom = null
          this.$emit('selectBom', {})
          this.$emit('close')
          return
        }
        this.selectedBom = bom.id
        this.$emit('selectBom', bom)
        this.$emit('close')
      } catch (err) {
        console.log(err)
      }
    },
    getBomList () {
      if (!this.product_code) return
      this.bomListLoading = true
      GetAllBomList(this.product_code)
        .then((res) => {
          this.bomList = res.core_bom.map((bom) => {
            if (this.selectedBomId === bom.id) {
              this.tempSelectBom.id = bom.id
              this.tempSelectBom.name = bom.name
              this.tempSelectBom.versionId = bom.bom_versions?.[0]?.id
              this.getAllBomversions()
            }
            return {
              id: bom.id,
              name: bom.name,
              version: bom.bom_versions?.[0]?.version_no,
              versionId: bom.bom_versions?.[0]?.id,
              state: bom?.state,
              total_cost: bom.bom_versions?.[0]?.total_cost,
              sale_price: bom.bom_versions?.[0]?.sale_price,
              productCode: this.product_code
            }
          })
          this.bomListLoading = false
        })
        .catch(() => {
          this.bomListLoading = false
        })
    },
    getAllBomversions () {
      GetAllBomVersionListByBomId(this.tempSelectBom?.id).then((data) => {
        this.selectedBomVersions = data?.bom_versions.filter((item) => item.active)
        // by this condition we are ensuring that if no bom id is there previously it should show the latest version
        if (
          this.stateForRemoval &&
          (this.selectedBomId || false)
        ) {
          this.tempSelectBom.versionId = this.selectedBomVersionId
          this.stateForRemoval = false
        } else {
          for (
            let index = 0;
            index < this.selectedBomVersions?.length;
            index++
          ) {
            if (this.selectedBomVersions[index]?.active) {
              this.tempSelectBom.versionId =
                this.selectedBomVersions[index]?.id
              this.selectCurrentVersion(this.tempSelectBom.versionId)
            }
          }
        }
      })
    },
    selectCurrentVersion (e) {
      // no need to check cyclic dependency in project level
      if (this.isOnProjectLevel) {
        this.tempSelectBom.versionId = parseInt(e.target.value)
      } else {
        this.cyclicDepChecking = true
        this.checkforCyclicDependency1(e?.target?.value || e, this.$route.params.productCode).then(response => {
          if (response) {
            this.cyclicDepChecking = false
            this.cyclicDependency = true
            alert('Bom triggered with cyclic dependency')
          } else {
            this.cyclicDepChecking = false
            this.cyclicDependency = false
          }
        })
          .catch((response) => {
            this.cyclicDepChecking = false
          })
      }
    },
    removeBom () {
      this.selectedBom = null
      this.$emit('selectBom', {})
      this.$emit('close')
    },
    // as per the written logic it will check if a current bom's product code is coming  or not  as a  material in any associated bom
    checkforCyclicDependency1 (bomVersionId, parentProdCode) {
      return new Promise((resolve, reject) => {
        async function checkforCyclicDependency (bomVersionId, index) {
          await getBomDatawithAssociatedBoms(bomVersionId, false).then(async data => {
            for (let i = 0; i < data.bom_items.length; i++) {
              if (data.bom_items[i]?.core_material?.product_code === parentProdCode) {
                resolve(true)
                return true
              } else if (data.bom_items[i]?.associated_bom_version?.id) {
                await checkforCyclicDependency(data.bom_items[i]?.associated_bom_version?.id, index + 1)
              }
            }
            if (index === 1) {
              resolve(false)
            }
          })
        }
        checkforCyclicDependency(bomVersionId, 1)
      })
    }

  },
  created () {
    this.getBomList()
    this.selectedBom = this.selectedBomId
  },
  computed: {
    ...mapGetters(['isOnProjectLevel']),
    showRemoveAssBom () {
      if (this.tempSelectBom.versionId === this.selectedBomVersionId &&
            this.tempSelectBom?.id === this.selectedBomId) { return true }
      return false
    }
  }
}
</script>

<style lang="scss" scoped >
.select-bom {
  width: 600px;
  background-color: rgba(var(--brand-rgb), 0.3);
  margin: -10px;
  padding: 10px;
  &-container {
    overflow: auto;
    max-height: 200px;
    min-height: 200px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    grid-gap: 10px;
    &.no-bom-found {
      font-size: 1.2rem;
    }
  }
  &-option {
    position: relative;
    width: 120px;
    height: 40px;
    padding: 10px 10px;
    margin: 5px;
    border-radius: 8px;
    font-size: 12px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    cursor: pointer;
    text-align: center;
    background: rgba(var(--brand-rgb), 0.3);
    &.selected {
      background-color: rgba(var(--brand-rgb), 1) !important;
    }
    &:hover {
      background-color: rgba(var(--brand-rgb), 0.7);
    }
    &.disable {
      background-color: rgba(var(--brand-rgb), 0.3) !important;
      cursor: not-allowed;
      pointer-events: none;
      opacity: 0.5;
    }
    &--icon {
      position: absolute;
      top: 2px;
      right: 2px;
      width: 12px;
      height: 12px;
      z-index: 1;
    }
  }
  &-buttonBox {
    display: flex;
    justify-content: space-between;
    & .select-bom-vesrion {
      span {
        background-color: rgba(var(--brand-rgb), 0.7) !important;
        font-size: 12px;
        text-align: center;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        width: 120px;
        padding: 0.5em 1.2em;
        border-radius: 5px;
      }
      select {
        margin-left: 1.5em;
        background-color: rgba(var(--brand-rgb), 0.7);
        font-size: 12px;
        text-align: center;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        width: 120px;
        padding: 0.5em 1.2em;
        border-radius: 5px;
        border: none;
      }
    }
  }
}
.loeading-button {
  position: relative;
}

.spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 0, 0);
  border-top-color: #b9b7b7;
  border-radius: 50%;
  animation: spin 1s infinite linear;

}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.loeading-button .loading .spinner {
  display: block;
  z-index: 1;
}
</style>
