import { runQuery } from '../graphQl'
import * as tenantConfig from '@/api/query/tenantConfigFeature'
import config from '@/config'
import http from '../http'
import store from '../../store'

export const GetTenantConfigTypes = () => {
  let conditions = null
  if (store.getters?.collaborator) {
    conditions = {
      tenant_id: {
        _eq: store.getters?.collaboratorId
      }
    }
  }
  return runQuery(tenantConfig.getTenantConfig(), { conditions }, 'tenant')
}

export const updateTenantCofig = async (configName, id, productcodeId) => {
  const updateTenant = {
    tenant_feature_configuration: {}
  }

  if (configName === 'MATERIAL') {
    updateTenant.tenant_feature_configuration.MATERIAL = {
      SEQUENCE_TEMPLATE_DEFAULT: id
    }
    if (productcodeId) {
      updateTenant.tenant_feature_configuration.PRODUCT_CODE = {
        SEQUENCE_TEMPLATE_DEFAULT: productcodeId
      }
    }
  } else {
    updateTenant.tenant_feature_configuration[configName] = {
      SEQUENCE_TEMPLATE_DEFAULT: id
    }
  }
  return http.POST(config.serverEndpoint + '/tenant/feature-config', updateTenant, 'tenant')
}
