<template>
    <div class="tenant-settings-list">
        <div class="tenant-settings-list__header s p-2 v-center space-between">
            <h2 class="weight-500 l">{{ title }}</h2>
        </div>
              <slot />
        </div>
</template>

<script>
export default {
  name: 'settings-list',
  props: {
    title: {
      type: String,
      required: true,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped >
.tenant-settings-list {
    width: 100%;
    height: max-content;
    overflow: auto;
    background-color: var(--bg-color);
    border-right: 1px solid var(--brand-color);
    position: relative;

    &__header {
        position: sticky;
        top: 0;
        background-color: var(--brand-color);
    }

}</style>
