<!-- eslint-disable object-curly-spacing -->
<template>
  <div class="container">
    <div ref="stencilContainer" class="stencil">
      <button
        class="btn"
        style="z-index: 100; position: absolute"
        @click="saveWorkflow"
      >
        Save
      </button>
    </div>
    <div ref="graphContainer" class="graph"></div>
    <modal
      @close="modalOpen = false"
      :open="modalOpen"
      title="Rename"
      :closeOnOutsideClick="true"
    >
      <div>
        <div class="modalcontainer">
          <div class="input-group"  v-if="selectedElement?.shape==='custom-rect'">
            <label class="key">Enter the heading name:</label>
          <input type="text" v-model="heading" placeholder="Enter new name"  maxlength="30"/>
          </div>
          <div class="input-group" v-if="selectedElement?.shape==='custom-rect'">
            <label class="key">Enter the description</label>
          <input type="text" v-model="newLabel" placeholder="Enter the description"  maxlength="100"/>
          </div>
          <div class="input-group" v-else>
            <label class="key">Enter the {{ selectedElement?.isNode()? ' node ': ' edge ' }} name:</label>
          <input type="text" v-model="newLabel" placeholder="Enter new name" maxlength="30">
          </div>

        </div>
        <div class="gap-1 flex-end">
          <button class="btn btn-black" @click="modalOpen=false">Cancel</button>
          <button class="btn" @click="updateEdgeName">
            Save
          </button>
        </div>
      </div>
    </modal>
  </div>
</template>

<script>
import { Graph, Shape } from '@antv/x6'
import { Stencil } from '@antv/x6-plugin-stencil'
import { Snapline } from '@antv/x6-plugin-snapline'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Clipboard } from '@antv/x6-plugin-clipboard'
import { History } from '@antv/x6-plugin-history'
import { Selection } from '@antv/x6-plugin-selection'
import { Scroller } from '@antv/x6-plugin-scroller'
import {
  deleteWfStepsAndTransitions,
  getWorkflowById,
  insertWorkflowSteps,
  insertWorkflowTransitions
} from '@/api/apis/workflows'
import approvedIcon from '../../assets/images/icons/annotation/approvedIcon.svg'
import Modal from '@/components/common/modal.vue'
const workflowId = '0aecf5b9-4c08-4096-9d34-fa345c098ba0'
const createdBy = '8efa7983-76d3-42ab-a2f1-55320a5e473f'
let graph = null
export default {
  name: 'WorkflowDesigner',
  mounted () {
    this.initGraph()
  },
  components: { Modal },

  data: () => ({
    modalOpen: false,
    newLabel: '',
    selectedElement: null,
    currentLabel: null,
    heading: null
  }),
  methods: {
    async saveWorkflow () {
      const data = graph.toJSON()
      console.log(data)
      localStorage.setItem('workflow', JSON.stringify(data))
      const transitions = []
      const steps = []
      data.cells.forEach((cell) => {
        if (cell.shape === 'edge') {
          transitions.push({
            name: cell.labels[0].attrs.label.text,
            prev_step: cell.source.cell,
            next_step: cell.target.cell,
            workflow_id: workflowId,
            created_by: createdBy
          })
        }
        if (cell.shape === 'custom-rect') {
          steps.push({
            position_x: String(cell.position.x),
            position_y: String(cell.position.y),
            name: cell.attrs.text.text,
            // description: cell.attrs.text.text,
            duration: 4,
            order: String(0),
            department_id: crypto.randomUUID(),
            phase: cell.parent,
            workflow_id: workflowId,
            created_by: createdBy,
            id: cell.id
          })
        }
        if (cell.shape === 'parent-custom-rect') {
        }
      })
      await deleteWfStepsAndTransitions(workflowId)
      const insertedWfStepMap = {}
      for (const step of steps) {
        const id = step.id
        step.id = undefined
        const {
          insert_WF_Steps_one: { id: insertedId }
        } = await insertWorkflowSteps(step)
        insertedWfStepMap[id] = insertedId
      }
      for (const transition of transitions) {
        transition.prev_step = insertedWfStepMap[transition.prev_step]
        transition.next_step = insertedWfStepMap[transition.next_step]
      }
      await insertWorkflowTransitions(transitions)
    },
    moveAdjacentParents (resizedParent, oldPos, oldSize) {
      const allNodes = graph.getNodes()
      const resizedBBox = resizedParent.getBBox()

      allNodes.forEach((node) => {
        if (node !== resizedParent && node.getData()?.parent) {
          const nodeBBox = node.getBBox()

          // If the node is adjacent to the right and the parent expanded rightward
          if (nodeBBox.x >= oldPos.x + oldSize.width) {
            node.translate(resizedBBox.width - oldSize.width, 0)
          }

          // If the node is below and the parent expanded downward
          if (nodeBBox.y >= oldPos.y + oldSize.height) {
            node.translate(0, resizedBBox.height - oldSize.height)
          }
        }
      })
    },
    addEventListeners () {
      // let ctrlPressed = false
      // const embedPadding = 20
      graph.on('node:added', ({ node }) => {
        if (node.shape === 'custom-rect') {
          const parent = graph
            .getNodes()
            .find(
              (n) =>
                n.shape === 'parent-custom-rect' &&
                n.getBBox().containsPoint(node.getPosition())
            )
          if (parent) {
            parent.addChild(node)
          } else {
            graph.removeCell(node.id)
          }
        }
      })
      graph.on('node:added', ({ node, options }) => {
        // Check if the node was added via a drop from the stencil.
        if (node.shape === 'parent-custom-rect') {
          if (node) {
            node.setZIndex(1) // Set a custom z-index value
          }
          node.setAttrs({
            'name-box': {
              width: 300,
              height: 20,
              fill: 'transparent',
              stroke: '#096dd9',
              strokeWidth: 1,
              x: 0,
              y: 0
            }
          })
          // const bbox = graph.getContentBBox() // Get the bounding box of the graph

          // Find the last added 'parent-custom-rect' node
          const prevNodes = graph.getNodes().filter((n) => {
            return n.shape === 'parent-custom-rect'
          })

          const parentNodeLength = prevNodes.length - 1 ?? 0
          // Position the new node near the previous one
          // const newX = lastNodeBBox.x + lastNodeBBox.width + 20 // Place it next to the previous node
          // const newY = 0 // Always start from the top

          node.resize(300, 1000) // Resize to fixed dimensions
          node.position((parentNodeLength ?? 0) * 300 + 20, 10) // Position it properly
        }
      })
      graph.on('node:change:size', ({ node, options }) => {
        if (options.skipParentHandler) return

        const children = node.getChildren()
        if (children && children.length) {
          node.prop('originSize', node.getSize())
        }
      })
      graph.on('node:embedded', ({ node, options }) => {
        // ctrlPressed = e.metaKey || e.ctrlKey
        if (node.shape === 'parent-custom-rect') {
        }
      })
      graph.on('node:mouseup', ({ node, options }) => {
        if (node.shape === 'custom-rect') {
          const parent = graph
            .getNodes()
            .find(
              (n) =>
                n.shape === 'parent-custom-rect' &&
                n.getBBox().containsPoint(node.getPosition())
            )
          if (parent) {
            parent.addChild(node)
          }
        }
      })
      // graph.on('node:change:position', ({ node, options }) => {
      //   if (options.skipParentHandler) return

      //   const children = node.getChildren()
      //   if (children && children.length) {
      //     node.prop('originPosition', node.getPosition())
      //   }

      //   const parent = node.getParent()
      //   if (parent && parent.isNode()) {
      //     const originSize = parent.prop('originSize') || parent.getSize()
      //     const originPosition = parent.prop('originPosition') || parent.getPosition()

      //     let x = originPosition.x
      //     let y = originPosition.y
      //     let cornerX = originPosition.x + originSize.width
      //     let cornerY = originPosition.y + originSize.height
      //     let hasChange = false

      //     const children = parent.getChildren()
      //     if (children) {
      //       children.forEach((child) => {
      //         const bbox = child.getBBox().inflate(embedPadding)
      //         const corner = bbox.getCorner()

      //         if (bbox.x < x) {
      //           x = bbox.x
      //           hasChange = true
      //         }

      //         if (bbox.y < y) {
      //           y = bbox.y
      //           hasChange = true
      //         }

      //         if (corner.x > cornerX) {
      //           cornerX = corner.x
      //           hasChange = true
      //         }

      //         if (corner.y > cornerY) {
      //           cornerY = corner.y
      //           hasChange = true
      //         }
      //       })
      //     }

      //     if (hasChange) {
      //       parent.prop(
      //         {
      //           position: { x, y },
      //           size: { width: cornerX - x, height: cornerY - y }
      //         },
      //         { skipParentHandler: true }
      //       )

      //       this.moveAdjacentParents(parent, originPosition, originSize)
      //     }
      //   }
      // })
      graph.on('node:change:position', ({ node, options }) => {
        if (options.skipParentHandler || node.shape !== 'custom-rect') {
          return // Skip if it's not a child node or if the event is triggered programmatically
        }
        if (node.shape === 'custom-rect') {
          const parent = graph
            .getNodes()
            .find(
              (n) =>
                n.shape === 'parent-custom-rect' &&
                n.getBBox().containsPoint(node.getPosition())
            )
          if (parent) {
            parent.addChild(node)
            // console.log('Child node embedded in parent:', node.id, 'Parent:', parent.id)
          }
        }

        const parent = node.getParent()
        if (parent && parent.shape === 'parent-custom-rect') {
          const parentBBox = parent.getBBox() // Get the parent's bounding box
          const childBBox = node.getBBox() // Get the child's bounding box

          // Calculate the allowed position for the child node
          let newX = childBBox.x
          let newY = childBBox.y

          // Restrict movement horizontally
          if (childBBox.x < parentBBox.x) {
            newX = parentBBox.x // Move child to the left boundary of the parent
          } else if (
            childBBox.x + childBBox.width >
            parentBBox.x + parentBBox.width
          ) {
            newX = parentBBox.x + parentBBox.width - childBBox.width // Move child to the right boundary of the parent
          }

          // Restrict movement vertically
          if (childBBox.y < parentBBox.y) {
            newY = parentBBox.y // Move child to the top boundary of the parent
          } else if (
            childBBox.y + childBBox.height >
            parentBBox.y + parentBBox.height
          ) {
            newY = parentBBox.y + parentBBox.height - childBBox.height // Move child to the bottom boundary of the parent
          }

          // If the position has changed, update the child node's position
          if (newX !== childBBox.x || newY !== childBBox.y) {
            node.position(newX, newY, { skipParentHandler: true }) // Skip the parent handler to avoid infinite loops
          }
        }
      })
      graph.on('edge:mouseenter', ({ edge }) => {
        edge.addTools('vertices', 'onhover') // Show vertices tool on hover of the edge line
        edge.attr('line/filter', {
          name: 'dropShadow',
          args: {
            dx: 2,
            dy: 2,
            blur: 4,
            color: '#A2B1C3' // Shadow color
          }
        })
        edge.addTools([
          {
            name: 'button-remove',
            args: {
              distance: -50
            }
          }
        ])
      })
      graph.on('edge:mouseleave', ({ edge }) => {
        edge.attr({
          line: {
            filter: null
            // stroke: '#A2B1C3', // Reset color
            // strokeWidth: 2 // Reset width
          }
        })
        edge.removeTools()
      })
      /* graph.on('node:mouseenter', ({ node }) => {
        node.attr('line/filter', {
          name: 'dropShadow',
          args: {
            dx: 2,
            dy: 2,
            blur: 4,
            color: '#A2B1C3' // Shadow color
          }
        })
      })
      graph.on('node:mouseleave', ({ node }) => {
        node.attr({
          line: {
            filter: null
          }
        })
      }) */
      /* graph.bindKey(['meta+c', 'ctrl+c'], () => {
        const cells = graph.getSelectedCells()
        if (cells.length) {
          graph.copy(cells)
        }
        return false
      })
      graph.bindKey(['meta+x', 'ctrl+x'], () => {
        const cells = graph.getSelectedCells()
        if (cells.length) {
          graph.cut(cells)
        }
        return false
      })
      graph.bindKey(['meta+v', 'ctrl+v'], () => {
        if (!graph.isClipboardEmpty()) {
          const cells = graph.paste({ offset: 32 })
          graph.cleanSelection()
          graph.select(cells)
        }
        return false
      })
*/
      // undo redo
      graph.bindKey(['meta+z', 'ctrl+z'], () => {
        if (graph.canUndo()) {
          graph.undo()
        }
        return false
      })
      graph.bindKey(['meta+z', 'ctrl+y'], () => {
        if (graph.canRedo()) {
          graph.redo()
        }
        return false
      })
      graph.bindKey(['meta+shift+z', 'ctrl+shift+z'], () => {
        if (graph.canRedo()) {
          graph.redo()
        }
        return false
      })
      // select all
      graph.bindKey(['meta+a', 'ctrl+a'], () => {
        const nodes = graph.getNodes()
        if (nodes) {
          graph.select(nodes)
        }
      })

      // delete
      graph.bindKey('backspace', () => {
        const cells = graph.getSelectedCells()
        if (cells.length) {
          graph.removeCells(cells)
        }
      })

      // zoom
      graph.bindKey(['ctrl+1', 'meta+1'], () => {
        const zoom = graph.zoom()
        if (zoom < 1.5) {
          graph.zoom(0.1)
        }
      })
      graph.bindKey(['ctrl+2', 'meta+2'], () => {
        const zoom = graph.zoom()
        if (zoom > 0.5) {
          graph.zoom(-0.1)
        }
      })

      /*
      // select all
      graph.bindKey(['meta+a', 'ctrl+a'], () => {
        const nodes = graph.getNodes()
        if (nodes) {
          graph.select(nodes)
        }
      })

      // delete
      graph.bindKey('backspace', () => {
        const cells = graph.getSelectedCells()
        if (cells.length) {
          graph.removeCells(cells)
        }
      })

      // zoom
      graph.bindKey(['ctrl+1', 'meta+1'], () => {
        const zoom = graph.zoom()
        if (zoom < 1.5) {
          graph.zoom(0.1)
        }
      })
      graph.bindKey(['ctrl+2', 'meta+2'], () => {
        const zoom = graph.zoom()
        if (zoom > 0.5) {
          graph.zoom(-0.1)
        }
      })
*/
      /* const showPorts = (ports, show) => {
        for (let i = 0, len = ports.length; i < len; i += 1) {
          ports[i].style.visibility = show ? 'visible' : 'visible'
        }
      }

      graph.on('node:mouseenter', () => {
        const container = this.$refs.graphContainer
        const ports = container.querySelectorAll(
          '.x6-port-body'
        )
        showPorts(ports, true)
      })
      graph.on('node:mouseleave', () => {
        const container = this.$refs.graphContainer
        const ports = container.querySelectorAll(
          '.x6-port-body'
        )
        showPorts(ports, false)
      })
*/
      graph.on('edge:dblclick', ({ edge }) => {
        // Prompt the user for a new label
        this.newLabel = edge?.getLabelAt(0)?.attrs?.label?.text
        this.selectedElement = edge
        this.modalOpen = true
      })
      graph.on('edge:contextmenu', ({ edge }) => {
        // Prompt the user for a new label
        this.newLabel = edge?.getLabelAt(0)?.attrs?.label?.text
        this.selectedElement = edge
        this.modalOpen = true
      })

      graph.on('node:dblclick', ({ node }) => {
        this.selectedElement = node
        this.modalOpen = true
        this.newLabel = node.getAttrs().text.text
        this.heading = node?.getAttrs()?.title?.text

        // Prompt the user for a new label
        // const newLabel = prompt(
        //   'Enter new label for node :',
        //   node.getAttrs().text.text
        // )
        // console.log(newLabel, 'newLabel')

        // If the user provided a new label, update the node's label
        // if (newLabel !== null) {
        //   node.setAttrs({
        //     label: {
        //       text: newLabel
        //     },
        //     text: {
        //       text: newLabel
        //     }
        //   })
        //   this.adjustSizeBasedOnLabel(node)
        // }
      })
      graph.on('node:contextmenu', ({ node }) => {
        this.selectedElement = node
        this.modalOpen = true
        this.newLabel = node.getAttrs().text.text
        this.heading = node.getAttrs()?.title?.text
      })
    },
    // adjustSizeBasedOnLabel (node) {
    //   const label = node.attr('label/text') || ''
    //   const fontSize = node.attr('label/fontSize') || 14
    //   const padding = 20 // Add some padding

    //   const context = document.createElement('canvas').getContext('2d')
    //   context.font = `${fontSize}px Arial` // Set the font
    //   const textWidth = context.measureText(label).width

    //   const newWidth = textWidth + padding
    //   const newHeight = fontSize * 2 // Adjust height based on text size
    //   node.resize(newWidth, newHeight)
    // },
    async addNodes () {
      const result = [
        {
          id: '0aecf5b9-4c08-4096-9d34-fa345c098ba0',
          name: 'test template',
          workflow_phases: [
            {
              id: 'c1154de5-5ef8-4761-96b8-b672227a1cbc',
              name: 'Design'
            },
            {
              id: '3209fdca-752e-4826-bb94-6a3bdaf76a4d',
              name: 'Quality'
            }
          ],
          workflow_steps: [
            {
              id: '5451ff69-f7bd-4a39-b759-3539ab11d460',
              name: 'Checkout',
              department_id: 'e9e1b925-3277-4111-af4e-91472aa4279a',
              duration: 4,
              phase: 'c1154de5-5ef8-4761-96b8-b672227a1cbc',
              position_x: '160',
              position_y: '120'
            },
            {
              id: '8593751d-ab56-44dd-a5da-51dffe5904ee',
              name: 'Approval By Admin',
              department_id: '12337605-b241-4cf8-aa0f-10de02c62415',
              duration: 4,
              phase: '3209fdca-752e-4826-bb94-6a3bdaf76a4d',
              position_x: '770',
              position_y: '220'
            },
            {
              id: 'b7ccb81f-a7bb-4c3a-bdc3-dd2bddfe3c19',
              name: 'Released',
              department_id: '7cbf24f9-3314-485c-86c1-23fc2e7c1ce4',
              duration: 4,
              phase: '3209fdca-752e-4826-bb94-6a3bdaf76a4d',
              position_x: '840',
              position_y: '380'
            }
          ],
          workflow_transitions: [
            {
              next_step: '8593751d-ab56-44dd-a5da-51dffe5904ee',
              prev_step: '5451ff69-f7bd-4a39-b759-3539ab11d460',
              name: 'Send For Approval'
            },
            {
              next_step: '5451ff69-f7bd-4a39-b759-3539ab11d460',
              prev_step: '8593751d-ab56-44dd-a5da-51dffe5904ee',
              name: 'Rejected'
            },
            {
              next_step: 'b7ccb81f-a7bb-4c3a-bdc3-dd2bddfe3c19',
              prev_step: '8593751d-ab56-44dd-a5da-51dffe5904ee',
              name: 'Approved'
            }
          ]
        }
      ]
      let result2
      try {
        result2 = await getWorkflowById(workflowId)
        console.log(result, result2)
      } catch (error) {}
      // const template = []
      // let i = 0
      // const nodesMap = {}
      // for (const phase of template.workflow_phases) {
      //   const node = graph.addNode({
      //     id: phase.id,
      //     shape: 'parent-custom-rect',
      //     x: 500 * i,
      //     y: 0,
      //     width: 500,
      //     height: 800,
      //     zIndex: 2,
      //     label: phase.name,
      //     resizing: false,
      //     movable: false,
      //     selectable: false,
      //     attrs: {
      //       body: {
      //         fill: '#fffbe6',
      //         stroke: '#ffe7ba'
      //       },
      //       label: {
      //         fontSize: 12,
      //         refX: '50%', // Centers the label horizontally
      //         refY: '1%', // Positions the label at the top
      //         textAnchor: 'middle', // Aligns text horizontally to the center
      //         textVerticalAnchor: 'top' // Aligns text vertically to the top
      //       },
      //       text: {
      //         text: phase.name
      //       }
      //     },
      //     data: {
      //       parent: true,
      //       ...phase
      //     }
      //   })
      //   nodesMap[phase.id] = node
      //   i++
      // }
      // for (const step of template.workflow_steps) {
      //   const node = graph.addNode({
      //     id: step.id,
      //     shape: 'custom-rect',
      //     x: step.position_x,
      //     y: step.position_y,
      //     zIndex: 5,
      //     attrs: {
      //       body: {
      //         refWidth: '100%',
      //         refHeight: '100%',
      //         strokeWidth: 1
      //       },
      //       label: {
      //         text: step.name,
      //         wordBreak: 'break-word', // Wrap words automatically
      //         whiteSpace: 'normal',
      //         textAnchor: 'middle',
      //         textVerticalAnchor: 'middle'
      //       },
      //       text: {
      //         text: step.name
      //       }
      //     },
      //     data: {
      //       parent: false,
      //       ...step
      //     }
      //   })
      //   node.fit({ padding: 10 })
      //   nodesMap[step.phase].addChild(node)
      //   nodesMap[step.id] = node
      //   this.adjustSizeBasedOnLabel(node)
      //   i++
      // }

      // for (const transition of template.workflow_transitions) {
      //   graph.addEdge({
      //     source: { cell: nodesMap[transition.prev_step], port: 'port-right' },
      //     target: { cell: nodesMap[transition.next_step], port: 'port-left' },
      //     attrs: {
      //       line: {
      //         stroke: '#A2B1C3',
      //         strokeWidth: 2,
      //         targetMarker: {
      //           name: 'block',
      //           width: 12,
      //           height: 8
      //         }
      //       }
      //     },
      //     labels: [
      //       {
      //         attrs: {
      //           label: {
      //             text: transition.name
      //           },
      //           text: {
      //             text: transition.name
      //           }
      //         }
      //       }
      //     ],
      //     zIndex: 3

      //   })
      // }
    },
    async initGraph () {
      graph = new Graph({
        container: this.$refs.graphContainer,
        autoResize: true,
        // mousewheel: true,
        // height: 1000,
        onToolItemCreated ({ name, cell, tool }) {
          if (name === 'vertices') {
            const options = tool.options
            if (options && options.index % 2 === 1) {
              tool.setAttrs({ fill: 'red' })
            }
          }
        },
        interacting: {
          edgeMovable: true, // Allow edges to be dragged
          // edgeLabelMovable: true // Allow edge labels to be moved
          nodeMovable: false
        },
        grid: {
          visible: true
        },
        mousewheel: {
          enabled: true,
          zoomAtMousePosition: true,
          modifiers: 'ctrl',
          minScale: 0.5,
          maxScale: 2
        },
        embedding: {
          enabled: true,
          findParent ({ node }) {
            const bbox = node.getBBox()
            return this.getNodes().filter((node) => {
              const data = node.getData()
              if (data && data.parent) {
                const targetBBox = node.getBBox()
                return bbox.isIntersectWithRect(targetBBox)
              }
              return false
            })
          }
        },
        connecting: {
          snap: false,
          allowBlank: false,
          allowMulti: true,
          allowLoop: false,
          highlight: true,
          connectionPoint: {
            name: 'anchor'
          },
          anchor: 'center',
          connector: {
            name: 'normal' // Smoothens links
          },
          router: {
            name: 'normal',
            args: {
              offset: 40,
              excludeShapes: ['parent-custom-rect'],
              startDirections: ['right'],
              endDirections: ['left']
            }
          },
          createEdge () {
            return new Shape.Edge({
              vertices: [],
              attrs: {
                line: {
                  stroke: '#3c4260',
                  strokeWidth: 2,
                  targetMarker: 'classic'
                }
              },
              tools: {
                name: 'vertices',
                args: {
                  snapRadius: 20,
                  attrs: {
                    fill: '#444'
                  }
                }
              },
              sourceAnchor: { name: 'right' },
              targetAnchor: { name: 'left' },
              labels: [
                {
                  attrs: {
                    label: {
                      text: 'transition'
                    },
                    text: {
                      text: 'transition'
                    }
                  }
                }
              ],
              // tools: [
              //   {
              //     name: 'vertices', // Enable vertices tool for dragging and adding vertices
              //     args: {
              //       snapRadius: 20, // Snap radius for vertices
              //       attrs: {
              //         fill: '#444' // Vertex color
              //       }
              //     }
              //   },
              //   {
              //     name: 'segments', // Enable segments tool for dragging edge segments
              //     args: {
              //       snapRadius: 100, // Snap radius for segments
              //       attrs: {
              //         fill: '#444' // Segment color
              //       }
              //     }
              //   }
              // ],
              zIndex: 10
            })
          },
          validateConnection ({ targetMagnet }) {
            if (!targetMagnet) {
              return false
            }
            return true
          }
        },
        highlighting: {
          magnetAdsorbed: {
            name: 'stroke',
            args: {
              attrs: {
                fill: '#5F95FF',
                stroke: '#5F95FF'
              }
            }
          }
        },
        translating: {
          restrict (view) {
            if (!view) {
              return null
            }
            const cell = view.cell
            if (cell.isNode() && !cell.getParent()) {
              // If the node has no parent (i.e., it's a parent node itself), restrict movement
              return cell.getBBox()
            }
            return null // Allow movement for child nodes
          }
        }
      })
      // #endregion
      graph
        .use(
          new Selection({
            enabled: true,
            rubberband: true,
            showNodeSelectionBox: true,
            multiple: false,
            filter: ['custom-rect']
          })
        )
        .use(
          new Scroller({
            enabled: true,
            padding: 20
          })
        )
        .use(
          new Snapline({
            enabled: true
            // filter: ['custom-rect']
          })
        )
        .use(new Keyboard())
        .use(new Clipboard())
        .use(new History())
      // #endregion
      const stencil = new Stencil({
        title: '1',
        target: graph,
        stencilGraphWidth: 200,
        stencilGraphHeight: 1000,
        stencilGraphOptions: {
          background: {
            color: '#f5f5f5', // stencil background
            marginTop: 20
          }
        },
        collapsable: true,
        groups: [
          {
            title: '1',
            name: 'group1'
          }
          // {
          //   title: '2',
          //   name: 'group2',
          //   graphHeight: 250,
          //   layoutOptions: {
          //     rowHeight: 70
          //   }
          // }
        ],
        layoutOptions: {
          columns: 1,
          columnWidth: 80,
          center: true,
          marginX: 30
          // resizeToFit: true
        }
      })
      this.$refs.stencilContainer.appendChild(stencil.container)
      // #endregion

      // #region
      const rectPorts = {
        groups: {
          right: {
            position: 'right',
            attrs: {
              circle: {
                r: 6,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'visible'
                }
              }
            },
            maxConnections: -1
          },
          left: {
            position: 'left',
            attrs: {
              circle: {
                r: 6,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'visible'
                }
              }
            },
            maxConnections: -1
          }
        },
        items: [
          {
            id: 'port-right',
            group: 'right'
          },
          {
            id: 'port-left',
            group: 'left'
          }
        ]
      }

      Graph.registerNode(
        'custom-rect',
        {
          inherit: 'rect',
          width: 140,
          height: 60,
          zIndex: 2,
          attrs: {
            body: {
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: 'rgba(95,149,255,0.2)',
              refWidth: 1,
              refHeight: 1
            },
            image: {
              'xlink:href':
                'https://plus.unsplash.com/premium_photo-1680740103993-21639956f3f0?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8c2FtcGxlfGVufDB8fDB8fHww',
              width: 16,
              height: 16,
              x: 12,
              y: 12
            },
            title: {
              text: 'Heading',
              refX: 40,
              refY: 14,
              fill: 'rgba(0,0,0)',
              fontSize: 12,
              'text-anchor': 'start'
            },
            text: {
              text: 'content',
              refX: 40,
              refY: 38,
              fontSize: 12,
              fill: 'rgba(0,0,0,0.6)',
              'text-anchor': 'start'
            }
          },
          markup: [
            {
              tagName: 'rect',
              selector: 'body'
            },
            {
              tagName: 'image',
              selector: 'image'
            },
            {
              tagName: 'text',
              selector: 'title'
            },
            {
              tagName: 'text',
              selector: 'text'
            }
          ],
          interacting: {
            nodeMovable: false // Disable movement by default
          },
          ports: { ...rectPorts }
        },
        true
      )

      Graph.registerNode(
        'parent-custom-rect',
        {
          inherit: 'rect',
          width: 140,
          height: 60,
          zIndex: 1,
          markup: [
            {
              tagName: 'text',
              selector: 'name-text'
            },
            {
              tagName: 'rect',
              selector: 'name-box'
            },
            {
              tagName: 'rect',
              selector: 'body'
            }
          ],
          attrs: {
            body: {
              strokeWidth: 1,
              fill: 'rgba(245, 240, 108,.3)',
              stroke: '#ffe7ba'
            },
            'name-box': {
              width: 140,
              height: 20,
              fill: 'transparent',
              stroke: '#096dd9',
              strokeWidth: 1,
              x: 0,
              y: 0
            },
            'name-text': {
              text: 'phase',
              fill: 'black',
              fontSize: 12,
              fontWeight: 'bold',
              refY: 10,
              textAnchor: 'middle',
              zIndex: 2
            }

          },
          ports: []
        },
        true
      )
      const rect = graph.createNode({
        shape: 'custom-rect'
        // label: 'Checkout'
      })
      const rect2 = graph.createNode({
        shape: 'parent-custom-rect',
        label: 'Phase'
      })
      stencil.load([rect, rect2], 'group1')
      // loading data from server ---- start-----
      const graphData = JSON.parse(localStorage.getItem('workflow'))
      const cells = graphData?.cells?.map((item) => {
        if (item.shape === 'edge') {
          // Edge metadata
          return {
            id: item.id,
            shape: item.shape,
            // attrs: item.attrs,
            source: {
              cell: item.source.cell,
              port: item.source.port
            },
            target: {
              cell: item.target.cell,
              port: item.target.port
            },
            labels: item.labels,
            zIndex: 100 ?? item.zIndex,
            vertices: item.vertices
          }
        } else {
          // Node metadata
          return {
            id: item.id,
            shape: item.shape,
            x: item.position.x,
            y: item.position.y,
            width: item.size.width,
            height: item.size.height,
            attrs:
              item.shape === 'custom-rect'
                ? {
                  body: {
                    stroke: '#5F95FF',
                    strokeWidth: 1,
                    fill: 'rgba(95,149,255,0.2)',
                    refWidth: 1,
                    refHeight: 1
                  },
                  image: {
                    'xlink:href': approvedIcon,
                    width: 16,
                    height: 16,
                    x: 12,
                    y: 12
                  },
                  title: {
                    text: 'New heading',
                    refX: 40,
                    refY: 14,
                    fill: 'rgba(0,0,0,)',
                    fontSize: 12,
                    'text-anchor': 'start'
                  },
                  text: {
                    text: 'latest content',
                    refX: 40,
                    refY: 38,
                    fontSize: 12,
                    fill: 'rgba(0,0,0,0.6)',
                    'text-anchor': 'start'
                  }
                }
                : {
                  ...item.attrs,
                  'name-box': {
                    width: 300,
                    height: 20,
                    stroke: '#096dd9',
                    strokeWidth: 2,
                    x: 0,
                    y: 0
                  }
                },
            // label: 'phase',
            // title: 'Heading',
            //  ports: item.ports,
            ports: item.shape === 'custom-rect' ? { ...rectPorts } : [],
            zIndex: item.shape === 'custom-rect' ? 2 : 1,
            visible: true,
            children: item.children || []
          }
        }
      })
      // graph.resetCells(cells)
      if (cells?.length > 0) {
        graph.fromJSON(cells)
      }

      // #region stencil
      // loading data from server ---- end-----
      await this.addNodes()
      this.addEventListeners()
      graph.center()
    },
    updateEdgeName () {
      if (this.selectedElement && this.newLabel) {
        if (this.selectedElement.isNode()) {
          if (this.selectedElement.shape === 'parent-custom-rect') {
            // Rename Node
            this.selectedElement.setAttrs({
              'name-text': { text: this.newLabel }
            })
          } else {
            if (this.newLabel.length > 30) {
              this.newLabel = this.newLabel.slice(0, 30)
            }
            this.selectedElement.setAttrs({
              text: { text: this.newLabel }
            })
            this.adjustSizeBasedOnLabel(this.selectedElement)
          }
        } else if (this.selectedElement.isEdge()) {
          if (this.newLabel.length > 30) {
            this.newLabel = this.newLabel.slice(0, 30)
          }
          // Rename Edge
          this.selectedElement.setLabels([{ attrs: { label: { text: this.newLabel } } }])
        }
      }
      this.modalOpen = false
    },
    adjustSizeBasedOnLabel (node) {
    // Adjust node size based on text length
      const textWidth = node.getAttrs().text.text.length * 10
      node.resize(textWidth + 20, node.getSize().height)
    }
  }

}

</script>
<style lang="scss" scoped>

.modalcontainer {
  padding: 17px 17px 17px 17px;
  width: 400px;
  height: fit-content;
}
.selected-bom {
    border: 1px solid rgba(59, 59, 59, 0.4666666667);
    width: 100%;
    border-radius: 0.285em;
    font-size: 1em;
    padding: 0.17em;
    padding-inline: .5em;
    display: flex;
    justify-content: space-between;
}
.input-group{
  padding: .3em;
  & label {
    margin-bottom: 0;
  }
  & input{
padding: .3em;
padding-inline: .5em;
  }
}
.container {
  display: flex;
  border: 1px solid #dfe3e8;
  overflow: hidden;
}
.stencil {
  width: 180px;
  height: 100vh;
  position: relative;
  border-right: 1px solid #dfe3e8;
}
.graph {
  width: calc(100vw - 180px);
  max-width: calc(100vw - 180px);
  height: 100vh;
  max-height: 100vh;
}
.x6-widget-stencil {
  background-color: #fff;
}
.x6-widget-stencil-title {
  background-color: #fff;
}
.x6-widget-stencil-group-title {
  background-color: #fff !important;
}
.x6-widget-transform {
  margin: -1px 0 0 -1px;
  padding: 0px;
  border: 1px solid #239edd;
}
.x6-widget-transform > div {
  border: 1px solid #239edd;
}
.x6-widget-transform > div:hover {
  background-color: #3dafe4;
}
.x6-widget-transform-active-handle {
  background-color: #3dafe4;
}
.x6-widget-transform-resize {
  border-radius: 0;
}
.x6-widget-selection-inner {
  border: 1px solid #239edd;
}
.x6-widget-selection-box {
  opacity: 0;
}
</style>
