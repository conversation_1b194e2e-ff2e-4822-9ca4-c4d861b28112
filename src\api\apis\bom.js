import { runQuery, runMutation } from '../graphQl'
import * as bomQuery from '../query/bom'
import Config from '../../config.js'
import store from '../../store'

export const GetAllBomList = (productCode) => {
  const conditions = { product_code: { _eq: productCode } }
  if (store.getters?.collaborator) {
    const userId = store.getters?.user?.userId
    const collaboratorId = store.getters?.collaboratorId
    conditions.collaborator_boms = { source_tenant_id: { _eq: collaboratorId }, target_tenant_user_id: { _eq: userId } }
  }
  return runQuery(bomQuery.GetAllBomListByProductCodeQuery(), {
    conditions
  }, 'tenant')
}

export const CreateProductBom = (productCode, bomName, bomItems, inheritedFrom, interitedFromVersionId, effectiveDate, bomFormItems, templateVersionId = null) => {
  return runMutation(bomQuery.CreateProductBomQuery(), {
    product_code_id: productCode,
    name: bomName,
    items: bomItems,
    inherited_from_bom_id: inheritedFrom || null,
    inherited_from_bom_version_id: interitedFromVersionId || null,
    effective_date: effectiveDate || null,
    custom_field_data: bomFormItems,
    form_template_version_id: templateVersionId || null
  }, 'tenant')
}

export const GetBomForSwitching = (productCode, name) => {
  return runQuery(bomQuery.GetBomForSwitching(), { product_code: productCode, name }, 'tenant')
}

export const GetBomForProjectSwitch = (projectId, name) => {
  return runQuery(bomQuery.GetBomForSwitchingProject(), { projectId, name }, 'project')
}

export const UpdateProductBom = (bomId, bomItems, customField) => {
  return runMutation(bomQuery.UpdateProductBomQuery(), {
    bom_id: bomId,
    bom_items: bomItems,
    custom_fields: customField
  }, 'tenant')
}

export const UpdateBomState = (bomId, state, token = 'tenant') => {
  return runMutation(bomQuery.UpdateBomStateQuery(), {
    id: bomId,
    state: state
  }, token)
}
export const setLastBomVersionActive = (bomVersionId, state, isProject) => {
  return runMutation(bomQuery.setLastBomVersionActiveQuery(), {
    id: bomVersionId,
    state: state
  }, isProject ? 'project' : 'tenant')
}

export const GetAllBomVersionListByBomId = (bomId, project = false) => {
  if (store.getters?.collaborator) {
    project = false
  }
  return runQuery(bomQuery.GetAllBomVersionListByBomIdQuery(), {
    bomId: bomId
  }, project ? 'project' : 'tenant')
}

export const GetBomDetailById = (bomId) => {
  return runQuery(bomQuery.GetBomDetailByIdQuery(), {
    id: bomId
  }, 'tenant')
}

export const GetBomDetailByIdAndBomVersion = (bomId, bomVersionId) => {
  return runQuery(bomQuery.GetBomDetailByIdAndBomVersionQuery(), {
    id: bomId,
    versionId: bomVersionId
  }, 'tenant')
}

export const GetAllBomItemsByBomVersionId = (bomVersionId) => {
  return runQuery(bomQuery.GetAllBomItemsByBomVersionIdQuery(), {
    bom_version: bomVersionId
  }, 'tenant')
}

export const GetBomForCopyAndInherit = (bomId, project = false) => {
  return runQuery(bomQuery.GetBomForCopyAndInheritQuery(), {
    id: bomId
  }, project ? 'project' : 'tenant')
}

export const InserBomItems = (bomItems, project = false) => {
  return runMutation(bomQuery.InserBomItemsQuery(), {
    bom_items: bomItems
  }, project ? 'project' : 'tenant')
}
export const updateBomName = (bomId, name, project = false) => {
  return runMutation(bomQuery.updateBomNameQuery(), {
    bomId,
    name
  }, project ? 'project' : 'tenant')
}

export const DeleteBomItems = (bomItem, project = false) => {
  return runMutation(bomQuery.DeleteBomItemsQuery(), {
    bom_version_id: bomItem.bomVersionId,
    material_id: bomItem.materilaId,
    unit_size: bomItem.unitSize
  }, project ? 'project' : 'tenant')
}

export const UpdateBomItems = (bomItems, project = false) => {
  return runMutation(bomQuery.UpdateBomItemsQuery(), bomItems, project ? 'project' : 'tenant')
}

export const ReleaseBomVersion = (bomVersionId, project = false) => {
  return runMutation(bomQuery.ReleaseBomVersionQuery(), {
    id: bomVersionId
  }, project ? 'project' : 'tenant')
}

export const DeleteBomVersion = (bomVersionId, project = false) => {
  return runMutation(bomQuery.DeleteBomVersionQuery(), {
    id: bomVersionId
  }, project ? 'project' : 'tenant')
}

export const DeleteBomItemsByVersionId = (bomVersionId, project = false) => {
  return runMutation(bomQuery.DeleteBomItemsByVersionIDQuery(), {
    bom_version_id: bomVersionId
  }, project ? 'project' : 'tenant')
}
// Project Bom
export const CreateProjectBom = (bomName, bomItems, inheritedFrom, interitedFromVersionId, addEffectiveDate, standardBomForm, templateVersionId) => {
  return runMutation(bomQuery.CreateProjectBomQuery(), {
    name: bomName,
    items: bomItems,
    inherited_from_bom_id: inheritedFrom || null,
    inherited_from_bom_version_id: interitedFromVersionId || null,
    effective_date: addEffectiveDate,
    custom_field_data: standardBomForm || undefined,
    form_template_version_id: templateVersionId || null
  }, 'project')
}

export const UpdateProjectBom = (bomId, bomItems, customField) => {
  return runMutation(bomQuery.UpdateProductBomQuery(), {
    bom_id: bomId,
    bom_items: bomItems,
    custom_fields: customField
  }, store.getters?.collaborator ? 'tenant' : 'project')
}

export const GetAllProjectBomList = (onlyActive = false, restrictCheckout = false) => {
  const conditions = { project_id: { _eq: localStorage.getItem(Config.localstorageKeys.LAST_OPENED_PROJECT) } }
  let token = 'project'
  if (store.getters?.collaborator) {
    token = 'tenant'
    const projectId = store.getters?.projectIdForCollaborator

    conditions.project_id = { _eq: projectId }
    const userId = store.getters?.user?.userId
    const collaboratorId = store.getters?.collaboratorId
    conditions.collaborator_boms = { source_tenant_id: { _eq: collaboratorId }, target_tenant_user_id: { _eq: userId } }
  }
  if (onlyActive) {
    conditions.state = { _neq: Config.BOM_STATE_MAP.OBSOLETE }
    conditions.bom_versions = { active: { _eq: true } }
  }
  if (restrictCheckout) {
    conditions.state = { _neq: Config.BOM_STATE_MAP.CHECKOUT }
    conditions.bom_versions = { state: { _neq: Config.BOM_STATE_MAP.CHECKOUT } }
  }
  return runQuery(bomQuery.GetAllProjectBomListQuery(), {
    conditions
  }, token)
}
export const GetAllProjectBomListByToken = (token, projectId) => {
  const conditions = { project_id: { _eq: projectId } }
  // let token = 'project'
  // if (store.getters?.collaborator ) {
  //   token = 'tenant'
  //   conditions.project_id = { _eq: store.getters?.projectIdForCollaborator }
  //   const userId = store.getters?.user?.userId
  //   const collaboratorId = parseInt(store.getters?.collaboratorId)
  //   conditions.collaborator_boms = { source_tenant_id: { _eq: collaboratorId }, target_tenant_user_id: { _eq: userId } }
  // }
  return runQuery(bomQuery.GetAllProjectBomListQuery(), {
    conditions
  }, 'current', token)
}

export const GetProjectBomDetailById = (bomId) => {
  let token = 'project'
  if (store.getters?.collaborator) {
    token = 'tenant'
  }
  return runQuery(bomQuery.GetProjectBomDetailByIdQuery(), {
    id: bomId
  }, token)
}

export const GetProjectBomDetailByIdAndBomVersion = (bomId, bomVersionId) => {
  return runQuery(bomQuery.GetProjectBomDetailByIdAndBomVersionQuery(), {
    id: bomId,
    versionId: bomVersionId
  }, store.getters?.collaborator ? 'tenant' : 'project')
}

export const GetAllProjectBomItemsByBomVersionId = (bomVersionId) => {
  return runQuery(bomQuery.GetAllProjectBomItemsByBomVersionIdQuery(), {
    bom_version: bomVersionId
  }, store.getters?.collaborator ? 'tenant' : 'project')
}

export const GetAllChildProductForProjectBom = (bomVersionId) => {
  let token = 'project'
  if (store.getters?.collaborator) {
    token = 'tenant'
  }
  return runQuery(bomQuery.GetAllChildProductForProjectBomQuery(), {
    id: bomVersionId
  }, token)
}

export const updateEffectedDate = (bomId, selectedDate, forProject) => {
  return runMutation(bomQuery.updateEffectiveDateQuery(), {
    id: bomId,
    effective_date: selectedDate
  }, forProject ? 'project' : 'tenant')
}

export const updateAssociatedBomVersion = (parentBomVersionId, materialId, unitSize, associatedBomVersion) => {
  return runMutation(bomQuery.updateAssociatedBomversionMutate(), {
    bom_version_id: parentBomVersionId,
    material_id: materialId,
    unit_size: unitSize,
    associated_product_code_bom: associatedBomVersion
  }, 'tenant')
}

export const updateBomVersion = (bomVersion, state, forProject) => {
  return runMutation(bomQuery.updateBomVersionQuery(), {
    bomVersion: bomVersion,
    state: state
  }, forProject ? 'project' : 'tenant')
}

export const deleteAllBomItemAndUpdate = (bomId, bomVersionId, bomItems, newInheritedVersionId, project = false) => {
  return runMutation(bomQuery.deleteAllBomItemAndUpdateQuery(), {
    bom_id: bomId,
    bom_version_id: bomVersionId,
    bom_items: bomItems,
    inherited_from_bom_version_id: newInheritedVersionId

  }, project ? 'project' : 'tenant')
}

export const deleteAllBomItems = (bomId, bomVersionId, project = false) => {
  return runMutation(bomQuery.deleteAllBomItemsMutation(), {
    bom_id: bomId,
    bom_version_id: bomVersionId
  }, project ? 'project' : 'tenant')
}

export const deleteAllBomItemsAndInsert = ({ bomId, bomItems, bomVersionId, project = false }) => {
  return runMutation(bomQuery.deleteAllBomItemsAndInsertMutation(), {
    bom_id: bomId,
    bom_version_id: bomVersionId,
    bom_items: bomItems
  }, project ? 'project' : 'tenant')
}

export const updateStdBomVersions = (bomVersionId, standardBomForm, stdBomTempId, stdBomVersionId, project = false) => {
  return runMutation(bomQuery.updateStdBomVersion(), {
    bom_version_id: bomVersionId,
    custom_fields_data: {
      values: standardBomForm,
      template_id: stdBomTempId || null,
      template_version_id: stdBomVersionId || null
    }

  }, project ? 'project' : 'tenant')
}
export const getBomDatawithAssociatedBoms = (bomVersion, forProject = false) => {
  return runMutation(bomQuery.getBomDatawithAssociatedBoms(), {
    bom_version_id: bomVersion
  }, forProject ? 'project' : 'tenant')
}

export const ShareBom = (data, token = 'tenant') => {
  return runMutation(bomQuery.shareBomToCollaborator(), { data }, token)
}

export const GetUsersWithAccessToSharedBom = (bomId, projectLevel = false) => {
  return runMutation(bomQuery.getUsersWithAccessToShared(), { bomId }, projectLevel ? 'project' : 'tenant')
}

export const RemoveSharedBomAccess = (id) => {
  return runMutation(bomQuery.removeSharedBomAccessOfCollaborator(), { id }, 'tenant')
}
