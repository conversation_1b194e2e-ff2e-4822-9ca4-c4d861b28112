<template>
   <div class="bom-list-container">
    <div class="empty-bom-list">
      <div v-if="loadingBomList"  style="padding-left: 33rem; padding-top: 20rem">
    <loading-circle />
      </div>
      <div v-else-if="!loadingBomList && !getBomList.length" style="padding-left: 33rem; padding-top: 20rem">
    <h3>
      No bom found
    </h3>
  </div>
    <div v-else class="bom-item">
    <template>
      <div class="empty-bom-list__header s  ">
     <div class="space-between p-2 v-center">
      <h2 class="weight-500 l">BOM List</h2>
     </div>
    </div>
      <div
      v-for="bom in getBomList"
        :key="bom.name"
        @click="openBomDetail(bom)">
        <div class="product-code-item v-center"  :class="{
        selected: bom.id == selectedBomId
      }">
      <div v-overflow-tooltip class="product-code-item__name v-center s elipsis-text "  >{{bom.name }}
      </div>
      <div>
        <img
          v-if="bom.state === 2"
          src="~@/assets/images/icons/checkin-icon.svg"
          class="icon"
          v-tooltip="'Bom is checked in'"
        />
        <img
        v-else-if="bom.state === 3"
         class="icon"
          src="~@/assets/images/icons/checkout-icon.svg"
          v-tooltip="'Bom is checked out'"
        />
        <img
          v-else-if="bom.state === 1"
           class="icon"
          src="~@/assets/images/icons/locked-icon.svg"
          v-tooltip="'Bom is locked'"
        />
        <img
        v-else-if="bom.state === OBSOLETE"
         class="icon"
          src="~@/assets/images/icons/obsolete-icon.svg"
          v-tooltip="'Bom is Obsolete'"
        />
      </div>
      </div>
    </div>
    </template>
  </div>
  </div>
  <div  class="bom-detail-view">
<router-view />
</div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Config from '@/config'
import loadingCircle from '../../../components/common/loadingCircle.vue'
export default {
  components: { loadingCircle },
  data: () => {
    return {
      OBSOLETE: Config.BOM_STATE_MAP.OBSOLETE,
      LOCK: Config.BOM_STATE_MAP.LOCK,
      loadingBomList: false
    }
  },
  name: 'ProjectBomList',
  computed: {
    ...mapGetters('projectBom', ['bomList', 'bomListLoading', 'bomSearchKeyword', 'selectedBomId']),
    ...mapGetters(['user']),
    createBomDisable () {
      if (this.user.projectLevelRole === 'ADMIN' ||
            this.user.projectLevelRole === 'COLLABORATOR') { return false } else return true
    },
    getBomList () {
      return this.bomList
        .filter((bom) => {
          if (!this.bomSearchKeyword || (this.bomSearchKeyword && bom.name.toLowerCase().includes(this.bomSearchKeyword))) {
            if (this.user.projectLevelRole !== 'ADMIN') {
              if (bom.state === 3 && bom.checked_out_by) {
                if (bom.checked_out_by !== this.user.userId && bom.bom_versions.length <= 1) {
                  return false
                }
              }
            }
            return true
          }
        }).map((bom) => {
          return {
            ...bom,
            stateValue: Config.STATE_MAP[bom.state]
          }
        })
    }
  },
  methods: {
    openBomDetail (bom) {
      this.$router.push(
        `/bom/project/bom/${bom?.id}${
          bom.bom_versions?.[0]?.id
            ? `?bomVersionId=${bom.bom_versions?.[0]?.id}`
            : ''
        }`
      )
    },
    setBaseData () {
      this.loadingBomList = true
      this.$store.dispatch('projectBom/getBomList')
      this.loadingBomList = false
    }
  },
  mounted () {
    this.setBaseData()
  },
  created () {
    this.setBaseData()
  }
}
</script>

<style lang="scss" scoped >
.bom-item{
  min-width: 24rem;
  max-width: 25rem;
  height:100%;
  overFlow: auto;
  background-color: var(--bg-color);
  border: 1px solid var(--brand-color);
  @media screen and (max-width: 1366px) {
    min-width: 14rem;
    max-width: 14rem;
  }
}
.product-code-item {
  border: 1px solid var(--brand-color);
  background-color: rgba(var(--brand-rgb), 0.2);
  margin: 6px 6px;
  padding-left: 6px;
  cursor: pointer;
  &.selected {
    background-color: rgba(var(--brand-rgb), 0.6);
  }
  &__name {
    height: 30px;
    width: 20rem;
  }
  & .icon {
    width: 20px;
  }
}
.empty-bom-list {
  &__header {
    position: sticky;
    top: 0;
    z-index: 1;
    background-color: var(--brand-color);
    display: flex;
    flex-direction: column;

  }

  h3 {
    font-size: 20px;
    font-weight: 500;
  }
}
.red-border {
  border: 1px solid red;
}
.bom-list-container{
  height:100%;
  display: flex;
  .bom-detail-view{
    flex: 1;
    overflow-x: auto;
    overflow-y: auto;
    padding-left: 5px;
  }
}
</style>
