<template>
  <div class="tenant-calendar-list">
    <div v-if="loading" class="center loader">
      <loading-circle />
    </div>
    <div class="calendar-list-data" v-else>
      <div class="calendar-list-data-bar v-center space-between p-3">
        <h3 class="weight-500 xl">Calendar Settings</h3>
      </div>
      <div>
        <calendar-details
        :selectedCalenderShow="selectedCalenderShow"
        :new_calendar="addNew"
        :holidays="holidays"
        :calendar_details="calendarLists"
        @update="getCalendarsList"
        />
      </div>
    </div>
  </div>
</template>

<script>
import LoadingCircle from '@/components/common/loadingCircle.vue'
import { mapGetters } from 'vuex'
import { GetCurrentCalendarList } from '@/api'
import CalendarDetails from '../../components/settings/calendar/calendarDetails.vue'
export default {
  components: {
    LoadingCircle,
    CalendarDetails
  },
  name: 'calendar-settings',
  data () {
    return {
      holidays: [],
      addNew: false,
      selectedCalenderShow: false,
      loading: false,
      calendarLists: {}
    }
  },
  computed: {
    ...mapGetters([
      'isOnProjectLevel'
    ])
  },
  methods: {
    getCalendarsList () {
      this.addNew = false
      // this.loading = true
      // if (this.isOnProjectLevel) {
      GetCurrentCalendarList().then((res) => {
        this.calendarLists = res?.core_project_calendar[0]
        const holidays = JSON.parse(JSON.stringify(res?.core_project_calendar))
        this.holidays = holidays[0].calendar_holidays
        this.holidays.forEach(holiday => {
          holiday.isEditing = false
        })
        this.loading = false
      }).catch(err => console.log(err.message)).finally(() => {
        this.loading = false
      })
      // } else {
      //   GetCalendarsList().then((res) => {
      //     this.calendarLists = res?.core_project_calendar
      //     console.log(this.calendarLists, 'this is the calendar list in the beginning')
      //     this.holidays = JSON.parse(JSON.stringify(res?.core_project_calendar))
      //     this.holidays.forEach(holiday => {
      //       holiday.isEditing = false
      //     })
      //   }).catch(err => console.log(err.message)).finally(() => {
      //     this.loading = false
      //   })
      // }
    }
    // handleSelect (index) {
    //   this.addNew = false
    //   this.selectedCalenderShow = true
    //   this.selectedCalendarIndex = index
    // },
    // newCalendar () {
    //   this.addNew = !this.addNew
    //   this.selectedCalenderShow = false
    // }
  },
  mounted () {
    this.getCalendarsList()
  },
  watch: {
  }
}
</script>

<style scoped lang="scss">
.tenant-calendar-list {
  width: 100%;
  height: 100%;
}
.calendar-list-data {
  height: 100%;
  &-bar {
    width: 100%;
    height: 40px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  &-container {
    width: 100%;
    height: calc(100% - 50px);
    display: grid;
    grid-template-columns: 1fr 2.5fr;
    grid-gap: 10px;
  }
}
</style>
