import { GQL } from '../graphQl'
export const GetMaterialMasterDataQuery =
  () => GQL`query GetMaterialMasterDataQuery($jump: Int, $perPage: Int, $conditions: core_material_master_bool_exp, $orderBy: [core_material_master_order_by!]) {
  core_material_master_aggregate(where: $conditions) {
    aggregate {
      count
    }
  }
  core_material_master(
    where: $conditions
    limit: $perPage
    offset: $jump
    order_by: $orderBy
  ) {
    resource_state_value {
      id
      name
    }
    type
    custom_material_id
    erp_material_id
    id
    gross_weight
    resource_group_details {
      name
    }
    inventory
    lead_time
    material_storage_location {
      name
    }
    material_description
    material_group
    resource_group
    material_group_details {
      name
      id
    }
    material_name
    material_type
    others
    parent_id
    plm_material_id
    plm_record_id
    inventory
    effective_date
    status
    sequence_id
    storage_loc
    unit_cost
    unit_of_material
    material_unit_details {
      name
      id
    }
    unit_sale_price
    weight_unit
    material_weight_details {
      name
      id
    }
    product_code
    material_product_code {
      id
      sequence_id
      product_code
    }
    tag_materials {
      tag {
        name
        id
        parent_id
      }
    }
    material_source {
      id
      source
    }
    material_document_associations(where: {core_document: {deleted: {_eq: false}}}) {
      material_id
      document_id
      core_document {
        id
        blob_key
        doc_name
        doc_size
        doc_type
        thumbnail_blob_key
        state
        associated_versions(order_by: {created_on: asc}) {
          id
          blob_key
          doc_name
          doc_size
          doc_type
          thumbnail_blob_key
          state
        }
      }
    }
    blob_reference_key
    material_status {
      id
      status
    }
    core_form {
      id
      template_version {
        active
        version_id
        id
        template_fields {
          autogenerated
          caption
          custom_list_id
          field_id
          field_name
          field_type_id
          fixed
          required
          template_version_id
          form_field {
            key
            __typename
          }
          __typename
        }
        __typename
      }
    }
  }
}
`
export const GetMaterialMasterDataQueryForCsv =
  () => GQL`query GetMaterialMasterDataQueryForCsv($jump: Int, $perPage: Int, $conditions: core_material_master_bool_exp) {
  core_material_master_aggregate(where: $conditions) {
    aggregate {
      count
    }
  }
  core_material_master(
    where: $conditions
    limit: $perPage
    offset: $jump
    order_by: {custom_material_id: desc}
  ) {
    resource_state_value {
      id
      name
    }
    type
    custom_material_id
    erp_material_id
    id
    gross_weight
    resource_group_details {
      name
    }
    inventory
    lead_time
    material_storage_location {
      name
    }
    material_description
    material_group
    resource_group
    material_group_details {
      name
      id
    }
    material_name
    material_type
    others
    parent_id
    plm_material_id
    plm_record_id
    inventory
    effective_date
    status
    storage_loc
    unit_cost
    unit_of_material
    material_unit_details {
      name
      id
    }
    unit_sale_price
    weight_unit
    material_weight_details {
      name
      id
    }
    product_code
    material_product_code {
      id
      product_code
    }
    tag_materials {
      tag {
        name
        id
        parent_id
      }
    }
    material_source {
      id
      source
    }
    material_status {
      id
      status
    }
        material_document_associations(where: {core_document: {deleted: {_eq: false}}}) {
      material_id
      document_id
      core_document {
        id
        blob_key
        doc_name
        doc_size
        doc_type
        thumbnail_blob_key
        state
        associated_versions(order_by: {created_on: asc}) {
          id
          blob_key
          doc_name
          doc_size
          doc_type
          thumbnail_blob_key
          state
        }
      }
    }
   core_form {
      template_version_id
        forms_metadata_by_id {
          field_id
          form_id
          time_value
      point_value
      int_value
      date_value
      bool_value
      string_value
        }
        forms_config_lists {
          custom_list_value
          custom_list_value_id
          field_id
          form_id
          custom_list_value_by_id {
            name
          }
        }
      }
  }
}
`

export const resourceGroup =
  () => GQL`query fetchingMaterialGroup($name: String) {
  custom_list_values(
    where: {custom_list: {name: {_eq: $name}}, deleted: {_eq: false}}
  ) {
    id
    name
  }
}`

export const GetSingleMaterialMasterDataQuery =
  () => GQL`query GetSingleMaterialMasterDataQuery(
  $id: uuid
  ) {
  core_material_master(
    where: {id:{_eq : $id}}
  ) {
    resource_state_value {
      id
      name
    }
    type
    custom_material_id
    erp_material_id
    id
    gross_weight
    resource_group_details {
    name
    }
    inventory
    lead_time
    material_storage_location {
    name
    }
    material_description
    material_group
    resource_group
    material_group_details {
      name
      id
    }
    material_name
    material_type
    others
    parent_id
    plm_material_id
    plm_record_id
    inventory
    effective_date
    status
    storage_loc
    unit_cost
    unit_of_material
    material_unit_details {
      name
      id
    }
    unit_sale_price
    weight_unit
    material_weight_details {
      name
      id
    }
    product_code
    material_product_code {
      id
      product_code
    }
    tag_materials {
      tag {
        name
        id
        parent_id
      }
    }
    material_source {
      id
      source
    }
   material_document_associations(where: {core_document: {deleted: {_eq: false}}}) {
      material_id
      document_id
      core_document {
        id
        blob_key
        doc_name
        doc_size
        doc_type
        thumbnail_blob_key
        state
        associated_versions(order_by: {created_on: asc}) {
          id
          blob_key
          doc_name
          doc_size
          doc_type
          thumbnail_blob_key
          state
        }
      }
    }
    blob_reference_key
    material_status {
      id
      status
    }
    core_form {
      id
      template_version {
        active
        version_id
        id
        template_fields {
          autogenerated
          caption
          custom_list_id
          field_id
          field_name
          field_type_id
          fixed
          required
          template_version_id
          form_field {
            key
            __typename
          }
          __typename
        }
        __typename
      }
    }
  }
}
`
export const GetMaterialMasterForBomQuery =
  () => GQL`query GetMaterialMasterForBomQuery(
  $jump: Int,
  $perPage: Int,
  $conditions: core_material_master_bool_exp
  ) {
  core_material_master_aggregate(where: $conditions){
    aggregate {
      count
    }
  }
  core_material_master(
    where: $conditions
    limit: $perPage
    offset: $jump
    order_by: {custom_material_id: desc}
  ) {
    custom_material_id
    erp_material_id
    id
    lead_time
    material_description
    material_group
    resource_group
    type
    material_name
    material_type
    plm_material_id
    unit_of_material
    unit_cost
    unit_sale_price
    core_form {
      id
      }
    material_unit_details {
      name
      id
    }
    product_code
    material_product_code {
      id
      product_code
    }
  }
}
`

export const CreateMaterialMaster = () => GQL`mutation CreateMaterialMaster(
  $custom_material_id: String,
  $erp_material_id: String,
  $form_id: uuid,
  $type: Int,
  $gross_weight: float8,
  $inventory: Int,
  $lead_time: Int,
  $material_description: String,
  $material_group: Int,
  $resource_group: Int,
  $material_name: String,
  $others: jsonb,
  $parent_id: Int,
  $plm_material_id: String,
  $plm_record_id: String,
  $product_code: uuid,
  $effective_date: date,
  $storage_loc: Int,
  $unit_cost: float8,
  $unit_of_material: Int,
  $unit_sale_price: float8,
  $resource_state: Int,
  $weight_unit: Int) {
  insert_core_material_master_one(
    object: {
      custom_material_id: $custom_material_id,
      form_id: $form_id,
      erp_material_id: $erp_material_id,
      gross_weight: $gross_weight,
      inventory: $inventory,
      lead_time: $lead_time,
      material_description: $material_description,
      material_group: $material_group,
      resource_group: $resource_group,
      material_name: $material_name,
      type: $type,
      resource_state: $resource_state,
      others: $others,
      parent_id: $parent_id,
      plm_material_id: $plm_material_id,
      plm_record_id: $plm_record_id,
      product_code: $product_code,
      effective_date: $effective_date,
      unit_cost: $unit_cost,
      storage_loc: $storage_loc,
      unit_of_material: $unit_of_material,
      unit_sale_price: $unit_sale_price,
      weight_unit: $weight_unit,
    }
  ) {
    id
  }
}`

export const UpdateMaterialMasterDataQuery =
  () => GQL`mutation UpdateMaterialMasterDataQuery(
  $id: uuid,
  $type: Int,
  $custom_material_id: String,
  $gross_weight: float8,
  $inventory: Int,
  $lead_time: Int,
  $material_description: String,
  $material_group: Int,
  $resource_group: Int,
  $material_name: String,
  $effective_date: date,
  $storage_loc: Int,
  $unit_cost: float8,
  $unit_of_material: Int,
  $unit_sale_price: float8,
  $erp_material_id: String,
  $weight_unit: Int,
  $product_code: uuid,
    $plm_material_id: String,
  ) {
  update_core_material_master(
    where: {id: {_eq: $id}}
    _set: {
      type: $type,
      custom_material_id: $custom_material_id,
      gross_weight: $gross_weight,
      inventory: $inventory,
      lead_time: $lead_time,
      material_description: $material_description,
      material_group: $material_group,
      resource_group: $resource_group,
      material_name: $material_name,
      effective_date: $effective_date,
      storage_loc: $storage_loc,
      unit_cost: $unit_cost,
      unit_of_material: $unit_of_material,
      unit_sale_price: $unit_sale_price,
      erp_material_id: $erp_material_id,
      weight_unit: $weight_unit,
      product_code: $product_code,
      plm_material_id:$plm_material_id
    }
  ) {
    affected_rows
  }
}
`
export const insertMaterialMasterDataQuery =
  () => GQL`mutation insertMaterialMasterDataQuery(
    $objects: [core_material_master_insert_input!] = {}
  ) {
  insert_core_material_master(objects: $objects) {
    affected_rows
  }
}`

export const getProductsUsingMaterialQuery =
  () => GQL`query getProductsUsingMaterialQuery($material_id: uuid) {
  core_material_master(where: {id: {_eq: $material_id}}) {
    bom_material_items(distinct_on: bom_id) {
      core_bom {
        name
        product_bom {
          product_code
        }
      }
    }
    material_product_code {
      product_code
    }
  }
}`
export const getProjectLevelProductsUsingMaterialQuery =
  () => GQL`query getProjectLevelProductsUsingMaterialQuery($material_id: uuid, $project_id:uuid) {
  core_material_master(where: {id: {_eq: $material_id}}) {
    bom_material_items(
      distinct_on: bom_id
      where: {core_bom: {project_id: {_eq: $project_id}}}
    ) {
      core_bom {
        project_id
        name
        product_bom {
          product_code
        }
      }
    }
    material_product_code {
      product_code
    }
  }
}`

export const UpdateMaterialStatusQuery =
  () => GQL`mutation UpdateMaterialStatusQuery($id:uuid! , $status:Int!) {
  update_core_material_master_by_pk(pk_columns: {id: $id}, _set: {status: $status}) {
    id
    status
  }
}`

export const ChangeResourceStatusQuery =
  () => GQL`mutation ChangeResourceStatusQuery ($id:uuid! , $status:Int!) {
  update_core_material_master_by_pk(pk_columns: {id: $id}, _set: {resource_state: $status}) {
    id
    status
  }
}`

export const unitOfMaterial = () => GQL`query fetchingUnitOfmaterial {
  custom_list_values(
    where: {custom_list: {name: {_eq: "Unit of Material"}}, deleted: {_eq: false}}
  ) {
    id
    name
  }
}`

export const weightUnit = () => GQL`query fetchingWeightUnit {
  custom_list_values(
    where: {custom_list: {name: {_eq: "Weight Unit"}}, deleted: {_eq: false}}
  ) {
    id
    name
  }
}
`

export const materialGroup = () => GQL`query fetchingMaterialGroup {
  custom_list_values(
    where: {custom_list: {name: {_eq: "Material Group"}}, deleted: {_eq: false}}
  ) {
    id
    name
  }
}`

export const addressLocation = () => GQL`query addressLocation {
  address_locations(where: {deleted: {_eq: false}}) {
    id
    name
  }
}`

export const attchMaterilIdToDocQuery =
  () => GQL`mutation attchMaterilIdToDocQuery($material_id: uuid , $docId: uuid! ) {
  update_core_documents_by_pk(
    pk_columns: {id: $docId}
    _set: {material_id: $material_id}
  ) {
    checked_out_by
    created_by
    doc_type
    id
    material_id
  }
}`
export const attachDocToMaterailMutation = () => GQL`mutation attachDocToMaterailMutation ($data: [material_document_association_insert_input!]!) {
  insert_material_document_association(objects: $data) {
    affected_rows
  }
}`
// export const attchMaterilIdToDocQuery = () => GQL`mutation attchMaterilIdToDocQuery($material_id: Int, $docIds: uuid! ) {
//   update_core_documents(
//     _set: {material_id: $material_id}
//     where: {id: {_eq: $docIds}}
//   ) {
//     affected_rows
//     returning{
//       id
//       material_id
//     }
//   }
// }`
export const removeAttachmentsDocQuery =
  () => GQL`mutation removeAttachmentsDocQuery($materialId: uuid = "", $docIds: [uuid!] = "") {
  delete_material_document_association(
    where: {material_id: {_eq: $materialId}, document_id: {_in: $docIds}}
  ) {
    affected_rows
    returning {
      material_id
    }
  }
}`

export const checkMaterialID =
  () => GQL`query check_material_ID($materialID: String!) {
  core_material_master(where: {custom_material_id: {_eq: $materialID}}) {
    id
    custom_material_id
  }
}
`
export const upadateMaterialFormsQuery =
  () => GQL`mutation upadateMaterialFormsQuery($materialId: uuid , $formId: uuid = "") {
  update_core_material_master(where: {id: {_eq: $materialId}}, _set: {form_id: $formId}) {
    affected_rows
    returning {
      id
    }
  }
}

`

export const createMaterialMasterMutation = () => GQL`mutation createMaterialMaster($customFieldInput: [customFieldData], $docInput: [uuid!], $materialInput: material_create_input!, $productCodeInput: String, $tagInput: [uuid!], $sequenceInput: [sequence_create_input], $productCodeSequenceInput: [product_code_sequence_create_input]) {
  create_material_master(custom_fields_input: $customFieldInput, material_input: $materialInput, product_code_input: $productCodeInput, tag_input: $tagInput, doc_input: $docInput, sequence_input: $sequenceInput, product_code_sequence_input: $productCodeSequenceInput) {
    message
  }
}`
