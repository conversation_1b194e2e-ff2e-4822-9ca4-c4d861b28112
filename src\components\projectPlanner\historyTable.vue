<template>
    <div>
      <!-- Notification Section -->
      <div>
        <!-- Your existing notification container -->
        <!-- ... (Same as your existing notification template) ... -->
      </div>

      <!-- History Section -->
      <div class="history-container">
        <div class="history-navbar">
          <span class="pl-3">History</span>
        </div>
        <div class="history-box">
          <!-- Dummy Data for History -->
          <div v-for="(historyItem, index) in historyItems" :key="index" class="history-item">
            <div class="history-content">
              <div class="history-message">{{ historyItem.message }}</div>
              <div class="history-time">{{ historyItem.time }}</div>
            </div>
            <div class="history-separator"></div>
          </div>
        </div>
      </div>
    </div>
  </template>

<script>
// Your existing imports and setup

export default {
  // Your existing setup

  data () {
    return {
      // ... Your existing data properties
      historyItems: [] // Add a new data property for history items
    }
  },

  // Your existing methods and lifecycle hooks

  created () {
    // Fetch and populate dummy data for history
    this.historyItems = [
      { message: 'Changed name from <PERSON> to <PERSON><PERSON>', time: '2 hours ago' },
      { message: 'Updated project description', time: '1 day ago' },
      { message: 'Assigned task to John Doe', time: '3 days ago' },
      { message: 'Created a new milestone', time: '1 week ago' },
      { message: 'Changed name from Ravi to Kelvin', time: '2 hours ago' },
      { message: 'Updated project description', time: '1 day ago' },
      { message: 'Assigned task to John Doe', time: '3 days ago' },
      { message: 'Created a new milestone', time: '1 week ago' }
      // Add more dummy data as needed
    ]
  }

  // Additional methods or computed properties for history section can be added
}
</script>

<style scoped>
/* Your existing styles */

.history-container {
  position: relative;
  margin-top: 20px; /* Adjust the margin as needed */
}

.history-box {
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 0px;
  max-width: 600px; /* Adjust the width as needed */
  height: auto; /* Set height to auto to expand based on content */
  max-height: 200px; /* Maximum height for the history box */
  overflow-y: auto; /* Add vertical scrollbar when content exceeds max-height */
}

.history-navbar {
  font-weight: bold;
  padding: 10px 0px 10px 0px;
  background-color: var(--brand-color);
  position: sticky;
  top: 0;
  z-index: 20;
}

.history-item {
  padding: 10px;
}

.history-separator {
  border-bottom: 1px solid #ccc;
  margin-top: 5px;
}

.history-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-message {
  flex-grow: 1;
  word-wrap: break-word;
  max-width: 400px;
}

.history-time {
  font-size: 12px;
  color: #888;
}

/* Additional styles for the history section can be added */
</style>
