<template>
  <table class="user-table" >
    <thead v-if="showHeader" >
      <tr class="m">
        <th></th>
        <th>Name</th>
        <th>Role</th>
        <th>Contact No</th>
        <th>Email </th>
        <th>Escalate To</th>
        <th v-if="isTenantAdminOrCollaborator">Action</th>
      </tr>
    </thead>
    <tbody>
      <tr :class="{'s':true,'deactivatedRow':row.status===2}" v-for="(row, index) in displayData" :key="row.id" >
        <td>
          <img v-if="row.status === 1" src="~@/assets/images/active-icon.svg" alt="">
          <img v-else-if="row.status===2"  src="~@/assets/images/deactivatedUser.svg" alt="">
          <img v-else  src="~@/assets/images/inactive-icon.svg" alt="">
        </td>
        <td>{{row.associated_user?.first_name + ' ' + row.associated_user?.last_name}}</td>
        <td  v-if="userEditObject?.editIndex === index" class="td-user-role-edit">
              <select v-model="userEditObject.userRole">
                <option v-for="(role, key) in roleMap" :key="key" :value="key" >{{ role }}</option>
              </select>
            </td>
            <td v-else class="td-user-role">
              {{ row.associated_role.name }}
           </td>
        <td>{{row.associated_user?.phone || '--'}}</td>
        <td>{{row.associated_user?.email || '--'}}</td>
        <td v-if="userEditObject?.editIndex === index">
          <custom-dropdown :list="formattedUserListForReportingManagers"
          @select="selectReportingManager"
          :showDescription="true"
          @deselect="deselectReportingManager"
          :searchText="userEditObject.searchTextForReportingManager"
          :selected="userEditObject.reportingManager">
            <div slot="before-items" class="p-2 input-group xs">
            <input placeholder="Search Users" type="text" v-model="userEditObject.searchTextForReportingManager" />
            </div>
          </custom-dropdown>
        </td>
        <td v-else>{{row.reporting_manager_user ? row.reporting_manager_user.first_name + row.reporting_manager_user.last_name  : '--'}}</td>
       <td v-if="isTenantAdminOrCollaborator">
        <span v-if=" row.associated_user.id!==user.userId"> <!-- checking same logined user and given user id are same -->
        <img v-tooltip="'Re-invite user'" name="reinvite"  v-if="row.associated_user.status===4" @click="reInviteTenantUser(row.associated_user?.email)"  src="~@/assets/images/reinvite.png"  height="15px" width="15px" alt=""/>
        <img v-tooltip="'Activate user'"  name="activate" v-if="row.status===2" src="~@/assets/images/icons/undo-icon.svg"   height="20px" alt="" @click="openConfirmModal(row.associated_user,'Activate')"/>
        <span v-else-if="row.status!==4" class="v-center">
              <img name="cancel" v-if="userEditObject.editIndex === index" src="~@/assets/images/icons/close-icon.svg" v-tooltip="'Cancel'"  @click="cancelUpdateUserRole" >
              <img name="save" v-if="userEditObject.editIndex === index" src="~@/assets/images/icons/save-icon.svg" v-tooltip="'Save'"  @click="updateUserRole" >
              <img name="edit"  v-else-if="row.status===1" v-tooltip="'Edit User Role'" src="~@/assets/images/edit-icon.svg" alt="" height="20px"  @click="editUserRole(row, index)" >
              <img name="deactivate" v-if="userEditObject.editIndex === index && row.status!==2" class="ml-2"  src="~@/assets/images/deactivate_user-icon.svg" v-tooltip="'Deactivate'"  height="14px"  @click="openConfirmModal(row.associated_user,'Deactivate')"  >
            </span>
          </span>
             </td>
      </tr>
    </tbody>
    <modal
    @close="isOpen=false"
      :open="isOpen"
      title="Deactivate the user"
      >
      <div class="px-5 py-9">{{ selectedUser?.action }}</div>
      <div class="flex-end ">
        <button  class="btn btn-black mr-2 p-1" @click="isOpen=false">
          Cancel
        </button>
        <button v-if="selectedUser?.actionBtn==='Deactivate'"  class="btn " @click="deactivateUser">{{ selectedUser?.actionBtn }}</button>
        <button v-if="selectedUser?.actionBtn==='Activate'"  class="btn " @click="activateUser">{{ selectedUser?.actionBtn }}</button>
      </div>
    </modal>
  </table>
</template>

<script>
import { mapGetters } from 'vuex'
import Modal from '../../components/common/modal.vue'
import { ReInviteUser, UpdateUserRoleForTenantUser } from '@/api'
import { alert, success } from '@/plugins/notification'
import Config from '../../config'
import { GetAllUsersList } from '../../api/apis/userFlow'
import CustomDropdown from './customDropdown.vue'
export default {
  components: {
    Modal,
    CustomDropdown
  },
  name: 'user-table',
  data () {
    return {
      isOpen: false,
      roleMap: Config.userRole,
      selectedUser: '',
      formattedUserListForReportingManagers: [],
      userEditObject: {
        editIndex: -1,
        userId: '',
        userRole: '',
        reportingManager: null,
        searchTextForReportingManager: ''
      }
    }
  },
  props: {
    userList: {
      type: Array,
      default: () => ([])
    },
    pageNumber: {
      type: [Number, String],
      default: 1
    },
    perPage: {
      type: [Number, String],
      default: 10
    },
    showHeader: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    pageNumber () {
      this.resetEditObject()
    }
  },
  mounted () {
    for (const user of this.userList) {
      if (user.status === Config.USER_STATUS_MAP.ACTIVE) {
        this.formattedUserListForReportingManagers.push({
          id: user.associated_user?.id,
          name: user.associated_user?.first_name + ' ' + user.associated_user?.last_name,
          description: user.associated_user?.email
        })
      }
    }
  },
  methods: {
    resetEditObject () {
      this.userEditObject = {
        editIndex: -1,
        userId: '',
        userRole: '',
        reportingManager: null,
        searchTextForReportingManager: ''
      }
    },
    reInviteTenantUser (email) {
      const body = {
        email
      }
      ReInviteUser(body).then(data => {
        if (data.message === 'Invitation Resent') {
          success('User Re-invited successfully')
        } else {
          alert('There was an error, re-inviting the user.')
        }
      }).catch(() => {
        alert('There was an error, re-inviting the user. Please try again')
      })
    },
    selectReportingManager (item) {
      this.userEditObject.reportingManager = item
    },
    deselectReportingManager () {
      this.userEditObject.reportingManager = null
    },
    editUserRole (raw, index) {
      this.userEditObject = {
        editIndex: index,
        userId: raw.associated_user?.id,
        userRole: raw?.associated_role?.id,
        reportingManager: raw.reporting_manager_user
          ? { id: raw.reporting_manager_user.id, name: raw.reporting_manager_user.first_name + ' ' + raw.reporting_manager_user.last_name } : null,
        searchTextForReportingManager: ''
      }
    },
    cancelUpdateUserRole () {
      this.resetEditObject()
    },
    updateUserRole () {
      const { userId, userRole, reportingManager } = this.userEditObject
      UpdateUserRoleForTenantUser({
        user_id: userId,
        role_id: parseInt(userRole),
        status: undefined,
        reportingManager: reportingManager ? reportingManager.id : null
      })
        .then((res) => {
          this.getAllTenantsListData()
          this.$notify.success('User role updated successfully')
          this.resetEditObject()
        })
        .catch(() => {
          this.$notify.alert('Something went wrong')
        })
    },
    getAllTenantsListData () {
      const tenantId = localStorage.getItem('tenantId')
      GetAllUsersList(tenantId).then((res) => {
        this.$store.commit('setTenantUserList', res.tenant_user_association)
      })
    },
    openConfirmModal (user, action) {
      this.selectedUser = user
      if (action === 'Activate') {
        this.selectedUser.actionBtn = 'Activate'
        this.selectedUser.action = `Are sure to activate the ${ user?.first_name }   ${ user.last_name }`
      } else {
        this.selectedUser.action = `Are sure to deactivate the ${user?.first_name }   ${ user.last_name }`
        this.selectedUser.actionBtn = 'Deactivate'
      }
      this.isOpen = true
    },
    deactivateUser () {
      UpdateUserRoleForTenantUser({
        user_id: this.selectedUser.id,
        role_id: undefined,
        status: 2 // for deactivate
      })
        .then((res) => {
          this.getAllTenantsListData()
          this.$notify.success('Deactivated successfully')
          this.resetEditObject()
          this.isOpen = false
        })
        .catch((err) => {
          this.$notify.alert(err?.message ?? 'Something went wrong')
        })
    },
    activateUser () {
      UpdateUserRoleForTenantUser({
        user_id: this.selectedUser.id,
        role_id: undefined,
        status: 1 // for activate
      })
        .then((res) => {
          this.getAllTenantsListData()
          this.$notify.success('Activated successfully')
          this.resetEditObject()
          this.isOpen = false
        })
        .catch(() => {
          this.$notify.alert('Something went wrong')
        })
    },
    deleteUser () {
      UpdateUserRoleForTenantUser({
        user_id: this.selectedUser.id,
        role_id: undefined,
        status: 3 // for delete
      })
        .then((res) => {
          this.getAllTenantsListData()
          this.$notify.success('Deleted successfully')
          this.resetEditObject()
          this.isOpen = false
        })
        .catch(() => {
          this.$notify.alert('Something went wrong')
        })
    }
  },
  computed: {
    ...mapGetters(['user', 'getUserById', 'collaborator']),
    displayData () {
      return this.userList.slice((this.pageNumber - 1) * this.perPage, this.pageNumber * this.perPage)
    },
    isTenantAdminOrCollaborator () {
      return (
        this.user.tenantLevelRole === 'ADMIN' && !this.collaborator
      )
    }
  }
}
</script>

<style lang="scss" scoped >
.user-table {
  width: 100%;
  font-size: 16px;
  border-collapse: collapse;
  position: relative;
  .btn {
    padding: 0.5em 0.8em;
  }
  th {
    background: var(--brand-color);
    font-weight: 500;
    padding: 12px 4px;
    position: sticky;
    top: 0;
  }
  td {
    border-bottom: 1px solid var(--brand-color);
    & deactivated-icon{
      background-color: red;
      background: -webkit-gradient(red,white);
    }
  }
  th,td {
    text-align: left;
    padding: 8px 4px;
    &:nth-child(1) {
      width: 50px;
      & > img {
        width: 24px;
      }
    }
   & > span img {
            cursor: pointer;
            width: 20px;
            margin-right: 4px;
          }
   & > span img {
      cursor: pointer;
      width: 20px;
      margin-right: 4px;
    }
    & option ,select {
      text-transform: uppercase;
    }
  }
  .td-user-role{
    min-width: 120px;
    // padding: 0px;
  }
  .td-user-role-edit{
    min-width: 120px;
  }

}
.deactivatedRow{
color:var(--alert);
  }
</style>
