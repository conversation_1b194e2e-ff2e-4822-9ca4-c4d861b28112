<template>
  <div class="product-code">
    <div class="product-code__header s  ">
     <div class="space-between p-2 v-center">
      <h2 class="weight-500 l">Product Code</h2>
      <button class="btn btn-black" v-show="showAddProduct" @click="openAddMaterialModal">
        Add Product
      </button>
     </div>
     <div class=" m prod-code-search">
        <div class="prod-code-search-btnbox">
          <div ref="selectedBg" class="prod-code-search-selectedbg"></div>
          <div class="prod-code-search-prodbtn" @click="searchForProd" title="product code search" >Prod</div>
          <div class="prod-code-search-bombtn" @click="searchForBoms" title="bom name search">Bom</div>
        </div>
        <input
            type="text"
            @input="updateOnSearch"
            placeholder="Search by product code "
            v-model="searchKeyWord"
          />
        </div>
    </div>

    <div class="product-code__list">
      <product-code-item
        v-for="productCode in productTreeFiltered"
        :key="productCode.cid"
        :productObject="productCode"
      />
    </div>
    <modal
      :open="addMaterialModal"
      @close="closeAddMaterialModal"
      title="Add Product"
    >
      <create-material-master v-if="(isTenantAdmin || isProjectAdmin || !collaborator) && addMaterialModal"
        @created="closeAddMaterialModal"
        @close="closeAddMaterialModal"
        :bomForm="true"
      />
    </modal>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import Modal from '../../common/modal.vue'
import CreateMaterialMaster from '../../materialMaster/createMaterialMaster.vue'
import productCodeItem from './productCodeItem.vue'
export default {
  components: { productCodeItem, Modal, CreateMaterialMaster },
  name: 'ProductCodeList',
  data () {
    return {
      addMaterialModal: false,
      searchKeyWord: '',
      productTreeFiltered: null,
      searchType: null
    }
  },
  computed: {
    ...mapGetters('productBom', ['productCodeTree', 'bomProdSearchKeyword']),
    ...mapGetters(['user', 'isTenantAdmin', 'isProjectAdmin', 'collaborator']),
    showAddProduct () {
      if (this.collaborator) return false
      else { return (this.user.tenantLevelRole === 'ADMIN' || this.user.tenantLevelRole === 'COLLABORATOR') }
    }
  },
  methods: {
    openAddMaterialModal () {
      this.addMaterialModal = true
    },
    closeAddMaterialModal (material = false) {
      this.addMaterialModal = false
      if (material) { this.$store.dispatch('productBom/getAllProductCode') }
    },
    // this function will search the name of given  product code  list
    updateOnSearch () {
      this.searchKeyWord = (this.searchKeyWord).toLowerCase()
      if (this.searchType === 'bomLevel' && this.searchKeyWord) {
        this.$store.dispatch('productBom/getAllProductCode', { searchKeyword: this.searchKeyWord, searchType: 'bomLevel' })
        this.$store.dispatch('productBom/addProdBomSearchKeyword', this.searchKeyWord)
        return
      } else if (this.searchType === 'bomLevel') {
        this.$store.dispatch('productBom/getAllProductCode')
      }
      this.$store.dispatch('productBom/addProdBomSearchKeyword', '')
      this.productTreeFiltered = this.productCodeTree.tree?.filter((product) => {
        return this.productCodeTree.items[product.cid].product_code_name?.toLowerCase().includes(this.searchKeyWord)
      }
      )
    },
    searchForBoms () {
      this.$refs.selectedBg.style.left = '50%'
      this.searchType = 'bomLevel'
      this.updateOnSearch()
    },
    searchForProd () {
      this.$refs.selectedBg.style.left = '0'
      this.searchType = null
      this.$store.dispatch('productBom/getAllProductCode')
      this.updateOnSearch()
    }
  },
  mounted () {
    this.updateOnSearch()
  },
  watch: {
    productCodeTree () {
      if (this.searchKeyWord === '' || this.searchType === 'bomLevel') { this.productTreeFiltered = this.productCodeTree.tree } else {
        this.updateOnSearch()
      }
    }
  }

}
</script>

<style lang="scss" scoped >
.product-code {
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: var(--bg-color);
  border-right: 1px solid var(--brand-color);
  position: relative;
  &__header {
    position: sticky;
    top: 0;
    z-index: 1;
    background-color: var(--brand-color);
    display: flex;
    flex-direction: column;
& .prod-code-search{
  background-color: white;
  position: relative;
  margin:0px 5px 5 5px;
  border-radius:5px ;
  border: 1px solid rgba(59, 59, 59, 0.4666666667);
    display: block;
    width: 99%;
    border-radius: 0.285em;
    font-size: 1em;
    height: 2.5em;
    margin-bottom: 4px;
        & input{
      all:none;
      height: 100%;
      border:none;
      padding-inline: 5px;
    }
  &-btnbox{
    position: absolute;
    right: 3px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    display: flex;
    background-color: var(--bg-color);
    border: 1px solid var(--brand-color);
    border-radius: 5px;

  }
  &-prodbtn{
    background: transparent;
    padding: 2px 6px;
    z-index: 2;
  }
  &-bombtn{
  background: transparent;
    padding: 2px 6px;
    z-index: 2;
  }
  &-selectedbg{
    position: absolute;
background-color: var(--brand-color);
    border-radius: 5px;
left: 0;
top: 0;
z-index: 1;
right: 0;
width: 50%;
height: 100%;
transition: .5s;
padding-inline: 5;
  }
}
  }
}
</style>
