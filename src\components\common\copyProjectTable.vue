<template>
    <div class="copy-dtx-table project-list-table">
    <table class="" :class="{ admin: isTenantAdmin }">
      <thead v-if="showHeader">
        <tr class="m">
          <th v-overflow-tooltip>Name</th>
          <th v-overflow-tooltip>Start Date</th>
          <th v-overflow-tooltip>End Date</th>
          <th v-overflow-tooltip>Created On</th>
          <th v-overflow-tooltip>Created By</th>
          <th v-overflow-tooltip>Revenue</th>
          <th v-overflow-tooltip>Location</th>
          <th v-overflow-tooltip v-if="showProjectAction">Action</th>
        </tr>
      </thead>
      <tbody v-if="!loading && displayData.length">
        <tr class="s" v-for="row in displayData" :key="row.id">
          <td class="elipsis-text " v-overflow-tooltip >{{ row.name || "--" }}</td>
          <td v-overflow-tooltip >{{ row.planned_start_date | genericFormatDate }}</td>
          <td v-overflow-tooltip >{{ row.planned_end_date | genericFormatDate}}</td>
          <td v-overflow-tooltip >{{ row.created_on | genericFormatDate }}</td>
          <td class="elipsis-text " v-overflow-tooltip >
            {{
              getFullName(row.tenant_associated_created_by_user?.associated_user)
            }}
          </td>

          <td v-overflow-tooltip>{{ row.project_revenue }}</td>
           <td class="location-column" @click="openMaps(row.longitude, row.latitude)">
            <div class="location-content">
                <img src="~@/assets/images/map.svg"
              class="edit pointer map"
              alt=""
              title="Loaction"
              />
              <br/> <label for="">View On Map</label>
            </div>
          </td>
          <td v-if="showProjectAction" class="action-column">
            <div class="action-icons">
              <img src="~@/assets/images/pencil.svg"
              class="edit pointer"
              alt=""
              v-tooltip="'Edit'"
              @click="$emit('edit', row)"
              />
              <img src="~@/assets/images/trash-2.svg"
              class="delete pointer"
              @click="openDeleteConfirmModal(row)"
              alt=""
              v-tooltip="'Delete'"
              v-if="isTenantAdmin"
              />
            </div>
          </td>
          </tr>
      </tbody>
      <tbody v-else-if="!loading && displayData.length === 0">
        <tr>
      <td :colspan="8" class="no-data">No Data Found</td>
    </tr>
      </tbody>
      <tbody v-else>
        <tr>
          <td colspan="6">
            <div class="center">
              <loading-circle />
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  </template>

<script>
import LoadingCircle from './loadingCircle.vue'
import { genericFormatDate } from '@/utils/date'
import { mapGetters } from 'vuex'
import ConfirmationDialog from '@/plugins/confirmationDialog'

export default {
  components: { LoadingCircle },
  filters: {
    genericFormatDate
  },
  name: 'project-table',
  props: {
    projectList: {
      type: Array,
      default: () => []
    },
    pageNumber: {
      type: [Number, String],
      default: 1
    },
    perPage: {
      type: [Number, String],
      default: 10
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    externalCollaborator: {
      type: String
    }
  },
  computed: {
    ...mapGetters(['user', 'getUserById']),
    ...mapGetters(['isTenantAdmin']),
    displayData () {
      return this.projectList.slice(
        (this.pageNumber - 1) * this.perPage,
        this.pageNumber * this.perPage
      )
    },
    isTenantAdmin () {
      return (
        this.user.tenantLevelRole === 'ADMIN' && !this.externalCollaborator
      )
    },
    showProjectAction () {
      return (
        this.user.tenantLevelRole === 'ADMIN' && !this.externalCollaborator)
    }
  },
  methods: {
    openMaps (long, lat) {
      console.log(long, lat)
      if (long && lat) {
        window.open(`https://www.google.com/maps?q=${lat},${long}`, '_blank')
      }
    },
    getFullName (obj) {
      if (obj) {
        return obj?.first_name + ' ' + obj?.last_name
      }
      return '--'
    },
    openDeleteConfirmModal (project) {
      ConfirmationDialog(`Are you sure to delete the project ${project.name}`, (res) => {
        if (res) {
          this.$emit('delete', project)
        }
      })
    }
  }
}
</script>
  <style lang="scss" scoped>
  .no-data {
    text-align: center;
    vertical-align: middle;
  }
  .project-list-table {
    margin-top: 10px;
    max-height: calc(100vh - 250px);
    table th {
      z-index: 1;
    }
    .map {
      z-index: 0;
    }
  }
  </style>
  <!-- <style lang="scss" >
  .project-table {
    width: 100%;
    font-size: 16px;
    border-collapse: collapse;
    position: relative;
    th {
      background: var(--brand-color);
      font-weight: 500;
      padding: 12px 4px;
      position: sticky;
      top: 0;
    }
    td {
      border-bottom: 1px solid var(--brand-color);
    }
    th,
    td {
      text-align: left;
      padding: 8px 4px;
      &:nth-child(1) {
        width: 50px;
        & > img {
          width: 24px;
        }
      }
      & > img {
            width: 24px;
            margin-right: 8px;
      }
    }
    &.admin {
      th,
      td {
        &:last-child {
          width: 80px;
          & > img {
            width: 24px;
            margin-right: 8px;
            &.disabled {
              opacity: 0.5;
              pointer-events: none;
            }
          }
        }
      }
    }
  }
  </style> -->
