<template>
  <div
 ref="schedulerContainer"
 id="schedulerContainer"
 class="schedulerContainer"
 style="width: 100%"
></div>
</template>
<script>
import '@/assets/scheduler/dhtmlxscheduler'
import { mapGetters } from 'vuex'

let scheduler
export default {
  name: 'MilestoneTimeLineView',
  props: {
    usersData: {
      type: Object,
      default: () => ({})
    }, // users list in hashmap
    timeLineData: {
      type: Array,
      default: () => []
    },
    currentDate: { type: Date },
    viewModeParent: { type: String, default: 'monthly' }
  },
  data () {
    return {
      viewMode: this.viewModeParent,
      usersList: Object.values(this.usersData)
    }
  },
  mounted () {
    this.initScheduler(this.usersList, this.timeLineData, new Date())
  },
  computed: {
    ...mapGetters(['currentProject'])
  },
  methods: {

    initScheduler (userData, taskData, date = new Date()) {
      if (scheduler) {
        scheduler.destructor()
        scheduler = null
      }
      scheduler = window.Scheduler.getSchedulerInstance()

      scheduler.config.readonly = true // it is make the gant is readonly
      scheduler.plugins({
        timeline: true,
        tooltip: true,
        minical: true,
        drag_between: false
      })

      scheduler.attachEvent('onLoad', (id, ev, render) => {
      })
      // this is configuration for tooltip for task
      scheduler.config.className = 'dhtmlXTooltip tooltip'
      scheduler.config.timeout_to_display = 50
      scheduler.config.timeout_to_hide = 50
      scheduler.config.delta_x = 15
      scheduler.config.delta_y = -20
      scheduler.config.date_format = '%Y-%m-%d '

      const format = scheduler.date.date_to_str('%Y-%m-%d')
      scheduler.templates.tooltip_text = function (start, end, event) {
        return '<b>Task name</b> ' + event.text + '<br/><b>Start date:</b> ' +
format(start) + '<br/><b>End date:</b> ' + format(end) + '<br/><b>Project Name</b> ' + event.project_name
      }
      // window.addEventListener('DOMContentLoaded', function () {
      scheduler.locale.labels.timeline_tab = 'Timeline'
      scheduler.locale.labels.section_custom = 'Section'
      scheduler.config.details_on_create = true
      scheduler.config.details_on_dblclick = true
      var sections = userData
      scheduler.config.header = [

        {
          view: 'minicalendar',
          click: function () {
            if (scheduler.isCalendarVisible()) {
              scheduler.destroyCalendar()
            } else {
              scheduler.renderCalendar({
                position: this,
                date: scheduler.getState().date,
                navigation: true,
                handler: function (date, calendar) {
                  scheduler.setCurrentView(date)
                  scheduler.destroyCalendar()
                }
              })
            }
          }
        },
        'prev',
        { view: 'date', css: 'date_box' },
        'next',
        'spacer']
      // create a timeline in the scheduler
      scheduler.createTimelineView({
        name: 'timeline',
        render: 'bar',
        section_autoheight: false,
        round_position: true,
        event_dy: 'full',
        month: 12,
        x_unit: 'month',
        x_date: '%F',
        x_step: 1,
        x_size: 12,
        x_start: 0,
        x_length: 12,
        y_unit: sections,
        y_property: 'section_id',
        second_scale: {
          x_unit: 'year',
          x_date: '%Y',
          x_step: 1
        }
      })

      // modal
      scheduler.config.lightbox.sections = [
        { name: 'description', height: 50, map_to: 'text', type: 'textarea', focus: true },
        { name: 'custom', height: 30, type: 'select', options: sections, map_to: 'section_id' },
        { name: 'time', height: 72, type: 'time', map_to: 'auto' }
      ]
      // to setting the color based on project
      scheduler.attachEvent('onEventLoading', (ev) => {
        ev.color = '#b3d9ff'
        ev.textColor = 'black'
        return true
      })

      scheduler.init('schedulerContainer', new Date(date), 'timeline')
      scheduler.parse(taskData) // task data
    }
  },
  watch: {
    timeLineData () {
      this.initScheduler(this.usersList, this.timeLineData, scheduler._date)
    }
  }
}
</script>
<style lang="scss" scoped>
@import "../../../assets/scheduler/dhtmlxscheduler.css";

.schedulerContainer{
  height: 500px;
 overflow: auto;
}
.dhx_cal_event_line {
  height: 40px !important;
  margin-top: 1px !important;
}
</style>
