class Card {
  constructor ({ id = null, progress = null, parent = null }) {
    this.id = id
    this.progress = progress
    this.parent = parent
  }
}

export class KanbanTree {
  constructor () {
    this.tree = {}
  }

  addCard (card) {
    const newCard = new Card({ id: card.id, progress: card.progress })
    if (this.tree[card.parent]) {
      if (card.parent) {
        this.tree[card.parent].push(newCard)
      }
    } else {
      if (card.parent) {
        this.tree[card.parent] = [newCard]
      }
    }
    if (!this.tree[card.id]) {
      this.tree[card.id] = []
    }
  }

  changeProgress (id, parentId, progress) {
    this.tree[parentId].forEach(item => {
      if (item.id === id) {
        item.progress = progress
      }
    })
  }

  autoComputeProgress (id) {
    let totalProgress = 0
    this.tree[id].forEach((item) => {
      totalProgress += item.progress
    })
    return totalProgress / this.tree[id].length
  }

  arrayToTree (cards) {
    for (const card of cards) {
      this.addCard(card)
    }
  }

  haveChildren (id) {
    if (this.tree[id]?.length) {
      return true
    } else {
      return false
    }
  }
}
