/* eslint-disable */
<template>
    <div class="time-sheet column" :key="componentKey">
      <div class="time-sheet-table--container flex-grow mt-3">
        <table>
          <thead>
            <tr>
                <th>Projects</th>
              <th v-for="day in date.weekDays" :key="day" >{{ day }}</th>
            </tr>
          </thead>
          <tbody >
          <tr v-for="(value,prop) in projectsData" :key="prop+1000" >
              <td class="flex v-center">{{ projectNames[prop] }}<img class="align-center ml-2" v-if="newProjects.includes(prop)"  width="18" src="~@/assets/images/delete-icon.svg" @click="removeNewlyAddedProject(prop)"/>
</td>
              <td  v-for="(entry, index) in value" :key="index">
                <!-- (new Date() < date.formatedDates[index]) -->
                <!-- this condition is to check whether  selected Date is beyond the current date then disable from modifucation -->
                <span class="flex v-center input-box" >
                  <span class=" input-box-inner v-center " :class="[entry?.approved ?'approved':'non-approved' , (!entry?.approved && editMode  && !(new Date() < date.formatedDates[index]))?'input-box-edit-mode border-edit':'']">
                    <input v-if="entry?.subTaskData?.length < 1" class="v-center h-center "  :class=" (!entry?.approved && editMode)?'input-box-edit-mode':''" type="datetime" v-model="entry.duration"  @blur="validateEnteredValue(prop,index,$event)" :disabled="entry?.approved||entry.is_absent|| entry.is_holiday || !editMode || new Date() < date.formatedDates[index]" v-on:keyup.enter="validateEnteredValue(prop,index,$event)">
                    <input v-else-if="entry?.totalDuration" class="v-center h-center "  :class=" (!entry?.approved && editMode)?'input-box-edit-mode':''"  type="datetime" v-model="entry.totalDuration"  @blur="validateEnteredValue(prop,index,$event)" :disabled="entry?.approved||entry.is_absent||entry.is_holiday || entry?.subTaskData?.length > 0 ||  new Date()< date.formatedDates[index]" v-on:keyup.enter="validateEnteredValue(prop,index,$event)">
                    <img v-if="!(new Date() < date.formatedDates[index])"  src="~@/assets/images/eye.svg" alt="" class="input-box-eye" @click="openTimeSheetEditModal(prop,index,entry)">
                  </span>
               Hrs
                </span>
                 </td>
            </tr>
           <tr>
           </tr>
          </tbody>
        </table>
        <div class=" indication-container mt-4">
        <div :class="{
    'form-input': true,
    }" >
    <span v-if="!selectProjectBox && editMode" class=" v-center pointer" @click="selectProjectBox=!selectProjectBox">
      <img src="~@/assets/images/icons/add-new-icon.svg" alt="" width="30px" />
      Select new project
    </span>

    <span v-else-if="selectProjectBox && editMode" @mouseleave="open=false" >
      <label class="">Select Project:</label>
      <input type="text form-input" @focus="open = true" v-model="searchKeyWord_poj" @input="filterProjets">
      <div class="form-input--options" v-if="open">
        <div class="form-input--option" v-for="project in filterProjectsData" :key="project?.id" :value="project?.id" @click="addProject(project)">
          <div class="form-input--option__name" >{{ project?.name ?? "--" }}</div>
        </div>
      </div>
    </span>
  </div>
<div class="approved-indicate flex-end">
  <span class='approved-indicate-box' ></span> Approved time-sheet
</div>
</div>
  </div>
      <div class="time-sheet-table-footer flex-end mb-3" v-if="editMode">
      <button class="btn btn-black mr-3 pointer" @click="removeEditMode">Cancel</button>
      <button class="btn btn-brand mr-3 pointer"  @click="saveTimeSheet">Save</button>
</div>

  <modal
      :open="timeSheetModalData?.open"
      @close="timeSheetModalData.open = false"
      :closeOnOutsideClick="true"
      title="Edit time sheet "
    >
   <time-sheet-edit-modal
   v-if="timeSheetModalData?.open"
   :open="timeSheetModalData?.open"
   :timeSheetModalData="timeSheetModalData"
   :editMode="editMode"
   @close="timeSheetModalData.open = false"
    @addMoreTimeData="addMoreTimeData"
   />
    </modal>
</div>
  </template>

<script>
import {
  timeSheetDataBasedOnWeek,
  getlastweekUsedProjectIds,
  insertTimeSheetData,
  updateTimeSheetData, removeTimeSheetEntries, insertMaterialsData, UpdatetimesheetMaterial, DeleteTimesheetMaterial
} from '@/api'
import { getOrderdDayNumbers } from '@/utils/date'
import {
  convertToTimeFormat,
  numberToTimeFormat,
  dateFormodalConverter,
  calculateTotalDuration,
  timeStringToDecimal
} from '@/utils/timeSheetHelper'
import modal from '../common/modal.vue'
import timeSheetEditModal from './timeSheetEditModal.vue'
import Loader from '@/plugins/loader'
import { mapGetters } from 'vuex'
import { alert, success } from '@/plugins/notification'

export default {
  components: {
    modal,
    timeSheetEditModal
  },
  name: 'timeSheetTable',
  props: {
    date: {
      type: Object,
      default: () => ({ weekDays: [], formatedDates: [] })
    },
    editMode: { type: Boolean, required: true },
    lastWeekDates: {
      type: Array,
      default: () => ([])
    },
    notViewerProjects: {
      type: Array,
      default: () => ([])
    }
  },
  data: function () {
    return {
      view: 'weekly',
      timeSheetModalData: {
        open: false,
        prop: null,
        index: null
      },
      selectProjectBox: false,
      searchKeyWord_poj: '',
      startDate: '',
      endDate: '',
      selectedDate: null,
      availableProjects: [],
      projectsData: {},
      open: false,
      projectNames: {},
      componentKey: 0,
      filterProjectsData: [],
      newProjects: []
    }
  },
  computed: {
    ...mapGetters(['user', 'getUserById', 'collaborator']),
    ...mapGetters(['isTenantAdmin', 'isProjectAdmin', 'isTenantViewer', 'isProjectViewer', 'isOnProjectLevel']),
    getUserId () {
      return (this.user.userId)
    }
  },
  methods: {
    setup (startDate = false, endDate = false) {
      this.newProjects = []
      const loader = new Loader()
      loader.show()
      this.projectsData = {}
      timeSheetDataBasedOnWeek(startDate || this.date.formatedDates[0], endDate || this.date.formatedDates[6], this.getUserId).then(res => {
        res.user_timesheet.forEach(data => {
          const durationInString = numberToTimeFormat(data.duration)
          const timeRemainingString = data.time_remaining ? numberToTimeFormat(data.time_remaining) : '0'
          if (Object.hasOwn(this.projectsData, data?.project_id)) {
            if (this.projectsData[`${data.project_id}`]?.length > 0) {
              let flag = false
              for (let i = 0; i < this.projectsData[`${data.project_id}`]?.length; i++) {
                if ((new Date(this.projectsData[`${data.project_id}`][i]?.entry_date)?.getDay() === new Date(data.entry_date).getDay())) {
                  data.duration = durationInString // numberToTimeFormat(data.duration)
                  data.time_remaining = timeRemainingString
                  this.projectsData[`${data.project_id}`][i].subTaskData.push(data)
                  // this code to find the total duration
                  this.projectsData[`${data.project_id}`][i].totalDuration = calculateTotalDuration(this.projectsData[`${data.project_id}`][i].subTaskData)
                  flag = true
                  break
                }
              }
              if (!flag) {
                const day = new Date(data.entry_date).getDay() // return the number of day (sun=0....sat=6)
                const dayIndex = getOrderdDayNumbers(day) // chanhes the day into  required order (mon=0,tues=1...sunday=6)
                this.projectsData[`${data.project_id}`][dayIndex] = data
                this.projectsData[`${data.project_id}`][dayIndex].duration = durationInString // numberToTimeFormat(data.duration)
                this.projectsData[`${data.project_id}`][dayIndex].time_remaining = timeRemainingString
                this.projectsData[`${data.project_id}`][dayIndex].totalDuration = durationInString
                this.projectsData[`${data.project_id}`][dayIndex].removedTaskIds = []
                data.duration = durationInString // numberToTimeFormat(data.duration)
                data.time_remaining = timeRemainingString // numberToTimeFormat(data.duration)
                if (this.projectsData[`${data.project_id}`][dayIndex].task_id) {
                  const timeSheetObj = {
                    approved: data?.approved,
                    duration: data.duration,
                    time_remaining: data.time_remaining,
                    project_id: data.project_id,
                    description: data.description,
                    entry_date: data.entry_date,
                    is_absent: data.is_absent,
                    is_holiday: data.is_holiday,
                    tag_id: data.tag_id,
                    task_id: data.task_id,
                    time_end: data.time_end,
                    time_start: data.time_start,
                    id: data.id,
                    rework: data.rework
                  }
                  timeSheetObj.timesheet_material_logs = data.timesheet_material_logs
                  this.projectsData[`${data.project_id}`][dayIndex].subTaskData = [timeSheetObj]
                } else {
                  this.projectsData[`${data.project_id}`][dayIndex].subTaskData = []
                }

                this.addtoProjectNames(data.project_id, data.core_projects?.name)
              }
            } else {
            }
          } else {
            this.projectsData[`${data.project_id}`] = [undefined, undefined, undefined, undefined, undefined, undefined, undefined]
            const day = new Date(data.entry_date).getDay() // return the number of day (sun=0....sat=6)
            const dayIndex = getOrderdDayNumbers(day) // chanhes the day into  required order (mon=0,tues=1...sunday=6)
            this.projectsData[`${data.project_id}`][dayIndex] = JSON.parse(JSON.stringify(data))
            this.projectsData[`${data.project_id}`][dayIndex].duration = durationInString
            this.projectsData[`${data.project_id}`][dayIndex].time_remaining = timeRemainingString
            this.projectsData[`${data.project_id}`][dayIndex].subTaskData = []
            this.projectsData[`${data.project_id}`][dayIndex].removedTaskIds = []
            this.projectsData[`${data.project_id}`][dayIndex].totalDuration = durationInString
            if (data.task_id) {
              data.duration = durationInString
              data.time_remaining = timeRemainingString
              this.projectsData[`${data.project_id}`][dayIndex].subTaskData.push(data)
            }
            // const day = new Date(data.entry_date).getDay()
            // const dayIndex = getOrderdDayNumbers(day)
            // this.projectsData[data.project_id][dayIndex].duration = numberToTimeFormat(data.duration)
            this.addtoProjectNames(data.project_id, data?.core_project?.name)
          }

          // this.forceRender()
        })
        this.getAllAssociatedProjects()
        this.fillAllEmptyObjects()
        this.forceRender()
        loader.hide()
      })
        .catch((err) => {
          console.log(err)
          loader.hide()
        })
    },
    getAllAssociatedProjects () {
      this.filterProjectsData = this.availableProjects = this.notViewerProjects.filter(project => {
        if (Object.hasOwn(this.projectsData, String(project?.id))) {

        } else {
          return project
        }
      })
    },
    addProject (project) {
      this.newProjects.push(`${project.id}`) // this is to understand which all the projects are added newly
      this.selectProjectBox = false
      this.open = false
      this.projectsData[project.id] = []
      this.addtoProjectNames(project.id, project?.name)
      for (let i = 0; i < 7; i++) {
        this.projectsData[project.id].push({
          duration: '0',
          totalDuration: null,
          project_id: project.id,
          approved: false,
          description: null,
          entry_date: this.date.formatedDates[i],
          is_absent: false,
          is_holiday: false,
          tag_id: null,
          task_id: null,
          time_end: null,
          time_start: null,
          change: 'new',
          rework: null,
          time_remaining: null,
          projected_end_date: null,
          subTaskData: [],
          removedTaskIds: []
        })
      }
      this.filterProjets()
    },
    addtoProjectNames (id, name) {
      if (Object.hasOwn(this.projectNames, id)) {

      } else {
        this.projectNames[id] = name
      }
    },
    validateEnteredValue (prop, index, e) {
      const newtime = convertToTimeFormat(String(String(e.target.value)))
      this.projectsData[prop][index].duration = newtime
      if (this.projectsData[prop][index].id) {
        this.projectsData[prop][index].change = 'update'
      } else {
        this.projectsData[prop][index].change = 'new'
      }
      this.forceRender()
    },
    forceRender () {
      this.componentKey += 1
    },
    openTimeSheetEditModal (prop, index, entry) {
      this.timeSheetModalData = {
        open: true,
        prop: prop,
        index: index,
        selectedDate: dateFormodalConverter(this.date.formatedDates[index]),
        selectedProject: {
          id: prop,
          name: this.projectNames[prop]
        },
        entry: entry
      }
    },
    addMoreTimeData (data) {
      this.timeSheetModalData.open = false
      this.projectsData[data.timeSheetModalData.prop][data.timeSheetModalData.index] = data.entryData
      // this.forceRender()
    },
    fillAllEmptyObjects () {
      for (const projectIndex in this.projectsData) {
        for (let i = 0; i < 7; i++) {
          if (!this?.projectsData[projectIndex][i]) {
            this.projectsData[projectIndex][i] = {
              duration: '0',
              totalDuration: null,
              project_id: projectIndex,
              approved: false,
              description: null,
              entry_date: this.date.formatedDates[i],
              is_absent: false,
              is_holiday: false,
              tag_id: null,
              task_id: null,
              time_end: null,
              time_start: null,
              change: 'new',
              rework: null,
              time_remaining: null,
              projected_end_date: null,
              subTaskData: [],
              removedTaskIds: []
            }
          }
        }
      }
    },
    filterProjets () {
      this.open = true

      if (!this.searchKeyWord_poj) {
        this.filterProjectsData = this.availableProjects.filter(project => {
          if (Object.hasOwn(this.projectsData, String(project.id))) {
          } else {
            return project
          }
        })
      } else {
        this.filterProjectsData = this.availableProjects.filter((project) => {
          if (project.name.toLowerCase().includes(this.searchKeyWord_poj.toLowerCase()) && !Object.hasOwn(this.projectsData, project.id)) {
            return project
          }
        })
      }
    },
    removeEditMode () {
      this.$emit('removeEditMode')
      this.setup(this.date.formatedDates[0], this.date.formatedDates[6])
    },
    setupLastweekTemplate () {
      const lasweekProjectIds = []
      this.lastWeekDates.length && getlastweekUsedProjectIds(this.lastWeekDates[0], this.lastWeekDates[6], this.getUserId).then(response => {
        response.user_timesheet_aggregate.nodes.forEach((element) => {
          element.core_project && lasweekProjectIds.push(element.core_project)
        })
        // which contain project ids of already setup data for current week
        const currentWeekProjectIds = Object.keys(this.projectsData)
        lasweekProjectIds.forEach(element => {
          if (currentWeekProjectIds.includes(String(element?.id))) {} else {
            this.addProject(element)
          }
        })
        this.forceRender()
      })
    },
    async saveTimeSheet () {
      const projectsData = this.projectsData
      const loader = new Loader()
      loader.show()
      try {
        const allTimeSheetObjects = []
        const forUpdate = []
        const insertNewSheetData = []
        const removedEntries = []
        const insertMaterialData = []
        let timeSheetMaterialLogs = null
        for (const prop in projectsData) {
          projectsData[prop].forEach(obj => {
            if (obj?.subTaskData?.length > 0 || obj?.removedTaskIds?.length > 0) {
              if (obj?.subTaskData?.length > 0) {
                allTimeSheetObjects.push(...obj.subTaskData)
              }
              if (obj?.removedTaskIds?.length > 0) {
                removedEntries.push(...obj.removedTaskIds)
              }
              if (obj?.removedTaskIds?.length > 0 && obj?.subTaskData?.length <= 0) {
                allTimeSheetObjects.push(obj)
              }
            } else {
              allTimeSheetObjects.push(obj)
            }
          })
        }
        allTimeSheetObjects.forEach((element) => {
          if (element.materialData) {
            const updatedMaterialData = element.materialData.map(({ material_name: materialName, tag, ...rest }) => rest)
            timeSheetMaterialLogs = {
              data: updatedMaterialData
            }
          }
          if (element?.approved === true) {
          } else if (element?.change === 'new') {
            const timeSheetObj = {
              duration: timeStringToDecimal(element.duration) || 0,
              project_id: element.project_id,
              description: element.description,
              entry_date: new Date(element.entry_date).toLocaleDateString('en-US'),
              is_absent: element.is_absent,
              is_holiday: element.is_holiday,
              tag_id: element.tag_id || undefined,
              task_id: element.task_id || undefined,
              time_remaining: timeStringToDecimal(String(element.time_remaining)) || null,
              rework: element?.rework || false,
              projected_end_date: element.projected_end_date || null
            }
            timeSheetObj.timesheet_material_logs = element.materialData ? timeSheetMaterialLogs : null
            insertNewSheetData.push(timeSheetObj)
          } else if (element?.change === 'update') {
            forUpdate.push({
              duration: timeStringToDecimal(String(element.duration)) || 0,
              id: element.id,
              description: element.description,
              entryDate: new Date(element.entry_date).toLocaleDateString('en-US'),
              isAbsent: element.is_absent,
              isHoliday: element.is_holiday,
              tagId: element.tag_id || undefined,
              taskId: element.task_id || undefined,
              timeRemaining: timeStringToDecimal(String(element.time_remaining)) || null,
              rework: element.rework || false,
              projectedEndDate: element.projected_end_date || null
            })
          }
          if (element.materialData) {
            element.materialData.forEach(async (item) => {
              if (item.tag === 'new' && element.change !== 'new') {
                insertMaterialData.push({
                  material_id: item.material_id,
                  quantity: item.quantity,
                  type: item.type,
                  timesheet_id: element.id
                })
              } else if (item.tag === 'update') {
                UpdatetimesheetMaterial({
                  materialId: item.material_id,
                  quantity: item.quantity,
                  type: item.type,
                  timesheetId: element.id
                })
              } else if (item.tag === 'delete') {
                await DeleteTimesheetMaterial({
                  material_id: item.material_id,
                  timesheet_id: element.id
                })
              }
            })
          }
        })
        insertMaterialData.length > 0 && await insertMaterialsData(insertMaterialData)
        await Promise.allSettled(forUpdate.map(obj => updateTimeSheetData(obj)))
        insertNewSheetData.length > 0 && await insertTimeSheetData(insertNewSheetData)
        removedEntries.length > 0 && await removeTimeSheetEntries(removedEntries)
        success('time sheet updated successfully')
        this.projectsData = {}
        loader.hide()
        this.removeEditMode()
      } catch (err) {
        loader.hide()
        alert('some thing went wrong')
      }
    },
    removeNewlyAddedProject (id) {
      delete this.projectsData[id]
      this.forceRender()
      this.filterProjets()
    },
    keyPress (e) {
      if (this.timeSheetModalData.open) { return }
      if (e instanceof KeyboardEvent && e.code === 'Escape') {
        this.removeEditMode()
      } else if (e instanceof KeyboardEvent && e.code === 'Enter') {
        this.saveTimeSheet()
      }
    }

  },
  mounted () {
    this.setup()
    this.getAllAssociatedProjects()
  },
  watch: {
    'date.formatedDates' () {
      this.setup()
      this.getAllAssociatedProjects()
      this.open = false
    },
    'notViewerProjects' () {
      this.getAllAssociatedProjects()
    },
    lastWeekDates () {
      this.setupLastweekTemplate()
    },
    saved () {
      this.saved !== 0 && this.setup(this.date.formatedDates[0], this.date.formatedDates[6])
    },
    searchKeyWord_poj () {
      this.filterProjets()
    }
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
  }
}
</script>

  <style lang="scss" scoped>

  .time-sheet {
    height: calc(100% - 147px);
    margin-top: 10px;
   &-table{
    &--container {
      height: calc(100%);
      overflow: auto;
      table {
        width: 100%;
        position: relative;
        border-collapse: collapse;
        th {
          position: sticky;
          top: -1px;
          font-weight: 500;
          background-color: var(--brand-color);
        }
        th,
        td {
          text-align: left;
          font-size: 12px;
          padding: 8px 4px;
        }
        td {
         & input{
          width: 80px;
          border: none;
          background: transparent;
        }
       & .input-box{
        gap: 4px;
        &-edit-mode{
        background-color: white;
        }
        &-inner{
          padding:5px;
          position: relative;
          img {
        position: absolute;
        max-height: 70%;
        max-width: 70%;
        object-fit: contain;
        opacity:.6;
        top:5px;
        right: 4px;
        display:none;
      }
      &:hover > img{
        display: block;
      }
        }

        & .approved{
          border: 1.6px solid #8beeb8;
    background: #c8e4c85e;
    border-radius: 4px;
        }
        & .non-approved{
          border:1px solid #e5e5e5;
        }
        & .border-edit{
  border: 1px solid #938585;
}
        }
      }
        tr:nth-child(odd) {
          background-color: rgba(var(--brand-rgb), 0.05);
          border: 1px solid var(--brand-color);
        }
      }
    }
  }
}
.form-input{
  width: 200px;
  z-index:2;
  &--options
  {
  width: 200px;
}
}

.approved-indicate-box{
  border: 1.6px solid #8beeb8;
    background: #c8e4c85e;
height: 1.5rem;
width: 1.5rem;
border-radius:4px;
margin-right: .5rem;
}
.indication-container{
  display:grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 10px;
  padding: 1rem;
}
  </style>
