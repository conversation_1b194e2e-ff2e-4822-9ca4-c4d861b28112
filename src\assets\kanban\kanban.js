/* eslint-disable */
export var kanban=function(Be){"use strict";/**
      @license
      
      DHTMLX Kanban v.1.6.3 
      
      This software is covered by DHTMLX Evaluation License and purposed only for evaluation.
      Contact <EMAIL> to get Commercial or Enterprise license.
      Usage without proper license is prohibited.
      
      (c) XB Software.
      
      **/
      function K(){}const nl=l=>l;function Ce(l,e){for(const t in e)l[t]=e[t];return l}function ll(l){return l()}function il(){return Object.create(null)}function De(l){l.forEach(ll)}function nt(l){return typeof l=="function"}function Z(l,e){return l!=l?e==e:l!==e||l&&typeof l=="object"||typeof l=="function"}let Gt;function Jt(l,e){return l===e?!0:(Gt||(Gt=document.createElement("a")),Gt.href=e,l===Gt.href)}function Zo(l){return Object.keys(l).length===0}function Ge(l,...e){if(l==null){for(const n of e)n(void 0);return K}const t=l.subscribe(...e);return t.unsubscribe?()=>t.unsubscribe():t}function he(l,e,t){l.$$.on_destroy.push(Ge(e,t))}function be(l,e,t,n){if(l){const i=sl(l,e,t,n);return l[0](i)}}function sl(l,e,t,n){return l[1]&&n?Ce(t.ctx.slice(),l[1](n(e))):t.ctx}function pe(l,e,t,n){if(l[2]&&n){const i=l[2](n(t));if(e.dirty===void 0)return i;if(typeof i=="object"){const s=[],r=Math.max(e.dirty.length,i.length);for(let a=0;a<r;a+=1)s[a]=e.dirty[a]|i[a];return s}return e.dirty|i}return e.dirty}function we(l,e,t,n,i,s){if(i){const r=sl(e,t,n,s);l.p(r,i)}}function ke(l){if(l.ctx.length>32){const e=[],t=l.ctx.length/32;for(let n=0;n<t;n++)e[n]=-1;return e}return-1}function We(l){const e={};for(const t in l)t[0]!=="$"&&(e[t]=l[t]);return e}function rl(l,e){const t={};e=new Set(e);for(const n in l)!e.has(n)&&n[0]!=="$"&&(t[n]=l[n]);return t}function Qt(l){const e={};for(const t in l)e[t]=!0;return e}function qe(l){return l??""}function Pt(l,e,t){return l.set(t),e}function He(l){return l&&nt(l.destroy)?l.destroy:K}const ol=typeof window<"u";let $o=ol?()=>window.performance.now():()=>Date.now(),wn=ol?l=>requestAnimationFrame(l):K;const bt=new Set;function al(l){bt.forEach(e=>{e.c(l)||(bt.delete(e),e.f())}),bt.size!==0&&wn(al)}function xo(l){let e;return bt.size===0&&wn(al),{promise:new Promise(t=>{bt.add(e={c:l,f:t})}),abort(){bt.delete(e)}}}const ea=typeof window<"u"?window:typeof globalThis<"u"?globalThis:global;function M(l,e){l.appendChild(e)}function ul(l){if(!l)return document;const e=l.getRootNode?l.getRootNode():l.ownerDocument;return e&&e.host?e:l.ownerDocument}function ta(l){const e=I("style");return e.textContent="/* empty */",na(ul(l),e),e.sheet}function na(l,e){return M(l.head||l,e),e.sheet}function v(l,e,t){l.insertBefore(e,t||null)}function k(l){l.parentNode&&l.parentNode.removeChild(l)}function at(l,e){for(let t=0;t<l.length;t+=1)l[t]&&l[t].d(e)}function I(l){return document.createElement(l)}function la(l){return document.createElementNS("http://www.w3.org/2000/svg",l)}function $(l){return document.createTextNode(l)}function H(){return $(" ")}function se(){return $("")}function te(l,e,t,n){return l.addEventListener(e,t,n),()=>l.removeEventListener(e,t,n)}function pt(l){return function(e){return e.stopPropagation(),l.call(this,e)}}function g(l,e,t){t==null?l.removeAttribute(e):l.getAttribute(e)!==t&&l.setAttribute(e,t)}function kn(l){return l===""?null:+l}function ia(l){return Array.from(l.childNodes)}function re(l,e){e=""+e,l.data!==e&&(l.data=e)}function je(l,e){l.value=e??""}function ce(l,e,t,n){t==null?l.style.removeProperty(e):l.style.setProperty(e,t,"")}function fl(l,e,t){for(let n=0;n<l.options.length;n+=1){const i=l.options[n];if(i.__value===e){i.selected=!0;return}}(!t||e!==void 0)&&(l.selectedIndex=-1)}function sa(l){const e=l.querySelector(":checked");return e&&e.__value}let Xt;function ra(){if(Xt===void 0){Xt=!1;try{typeof window<"u"&&window.parent&&window.parent.document}catch{Xt=!0}}return Xt}function oa(l,e){getComputedStyle(l).position==="static"&&(l.style.position="relative");const n=I("iframe");n.setAttribute("style","display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; border: 0; opacity: 0; pointer-events: none; z-index: -1;"),n.setAttribute("aria-hidden","true"),n.tabIndex=-1;const i=ra();let s;return i?(n.src="data:text/html,<script>onresize=function(){parent.postMessage(0,'*')}<\/script>",s=te(window,"message",r=>{r.source===n.contentWindow&&e()})):(n.src="about:blank",n.onload=()=>{s=te(n.contentWindow,"resize",e),e()}),M(l,n),()=>{(i||s&&n.contentWindow)&&s(),k(n)}}function X(l,e,t){l.classList.toggle(e,!!t)}function cl(l,e,{bubbles:t=!1,cancelable:n=!1}={}){return new CustomEvent(l,{detail:e,bubbles:t,cancelable:n})}class Zt{is_svg=!1;e=void 0;n=void 0;t=void 0;a=void 0;constructor(e=!1){this.is_svg=e,this.e=this.n=null}c(e){this.h(e)}m(e,t,n=null){this.e||(this.is_svg?this.e=la(t.nodeName):this.e=I(t.nodeType===11?"TEMPLATE":t.nodeName),this.t=t.tagName!=="TEMPLATE"?t:t.content,this.c(e)),this.i(n)}h(e){this.e.innerHTML=e,this.n=Array.from(this.e.nodeName==="TEMPLATE"?this.e.content.childNodes:this.e.childNodes)}i(e){for(let t=0;t<this.n.length;t+=1)v(this.t,this.n[t],e)}p(e){this.d(),this.h(e),this.i(this.a)}d(){this.n.forEach(k)}}function Je(l,e){return new l(e)}const $t=new Map;let xt=0;function aa(l){let e=5381,t=l.length;for(;t--;)e=(e<<5)-e^l.charCodeAt(t);return e>>>0}function ua(l,e){const t={stylesheet:ta(e),rules:{}};return $t.set(l,t),t}function dl(l,e,t,n,i,s,r,a=0){const o=16.666/n;let u=`{
      `;for(let w=0;w<=1;w+=o){const y=e+(t-e)*s(w);u+=w*100+`%{${r(y,1-y)}}
      `}const f=u+`100% {${r(t,1-t)}}
      }`,c=`__svelte_${aa(f)}_${a}`,d=ul(l),{stylesheet:m,rules:_}=$t.get(d)||ua(d,l);_[c]||(_[c]=!0,m.insertRule(`@keyframes ${c} ${f}`,m.cssRules.length));const h=l.style.animation||"";return l.style.animation=`${h?`${h}, `:""}${c} ${n}ms linear ${i}ms 1 both`,xt+=1,c}function fa(l,e){const t=(l.style.animation||"").split(", "),n=t.filter(e?s=>s.indexOf(e)<0:s=>s.indexOf("__svelte")===-1),i=t.length-n.length;i&&(l.style.animation=n.join(", "),xt-=i,xt||ca())}function ca(){wn(()=>{xt||($t.forEach(l=>{const{ownerNode:e}=l.stylesheet;e&&k(e)}),$t.clear())})}let Nt;function zt(l){Nt=l}function wt(){if(!Nt)throw new Error("Function called outside component initialization");return Nt}function ot(l){wt().$$.on_mount.push(l)}function vn(l){wt().$$.after_update.push(l)}function yn(l){wt().$$.on_destroy.push(l)}function Ee(){const l=wt();return(e,t,{cancelable:n=!1}={})=>{const i=l.$$.callbacks[e];if(i){const s=cl(e,t,{cancelable:n});return i.slice().forEach(r=>{r.call(l,s)}),!s.defaultPrevented}return!0}}function _t(l,e){return wt().$$.context.set(l,e),e}function ve(l){return wt().$$.context.get(l)}function ye(l,e){const t=l.$$.callbacks[e.type];t&&t.slice().forEach(n=>n.call(this,e))}const kt=[],ue=[];let vt=[];const Sn=[],ml=Promise.resolve();let Cn=!1;function _l(){Cn||(Cn=!0,ml.then(hl))}function ct(){return _l(),ml}function ut(l){vt.push(l)}function Ie(l){Sn.push(l)}const In=new Set;let yt=0;function hl(){if(yt!==0)return;const l=Nt;do{try{for(;yt<kt.length;){const e=kt[yt];yt++,zt(e),da(e.$$)}}catch(e){throw kt.length=0,yt=0,e}for(zt(null),kt.length=0,yt=0;ue.length;)ue.pop()();for(let e=0;e<vt.length;e+=1){const t=vt[e];In.has(t)||(In.add(t),t())}vt.length=0}while(kt.length);for(;Sn.length;)Sn.pop()();Cn=!1,In.clear(),zt(l)}function da(l){if(l.fragment!==null){l.update(),De(l.before_update);const e=l.dirty;l.dirty=[-1],l.fragment&&l.fragment.p(l.ctx,e),l.after_update.forEach(ut)}}function ma(l){const e=[],t=[];vt.forEach(n=>l.indexOf(n)===-1?e.push(n):t.push(n)),t.forEach(n=>n()),vt=e}let Ht;function _a(){return Ht||(Ht=Promise.resolve(),Ht.then(()=>{Ht=null})),Ht}function Mn(l,e,t){l.dispatchEvent(cl(`${e?"intro":"outro"}${t}`))}const en=new Set;let ft;function W(){ft={r:0,c:[],p:ft}}function Y(){ft.r||De(ft.c),ft=ft.p}function b(l,e){l&&l.i&&(en.delete(l),l.i(e))}function p(l,e,t,n){if(l&&l.o){if(en.has(l))return;en.add(l),ft.c.push(()=>{en.delete(l),n&&(t&&l.d(1),n())}),l.o(e)}else n&&n()}const ha={duration:0};function St(l,e,t,n){let s=e(l,t,{direction:"both"}),r=n?0:1,a=null,o=null,u=null,f;function c(){u&&fa(l,u)}function d(_,h){const w=_.b-r;return h*=Math.abs(w),{a:r,b:_.b,d:w,duration:h,start:_.start,end:_.start+h,group:_.group}}function m(_){const{delay:h=0,duration:w=300,easing:y=nl,tick:S=K,css:C}=s||ha,T={start:$o()+h,b:_};_||(T.group=ft,ft.r+=1),"inert"in l&&(_?f!==void 0&&(l.inert=f):(f=l.inert,l.inert=!0)),a||o?o=T:(C&&(c(),u=dl(l,r,_,w,h,y,C)),_&&S(0,1),a=d(T,w),ut(()=>Mn(l,_,"start")),xo(L=>{if(o&&L>o.start&&(a=d(o,w),o=null,Mn(l,a.b,"start"),C&&(c(),u=dl(l,r,a.b,a.duration,0,y,s.css))),a){if(L>=a.end)S(r=a.b,1-r),Mn(l,a.b,"end"),o||(a.b?c():--a.group.r||De(a.group.c)),a=null;else if(L>=a.start){const N=L-a.start;r=a.a+a.d*y(N/a.duration),S(r,1-r)}}return!!(a||o)}))}return{run(_){nt(s)?_a().then(()=>{s=s({direction:_?"in":"out"}),m(_)}):m(_)},end(){c(),a=o=null}}}function fe(l){return l?.length!==void 0?l:Array.from(l)}function gl(l,e){l.d(1),e.delete(l.key)}function Ue(l,e){p(l,1,1,()=>{e.delete(l.key)})}function Oe(l,e,t,n,i,s,r,a,o,u,f,c){let d=l.length,m=s.length,_=d;const h={};for(;_--;)h[l[_].key]=_;const w=[],y=new Map,S=new Map,C=[];for(_=m;_--;){const E=c(i,s,_),F=t(E);let G=r.get(F);G?C.push(()=>G.p(E,e)):(G=u(F,E),G.c()),y.set(F,w[_]=G),F in h&&S.set(F,Math.abs(_-h[F]))}const T=new Set,L=new Set;function N(E){b(E,1),E.m(a,f),r.set(E.key,E),f=E.first,m--}for(;d&&m;){const E=w[m-1],F=l[d-1],G=E.key,U=F.key;E===F?(f=E.first,d--,m--):y.has(U)?!r.has(G)||T.has(G)?N(E):L.has(U)?d--:S.get(G)>S.get(U)?(L.add(G),N(E)):(T.add(U),d--):(o(F,r),d--)}for(;d--;){const E=l[d];y.has(E.key)||o(E,r)}for(;m;)N(w[m-1]);return De(C),w}function st(l,e){const t={},n={},i={$$scope:1};let s=l.length;for(;s--;){const r=l[s],a=e[s];if(a){for(const o in r)o in a||(n[o]=1);for(const o in a)i[o]||(t[o]=a[o],i[o]=1);l[s]=a}else for(const o in r)i[o]=1}for(const r in n)r in t||(t[r]=void 0);return t}function rt(l){return typeof l=="object"&&l!==null?l:{}}function Se(l,e,t){const n=l.$$.props[e];n!==void 0&&(l.$$.bound[n]=t,t(l.$$.ctx[n]))}function P(l){l&&l.c()}function R(l,e,t){const{fragment:n,after_update:i}=l.$$;n&&n.m(e,t),ut(()=>{const s=l.$$.on_mount.map(ll).filter(nt);l.$$.on_destroy?l.$$.on_destroy.push(...s):De(s),l.$$.on_mount=[]}),i.forEach(ut)}function A(l,e){const t=l.$$;t.fragment!==null&&(ma(t.after_update),De(t.on_destroy),t.fragment&&t.fragment.d(e),t.on_destroy=t.fragment=null,t.ctx=[])}function ga(l,e){l.$$.dirty[0]===-1&&(kt.push(l),_l(),l.$$.dirty.fill(0)),l.$$.dirty[e/31|0]|=1<<e%31}function x(l,e,t,n,i,s,r=null,a=[-1]){const o=Nt;zt(l);const u=l.$$={fragment:null,ctx:[],props:s,update:K,not_equal:i,bound:il(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(o?o.$$.context:[])),callbacks:il(),dirty:a,skip_bound:!1,root:e.target||o.$$.root};r&&r(u.root);let f=!1;if(u.ctx=t?t(l,e.props||{},(c,d,...m)=>{const _=m.length?m[0]:d;return u.ctx&&i(u.ctx[c],u.ctx[c]=_)&&(!u.skip_bound&&u.bound[c]&&u.bound[c](_),f&&ga(l,c)),d}):[],u.update(),f=!0,De(u.before_update),u.fragment=n?n(u.ctx):!1,e.target){if(e.hydrate){const c=ia(e.target);u.fragment&&u.fragment.l(c),c.forEach(k)}else u.fragment&&u.fragment.c();e.intro&&b(l.$$.fragment),R(l,e.target,e.anchor),hl()}zt(o)}class ee{$$=void 0;$$set=void 0;$destroy(){A(this,1),this.$destroy=K}$on(e,t){if(!nt(t))return K;const n=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return n.push(t),()=>{const i=n.indexOf(t);i!==-1&&n.splice(i,1)}}$set(e){this.$$set&&!Zo(e)&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}}const ba="4";typeof window<"u"&&(window.__svelte||(window.__svelte={v:new Set})).v.add(ba);const Tn={kanban:{Save:"Save",Close:"Close",Delete:"Delete",Name:"Name",Description:"Description",Type:"Type","Start date":"Start date","End date":"End date",Result:"Result","No results":"No results",Search:"Search","Search in":"Search in","Add new row":"Add new row","Add new column":"Add new column","Add new card...":"Add new card...","Add new card":"Add new card","Edit card":"Edit card",Edit:"Edit",Everywhere:"Everywhere",Label:"Label",Status:"Status",Color:"Color",Date:"Date",Priority:"Priority",Progress:"Progress",Users:"Users",Untitled:"Untitled",Rename:"Rename","Move up":"Move up","Move down":"Move down","Move left":"Move left","Move right":"Move right",Sort:"Sort","Label (a-z)":"Label (a-z)","Label (z-a)":"Label (z-a)","Description (a-z)":"Description (a-z)","Description (z-a)":"Description (z-a)",Duplicate:"Duplicate","Duplicate of":"Duplicate of","Relates to":"Relates to","Depends on":"Depends on","Is required for":"Is required for",Duplicates:"Duplicates","Is duplicated by":"Is duplicated by","Parent for":"Parent for","SubTask of":"SubTask of",Cancel:"Cancel","Link task":"Link task","Select a relation":"Select a relation","Select a task":"Select a task",Send:"Send","Would you like to delete this comment?":"Would you like to delete this comment?","No comments yet":"No comments yet","Would you like to delete this card?":"Would you like to delete this card?"}},bl={core:{ok:"OK",cancel:"Cancel"},calendar:{monthFull:["January","February","March","April","May","June","July","August","September","October","November","December"],monthShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayFull:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],hours:"Hours",minutes:"Minutes",done:"Done",clear:"Clear",today:"Today",am:["am","AM"],pm:["pm","PM"],weekStart:7,clockFormat:24},formats:{timeFormat:"%H:%i",dateFormat:"%m/%d/%Y"}};let pl=new Date().valueOf();const pa=()=>pl++;function tn(){return"temp://"+pl++}class wa{constructor(e){this._nextHandler=null,this._dispatch=e,this.exec=this.exec.bind(this)}async exec(e,t){return this._dispatch(e,t),this._nextHandler&&await this._nextHandler.exec(e,t),t}setNext(e){return this._nextHandler=e}}function Dn(l,e="data-id"){let t=l;for(!t.tagName&&l.target&&(t=l.target);t;){if(t.getAttribute&&t.getAttribute(e))return t;t=t.parentNode}return null}function ka(l,e="data-id"){const t=Dn(l,e);return t?wl(t.getAttribute(e)):null}function wl(l){if(typeof l=="string"){const e=l*1;if(!isNaN(e))return e}return l}function kl(l,e,t){function n(i){const s=Dn(i);if(!s)return;const r=wl(s.dataset.id);if(typeof e=="function")return e(r,i);let a,o=i.target;for(;o!=s;){if(a=o.dataset?o.dataset.action:null,a&&e[a]){e[a](r,i);return}o=o.parentNode}e[t]&&e[t](r,i)}l.addEventListener(t,n)}function Ot(l,e){kl(l,e,"click"),e.dblclick&&kl(l,e.dblclick,"dblclick")}function va(l,e){for(let t=l.length-1;t>=0;t--)if(l[t]===e){l.splice(t,1);break}}let vl=new Date,nn=!1;const dt=[],yl=l=>{if(nn){nn=!1;return}for(let e=dt.length-1;e>=0;e--){const{node:t,date:n,props:i}=dt[e];if(!(n>vl)&&!t.contains(l.target)&&t!==l.target&&(i.callback&&i.callback(l),i.modal||l.defaultPrevented))break}},Sl=["click","contextmenu"],ya=l=>{vl=new Date,nn=!0;for(let e=dt.length-1;e>=0;e--){const{node:t}=dt[e];if(!t.contains(l.target)&&t!==l.target){nn=!1;break}}};function Sa(l,e){dt.length||(Sl.forEach(n=>document.addEventListener(n,yl)),document.addEventListener("mousedown",ya)),typeof e!="object"&&(e={callback:e});const t={node:l,date:new Date,props:e};return dt.push(t),{destroy(){va(dt,t),dt.length||Sl.forEach(n=>document.removeEventListener(n,yl))}}}let Cl=new Date().valueOf();function xe(){return Cl+=1,Cl}function et(l){return l<10?"0"+l:l.toString()}function Ca(l){const e=et(l);return e.length==2?"0"+e:e}function Ia(l){let e=l.getDay();e===0&&(e=7);const t=new Date(l.valueOf());t.setDate(l.getDate()+(4-e));const n=t.getFullYear(),i=Math.floor((t.getTime()-new Date(n,0,1).getTime())/864e5);return 1+Math.floor(i/7)}const Il=["",""];function Ma(l,e,t){switch(l){case"%d":return et(e.getDate());case"%m":return et(e.getMonth()+1);case"%j":return e.getDate();case"%n":return e.getMonth()+1;case"%y":return et(e.getFullYear()%100);case"%Y":return e.getFullYear();case"%D":return t.dayShort[e.getDay()];case"%l":return t.dayFull[e.getDay()];case"%M":return t.monthShort[e.getMonth()];case"%F":return t.monthFull[e.getMonth()];case"%h":return et((e.getHours()+11)%12+1);case"%g":return(e.getHours()+11)%12+1;case"%G":return e.getHours();case"%H":return et(e.getHours());case"%i":return et(e.getMinutes());case"%a":return((e.getHours()>11?t.pm:t.am)||Il)[0];case"%A":return((e.getHours()>11?t.pm:t.am)||Il)[1];case"%s":return et(e.getSeconds());case"%S":return Ca(e.getMilliseconds());case"%W":return et(Ia(e));case"%c":{let n=e.getFullYear()+"";return n+="-"+et(e.getMonth()+1),n+="-"+et(e.getDate()),n+="T",n+=et(e.getHours()),n+=":"+et(e.getMinutes()),n+=":"+et(e.getSeconds()),n}default:return l}}const Ta=/%[a-zA-Z]/g;function Ft(l,e){return typeof l=="function"?l:function(t){return t?(t.getMonth||(t=new Date(t)),l.replace(Ta,n=>Ma(n,t,e))):""}}function Ml(l){return l&&typeof l=="object"&&!Array.isArray(l)}function En(l,e){for(const t in e){const n=e[t];Ml(l[t])&&Ml(n)?l[t]=En({...l[t]},e[t]):l[t]=e[t]}return l}function Tl(l){return{getGroup(e){const t=l[e];return n=>t&&t[n]||n},getRaw(){return l},extend(e,t){if(!e)return this;let n;return t?n=En({...e},l):n=En({...l},e),Tl(n)}}}function Da(l){let e,t,n;return{c(){e=I("textarea"),g(e,"class","wx-textarea svelte-1eba9c5"),g(e,"id",l[1]),e.disabled=l[4],g(e,"placeholder",l[2]),e.readOnly=l[6],g(e,"title",l[3]),X(e,"wx-error",l[5])},m(i,s){v(i,e,s),je(e,l[0]),t||(n=te(e,"input",l[7]),t=!0)},p(i,[s]){s&2&&g(e,"id",i[1]),s&16&&(e.disabled=i[4]),s&4&&g(e,"placeholder",i[2]),s&64&&(e.readOnly=i[6]),s&8&&g(e,"title",i[3]),s&1&&je(e,i[0]),s&32&&X(e,"wx-error",i[5])},i:K,o:K,d(i){i&&k(e),t=!1,n()}}}function Ea(l,e,t){let{value:n=""}=e,{id:i=xe()}=e,{placeholder:s=""}=e,{title:r=""}=e,{disabled:a=!1}=e,{error:o=!1}=e,{readonly:u=!1}=e;function f(){n=this.value,t(0,n)}return l.$$set=c=>{"value"in c&&t(0,n=c.value),"id"in c&&t(1,i=c.id),"placeholder"in c&&t(2,s=c.placeholder),"title"in c&&t(3,r=c.title),"disabled"in c&&t(4,a=c.disabled),"error"in c&&t(5,o=c.error),"readonly"in c&&t(6,u=c.readonly)},[n,i,s,r,a,o,u,f]}class Ln extends ee{constructor(e){super(),x(this,e,Ea,Da,Z,{value:0,id:1,placeholder:2,title:3,disabled:4,error:5,readonly:6})}}function Dl(l){let e,t;return{c(){e=I("i"),g(e,"class",t=qe(l[0])+" svelte-ap8ojf")},m(n,i){v(n,e,i)},p(n,i){i&1&&t!==(t=qe(n[0])+" svelte-ap8ojf")&&g(e,"class",t)},d(n){n&&k(e)}}}function La(l){let e;return{c(){e=$(l[3])},m(t,n){v(t,e,n)},p(t,n){n&8&&re(e,t[3])},i:K,o:K,d(t){t&&k(e)}}}function Ra(l){let e;const t=l[11].default,n=be(t,l,l[10],null);return{c(){n&&n.c()},m(i,s){n&&n.m(i,s),e=!0},p(i,s){n&&n.p&&(!e||s&1024)&&we(n,t,i,i[10],e?pe(t,i[10],s,null):ke(i[10]),null)},i(i){e||(b(n,i),e=!0)},o(i){p(n,i),e=!1},d(i){n&&n.d(i)}}}function Aa(l){let e,t,n,i,s,r,a,o,u=l[0]&&Dl(l);const f=[Ra,La],c=[];function d(m,_){return m[5]?0:1}return n=d(l),i=c[n]=f[n](l),{c(){e=I("button"),u&&u.c(),t=H(),i.c(),g(e,"title",l[2]),g(e,"class",s=qe(`wx-button ${l[4]}`)+" svelte-ap8ojf"),e.disabled=l[1],X(e,"wx-icon",l[0]&&(!l[5]||!l[5].default))},m(m,_){v(m,e,_),u&&u.m(e,null),M(e,t),c[n].m(e,null),r=!0,a||(o=te(e,"click",l[6]),a=!0)},p(m,[_]){m[0]?u?u.p(m,_):(u=Dl(m),u.c(),u.m(e,t)):u&&(u.d(1),u=null),i.p(m,_),(!r||_&4)&&g(e,"title",m[2]),(!r||_&16&&s!==(s=qe(`wx-button ${m[4]}`)+" svelte-ap8ojf"))&&g(e,"class",s),(!r||_&2)&&(e.disabled=m[1]),(!r||_&49)&&X(e,"wx-icon",m[0]&&(!m[5]||!m[5].default))},i(m){r||(b(i),r=!0)},o(m){p(i),r=!1},d(m){m&&k(e),u&&u.d(),c[n].d(),a=!1,o()}}}function Pa(l,e,t){let{$$slots:n={},$$scope:i}=e,{type:s=""}=e,{css:r=""}=e,{click:a}=e,{icon:o=""}=e,{disabled:u=!1}=e,{title:f=""}=e,{text:c=""}=e;const d=e.$$slots;let m;const _=Ee(),h=w=>{u||(_("click"),a&&a(w))};return l.$$set=w=>{t(13,e=Ce(Ce({},e),We(w))),"type"in w&&t(7,s=w.type),"css"in w&&t(8,r=w.css),"click"in w&&t(9,a=w.click),"icon"in w&&t(0,o=w.icon),"disabled"in w&&t(1,u=w.disabled),"title"in w&&t(2,f=w.title),"text"in w&&t(3,c=w.text),"$$scope"in w&&t(10,i=w.$$scope)},l.$$.update=()=>{if(l.$$.dirty&384){let w=s?s.split(" ").filter(y=>y!=="").map(y=>"wx-"+y).join(" "):"";t(4,m=r+(r?" ":"")+w)}},e=We(e),[o,u,f,c,m,d,h,s,r,a,i,n]}let Ve=class extends ee{constructor(e){super(),x(this,e,Pa,Aa,Z,{type:7,css:8,click:9,icon:0,disabled:1,title:2,text:3})}};function El(l){let e,t;return{c(){e=I("span"),t=$(l[2]),g(e,"class","svelte-1va8f8p")},m(n,i){v(n,e,i),M(e,t)},p(n,i){i&4&&re(t,n[2])},d(n){n&&k(e)}}}function Na(l){let e,t,n,i,s,r,a,o,u=l[2]&&El(l);return{c(){e=I("div"),t=I("input"),n=H(),i=I("label"),s=I("span"),r=H(),u&&u.c(),g(t,"type","checkbox"),g(t,"id",l[1]),t.disabled=l[5],t.checked=l[0],t.value=l[3],g(t,"class","svelte-1va8f8p"),g(s,"class","svelte-1va8f8p"),g(i,"for",l[1]),g(i,"class","svelte-1va8f8p"),g(e,"style",l[4]),g(e,"class","wx-checkbox svelte-1va8f8p")},m(f,c){v(f,e,c),M(e,t),M(e,n),M(e,i),M(i,s),M(i,r),u&&u.m(i,null),a||(o=te(t,"change",l[6]),a=!0)},p(f,[c]){c&2&&g(t,"id",f[1]),c&32&&(t.disabled=f[5]),c&1&&(t.checked=f[0]),c&8&&(t.value=f[3]),f[2]?u?u.p(f,c):(u=El(f),u.c(),u.m(i,null)):u&&(u.d(1),u=null),c&2&&g(i,"for",f[1]),c&16&&g(e,"style",f[4])},i:K,o:K,d(f){f&&k(e),u&&u.d(),a=!1,o()}}}function za(l,e,t){const n=Ee();let{id:i=xe()}=e,{label:s=""}=e,{name:r=""}=e,{value:a=!1}=e,{style:o=""}=e,{disabled:u=!1}=e;function f({target:c}){t(0,a=c.checked),n("change",{value:a,name:r})}return l.$$set=c=>{"id"in c&&t(1,i=c.id),"label"in c&&t(2,s=c.label),"name"in c&&t(3,r=c.name),"value"in c&&t(0,a=c.value),"style"in c&&t(4,o=c.style),"disabled"in c&&t(5,u=c.disabled)},[a,i,s,r,o,u,f]}class Ha extends ee{constructor(e){super(),x(this,e,za,Na,Z,{id:1,label:2,name:3,value:0,style:4,disabled:5})}}function Oa(l){let e,t,n,i,s;const r=l[8].default,a=be(r,l,l[7],null);return{c(){e=I("div"),a&&a.c(),g(e,"class",t=`wx-dropdown wx-${l[0]}-${l[1]} svelte-1jzzq2v`),ce(e,"width",l[2])},m(o,u){v(o,e,u),a&&a.m(e,null),l[9](e),n=!0,i||(s=He(Sa.call(null,e,l[4])),i=!0)},p(o,[u]){a&&a.p&&(!n||u&128)&&we(a,r,o,o[7],n?pe(r,o[7],u,null):ke(o[7]),null),(!n||u&3&&t!==(t=`wx-dropdown wx-${o[0]}-${o[1]} svelte-1jzzq2v`))&&g(e,"class",t),(!n||u&4)&&ce(e,"width",o[2])},i(o){n||(b(a,o),n=!0)},o(o){p(a,o),n=!1},d(o){o&&k(e),a&&a.d(o),l[9](null),i=!1,s()}}}function Fa(l,e,t){let{$$slots:n={},$$scope:i}=e,{position:s="bottom"}=e,{align:r="start"}=e,{autoFit:a=!0}=e,{cancel:o=null}=e,{width:u="100%"}=e,f;vn(()=>{if(a){const m=f.getBoundingClientRect(),_=document.body.getBoundingClientRect();return m.right>=_.right&&t(1,r="end"),m.bottom>=_.bottom&&t(0,s="top"),`${s}-${r}`}});function c(m){o&&o(m)}function d(m){ue[m?"unshift":"push"](()=>{f=m,t(3,f)})}return l.$$set=m=>{"position"in m&&t(0,s=m.position),"align"in m&&t(1,r=m.align),"autoFit"in m&&t(5,a=m.autoFit),"cancel"in m&&t(6,o=m.cancel),"width"in m&&t(2,u=m.width),"$$scope"in m&&t(7,i=m.$$scope)},[s,r,u,f,c,a,o,i,n,d]}let Ct=class extends ee{constructor(e){super(),x(this,e,Fa,Oa,Z,{position:0,align:1,autoFit:5,cancel:6,width:2})}};function Ll(l,e,t){const n=l.slice();return n[15]=e[t],n}function Rl(l){let e,t,n;return{c(){e=I("i"),g(e,"class","wx-clear wxi-close svelte-1zsa67")},m(i,s){v(i,e,s),t||(n=te(e,"click",pt(l[10])),t=!0)},p:K,d(i){i&&k(e),t=!1,n()}}}function qa(l){let e;return{c(){e=I("div"),g(e,"class","wx-empty wx-selected svelte-1zsa67")},m(t,n){v(t,e,n)},p:K,d(t){t&&k(e)}}}function ja(l){let e;return{c(){e=I("div"),g(e,"class","wx-color wx-selected svelte-1zsa67"),ce(e,"background-color",l[0]||"#00a037")},m(t,n){v(t,e,n)},p(t,n){n&1&&ce(e,"background-color",t[0]||"#00a037")},d(t){t&&k(e)}}}function Al(l){let e,t;return e=new Ct({props:{cancel:l[14],$$slots:{default:[Ua]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&256&&(s.cancel=n[14]),i&262146&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Pl(l){let e,t,n;function i(){return l[13](l[15])}return{c(){e=I("div"),g(e,"class","wx-color svelte-1zsa67"),ce(e,"background-color",l[15])},m(s,r){v(s,e,r),t||(n=te(e,"click",pt(i)),t=!0)},p(s,r){l=s,r&2&&ce(e,"background-color",l[15])},d(s){s&&k(e),t=!1,n()}}}function Ua(l){let e,t,n,i,s,r=fe(l[1]),a=[];for(let o=0;o<r.length;o+=1)a[o]=Pl(Ll(l,r,o));return{c(){e=I("div"),t=I("div"),n=H();for(let o=0;o<a.length;o+=1)a[o].c();g(t,"class","wx-empty svelte-1zsa67"),g(e,"class","wx-colors svelte-1zsa67")},m(o,u){v(o,e,u),M(e,t),M(e,n);for(let f=0;f<a.length;f+=1)a[f]&&a[f].m(e,null);i||(s=te(t,"click",pt(l[12])),i=!0)},p(o,u){if(u&514){r=fe(o[1]);let f;for(f=0;f<r.length;f+=1){const c=Ll(o,r,f);a[f]?a[f].p(c,u):(a[f]=Pl(c),a[f].c(),a[f].m(e,null))}for(;f<a.length;f+=1)a[f].d(1);a.length=r.length}},d(o){o&&k(e),at(a,o),i=!1,s()}}}function Va(l){let e,t,n,i,s,r,a,o,u=l[3]&&l[0]&&!l[6]&&Rl(l);function f(_,h){return _[0]?ja:qa}let c=f(l),d=c(l),m=l[8]&&Al(l);return{c(){e=I("div"),t=I("input"),n=H(),u&&u.c(),i=H(),d.c(),s=H(),m&&m.c(),g(t,"title",l[5]),t.value=l[0],t.readOnly=!0,g(t,"id",l[2]),g(t,"placeholder",l[4]),t.disabled=l[6],g(t,"class","svelte-1zsa67"),X(t,"wx-error",l[7]),X(t,"wx-focus",l[8]),g(e,"class","wx-colorselect svelte-1zsa67")},m(_,h){v(_,e,h),M(e,t),M(e,n),u&&u.m(e,null),M(e,i),d.m(e,null),M(e,s),m&&m.m(e,null),r=!0,a||(o=te(e,"click",l[11]),a=!0)},p(_,[h]){(!r||h&32)&&g(t,"title",_[5]),(!r||h&1&&t.value!==_[0])&&(t.value=_[0]),(!r||h&4)&&g(t,"id",_[2]),(!r||h&16)&&g(t,"placeholder",_[4]),(!r||h&64)&&(t.disabled=_[6]),(!r||h&128)&&X(t,"wx-error",_[7]),(!r||h&256)&&X(t,"wx-focus",_[8]),_[3]&&_[0]&&!_[6]?u?u.p(_,h):(u=Rl(_),u.c(),u.m(e,i)):u&&(u.d(1),u=null),c===(c=f(_))&&d?d.p(_,h):(d.d(1),d=c(_),d&&(d.c(),d.m(e,s))),_[8]?m?(m.p(_,h),h&256&&b(m,1)):(m=Al(_),m.c(),b(m,1),m.m(e,null)):m&&(W(),p(m,1,1,()=>{m=null}),Y())},i(_){r||(b(m),r=!0)},o(_){p(m),r=!1},d(_){_&&k(e),u&&u.d(),d.d(),m&&m.d(),a=!1,o()}}}function Ka(l,e,t){let{colors:n=["#00a037","#df282f","#fd772c","#6d4bce","#b26bd3","#c87095","#90564d","#eb2f89","#ea77c0","#777676","#a9a8a8","#9bb402","#e7a90b","#0bbed7","#038cd9"]}=e,{value:i=""}=e,{id:s=xe()}=e,{clear:r=!0}=e,{placeholder:a=""}=e,{title:o}=e,{disabled:u=!1}=e,{error:f=!1}=e,c;function d(S){t(0,i=S),t(8,c=null)}function m(){t(0,i=null)}function _(){if(u)return!1;t(8,c=!0)}const h=()=>d(""),w=S=>d(S),y=()=>t(8,c=null);return l.$$set=S=>{"colors"in S&&t(1,n=S.colors),"value"in S&&t(0,i=S.value),"id"in S&&t(2,s=S.id),"clear"in S&&t(3,r=S.clear),"placeholder"in S&&t(4,a=S.placeholder),"title"in S&&t(5,o=S.title),"disabled"in S&&t(6,u=S.disabled),"error"in S&&t(7,f=S.error)},[i,n,s,r,a,o,u,f,c,d,m,_,h,w,y]}class Ba extends ee{constructor(e){super(),x(this,e,Ka,Va,Z,{colors:1,value:0,id:2,clear:3,placeholder:4,title:5,disabled:6,error:7})}}function Wa(){let l=null,e=!1,t,n,i,s;const r=(d,m,_,h)=>{t=d,n=m,i=_,s=h},a=d=>{l=d,e=l!==null,i(l)},o=(d,m)=>{const _=d===null?null:Math.max(0,Math.min(l+d,n.length-1));_!==l&&(a(_),t?u(_,m):requestAnimationFrame(()=>u(_,m)))},u=(d,m)=>{if(d!==null&&t){const _=t.querySelectorAll(".list > .item")[d];_&&(_.scrollIntoView({block:"nearest"}),m&&m.preventDefault())}};return{move:d=>{const m=ka(d),_=n.findIndex(h=>h.id==m);_!==l&&a(_)},keydown:(d,m)=>{switch(d.code){case"Enter":e?s():a(0);break;case"Space":e||a(0);break;case"Escape":i(l=null);break;case"Tab":i(l=null);break;case"ArrowDown":o(e?1:m||0,d);break;case"ArrowUp":o(e?-1:m||0,d);break}},init:r,navigate:o}}function Nl(l,e,t){const n=l.slice();return n[13]=e[t],n[15]=t,n}const Ya=l=>({option:l&1}),zl=l=>({option:l[13]});function Hl(l){let e,t;return e=new Ct({props:{cancel:l[8],$$slots:{default:[Xa]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&519&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Ga(l){let e;return{c(){e=I("div"),e.textContent="No data",g(e,"class","wx-no-data svelte-fl05h9")},m(t,n){v(t,e,n)},p:K,i:K,o:K,d(t){t&&k(e)}}}function Ja(l){let e=[],t=new Map,n,i,s=fe(l[0]);const r=a=>a[13].id;for(let a=0;a<s.length;a+=1){let o=Nl(l,s,a),u=r(o);t.set(u,e[a]=Ol(u,o))}return{c(){for(let a=0;a<e.length;a+=1)e[a].c();n=se()},m(a,o){for(let u=0;u<e.length;u+=1)e[u]&&e[u].m(a,o);v(a,n,o),i=!0},p(a,o){o&517&&(s=fe(a[0]),W(),e=Oe(e,o,r,1,a,s,t,n.parentNode,Ue,Ol,n,Nl),Y())},i(a){if(!i){for(let o=0;o<s.length;o+=1)b(e[o]);i=!0}},o(a){for(let o=0;o<e.length;o+=1)p(e[o]);i=!1},d(a){a&&k(n);for(let o=0;o<e.length;o+=1)e[o].d(a)}}}function Qa(l){let e=l[13].name+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p(n,i){i&1&&e!==(e=n[13].name+"")&&re(t,e)},d(n){n&&k(t)}}}function Ol(l,e){let t,n,i,s;const r=e[6].default,a=be(r,e,e[9],zl),o=a||Qa(e);return{key:l,first:null,c(){t=I("div"),o&&o.c(),n=H(),g(t,"class","wx-item svelte-fl05h9"),g(t,"data-id",i=e[13].id),X(t,"wx-focus",e[15]===e[2]),this.first=t},m(u,f){v(u,t,f),o&&o.m(t,null),M(t,n),s=!0},p(u,f){e=u,a?a.p&&(!s||f&513)&&we(a,r,e,e[9],s?pe(r,e[9],f,Ya):ke(e[9]),zl):o&&o.p&&(!s||f&1)&&o.p(e,s?f:-1),(!s||f&1&&i!==(i=e[13].id))&&g(t,"data-id",i),(!s||f&5)&&X(t,"wx-focus",e[15]===e[2])},i(u){s||(b(o,u),s=!0)},o(u){p(o,u),s=!1},d(u){u&&k(t),o&&o.d(u)}}}function Xa(l){let e,t,n,i,s,r;const a=[Ja,Ga],o=[];function u(f,c){return f[0].length?0:1}return t=u(l),n=o[t]=a[t](l),{c(){e=I("div"),n.c(),g(e,"class","wx-list svelte-fl05h9")},m(f,c){v(f,e,c),o[t].m(e,null),l[7](e),i=!0,s||(r=[te(e,"click",pt(l[5])),te(e,"mousemove",l[3])],s=!0)},p(f,c){let d=t;t=u(f),t===d?o[t].p(f,c):(W(),p(o[d],1,1,()=>{o[d]=null}),Y(),n=o[t],n?n.p(f,c):(n=o[t]=a[t](f),n.c()),b(n,1),n.m(e,null))},i(f){i||(b(n),i=!0)},o(f){p(n),i=!1},d(f){f&&k(e),o[t].d(),l[7](null),s=!1,De(r)}}}function Za(l){let e,t,n=l[2]!==null&&Hl(l);return{c(){n&&n.c(),e=se()},m(i,s){n&&n.m(i,s),v(i,e,s),t=!0},p(i,[s]){i[2]!==null?n?(n.p(i,s),s&4&&b(n,1)):(n=Hl(i),n.c(),b(n,1),n.m(e.parentNode,e)):n&&(W(),p(n,1,1,()=>{n=null}),Y())},i(i){t||(b(n),t=!0)},o(i){p(n),t=!1},d(i){i&&k(e),n&&n.d(i)}}}function $a(l,e,t){let{$$slots:n={},$$scope:i}=e,{items:s=[]}=e,r,a=null;const o=Ee(),{move:u,keydown:f,init:c,navigate:d}=Wa(),m=()=>o("select",{id:s[a]?.id});ot(()=>{o("ready",{navigate:d,keydown:f,move:u})});function _(w){ue[w?"unshift":"push"](()=>{r=w,t(1,r)})}const h=()=>d(null);return l.$$set=w=>{"items"in w&&t(0,s=w.items),"$$scope"in w&&t(9,i=w.$$scope)},l.$$.update=()=>{l.$$.dirty&3&&c(r,s,w=>t(2,a=w),m)},[s,r,a,u,d,m,n,_,h,i]}class Fl extends ee{constructor(e){super(),x(this,e,$a,Za,Z,{items:0})}}const xa=l=>({option:l[1]&4}),ql=l=>({option:l[33]});function eu(l){let e;return{c(){e=I("i"),g(e,"class","wx-icon wxi-angle-down svelte-1oh2bu")},m(t,n){v(t,e,n)},p:K,d(t){t&&k(e)}}}function tu(l){let e,t,n;return{c(){e=I("i"),g(e,"class","wx-icon wxi-close svelte-1oh2bu")},m(i,s){v(i,e,s),t||(n=te(e,"click",pt(l[14])),t=!0)},p:K,d(i){i&&k(e),t=!1,n()}}}function jl(l){let e,t;return e=new Fl({props:{items:l[8],$$slots:{default:[lu,({option:n})=>({33:n}),({option:n})=>[0,n?4:0]]},$$scope:{ctx:l}}}),e.$on("ready",l[12]),e.$on("select",l[13]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i[0]&256&&(s.items=n[8]),i[0]&134217728|i[1]&4&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function nu(l){let e=l[33].name+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p(n,i){i[1]&4&&e!==(e=n[33].name+"")&&re(t,e)},d(n){n&&k(t)}}}function lu(l){let e;const t=l[22].default,n=be(t,l,l[27],ql),i=n||nu(l);return{c(){i&&i.c()},m(s,r){i&&i.m(s,r),e=!0},p(s,r){n?n.p&&(!e||r[0]&134217728|r[1]&4)&&we(n,t,s,s[27],e?pe(t,s[27],r,xa):ke(s[27]),ql):i&&i.p&&(!e||r[1]&4)&&i.p(s,e?r:[-1,-1])},i(s){e||(b(i,s),e=!0)},o(s){p(i,s),e=!1},d(s){i&&i.d(s)}}}function iu(l){let e,t,n,i,s,r,a;function o(d,m){return d[6]&&!d[4]&&d[0]?tu:eu}let u=o(l),f=u(l),c=!l[4]&&jl(l);return{c(){e=I("div"),t=I("input"),n=H(),f.c(),i=H(),c&&c.c(),g(t,"id",l[1]),t.disabled=l[4],g(t,"placeholder",l[2]),g(t,"class","svelte-1oh2bu"),X(t,"wx-error",l[5]),g(e,"class","wx-combo svelte-1oh2bu"),g(e,"title",l[3])},m(d,m){v(d,e,m),M(e,t),l[23](t),je(t,l[7]),M(e,n),f.m(e,null),M(e,i),c&&c.m(e,null),s=!0,r||(a=[te(t,"input",l[24]),te(t,"focus",l[16]),te(t,"blur",l[17]),te(t,"input",l[15]),te(e,"click",l[25]),te(e,"keydown",l[26])],r=!0)},p(d,m){(!s||m[0]&2)&&g(t,"id",d[1]),(!s||m[0]&16)&&(t.disabled=d[4]),(!s||m[0]&4)&&g(t,"placeholder",d[2]),m[0]&128&&t.value!==d[7]&&je(t,d[7]),(!s||m[0]&32)&&X(t,"wx-error",d[5]),u===(u=o(d))&&f?f.p(d,m):(f.d(1),f=u(d),f&&(f.c(),f.m(e,i))),d[4]?c&&(W(),p(c,1,1,()=>{c=null}),Y()):c?(c.p(d,m),m[0]&16&&b(c,1)):(c=jl(d),c.c(),b(c,1),c.m(e,null)),(!s||m[0]&8)&&g(e,"title",d[3])},i(d){s||(b(c),s=!0)},o(d){p(c),s=!1},d(d){d&&k(e),l[23](null),f.d(),c&&c.d(),r=!1,De(a)}}}function su(l,e,t){let{$$slots:n={},$$scope:i}=e,{value:s=""}=e,{id:r=xe()}=e,{options:a=[]}=e,{textField:o="label"}=e,{placeholder:u=""}=e,{title:f=""}=e,{disabled:c=!1}=e,{error:d=!1}=e,{clearButton:m=!1}=e;const _=Ee();let h="",w=[],y,S;function C(Q){t(9,y=Q.detail.navigate),t(10,S=Q.detail.keydown)}let T;function L(Q){const B=Q.detail.id;E(B,!0)}function N(Q){if(!a.length)return;if(Q===""&&m){F();return}let B=a.find(ge=>ge[o]===Q);B||(B=a.find(ge=>ge[o].toLowerCase().includes(Q.toLowerCase())));const ie=B?B.id:T||a[0].id;E(ie,!1)}function E(Q,B){if(Q||Q===0){let ie=a.find(ge=>ge.id===Q);t(7,h=ie[o]),t(8,w=a),B&&y(null),s!==ie.id&&(t(0,s=ie.id),_("select",{selected:ie}))}!O&&B&&U.focus()}function F(){t(7,h=t(0,s="")),t(8,w=a),_("select",{selected:null})}function G(){t(8,w=h?a.filter(Q=>Q[o].toLowerCase().includes(h.toLowerCase())):a),w.length?y(0):y(null)}let U,O;function D(){O=!0}function j(){O=!1,setTimeout(()=>{O||N(h)},200)}const z=()=>w.findIndex(Q=>Q.id===s);function q(Q){ue[Q?"unshift":"push"](()=>{U=Q,t(11,U)})}function J(){h=this.value,t(7,h),t(21,T),t(0,s),t(19,a),t(20,o)}const V=()=>y(z()),ne=Q=>S(Q,z());return l.$$set=Q=>{"value"in Q&&t(0,s=Q.value),"id"in Q&&t(1,r=Q.id),"options"in Q&&t(19,a=Q.options),"textField"in Q&&t(20,o=Q.textField),"placeholder"in Q&&t(2,u=Q.placeholder),"title"in Q&&t(3,f=Q.title),"disabled"in Q&&t(4,c=Q.disabled),"error"in Q&&t(5,d=Q.error),"clearButton"in Q&&t(6,m=Q.clearButton),"$$scope"in Q&&t(27,i=Q.$$scope)},l.$$.update=()=>{l.$$.dirty[0]&3670017&&T!=s&&(t(7,h=s||s===0?a.find(Q=>Q.id===s)[o]:""),t(21,T=s)),l.$$.dirty[0]&524288&&t(8,w=a)},[s,r,u,f,c,d,m,h,w,y,S,U,C,L,F,G,D,j,z,a,o,T,n,q,J,V,ne,i]}class Rn extends ee{constructor(e){super(),x(this,e,su,iu,Z,{value:0,id:1,options:19,textField:20,placeholder:2,title:3,disabled:4,error:5,clearButton:6},null,[-1,-1])}}function ru(l){let e,t,n;return{c(){e=I("input"),g(e,"id",l[2]),e.readOnly=l[3],e.disabled=l[6],g(e,"placeholder",l[5]),g(e,"title",l[9]),g(e,"style",l[8]),g(e,"class","svelte-1axmg32")},m(i,s){v(i,e,s),je(e,l[0]),l[24](e),t||(n=[te(e,"input",l[23]),te(e,"input",l[25]),te(e,"change",l[26])],t=!0)},p(i,s){s&4&&g(e,"id",i[2]),s&8&&(e.readOnly=i[3]),s&64&&(e.disabled=i[6]),s&32&&g(e,"placeholder",i[5]),s&512&&g(e,"title",i[9]),s&256&&g(e,"style",i[8]),s&1&&e.value!==i[0]&&je(e,i[0])},d(i){i&&k(e),l[24](null),t=!1,De(n)}}}function ou(l){let e,t,n;return{c(){e=I("input"),g(e,"id",l[2]),e.readOnly=l[3],e.disabled=l[6],g(e,"placeholder",l[5]),g(e,"type","number"),g(e,"style",l[8]),g(e,"title",l[9]),g(e,"class","svelte-1axmg32")},m(i,s){v(i,e,s),je(e,l[0]),l[20](e),t||(n=[te(e,"input",l[19]),te(e,"input",l[21]),te(e,"change",l[22])],t=!0)},p(i,s){s&4&&g(e,"id",i[2]),s&8&&(e.readOnly=i[3]),s&64&&(e.disabled=i[6]),s&32&&g(e,"placeholder",i[5]),s&256&&g(e,"style",i[8]),s&512&&g(e,"title",i[9]),s&1&&kn(e.value)!==i[0]&&je(e,i[0])},d(i){i&&k(e),l[20](null),t=!1,De(n)}}}function au(l){let e,t,n;return{c(){e=I("input"),g(e,"id",l[2]),e.readOnly=l[3],e.disabled=l[6],g(e,"placeholder",l[5]),g(e,"type","password"),g(e,"style",l[8]),g(e,"title",l[9]),g(e,"class","svelte-1axmg32")},m(i,s){v(i,e,s),je(e,l[0]),l[16](e),t||(n=[te(e,"input",l[15]),te(e,"input",l[17]),te(e,"change",l[18])],t=!0)},p(i,s){s&4&&g(e,"id",i[2]),s&8&&(e.readOnly=i[3]),s&64&&(e.disabled=i[6]),s&32&&g(e,"placeholder",i[5]),s&256&&g(e,"style",i[8]),s&512&&g(e,"title",i[9]),s&1&&e.value!==i[0]&&je(e,i[0])},d(i){i&&k(e),l[16](null),t=!1,De(n)}}}function Ul(l){let e,t;return{c(){e=I("i"),g(e,"class",t="wx-icon "+l[10]+" svelte-1axmg32")},m(n,i){v(n,e,i)},p(n,i){i&1024&&t!==(t="wx-icon "+n[10]+" svelte-1axmg32")&&g(e,"class",t)},d(n){n&&k(e)}}}function uu(l){let e,t,n;function i(o,u){return o[4]=="password"?au:o[4]=="number"?ou:ru}let s=i(l),r=s(l),a=l[10]&&Ul(l);return{c(){e=I("div"),r.c(),t=H(),a&&a.c(),g(e,"class",n="wx-text "+l[1]+" svelte-1axmg32"),X(e,"wx-error",l[7]),X(e,"wx-disabled",l[6])},m(o,u){v(o,e,u),r.m(e,null),M(e,t),a&&a.m(e,null)},p(o,[u]){s===(s=i(o))&&r?r.p(o,u):(r.d(1),r=s(o),r&&(r.c(),r.m(e,t))),o[10]?a?a.p(o,u):(a=Ul(o),a.c(),a.m(e,null)):a&&(a.d(1),a=null),u&2&&n!==(n="wx-text "+o[1]+" svelte-1axmg32")&&g(e,"class",n),u&130&&X(e,"wx-error",o[7]),u&66&&X(e,"wx-disabled",o[6])},i:K,o:K,d(o){o&&k(e),r.d(),a&&a.d()}}}function fu(l,e,t){let{value:n=""}=e,{id:i=xe()}=e,{readonly:s=!1}=e,{focus:r=!1}=e,{select:a=!1}=e,{type:o="text"}=e,{placeholder:u=""}=e,{disabled:f=!1}=e,{error:c=!1}=e,{inputStyle:d=""}=e,{title:m=""}=e,{css:_=""}=e,{icon:h=""}=e;const w=Ee();h&&_.indexOf("wx-icon-left")===-1&&(_="wx-icon-right "+_);let y;ot(()=>{setTimeout(()=>{r&&y&&y.focus(),a&&y&&y.select()},1)});function S(){n=this.value,t(0,n)}function C(z){ue[z?"unshift":"push"](()=>{y=z,t(11,y)})}const T=()=>w("change",{value:n,input:!0}),L=()=>w("change",{value:n});function N(){n=kn(this.value),t(0,n)}function E(z){ue[z?"unshift":"push"](()=>{y=z,t(11,y)})}const F=()=>w("change",{value:n,input:!0}),G=()=>w("change",{value:n});function U(){n=this.value,t(0,n)}function O(z){ue[z?"unshift":"push"](()=>{y=z,t(11,y)})}const D=()=>w("change",{value:n,input:!0}),j=()=>w("change",{value:n});return l.$$set=z=>{"value"in z&&t(0,n=z.value),"id"in z&&t(2,i=z.id),"readonly"in z&&t(3,s=z.readonly),"focus"in z&&t(13,r=z.focus),"select"in z&&t(14,a=z.select),"type"in z&&t(4,o=z.type),"placeholder"in z&&t(5,u=z.placeholder),"disabled"in z&&t(6,f=z.disabled),"error"in z&&t(7,c=z.error),"inputStyle"in z&&t(8,d=z.inputStyle),"title"in z&&t(9,m=z.title),"css"in z&&t(1,_=z.css),"icon"in z&&t(10,h=z.icon)},[n,_,i,s,o,u,f,c,d,m,h,y,w,r,a,S,C,T,L,N,E,F,G,U,O,D,j]}class An extends ee{constructor(e){super(),x(this,e,fu,uu,Z,{value:0,id:2,readonly:3,focus:13,select:14,type:4,placeholder:5,disabled:6,error:7,inputStyle:8,title:9,css:1,icon:10})}}function cu(l){let e;return{c(){e=I("span"),g(e,"class","wx-spacer svelte-wurt7c")},m(t,n){v(t,e,n)},p:K,d(t){t&&k(e)}}}function du(l){let e,t,n;return{c(){e=I("i"),g(e,"class","wx-pager wxi-angle-left svelte-wurt7c")},m(i,s){v(i,e,s),t||(n=te(e,"click",l[8]),t=!0)},p:K,d(i){i&&k(e),t=!1,n()}}}function mu(l){let e;return{c(){e=I("span"),g(e,"class","wx-spacer svelte-wurt7c")},m(t,n){v(t,e,n)},p:K,d(t){t&&k(e)}}}function _u(l){let e,t,n;return{c(){e=I("i"),g(e,"class","wx-pager wxi-angle-right svelte-wurt7c")},m(i,s){v(i,e,s),t||(n=te(e,"click",l[9]),t=!0)},p:K,d(i){i&&k(e),t=!1,n()}}}function hu(l){let e,t,n,i,s,r,a;function o(_,h){return _[1]!="right"?du:cu}let u=o(l),f=u(l);function c(_,h){return _[1]!="left"?_u:mu}let d=c(l),m=d(l);return{c(){e=I("div"),f.c(),t=H(),n=I("span"),i=$(l[2]),s=H(),m.c(),g(n,"class","wx-label svelte-wurt7c"),g(e,"class","wx-header svelte-wurt7c")},m(_,h){v(_,e,h),f.m(e,null),M(e,t),M(e,n),M(n,i),M(e,s),m.m(e,null),r||(a=te(n,"click",l[4]),r=!0)},p(_,[h]){u===(u=o(_))&&f?f.p(_,h):(f.d(1),f=u(_),f&&(f.c(),f.m(e,t))),h&4&&re(i,_[2]),d===(d=c(_))&&m?m.p(_,h):(m.d(1),m=d(_),m&&(m.c(),m.m(e,null)))},i:K,o:K,d(_){_&&k(e),f.d(),m.d(),r=!1,a()}}}function gu(l,e,t){const n=Ee(),s=ve("wx-i18n").getRaw().calendar.monthFull;let{date:r}=e,{type:a}=e,{part:o}=e,u,f,c;function d(){n("shift",{diff:0,type:a})}const m=()=>n("shift",{diff:-1,type:a}),_=()=>n("shift",{diff:1,type:a});return l.$$set=h=>{"date"in h&&t(5,r=h.date),"type"in h&&t(0,a=h.type),"part"in h&&t(1,o=h.part)},l.$$.update=()=>{if(l.$$.dirty&225)switch(t(6,u=r.getMonth()),t(7,f=r.getFullYear()),a){case"month":t(2,c=`${s[u]} ${f}`);break;case"year":t(2,c=f);break;case"duodecade":{const h=f-f%10,w=h+9;t(2,c=`${h} - ${w}`);break}}},[a,o,c,n,d,r,u,f,m,_]}let bu=class extends ee{constructor(e){super(),x(this,e,gu,hu,Z,{date:5,type:0,part:1})}};function pu(l){let e,t,n,i;const s=l[2].default,r=be(s,l,l[1],null);return{c(){e=I("button"),r&&r.c(),g(e,"class","svelte-1f88uh6")},m(a,o){v(a,e,o),r&&r.m(e,null),t=!0,n||(i=te(e,"click",function(){nt(l[0])&&l[0].apply(this,arguments)}),n=!0)},p(a,[o]){l=a,r&&r.p&&(!t||o&2)&&we(r,s,l,l[1],t?pe(s,l[1],o,null):ke(l[1]),null)},i(a){t||(b(r,a),t=!0)},o(a){p(r,a),t=!1},d(a){a&&k(e),r&&r.d(a),n=!1,i()}}}function wu(l,e,t){let{$$slots:n={},$$scope:i}=e,{click:s}=e;return l.$$set=r=>{"click"in r&&t(0,s=r.click),"$$scope"in r&&t(1,i=r.$$scope)},[s,i,n]}let qt=class extends ee{constructor(e){super(),x(this,e,wu,pu,Z,{click:0})}};function Vl(l,e,t){const n=l.slice();return n[17]=e[t],n}function Kl(l,e,t){const n=l.slice();return n[17]=e[t],n}function Bl(l){let e;return{c(){e=I("div"),e.textContent=`${l[17]}`,g(e,"class","wx-weekday svelte-1bsdg9l")},m(t,n){v(t,e,n)},p:K,d(t){t&&k(e)}}}function Wl(l,e){let t,n=e[17].day+"",i,s,r,a;return{key:l,first:null,c(){t=I("div"),i=$(n),s=H(),g(t,"class",r="wx-day "+e[17].css+" svelte-1bsdg9l"),g(t,"data-id",a=e[17].date),X(t,"wx-out",!e[17].in),this.first=t},m(o,u){v(o,t,u),M(t,i),M(t,s)},p(o,u){e=o,u&1&&n!==(n=e[17].day+"")&&re(i,n),u&1&&r!==(r="wx-day "+e[17].css+" svelte-1bsdg9l")&&g(t,"class",r),u&1&&a!==(a=e[17].date)&&g(t,"data-id",a),u&1&&X(t,"wx-out",!e[17].in)},d(o){o&&k(t)}}}function ku(l){let e,t,n,i,s=[],r=new Map,a,o,u=fe(l[1]),f=[];for(let m=0;m<u.length;m+=1)f[m]=Bl(Kl(l,u,m));let c=fe(l[0]);const d=m=>m[17].date;for(let m=0;m<c.length;m+=1){let _=Vl(l,c,m),h=d(_);r.set(h,s[m]=Wl(h,_))}return{c(){e=I("div"),t=I("div");for(let m=0;m<f.length;m+=1)f[m].c();n=H(),i=I("div");for(let m=0;m<s.length;m+=1)s[m].c();g(t,"class","wx-weekdays svelte-1bsdg9l"),g(i,"class","wx-days svelte-1bsdg9l")},m(m,_){v(m,e,_),M(e,t);for(let h=0;h<f.length;h+=1)f[h]&&f[h].m(t,null);M(e,n),M(e,i);for(let h=0;h<s.length;h+=1)s[h]&&s[h].m(i,null);a||(o=He(Ot.call(null,i,l[2])),a=!0)},p(m,[_]){if(_&2){u=fe(m[1]);let h;for(h=0;h<u.length;h+=1){const w=Kl(m,u,h);f[h]?f[h].p(w,_):(f[h]=Bl(w),f[h].c(),f[h].m(t,null))}for(;h<f.length;h+=1)f[h].d(1);f.length=u.length}_&1&&(c=fe(m[0]),s=Oe(s,_,d,1,m,c,r,i,gl,Wl,null,Vl))},i:K,o:K,d(m){m&&k(e),at(f,m);for(let _=0;_<s.length;_+=1)s[_].d();a=!1,o()}}}function vu(l){const e=l.getDay();return e===0||e===6}function yu(l,e,t){let{value:n}=e,{current:i}=e,{cancel:s}=e,{select:r}=e,{part:a}=e,{markers:o=null}=e;const u=ve("wx-i18n").getRaw().calendar,f=(u.weekStart||7)%7,c=u.dayShort.slice(f).concat(u.dayShort.slice(0,f));let d,m;const _=(T,L,N)=>new Date(T.getFullYear(),T.getMonth()+(L||0),T.getDate()+(N||0));let h=a!=="normal";function w(){const T=_(i,0,1-i.getDate());return T.setDate(T.getDate()-(T.getDay()-(f-7))%7),T}function y(){const T=_(i,1,-i.getDate());return T.setDate(T.getDate()+(6-T.getDay()+f)%7),T}const S={click:C};function C(T,L){r&&(L.stopPropagation(),r(new Date(new Date(T)))),s&&s()}return l.$$set=T=>{"value"in T&&t(3,n=T.value),"current"in T&&t(4,i=T.current),"cancel"in T&&t(5,s=T.cancel),"select"in T&&t(6,r=T.select),"part"in T&&t(7,a=T.part),"markers"in T&&t(8,o=T.markers)},l.$$.update=()=>{if(l.$$.dirty&921){a=="normal"?t(9,m=[n?_(n).valueOf():0]):t(9,m=n?[n.start?_(n.start).valueOf():0,n.end?_(n.end).valueOf():0]:[0,0]);const T=w(),L=y(),N=i.getMonth();t(0,d=[]);for(let E=T;E<=L;E.setDate(E.getDate()+1)){const F={day:E.getDate(),in:E.getMonth()===N,date:E.valueOf()};let G="";if(G+=F.in?"":" wx-inactive",G+=m.indexOf(F.date)>-1?" wx-selected":"",h){const U=F.date==m[0],O=F.date==m[1];U&&!O?G+=" wx-left":O&&!U&&(G+=" wx-right"),F.date>m[0]&&F.date<m[1]&&(G+=" wx-inrange")}if(G+=vu(E)?" wx-weekend":"",o){const U=o(E);U&&(G+=" "+U)}d.push({...F,css:G})}}},[d,c,S,n,i,s,r,a,o,m]}class Su extends ee{constructor(e){super(),x(this,e,yu,ku,Z,{value:3,current:4,cancel:5,select:6,part:7,markers:8})}}function Yl(l,e,t){const n=l.slice();return n[9]=e[t],n[11]=t,n}function Gl(l){let e;return{c(){e=I("div"),e.textContent=`${l[9]} `,g(e,"class","wx-month svelte-pmn9ti"),g(e,"data-id",l[11]),X(e,"wx-current",l[1]===l[11])},m(t,n){v(t,e,n)},p(t,n){n&2&&X(e,"wx-current",t[1]===t[11])},d(t){t&&k(e)}}}function Cu(l){let e=l[2].done+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p:K,d(n){n&&k(t)}}}function Iu(l){let e,t,n,i,s,r,a,o=fe(l[3]),u=[];for(let f=0;f<o.length;f+=1)u[f]=Gl(Yl(l,o,f));return i=new qt({props:{click:l[0],$$slots:{default:[Cu]},$$scope:{ctx:l}}}),{c(){e=I("div");for(let f=0;f<u.length;f+=1)u[f].c();t=H(),n=I("div"),P(i.$$.fragment),g(e,"class","wx-months svelte-pmn9ti"),g(n,"class","wx-buttons svelte-pmn9ti")},m(f,c){v(f,e,c);for(let d=0;d<u.length;d+=1)u[d]&&u[d].m(e,null);v(f,t,c),v(f,n,c),R(i,n,null),s=!0,r||(a=He(Ot.call(null,e,l[4])),r=!0)},p(f,[c]){if(c&10){o=fe(f[3]);let m;for(m=0;m<o.length;m+=1){const _=Yl(f,o,m);u[m]?u[m].p(_,c):(u[m]=Gl(_),u[m].c(),u[m].m(e,null))}for(;m<u.length;m+=1)u[m].d(1);u.length=o.length}const d={};c&1&&(d.click=f[0]),c&4096&&(d.$$scope={dirty:c,ctx:f}),i.$set(d)},i(f){s||(b(i.$$.fragment,f),s=!0)},o(f){p(i.$$.fragment,f),s=!1},d(f){f&&(k(e),k(t),k(n)),at(u,f),A(i),r=!1,a()}}}function Mu(l,e,t){let{value:n}=e,{current:i}=e,{cancel:s}=e,{part:r}=e;const a=ve("wx-i18n").getRaw().calendar,o=a.monthShort;let u;const f={click:c};function c(d,m){(d||d===0)&&(m.stopPropagation(),i.setMonth(d),t(6,i)),r==="normal"&&t(5,n=new Date(i)),s()}return l.$$set=d=>{"value"in d&&t(5,n=d.value),"current"in d&&t(6,i=d.current),"cancel"in d&&t(0,s=d.cancel),"part"in d&&t(7,r=d.part)},l.$$.update=()=>{l.$$.dirty&224&&(r!=="normal"&&n?r==="left"&&n.start?t(1,u=n.start.getMonth()):r==="right"&&n.end?t(1,u=n.end.getMonth()):t(1,u=i.getMonth()):t(1,u=i.getMonth()))},[s,u,a,o,f,n,i,r]}class Tu extends ee{constructor(e){super(),x(this,e,Mu,Iu,Z,{value:5,current:6,cancel:0,part:7})}}function Jl(l,e,t){const n=l.slice();return n[9]=e[t],n[11]=t,n}function Ql(l){let e,t=l[9]+"",n,i,s;return{c(){e=I("div"),n=$(t),i=H(),g(e,"class","wx-year svelte-is1ghx"),g(e,"data-id",s=l[9]),X(e,"wx-current",l[2]==l[9]),X(e,"wx-prev-decade",l[11]===0),X(e,"wx-next-decade",l[11]===11)},m(r,a){v(r,e,a),M(e,n),M(e,i)},p(r,a){a&2&&t!==(t=r[9]+"")&&re(n,t),a&2&&s!==(s=r[9])&&g(e,"data-id",s),a&6&&X(e,"wx-current",r[2]==r[9])},d(r){r&&k(e)}}}function Du(l){let e=l[3].done+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p:K,d(n){n&&k(t)}}}function Eu(l){let e,t,n,i,s,r,a,o=fe(l[1]),u=[];for(let f=0;f<o.length;f+=1)u[f]=Ql(Jl(l,o,f));return i=new qt({props:{click:l[0],$$slots:{default:[Du]},$$scope:{ctx:l}}}),{c(){e=I("div");for(let f=0;f<u.length;f+=1)u[f].c();t=H(),n=I("div"),P(i.$$.fragment),g(e,"class","wx-years svelte-is1ghx"),g(n,"class","wx-buttons svelte-is1ghx")},m(f,c){v(f,e,c);for(let d=0;d<u.length;d+=1)u[d]&&u[d].m(e,null);v(f,t,c),v(f,n,c),R(i,n,null),s=!0,r||(a=He(Ot.call(null,e,l[4])),r=!0)},p(f,[c]){if(c&6){o=fe(f[1]);let m;for(m=0;m<o.length;m+=1){const _=Jl(f,o,m);u[m]?u[m].p(_,c):(u[m]=Ql(_),u[m].c(),u[m].m(e,null))}for(;m<u.length;m+=1)u[m].d(1);u.length=o.length}const d={};c&1&&(d.click=f[0]),c&4096&&(d.$$scope={dirty:c,ctx:f}),i.$set(d)},i(f){s||(b(i.$$.fragment,f),s=!0)},o(f){p(i.$$.fragment,f),s=!1},d(f){f&&(k(e),k(t),k(n)),at(u,f),A(i),r=!1,a()}}}function Lu(l,e,t){const n=ve("wx-i18n").getRaw().calendar;let{value:i}=e,{current:s}=e,{cancel:r}=e,{part:a}=e,o,u;const f={click:c};function c(d,m){d&&(m.stopPropagation(),s.setFullYear(d),t(5,s)),a==="normal"&&t(6,i=new Date(s)),r()}return l.$$set=d=>{"value"in d&&t(6,i=d.value),"current"in d&&t(5,s=d.current),"cancel"in d&&t(0,r=d.cancel),"part"in d&&t(7,a=d.part)},l.$$.update=()=>{if(l.$$.dirty&38){t(2,u=s.getFullYear());const d=u-u%10-1,m=d+12;t(1,o=[]);for(let _=d;_<m;++_)o.push(_)}},[r,o,u,n,f,s,i,a]}class Ru extends ee{constructor(e){super(),x(this,e,Lu,Eu,Z,{value:6,current:5,cancel:0,part:7})}}const ln={month:{component:Su,next:Pu,prev:Au},year:{component:Tu,next:zu,prev:Nu},duodecade:{component:Ru,next:Ou,prev:Hu}};function Au(l){let e=new Date(l);for(e.setMonth(l.getMonth()-1);l.getMonth()===e.getMonth();)e.setDate(e.getDate()-1);return e}function Pu(l){return l=new Date(l),l.setDate(1),l.setMonth(l.getMonth()+1),l}function Nu(l){return l=new Date(l),l.setFullYear(l.getFullYear()-1),l}function zu(l){return l=new Date(l),l.setFullYear(l.getFullYear()+1),l}function Hu(l){return l=new Date(l),l.setFullYear(l.getFullYear()-10),l}function Ou(l){return l=new Date(l),l.setFullYear(l.getFullYear()+10),l}function Xl(l){let e,t,n,i,s,r,a,o,u=l[2]&&Zl(l);return i=new qt({props:{click:l[14],$$slots:{default:[qu]},$$scope:{ctx:l}}}),a=new qt({props:{click:l[15],$$slots:{default:[ju]},$$scope:{ctx:l}}}),{c(){e=I("div"),u&&u.c(),t=H(),n=I("div"),P(i.$$.fragment),s=H(),r=I("div"),P(a.$$.fragment),g(n,"class","wx-button-item svelte-9ihaic"),g(r,"class","wx-button-item svelte-9ihaic"),g(e,"class","wx-buttons svelte-9ihaic")},m(f,c){v(f,e,c),u&&u.m(e,null),M(e,t),M(e,n),R(i,n,null),M(e,s),M(e,r),R(a,r,null),o=!0},p(f,c){f[2]?u?(u.p(f,c),c&4&&b(u,1)):(u=Zl(f),u.c(),b(u,1),u.m(e,t)):u&&(W(),p(u,1,1,()=>{u=null}),Y());const d={};c&131072&&(d.$$scope={dirty:c,ctx:f}),i.$set(d);const m={};c&131072&&(m.$$scope={dirty:c,ctx:f}),a.$set(m)},i(f){o||(b(u),b(i.$$.fragment,f),b(a.$$.fragment,f),o=!0)},o(f){p(u),p(i.$$.fragment,f),p(a.$$.fragment,f),o=!1},d(f){f&&k(e),u&&u.d(),A(i),A(a)}}}function Zl(l){let e,t,n;return t=new qt({props:{click:l[13],$$slots:{default:[Fu]},$$scope:{ctx:l}}}),{c(){e=I("div"),P(t.$$.fragment),g(e,"class","wx-button-item svelte-9ihaic")},m(i,s){v(i,e,s),R(t,e,null),n=!0},p(i,s){const r={};s&131072&&(r.$$scope={dirty:s,ctx:i}),t.$set(r)},i(i){n||(b(t.$$.fragment,i),n=!0)},o(i){p(t.$$.fragment,i),n=!1},d(i){i&&k(e),A(t)}}}function Fu(l){let e=l[7]("done")+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p:K,d(n){n&&k(t)}}}function qu(l){let e=l[7]("clear")+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p:K,d(n){n&&k(t)}}}function ju(l){let e=l[7]("today")+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p:K,d(n){n&&k(t)}}}function Uu(l){let e,t,n,i,s,r,a,o,u;n=new bu({props:{date:l[1],part:l[3],type:l[6]}}),n.$on("shift",l[12]);var f=ln[l[6]].component;function c(m,_){return{props:{value:m[0],current:m[1],part:m[3],markers:m[4],select:m[11],cancel:m[9]}}}f&&(r=Je(f,c(l)));let d=l[6]==="month"&&l[5]&&Xl(l);return{c(){e=I("div"),t=I("div"),P(n.$$.fragment),i=H(),s=I("div"),r&&P(r.$$.fragment),a=H(),d&&d.c(),g(t,"class","wx-wrap svelte-9ihaic"),g(e,"class",o="wx-calendar "+(l[3]!=="normal"&&l[3]!=="both"?"wx-part":"")+" svelte-9ihaic")},m(m,_){v(m,e,_),M(e,t),R(n,t,null),M(t,i),M(t,s),r&&R(r,s,null),M(s,a),d&&d.m(s,null),u=!0},p(m,[_]){const h={};if(_&2&&(h.date=m[1]),_&8&&(h.part=m[3]),_&64&&(h.type=m[6]),n.$set(h),_&64&&f!==(f=ln[m[6]].component)){if(r){W();const w=r;p(w.$$.fragment,1,0,()=>{A(w,1)}),Y()}f?(r=Je(f,c(m)),P(r.$$.fragment),b(r.$$.fragment,1),R(r,s,a)):r=null}else if(f){const w={};_&1&&(w.value=m[0]),_&2&&(w.current=m[1]),_&8&&(w.part=m[3]),_&16&&(w.markers=m[4]),r.$set(w)}m[6]==="month"&&m[5]?d?(d.p(m,_),_&96&&b(d,1)):(d=Xl(m),d.c(),b(d,1),d.m(s,null)):d&&(W(),p(d,1,1,()=>{d=null}),Y()),(!u||_&8&&o!==(o="wx-calendar "+(m[3]!=="normal"&&m[3]!=="both"?"wx-part":"")+" svelte-9ihaic"))&&g(e,"class",o)},i(m){u||(b(n.$$.fragment,m),r&&b(r.$$.fragment,m),b(d),u=!0)},o(m){p(n.$$.fragment,m),r&&p(r.$$.fragment,m),p(d),u=!1},d(m){m&&k(e),A(n),r&&A(r),d&&d.d()}}}function Vu(l,e,t){const n=Ee(),i=ve("wx-i18n").getGroup("calendar");let{value:s}=e,{current:r}=e,{done:a=!1}=e,{part:o="normal"}=e,{markers:u=null}=e,{buttons:f=!0}=e,c="month";function d(T,L){T.preventDefault(),n("change",{value:L})}function m(){c==="duodecade"?t(6,c="year"):c==="year"&&t(6,c="month")}function _(T){T.diff==0?c==="month"?t(6,c="year"):c==="year"&&t(6,c="duodecade"):n("shift",T)}function h(T){n("change",{select:!0,value:T})}const w=T=>_(T.detail),y=T=>d(T,-1),S=T=>d(T,null),C=T=>d(T,new Date);return l.$$set=T=>{"value"in T&&t(0,s=T.value),"current"in T&&t(1,r=T.current),"done"in T&&t(2,a=T.done),"part"in T&&t(3,o=T.part),"markers"in T&&t(4,u=T.markers),"buttons"in T&&t(5,f=T.buttons)},[s,r,a,o,u,f,c,i,d,m,_,h,w,y,S,C]}class sn extends ee{constructor(e){super(),x(this,e,Vu,Uu,Z,{value:0,current:1,done:2,part:3,markers:4,buttons:5})}}function Ku(l){let e,t;return e=new sn({props:{value:l[0],current:l[1],markers:l[2],buttons:l[3]}}),e.$on("shift",l[6]),e.$on("change",l[7]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,[i]){const s={};i&1&&(s.value=n[0]),i&2&&(s.current=n[1]),i&4&&(s.markers=n[2]),i&8&&(s.buttons=n[3]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Bu(l,e,t){const n=Ee();let{value:i}=e,{current:s}=e,{markers:r=null}=e,{buttons:a=!0}=e;function o(){s||t(1,s=i?new Date(i):new Date)}function u({diff:m,type:_}){const h=ln[_];t(1,s=m>0?h.next(s):h.prev(s))}function f(m){const _=m.value;_?(t(1,s=new Date(_)),t(0,i=new Date(_))):t(0,i=null),n("change",{value:i})}const c=m=>u(m.detail),d=m=>f(m.detail);return l.$$set=m=>{"value"in m&&t(0,i=m.value),"current"in m&&t(1,s=m.current),"markers"in m&&t(2,r=m.markers),"buttons"in m&&t(3,a=m.buttons)},l.$$.update=()=>{l.$$.dirty&1&&o()},[i,s,r,a,u,f,c,d]}class Wu extends ee{constructor(e){super(),x(this,e,Bu,Ku,Z,{value:0,current:1,markers:2,buttons:3})}}function $l(l){let e,t;return e=new Ct({props:{cancel:l[13],width:l[4],align:l[5],autoFit:!!l[5],$$slots:{default:[Yu]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&16&&(s.width=n[4]),i&32&&(s.align=n[5]),i&32&&(s.autoFit=!!n[5]),i&16777345&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Yu(l){let e,t;return e=new Wu({props:{buttons:l[7],value:l[0]}}),e.$on("change",l[17]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&128&&(s.buttons=n[7]),i&1&&(s.value=n[0]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Gu(l){let e,t,n,i,s,r;t=new An({props:{css:l[8],title:l[9],value:l[12],id:l[1],readonly:!l[10],disabled:l[2],error:l[3],placeholder:l[6],icon:"wxi-calendar",inputStyle:"cursor: pointer; width: 100%; padding-right: calc(var(--wx-input-icon-size) + var(--wx-input-icon-indent) * 2);"}}),t.$on("input",l[13]),t.$on("change",l[15]);let a=l[11]&&!l[2]&&$l(l);return{c(){e=I("div"),P(t.$$.fragment),n=H(),a&&a.c(),g(e,"class","wx-datepicker svelte-1k3rk87")},m(o,u){v(o,e,u),R(t,e,null),M(e,n),a&&a.m(e,null),i=!0,s||(r=[te(window,"scroll",l[13]),te(e,"click",l[18])],s=!0)},p(o,[u]){const f={};u&256&&(f.css=o[8]),u&512&&(f.title=o[9]),u&4096&&(f.value=o[12]),u&2&&(f.id=o[1]),u&1024&&(f.readonly=!o[10]),u&4&&(f.disabled=o[2]),u&8&&(f.error=o[3]),u&64&&(f.placeholder=o[6]),t.$set(f),o[11]&&!o[2]?a?(a.p(o,u),u&2052&&b(a,1)):(a=$l(o),a.c(),b(a,1),a.m(e,null)):a&&(W(),p(a,1,1,()=>{a=null}),Y())},i(o){i||(b(t.$$.fragment,o),b(a),i=!0)},o(o){p(t.$$.fragment,o),p(a),i=!1},d(o){o&&k(e),A(t),a&&a.d(),s=!1,De(r)}}}function Ju(l,e,t){let{value:n}=e,{id:i=xe()}=e,{disabled:s=!1}=e,{error:r=!1}=e,{width:a="unset"}=e,{align:o="start"}=e,{placeholder:u=""}=e,{format:f}=e,{buttons:c=!0}=e,{css:d=""}=e,{title:m=""}=e,{editable:_=!1}=e;const h=Ee(),{calendar:w,formats:y}=ve("wx-i18n").getRaw(),S=f||y.dateFormat;let C=typeof S=="function"?S:Ft(S,w),T;function L(){t(11,T=!1)}function N(O){const D=O===n||O&&n&&O.valueOf()===n.valueOf()||!O&&!n;t(0,n=O),D||h("select",{selected:O}),setTimeout(L,1)}let E;function F(O){if(!_)return;const{value:D,input:j}=O.detail;if(j)return;t(12,E="");let z=typeof _=="function"?_(D):D?new Date(D):null;z=isNaN(z)?n||null:z||null,N(z)}const G=O=>N(O.detail.value),U=()=>t(11,T=!0);return l.$$set=O=>{"value"in O&&t(0,n=O.value),"id"in O&&t(1,i=O.id),"disabled"in O&&t(2,s=O.disabled),"error"in O&&t(3,r=O.error),"width"in O&&t(4,a=O.width),"align"in O&&t(5,o=O.align),"placeholder"in O&&t(6,u=O.placeholder),"format"in O&&t(16,f=O.format),"buttons"in O&&t(7,c=O.buttons),"css"in O&&t(8,d=O.css),"title"in O&&t(9,m=O.title),"editable"in O&&t(10,_=O.editable)},l.$$.update=()=>{l.$$.dirty&1&&t(12,E=n?C(n):"")},[n,i,s,r,a,o,u,c,d,m,_,T,E,L,N,F,f,G,U]}class Qu extends ee{constructor(e){super(),x(this,e,Ju,Gu,Z,{value:0,id:1,disabled:2,error:3,width:4,align:5,placeholder:6,format:16,buttons:7,css:8,title:9,editable:10})}}function Xu(l){let e,t,n,i,s,r,a;return n=new sn({props:{value:{start:l[0],end:l[1]},current:l[6],markers:l[4],buttons:!1,part:"left"}}),n.$on("shift",l[14]),n.$on("change",l[15]),r=new sn({props:{value:{start:l[0],end:l[1]},current:l[7],markers:l[4],done:l[2],buttons:l[5],part:"right"}}),r.$on("shift",l[16]),r.$on("change",l[17]),{c(){e=I("div"),t=I("div"),P(n.$$.fragment),i=H(),s=I("div"),P(r.$$.fragment),g(t,"class","wx-half svelte-wlbsu6"),g(s,"class","wx-half svelte-wlbsu6"),g(e,"class","wx-rangecalendar svelte-wlbsu6")},m(o,u){v(o,e,u),M(e,t),R(n,t,null),M(e,i),M(e,s),R(r,s,null),a=!0},p(o,u){const f={};u&3&&(f.value={start:o[0],end:o[1]}),u&64&&(f.current=o[6]),u&16&&(f.markers=o[4]),n.$set(f);const c={};u&3&&(c.value={start:o[0],end:o[1]}),u&128&&(c.current=o[7]),u&16&&(c.markers=o[4]),u&4&&(c.done=o[2]),u&32&&(c.buttons=o[5]),r.$set(c)},i(o){a||(b(n.$$.fragment,o),b(r.$$.fragment,o),a=!0)},o(o){p(n.$$.fragment,o),p(r.$$.fragment,o),a=!1},d(o){o&&k(e),A(n),A(r)}}}function Zu(l){let e,t;return e=new sn({props:{value:{start:l[0],end:l[1]},current:l[6],markers:l[4],done:l[2],buttons:l[5],part:"both"}}),e.$on("shift",l[12]),e.$on("change",l[13]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&3&&(s.value={start:n[0],end:n[1]}),i&64&&(s.current=n[6]),i&16&&(s.markers=n[4]),i&4&&(s.done=n[2]),i&32&&(s.buttons=n[5]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function $u(l){let e,t,n,i;const s=[Zu,Xu],r=[];function a(o,u){return o[3]==1?0:1}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=se()},m(o,u){r[e].m(o,u),v(o,n,u),i=!0},p(o,[u]){let f=e;e=a(o),e===f?r[e].p(o,u):(W(),p(r[f],1,1,()=>{r[f]=null}),Y(),t=r[e],t?t.p(o,u):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(n.parentNode,n))},i(o){i||(b(t),i=!0)},o(o){p(t),i=!1},d(o){o&&k(n),r[e].d(o)}}}function xl(l,e,t){const n=new Date(l);return n.setMonth(n.getMonth()+e),n}function xu(l,e,t){const n=Ee();let{start:i}=e,{end:s}=e,{done:r}=e,{current:a}=e,{months:o=2}=e,{markers:u=null}=e,{buttons:f=!0}=e,c,d;function m(U){t(6,c=U?new Date(U):a||new Date)}function _(){c&&t(7,d=xl(c,1))}function h(){d&&t(6,c=xl(d,-1))}function w({diff:U,type:O}){const D=ln[O];U>0?t(7,d=D.next(d)):U<0&&t(6,c=D.prev(c))}function y(U){C(U),i&&t(6,c=new Date(i))}function S(U){C(U),s&&t(7,d=new Date(s))}function C(U){const O=U.value,D=O===-1;D||(U.select?!i||s?(t(0,i=O),t(1,s=null)):i>O?(t(1,s=i),t(0,i=O)):t(1,s=O):O?(t(0,i=new Date(O)),t(1,s=new Date(O))):t(0,i=t(1,s=null))),(D||!r)&&n("change",{start:i,end:s})}const T=U=>w(U.detail),L=U=>y(U.detail),N=U=>w(U.detail),E=U=>y(U.detail),F=U=>w(U.detail),G=U=>S(U.detail);return l.$$set=U=>{"start"in U&&t(0,i=U.start),"end"in U&&t(1,s=U.end),"done"in U&&t(2,r=U.done),"current"in U&&t(11,a=U.current),"months"in U&&t(3,o=U.months),"markers"in U&&t(4,u=U.markers),"buttons"in U&&t(5,f=U.buttons)},l.$$.update=()=>{l.$$.dirty&1&&m(i),l.$$.dirty&64&&_(),l.$$.dirty&128&&h()},[i,s,r,o,u,f,c,d,w,y,S,a,T,L,N,E,F,G]}class ef extends ee{constructor(e){super(),x(this,e,xu,$u,Z,{start:0,end:1,done:2,current:11,months:3,markers:4,buttons:5})}}function tf(l){let e,t,n,i;return{c(){e=I("i"),g(e,"title",l[1]),g(e,"class",t="wx-icon "+l[0]+" svelte-12ezr0r")},m(s,r){v(s,e,r),n||(i=te(e,"click",l[6]),n=!0)},p(s,r){r&2&&g(e,"title",s[1]),r&1&&t!==(t="wx-icon "+s[0]+" svelte-12ezr0r")&&g(e,"class",t)},i:K,o:K,d(s){s&&k(e),n=!1,i()}}}function nf(l){let e,t,n,i,s;const r=l[4].default,a=be(r,l,l[3],null);return{c(){e=I("i"),a&&a.c(),g(e,"title",l[1]),g(e,"role","icon"),g(e,"class",t="wx-icon "+l[0]+" svelte-12ezr0r")},m(o,u){v(o,e,u),a&&a.m(e,null),n=!0,i||(s=te(e,"click",l[5]),i=!0)},p(o,u){a&&a.p&&(!n||u&8)&&we(a,r,o,o[3],n?pe(r,o[3],u,null):ke(o[3]),null),(!n||u&2)&&g(e,"title",o[1]),(!n||u&1&&t!==(t="wx-icon "+o[0]+" svelte-12ezr0r"))&&g(e,"class",t)},i(o){n||(b(a,o),n=!0)},o(o){p(a,o),n=!1},d(o){o&&k(e),a&&a.d(o),i=!1,s()}}}function lf(l){let e,t,n,i;const s=[nf,tf],r=[];function a(o,u){return o[2]&&o[2].default?0:1}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=se()},m(o,u){r[e].m(o,u),v(o,n,u),i=!0},p(o,[u]){t.p(o,u)},i(o){i||(b(t),i=!0)},o(o){p(t),i=!1},d(o){o&&k(n),r[e].d(o)}}}function sf(l,e,t){let{$$slots:n={},$$scope:i}=e,{css:s=""}=e,{title:r=""}=e;const a=e.$$slots;function o(f){ye.call(this,l,f)}function u(f){ye.call(this,l,f)}return l.$$set=f=>{t(7,e=Ce(Ce({},e),We(f))),"css"in f&&t(0,s=f.css),"title"in f&&t(1,r=f.title),"$$scope"in f&&t(3,i=f.$$scope)},e=We(e),[s,r,a,i,n,o,u]}let Me=class extends ee{constructor(e){super(),x(this,e,sf,lf,Z,{css:0,title:1})}};const rf=l=>({option:l&33554432}),ei=l=>({option:l[25]});function ti(l,e,t){const n=l.slice();return n[26]=e[t],n}const of=l=>({option:l&256}),ni=l=>({option:l[26]});function af(l){let e=l[26][l[1]]+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p(n,i){i&258&&e!==(e=n[26][n[1]]+"")&&re(t,e)},d(n){n&&k(t)}}}function li(l){let e,t,n;function i(){return l[19](l[26])}return{c(){e=I("i"),g(e,"class","wxi-close svelte-ijj3jn")},m(s,r){v(s,e,r),t||(n=te(e,"click",pt(i)),t=!0)},p(s,r){l=s},d(s){s&&k(e),t=!1,n()}}}function ii(l,e){let t,n,i,s;const r=e[18].default,a=be(r,e,e[23],ni),o=a||af(e);let u=!e[4]&&li(e);return{key:l,first:null,c(){t=I("div"),o&&o.c(),n=H(),u&&u.c(),i=H(),g(t,"class","wx-tag svelte-ijj3jn"),this.first=t},m(f,c){v(f,t,c),o&&o.m(t,null),M(t,n),u&&u.m(t,null),M(t,i),s=!0},p(f,c){e=f,a?a.p&&(!s||c&8388864)&&we(a,r,e,e[23],s?pe(r,e[23],c,of):ke(e[23]),ni):o&&o.p&&(!s||c&258)&&o.p(e,s?c:-1),e[4]?u&&(u.d(1),u=null):u?u.p(e,c):(u=li(e),u.c(),u.m(t,i))},i(f){s||(b(o,f),s=!0)},o(f){p(o,f),s=!1},d(f){f&&k(t),o&&o.d(f),u&&u.d()}}}function si(l){let e,t;return e=new Fl({props:{items:l[9],$$slots:{default:[ff,({option:n})=>({25:n}),({option:n})=>n?33554432:0]},$$scope:{ctx:l}}}),e.$on("ready",l[12]),e.$on("select",l[14]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&512&&(s.items=n[9]),i&41943105&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function ri(l){let e,t;return e=new Ha({props:{style:"margin-right: 8px; pointer-events: none;",name:l[25].id,value:l[0]&&l[0].includes(l[25].id)}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&33554432&&(s.name=n[25].id),i&33554433&&(s.value=n[0]&&n[0].includes(n[25].id)),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function uf(l){let e=l[25].name+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p(n,i){i&33554432&&e!==(e=n[25].name+"")&&re(t,e)},d(n){n&&k(t)}}}function ff(l){let e,t,n=l[6]&&ri(l);const i=l[18].default,s=be(i,l,l[23],ei),r=s||uf(l);return{c(){n&&n.c(),e=H(),r&&r.c()},m(a,o){n&&n.m(a,o),v(a,e,o),r&&r.m(a,o),t=!0},p(a,o){a[6]?n?(n.p(a,o),o&64&&b(n,1)):(n=ri(a),n.c(),b(n,1),n.m(e.parentNode,e)):n&&(W(),p(n,1,1,()=>{n=null}),Y()),s?s.p&&(!t||o&41943040)&&we(s,i,a,a[23],t?pe(i,a[23],o,rf):ke(a[23]),ei):r&&r.p&&(!t||o&33554432)&&r.p(a,t?o:-1)},i(a){t||(b(n),b(r,a),t=!0)},o(a){p(n),p(r,a),t=!1},d(a){a&&k(e),n&&n.d(a),r&&r.d(a)}}}function cf(l){let e,t,n,i=[],s=new Map,r,a,o,u,f,c,d,m,_,h=fe(l[8]);const w=S=>S[26].id;for(let S=0;S<h.length;S+=1){let C=ti(l,h,S),T=w(C);s.set(T,i[S]=ii(T,C))}let y=!l[4]&&si(l);return{c(){e=I("div"),t=I("div"),n=I("div");for(let S=0;S<i.length;S+=1)i[S].c();r=H(),a=I("div"),o=I("input"),u=H(),f=I("i"),c=H(),y&&y.c(),g(n,"class","wx-tags svelte-ijj3jn"),g(o,"type","text"),g(o,"placeholder",l[2]),o.disabled=l[4],g(o,"class","svelte-ijj3jn"),g(f,"class","wx-icon wxi-angle-down svelte-ijj3jn"),g(a,"class","wx-select svelte-ijj3jn"),g(t,"class","wx-wrapper svelte-ijj3jn"),g(e,"title",l[3]),g(e,"class","wx-multicombo svelte-ijj3jn"),X(e,"wx-error",l[5]),X(e,"wx-disabled",l[4]),X(e,"wx-not-empty",l[8].length),X(e,"wx-focus",oi)},m(S,C){v(S,e,C),M(e,t),M(t,n);for(let T=0;T<i.length;T+=1)i[T]&&i[T].m(n,null);M(t,r),M(t,a),M(a,o),je(o,l[7]),M(a,u),M(a,f),M(e,c),y&&y.m(e,null),d=!0,m||(_=[te(o,"input",l[20]),te(o,"input",l[13]),te(e,"click",l[21]),te(e,"keydown",l[22])],m=!0)},p(S,[C]){C&8421650&&(h=fe(S[8]),W(),i=Oe(i,C,w,1,S,h,s,n,Ue,ii,null,ti),Y()),(!d||C&4)&&g(o,"placeholder",S[2]),(!d||C&16)&&(o.disabled=S[4]),C&128&&o.value!==S[7]&&je(o,S[7]),S[4]?y&&(W(),p(y,1,1,()=>{y=null}),Y()):y?(y.p(S,C),C&16&&b(y,1)):(y=si(S),y.c(),b(y,1),y.m(e,null)),(!d||C&8)&&g(e,"title",S[3]),(!d||C&32)&&X(e,"wx-error",S[5]),(!d||C&16)&&X(e,"wx-disabled",S[4]),(!d||C&256)&&X(e,"wx-not-empty",S[8].length),(!d||C&16)&&X(e,"wx-focus",oi)},i(S){if(!d){for(let C=0;C<h.length;C+=1)b(i[C]);b(y),d=!0}},o(S){for(let C=0;C<i.length;C+=1)p(i[C]);p(y),d=!1},d(S){S&&k(e);for(let C=0;C<i.length;C+=1)i[C].d();y&&y.d(),m=!1,De(_)}}}let oi=!1;function df(l,e,t){let{$$slots:n={},$$scope:i}=e,{value:s}=e,{options:r=[]}=e,{textField:a="label"}=e,{placeholder:o=""}=e,{title:u=""}=e,{disabled:f=!1}=e,{error:c=!1}=e,{checkboxes:d=!1}=e;const m=Ee();let _="",h=[],w,y,S;function C(D){t(10,y=D.detail.navigate),t(11,S=D.detail.keydown)}function T(){t(9,w=_?r.filter(D=>D[a].toLowerCase().includes(_.toLowerCase())):r),w.length?y(0):y(null)}function L(D){const{id:j}=D.detail;if(j){let z;s?s.includes(j)?z=s.filter(q=>q!==j):z=[...s,j]:z=[j],t(0,s=z),t(8,h=r.filter(q=>s.includes(q.id))),m("select",{selected:h}),m("change",{value:s})}}function N(D){t(0,s=s.filter(j=>j!==D)),m("change",{value:s})}const E=()=>s&&s.length?w.findIndex(D=>D.id===s[0]):0,F=D=>N(D.id);function G(){_=this.value,t(7,_)}const U=()=>!f&&y(E()),O=D=>S(D,E());return l.$$set=D=>{"value"in D&&t(0,s=D.value),"options"in D&&t(17,r=D.options),"textField"in D&&t(1,a=D.textField),"placeholder"in D&&t(2,o=D.placeholder),"title"in D&&t(3,u=D.title),"disabled"in D&&t(4,f=D.disabled),"error"in D&&t(5,c=D.error),"checkboxes"in D&&t(6,d=D.checkboxes),"$$scope"in D&&t(23,i=D.$$scope)},l.$$.update=()=>{l.$$.dirty&131072&&t(9,w=r),l.$$.dirty&131073&&t(8,h=s?r.filter(D=>s.includes(D.id)):[])},[s,a,o,u,f,c,d,_,h,w,y,S,C,T,L,N,E,r,n,F,G,U,O,i]}class mf extends ee{constructor(e){super(),x(this,e,df,cf,Z,{value:0,options:17,textField:1,placeholder:2,title:3,disabled:4,error:5,checkboxes:6})}}function ai(l,e,t){const n=l.slice();return n[9]=e[t],n}function ui(l,e){let t,n=e[9][e[1]]+"",i,s;return{key:l,first:null,c(){t=I("option"),i=$(n),t.__value=s=e[9].id,je(t,t.__value),g(t,"class","svelte-f9enzu"),this.first=t},m(r,a){v(r,t,a),M(t,i)},p(r,a){e=r,a&6&&n!==(n=e[9][e[1]]+"")&&re(i,n),a&4&&s!==(s=e[9].id)&&(t.__value=s,je(t,t.__value))},d(r){r&&k(t)}}}function fi(l){let e,t;return{c(){e=I("div"),t=$(l[3]),g(e,"class","wx-placeholder svelte-f9enzu")},m(n,i){v(n,e,i),M(e,t)},p(n,i){i&8&&re(t,n[3])},d(n){n&&k(e)}}}function _f(l){let e,t,n=[],i=new Map,s,r,a,o,u,f=fe(l[2]);const c=m=>m[9].id;for(let m=0;m<f.length;m+=1){let _=ai(l,f,m),h=c(_);i.set(h,n[m]=ui(h,_))}let d=!l[0]&&l[0]!==0&&fi(l);return{c(){e=I("div"),t=I("select");for(let m=0;m<n.length;m+=1)n[m].c();s=H(),d&&d.c(),r=H(),a=I("i"),g(t,"id",l[7]),t.disabled=l[5],g(t,"title",l[4]),g(t,"class","svelte-f9enzu"),l[0]===void 0&&ut(()=>l[8].call(t)),X(t,"wx-error",l[6]),g(a,"class","wx-icon wxi-angle-down svelte-f9enzu"),g(e,"class","wx-select svelte-f9enzu")},m(m,_){v(m,e,_),M(e,t);for(let h=0;h<n.length;h+=1)n[h]&&n[h].m(t,null);fl(t,l[0],!0),M(e,s),d&&d.m(e,null),M(e,r),M(e,a),o||(u=te(t,"change",l[8]),o=!0)},p(m,[_]){_&6&&(f=fe(m[2]),n=Oe(n,_,c,1,m,f,i,t,gl,ui,null,ai)),_&128&&g(t,"id",m[7]),_&32&&(t.disabled=m[5]),_&16&&g(t,"title",m[4]),_&5&&fl(t,m[0]),_&64&&X(t,"wx-error",m[6]),!m[0]&&m[0]!==0?d?d.p(m,_):(d=fi(m),d.c(),d.m(e,r)):d&&(d.d(1),d=null)},i:K,o:K,d(m){m&&k(e);for(let _=0;_<n.length;_+=1)n[_].d();d&&d.d(),o=!1,u()}}}function hf(l,e,t){let{label:n="label"}=e,{value:i=""}=e,{options:s=[]}=e,{placeholder:r=""}=e,{title:a}=e,{disabled:o=!1}=e,{error:u=!1}=e,{id:f=xe()}=e;function c(){i=sa(this),t(0,i),t(2,s)}return l.$$set=d=>{"label"in d&&t(1,n=d.label),"value"in d&&t(0,i=d.value),"options"in d&&t(2,s=d.options),"placeholder"in d&&t(3,r=d.placeholder),"title"in d&&t(4,a=d.title),"disabled"in d&&t(5,o=d.disabled),"error"in d&&t(6,u=d.error),"id"in d&&t(7,f=d.id)},[i,n,s,r,a,o,u,f,c]}class ci extends ee{constructor(e){super(),x(this,e,hf,_f,Z,{label:1,value:0,options:2,placeholder:3,title:4,disabled:5,error:6,id:7})}}function di(l){let e,t;return{c(){e=I("label"),t=$(l[2]),g(e,"for",l[1]),g(e,"class","svelte-vxce8u")},m(n,i){v(n,e,i),M(e,t)},p(n,i){i&4&&re(t,n[2]),i&2&&g(e,"for",n[1])},d(n){n&&k(e)}}}function gf(l){let e,t,n,i,s,r,a,o=l[2]&&di(l);return{c(){e=I("div"),o&&o.c(),t=H(),n=I("div"),i=I("input"),g(i,"id",l[1]),g(i,"type","range"),g(i,"min",l[4]),g(i,"max",l[5]),g(i,"step",l[6]),i.disabled=l[8],g(i,"style",l[9]),g(i,"class","svelte-vxce8u"),g(n,"class","svelte-vxce8u"),g(e,"class","wx-slider svelte-vxce8u"),g(e,"style",s=l[3]?`width: ${l[3]}`:""),g(e,"title",l[7])},m(u,f){v(u,e,f),o&&o.m(e,null),M(e,t),M(e,n),M(n,i),je(i,l[0]),r||(a=[te(i,"change",l[13]),te(i,"input",l[13]),te(i,"change",l[10])],r=!0)},p(u,[f]){u[2]?o?o.p(u,f):(o=di(u),o.c(),o.m(e,t)):o&&(o.d(1),o=null),f&2&&g(i,"id",u[1]),f&16&&g(i,"min",u[4]),f&32&&g(i,"max",u[5]),f&64&&g(i,"step",u[6]),f&256&&(i.disabled=u[8]),f&512&&g(i,"style",u[9]),f&1&&je(i,u[0]),f&8&&s!==(s=u[3]?`width: ${u[3]}`:"")&&g(e,"style",s),f&128&&g(e,"title",u[7])},i:K,o:K,d(u){u&&k(e),o&&o.d(),r=!1,De(a)}}}function bf(l,e,t){const n=Ee();let{id:i=xe()}=e,{label:s=""}=e,{width:r=""}=e,{min:a=0}=e,{max:o=100}=e,{value:u=0}=e,{step:f=1}=e,{title:c=""}=e,{disabled:d=!1}=e,m=0,_="",h;function w({target:S}){const C=S.value*1;n("change",{value:C}),t(0,u=C)}function y(){u=kn(this.value),t(0,u),t(4,a),t(5,o),t(8,d),t(11,m),t(12,h)}return l.$$set=S=>{"id"in S&&t(1,i=S.id),"label"in S&&t(2,s=S.label),"width"in S&&t(3,r=S.width),"min"in S&&t(4,a=S.min),"max"in S&&t(5,o=S.max),"value"in S&&t(0,u=S.value),"step"in S&&t(6,f=S.step),"title"in S&&t(7,c=S.title),"disabled"in S&&t(8,d=S.disabled)},l.$$.update=()=>{l.$$.dirty&6449&&(t(11,m=(u-a)/(o-a)*100+"%"),t(9,_=d?"":`background: linear-gradient(90deg, var(--wx-slider-primary) 0% ${m}, var(--wx-slider-background) ${m} 100%);`),isNaN(u)&&t(0,u=0),h!==u&&(n("change",{value:u,previous:h,input:!0}),t(12,h=u)))},[u,i,s,r,a,o,f,c,d,_,w,m,h,y]}class pf extends ee{constructor(e){super(),x(this,e,bf,gf,Z,{id:1,label:2,width:3,min:4,max:5,value:0,step:6,title:7,disabled:8})}}function It(l,{delay:e=0,duration:t=400,easing:n=nl}={}){const i=+getComputedStyle(l).opacity;return{delay:e,duration:t,easing:n,css:s=>`opacity: ${s*i}`}}function wf(l){let e,t,n=l[0].text+"",i,s,r,a,o,u,f,c,d;return{c(){e=I("div"),t=I("div"),i=$(n),s=H(),r=I("div"),a=I("i"),g(t,"class","wx-text svelte-5yx8ba"),g(a,"class","wx-close wxi-close svelte-5yx8ba"),g(r,"class","wx-button svelte-5yx8ba"),g(e,"class",o="wx-notice wx-"+(l[0].type?l[0].type:"")+" svelte-5yx8ba"),g(e,"role","status"),g(e,"aria-live","polite")},m(m,_){v(m,e,_),M(e,t),M(t,i),M(e,s),M(e,r),M(r,a),f=!0,c||(d=te(a,"click",l[1]),c=!0)},p(m,[_]){(!f||_&1)&&n!==(n=m[0].text+"")&&re(i,n),(!f||_&1&&o!==(o="wx-notice wx-"+(m[0].type?m[0].type:"")+" svelte-5yx8ba"))&&g(e,"class",o)},i(m){f||(m&&ut(()=>{f&&(u||(u=St(e,It,{},!0)),u.run(1))}),f=!0)},o(m){m&&(u||(u=St(e,It,{},!1)),u.run(0)),f=!1},d(m){m&&k(e),m&&u&&u.end(),c=!1,d()}}}function kf(l,e,t){let{notice:n={}}=e;function i(){n.remove&&n.remove()}return l.$$set=s=>{"notice"in s&&t(0,n=s.notice)},[n,i]}class vf extends ee{constructor(e){super(),x(this,e,kf,wf,Z,{notice:0})}}function mi(l,e,t){const n=l.slice();return n[1]=e[t],n}function _i(l,e){let t,n,i;return n=new vf({props:{notice:e[1]}}),{key:l,first:null,c(){t=se(),P(n.$$.fragment),this.first=t},m(s,r){v(s,t,r),R(n,s,r),i=!0},p(s,r){e=s;const a={};r&1&&(a.notice=e[1]),n.$set(a)},i(s){i||(b(n.$$.fragment,s),i=!0)},o(s){p(n.$$.fragment,s),i=!1},d(s){s&&k(t),A(n,s)}}}function yf(l){let e,t=[],n=new Map,i,s=fe(l[0]);const r=a=>a[1].id;for(let a=0;a<s.length;a+=1){let o=mi(l,s,a),u=r(o);n.set(u,t[a]=_i(u,o))}return{c(){e=I("div");for(let a=0;a<t.length;a+=1)t[a].c();g(e,"class","wx-notices svelte-ervf1h")},m(a,o){v(a,e,o);for(let u=0;u<t.length;u+=1)t[u]&&t[u].m(e,null);i=!0},p(a,[o]){o&1&&(s=fe(a[0]),W(),t=Oe(t,o,r,1,a,s,n,e,Ue,_i,null,mi),Y())},i(a){if(!i){for(let o=0;o<s.length;o+=1)b(t[o]);i=!0}},o(a){for(let o=0;o<t.length;o+=1)p(t[o]);i=!1},d(a){a&&k(e);for(let o=0;o<t.length;o+=1)t[o].d()}}}function Sf(l,e,t){let{data:n}=e;return l.$$set=i=>{"data"in i&&t(0,n=i.data)},[n]}class Cf extends ee{constructor(e){super(),x(this,e,Sf,yf,Z,{data:0})}}function hi(l,e,t){const n=l.slice();return n[11]=e[t],n}const If=l=>({}),gi=l=>({}),Mf=l=>({}),bi=l=>({});function pi(l){let e,t;return{c(){e=I("div"),t=$(l[0]),g(e,"class","wx-header svelte-at32q2")},m(n,i){v(n,e,i),M(e,t)},p(n,i){i&1&&re(t,n[0])},d(n){n&&k(e)}}}function Tf(l){let e,t=l[0]&&pi(l);return{c(){t&&t.c(),e=se()},m(n,i){t&&t.m(n,i),v(n,e,i)},p(n,i){n[0]?t?t.p(n,i):(t=pi(n),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(n){n&&k(e),t&&t.d(n)}}}function Df(l){let e=l[5](l[11])+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p(n,i){i&8&&e!==(e=n[5](n[11])+"")&&re(t,e)},d(n){n&&k(t)}}}function wi(l){let e,t,n,i;function s(){return l[8](l[11])}return t=new Ve({props:{type:"block "+(l[11]==="ok"?"primary":"secondary"),click:s,$$slots:{default:[Df]},$$scope:{ctx:l}}}),{c(){e=I("div"),P(t.$$.fragment),n=H(),g(e,"class","wx-button svelte-at32q2")},m(r,a){v(r,e,a),R(t,e,null),M(e,n),i=!0},p(r,a){l=r;const o={};a&8&&(o.type="block "+(l[11]==="ok"?"primary":"secondary")),a&14&&(o.click=s),a&1032&&(o.$$scope={dirty:a,ctx:l}),t.$set(o)},i(r){i||(b(t.$$.fragment,r),i=!0)},o(r){p(t.$$.fragment,r),i=!1},d(r){r&&k(e),A(t)}}}function Ef(l){let e,t,n=fe(l[3]),i=[];for(let r=0;r<n.length;r+=1)i[r]=wi(hi(l,n,r));const s=r=>p(i[r],1,1,()=>{i[r]=null});return{c(){e=I("div");for(let r=0;r<i.length;r+=1)i[r].c();g(e,"class","wx-buttons svelte-at32q2")},m(r,a){v(r,e,a);for(let o=0;o<i.length;o+=1)i[o]&&i[o].m(e,null);t=!0},p(r,a){if(a&46){n=fe(r[3]);let o;for(o=0;o<n.length;o+=1){const u=hi(r,n,o);i[o]?(i[o].p(u,a),b(i[o],1)):(i[o]=wi(u),i[o].c(),b(i[o],1),i[o].m(e,null))}for(W(),o=n.length;o<i.length;o+=1)s(o);Y()}},i(r){if(!t){for(let a=0;a<n.length;a+=1)b(i[a]);t=!0}},o(r){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)p(i[a]);t=!1},d(r){r&&k(e),at(i,r)}}}function Lf(l){let e,t,n,i,s,r,a,o,u;const f=l[7].title,c=be(f,l,l[10],bi),d=c||Tf(l),m=l[7].default,_=be(m,l,l[10],null),h=l[7].buttons,w=be(h,l,l[10],gi),y=w||Ef(l);return{c(){e=I("div"),t=I("div"),d&&d.c(),n=H(),i=I("div"),_&&_.c(),s=H(),y&&y.c(),g(t,"class","wx-window svelte-at32q2"),g(e,"class","wx-modal svelte-at32q2"),g(e,"tabindex","0")},m(S,C){v(S,e,C),M(e,t),d&&d.m(t,null),M(t,n),M(t,i),_&&_.m(i,null),M(t,s),y&&y.m(t,null),l[9](e),a=!0,o||(u=te(e,"keydown",l[6]),o=!0)},p(S,[C]){c?c.p&&(!a||C&1024)&&we(c,f,S,S[10],a?pe(f,S[10],C,Mf):ke(S[10]),bi):d&&d.p&&(!a||C&1)&&d.p(S,a?C:-1),_&&_.p&&(!a||C&1024)&&we(_,m,S,S[10],a?pe(m,S[10],C,null):ke(S[10]),null),w?w.p&&(!a||C&1024)&&we(w,h,S,S[10],a?pe(h,S[10],C,If):ke(S[10]),gi):y&&y.p&&(!a||C&14)&&y.p(S,a?C:-1)},i(S){a||(b(d,S),b(_,S),b(y,S),S&&ut(()=>{a&&(r||(r=St(e,It,{duration:100},!0)),r.run(1))}),a=!0)},o(S){p(d,S),p(_,S),p(y,S),S&&(r||(r=St(e,It,{duration:100},!1)),r.run(0)),a=!1},d(S){S&&k(e),d&&d.d(S),_&&_.d(S),y&&y.d(S),l[9](null),S&&r&&r.end(),o=!1,u()}}}function Rf(l,e,t){let{$$slots:n={},$$scope:i}=e;const s=ve("wx-i18n").getGroup("core");let{title:r=""}=e,{ok:a}=e,{cancel:o}=e,{buttons:u=["cancel","ok"]}=e;function f(_){switch(_.code){case"Enter":{const h=_.target.tagName;if(h==="TEXTAREA"||h==="BUTTON")return;a();break}case"Escape":o();break}}let c;ot(()=>{c.focus()});const d=_=>_==="ok"?a():o();function m(_){ue[_?"unshift":"push"](()=>{c=_,t(4,c)})}return l.$$set=_=>{"title"in _&&t(0,r=_.title),"ok"in _&&t(1,a=_.ok),"cancel"in _&&t(2,o=_.cancel),"buttons"in _&&t(3,u=_.buttons),"$$scope"in _&&t(10,i=_.$$scope)},[r,a,o,u,c,s,f,n,d,m,i]}class Af extends ee{constructor(e){super(),x(this,e,Rf,Lf,Z,{title:0,ok:1,cancel:2,buttons:3})}}function ki(l){let e,t;return e=new Af({props:{title:l[0].title,buttons:l[0].buttons,ok:l[0].resolve,cancel:l[0].reject,$$slots:{default:[Pf]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.title=n[0].title),i&1&&(s.buttons=n[0].buttons),i&1&&(s.ok=n[0].resolve),i&1&&(s.cancel=n[0].reject),i&9&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Pf(l){let e=l[0].message+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p(n,i){i&1&&e!==(e=n[0].message+"")&&re(t,e)},d(n){n&&k(t)}}}function Nf(l){let e,t,n,i;const s=l[2].default,r=be(s,l,l[3],null);let a=l[0]&&ki(l);return n=new Cf({props:{data:l[1]}}),{c(){r&&r.c(),e=H(),a&&a.c(),t=H(),P(n.$$.fragment)},m(o,u){r&&r.m(o,u),v(o,e,u),a&&a.m(o,u),v(o,t,u),R(n,o,u),i=!0},p(o,[u]){r&&r.p&&(!i||u&8)&&we(r,s,o,o[3],i?pe(s,o[3],u,null):ke(o[3]),null),o[0]?a?(a.p(o,u),u&1&&b(a,1)):(a=ki(o),a.c(),b(a,1),a.m(t.parentNode,t)):a&&(W(),p(a,1,1,()=>{a=null}),Y());const f={};u&2&&(f.data=o[1]),n.$set(f)},i(o){i||(b(r,o),b(a),b(n.$$.fragment,o),i=!0)},o(o){p(r,o),p(a),p(n.$$.fragment,o),i=!1},d(o){o&&(k(e),k(t)),r&&r.d(o),a&&a.d(o),A(n,o)}}}function zf(l,e,t){let{$$slots:n={},$$scope:i}=e,s=null;function r(u){return t(0,s={...u}),new Promise((f,c)=>{t(0,s.resolve=d=>{t(0,s=null),f(d)},s),t(0,s.reject=d=>{t(0,s=null),c(d)},s)})}let a=[];function o(u){u={...u},u.id=u.id||xe(),u.remove=()=>t(1,a=a.filter(f=>f.id!==u.id)),u.expire!=-1&&setTimeout(u.remove,u.expire||5e3),t(1,a=[...a,u])}return _t("wx-helpers",{showNotice:o,showModal:r}),l.$$set=u=>{"$$scope"in u&&t(3,i=u.$$scope)},[s,a,n,i]}class Hf extends ee{constructor(e){super(),x(this,e,zf,Nf,Z,{})}}const Of=l=>({}),vi=l=>({id:l[5]});function yi(l){let e,t;return{c(){e=I("label"),t=$(l[0]),g(e,"for",l[5]),g(e,"class","svelte-16h42zq")},m(n,i){v(n,e,i),M(e,t)},p(n,i){i&1&&re(t,n[0])},d(n){n&&k(e)}}}function Ff(l){let e,t,n,i,s,r,a,o=l[0]&&yi(l);const u=l[7].default,f=be(u,l,l[6],vi);return{c(){e=I("div"),o&&o.c(),t=H(),n=I("div"),f&&f.c(),g(n,"class",i="wx-field-control wx-"+l[4]+" svelte-16h42zq"),g(e,"class",s="wx-field wx-"+l[1]+" svelte-16h42zq"),g(e,"style",r=l[2]?`width: ${l[2]}`:""),X(e,"wx-error",l[3])},m(c,d){v(c,e,d),o&&o.m(e,null),M(e,t),M(e,n),f&&f.m(n,null),a=!0},p(c,[d]){c[0]?o?o.p(c,d):(o=yi(c),o.c(),o.m(e,t)):o&&(o.d(1),o=null),f&&f.p&&(!a||d&64)&&we(f,u,c,c[6],a?pe(u,c[6],d,Of):ke(c[6]),vi),(!a||d&16&&i!==(i="wx-field-control wx-"+c[4]+" svelte-16h42zq"))&&g(n,"class",i),(!a||d&2&&s!==(s="wx-field wx-"+c[1]+" svelte-16h42zq"))&&g(e,"class",s),(!a||d&4&&r!==(r=c[2]?`width: ${c[2]}`:""))&&g(e,"style",r),(!a||d&10)&&X(e,"wx-error",c[3])},i(c){a||(b(f,c),a=!0)},o(c){p(f,c),a=!1},d(c){c&&k(e),o&&o.d(),f&&f.d(c)}}}function qf(l,e,t){let{$$slots:n={},$$scope:i}=e,{label:s=""}=e,{position:r=""}=e,{width:a=""}=e,{error:o=!1}=e,{type:u=""}=e,f=xe();return l.$$set=c=>{"label"in c&&t(0,s=c.label),"position"in c&&t(1,r=c.position),"width"in c&&t(2,a=c.width),"error"in c&&t(3,o=c.error),"type"in c&&t(4,u=c.type),"$$scope"in c&&t(6,i=c.$$scope)},[s,r,a,o,u,f,i,n]}class lt extends ee{constructor(e){super(),x(this,e,qf,Ff,Z,{label:0,position:1,width:2,error:3,type:4})}}function jf(l){let e,t,n,i;const s=l[1].default,r=be(s,l,l[0],null);return{c(){e=I("div"),t=I("div"),r&&r.c(),g(t,"class","wx-window svelte-1ki3q24"),g(e,"class","wx-modal svelte-1ki3q24")},m(a,o){v(a,e,o),M(e,t),r&&r.m(t,null),i=!0},p(a,[o]){r&&r.p&&(!i||o&1)&&we(r,s,a,a[0],i?pe(s,a[0],o,null):ke(a[0]),null)},i(a){i||(b(r,a),a&&ut(()=>{i&&(n||(n=St(e,It,{duration:100},!0)),n.run(1))}),i=!0)},o(a){p(r,a),a&&(n||(n=St(e,It,{duration:100},!1)),n.run(0)),i=!1},d(a){a&&k(e),r&&r.d(a),a&&n&&n.end()}}}function Uf(l,e,t){let{$$slots:n={},$$scope:i}=e;return l.$$set=s=>{"$$scope"in s&&t(0,i=s.$$scope)},[i,n]}class Vf extends ee{constructor(e){super(),x(this,e,Uf,jf,Z,{})}}const Kf=l=>({}),Si=l=>({mount:l[1]});function Bf(l){let e,t,n,i;const s=l[5].default,r=be(s,l,l[4],Si);return{c(){e=I("div"),t=I("div"),r&&r.c(),g(t,"class",n="wx-"+l[0]+"-theme svelte-1dixdmq"),g(e,"class","wx-portal svelte-1dixdmq")},m(a,o){v(a,e,o),M(e,t),r&&r.m(t,null),l[6](t),i=!0},p(a,[o]){r&&r.p&&(!i||o&16)&&we(r,s,a,a[4],i?pe(s,a[4],o,Kf):ke(a[4]),Si),(!i||o&1&&n!==(n="wx-"+a[0]+"-theme svelte-1dixdmq"))&&g(t,"class",n)},i(a){i||(b(r,a),i=!0)},o(a){p(r,a),i=!1},d(a){a&&k(e),r&&r.d(a),l[6](null)}}}function Wf(l){for(;l!==document.body&&!l.getAttribute("data-wx-portal-root");)l=l.parentNode;return l}function Yf(l,e,t){let{$$slots:n={},$$scope:i}=e,s,{theme:r=""}=e,{target:a=void 0}=e,o=[];const u=c=>{o&&o.push(c)};r===""&&(r=ve("wx-theme")),ot(()=>{(a||Wf(s)).appendChild(s),o&&o.forEach(d=>d())}),yn(()=>{s&&s.parentNode&&s.parentNode.removeChild(s)});function f(c){ue[c?"unshift":"push"](()=>{s=c,t(2,s)})}return l.$$set=c=>{"theme"in c&&t(0,r=c.theme),"target"in c&&t(3,a=c.target),"$$scope"in c&&t(4,i=c.$$scope)},[r,u,s,a,i,n,f]}let Ci=class extends ee{constructor(e){super(),x(this,e,Yf,Bf,Z,{theme:0,target:3,mount:1})}get mount(){return this.$$.ctx[1]}};function Gf(l){let e,t=`<style>
      @font-face {
      font-family: 'Roboto';
      font-style: normal;
      font-weight: 400;
      src: local(''),
          url('https://cdn.webix.com/fonts/roboto/regular.woff2') format('woff2'),
          url('https://cdn.webix.com/fonts/roboto/regular.woff') format('woff');
      }
      @font-face {
      font-family: 'Roboto';
      font-style: normal;
      font-weight: 500;
      src: local(''),
          url('https://cdn.webix.com/fonts/roboto/500.woff2') format('woff2'),
          url('https://cdn.webix.com/fonts/roboto/500.woff') format('woff');
      }
      </style>`,n;return{c(){e=new Zt(!1),n=se(),e.a=n},m(i,s){e.m(t,i,s),v(i,n,s)},p:K,i:K,o:K,d(i){i&&(k(n),e.d())}}}class Jf extends ee{constructor(e){super(),x(this,e,null,Gf,Z,{})}}function Qf(l){let e,t;const n=l[3].default,i=be(n,l,l[2],null);return{c(){e=I("div"),i&&i.c(),g(e,"class","wx-material-theme"),ce(e,"height","100%")},m(s,r){v(s,e,r),i&&i.m(e,null),t=!0},p(s,r){i&&i.p&&(!t||r&4)&&we(i,n,s,s[2],t?pe(n,s[2],r,null):ke(s[2]),null)},i(s){t||(b(i,s),t=!0)},o(s){p(i,s),t=!1},d(s){s&&k(e),i&&i.d(s)}}}function Ii(l){let e,t,n,i,s,r;return n=new Jf({}),{c(){e=I("link"),t=H(),P(n.$$.fragment),i=H(),s=I("link"),g(e,"rel","preconnect"),g(e,"href","https://cdn.webix.com"),g(e,"crossorigin",""),g(s,"rel","stylesheet"),g(s,"href","https://webix.io/dev/fonts/wxi/wx-icons.css")},m(a,o){v(a,e,o),v(a,t,o),R(n,a,o),v(a,i,o),v(a,s,o),r=!0},i(a){r||(b(n.$$.fragment,a),r=!0)},o(a){p(n.$$.fragment,a),r=!1},d(a){a&&(k(e),k(t),k(i),k(s)),A(n,a)}}}function Xf(l){let e,t,n,i=l[1]&&l[1].default&&Qf(l),s=l[0]&&Ii();return{c(){i&&i.c(),e=H(),s&&s.c(),t=se()},m(r,a){i&&i.m(r,a),v(r,e,a),s&&s.m(document.head,null),M(document.head,t),n=!0},p(r,[a]){r[1]&&r[1].default&&i.p(r,a),r[0]?s?a&1&&b(s,1):(s=Ii(),s.c(),b(s,1),s.m(t.parentNode,t)):s&&(W(),p(s,1,1,()=>{s=null}),Y())},i(r){n||(b(i),b(s),n=!0)},o(r){p(i),p(s),n=!1},d(r){r&&k(e),i&&i.d(r),s&&s.d(r),k(t)}}}function Zf(l,e,t){let{$$slots:n={},$$scope:i}=e,{fonts:s=!0}=e;const r=e.$$slots;return _t("wx-theme","material"),l.$$set=a=>{t(4,e=Ce(Ce({},e),We(a))),"fonts"in a&&t(0,s=a.fonts),"$$scope"in a&&t(2,i=a.$$scope)},e=We(e),[s,r,i,n]}class Mi extends ee{constructor(e){super(),x(this,e,Zf,Xf,Z,{fonts:0})}}function $f(l){let e,t=`<style>
      @font-face {
      font-family: 'Open Sans';
      font-style: normal;
      font-weight: 500;
      src: local(''),
            url('https://cdn.webix.com/fonts/open-sans/500.woff2') format('woff2'),
            url('https://cdn.webix.com/fonts/open-sans/500.woff') format('woff');
      }
      @font-face {
      font-family: 'Open Sans';
      font-style: normal;
      font-weight: 400;
      src: local(''),
            url('https://cdn.webix.com/fonts/open-sans/regular.woff2') format('woff2'),
            url('https://cdn.webix.com/fonts/open-sans/regular.woff') format('woff');
      }
      @font-face {
      font-family: 'Open Sans';
      font-style: normal;
      font-weight: 600;
      src: local(''),
            url('https://cdn.webix.com/fonts/open-sans/600.woff2') format('woff2'),
            url('https://cdn.webix.com/fonts/open-sans/600.woff') format('woff');
      }
      @font-face {
      font-family: 'Open Sans';
      font-style: normal;
      font-weight: 700;
      src: local(''),
            url('https://cdn.webix.com/fonts/open-sans/700.woff2') format('woff2'),
            url('https://cdn.webix.com/fonts/open-sans/700.woff') format('woff');
      }
        </style>`,n;return{c(){e=new Zt(!1),n=se(),e.a=n},m(i,s){e.m(t,i,s),v(i,n,s)},p:K,i:K,o:K,d(i){i&&(k(n),e.d())}}}class Ti extends ee{constructor(e){super(),x(this,e,null,$f,Z,{})}}function xf(l){let e,t;const n=l[3].default,i=be(n,l,l[2],null);return{c(){e=I("div"),i&&i.c(),g(e,"class","wx-willow-theme"),ce(e,"height","100%")},m(s,r){v(s,e,r),i&&i.m(e,null),t=!0},p(s,r){i&&i.p&&(!t||r&4)&&we(i,n,s,s[2],t?pe(n,s[2],r,null):ke(s[2]),null)},i(s){t||(b(i,s),t=!0)},o(s){p(i,s),t=!1},d(s){s&&k(e),i&&i.d(s)}}}function Di(l){let e,t,n,i,s,r;return n=new Ti({}),{c(){e=I("link"),t=H(),P(n.$$.fragment),i=H(),s=I("link"),g(e,"rel","preconnect"),g(e,"href","https://cdn.webix.com"),g(e,"crossorigin",""),g(s,"rel","stylesheet"),g(s,"href","https://webix.io/dev/fonts/wxi/wx-icons.css")},m(a,o){v(a,e,o),v(a,t,o),R(n,a,o),v(a,i,o),v(a,s,o),r=!0},i(a){r||(b(n.$$.fragment,a),r=!0)},o(a){p(n.$$.fragment,a),r=!1},d(a){a&&(k(e),k(t),k(i),k(s)),A(n,a)}}}function ec(l){let e,t,n,i=l[1]&&l[1].default&&xf(l),s=l[0]&&Di();return{c(){i&&i.c(),e=H(),s&&s.c(),t=se()},m(r,a){i&&i.m(r,a),v(r,e,a),s&&s.m(document.head,null),M(document.head,t),n=!0},p(r,[a]){r[1]&&r[1].default&&i.p(r,a),r[0]?s?a&1&&b(s,1):(s=Di(),s.c(),b(s,1),s.m(t.parentNode,t)):s&&(W(),p(s,1,1,()=>{s=null}),Y())},i(r){n||(b(i),b(s),n=!0)},o(r){p(i),p(s),n=!1},d(r){r&&k(e),i&&i.d(r),s&&s.d(r),k(t)}}}function tc(l,e,t){let{$$slots:n={},$$scope:i}=e,{fonts:s=!0}=e;const r=e.$$slots;return _t("wx-theme","willow"),l.$$set=a=>{t(4,e=Ce(Ce({},e),We(a))),"fonts"in a&&t(0,s=a.fonts),"$$scope"in a&&t(2,i=a.$$scope)},e=We(e),[s,r,i,n]}class Ei extends ee{constructor(e){super(),x(this,e,tc,ec,Z,{fonts:0})}}function nc(l){let e,t;const n=l[3].default,i=be(n,l,l[2],null);return{c(){e=I("div"),i&&i.c(),g(e,"class","wx-willow-dark-theme"),ce(e,"height","100%")},m(s,r){v(s,e,r),i&&i.m(e,null),t=!0},p(s,r){i&&i.p&&(!t||r&4)&&we(i,n,s,s[2],t?pe(n,s[2],r,null):ke(s[2]),null)},i(s){t||(b(i,s),t=!0)},o(s){p(i,s),t=!1},d(s){s&&k(e),i&&i.d(s)}}}function Li(l){let e,t,n,i,s,r;return n=new Ti({}),{c(){e=I("link"),t=H(),P(n.$$.fragment),i=H(),s=I("link"),g(e,"rel","preconnect"),g(e,"href","https://cdn.webix.com"),g(e,"crossorigin",""),g(s,"rel","stylesheet"),g(s,"href","https://webix.io/dev/fonts/wxi/wx-icons.css")},m(a,o){v(a,e,o),v(a,t,o),R(n,a,o),v(a,i,o),v(a,s,o),r=!0},i(a){r||(b(n.$$.fragment,a),r=!0)},o(a){p(n.$$.fragment,a),r=!1},d(a){a&&(k(e),k(t),k(i),k(s)),A(n,a)}}}function lc(l){let e,t,n,i=l[1]&&l[1].default&&nc(l),s=l[0]&&Li();return{c(){i&&i.c(),e=H(),s&&s.c(),t=se()},m(r,a){i&&i.m(r,a),v(r,e,a),s&&s.m(document.head,null),M(document.head,t),n=!0},p(r,[a]){r[1]&&r[1].default&&i.p(r,a),r[0]?s?a&1&&b(s,1):(s=Li(),s.c(),b(s,1),s.m(t.parentNode,t)):s&&(W(),p(s,1,1,()=>{s=null}),Y())},i(r){n||(b(i),b(s),n=!0)},o(r){p(i),p(s),n=!1},d(r){r&&k(e),i&&i.d(r),s&&s.d(r),k(t)}}}function ic(l,e,t){let{$$slots:n={},$$scope:i}=e,{fonts:s=!0}=e;const r=e.$$slots;return _t("wx-theme","willow-dark"),l.$$set=a=>{t(4,e=Ce(Ce({},e),We(a))),"fonts"in a&&t(0,s=a.fonts),"$$scope"in a&&t(2,i=a.$$scope)},e=We(e),[s,r,i,n]}class Ri extends ee{constructor(e){super(),x(this,e,ic,lc,Z,{fonts:0})}}const sc={core:{ok:"OK",cancel:"Cancel"},calendar:{monthFull:["January","February","March","April","May","June","July","August","September","October","November","December"],monthShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayFull:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],hours:"Hours",minutes:"Minutes",done:"Done",clear:"Clear",today:"Today",am:["am","AM"],pm:["pm","PM"],weekStart:7,clockFormat:24},formats:{timeFormat:"%H:%i",dateFormat:"%m/%d/%Y"}};function rc(l){let e;const t=l[3].default,n=be(t,l,l[2],null);return{c(){n&&n.c()},m(i,s){n&&n.m(i,s),e=!0},p(i,[s]){n&&n.p&&(!e||s&4)&&we(n,t,i,i[2],e?pe(t,i[2],s,null):ke(i[2]),null)},i(i){e||(b(n,i),e=!0)},o(i){p(n,i),e=!1},d(i){n&&n.d(i)}}}function oc(l,e,t){let{$$slots:n={},$$scope:i}=e,{words:s=null}=e,{optional:r=!1}=e,a=ve("wx-i18n");return a||(a=Tl(sc)),a=a.extend(s,r),_t("wx-i18n",a),l.$$set=o=>{"words"in o&&t(0,s=o.words),"optional"in o&&t(1,r=o.optional),"$$scope"in o&&t(2,i=o.$$scope)},[s,r,i,n]}let Ai=class extends ee{constructor(e){super(),x(this,e,oc,rc,Z,{words:0,optional:1})}};const Mt=[];function Pn(l,e=K){let t;const n=new Set;function i(a){if(Z(l,a)&&(l=a,t)){const o=!Mt.length;for(const u of n)u[1](),Mt.push(u,l);if(o){for(let u=0;u<Mt.length;u+=2)Mt[u][0](Mt[u+1]);Mt.length=0}}}function s(a){i(a(l))}function r(a,o=K){const u=[a,o];return n.add(u),n.size===1&&(t=e(i,s)||K),a(l),()=>{n.delete(u),n.size===0&&t&&(t(),t=null)}}return{set:i,update:s,subscribe:r}}let Pi=new Date().valueOf();const Nn=()=>Pi++;function Tt(){return"temp://"+Pi++}const Ni=2;class ac{constructor(e){e&&(this._writable=e.writable,this._async=e.async),this._values={},this._state={}}setState(e,t=0){const n={};return this._wrapProperties(e,this._state,this._values,"",n,t),n}getState(){return this._values}getReactive(){return this._state}_wrapProperties(e,t,n,i,s,r){for(const a in e){const o=t[a],u=n[a],f=e[a];if(o&&(u===f&&typeof f!="object"||f instanceof Date&&u instanceof Date&&u.getTime()===f.getTime()))continue;const c=i+(i?".":"")+a;o?(o.__parse(f,c,s,r)&&(n[a]=f),r&Ni?s[c]=o.__trigger:o.__trigger()):(f&&f.__reactive?t[a]=this._wrapNested(f,f,c,s):t[a]=this._wrapWritable(f),n[a]=f),s[c]=s[c]||null}}_wrapNested(e,t,n,i){const s=this._wrapWritable(e);return this._wrapProperties(e,s,t,n,i,0),s.__parse=(r,a,o,u)=>(this._wrapProperties(r,s,t,a,o,u),!1),s}_wrapWritable(e){const t=[],n=function(){for(let i=0;i<t.length;i++)t[i](e)};return{subscribe:i=>(t.push(i),this._async?setTimeout(i,1,e):i(e),()=>{const s=t.indexOf(i);s>=0&&t.splice(s,1)}),__trigger:()=>{t.length&&(this._async?setTimeout(n,1):n())},__parse:function(i){return e=i,!0}}}}class uc{constructor(e,t,n,i){typeof e=="function"?this._setter=e:this._setter=e.setState.bind(e),this._routes=t,this._parsers=n,this._prev={},this._triggers=new Map,this._sources=new Map,this._routes.forEach(s=>{s.in.forEach(r=>{const a=this._triggers.get(r)||[];a.push(s),this._triggers.set(r,a)}),s.out.forEach(r=>{const a=this._sources.get(r)||{};s.in.forEach(o=>a[o]=!0),this._sources.set(r,a)})}),this._routes.forEach(s=>{s.length=Math.max(...s.in.map(r=>zi(r,this._sources,1)))}),this._bus=i}init(e){const t={};for(const n in e)if(this._prev[n]!==e[n]){const i=this._parsers[n];t[n]=i?i(e[n]):e[n]}this._prev=this._prev?{...this._prev,...e}:{...e},this.setState(t),this._bus&&this._bus.exec("init-state",t)}setStateAsync(e){const t=this._setter(e,Ni);return this._async?Object.assign(this._async.signals,t):this._async={signals:t,timer:setTimeout(this._applyState.bind(this),1)},t}_applyState(){const e=this._async;if(e){this._async=null,this._triggerUpdates(e.signals,[]);for(const t in e.signals){const n=e.signals[t];n&&n()}}}setState(e,t=[]){const n=this._setter(e);return this._triggerUpdates(n,t),n}_triggerUpdates(e,t){const n=Object.keys(e),i=!t.length;t=t||[];for(let s=0;s<n.length;s++){const r=n[s],a=this._triggers.get(r);a&&a.forEach(o=>{t.indexOf(o)==-1&&t.push(o)})}i&&this._execNext(t)}_execNext(e){for(;e.length;){e.sort((n,i)=>n.length<i.length?1:-1);const t=e[e.length-1];e.splice(e.length-1),t.exec(e)}}}function zi(l,e,t){const n=e.get(l);if(!n)return t;const i=Object.keys(n).map(s=>zi(s,e,t+1));return Math.max(...i)}class Hi{constructor(){this._nextHandler=null,this._handlers={},this._tag=new WeakMap,this.exec=this.exec.bind(this)}on(e,t,n){let i=this._handlers[e];i?n&&n.intercept?i.unshift(t):i.push(t):i=this._handlers[e]=[t],n&&n.tag&&this._tag.set(t,n.tag)}intercept(e,t,n){this.on(e,t,{...n,intercept:!0})}detach(e){for(const t in this._handlers){const n=this._handlers[t];for(let i=n.length-1;i>=0;i--)this._tag.get(n[i])===e&&n.splice(i,1)}}async exec(e,t){const n=this._handlers[e];if(n)for(let i=0;i<n.length;i++){const s=n[i](t);if(s===!1||s&&s.then&&await s===!1)return}return this._nextHandler&&await this._nextHandler.exec(e,t),t}setNext(e){return this._nextHandler=e}}let Oi;function fc(){if(typeof window>"u")return!0;const l=location.hostname,e=["c3Zhci5kZXY=","cmVhY3Qtd2lkZ2V0cy5jb20=","c3ZlbHRlLXdpZGdldHMuY29t","dnVlLXdpZGdldHMuY29t","YW5ndWxhci13aWRnZXRzLmNvbQ==","ZGh0bWx4LmNvbQ==","ZGh0bWx4Y29kZS5jb20=","d2ViaXhjb2RlLmNvbQ==","d2ViaXguaW8=","cmVwbC5jbw==","Y3NiLmFwcA==","cmVwbGl0LmRldg=="];for(let t=0;t<e.length;t++){const n=window.atob(e[t]);if(n===l||l.endsWith("."+n))return!0}return!1}Oi=fc();function Fi(){return Oi}function qi(){return new Date().valueOf()>Math.imul(0x00682D76,1)*256e3}function cc(l){Fi()||setTimeout(function(){if(typeof window<"u"&&qi()){const e=window.atob("IFlvdXIgdHJpYWwgaGFzIGV4cGlyZWQuIFBsZWFzZSBwdXJjaGFzZSB0aGUgY29tbWVyY2lhbCBsaWNlbnNlIGZvciB0aGUgS2FuYmFuIHdpZGdldCBhdCBodHRwczovL2RodG1seC5jb20="),{columns:t}=l.getState();t.forEach(n=>n.label+=e),l.setState({columns:t})}},36e3)}const dc=[{id:1,color:"#FE6158",label:"High"},{id:2,color:"#F1B941",label:"Medium"},{id:3,color:"#77D257",label:"Low"}],mc=["#33B0B4","#0096FA","#F1B941"],jt={label:{show:!0},description:{show:!1},progress:{show:!1},start_date:{show:!1},end_date:{show:!1},users:{show:!1},priority:{show:!1,values:dc},color:{show:!1,values:mc},cover:{show:!1},attached:{show:!1},menu:{show:!0}},zn=[{key:"label",type:"text",label:"Label"},{key:"description",type:"textarea",label:"Description"},{type:"combo",label:"Priority",key:"priority",config:{clearButton:!0}},{type:"color",label:"Color",key:"color"},{type:"progress",key:"progress",label:"Progress"},{type:"date",key:"start_date",label:"Start date"},{type:"date",key:"end_date",label:"End date"},{type:"multiselect",key:"users",label:"Users"}],Dt={debounce:100,autoSave:!0,placement:"sidebar"},Xe={kanban:"wx-kanban",toolbar:"wx-kanban-toolbar",editor:"wx-kanban-editor",content:"wx-kanban-content",scrollableContent:"wx-kanban-scrollable-content",search:"wx-kanban-search",vote:"wx-vote-card-button"},_c=()=>[{by:"label",dir:"asc",text:"Label (a-z)",id:7},{by:"label",dir:"desc",text:"Label (z-a)",id:8},{by:"description",dir:"asc",text:"Description (a-z)",id:9},{by:"description",dir:"desc",text:"Description (z-a)",id:10}],ji=({store:l})=>{const{readonly:e}=l.getState(),t=[{id:"duplicate-card",icon:"wxi-content-copy",text:"Duplicate"},{id:"delete-card",icon:"wxi-delete-outline",text:"Delete"}];return!e?.select&&e?.edit?[{id:"set-edit",icon:"wxi-edit-outline",text:"Edit"},...t]:t},Ui=({columns:l,columnIndex:e})=>[{id:"add-card",icon:"wxi-plus",text:"Add new card"},{id:"set-edit",icon:"wxi-edit-outline",text:"Rename"},{id:"move-column:left",icon:"wxi-arrow-left",text:"Move left",disabled:e<=0},{id:"move-column:right",icon:"wxi-arrow-right",text:"Move right",disabled:e>=l.length-1},{id:"delete-column",icon:"wxi-delete-outline",text:"Delete"}],Vi=({rows:l,rowIndex:e})=>[{id:"set-edit",icon:"wxi-edit-outline",text:"Rename"},{id:"move-row:up",icon:"wxi-arrow-up",text:"Move up",disabled:e<=0},{id:"move-row:down",icon:"wxi-arrow-down",text:"Move down",disabled:e>=l.length-1},{id:"delete-row",icon:"wxi-delete-outline",text:"Delete"}];function le(l,e){return!l||!e?!1:l==e}function ht(l,e){return!!l?.find(t=>le(t,e))}function hc(l){return Object.keys(l.cardsMap).reduce((e,t)=>e.concat(l.cardsMap[t]),[])}function ze(l,e){return`${l}`+(e?`:${e}`:"")}function Ki(l){return l.split(/(?<!temp):(?!\/\/)/)}function rn(l,e,t){return t?l[e]+":"+l[t]:l[e]}function gc(l,e,t={shift:20}){let n=null;function i(){if(e){const s=e.getBoundingClientRect(),r={x:e.scrollLeft,y:e.scrollTop},{shift:a}=t;l.clientX>s.width+s.left-a&&e.scrollTo(r.x+a,r.y),l.clientX<s.left+a&&e.scrollTo(r.x-a,r.y),l.clientY>s.height+s.top-a&&e.scrollTo(r.x,r.y+a),l.clientY<s.top+a&&e.scrollTo(r.x,r.y-a),n=setTimeout(()=>{i()},100)}}return i(),()=>{n&&clearTimeout(n)}}function Hn(l){const{shape:e,defaultMenuItems:t,store:n}=l,i=e||{};if(i.menu===!1)return{menu:{show:!1,items:()=>!1}};if(i.menu||={},i.menu===!0&&(i.menu={show:!0}),typeof i.menu=="object"){if(Array.isArray(i.menu.items)){const r=[...i.menu.items];i.menu.items=()=>r}const s=i.menu.items||t;i.menu.items=r=>{let a=s({...r,store:n});return a&&(a=Bi(a)),a},i.menu.show??=!0}return i}function Bi(l){return l.map(e=>{const t={...e};return e.items&&(t.data=e.items),t.data&&(t.data=Bi(t.data)),e.label&&(t.text=e.label),t})}function Ut(l,e){const{cards:t,columnKey:n,sort:i}=l;if(!i)return t;const s=t.reduce((a,o)=>(a[o[n]]=a[o[n]]||[],a[o[n]].push(o),a),{}),r=(a,o)=>`${typeof o=="function"?o(a):a[o]}`;return Object.keys(s).forEach(a=>{let o;"columns"in i?o=i.columns[a]||{}:o=i,o.by&&(e=e||(u=>{const{dir:f}=u,c=u.by;return(d,m)=>{const _=r(d,c),h=r(m,c);return f==="desc"?h.localeCompare(_,void 0,{numeric:!0}):_.localeCompare(h,void 0,{numeric:!0})}}),s[a].sort(e(o)))}),Object.values(s).flat()}function bc(l,e){return(l||zn.filter(t=>e[t.key]?.show)).map(t=>{const n=e[t.key];return n&&typeof t.key=="string"&&(n.values&&!t.values&&(t.values=n.values),t.config&&(n.config=t.config)),(t.type==="comments"||t.key==="users"&&(t.type==="multiselect"||t.type==="combo"))&&t.values&&t.values.forEach(Wi),t.id=t.id||Nn(),t})}let pc=0;function wc(l){return l.users?.values&&l.users.values.forEach(Wi),l}function Wi(l){if(!l.id)throw"Please provide user IDs";return!l.avatar&&!l.avatarColor&&(l.avatarColor=["#00D19A","#2F77E3","#FFC975"][Math.floor(pc++%3)]),l}function Yi(l,e){const{id:t,before:n,columnId:i,rowId:s}=e,r=l.getState(),{areasMeta:a,cards:o,cardsMap:u,columns:f,columnKey:c,rowKey:d,sort:m}=r,_=o.findIndex(L=>le(L.id,t));if(_<0||!u[ze(i,s)]||le(t,n))return;const h=o[_];if(typeof f.find(L=>le(L.id,i)).limit=="object"){const L=ze(i,s),N=L===rn(h,c,d);if(a[L].noFreeSpace&&!N)return}else{const L=le(i,h[c]);if(a[i].noFreeSpace&&!L)return}const w=o.splice(_,1)[0],y={...w},S=u[rn(y,c,d)]||[],C=S.findIndex(L=>le(L.id,t))||0,T=S[C+1]||{};if(w[c]=i,d&&s&&(w[d]=s),!n)o.push(w);else{const L=o.findIndex(N=>le(N.id,n));o.splice(L,0,w)}return l.setState({cards:m?Ut({...r,cards:o},l.sortRule):o}),()=>{const L=y[c],N=d&&y[d];l.in.exec("move-card",{id:t,before:T.id,columnId:L,rowId:N,$meta:{skipHistory:!0}})}}function kc(l,e){const t=e.card||{},n=e.id||t.id||Tt(),i=l.getState(),{columnKey:s,rowKey:r,areasMeta:a,cards:o,columns:u,rows:f,sort:c}=i,d=e.rowId||r&&t[r]||f[0].id;d&&!e.rowId&&(e.rowId=d);const m=e.columnId||t[s]||u[0].id;if(typeof u.find(h=>le(h.id,m)).limit=="object"){if(a[ze(m,d)].noFreeSpace)return!1}else if(a[m].noFreeSpace)return!1;const _={[s]:m,id:n,...t};return r&&(_[r]=d),o.push(_),l.setState({cards:c?Ut({...i,cards:o},l.sortRule):o}),e.before&&Yi(l,{...e,id:n}),e.select!==!1&&l.in.exec("select-card",{id:n}),e.card=_,e.id=n,()=>{l.in.exec("delete-card",{id:n,$meta:{skipHistory:!0}})}}function vc(l,{id:e,card:t,replace:n}){const i=l.getState();let s,r=i.cards.map(a=>le(a.id,e)?(s={...a},n?{id:e,...t}:{...a,...t}):a);return i.sort&&(r=Ut({...i,cards:r},l.sortRule)),l.setState({cards:r}),()=>{l.in.exec("update-card",{id:e,card:s,replace:!0,$meta:{skipHistory:!0}})}}function yc(l,{id:e,card:t,select:n}){const{cards:i,columnKey:s}=l.getState(),r=i.find(a=>le(a.id,e));if(r){const a={...r,id:Tt(),...t||{}};l.in.exec("add-card",{columnId:a[s],before:e,card:a,select:n})}}function Sc(l,{id:e}){const t=l.getState();t.selected?.includes(e)&&l.in.exec("unselect-card",{id:e});const n=t.cards.findIndex(a=>le(a.id,e)),i=t.cards[n],s=t.cards[n+1]?.id,r=t.cards.filter(a=>!le(a.id,e));return l.setState({cards:r}),()=>{l.in.exec("add-card",{columnId:i[t.columnKey],before:s,card:i,$meta:{skipHistory:!0,restore:e}})}}function Gi(l,{id:e,before:t}){const{columns:n}=l.getState(),i=n.findIndex(a=>le(a.id,e)),s=n[i+1]?.id,r=n.splice(i,1)[0];if(t){const a=n.findIndex(o=>le(o.id,t));n.splice(a,0,r)}else n.push(r);return l.setState({columns:n}),()=>{l.in.exec("move-column",{id:e,before:s,$meta:{skipHistory:!0}})}}function Cc(l,e){const t=e.id||e.column?.id||Tt(),n=l.getState().columns,i={id:t,label:"Untitled",...e.column||{}};return n.push(i),l.setState({columns:n}),e.before&&Gi(l,{...e,id:t}),l.in.exec("scroll",{to:"column",id:t}),e.id=t,e.column=i,()=>{l.in.exec("delete-column",{id:t,$meta:{skipHistory:!0}})}}function Ic(l,e){const t=l.getState();let n;const i=e.id||e.column?.id,s=t.columns.map(r=>le(r.id,i)?(n={...r},e.replace?{id:i,...e.column}:{...r,...e.column}):r);return l.setState({columns:s}),()=>{l.in.exec("update-column",{column:n,replace:!0,$meta:{skipHistory:!0}})}}function Mc(l,{id:e}){if(e){const t=l.getState(),n=t.columns.findIndex(a=>le(a.id,e)),i=t.columns[n],s=t.columns[n+1]?.id,r=t.columns.filter(a=>!le(a.id,e));return l.setState({columns:r}),()=>{l.in.exec("add-column",{column:i,before:s,$meta:{skipHistory:!0,restore:e}})}}}function Ji(l,{id:e,before:t}){const{rows:n,rowKey:i}=l.getState();if(!i)return;const s=n.findIndex(o=>le(o.id,e)),r=n[s+1]?.id,a=n.splice(s,1)[0];if(t){const o=n.findIndex(u=>le(u.id,t));n.splice(o,0,a)}else n.push(a);return l.setState({rows:n}),()=>{l.in.exec("move-row",{id:e,before:r,$meta:{skipHistory:!0}})}}function Tc(l,e){const t=l.getState(),n=t.rows,i=e.id||e.row?.id||Tt(),s={id:i,label:"Untitled",collapsed:!1,...e.row||{}};if(n.push(s),!t.rowKey){const r=t.rowKey="rowKey";t.rows[0]={id:"default",label:"Untitled"},t.cards.map(a=>{a[r]="default"})}return l.setState({rows:n,rowKey:t.rowKey}),e.before&&Ji(l,{id:i,before:e.before}),l.in.exec("scroll",{to:"row",id:i}),e.id=i,e.row=s,()=>{l.in.exec("delete-row",{id:i,$meta:{skipHistory:!0}})}}function Dc(l,e){const t=l.getState();let n;const i=e.id||e.row?.id,s=t.rows.map(r=>le(r.id,i)?(n={...r},e.replace?{id:i,...e.row}:{...r,...e.row}):r);return l.setState({rows:s}),()=>{l.in.exec("update-row",{row:n,replace:!0,$meta:{skipHistory:!0}})}}function Ec(l,{id:e}){if(e){const t=l.getState(),{rows:n}=t,i=n.findIndex(a=>le(a.id,e)),s=n[i],r=n[i+1]?.id;return n.splice(i,1),l.setState({rows:n}),()=>{l.in.exec("add-row",{row:s,before:r,$meta:{skipHistory:!0,restore:e}})}}}function Lc(l,{id:e,columnId:t,rowId:n,before:i,source:s,dragItemsCoords:r,dropAreasCoords:a}){const{areasMeta:o,cardsMeta:u,layout:f}=l.getState();a?.forEach(d=>{d.id&&(o[d.id].height=f!=="default:lazy"?d.height:null)}),[...s].forEach(d=>{const m=u[d]||{};m.dragging=!0,u[d]=m});const c=ze(t,n);l.setState({dragItemId:e,dragItemsCoords:r,overAreaId:c,before:i,areasMeta:o,cardsMeta:u})}function Rc(l,e){const{rowId:t,columnId:n,before:i}=e;if(!n)return;const{areasMeta:s,cards:r,columns:a,rowKey:o}=l.getState(),u=r.find(m=>le(m.id,e.source[e.source.length-1])),f=ze(n,t),c=a.find(m=>le(m.id,n));let d;typeof c.limit=="object"?d=!s[f].noFreeSpace||le(f,ze(u.column,u[o])):d=!s[n].noFreeSpace||le(n,u.column),d&&l.setState({overAreaId:d?f:null,before:i})}function Ac(l,{id:e,columnId:t,rowId:n,before:i,source:s}){if(!t)return;const r={dragItemsCoords:null,dragItemId:null,before:null,overAreaId:null,areasMeta:{}},{areasMeta:a,cardsMeta:o}=l.getState(),u=ze(t,n);if(u&&e){const c=a[u],{columnId:d,rowId:m}=c;if(s.length>1){const _=Nn();s.forEach(h=>{l.in.exec("move-card",{id:h,columnId:d,rowId:m,before:i,$meta:{batch:_}});const w=o[h];w&&(w.dragging=!1)})}else{l.in.exec("move-card",{id:e,columnId:d,rowId:m,before:i});const _=o[e];_&&(_.dragging=!1)}}r.cardsMeta=o;const f=l.getState().areasMeta;Object.keys(f).forEach(c=>{r.areasMeta[c]={...f[c],height:null}}),l.setState(r)}function Pc(l,{id:e,groupMode:t,eventSource:n}){const{selected:i,search:s}=l.getState();if(e){let r=null;if(t)if(r=i?[...i]:[],r.includes(e)){l.in.exec("unselect-card",{id:e});return}else r.push(e);else r=[e];s&&l.in.exec("set-search",{value:null}),l.setState({selected:r}),r.length>1||n==="dnd"?l.in.exec("set-edit",null):l.in.exec("set-edit",{cardId:e,eventSource:"select-card"})}}function Nc(l,{id:e}){const t=l.getState().selected;if(t){if(l.in.exec("set-edit",null),!e){l.setState({selected:null});return}const n=t.filter(i=>!le(i,e));l.setState({selected:n})}}function On(l,e){return`${l}`.toLowerCase().includes(`${e}`.toLowerCase())}function zc(l,e,t){return t?On(l[t]||"",e):On(l.label||"",e)||On(l.description||"",e)}function Hc(l,{value:e,by:t,searchRule:n}){const i=l.getState(),s=e?.trim(),r=i.cardsMeta;let a={value:e,by:t};s?hc(i).map(o=>{const u=r[o.id]=r[o.id]||{};(n||zc)(o,s,t)?(u.found=!0,u.dimmed=!1):(u.found=!1,u.dimmed=!0)}):(Object.keys(r).forEach(o=>{const u=r[o];u&&(delete u.dimmed,delete u.found)}),a=null),l.setState({cardsMeta:r,search:a})}function Oc(l,e){l.setState({scroll:e})}function Fc(l,e){if(!e){l.setState({sort:null});return}const t=l.getState(),n=e.columnId,i=e.by||"label",s=e.dir||"asc",r=e.preserve||!1;let a=t.sort||{};n?("column"in a||(a={columns:{}}),a.columns[n]={by:i,dir:s,preserve:r}):a={dir:s,by:i,preserve:r};const o=Ut({...t,sort:a},l.sortRule);r?l.setState({sort:a,cards:o}):l.setState({cards:o})}function qc(l,e){l.setState({edit:e})}function jc(l,{id:e,cardId:t,comment:n}){if(t){const i=l.getState(),{currentUser:s}=i,r=e||n.id||Tt(),a=t||n.cardId;if(!a||!s)return;const o=i.cards.map(u=>le(u.id,a)?{...u,comments:[...u.comments||[],{...n,id:r,cardId:a,userId:s,date:n.date||new Date}]}:u);return l.setState({cards:o}),()=>{l.in.exec("delete-comment",{id:r,cardId:a,$meta:{skipHistory:!0}})}}}function Uc(l,{cardId:e,id:t,comment:n}){if(e){const i=l.getState(),s=t||n.id,r=e||n.cardId;if(!s||!r)return;let a={};const o=i.cards.map(u=>le(u.id,r)?{...u,comments:(u.comments||[]).map(f=>le(f.id,s)?(a={...f},{...f,...n}):f)}:u);return l.setState({cards:o}),()=>{l.in.exec("update-comment",{id:s,cardId:r,comment:a,$meta:{skipHistory:!0}})}}}function Vc(l,{cardId:e,id:t}){if(e){const n=l.getState();if(!t||!e)return;let i={};const s=n.cards.map(r=>le(r.id,e)?{...r,comments:(r.comments||[]).filter(a=>le(a.id,t)?(i={...a},!1):!0)}:r);return l.setState({cards:s}),()=>{l.in.exec("add-comment",{id:t,cardId:e,comment:i,$meta:{skipHistory:!0}})}}}function Kc(l,e){const t=l.getState().links,n=e.id||e.link.id||Tt();if(t.find(s=>le(n,s.id)))return;const i={...e.link,id:n};return t.push(i),l.setState({links:t}),e.link=i,e.id=n,()=>{l.in.exec("delete-link",{id:n,$meta:{skipHistory:!0}})}}function Bc(l,{id:e}){if(e){const t=l.getState(),n=t.links.find(s=>le(s.id,e)),i=t.links.filter(s=>!le(s.id,e));return l.setState({links:i}),()=>{l.in.exec("add-link",{id:e,link:n,$meta:{skipHistory:!0}})}}}function Wc(l,{cardId:e}){const t=l.getState(),{currentUser:n}=t;if(!e||!n)return;const i=t.cards.map(s=>le(s.id,e)?{...s,votes:[...s.votes||[],n]}:s);l.setState({cards:i})}function Yc(l,{cardId:e}){const t=l.getState(),{currentUser:n}=t;if(!e||!n)return;const i=t.cards.map(s=>le(s.id,e)?{...s,votes:(s.votes||[]).filter(r=>!le(r,n))}:s);l.setState({cards:i})}class Gc extends ac{in;out;sortRule;config;_router;constructor(e,t){super(e),cc(this),this.in=new Hi,this.out=new Hi,this.in.setNext(this.out),this.config={history:!0,...t||{}},this._router=new uc(super.setState.bind(this),[{in:["cards","rows","columns","columnKey","rowKey"],out:["areasMeta","cardsMap"],exec:i=>{const s=this.getState(),{rows:r,columns:a,columnKey:o,rowKey:u,cards:f}=s,c={},d={};if(!o)return{cardsMap:d,areasMeta:c};f.map(m=>{const _=rn(m,o,u);d[_]=d[_]||[],d[_]?.push(m)}),a.map(m=>{d[m.id]=d[m.id]||[],u&&r.map(_=>{const h=ze(m.id,_.id);c[h]={columnId:m.id,rowId:_.id,column:m,row:_,cardsCount:0},d[h]=d[h]||[],d[m.id]=d[m.id]?.concat(d[h]||[])}),c[m.id]={columnId:m.id,column:m,cardsCount:0}}),this._computeLimits({areasMeta:c,cardsMap:d}),this.setState({areasMeta:c,cardsMap:d},i)}},{in:["cards","rows","columns","columnKey","rowKey"],out:["areasMeta","cardsMap"],exec:i=>{const{renderType:s,scrollType:r}=this.getState(),a=`${r}:${s}`;this.setState({layout:a},i)}}],{}),this._initStructure();const n={"add-card":kc,"update-card":vc,"move-card":Yi,"duplicate-card":yc,"delete-card":Sc,"add-column":Cc,"update-column":Ic,"move-column":Gi,"delete-column":Mc,"add-row":Tc,"update-row":Dc,"move-row":Ji,"delete-row":Ec,"start-drag-card":Lc,"drag-card":Rc,"end-drag-card":Ac,"set-search":Hc,"select-card":Pc,"unselect-card":Nc,scroll:Oc,"set-sort":Fc,"set-edit":qc,"add-comment":jc,"update-comment":Uc,"delete-comment":Vc,"add-link":Kc,"delete-link":Bc,"add-vote":Wc,"delete-vote":Yc};this._setHandlers(n)}setState(e,t){return this._router.setState(e,t)}init(e){const{cards:t=[],links:n=[],columns:i=[],rows:s,columnKey:r="column",rowKey:a="",sort:o=null,readonly:u=!1,...f}=e,c=this._normalizeReadonlyConfig(u);let d=this._normalizeCards(t);const m=(n||[]).map(L=>({...L}));o&&(d=Ut({columnKey:r,sort:o,cards:d},this.sortRule));const _=i.map(L=>({...L})),h=(a&&s||[{id:""}]).map(L=>({...L})),{cardShape:w,columnShape:y,rowShape:S,editorShape:C}=this._normalizeShapes({...e,cards:d,readonly:c}),T={...f,cards:d,links:m,columns:_,columnKey:r,rowKey:a,rows:h,cardShape:w,columnShape:y,editorShape:C,rowShape:S,readonly:c};this._router.init(T),this.setState({edit:null,selected:null})}undo(){const{history:e}=this.getState(),t=e.undo.pop();if(t){if(typeof t=="object")t.undo(),e.redo.push({ev:t.ev,key:t.key});else if(typeof t=="number"){const n=e.batches[t];for(let i=n.length-1;i>=0;i--)n[i].undo();e.redo.push(t)}}this.setState({history:e})}redo(){const{history:e}=this.getState(),t=[...e.redo],n=t.pop();if(n){if(typeof n=="object"){const{ev:i,key:s}=n;this.in.exec(s,i),this.setState({history:{...e,redo:t}})}else if(typeof n=="number"){const i=e.batches[n];delete e.batches[n],i.forEach(s=>{const{ev:r,key:a}=s;this.in.exec(a,r)}),this.setState({history:{...e,redo:t}})}}}_setHandlers(e){const{history:t}=this.getState();Object.keys(e).forEach(n=>{this.in.on(n,i=>{const s=e[n](this,i),r=i?.$meta;this.config.history&&s&&!r?.skipHistory&&(r?.batch?(t.batches[r.batch]??=[],t.batches[r.batch].push({undo:s,key:n,ev:i}),t.undo.includes(r.batch)||t.undo.push(r.batch)):t.undo.push({undo:s,key:n,ev:i}),t.redo.forEach(a=>{typeof a=="string"&&delete t.batches[a]}),t.redo=[],this.setState({history:t}))})})}_initStructure(){const e="default",t="default";this.setState({columnKey:"column",rowKey:"",columns:[],rows:[],cards:[],cardsMap:{},cardsMeta:{},cardShape:jt,columnShape:{},rowShape:{},editorShape:zn,areasMeta:{},dragItemsCoords:null,dragItemId:null,before:null,overAreaId:null,selected:null,search:null,scroll:null,sort:null,edit:null,readonly:null,cardHeight:null,layout:`${e}:${t}`,scrollType:e,renderType:t,history:{undo:[],redo:[],batches:{}},editor:{},currentUser:null,links:[]})}_computeLimits({areasMeta:e,cardsMap:t}){for(const n in e){const i=t[n];if(i){const s=e[n];s.cardsCount=i.length||0;const r=s.column;if(r.limit){let a=0;typeof r.limit=="object"?s.rowId?a=r.limit[s.rowId]||0:a=Object.keys(r.limit).reduce((o,u)=>o+r.limit[u],0):a=r.limit,s.totalLimit=a,s.isOverLimit=!!a&&s.cardsCount>a,s.noFreeSpace=r.strictLimit&&!!a&&s.cardsCount>=a}}}}_normalizeCards(e){return e.map(t=>{const n=t.id||Nn();return{...t,id:n}})}_normalizeShapes(e){const{cardShape:t=jt,columnShape:n,rowShape:i,readonly:s,editorShape:r}=e;let a={...t};for(const c in t){const d=t[c];typeof d=="boolean"&&(a[c]={show:d})}a=Object.keys(a).reduce((c,d)=>{const m=jt[d];return m?c[d]={...m,...a[d]}:c[d]=a[d],c},{}),s&&(s.edit||(a.menu=a.menu||{},a.menu.show=!1)),a=wc(a),a=Hn({store:this,shape:a,defaultMenuItems:ji});const o=bc(r,a),u=Hn({store:this,shape:n,defaultMenuItems:Ui}),f=Hn({store:this,shape:i,defaultMenuItems:Vi});return{cardShape:a,columnShape:u,rowShape:f,editorShape:o}}_normalizeReadonlyConfig(e){let t={add:!0,dnd:!0,edit:!0,select:!0};return typeof e=="object"?t={...t,...e}:e===!0&&Object.keys(t).forEach(n=>{t[n]=!1}),t}}function Jc(l,e,t=5){return Math.abs(e.x-l.x)>t||Math.abs(e.y-l.y)>t}function Fn(l,e){return l>=e[0]&&l<=e[1]}function Qc(l,e){const{x:t,y:n}=l,i=Fn(t,[e.x,e.right]),s=Fn(n,[e.y,e.bottom]);return i&&s}function Qi(l,e,t){const n={x:e.x-t.x,y:e.y-t.y};return{x:l.x-n.x,y:l.y-n.y}}function Xi(l,e,t=!1){const n=Array.from(l.querySelectorAll("[data-drop-area]")),i=Array.isArray(e)?e:[e],s=l.querySelector(`[data-drag-item='${i[i.length-1]}']`)?.offsetHeight||300,r={},a=[],o=n.reduce((u,f)=>{const c=JSON.parse(JSON.stringify(f.getBoundingClientRect())),d=f.getAttribute("data-drop-area"),m=Array.from(f.querySelectorAll("[data-drag-item]")),_=[],h=m.reduce((C,T)=>{const L=JSON.parse(JSON.stringify(T.getBoundingClientRect())),N=T.getAttribute("data-drag-item"),E=C[C.length-1]?.bottom??L.y,F={...L,y:E,id:N};return r[N]=F,C.push(F),ht(i,N)||_.push(N),C},[]),w=_.map((C,T)=>({...h[T],id:C}));if(!t){const C=f.offsetParent,T=30;f.offsetTop+f.offsetHeight+T>=C.scrollHeight&&(c.bottom+=s+T,c.height+=s+T)}const y={...c,id:d},S=f.querySelector(".wx-list-wrapper");return S&&(y.scrollList={node:S,initialScrollY:S.scrollTop}),a.push(y),u[d]=w,u},{});return{dragItemsCoords:r,dropAreasCoords:a,dropAreaItemsCoords:o}}function Zi(l){const e={};if(e.target=l.target,"touches"in l){const t=l.touches[0];e.touches=l.touches,e.clientX=t.clientX,e.clientY=t.clientY}else e.clientX=l.clientX,e.clientY=l.clientY;return e}function qn(l,e="data-id"){let t=l;for(!t.tagName&&l.target&&(t=l.target);t;){if(t.getAttribute&&t.getAttribute(e))return t;t=t.parentNode}return null}function jn(l,e="data-id"){const t=qn(l,e);return t?t.getAttribute(e):null}function Vt(l,e="data-id"){const t=qn(l,e);return t?Xc(t.getAttribute(e)):null}function Xc(l){if(typeof l=="string"){const e=l*1;if(!isNaN(e))return e}return l}new Date().valueOf();function Zc(l,e){if(e.readonly)return;let t,n;const i=l;let s,r,a,o,u,f,c,d,m,_;const h=(z,q)=>{e.api.exec(z,q),e.onAction&&e.onAction(z,q)};l.querySelector(`[data-kanban-id='${Xe.scrollableContent}']`)?.addEventListener("scroll",()=>{if(o){const{itemId:z,itemRect:q,itemsId:J}=o;o.scroll={x:s.scrollLeft,y:s.scrollTop};const V=Xi(i,J,!0);u=V.dragItemsCoords,c=V.dropAreasCoords,f=V.dropAreaItemsCoords,u[z]=q}},{capture:!0});const{data:w}=e.api.getStores(),y={duration:500,timer:null},S=()=>{y.callback&&(y.timer=setTimeout(y.callback,y.duration))},C=()=>{y.timer&&clearTimeout(y.timer)};function T(z){if(r&&clearTimeout(r),s){const q=s.getBoundingClientRect(),J={x:s.scrollLeft,y:s.scrollTop},V=50;z.clientX>q.width+q.left-V&&s.scrollTo(J.x+V,J.y),z.clientX<q.left+V&&s.scrollTo(J.x-V,J.y),z.clientY>q.height+q.top-V&&s.scrollTo(J.x,J.y+V),z.clientY<q.top+V&&s.scrollTo(J.x,J.y-V),r=setTimeout(()=>{T(z)},100)}}function L(z){const q={},J=c.find(ne=>Qc(z,ne)),V=J?.id;if(V){const[ne,Q]=Ki(V);q.overAreaId={rowId:Q,columnId:ne};let B=z.y;J.scrollList&&(B+=J.scrollList.node.scrollTop-J.scrollList.initialScrollY);const ie=f[V];q.before=ie.find(ge=>Fn(B,[ge.y,ge.bottom]))?.id}return q}function N(z,q,J,V,ne){if(z.touches&&z.touches.length>1)return;const Q=V.itemId;ne?.indexOf(Q)===-1&&(h("select-card",{id:Q,eventSource:"dnd"}),ne=[Q]),a=Qi(q,V,u[Q]),F(ne);const B=L(J);m=B.before,d=B.overAreaId,_=ne||[Q],h("start-drag-card",{id:Q,rowId:d.rowId,columnId:d.columnId,before:m,source:_,dragItemsCoords:u,dropAreasCoords:c})}function E(z,q,J,V){const{dragItemId:ne}=z,Q=u[V.itemId];a=Qi(q,V,Q),t.style.left=a.x+"px",t.style.top=a.y+"px";const B=L(J),ie={id:ne,rowId:d.rowId,columnId:d.columnId,before:m,source:_};B.overAreaId?.columnId&&(ie.rowId!==B.overAreaId?.rowId||ie.columnId!==B.overAreaId?.columnId)&&(d=B.overAreaId,ie.rowId=d.rowId,ie.columnId=d.columnId),m!==B.before&&(m=ie.before=B.before),h("drag-card",ie)}function F(z){const q=document.querySelector(".wx-kanban");z&&z.length>1&&q.style.setProperty("--wx-kanban-dragged-cards-count",JSON.stringify(`${z.length}`)),q.appendChild(t),t.classList.add("wx-dragged-card"),t.style.left=a.x+"px",t.style.top=a.y+"px",document.body.classList.add("wx-ondrag")}function G(){const z=document.querySelector(".wx-kanban");t.parentNode&&z.removeChild(t),document.body.classList.remove("wx-ondrag"),z.style.removeProperty("--wx-kanban-dragged-cards-count"),t=null}function U(z,q,J){const V=J.scroll,ne={x:q.scrollLeft,y:q.scrollTop};return{x:z.clientX+(ne.x-V.x),y:z.clientY+(ne.y-V.y)}}function O(z){const q=Zi(z);if(q.touches&&q.touches.length>1||"button"in z&&z.button!==0)return;const J=qn(q.target,"data-drag-item");if(s=i.querySelector(`[data-kanban-id="${Xe.content}"]`),J){const V=Vt(J,"data-drag-item"),ne=Vt(q.target,"data-drag-item"),Q=w.getState().selected,B=Q&&Q.length>1?[...Q,V]:[V],ie=Xi(i,B);u=ie.dragItemsCoords,c=ie.dropAreasCoords,f=ie.dropAreaItemsCoords,t=J.cloneNode(!0),n="touches"in z?{up:"touchend",move:"touchmove"}:{up:"mouseup",move:"mousemove"},"touches"in z?(y.callback=()=>{document.addEventListener(n.move,D,{passive:!1})},S()):document.addEventListener(n.move,D),document.addEventListener(n.up,j),o={x:q.clientX,y:q.clientY,itemId:V,itemsId:B,itemRect:u[V],areaId:ne,scroll:{x:s.scrollLeft,y:s.scrollTop}}}}function D(z){z.preventDefault(),z.stopPropagation();const q=Zi(z);if(T(q),!o)return;const J=w.getState(),{selected:V,dragItemId:ne}=J,Q=U(q,s,o),B={x:q.clientX,y:q.clientY};!ne&&Jc(o,B)&&N(q,B,Q,o,V),ne&&E(J,B,Q,o)}function j(){document.removeEventListener(n.move,D),document.removeEventListener(n.up,j),C(),G(),r&&clearTimeout(r);const{dragItemId:z}=w.getState();z&&h("end-drag-card",{id:z,rowId:d.rowId,columnId:d.columnId,before:m,source:_}),o=null}return i.addEventListener("mousedown",O),i.addEventListener("touchstart",O),i.addEventListener("dragstart",z=>z.preventDefault()),{destroy(){i.removeEventListener("mousedown",O),i.removeEventListener("touchstart",O)}}}function $c(l,e){if(e.readonly)return;let t;const{api:n}=e,i=r=>{t=r.target};n.on("select-card",({id:r})=>{t||setTimeout(()=>{n.exec("scroll",{to:"card",id:r})},100)});const s=r=>{if(!t||jn(r.target,"data-ignore-selection"))return;const a=Vt(t,"data-drag-item"),o=Vt(t,"data-kanban-id"),u=r.metaKey||r.ctrlKey,f=r.shiftKey;t===r.target&&o!==Xe.editor&&o!==Xe.vote&&xc({itemId:a,groupMode:u,rangeMode:f,api:n}),t=null};return l.addEventListener("mousedown",i),l.addEventListener("mouseup",s),{destroy(){l.removeEventListener("mousedown",i),l.removeEventListener("mouseup",s)}}}function xc(l){const{itemId:e,groupMode:t,rangeMode:n,api:i}=l,{cardsMap:s,columnKey:r}=i.getState(),{selected:a}=i.getState();if(!e&&a?.length){i.exec("unselect-card",{id:null});return}if(n&&a?.length){const o=i.getCard(e),u=i.getCard(a[a.length-1]);if(ed(o,u,r)){const f=Object.keys(s).filter(h=>td(h)===o[r]).reduce((h,w)=>{const y=s[w];return h.concat(y)},[]),c=f.findIndex(h=>le(h.id,e)),d=f.findIndex(h=>le(h.id,u?.id)),m=Math.min(c,d),_=Math.max(c,d);f.slice(m,_+1).forEach(h=>{a.indexOf(h.id)===-1&&i.exec("select-card",{id:h.id,groupMode:!0})});return}}i.exec("select-card",{id:e,groupMode:t})}function ed(l,e,t){return!l||!e||!t?!1:le(l[t],e[t])}function td(l){return Ki(l)[0]}function nd(l,e){const{api:t,tick:n=()=>new Promise(a=>{requestAnimationFrame(()=>{a()})})}=e,i=t.getReactiveState().scroll,s={card:"data-drag-item",column:"data-column-header",row:"data-row-header"};i?.subscribe(a=>{if(a){const{to:o,id:u,options:f}=a;r(`[${s[o]}="${u}"]`,f).then(c=>{c&&t.exec("scroll",null)}).catch()}});function r(a,o){return new Promise(u=>{n().then(()=>{const f=l.querySelector(a);f&&(f.scrollIntoView(o||{behavior:"smooth",block:"nearest",inline:"nearest"}),u(!0)),u(!1)})})}}function Un(l){let e=!1;function t(a){a.buttons===1&&(e=!0)}function n(){e=!1,i&&i()}let i=null;function s(a){e&&(i&&i(),i=gc(a,l))}function r(){i&&(i(),i=null),e=!1}return document.body.addEventListener("mousemove",s),document.body.addEventListener("mouseup",r),l.addEventListener("mousedown",t),l.addEventListener("mouseover",t),l.addEventListener("mouseleave",n),{destroy:()=>{document.body.removeEventListener("mousemove",s),document.body.removeEventListener("mouseup",r),l.removeEventListener("mousedown",t),l.removeEventListener("mouseover",t),l.removeEventListener("mouseleave",n)}}}function ld(l,e){if(e.readonly)return;const t=e.locale,{api:n}=e,i=e.onAction;let s=e.inFocus||!1,r;function a(c,d){switch(c){case"delete":{const m=n.getState().selected;m?.length&&(e.confirmDeletion?.()||Promise.resolve()).then(()=>{m.forEach(_=>{n.exec("delete-card",{id:_})})}).catch(()=>{});break}case"ctrl+d":d.preventDefault(),n.getState().selected?.forEach(m=>{const _=n.getCard(m);n.exec("duplicate-card",{id:m,card:{label:`${t("Duplicate of")} ${_?.label}`}})});break;case"ctrl+z":d.preventDefault(),n.getStores().data.undo();break;case"ctrl+shift+z":case"ctrl+y":d.preventDefault(),n.getStores().data.redo();break}}function o(c){if(s){const d=c.ctrlKey||c.metaKey,m=c.shiftKey,_=c.code.replace("Key","").toLowerCase(),h=`${d?"ctrl+":""}${m?"shift+":""}${_}`;a(h,c),i&&i("keydown",{hotkey:h})}}function u(c){const d=jn(c.target,"data-wx-widget");s=d===Xe.kanban||d===Xe.toolbar,r=d,i&&i("set-focus",{inFocus:s})}function f(c){const d=jn(c.target,"data-wx-widget");r===d&&!s&&(s=!0,r=Xe.kanban,i&&i("set-focus",{inFocus:s}))}return document.addEventListener("keydown",o),document.addEventListener("mousedown",u),document.addEventListener("focusin",u),document.addEventListener("focusout",f),{destroy:()=>{document.removeEventListener("keydown",o),document.removeEventListener("mousedown",u),document.removeEventListener("focusin",u),document.removeEventListener("focusout",f)}}}function $i(l){switch(l?.toLowerCase()){case"jpg":case"jpeg":case"gif":case"png":case"bmp":case"tiff":case"pcx":case"svg":case"ico":return!0;default:return!1}}class id{_store;constructor(e){this._store=e}json(e){const{cards:t,links:n,columns:i,rows:s}=this._store.getState(),r={cards:t,links:n,columns:i};s.length>0&&(r.rows=s),this._save(r,`${e||"kanban-export"}`,"json")}_save(e,t,n){const i=document.createElement("a");document.body.appendChild(i),i.style.display="none";const s=JSON.stringify(e),r=new Blob([s],{type:"octet/stream"}),a=window.URL.createObjectURL(r);i.href=a,i.download=`${t}.${n}`,i.click(),window.URL.revokeObjectURL(a),document.body.removeChild(i)}}function sd(l,e){return{exec:l.in.exec.bind(l.in),on:l.out.on.bind(l.in),intercept:l.in.intercept.bind(l.in),getState:l.getState.bind(l),getReactiveState:l.getReactive.bind(l),setNext:t=>e=e.setNext(t),getStores:()=>({data:l}),getCard:t=>{const{cards:n}=l.getState();return n.find(i=>i.id==t)},serialize:()=>{const{cards:t,links:n,columns:i,rows:s}=l.getState();return{cards:t,links:n,columns:i,rows:s}},export:new id(l),undo:l.undo.bind(l),redo:l.redo.bind(l),getAreaCards:(t,n)=>{const{cardsMap:i}=l.getState(),s=ze(t,n);return i[s]}}}function rd(l,e){const{container:t,at:n,position:i="top",align:s="start"}=e,r=od(t)||document.body;if(n){l.style.position="absolute";const a=n.getBoundingClientRect();l.style.top=`${a[i]}px`,l.style.left=`${a[s==="start"?"left":"right"]}px`}return r.appendChild(l),{destroy(){l.remove()}}}function od(l){return typeof l=="string"?document.querySelector(l):l}var Le={};function Vn(){return typeof navigator.userAgentData=="object"&&"mobile"in navigator.userAgentData?navigator.userAgentData.mobile:function(l){return/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(l)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(l.slice(0,4))}(navigator.userAgent||navigator.vendor||window.opera)}function xi(){if(Vn()){var l=window.innerWidth,e=window.innerHeight,t=Math.min(l,e),n=Math.max(l,e);return t<=480&&n<=896}else return!1}function Kn(){return Le.AppRunsOnLegacyTouchDevice==null&&(Le.AppRunsOnLegacyTouchDevice=!gt("(pointer:fine)")&&!gt("(pointer:coarse)")&&!gt("-moz-touch-enabled")&&("ontouchstart"in Window||(navigator.maxTouchPoints||0)>0||/touch|android|iphone|ipod|ipad/i.test(navigator.userAgent))),Le.AppRunsOnLegacyTouchDevice}function ad(){return Le.DevicePointingAccuracy==null&&(Kt(),!an()&&!Le.waitingForLoaded&&(Le.waitingForLoaded=!0,window.addEventListener("DOMContentLoaded",Kt))),Le.DeviceCanHover}function on(){return Le.DevicePointingAccuracy==null&&(Kt(),!an()&&!Le.waitingForLoaded&&(Le.waitingForLoaded=!0,window.addEventListener("DOMContentLoaded",Kt))),Le.DevicePointingAccuracy}function Kt(){Le.DeviceCanHover=gt("(hover:hover)");var l="fine";switch(!0){case gt("(pointer:none)"):l="none";break;case gt("(pointer:coarse)"):case gt("-moz-touch-enabled"):case Kn():l="coarse";break}if(Le.DevicePointingAccuracy=l,an()){var e=document.body.classList;if(l==="none"!==e.contains("noPointer")||l==="fine"!==e.contains("finePointer")||l==="coarse"!==e.contains("coarsePointer"))switch(document.body.classList.remove("noPointer","finePointer","coarsePointer"),l){case"none":document.body.classList.add("noPointer");break;case"fine":document.body.classList.add("finePointer");break;case"coarse":document.body.classList.add("coarsePointer");break}}}function ud(l){ts(l,!1)}function fd(l){ts(l,!0)}function cd(l){ns(l)}function an(){return document.readyState==="interactive"||document.readyState==="complete"}function gt(l){var e=window.matchMedia||window.webkitMatchmedia||window.mozMatchmedia||window.oMatchmedia;return e!=null&&e(l).matches}function dd(l,e){return typeof l.item=="function"?l.item(e):l[e]}function md(l,e){for(var t=0,n=l.length;t<n;t++)if(e.test(dd(l,t)))return!0;return!1}function es(){if(!Le.MediaQueriesHaveBeenRewritten&&Kn())if(an()){for(var l=document.styleSheets,e=0,t=l.length;e<t;e++)for(var n=l[e].cssRules||l[e].rules,i=0,s=n.length;i<s;i++){var r=n[i];if(r.type===CSSRule.MEDIA_RULE&&md(r.media,/handheld/i)){var a=r.media;a.mediaText=a.mediaText.replace("handheld","screen")}}for(var o=document.getElementsByTagName("link"),e=0,t=o.length;e<t;e++){var u=o[e];/handheld/i.test(u.media)&&(u.media=u.media.replace("handheld","screen"))}Le.MediaQueriesHaveBeenRewritten=!0}else window.addEventListener("DOMContentLoaded",es)}function ts(l,e){if(typeof l!="function")throw new Error("handler function expected");Le.EventHandlerRegistry==null&&(Le.EventHandlerRegistry=[]);for(var t=Le.EventHandlerRegistry,n=0,i=t.length;n<i;n++)if(t[n].Handler===l){t[n].onceOnly=e;return}t.push({Handler:l,onceOnly:e}),t.length===1&&_d()}function ns(l){Le.EventHandlerRegistry==null&&(Le.EventHandlerRegistry=[]);for(var e=Le.EventHandlerRegistry,t=0,n=e.length;t<n;t++)if(e[t].Handler===l){e.splice(t,1);break}e.length===0&&hd()}function _d(){Le.AccuracyPoller=setInterval(function(){var l=on();Kt(),on()!==l&&gd()},500)}function hd(){clearInterval(Le.AccuracyPoller),Le.AccuracyPoller=void 0}function gd(){Le.EventHandlerRegistry==null&&(Le.EventHandlerRegistry=[]);for(var l=Le.EventHandlerRegistry,e=0,t=l.length;e<t;e++){var n=l[e],i=n.Handler,s=n.onceOnly;try{i(on())}catch(r){console.warn("PointingAccuracy observation function failed with",r)}s&&ns(i)}}var ls={get isMobile(){return Vn()},get isPhone(){return xi()},get isTablet(){return Vn()&&!xi()},get isLegacyTouchDevice(){return Kn()},rewriteMediaQueriesOnLegacyTouchDevices:es,get PointingAccuracy(){return on()},get canHover(){return ad()},onPointingAccuracyChanged:ud,oncePointingAccuracyChanged:fd,offPointingAccuracyChanged:cd,get observesPointingAccuracy(){return Le.AccuracyPoller!=null}};function un(l,e){return l.data&&(l.data=l.data.map(t=>un(t,e))),{...l,text:e(l.text),css:l.disabled?"disabled":""}}function bd(l){if(typeof l=="string"){const e=l*1;if(!isNaN(e))return e}return l}function pd(l,e){for(let t=l.length-1;t>=0;t--)if(l[t]===e){l.splice(t,1);break}}let is=new Date,fn=!1;const mt=[],ss=l=>{if(fn){fn=!1;return}for(let e=mt.length-1;e>=0;e--){const{node:t,date:n,props:i}=mt[e];if(!(n>is)&&!t.contains(l.target)&&t!==l.target&&(i.callback&&i.callback(l),i.modal||l.defaultPrevented))break}},rs=["click","contextmenu"],wd=l=>{is=new Date,fn=!0;for(let e=mt.length-1;e>=0;e--){const{node:t}=mt[e];if(!t.contains(l.target)&&t!==l.target){fn=!1;break}}};function kd(l,e){mt.length||(rs.forEach(n=>document.addEventListener(n,ss)),document.addEventListener("mousedown",wd)),typeof e!="object"&&(e={callback:e});const t={node:l,date:new Date,props:e};return mt.push(t),{destroy(){pd(mt,t),mt.length||rs.forEach(n=>document.removeEventListener(n,ss))}}}new Date().valueOf();function os(l,e){l.forEach(t=>{e(t),t.data&&t.data.length&&os(t.data,e)})}function as(l,e){const t=[];return l.forEach(n=>{if(n.data){const i=as(n.data,e);i.length&&t.push({...n,data:i})}else e(n)&&t.push(n)}),t}let vd=1;function yd(l){return os(l,e=>{e.id=e.id||vd++}),l}const Sd={};function us(l){return Sd[l]}function fs(l){let e,t;return{c(){e=I("i"),g(e,"class",t="wx-icon "+l[0].icon+" svelte-xfznf6")},m(n,i){v(n,e,i)},p(n,i){i&1&&t!==(t="wx-icon "+n[0].icon+" svelte-xfznf6")&&g(e,"class",t)},d(n){n&&k(e)}}}function Cd(l){let e,t=l[0].text+"",n;return{c(){e=I("span"),n=$(t),g(e,"class","wx-value svelte-xfznf6")},m(i,s){v(i,e,s),M(e,n)},p(i,s){s&1&&t!==(t=i[0].text+"")&&re(n,t)},i:K,o:K,d(i){i&&k(e)}}}function Id(l){let e,t,n;var i=us(l[0].type);function s(r,a){return{props:{item:r[0]}}}return i&&(e=Je(i,s(l))),{c(){e&&P(e.$$.fragment),t=se()},m(r,a){e&&R(e,r,a),v(r,t,a),n=!0},p(r,a){if(a&1&&i!==(i=us(r[0].type))){if(e){W();const o=e;p(o.$$.fragment,1,0,()=>{A(o,1)}),Y()}i?(e=Je(i,s(r)),P(e.$$.fragment),b(e.$$.fragment,1),R(e,t.parentNode,t)):e=null}else if(i){const o={};a&1&&(o.item=r[0]),e.$set(o)}},i(r){n||(e&&b(e.$$.fragment,r),n=!0)},o(r){e&&p(e.$$.fragment,r),n=!1},d(r){r&&k(t),e&&A(e,r)}}}function cs(l){let e,t=l[0].subtext+"",n;return{c(){e=I("span"),n=$(t),g(e,"class","wx-subtext svelte-xfznf6")},m(i,s){v(i,e,s),M(e,n)},p(i,s){s&1&&t!==(t=i[0].subtext+"")&&re(n,t)},d(i){i&&k(e)}}}function ds(l){let e;return{c(){e=I("i"),g(e,"class","wx-sub-icon wxi-angle-right svelte-xfznf6")},m(t,n){v(t,e,n)},d(t){t&&k(e)}}}function Md(l){let e,t,n,i,s,r,a,o,u,f,c,d=l[0].icon&&fs(l);const m=[Id,Cd],_=[];function h(S,C){return S[0].type?0:1}n=h(l),i=_[n]=m[n](l);let w=l[0].subtext&&cs(l),y=l[0].data&&ds();return{c(){e=I("div"),d&&d.c(),t=H(),i.c(),s=H(),w&&w.c(),r=H(),y&&y.c(),g(e,"class",a="wx-item "+(l[0].css||"")+" svelte-xfznf6"),g(e,"data-id",o=l[0].id)},m(S,C){v(S,e,C),d&&d.m(e,null),M(e,t),_[n].m(e,null),M(e,s),w&&w.m(e,null),M(e,r),y&&y.m(e,null),u=!0,f||(c=[te(e,"mouseenter",l[1]),te(e,"click",l[4])],f=!0)},p(S,[C]){S[0].icon?d?d.p(S,C):(d=fs(S),d.c(),d.m(e,t)):d&&(d.d(1),d=null);let T=n;n=h(S),n===T?_[n].p(S,C):(W(),p(_[T],1,1,()=>{_[T]=null}),Y(),i=_[n],i?i.p(S,C):(i=_[n]=m[n](S),i.c()),b(i,1),i.m(e,s)),S[0].subtext?w?w.p(S,C):(w=cs(S),w.c(),w.m(e,r)):w&&(w.d(1),w=null),S[0].data?y||(y=ds(),y.c(),y.m(e,null)):y&&(y.d(1),y=null),(!u||C&1&&a!==(a="wx-item "+(S[0].css||"")+" svelte-xfznf6"))&&g(e,"class",a),(!u||C&1&&o!==(o=S[0].id))&&g(e,"data-id",o)},i(S){u||(b(i),u=!0)},o(S){p(i),u=!1},d(S){S&&k(e),d&&d.d(),_[n].d(),w&&w.d(),y&&y.d(),f=!1,De(c)}}}function Td(l,e,t){let{item:n}=e,{showSub:i=!1}=e,{activeItem:s=null}=e;function r(){t(2,i=n.data?n.id:!1),t(3,s=this)}function a(o){ye.call(this,l,o)}return l.$$set=o=>{"item"in o&&t(0,n=o.item),"showSub"in o&&t(2,i=o.showSub),"activeItem"in o&&t(3,s=o.activeItem)},[n,r,i,s,a]}class Dd extends ee{constructor(e){super(),x(this,e,Td,Md,Z,{item:0,showSub:2,activeItem:3})}}function ms(l,e,t){const n=l.slice();return n[30]=e[t],n}function Ed(l){let e,t,n,i;function s(u){l[17](u)}function r(u){l[18](u)}function a(...u){return l[19](l[30],...u)}let o={item:l[30]};return l[6]!==void 0&&(o.showSub=l[6]),l[7]!==void 0&&(o.activeItem=l[7]),e=new Dd({props:o}),ue.push(()=>Se(e,"showSub",s)),ue.push(()=>Se(e,"activeItem",r)),e.$on("click",a),{c(){P(e.$$.fragment)},m(u,f){R(e,u,f),i=!0},p(u,f){l=u;const c={};f[0]&1&&(c.item=l[30]),!t&&f[0]&64&&(t=!0,c.showSub=l[6],Ie(()=>t=!1)),!n&&f[0]&128&&(n=!0,c.activeItem=l[7],Ie(()=>n=!1)),e.$set(c)},i(u){i||(b(e.$$.fragment,u),i=!0)},o(u){p(e.$$.fragment,u),i=!1},d(u){A(e,u)}}}function Ld(l){let e;return{c(){e=I("div"),g(e,"class","wx-separator svelte-1tqohog")},m(t,n){v(t,e,n)},p:K,i:K,o:K,d(t){t&&k(e)}}}function _s(l){let e,t;return e=new Bn({props:{css:l[2],options:l[30].data,at:"right-overlap",parent:l[7],context:l[1]}}),e.$on("click",l[20]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i[0]&4&&(s.css=n[2]),i[0]&1&&(s.options=n[30].data),i[0]&128&&(s.parent=n[7]),i[0]&2&&(s.context=n[1]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function hs(l,e){let t,n,i,s,r,a;const o=[Ld,Ed],u=[];function f(d,m){return d[30].type==="separator"?0:1}n=f(e),i=u[n]=o[n](e);let c=e[30].data&&e[6]===e[30].id&&_s(e);return{key:l,first:null,c(){t=se(),i.c(),s=H(),c&&c.c(),r=se(),this.first=t},m(d,m){v(d,t,m),u[n].m(d,m),v(d,s,m),c&&c.m(d,m),v(d,r,m),a=!0},p(d,m){e=d;let _=n;n=f(e),n===_?u[n].p(e,m):(W(),p(u[_],1,1,()=>{u[_]=null}),Y(),i=u[n],i?i.p(e,m):(i=u[n]=o[n](e),i.c()),b(i,1),i.m(s.parentNode,s)),e[30].data&&e[6]===e[30].id?c?(c.p(e,m),m[0]&65&&b(c,1)):(c=_s(e),c.c(),b(c,1),c.m(r.parentNode,r)):c&&(W(),p(c,1,1,()=>{c=null}),Y())},i(d){a||(b(i),b(c),a=!0)},o(d){p(i),p(c),a=!1},d(d){d&&(k(t),k(s),k(r)),u[n].d(d),c&&c.d(d)}}}function Rd(l){let e,t=[],n=new Map,i,s,r,a,o=fe(l[0]);const u=f=>f[30].id;for(let f=0;f<o.length;f+=1){let c=ms(l,o,f),d=u(c);n.set(d,t[f]=hs(d,c))}return{c(){e=I("div");for(let f=0;f<t.length;f+=1)t[f].c();g(e,"data-wx-menu","true"),g(e,"class",i="wx-menu "+l[2]+" svelte-1tqohog"),ce(e,"top",l[4]+"px"),ce(e,"left",l[3]+"px"),ce(e,"width",l[5])},m(f,c){v(f,e,c);for(let d=0;d<t.length;d+=1)t[d]&&t[d].m(e,null);l[21](e),s=!0,r||(a=[He(kd.call(null,e,{callback:l[11],modal:!0})),te(e,"mouseleave",l[10])],r=!0)},p(f,c){c[0]&711&&(o=fe(f[0]),W(),t=Oe(t,c,u,1,f,o,n,e,Ue,hs,null,ms),Y()),(!s||c[0]&4&&i!==(i="wx-menu "+f[2]+" svelte-1tqohog"))&&g(e,"class",i),(!s||c[0]&16)&&ce(e,"top",f[4]+"px"),(!s||c[0]&8)&&ce(e,"left",f[3]+"px"),(!s||c[0]&32)&&ce(e,"width",f[5])},i(f){if(!s){for(let c=0;c<o.length;c+=1)b(t[c]);s=!0}},o(f){for(let c=0;c<t.length;c+=1)p(t[c]);s=!1},d(f){f&&k(e);for(let c=0;c<t.length;c+=1)t[c].d();l[21](null),r=!1,De(a)}}}function Ad(l){for(;l;){l=l.parentNode;const e=getComputedStyle(l).position;if(l===document.body||e==="relative"||e==="absolute"||e==="fixed")return l}return null}function Pd(l,e){let t=0;for(;l;){const n=getComputedStyle(l).position;if((n==="absolute"||n==="relative"||n==="fixed")&&(t=parseInt(getComputedStyle(l)["z-index"])||0),l=l.parentNode,l===e||l===document.body)break}return t}function Nd(l,e,t){const n=Ee();let{options:i}=e,{left:s=0}=e,{top:r=0}=e,{at:a="bottom"}=e,{parent:o=null}=e,{mount:u=null}=e,{context:f=null}=e,{css:c=""}=e,d=-1e4,m=-1e4,_,h,w,y,S,C,T,L,N,E,F;function G(){if(t(3,d=s),t(4,m=r),!F)return;const V=Ad(F),ne=L?document.body:V;if(!V)return;const Q=F.getBoundingClientRect();if(!Q.width)return;const B=V.getBoundingClientRect(),ie=ne.getBoundingClientRect();if(o){const Te=Pd(o,V);t(8,F.style.zIndex=Math.max(Te+1,20),F)}if(o&&a!=="point"){h=o.getBoundingClientRect();let Te=L?0:1;t(3,d=C?h.right+Te:h.left-Te),t(4,m=w?h.bottom+1:h.top),t(5,_=S?h.width+"px":"auto")}else h={left:s,right:s,top:r,bottom:r};let ge=y;T&&t(4,m=h.top-Q.height);const Fe=m+Q.height-ie.bottom;Fe>0&&t(4,m-=Fe),d+Q.width-ie.right>0&&(C?ge=!0:t(3,d=h.right-Q.width)),ge&&t(3,d=h.left-Q.width),d<0&&(a!=="left"?t(3,d=0):t(3,d=h.right)),t(3,d+=ne.scrollLeft-B.left),t(4,m+=ne.scrollTop-B.top)}u&&u(G),ot(G);function U(){t(6,N=!1)}function O(){n("click",{action:null})}function D(V){N=V,t(6,N)}function j(V){E=V,t(7,E)}const z=(V,ne)=>{if(!V.data&&!ne.defaultPrevented){const Q={context:f,action:V,event:ne};V.handler&&V.handler(Q),n("click",Q),ne.stopPropagation()}};function q(V){ye.call(this,l,V)}function J(V){ue[V?"unshift":"push"](()=>{F=V,t(8,F)})}return l.$$set=V=>{"options"in V&&t(0,i=V.options),"left"in V&&t(12,s=V.left),"top"in V&&t(13,r=V.top),"at"in V&&t(14,a=V.at),"parent"in V&&t(15,o=V.parent),"mount"in V&&t(16,u=V.mount),"context"in V&&t(1,f=V.context),"css"in V&&t(2,c=V.css)},l.$$.update=()=>{l.$$.dirty[0]&1&&yd(i),l.$$.dirty[0]&16384&&(w=a.indexOf("bottom")!==-1,y=a.indexOf("left")!==-1,C=a.indexOf("right")!==-1,T=a.indexOf("top")!==-1,L=a.indexOf("overlap")!==-1,S=a.indexOf("fit")!==-1),l.$$.dirty[0]&32768&&G()},[i,f,c,d,m,_,N,E,F,n,U,O,s,r,a,o,u,D,j,z,q,J]}let Bn=class extends ee{constructor(e){super(),x(this,e,Nd,Rd,Z,{options:0,left:12,top:13,at:14,parent:15,mount:16,context:1,css:2},null,[-1,-1])}};const zd=l=>({}),gs=l=>({mount:l[1]});function Hd(l){let e,t,n,i;const s=l[5].default,r=be(s,l,l[4],gs);return{c(){e=I("div"),t=I("div"),r&&r.c(),g(t,"class",n="wx-"+l[0]+"-theme svelte-1dixdmq"),g(e,"class","wx-portal svelte-1dixdmq")},m(a,o){v(a,e,o),M(e,t),r&&r.m(t,null),l[6](t),i=!0},p(a,[o]){r&&r.p&&(!i||o&16)&&we(r,s,a,a[4],i?pe(s,a[4],o,zd):ke(a[4]),gs),(!i||o&1&&n!==(n="wx-"+a[0]+"-theme svelte-1dixdmq"))&&g(t,"class",n)},i(a){i||(b(r,a),i=!0)},o(a){p(r,a),i=!1},d(a){a&&k(e),r&&r.d(a),l[6](null)}}}function Od(l){for(;l!==document.body&&!l.getAttribute("data-wx-portal-root");)l=l.parentNode;return l}function Fd(l,e,t){let{$$slots:n={},$$scope:i}=e,s,{theme:r=""}=e,{target:a=void 0}=e,o=[];const u=c=>{o&&o.push(c)};r===""&&(r=ve("wx-theme")),ot(()=>{(a||Od(s)).appendChild(s),o&&o.forEach(d=>d())}),yn(()=>{s&&s.parentNode&&s.parentNode.removeChild(s)});function f(c){ue[c?"unshift":"push"](()=>{s=c,t(2,s)})}return l.$$set=c=>{"theme"in c&&t(0,r=c.theme),"target"in c&&t(3,a=c.target),"$$scope"in c&&t(4,i=c.$$scope)},[r,u,s,a,i,n,f]}class bs extends ee{constructor(e){super(),x(this,e,Fd,Hd,Z,{theme:0,target:3,mount:1})}get mount(){return this.$$.ctx[1]}}function qd(l){let e,t,n,i;const s=l[14].default,r=be(s,l,l[15],null);return{c(){e=I("div"),r&&r.c(),g(e,"data-menu-ignore","true")},m(a,o){v(a,e,o),r&&r.m(e,null),t=!0,n||(i=te(e,"click",l[2]),n=!0)},p(a,o){r&&r.p&&(!t||o&32768)&&we(r,s,a,a[15],t?pe(s,a[15],o,null):ke(a[15]),null)},i(a){t||(b(r,a),t=!0)},o(a){p(r,a),t=!1},d(a){a&&k(e),r&&r.d(a),n=!1,i()}}}function ps(l){let e,t;return e=new bs({props:{$$slots:{default:[jd,({mount:n})=>({20:n}),({mount:n})=>n?1048576:0]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1081595&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function ws(l){let e,t;return e=new Bn({props:{css:l[1],at:l[0],top:l[7],left:l[6],mount:l[20],parent:l[5],context:l[4],options:l[3]}}),e.$on("click",l[9]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&2&&(s.css=n[1]),i&1&&(s.at=n[0]),i&128&&(s.top=n[7]),i&64&&(s.left=n[6]),i&1048576&&(s.mount=n[20]),i&32&&(s.parent=n[5]),i&16&&(s.context=n[4]),i&8&&(s.options=n[3]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function jd(l){let e=l[5],t,n,i=ws(l);return{c(){i.c(),t=se()},m(s,r){i.m(s,r),v(s,t,r),n=!0},p(s,r){r&32&&Z(e,e=s[5])?(W(),p(i,1,1,K),Y(),i=ws(s),i.c(),b(i,1),i.m(t.parentNode,t)):i.p(s,r)},i(s){n||(b(i),n=!0)},o(s){p(i),n=!1},d(s){s&&k(t),i.d(s)}}}function Ud(l){let e,t,n,i=l[8]&&l[8].default&&qd(l),s=l[5]&&ps(l);return{c(){i&&i.c(),e=H(),s&&s.c(),t=se()},m(r,a){i&&i.m(r,a),v(r,e,a),s&&s.m(r,a),v(r,t,a),n=!0},p(r,[a]){r[8]&&r[8].default&&i.p(r,a),r[5]?s?(s.p(r,a),a&32&&b(s,1)):(s=ps(r),s.c(),b(s,1),s.m(t.parentNode,t)):s&&(W(),p(s,1,1,()=>{s=null}),Y())},i(r){n||(b(i),b(s),n=!0)},o(r){p(i),p(s),n=!1},d(r){r&&(k(e),k(t)),i&&i.d(r),s&&s.d(r)}}}function Vd(l,e,t){let{$$slots:n={},$$scope:i}=e;const s=Ee(),r=e.$$slots;let{options:a}=e,{at:o="bottom"}=e,{resolver:u=null}=e,{dataKey:f="contextId"}=e,{filter:c=null}=e,{css:d=""}=e;const m=L;var _,h=null,w=null;let y=0,S=0;function C(N){t(5,w=null),s("click",N.detail)}function T(N,E){let F=null;for(;N&&N.dataset&&!F;)F=N.dataset[E],N=N.parentNode;return F?bd(F):null}function L(N,E){if(!N){t(5,w=null);return}if(N.defaultPrevented)return;const F=N.target;F&&F.dataset&&F.dataset.menuIgnore||(t(6,y=N.clientX+1),t(7,S=N.clientY+1),t(4,h=typeof E<"u"?E:T(F,f)),!(u&&(t(4,h=u(h,N)),!h))&&(h!==null&&c&&t(3,_=as(a,G=>c(G,h))),t(5,w=F),N.preventDefault()))}return l.$$set=N=>{t(19,e=Ce(Ce({},e),We(N))),"options"in N&&t(10,a=N.options),"at"in N&&t(0,o=N.at),"resolver"in N&&t(11,u=N.resolver),"dataKey"in N&&t(12,f=N.dataKey),"filter"in N&&t(13,c=N.filter),"css"in N&&t(1,d=N.css),"$$scope"in N&&t(15,i=N.$$scope)},l.$$.update=()=>{l.$$.dirty&1024&&t(3,_=a)},e=We(e),[o,d,m,_,h,w,y,S,r,C,a,u,f,c,n,i]}class cn extends ee{constructor(e){super(),x(this,e,Vd,Ud,Z,{options:10,at:0,resolver:11,dataKey:12,filter:13,css:1,handler:2})}get handler(){return this.$$.ctx[2]}}function ks(l){let e,t;return e=new bs({props:{$$slots:{default:[Kd,({mount:n})=>({10:n}),({mount:n})=>n?1024:0]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1295&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function vs(l){let e,t;return e=new Bn({props:{css:l[2],at:l[1],mount:l[10],parent:l[3],options:l[0]}}),e.$on("click",l[4]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&4&&(s.css=n[2]),i&2&&(s.at=n[1]),i&1024&&(s.mount=n[10]),i&8&&(s.parent=n[3]),i&1&&(s.options=n[0]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Kd(l){let e=l[3],t,n,i=vs(l);return{c(){i.c(),t=se()},m(s,r){i.m(s,r),v(s,t,r),n=!0},p(s,r){r&8&&Z(e,e=s[3])?(W(),p(i,1,1,K),Y(),i=vs(s),i.c(),b(i,1),i.m(t.parentNode,t)):i.p(s,r)},i(s){n||(b(i),n=!0)},o(s){p(i),n=!1},d(s){s&&k(t),i.d(s)}}}function Bd(l){let e,t,n,i,s,r;const a=l[7].default,o=be(a,l,l[8],null);let u=l[3]&&ks(l);return{c(){e=I("div"),o&&o.c(),t=H(),u&&u.c(),n=se(),g(e,"data-menu-ignore","true")},m(f,c){v(f,e,c),o&&o.m(e,null),v(f,t,c),u&&u.m(f,c),v(f,n,c),i=!0,s||(r=te(e,"click",l[5]),s=!0)},p(f,[c]){o&&o.p&&(!i||c&256)&&we(o,a,f,f[8],i?pe(a,f[8],c,null):ke(f[8]),null),f[3]?u?(u.p(f,c),c&8&&b(u,1)):(u=ks(f),u.c(),b(u,1),u.m(n.parentNode,n)):u&&(W(),p(u,1,1,()=>{u=null}),Y())},i(f){i||(b(o,f),b(u),i=!0)},o(f){p(o,f),p(u),i=!1},d(f){f&&(k(e),k(t),k(n)),o&&o.d(f),u&&u.d(f),s=!1,r()}}}function Wd(l,e,t){let{$$slots:n={},$$scope:i}=e;const s=Ee();let{options:r}=e,{at:a="bottom"}=e,{css:o=""}=e;const u=m=>{t(3,f=m.target),m.preventDefault()};var f=null;function c(m){t(3,f=null),s("click",m.detail)}function d(m){let _=m.target;for(;!_.dataset.menuIgnore;)t(3,f=_),_=_.parentNode}return l.$$set=m=>{"options"in m&&t(0,r=m.options),"at"in m&&t(1,a=m.at),"css"in m&&t(2,o=m.css),"$$scope"in m&&t(8,i=m.$$scope)},[r,a,o,f,c,d,u,n,i]}class Yd extends ee{constructor(e){super(),x(this,e,Wd,Bd,Z,{options:0,at:1,css:2,handler:6})}get handler(){return this.$$.ctx[6]}}function ys(l){let e,t,n,i,s,r,a,o,u;t=new Me({props:{css:`wxi-angle-${l[0].collapsed?"right":"down"}`}}),t.$on("click",l[8]);function f(_,h){return _[2]?Jd:Gd}let c=f(l),d=c(l),m=l[7]&&Ss(l);return{c(){e=I("div"),P(t.$$.fragment),n=H(),i=I("div"),d.c(),s=H(),m&&m.c(),r=se(),g(e,"class","wx-label-icon svelte-9z9yku"),g(i,"class","wx-label-text svelte-9z9yku")},m(_,h){v(_,e,h),R(t,e,null),v(_,n,h),v(_,i,h),d.m(i,null),v(_,s,h),m&&m.m(_,h),v(_,r,h),a=!0,o||(u=te(i,"dblclick",l[14]),o=!0)},p(_,h){const w={};h[0]&1&&(w.css=`wxi-angle-${_[0].collapsed?"right":"down"}`),t.$set(w),c===(c=f(_))&&d?d.p(_,h):(d.d(1),d=c(_),d&&(d.c(),d.m(i,null))),_[7]?m?(m.p(_,h),h[0]&128&&b(m,1)):(m=Ss(_),m.c(),b(m,1),m.m(r.parentNode,r)):m&&(W(),p(m,1,1,()=>{m=null}),Y())},i(_){a||(b(t.$$.fragment,_),b(m),a=!0)},o(_){p(t.$$.fragment,_),p(m),a=!1},d(_){_&&(k(e),k(n),k(i),k(s),k(r)),A(t),d.d(),m&&m.d(_),o=!1,u()}}}function Gd(l){let e=l[0].label+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p(n,i){i[0]&1&&e!==(e=n[0].label+"")&&re(t,e)},d(n){n&&k(t)}}}function Jd(l){let e,t,n,i;return{c(){e=I("input"),g(e,"type","text"),g(e,"class","wx-input svelte-9z9yku"),e.value=t=l[0].label},m(s,r){v(s,e,r),n||(i=[te(e,"input",l[15]),te(e,"keypress",l[16]),te(e,"blur",l[13]),He(Zd.call(null,e))],n=!0)},p(s,r){r[0]&1&&t!==(t=s[0].label)&&e.value!==t&&(e.value=t)},d(s){s&&k(e),n=!1,De(i)}}}function Ss(l){let e,t;return e=new cn({props:{options:l[3],$$slots:{default:[Qd]},$$scope:{ctx:l}}}),e.$on("click",l[17]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i[0]&8&&(s.options=n[3]),i[0]&536870944&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Qd(l){let e,t,n;return t=new Me({props:{css:"wxi-dots-h"}}),{c(){e=I("div"),P(t.$$.fragment),g(e,"class","wx-menu svelte-9z9yku")},m(i,s){v(i,e,s),R(t,e,null),l[27](e),n=!0},p:K,i(i){n||(b(t.$$.fragment,i),n=!0)},o(i){p(t.$$.fragment,i),n=!1},d(i){i&&k(e),A(t),l[27](null)}}}function Cs(l){let e,t;const n=l[26].default,i=be(n,l,l[29],null);return{c(){e=I("div"),i&&i.c(),g(e,"class","wx-content svelte-9z9yku")},m(s,r){v(s,e,r),i&&i.m(e,null),t=!0},p(s,r){i&&i.p&&(!t||r[0]&536870912)&&we(i,n,s,s[29],t?pe(n,s[29],r,null):ke(s[29]),null)},i(s){t||(b(i,s),t=!0)},o(s){p(i,s),t=!1},d(s){s&&k(e),i&&i.d(s)}}}function Xd(l){let e,t,n,i,s,r,a,o,u,f,c=l[1]&&ys(l),d=!l[0].collapsed&&Cs(l);return{c(){e=I("div"),t=I("div"),c&&c.c(),n=H(),i=I("div"),o=H(),d&&d.c(),g(i,"class",s="wx-label-line "+(l[1]?"collapsable":"")+" svelte-9z9yku"),g(t,"class",r="wx-label "+(l[1]?"collapsable":"")+" svelte-9z9yku"),g(t,"data-row-header",a=l[0].id),g(e,"class",u=qe(l[6])+" svelte-9z9yku"),X(e,"wx-collapsed",l[0].collapsed)},m(m,_){v(m,e,_),M(e,t),c&&c.m(t,null),M(t,n),M(t,i),l[28](t),M(e,o),d&&d.m(e,null),f=!0},p(m,_){m[1]?c?(c.p(m,_),_[0]&2&&b(c,1)):(c=ys(m),c.c(),b(c,1),c.m(t,n)):c&&(W(),p(c,1,1,()=>{c=null}),Y()),(!f||_[0]&2&&s!==(s="wx-label-line "+(m[1]?"collapsable":"")+" svelte-9z9yku"))&&g(i,"class",s),(!f||_[0]&2&&r!==(r="wx-label "+(m[1]?"collapsable":"")+" svelte-9z9yku"))&&g(t,"class",r),(!f||_[0]&1&&a!==(a=m[0].id))&&g(t,"data-row-header",a),m[0].collapsed?d&&(W(),p(d,1,1,()=>{d=null}),Y()):d?(d.p(m,_),_[0]&1&&b(d,1)):(d=Cs(m),d.c(),b(d,1),d.m(e,null)),(!f||_[0]&64&&u!==(u=qe(m[6])+" svelte-9z9yku"))&&g(e,"class",u),(!f||_[0]&65)&&X(e,"wx-collapsed",m[0].collapsed)},i(m){f||(b(c),b(d),f=!0)},o(m){p(c),p(d),f=!1},d(m){m&&k(e),c&&c.d(),l[28](null),d&&d.d()}}}function Zd(l){l.focus()}function $d(l,e,t){let n,i,s,r,a,o,u,f,c,{$$slots:d={},$$scope:m}=e,{row:_={id:"default",label:"default",collapsed:!1}}=e,{rows:h=[]}=e,{api:w}=e,{collapsable:y=!0}=e;const S=ve("wx-i18n").getGroup("kanban"),C=Ee(),{showModal:T}=ve("wx-helpers");function L(){C("action",{action:"update-row",data:{id:_.id,row:{collapsed:!_.collapsed}}})}const{readonly:N,rowShape:E,cardsMap:F,columns:G}=w.getReactiveState();he(l,N,me=>t(25,c=me)),he(l,E,me=>t(24,f=me)),he(l,F,me=>t(22,o=me)),he(l,G,me=>t(23,u=me));let U=!1,O=null;function D(){U&&O?.trim()&&C("action",{action:"update-row",data:{id:_.id,row:{label:O}}}),t(2,U=!1),O=null}function j(){n&&t(2,U=!0)}function z(me){O=me.target.value}function q(me){me.charCode===13&&D()}function J(me){const Te=me==="up"?i-1:i+2,Ye=h[Te]?.id;C("action",{action:"move-row",data:{id:_.id,before:Ye}})}function V(me){const Te=me.detail.action;if(Te){if(Te.onClick){Te.onClick({id:Te.id,item:Te,row:_});return}switch(Te.id){case"set-edit":j();break;case"delete-row":{(f.confirmDeletion??!0?T({message:S("Would you like to delete this row?")}):Promise.resolve()).then(()=>{C("action",{action:"delete-row",data:{id:_.id}})}).catch(()=>{});break}case"move-row:up":J("up");break;case"move-row:down":J("down");break}}}let ne,Q;const B=(me,Te,Ye,de)=>{const Re=de.menu.items({rows:Ye,rowIndex:Te,row:me});return!Re||!Re.length?null:Re.map(Ae=>un(Ae,S))};function ie(me,Te,Ye,de){let Re="wx-row";if(me.collapsed&&(Re+=" wx-collapsed"),me.css&&(Re+=" "+me.css),Te&&Te.css){let Ae=[];Ye.forEach(Pe=>Ae=Ae.concat(de[ze(Pe.id,me.id)])),Re+=" "+Te.css(me,Ae)}return Re}function ge(me){ue[me?"unshift":"push"](()=>{Q=me,t(5,Q)})}function Fe(me){ue[me?"unshift":"push"](()=>{ne=me,t(4,ne)})}return l.$$set=me=>{"row"in me&&t(0,_=me.row),"rows"in me&&t(18,h=me.rows),"api"in me&&t(19,w=me.api),"collapsable"in me&&t(1,y=me.collapsable),"$$scope"in me&&t(29,m=me.$$scope)},l.$$.update=()=>{l.$$.dirty[0]&33554432&&t(20,n=c.edit),l.$$.dirty[0]&262145&&t(21,i=h.findIndex(me=>me.id===_.id)),l.$$.dirty[0]&19136513&&t(3,s=B(_,i,h,f)),l.$$.dirty[0]&17825804&&t(7,r=f.menu.show&&!!s&&n&&!U),l.$$.dirty[0]&29360129&&t(6,a=ie(_,f,u,o))},[_,y,U,s,ne,Q,a,r,L,N,E,F,G,D,j,z,q,V,h,w,n,i,o,u,f,c,d,ge,Fe,m]}class Is extends ee{constructor(e){super(),x(this,e,$d,Xd,Z,{row:0,rows:18,api:19,collapsable:1},null,[-1,-1])}}function Ms(l){let e,t;return{c(){e=I("div"),t=$(l[0]),g(e,"class","wx-label svelte-fhl0ry")},m(n,i){v(n,e,i),M(e,t)},p(n,i){i&1&&re(t,n[0])},d(n){n&&k(e)}}}function Ts(l){let e,t;return{c(){e=I("div"),t=$(l[2]),g(e,"class","wx-value svelte-fhl0ry")},m(n,i){v(n,e,i),M(e,t)},p(n,i){i&4&&re(t,n[2])},d(n){n&&k(e)}}}function xd(l){let e,t,n,i,s,r=l[0]&&Ms(l),a=l[1]&&Ts(l);return{c(){e=I("div"),r&&r.c(),t=H(),n=I("div"),i=I("div"),s=H(),a&&a.c(),g(i,"class","wx-progress svelte-fhl0ry"),g(i,"style",l[3]),g(n,"class","wx-wrap svelte-fhl0ry"),g(e,"class","wx-layout svelte-fhl0ry")},m(o,u){v(o,e,u),r&&r.m(e,null),M(e,t),M(e,n),M(n,i),M(n,s),a&&a.m(n,null)},p(o,[u]){o[0]?r?r.p(o,u):(r=Ms(o),r.c(),r.m(e,t)):r&&(r.d(1),r=null),u&8&&g(i,"style",o[3]),o[1]?a?a.p(o,u):(a=Ts(o),a.c(),a.m(n,null)):a&&(a.d(1),a=null)},i:K,o:K,d(o){o&&k(e),r&&r.d(),a&&a.d()}}}function em(l,e,t){let{label:n=""}=e,{min:i=0}=e,{max:s=100}=e,{value:r=0}=e,{showValue:a=!0}=e,o="0",u="";return l.$$set=f=>{"label"in f&&t(0,n=f.label),"min"in f&&t(4,i=f.min),"max"in f&&t(5,s=f.max),"value"in f&&t(6,r=f.value),"showValue"in f&&t(1,a=f.showValue)},l.$$.update=()=>{l.$$.dirty&116&&(t(2,o=Math.round((r-i)/(s-i)*100)+"%"),t(3,u=`background: linear-gradient(90deg, var(--wx-color-primary) 0% ${o}, var(--wx-kanban-progress-inactive-color) ${o} 100%);`))},[n,a,o,u,i,s,r]}class tm extends ee{constructor(e){super(),x(this,e,em,xd,Z,{label:0,min:4,max:5,value:6,showValue:1})}}function nm(l){let e;return{c(){e=$(l[5])},m(t,n){v(t,e,n)},p(t,n){n&32&&re(e,t[5])},d(t){t&&k(e)}}}function lm(l){let e=l[0].label+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p(n,i){i&1&&e!==(e=n[0].label+"")&&re(t,e)},d(n){n&&k(t)}}}function im(l){let e,t,n;return{c(){e=I("img"),Jt(e.src,t=l[0].avatar)||g(e,"src",t),g(e,"alt",n=l[0].label),g(e,"class","svelte-vorcau")},m(i,s){v(i,e,s)},p(i,s){s&1&&!Jt(e.src,t=i[0].avatar)&&g(e,"src",t),s&1&&n!==(n=i[0].label)&&g(e,"alt",n)},d(i){i&&k(e)}}}function sm(l){let e,t;function n(r,a){return r[0].avatar?im:r[1]?lm:nm}let i=n(l),s=i(l);return{c(){e=I("div"),s.c(),g(e,"class",t="wx-user "+l[2]+" svelte-vorcau"),g(e,"style",l[4]),X(e,"border",l[3])},m(r,a){v(r,e,a),s.m(e,null)},p(r,[a]){i===(i=n(r))&&s?s.p(r,a):(s.d(1),s=i(r),s&&(s.c(),s.m(e,null))),a&4&&t!==(t="wx-user "+r[2]+" svelte-vorcau")&&g(e,"class",t),a&16&&g(e,"style",r[4]),a&12&&X(e,"border",r[3])},i:K,o:K,d(r){r&&k(e),s.d()}}}function rm(l,e,t){let n,{data:i={label:"",avatar:"",avatarColor:""}}=e,{noTransform:s=!1}=e,{size:r="normal"}=e,{border:a=!0}=e,o="";return l.$$set=u=>{"data"in u&&t(0,i=u.data),"noTransform"in u&&t(1,s=u.noTransform),"size"in u&&t(2,r=u.size),"border"in u&&t(3,a=u.border)},l.$$.update=()=>{l.$$.dirty&1&&t(5,n=i.label.split(" ").map(u=>u[0]).join("")),l.$$.dirty&1&&i.avatarColor&&t(4,o=`background: ${i.avatarColor}; color: var(--wx-color-primary-font);`)},[i,s,r,a,o,n]}class Et extends ee{constructor(e){super(),x(this,e,rm,sm,Z,{data:0,noTransform:1,size:2,border:3})}}function Ds(l,e,t){const n=l.slice();return n[10]=e[t],n}function Es(l){let e,t=[],n=new Map,i,s=fe(l[3].users);const r=a=>a[10].id;for(let a=0;a<s.length;a+=1){let o=Ds(l,s,a),u=r(o);n.set(u,t[a]=Ls(u,o))}return{c(){e=I("div");for(let a=0;a<t.length;a+=1)t[a].c();g(e,"class","wx-users svelte-t8nmm1")},m(a,o){v(a,e,o);for(let u=0;u<t.length;u+=1)t[u]&&t[u].m(e,null);i=!0},p(a,o){o&8&&(s=fe(a[3].users),W(),t=Oe(t,o,r,1,a,s,n,e,Ue,Ls,null,Ds),Y())},i(a){if(!i){for(let o=0;o<s.length;o+=1)b(t[o]);i=!0}},o(a){for(let o=0;o<t.length;o+=1)p(t[o]);i=!1},d(a){a&&k(e);for(let o=0;o<t.length;o+=1)t[o].d()}}}function Ls(l,e){let t,n,i;return n=new Et({props:{data:e[10],noTransform:e[10].id==="$total"}}),{key:l,first:null,c(){t=se(),P(n.$$.fragment),this.first=t},m(s,r){v(s,t,r),R(n,s,r),i=!0},p(s,r){e=s;const a={};r&8&&(a.data=e[10]),r&8&&(a.noTransform=e[10].id==="$total"),n.$set(a)},i(s){i||(b(n.$$.fragment,s),i=!0)},o(s){p(n.$$.fragment,s),i=!1},d(s){s&&k(t),A(n,s)}}}function Rs(l){let e,t,n,i,s,r;t=new Me({props:{css:"wxi-calendar"}});let a=l[3].startDate&&As(l),o=l[3].endDate&&l[3].startDate&&Ps(),u=l[3].endDate&&Ns(l);return{c(){e=I("div"),P(t.$$.fragment),n=H(),a&&a.c(),i=H(),o&&o.c(),s=H(),u&&u.c(),g(e,"class","wx-date svelte-t8nmm1")},m(f,c){v(f,e,c),R(t,e,null),M(e,n),a&&a.m(e,null),M(e,i),o&&o.m(e,null),M(e,s),u&&u.m(e,null),r=!0},p(f,c){f[3].startDate?a?a.p(f,c):(a=As(f),a.c(),a.m(e,i)):a&&(a.d(1),a=null),f[3].endDate&&f[3].startDate?o||(o=Ps(),o.c(),o.m(e,s)):o&&(o.d(1),o=null),f[3].endDate?u?u.p(f,c):(u=Ns(f),u.c(),u.m(e,null)):u&&(u.d(1),u=null)},i(f){r||(b(t.$$.fragment,f),r=!0)},o(f){p(t.$$.fragment,f),r=!1},d(f){f&&k(e),A(t),a&&a.d(),o&&o.d(),u&&u.d()}}}function As(l){let e,t=l[3].startDate+"",n;return{c(){e=I("span"),n=$(t),g(e,"class","wx-date-value svelte-t8nmm1")},m(i,s){v(i,e,s),M(e,n)},p(i,s){s&8&&t!==(t=i[3].startDate+"")&&re(n,t)},d(i){i&&k(e)}}}function Ps(l){let e;return{c(){e=$("-")},m(t,n){v(t,e,n)},d(t){t&&k(e)}}}function Ns(l){let e,t=l[3].endDate+"",n;return{c(){e=I("span"),n=$(t),g(e,"class","wx-date-value svelte-t8nmm1")},m(i,s){v(i,e,s),M(e,n)},p(i,s){s&8&&t!==(t=i[3].endDate+"")&&re(n,t)},d(i){i&&k(e)}}}function zs(l){let e,t,n,i;return t=new Me({props:{css:"wxi-like",$$slots:{default:[om]},$$scope:{ctx:l}}}),t.$on("click",l[5]),{c(){e=I("div"),P(t.$$.fragment),g(e,"class","wx-votes svelte-t8nmm1"),g(e,"data-kanban-id",n=l[2]?"wx-vote-card-button":""),X(e,"wx-kanban-editor-voted",l[0].votes?.includes(l[4])),X(e,"wx-clickable",l[2])},m(s,r){v(s,e,r),R(t,e,null),i=!0},p(s,r){const a={};r&8200&&(a.$$scope={dirty:r,ctx:s}),t.$set(a),(!i||r&4&&n!==(n=s[2]?"wx-vote-card-button":""))&&g(e,"data-kanban-id",n),(!i||r&17)&&X(e,"wx-kanban-editor-voted",s[0].votes?.includes(s[4])),(!i||r&4)&&X(e,"wx-clickable",s[2])},i(s){i||(b(t.$$.fragment,s),i=!0)},o(s){p(t.$$.fragment,s),i=!1},d(s){s&&k(e),A(t)}}}function om(l){let e,t=l[3].votes+"",n;return{c(){e=I("span"),n=$(t),g(e,"class","wx-item-value svelte-t8nmm1")},m(i,s){v(i,e,s),M(e,n)},p(i,s){s&8&&t!==(t=i[3].votes+"")&&re(n,t)},d(i){i&&k(e)}}}function Hs(l){let e,t,n;return t=new Me({props:{css:"wxi-message",$$slots:{default:[am]},$$scope:{ctx:l}}}),{c(){e=I("div"),P(t.$$.fragment),g(e,"class","wx-comments svelte-t8nmm1")},m(i,s){v(i,e,s),R(t,e,null),n=!0},p(i,s){const r={};s&8200&&(r.$$scope={dirty:s,ctx:i}),t.$set(r)},i(i){n||(b(t.$$.fragment,i),n=!0)},o(i){p(t.$$.fragment,i),n=!1},d(i){i&&k(e),A(t)}}}function am(l){let e,t=l[3].comments+"",n;return{c(){e=I("span"),n=$(t),g(e,"class","wx-item-value svelte-t8nmm1")},m(i,s){v(i,e,s),M(e,n)},p(i,s){s&8&&t!==(t=i[3].comments+"")&&re(n,t)},d(i){i&&k(e)}}}function Os(l){let e,t,n;return t=new Me({props:{css:"wxi-paperclip",$$slots:{default:[um]},$$scope:{ctx:l}}}),{c(){e=I("div"),P(t.$$.fragment),g(e,"class","wx-attached svelte-t8nmm1")},m(i,s){v(i,e,s),R(t,e,null),n=!0},p(i,s){const r={};s&8200&&(r.$$scope={dirty:s,ctx:i}),t.$set(r)},i(i){n||(b(t.$$.fragment,i),n=!0)},o(i){p(t.$$.fragment,i),n=!1},d(i){i&&k(e),A(t)}}}function um(l){let e,t=l[3].attached+"",n;return{c(){e=I("span"),n=$(t),g(e,"class","wx-item-value svelte-t8nmm1")},m(i,s){v(i,e,s),M(e,n)},p(i,s){s&8&&t!==(t=i[3].attached+"")&&re(n,t)},d(i){i&&k(e)}}}function fm(l){let e,t,n,i,s,r,a,o,u,f=l[3].users&&Es(l),c=(l[3].endDate||l[3].startDate)&&Rs(l),d=(l[3].votes||l[3].votes===0&&l[2])&&zs(l),m=l[3].comments&&Hs(l),_=l[3].attached&&Os(l);return{c(){e=I("div"),f&&f.c(),t=H(),n=I("div"),i=I("div"),c&&c.c(),s=H(),r=I("div"),d&&d.c(),a=H(),m&&m.c(),o=H(),_&&_.c(),g(i,"class","wx-icons-container svelte-t8nmm1"),g(r,"class","wx-icons-container svelte-t8nmm1"),g(n,"class","wx-card-icons svelte-t8nmm1"),g(e,"class","wx-footer svelte-t8nmm1"),X(e,"wx-with-content",!!Object.keys(l[3]).length)},m(h,w){v(h,e,w),f&&f.m(e,null),M(e,t),M(e,n),M(n,i),c&&c.m(i,null),M(n,s),M(n,r),d&&d.m(r,null),M(r,a),m&&m.m(r,null),M(r,o),_&&_.m(r,null),u=!0},p(h,[w]){h[3].users?f?(f.p(h,w),w&8&&b(f,1)):(f=Es(h),f.c(),b(f,1),f.m(e,t)):f&&(W(),p(f,1,1,()=>{f=null}),Y()),h[3].endDate||h[3].startDate?c?(c.p(h,w),w&8&&b(c,1)):(c=Rs(h),c.c(),b(c,1),c.m(i,null)):c&&(W(),p(c,1,1,()=>{c=null}),Y()),h[3].votes||h[3].votes===0&&h[2]?d?(d.p(h,w),w&12&&b(d,1)):(d=zs(h),d.c(),b(d,1),d.m(r,a)):d&&(W(),p(d,1,1,()=>{d=null}),Y()),h[3].comments?m?(m.p(h,w),w&8&&b(m,1)):(m=Hs(h),m.c(),b(m,1),m.m(r,o)):m&&(W(),p(m,1,1,()=>{m=null}),Y()),h[3].attached?_?(_.p(h,w),w&8&&b(_,1)):(_=Os(h),_.c(),b(_,1),_.m(r,null)):_&&(W(),p(_,1,1,()=>{_=null}),Y()),(!u||w&8)&&X(e,"wx-with-content",!!Object.keys(h[3]).length)},i(h){u||(b(f),b(c),b(d),b(m),b(_),u=!0)},o(h){p(f),p(c),p(d),p(m),p(_),u=!1},d(h){h&&k(e),f&&f.d(),c&&c.d(),d&&d.d(),m&&m.d(),_&&_.d()}}}const Fs="%M %d";function cm(l,e,t){let n,i,s,r=K,a=()=>(r(),r=Ge(m,h=>t(4,s=h)),m);l.$$.on_destroy.push(()=>r());let{cardFields:o}=e,{cardShape:u}=e,{api:f}=e;const c=ve("wx-i18n");function d(h,w){let y={};const{show:S}=w?.users||{},C=h.users;if(S&&C){const U=(Array.isArray(C)?C:[C]).reduce((j,z)=>{const q=w.users.values?.find(J=>J.id===z);return q&&j.push(q),j},[]);let O=U.map(j=>({...j,label:j.label||""})),D=2;u.users.maxCount===!1?D=1/0:u.users.maxCount&&(D=u.users.maxCount),U.length>D&&(O=O.splice(0,D),O.push({label:`+${U.length-O.length}`,id:"$total"})),O?.length&&(y.users=O)}const{show:T,format:L}=w.start_date||{},{show:N,format:E}=w.end_date||{};let{end_date:F,start_date:G}=h;return(T||N)&&(G&&(y.startDate=Ft(L||Fs,c.getRaw().calendar)(G)),F&&(y.endDate=Ft(E||Fs,c.getRaw().calendar)(F))),w?.attached?.show&&h.attached?.length&&(y.attached=h.attached.length),w.comments?.show&&h.comments?.length&&(y.comments=h.comments?.length),w.votes?.show&&(y.votes=h.votes?.length||0),y}let m=null;a();function _(){if(i){const h=o.id;o.votes?.includes(s)?f.exec("delete-vote",{cardId:h}):f.exec("add-vote",{cardId:h})}}return l.$$set=h=>{"cardFields"in h&&t(0,o=h.cardFields),"cardShape"in h&&t(6,u=h.cardShape),"api"in h&&t(7,f=h.api)},l.$$.update=()=>{l.$$.dirty&65&&t(3,n=d(o,u)),l.$$.dirty&128&&f&&a(t(1,m=f.getReactiveState().currentUser)),l.$$.dirty&64&&t(2,i=u.votes?.clickable)},[o,m,i,n,s,_,u,f]}class dm extends ee{constructor(e){super(),x(this,e,cm,fm,Z,{cardFields:0,cardShape:6,api:7})}}function qs(l,e,t){const n=l.slice();return n[3]=e[t],n}function js(l){let e;function t(s,r){return s[3].type==="priority"?_m:mm}let n=t(l),i=n(l);return{c(){i.c(),e=se()},m(s,r){i.m(s,r),v(s,e,r)},p(s,r){n===(n=t(s))&&i?i.p(s,r):(i.d(1),i=n(s),i&&(i.c(),i.m(e.parentNode,e)))},d(s){s&&k(e),i.d(s)}}}function mm(l){let e,t,n,i=l[3].value+"",s,r,a,o=l[3]?.label&&Us(l);return{c(){e=I("div"),o&&o.c(),t=H(),n=I("span"),s=$(i),r=H(),g(n,"class","wx-value"),g(e,"class",a="wx-field "+(l[3].css||"")+" svelte-16qucgr")},m(u,f){v(u,e,f),o&&o.m(e,null),M(e,t),M(e,n),M(n,s),M(e,r)},p(u,f){u[3]?.label?o?o.p(u,f):(o=Us(u),o.c(),o.m(e,t)):o&&(o.d(1),o=null),f&1&&i!==(i=u[3].value+"")&&re(s,i),f&1&&a!==(a="wx-field "+(u[3].css||"")+" svelte-16qucgr")&&g(e,"class",a)},d(u){u&&k(e),o&&o.d()}}}function _m(l){let e,t,n=l[3].value+"",i,s;return{c(){e=I("div"),t=I("span"),i=$(n),s=H(),g(t,"class","wx-priority-label svelte-16qucgr"),g(e,"class","wx-field wx-priority svelte-16qucgr"),ce(e,"background",l[3].color)},m(r,a){v(r,e,a),M(e,t),M(t,i),M(e,s)},p(r,a){a&1&&n!==(n=r[3].value+"")&&re(i,n),a&1&&ce(e,"background",r[3].color)},d(r){r&&k(e)}}}function Us(l){let e,t=l[3].label+"",n,i;return{c(){e=I("span"),n=$(t),i=$(":"),g(e,"class","wx-label")},m(s,r){v(s,e,r),M(e,n),M(e,i)},p(s,r){r&1&&t!==(t=s[3].label+"")&&re(n,t)},d(s){s&&k(e)}}}function Vs(l){let e,t=l[3].value&&js(l);return{c(){t&&t.c(),e=se()},m(n,i){t&&t.m(n,i),v(n,e,i)},p(n,i){n[3].value?t?t.p(n,i):(t=js(n),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(n){n&&k(e),t&&t.d(n)}}}function hm(l){let e,t=fe(l[0]),n=[];for(let i=0;i<t.length;i+=1)n[i]=Vs(qs(l,t,i));return{c(){e=I("div");for(let i=0;i<n.length;i+=1)n[i].c();g(e,"class","wx-card-header svelte-16qucgr")},m(i,s){v(i,e,s);for(let r=0;r<n.length;r+=1)n[r]&&n[r].m(e,null)},p(i,[s]){if(s&1){t=fe(i[0]);let r;for(r=0;r<t.length;r+=1){const a=qs(i,t,r);n[r]?n[r].p(a,s):(n[r]=Vs(a),n[r].c(),n[r].m(e,null))}for(;r<n.length;r+=1)n[r].d(1);n.length=t.length}},i:K,o:K,d(i){i&&k(e),at(n,i)}}}function gm(l,e){let t=[];if(e.priority?.show){const i=e.priority.values?.find(s=>s.id===l.priority);i&&t.push({type:"priority",value:i.label,color:i.color})}const n=e.headerFields;if(n){const i=n.reduce((s,r)=>(l[r.key]&&s.push({value:l[r.key],label:r.label,css:r.css}),s),[]);i&&t.push(...i)}return t}function bm(l,e,t){let n,{cardFields:i}=e,{cardShape:s}=e;return l.$$set=r=>{"cardFields"in r&&t(1,i=r.cardFields),"cardShape"in r&&t(2,s=r.cardShape)},l.$$.update=()=>{l.$$.dirty&6&&t(0,n=gm(i,s))},[n,i,s]}class pm extends ee{constructor(e){super(),x(this,e,bm,hm,Z,{cardFields:1,cardShape:2})}}function Ks(l){let e;return{c(){e=I("div"),g(e,"class","wx-color wx-rounded svelte-1emjf0g"),ce(e,"background",l[0].color)},m(t,n){v(t,e,n)},p(t,n){n&1&&ce(e,"background",t[0].color)},d(t){t&&k(e)}}}function Bs(l){let e,t,n;return{c(){e=I("div"),t=I("img"),Jt(t.src,n=l[7])||g(t,"src",n),g(t,"alt",""),g(t,"class","svelte-1emjf0g"),g(e,"class","wx-field wx-image svelte-1emjf0g"),X(e,"wx-rounded",!(l[1].color?.show&&l[0].color))},m(i,s){v(i,e,s),M(e,t)},p(i,s){s&128&&!Jt(t.src,n=i[7])&&g(t,"src",n),s&3&&X(e,"wx-rounded",!(i[1].color?.show&&i[0].color))},d(i){i&&k(e)}}}function Ws(l){let e,t=l[0].label+"",n;return{c(){e=I("span"),n=$(t)},m(i,s){v(i,e,s),M(e,n)},p(i,s){s&1&&t!==(t=i[0].label+"")&&re(n,t)},d(i){i&&k(e)}}}function Ys(l){let e,t,n,i,s;return n=new Me({props:{css:"wxi-dots-v"}}),{c(){e=I("div"),t=I("div"),P(n.$$.fragment),g(t,"data-menu-id",i=l[0].id),g(e,"class","wx-menu svelte-1emjf0g"),g(e,"data-ignore-selection","true")},m(r,a){v(r,e,a),M(e,t),R(n,t,null),s=!0},p(r,a){(!s||a&1&&i!==(i=r[0].id))&&g(t,"data-menu-id",i)},i(r){s||(b(n.$$.fragment,r),s=!0)},o(r){p(n.$$.fragment,r),s=!1},d(r){r&&k(e),A(n)}}}function Gs(l){let e,t=l[0].description+"",n;return{c(){e=I("div"),n=$(t),g(e,"class","wx-field wx-description svelte-1emjf0g")},m(i,s){v(i,e,s),M(e,n)},p(i,s){s&1&&t!==(t=i[0].description+"")&&re(n,t)},d(i){i&&k(e)}}}function Js(l){let e,t,n;return t=new tm({props:{min:l[1].progress?.config?.min||0,max:l[1].progress?.config?.max||100,value:l[0].progress}}),{c(){e=I("div"),P(t.$$.fragment),g(e,"class","wx-field svelte-1emjf0g")},m(i,s){v(i,e,s),R(t,e,null),n=!0},p(i,s){const r={};s&2&&(r.min=i[1].progress?.config?.min||0),s&2&&(r.max=i[1].progress?.config?.max||100),s&1&&(r.value=i[0].progress),t.$set(r)},i(i){n||(b(t.$$.fragment,i),n=!0)},o(i){p(t.$$.fragment,i),n=!1},d(i){i&&k(e),A(t)}}}function wm(l){let e,t,n,i,s,r,a,o,u,f,c,d,m,_,h=l[1].color?.show&&l[0].color&&Ks(l),w=l[1]?.cover?.show&&l[7]&&Bs(l);i=new pm({props:{cardFields:l[0],cardShape:l[1]}});let y=l[1]?.label?.show&&l[0].label&&Ws(l),S=l[2]&&Ys(l),C=l[1]?.description?.show&&l[0].description&&Gs(l),T=l[1]?.progress?.show&&l[0].progress&&Js(l);return d=new dm({props:{cardFields:l[0],cardShape:l[1],api:l[3]}}),{c(){h&&h.c(),e=H(),w&&w.c(),t=H(),n=I("div"),P(i.$$.fragment),s=H(),r=I("div"),a=I("div"),y&&y.c(),o=H(),S&&S.c(),u=H(),C&&C.c(),f=H(),T&&T.c(),c=H(),P(d.$$.fragment),g(a,"class","wx-field wx-label svelte-1emjf0g"),g(r,"class","wx-body svelte-1emjf0g"),g(n,"class",m=qe(l[6])+" svelte-1emjf0g"),X(n,"wx-selected",l[5]),X(n,"wx-dragging",l[4])},m(L,N){h&&h.m(L,N),v(L,e,N),w&&w.m(L,N),v(L,t,N),v(L,n,N),R(i,n,null),M(n,s),M(n,r),M(r,a),y&&y.m(a,null),M(a,o),S&&S.m(a,null),M(r,u),C&&C.m(r,null),M(r,f),T&&T.m(r,null),M(n,c),R(d,n,null),_=!0},p(L,[N]){L[1].color?.show&&L[0].color?h?h.p(L,N):(h=Ks(L),h.c(),h.m(e.parentNode,e)):h&&(h.d(1),h=null),L[1]?.cover?.show&&L[7]?w?w.p(L,N):(w=Bs(L),w.c(),w.m(t.parentNode,t)):w&&(w.d(1),w=null);const E={};N&1&&(E.cardFields=L[0]),N&2&&(E.cardShape=L[1]),i.$set(E),L[1]?.label?.show&&L[0].label?y?y.p(L,N):(y=Ws(L),y.c(),y.m(a,o)):y&&(y.d(1),y=null),L[2]?S?(S.p(L,N),N&4&&b(S,1)):(S=Ys(L),S.c(),b(S,1),S.m(a,null)):S&&(W(),p(S,1,1,()=>{S=null}),Y()),L[1]?.description?.show&&L[0].description?C?C.p(L,N):(C=Gs(L),C.c(),C.m(r,f)):C&&(C.d(1),C=null),L[1]?.progress?.show&&L[0].progress?T?(T.p(L,N),N&3&&b(T,1)):(T=Js(L),T.c(),b(T,1),T.m(r,null)):T&&(W(),p(T,1,1,()=>{T=null}),Y());const F={};N&1&&(F.cardFields=L[0]),N&2&&(F.cardShape=L[1]),N&8&&(F.api=L[3]),d.$set(F),(!_||N&64&&m!==(m=qe(L[6])+" svelte-1emjf0g"))&&g(n,"class",m),(!_||N&96)&&X(n,"wx-selected",L[5]),(!_||N&80)&&X(n,"wx-dragging",L[4])},i(L){_||(b(i.$$.fragment,L),b(S),b(T),b(d.$$.fragment,L),_=!0)},o(L){p(i.$$.fragment,L),p(S),p(T),p(d.$$.fragment,L),_=!1},d(L){L&&(k(e),k(t),k(n)),h&&h.d(L),w&&w.d(L),A(i),y&&y.d(),S&&S.d(),C&&C.d(),T&&T.d(),A(d)}}}function km(l,e){let t="wx-content";return l.css&&(t+=" "+l.css),e.css?()=>t+=` ${e.css(l)}`:()=>t}function vm(l,e,t){let n,i,s,{cardFields:r}=e,{cardShape:a}=e,{menu:o=!0}=e,{api:u}=e,{dragging:f}=e,{selected:c}=e;return l.$$set=d=>{"cardFields"in d&&t(0,r=d.cardFields),"cardShape"in d&&t(1,a=d.cardShape),"menu"in d&&t(2,o=d.menu),"api"in d&&t(3,u=d.api),"dragging"in d&&t(4,f=d.dragging),"selected"in d&&t(5,c=d.selected)},l.$$.update=()=>{l.$$.dirty&1&&t(8,n=r?.attached?.find(d=>d.isCover)),l.$$.dirty&256&&t(7,i=n?n.coverURL||n.url:null),l.$$.dirty&3&&t(6,s=km(r,a)())},[r,a,o,u,f,c,s,i,n]}class Lt extends ee{constructor(e){super(),x(this,e,vm,wm,Z,{cardFields:0,cardShape:1,menu:2,api:3,dragging:4,selected:5})}}function ym(l){let e,t,n,i,s;var r=l[4];function a(o,u){return{props:{api:o[7],cardFields:o[0],dragging:o[1],selected:o[2],cardShape:o[3],menu:o[6]}}}return r&&(t=Je(r,a(l)),t.$on("action",l[10])),{c(){e=I("div"),t&&P(t.$$.fragment),g(e,"class","wx-card svelte-7hhwxe"),g(e,"data-drag-item",n=l[0].id),ce(e,"height",l[8]),ce(e,"max-height",l[8]),g(e,"data-id",i=l[0].id),X(e,"wx-hidden",l[1]),X(e,"wx-selected",l[2]),X(e,"wx-dimmed",l[5]?.dimmed)},m(o,u){v(o,e,u),t&&R(t,e,null),s=!0},p(o,[u]){if(u&16&&r!==(r=o[4])){if(t){W();const f=t;p(f.$$.fragment,1,0,()=>{A(f,1)}),Y()}r?(t=Je(r,a(o)),t.$on("action",o[10]),P(t.$$.fragment),b(t.$$.fragment,1),R(t,e,null)):t=null}else if(r){const f={};u&128&&(f.api=o[7]),u&1&&(f.cardFields=o[0]),u&2&&(f.dragging=o[1]),u&4&&(f.selected=o[2]),u&8&&(f.cardShape=o[3]),u&64&&(f.menu=o[6]),t.$set(f)}(!s||u&1&&n!==(n=o[0].id))&&g(e,"data-drag-item",n),(!s||u&256)&&ce(e,"height",o[8]),(!s||u&256)&&ce(e,"max-height",o[8]),(!s||u&1&&i!==(i=o[0].id))&&g(e,"data-id",i),(!s||u&2)&&X(e,"wx-hidden",o[1]),(!s||u&4)&&X(e,"wx-selected",o[2]),(!s||u&32)&&X(e,"wx-dimmed",o[5]?.dimmed)},i(o){s||(t&&b(t.$$.fragment,o),s=!0)},o(o){t&&p(t.$$.fragment,o),s=!1},d(o){o&&k(e),t&&A(t)}}}function Sm(l,e,t){let n,{cardFields:i}=e,{dragging:s=!1}=e,{selected:r=!1}=e,{cardShape:a}=e,{cardTemplate:o}=e,{meta:u=null}=e,{cardHeight:f}=e,{menu:c}=e,{api:d}=e;function m(_){ye.call(this,l,_)}return l.$$set=_=>{"cardFields"in _&&t(0,i=_.cardFields),"dragging"in _&&t(1,s=_.dragging),"selected"in _&&t(2,r=_.selected),"cardShape"in _&&t(3,a=_.cardShape),"cardTemplate"in _&&t(4,o=_.cardTemplate),"meta"in _&&t(5,u=_.meta),"cardHeight"in _&&t(9,f=_.cardHeight),"menu"in _&&t(6,c=_.menu),"api"in _&&t(7,d=_.api)},l.$$.update=()=>{l.$$.dirty&512&&t(8,n=f?f+"px":"auto")},[i,s,r,a,o,u,c,d,n,f,m]}class Wn extends ee{constructor(e){super(),x(this,e,Sm,ym,Z,{cardFields:0,dragging:1,selected:2,cardShape:3,cardTemplate:4,meta:5,cardHeight:9,menu:6,api:7})}}function Cm(l){let e,t,n=l[0].label+"",i;return{c(){e=I("div"),t=I("div"),i=$(n),g(t,"class","wx-label-text svelte-1betjxn"),g(e,"class","wx-collapsed-label svelte-1betjxn")},m(s,r){v(s,e,r),M(e,t),M(t,i)},p(s,[r]){r&1&&n!==(n=s[0].label+"")&&re(i,n)},i:K,o:K,d(s){s&&k(e)}}}function Im(l,e,t){let{column:n}=e,{columnState:i}=e;return l.$$set=s=>{"column"in s&&t(0,n=s.column),"columnState"in s&&t(1,i=s.columnState)},[n,i]}class Mm extends ee{constructor(e){super(),x(this,e,Im,Cm,Z,{column:0,columnState:1})}}const Tm=l=>({}),Qs=l=>({});function Xs(l,e,t){const n=l.slice();return n[27]=e[t],n}const Dm=l=>({item:l&16}),Zs=l=>({item:l[27].data});function $s(l,e){let t,n,i,s,r;const a=e[15].default,o=be(a,e,e[14],Zs);return{key:l,first:null,c(){t=I("div"),o&&o.c(),n=H(),g(t,"class","wx-item svelte-12ih14s"),g(t,"data-id",i=e[27].data.id),g(t,"data-index",s=e[27].index),this.first=t},m(u,f){v(u,t,f),o&&o.m(t,null),M(t,n),r=!0},p(u,f){e=u,o&&o.p&&(!r||f&16400)&&we(o,a,e,e[14],r?pe(a,e[14],f,Dm):ke(e[14]),Zs),(!r||f&16&&i!==(i=e[27].data.id))&&g(t,"data-id",i),(!r||f&16&&s!==(s=e[27].index))&&g(t,"data-index",s)},i(u){r||(b(o,u),r=!0)},o(u){p(o,u),r=!1},d(u){u&&k(t),o&&o.d(u)}}}function Em(l){let e,t,n=[],i=new Map,s,r,a,o,u,f=fe(l[4]);const c=_=>_[27].index;for(let _=0;_<f.length;_+=1){let h=Xs(l,f,_),w=c(h);i.set(w,n[_]=$s(w,h))}const d=l[15].extra,m=be(d,l,l[14],Qs);return{c(){e=I("div"),t=I("div");for(let _=0;_<n.length;_+=1)n[_].c();s=H(),m&&m.c(),g(t,"class","wx-content svelte-12ih14s"),ce(t,"padding-top",l[5]+"px"),ce(t,"padding-bottom",l[6]+"px"),g(e,"class","wx-virtual-list svelte-12ih14s"),ce(e,"height",l[0]),ut(()=>l[18].call(e))},m(_,h){v(_,e,h),M(e,t);for(let w=0;w<n.length;w+=1)n[w]&&n[w].m(t,null);l[16](t),M(e,s),m&&m.m(e,null),l[17](e),r=oa(e,l[18].bind(e)),a=!0,o||(u=[te(e,"scroll",l[7]),He(Un.call(null,e))],o=!0)},p(_,[h]){h&16400&&(f=fe(_[4]),W(),n=Oe(n,h,c,1,_,f,i,t,Ue,$s,null,Xs),Y()),(!a||h&32)&&ce(t,"padding-top",_[5]+"px"),(!a||h&64)&&ce(t,"padding-bottom",_[6]+"px"),m&&m.p&&(!a||h&16384)&&we(m,d,_,_[14],a?pe(d,_[14],h,Tm):ke(_[14]),Qs),(!a||h&1)&&ce(e,"height",_[0])},i(_){if(!a){for(let h=0;h<f.length;h+=1)b(n[h]);b(m,_),a=!0}},o(_){for(let h=0;h<n.length;h+=1)p(n[h]);p(m,_),a=!1},d(_){_&&k(e);for(let h=0;h<n.length;h+=1)n[h].d();l[16](null),m&&m.d(_),l[17](null),r(),o=!1,De(u)}}}function Lm(l,e,t){let{$$slots:n={},$$scope:i}=e,{items:s}=e,{scrollToId:r}=e,{height:a="100%"}=e,{itemHeight:o=void 0}=e,{start:u=0}=e,{end:f=0}=e;const c=Ee();let d=[],m,_,h,w=0,y,S=!1,C=0,T=0,L;async function N(z){if(_.querySelector(`[data-id="${z}"]`))return;const J=s.findIndex(V=>V.id===z);J>-1&&(t(2,_.scrollTop=(J+1)*L-w/2,_),G(),z=null)}async function E(z,q,J,V){await ct(),d.length=z.length,d.fill(0);const{scrollTop:ne}=_;let Q=C-ne;for(let B=u;B<z.length;B++){if(Q>q){t(9,f=B);break}let ie=m[B-u];ie||(t(9,f=B+1),await ct(),ie=m[B-u]);const ge=J||ie.offsetHeight;d[B]=ge,Q+=ge}L=Math.round((C+Q)/f),F(L),await ct(),V&&N(V)}function F(z){const q=s.length-f;t(6,T=q*z)}async function G(){const{scrollTop:z}=_;y.forEach((V,ne)=>{const{index:Q}=V;d[Q]=o||m[ne].offsetHeight});let q=0,J=0;for(;q<s.length;){const V=d[q]||L;if(J+V>z){t(8,u=q),t(5,C=J);break}J+=V,q+=1}for(;q<s.length&&(J+=d[q]||L,q+=1,!(J>z+w)););t(9,f=q),L=Math.round(J/f),F(L)}async function U(){G(),c("scroll",{start:u,end:f})}ot(()=>{m=h.children,t(13,S=!0)});function O(z){ue[z?"unshift":"push"](()=>{h=z,t(3,h)})}function D(z){ue[z?"unshift":"push"](()=>{_=z,t(2,_)})}function j(){w=this.offsetHeight,t(1,w)}return l.$$set=z=>{"items"in z&&t(10,s=z.items),"scrollToId"in z&&t(11,r=z.scrollToId),"height"in z&&t(0,a=z.height),"itemHeight"in z&&t(12,o=z.itemHeight),"start"in z&&t(8,u=z.start),"end"in z&&t(9,f=z.end),"$$scope"in z&&t(14,i=z.$$scope)},l.$$.update=()=>{l.$$.dirty&1792&&t(4,y=s.slice(u,f).map((z,q)=>({index:q+u,data:z}))),l.$$.dirty&15362&&S&&E(s,w,o,r)},[a,w,_,h,y,C,T,U,u,f,s,r,o,S,i,n,O,D,j]}class Rm extends ee{constructor(e){super(),x(this,e,Lm,Em,Z,{items:10,scrollToId:11,height:0,itemHeight:12,start:8,end:9})}}function xs(l){let e,t,n;return t=new Rm({props:{items:l[13],scrollToId:l[14],$$slots:{extra:[Pm],default:[Am,({item:i})=>({18:i}),({item:i})=>i?262144:0]},$$scope:{ctx:l}}}),{c(){e=I("div"),P(t.$$.fragment),g(e,"class","wx-list-wrapper svelte-tuxumq"),g(e,"data-id","scroll-column"),X(e,"wx-not-anchored",l[2])},m(i,s){v(i,e,s),R(t,e,null),n=!0},p(i,s){const r={};s&8192&&(r.items=i[13]),s&16384&&(r.scrollToId=i[14]),s&793598&&(r.$$scope={dirty:s,ctx:i}),t.$set(r),(!n||s&4)&&X(e,"wx-not-anchored",i[2])},i(i){n||(b(t.$$.fragment,i),n=!0)},o(i){p(t.$$.fragment,i),n=!1},d(i){i&&k(e),A(t)}}}function er(l){let e;return{c(){e=I("div"),g(e,"class","wx-drop-area svelte-tuxumq"),ce(e,"min-height",l[8]+"px")},m(t,n){v(t,e,n)},p(t,n){n&256&&ce(e,"min-height",t[8]+"px")},d(t){t&&k(e)}}}function Am(l){let e=le(l[18].id,l[1]),t,n,i,s=e&&er(l);return n=new Wn({props:{api:l[12],cardTemplate:l[5]||Lt,cardFields:l[18],cardHeight:l[7],dragging:l[6][l[18].id]?.dragging,selected:ht(l[3],l[18].id),meta:l[6]&&l[6][l[18].id],cardShape:l[4],menu:l[11][l[18].id]}}),n.$on("action",l[17]),{c(){s&&s.c(),t=H(),P(n.$$.fragment)},m(r,a){s&&s.m(r,a),v(r,t,a),R(n,r,a),i=!0},p(r,a){a&262146&&(e=le(r[18].id,r[1])),e?s?s.p(r,a):(s=er(r),s.c(),s.m(t.parentNode,t)):s&&(s.d(1),s=null);const o={};a&4096&&(o.api=r[12]),a&32&&(o.cardTemplate=r[5]||Lt),a&262144&&(o.cardFields=r[18]),a&128&&(o.cardHeight=r[7]),a&262208&&(o.dragging=r[6][r[18].id]?.dragging),a&262152&&(o.selected=ht(r[3],r[18].id)),a&262208&&(o.meta=r[6]&&r[6][r[18].id]),a&16&&(o.cardShape=r[4]),a&264192&&(o.menu=r[11][r[18].id]),n.$set(o)},i(r){i||(b(n.$$.fragment,r),i=!0)},o(r){p(n.$$.fragment,r),i=!1},d(r){r&&k(t),s&&s.d(r),A(n,r)}}}function tr(l){let e;return{c(){e=I("div"),g(e,"class","wx-drop-area svelte-tuxumq"),ce(e,"min-height",l[8]+"px")},m(t,n){v(t,e,n)},p(t,n){n&256&&ce(e,"min-height",t[8]+"px")},d(t){t&&k(e)}}}function Pm(l){let e,t=!l[1]&&le(l[2],l[9]),n=t&&tr(l);return{c(){e=I("div"),n&&n.c(),g(e,"slot","extra")},m(i,s){v(i,e,s),n&&n.m(e,null)},p(i,s){s&518&&(t=!i[1]&&le(i[2],i[9])),t?n?n.p(i,s):(n=tr(i),n.c(),n.m(e,null)):n&&(n.d(1),n=null)},d(i){i&&k(e),n&&n.d()}}}function Nm(l){let e,t,n=l[0]&&xs(l);return{c(){n&&n.c(),e=se()},m(i,s){n&&n.m(i,s),v(i,e,s),t=!0},p(i,[s]){i[0]?n?(n.p(i,s),s&1&&b(n,1)):(n=xs(i),n.c(),b(n,1),n.m(e.parentNode,e)):n&&(W(),p(n,1,1,()=>{n=null}),Y())},i(i){t||(b(n),t=!0)},o(i){p(n),t=!1},d(i){i&&k(e),n&&n.d(i)}}}function zm(l,e,t){let n,i,s=K,r=()=>(s(),s=Ge(S,E=>t(16,i=E)),S);l.$$.on_destroy.push(()=>s());let{cards:a}=e,{overCardId:o}=e,{overColId:u}=e,{movedCardId:f}=e,{selected:c}=e,{cardShape:d}=e,{cardTemplate:m=null}=e,{cardsMeta:_={}}=e,{cardHeight:h=null}=e,{movedCardHeight:w=0}=e,{areaId:y}=e,{scrollTo:S}=e;r();let{isMenuVisible:C}=e,{api:T}=e,L;function N(E){ye.call(this,l,E)}return l.$$set=E=>{"cards"in E&&t(0,a=E.cards),"overCardId"in E&&t(1,o=E.overCardId),"overColId"in E&&t(2,u=E.overColId),"movedCardId"in E&&t(15,f=E.movedCardId),"selected"in E&&t(3,c=E.selected),"cardShape"in E&&t(4,d=E.cardShape),"cardTemplate"in E&&t(5,m=E.cardTemplate),"cardsMeta"in E&&t(6,_=E.cardsMeta),"cardHeight"in E&&t(7,h=E.cardHeight),"movedCardHeight"in E&&t(8,w=E.movedCardHeight),"areaId"in E&&t(9,y=E.areaId),"scrollTo"in E&&r(t(10,S=E.scrollTo)),"isMenuVisible"in E&&t(11,C=E.isMenuVisible),"api"in E&&t(12,T=E.api)},l.$$.update=()=>{l.$$.dirty&32769&&t(13,n=a.filter(E=>!le(f,E.id))),l.$$.dirty&77824&&i&&i.to==="card"&&i.id&&n.find(E=>le(E.id,i.id))&&(t(14,L=i.id),T.exec("scroll",null))},[a,o,u,c,d,m,_,h,w,y,S,C,T,n,L,f,i,N]}class Hm extends ee{constructor(e){super(),x(this,e,zm,Nm,Z,{cards:0,overCardId:1,overColId:2,movedCardId:15,selected:3,cardShape:4,cardTemplate:5,cardsMeta:6,cardHeight:7,movedCardHeight:8,areaId:9,scrollTo:10,isMenuVisible:11,api:12})}}function nr(l,e,t){const n=l.slice();return n[13]=e[t],n}function lr(l){let e,t,n,i,s=l[0]&&ir(l);return{c(){e=I("div"),s&&s.c(),g(e,"class","wx-list-wrapper svelte-1mw05qg"),X(e,"wx-not-anchored",l[2])},m(r,a){v(r,e,a),s&&s.m(e,null),t=!0,n||(i=He(Un.call(null,e)),n=!0)},p(r,a){r[0]?s?(s.p(r,a),a&1&&b(s,1)):(s=ir(r),s.c(),b(s,1),s.m(e,null)):s&&(W(),p(s,1,1,()=>{s=null}),Y()),(!t||a&4)&&X(e,"wx-not-anchored",r[2])},i(r){t||(b(s),t=!0)},o(r){p(s),t=!1},d(r){r&&k(e),s&&s.d(),n=!1,i()}}}function ir(l){let e=[],t=new Map,n,i=!l[1]&&le(l[2],l[9]),s,r,a=fe(l[0]);const o=f=>f[13].id;for(let f=0;f<a.length;f+=1){let c=nr(l,a,f),d=o(c);t.set(d,e[f]=rr(d,c))}let u=i&&or(l);return{c(){for(let f=0;f<e.length;f+=1)e[f].c();n=H(),u&&u.c(),s=se()},m(f,c){for(let d=0;d<e.length;d+=1)e[d]&&e[d].m(f,c);v(f,n,c),u&&u.m(f,c),v(f,s,c),r=!0},p(f,c){c&3579&&(a=fe(f[0]),W(),e=Oe(e,c,o,1,f,a,t,n.parentNode,Ue,rr,n,nr),Y()),c&518&&(i=!f[1]&&le(f[2],f[9])),i?u?u.p(f,c):(u=or(f),u.c(),u.m(s.parentNode,s)):u&&(u.d(1),u=null)},i(f){if(!r){for(let c=0;c<a.length;c+=1)b(e[c]);r=!0}},o(f){for(let c=0;c<e.length;c+=1)p(e[c]);r=!1},d(f){f&&(k(n),k(s));for(let c=0;c<e.length;c+=1)e[c].d(f);u&&u.d(f)}}}function sr(l){let e;return{c(){e=I("div"),g(e,"class","wx-drop-area svelte-1mw05qg"),ce(e,"min-height",l[8]+"px")},m(t,n){v(t,e,n)},p(t,n){n&256&&ce(e,"min-height",t[8]+"px")},d(t){t&&k(e)}}}function rr(l,e){let t,n=le(e[13].id,e[1]),i,s,r,a=n&&sr(e);return s=new Wn({props:{api:e[11],cardTemplate:e[5]||Lt,cardFields:e[13],cardHeight:e[7],dragging:e[6][e[13].id]?.dragging,selected:ht(e[3],e[13].id),meta:e[6]&&e[6][e[13].id],cardShape:e[4],menu:e[10][e[13].id]}}),s.$on("action",e[12]),{key:l,first:null,c(){t=se(),a&&a.c(),i=H(),P(s.$$.fragment),this.first=t},m(o,u){v(o,t,u),a&&a.m(o,u),v(o,i,u),R(s,o,u),r=!0},p(o,u){e=o,u&3&&(n=le(e[13].id,e[1])),n?a?a.p(e,u):(a=sr(e),a.c(),a.m(i.parentNode,i)):a&&(a.d(1),a=null);const f={};u&2048&&(f.api=e[11]),u&32&&(f.cardTemplate=e[5]||Lt),u&1&&(f.cardFields=e[13]),u&128&&(f.cardHeight=e[7]),u&65&&(f.dragging=e[6][e[13].id]?.dragging),u&9&&(f.selected=ht(e[3],e[13].id)),u&65&&(f.meta=e[6]&&e[6][e[13].id]),u&16&&(f.cardShape=e[4]),u&1025&&(f.menu=e[10][e[13].id]),s.$set(f)},i(o){r||(b(s.$$.fragment,o),r=!0)},o(o){p(s.$$.fragment,o),r=!1},d(o){o&&(k(t),k(i)),a&&a.d(o),A(s,o)}}}function or(l){let e;return{c(){e=I("div"),g(e,"class","wx-drop-area svelte-1mw05qg"),ce(e,"min-height",l[8]+"px")},m(t,n){v(t,e,n)},p(t,n){n&256&&ce(e,"min-height",t[8]+"px")},d(t){t&&k(e)}}}function Om(l){let e,t,n=l[0]&&lr(l);return{c(){n&&n.c(),e=se()},m(i,s){n&&n.m(i,s),v(i,e,s),t=!0},p(i,[s]){i[0]?n?(n.p(i,s),s&1&&b(n,1)):(n=lr(i),n.c(),b(n,1),n.m(e.parentNode,e)):n&&(W(),p(n,1,1,()=>{n=null}),Y())},i(i){t||(b(n),t=!0)},o(i){p(n),t=!1},d(i){i&&k(e),n&&n.d(i)}}}function Fm(l,e,t){let{cards:n}=e,{overCardId:i}=e,{overColId:s}=e,{selected:r}=e,{cardShape:a}=e,{cardTemplate:o=null}=e,{cardsMeta:u={}}=e,{cardHeight:f=null}=e,{movedCardHeight:c=0}=e,{areaId:d}=e,{isMenuVisible:m}=e,{api:_}=e;function h(w){ye.call(this,l,w)}return l.$$set=w=>{"cards"in w&&t(0,n=w.cards),"overCardId"in w&&t(1,i=w.overCardId),"overColId"in w&&t(2,s=w.overColId),"selected"in w&&t(3,r=w.selected),"cardShape"in w&&t(4,a=w.cardShape),"cardTemplate"in w&&t(5,o=w.cardTemplate),"cardsMeta"in w&&t(6,u=w.cardsMeta),"cardHeight"in w&&t(7,f=w.cardHeight),"movedCardHeight"in w&&t(8,c=w.movedCardHeight),"areaId"in w&&t(9,d=w.areaId),"isMenuVisible"in w&&t(10,m=w.isMenuVisible),"api"in w&&t(11,_=w.api)},[n,i,s,r,a,o,u,f,c,d,m,_,h]}class qm extends ee{constructor(e){super(),x(this,e,Fm,Om,Z,{cards:0,overCardId:1,overColId:2,selected:3,cardShape:4,cardTemplate:5,cardsMeta:6,cardHeight:7,movedCardHeight:8,areaId:9,isMenuVisible:10,api:11})}}function ar(l,e,t){const n=l.slice();return n[55]=e[t],n}function jm(l){let e,t;return e=new Mm({props:{column:l[0],columnState:l[1]}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i[0]&1&&(s.column=n[0]),i[0]&2&&(s.columnState=n[1]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Um(l){let e,t,n;var i=l[7].collapsedTemplate;function s(r,a){return{props:{column:r[0],columnState:r[1]}}}return i&&(e=Je(i,s(l))),{c(){e&&P(e.$$.fragment),t=se()},m(r,a){e&&R(e,r,a),v(r,t,a),n=!0},p(r,a){if(a[0]&128&&i!==(i=r[7].collapsedTemplate)){if(e){W();const o=e;p(o.$$.fragment,1,0,()=>{A(o,1)}),Y()}i?(e=Je(i,s(r)),P(e.$$.fragment),b(e.$$.fragment,1),R(e,t.parentNode,t)):e=null}else if(i){const o={};a[0]&1&&(o.column=r[0]),a[0]&2&&(o.columnState=r[1]),e.$set(o)}},i(r){n||(e&&b(e.$$.fragment,r),n=!0)},o(r){e&&p(e.$$.fragment,r),n=!1},d(r){r&&k(t),e&&A(e,r)}}}function Vm(l){let e,t,n,i,s,r;const a=[Wm,Bm,Km],o=[];function u(d,m){return d[13]==="column:lazy"?0:d[13]==="column:default"?1:2}e=u(l),t=o[e]=a[e](l);let f=l[20]&&(typeof l[0].limit=="object"?!l[1].noFreeSpace:!l[2].noFreeSpace)&&mr(l),c=l[1].rowId&&typeof l[0].limit=="object"&&l[1].totalLimit&&_r(l);return{c(){t.c(),n=H(),i=I("div"),f&&f.c(),s=H(),c&&c.c(),g(i,"class","wx-controls-wrapper svelte-10x9r2")},m(d,m){o[e].m(d,m),v(d,n,m),v(d,i,m),f&&f.m(i,null),M(i,s),c&&c.m(i,null),r=!0},p(d,m){let _=e;e=u(d),e===_?o[e].p(d,m):(W(),p(o[_],1,1,()=>{o[_]=null}),Y(),t=o[e],t?t.p(d,m):(t=o[e]=a[e](d),t.c()),b(t,1),t.m(n.parentNode,n)),d[20]&&(typeof d[0].limit=="object"?!d[1].noFreeSpace:!d[2].noFreeSpace)?f?(f.p(d,m),m[0]&1048583&&b(f,1)):(f=mr(d),f.c(),b(f,1),f.m(i,s)):f&&(W(),p(f,1,1,()=>{f=null}),Y()),d[1].rowId&&typeof d[0].limit=="object"&&d[1].totalLimit?c?c.p(d,m):(c=_r(d),c.c(),c.m(i,null)):c&&(c.d(1),c=null)},i(d){r||(b(t),b(f),r=!0)},o(d){p(t),p(f),r=!1},d(d){d&&(k(n),k(i)),o[e].d(d),f&&f.d(),c&&c.d()}}}function Km(l){let e,t=!l[16]&&le(l[15],l[11]),n,i,s=l[3]&&ur(l),r=t&&dr(l);return{c(){s&&s.c(),e=H(),r&&r.c(),n=se()},m(a,o){s&&s.m(a,o),v(a,e,o),r&&r.m(a,o),v(a,n,o),i=!0},p(a,o){a[3]?s?(s.p(a,o),o[0]&8&&b(s,1)):(s=ur(a),s.c(),b(s,1),s.m(e.parentNode,e)):s&&(W(),p(s,1,1,()=>{s=null}),Y()),o[0]&100352&&(t=!a[16]&&le(a[15],a[11])),t?r?r.p(a,o):(r=dr(a),r.c(),r.m(n.parentNode,n)):r&&(r.d(1),r=null)},i(a){i||(b(s),i=!0)},o(a){p(s),i=!1},d(a){a&&(k(e),k(n)),s&&s.d(a),r&&r.d(a)}}}function Bm(l){let e,t;return e=new qm({props:{api:l[5],cards:l[3],overCardId:l[16],overColId:l[15],selected:l[19],cardShape:l[18],cardTemplate:l[4],cardsMeta:l[17],movedCardHeight:l[9],areaId:l[11],cardHeight:l[12],isMenuVisible:l[6]}}),e.$on("action",l[51]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i[0]&32&&(s.api=n[5]),i[0]&8&&(s.cards=n[3]),i[0]&65536&&(s.overCardId=n[16]),i[0]&32768&&(s.overColId=n[15]),i[0]&524288&&(s.selected=n[19]),i[0]&262144&&(s.cardShape=n[18]),i[0]&16&&(s.cardTemplate=n[4]),i[0]&131072&&(s.cardsMeta=n[17]),i[0]&512&&(s.movedCardHeight=n[9]),i[0]&2048&&(s.areaId=n[11]),i[0]&4096&&(s.cardHeight=n[12]),i[0]&64&&(s.isMenuVisible=n[6]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Wm(l){let e,t;return e=new Hm({props:{api:l[5],cards:l[3],overCardId:l[16],overColId:l[15],movedCardId:l[14],selected:l[19],cardShape:l[18],cardTemplate:l[4],cardsMeta:l[17],movedCardHeight:l[9],areaId:l[11],scrollTo:l[25],cardHeight:l[12],isMenuVisible:l[6]}}),e.$on("action",l[50]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i[0]&32&&(s.api=n[5]),i[0]&8&&(s.cards=n[3]),i[0]&65536&&(s.overCardId=n[16]),i[0]&32768&&(s.overColId=n[15]),i[0]&16384&&(s.movedCardId=n[14]),i[0]&524288&&(s.selected=n[19]),i[0]&262144&&(s.cardShape=n[18]),i[0]&16&&(s.cardTemplate=n[4]),i[0]&131072&&(s.cardsMeta=n[17]),i[0]&512&&(s.movedCardHeight=n[9]),i[0]&2048&&(s.areaId=n[11]),i[0]&4096&&(s.cardHeight=n[12]),i[0]&64&&(s.isMenuVisible=n[6]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function ur(l){let e=[],t=new Map,n,i,s=fe(l[3]);const r=a=>a[55].id;for(let a=0;a<s.length;a+=1){let o=ar(l,s,a),u=r(o);t.set(u,e[a]=cr(u,o))}return{c(){for(let a=0;a<e.length;a+=1)e[a].c();n=se()},m(a,o){for(let u=0;u<e.length;u+=1)e[u]&&e[u].m(a,o);v(a,n,o),i=!0},p(a,o){o[0]&1022584&&(s=fe(a[3]),W(),e=Oe(e,o,r,1,a,s,t,n.parentNode,Ue,cr,n,ar),Y())},i(a){if(!i){for(let o=0;o<s.length;o+=1)b(e[o]);i=!0}},o(a){for(let o=0;o<e.length;o+=1)p(e[o]);i=!1},d(a){a&&k(n);for(let o=0;o<e.length;o+=1)e[o].d(a)}}}function fr(l){let e;return{c(){e=I("div"),g(e,"class","wx-drop-area svelte-10x9r2"),ce(e,"min-height",l[9]+"px")},m(t,n){v(t,e,n)},p(t,n){n[0]&512&&ce(e,"min-height",t[9]+"px")},d(t){t&&k(e)}}}function cr(l,e){let t,n=le(e[55].id,e[16])&&le(e[15],e[11]),i,s,r,a=n&&fr(e);return s=new Wn({props:{api:e[5],cardTemplate:e[4]||Lt,cardFields:e[55],dragging:e[17][e[55].id]?.dragging,selected:ht(e[19],e[55].id),meta:e[17]&&e[17][e[55].id],cardShape:e[18],cardHeight:e[12],menu:e[6][e[55].id]}}),s.$on("action",e[52]),{key:l,first:null,c(){t=se(),a&&a.c(),i=H(),P(s.$$.fragment),this.first=t},m(o,u){v(o,t,u),a&&a.m(o,u),v(o,i,u),R(s,o,u),r=!0},p(o,u){e=o,u[0]&100360&&(n=le(e[55].id,e[16])&&le(e[15],e[11])),n?a?a.p(e,u):(a=fr(e),a.c(),a.m(i.parentNode,i)):a&&(a.d(1),a=null);const f={};u[0]&32&&(f.api=e[5]),u[0]&16&&(f.cardTemplate=e[4]||Lt),u[0]&8&&(f.cardFields=e[55]),u[0]&131080&&(f.dragging=e[17][e[55].id]?.dragging),u[0]&524296&&(f.selected=ht(e[19],e[55].id)),u[0]&131080&&(f.meta=e[17]&&e[17][e[55].id]),u[0]&262144&&(f.cardShape=e[18]),u[0]&4096&&(f.cardHeight=e[12]),u[0]&72&&(f.menu=e[6][e[55].id]),s.$set(f)},i(o){r||(b(s.$$.fragment,o),r=!0)},o(o){p(s.$$.fragment,o),r=!1},d(o){o&&(k(t),k(i)),a&&a.d(o),A(s,o)}}}function dr(l){let e;return{c(){e=I("div"),g(e,"class","wx-drop-area svelte-10x9r2"),ce(e,"min-height",l[9]+"px")},m(t,n){v(t,e,n)},p(t,n){n[0]&512&&ce(e,"min-height",t[9]+"px")},d(t){t&&k(e)}}}function mr(l){let e,t,n,i,s,r,a;return t=new Me({props:{css:"wxi-plus"}}),{c(){e=I("div"),P(t.$$.fragment),n=H(),i=I("span"),i.textContent=`${l[21]("Add new card...")}`,g(i,"class","wx-add-card-tip svelte-10x9r2"),g(e,"class","wx-add-card-btn svelte-10x9r2")},m(o,u){v(o,e,u),R(t,e,null),M(e,n),M(e,i),s=!0,r||(a=te(e,"click",l[35]),r=!0)},p:K,i(o){s||(b(t.$$.fragment,o),s=!0)},o(o){p(t.$$.fragment,o),s=!1},d(o){o&&k(e),A(t),r=!1,a()}}}function _r(l){let e,t=l[1].cardsCount+"",n,i,s=l[1].totalLimit+"",r;return{c(){e=I("div"),n=$(t),i=$("/"),r=$(s),g(e,"class","wx-swimlane-limit svelte-10x9r2")},m(a,o){v(a,e,o),M(e,n),M(e,i),M(e,r)},p(a,o){o[0]&2&&t!==(t=a[1].cardsCount+"")&&re(n,t),o[0]&2&&s!==(s=a[1].totalLimit+"")&&re(r,s)},d(a){a&&k(e)}}}function hr(l){let e,t,n;var i=l[0].overlay;function s(r,a){return{}}return i&&(e=Je(i,s())),{c(){e&&P(e.$$.fragment),t=se()},m(r,a){e&&R(e,r,a),v(r,t,a),n=!0},p(r,a){if(a[0]&1&&i!==(i=r[0].overlay)){if(e){W();const o=e;p(o.$$.fragment,1,0,()=>{A(o,1)}),Y()}i?(e=Je(i,s()),P(e.$$.fragment),b(e.$$.fragment,1),R(e,t.parentNode,t)):e=null}},i(r){n||(e&&b(e.$$.fragment,r),n=!0)},o(r){e&&p(e.$$.fragment,r),n=!1},d(r){r&&k(t),e&&A(e,r)}}}function Ym(l){let e,t,n,i,s,r;const a=[Vm,Um,jm],o=[];function u(c,d){return c[0].collapsed?c[7].collapsedTemplate?1:2:0}t=u(l),n=o[t]=a[t](l);let f=l[0].overlay&&hr(l);return{c(){e=I("div"),n.c(),i=H(),f&&f.c(),g(e,"class",s=qe(l[8])+" svelte-10x9r2"),g(e,"data-drop-area",l[11]),ce(e,"min-height",l[10])},m(c,d){v(c,e,d),o[t].m(e,null),M(e,i),f&&f.m(e,null),r=!0},p(c,d){let m=t;t=u(c),t===m?o[t].p(c,d):(W(),p(o[m],1,1,()=>{o[m]=null}),Y(),n=o[t],n?n.p(c,d):(n=o[t]=a[t](c),n.c()),b(n,1),n.m(e,i)),c[0].overlay?f?(f.p(c,d),d[0]&1&&b(f,1)):(f=hr(c),f.c(),b(f,1),f.m(e,null)):f&&(W(),p(f,1,1,()=>{f=null}),Y()),(!r||d[0]&256&&s!==(s=qe(c[8])+" svelte-10x9r2"))&&g(e,"class",s),(!r||d[0]&2048)&&g(e,"data-drop-area",c[11]),(!r||d[0]&1024)&&ce(e,"min-height",c[10])},i(c){r||(b(n),b(f),r=!0)},o(c){p(n),p(f),r=!1},d(c){c&&k(e),o[t].d(),f&&f.d()}}}function Gm(l,e,t){let n,i,s,r,a,o,u,f,c,d,m,_,h,w,y,S,C,T,L,N,E,F,G,U,O,D,j,{column:z}=e,{areaMeta:q}=e,{columnMeta:J}=e,{row:V}=e,{cards:ne}=e,{cardTemplate:Q=null}=e,{api:B}=e,{isMenuVisible:ie}=e;const ge=ve("wx-i18n").getGroup("kanban"),{selected:Fe,cardShape:me,cardsMeta:Te,scroll:Ye,readonly:de,columnShape:Re,before:Ae,overAreaId:Pe,dragItemId:it,dragItemsCoords:Ze,layout:$e,cardHeight:tt,cardsMap:ae}=B.getReactiveState();he(l,Fe,_e=>t(47,O=_e)),he(l,me,_e=>t(46,U=_e)),he(l,Te,_e=>t(45,G=_e)),he(l,de,_e=>t(49,j=_e)),he(l,Re,_e=>t(7,C=_e)),he(l,Ae,_e=>t(44,F=_e)),he(l,Pe,_e=>t(43,E=_e)),he(l,it,_e=>t(42,N=_e)),he(l,Ze,_e=>t(48,D=_e)),he(l,$e,_e=>t(41,L=_e)),he(l,tt,_e=>t(40,T=_e)),he(l,ae,_e=>t(39,S=_e));const Qe=Ee();function oe(){Qe("action",{action:"add-card",data:{columnId:z.id,rowId:V.id,card:{label:ge("Untitled")}}})}function Ne(_e,pn,$b,xb){let Yt="wx-column";return pn.collapsed&&(Yt+=" wx-collapsed"),$b&&(Yt+=" wx-over-limit"),pn.css&&(Yt+=" "+pn.css),_e&&_e.css&&(Yt+=" "+_e.css(pn,xb)),Yt}function Ke(_e){ye.call(this,l,_e)}function Xb(_e){ye.call(this,l,_e)}function Zb(_e){ye.call(this,l,_e)}return l.$$set=_e=>{"column"in _e&&t(0,z=_e.column),"areaMeta"in _e&&t(1,q=_e.areaMeta),"columnMeta"in _e&&t(2,J=_e.columnMeta),"row"in _e&&t(36,V=_e.row),"cards"in _e&&t(3,ne=_e.cards),"cardTemplate"in _e&&t(4,Q=_e.cardTemplate),"api"in _e&&t(5,B=_e.api),"isMenuVisible"in _e&&t(6,ie=_e.isMenuVisible)},l.$$.update=()=>{l.$$.dirty[1]&262144&&t(20,n=j.add),l.$$.dirty[1]&133120&&t(37,i=D&&D[N]),l.$$.dirty[1]&65536&&t(19,s=O),l.$$.dirty[1]&32768&&t(18,r=U),l.$$.dirty[1]&16384&&t(17,a=G),l.$$.dirty[1]&8192&&t(16,o=F),l.$$.dirty[1]&4096&&t(15,u=E),l.$$.dirty[1]&2048&&t(14,f=N),l.$$.dirty[1]&1024&&t(13,c=L),l.$$.dirty[1]&512&&t(12,d=T),l.$$.dirty[0]&1|l.$$.dirty[1]&32&&t(11,m=ze(z.id,V.id)),l.$$.dirty[0]&2&&t(38,_=q?.height),l.$$.dirty[1]&128&&t(10,h=_?`${_}px`:"auto"),l.$$.dirty[1]&64&&t(9,w=i?.height||50),l.$$.dirty[0]&131|l.$$.dirty[1]&256&&t(8,y=Ne(C,z,q.isOverLimit,S[z.id]))},[z,q,J,ne,Q,B,ie,C,y,w,h,m,d,c,f,u,o,a,r,s,n,ge,Fe,me,Te,Ye,de,Re,Ae,Pe,it,Ze,$e,tt,ae,oe,V,i,_,S,T,L,N,E,F,G,U,O,D,j,Ke,Xb,Zb]}class gr extends ee{constructor(e){super(),x(this,e,Gm,Ym,Z,{column:0,areaMeta:1,columnMeta:2,row:36,cards:3,cardTemplate:4,api:5,isMenuVisible:6},null,[-1,-1])}}function br(l){let e,t=l[1].label+"",n,i,s=l[1].limit&&pr(l);return{c(){e=I("div"),n=$(t),i=H(),s&&s.c(),g(e,"class","wx-label svelte-1dnc12v"),g(e,"data-action","rename")},m(r,a){v(r,e,a),M(e,n),M(e,i),s&&s.m(e,null)},p(r,a){a&2&&t!==(t=r[1].label+"")&&re(n,t),r[1].limit?s?s.p(r,a):(s=pr(r),s.c(),s.m(e,null)):s&&(s.d(1),s=null)},d(r){r&&k(e),s&&s.d()}}}function pr(l){let e,t=l[2].cardsCount+"",n,i,s=l[2].totalLimit+"",r,a;return{c(){e=$("("),n=$(t),i=$("/"),r=$(s),a=$(")")},m(o,u){v(o,e,u),v(o,n,u),v(o,i,u),v(o,r,u),v(o,a,u)},p(o,u){u&4&&t!==(t=o[2].cardsCount+"")&&re(n,t),u&4&&s!==(s=o[2].totalLimit+"")&&re(r,s)},d(o){o&&(k(e),k(n),k(i),k(r),k(a))}}}function wr(l){let e,t,n,i;return t=new Me({props:{css:"wxi-dots-h"}}),{c(){e=I("div"),P(t.$$.fragment),g(e,"class","wx-menu svelte-1dnc12v"),g(e,"data-menu-id",n=l[1].id)},m(s,r){v(s,e,r),R(t,e,null),i=!0},p(s,r){(!i||r&2&&n!==(n=s[1].id))&&g(e,"data-menu-id",n)},i(s){i||(b(t.$$.fragment,s),i=!0)},o(s){p(t.$$.fragment,s),i=!1},d(s){s&&k(e),A(t)}}}function Jm(l){let e,t,n,i,s,r;t=new Me({props:{css:l[1].collapsed?"wxi-angle-right":"wxi-angle-left"}});let a=!l[3]&&!l[1].collapsed&&br(l),o=l[0]&&!l[4]&&!l[3]&&!l[1].collapsed&&wr(l);return{c(){e=I("div"),P(t.$$.fragment),n=H(),a&&a.c(),i=H(),o&&o.c(),s=se(),g(e,"class","wx-collapse-icon svelte-1dnc12v"),g(e,"data-action","collapse")},m(u,f){v(u,e,f),R(t,e,null),v(u,n,f),a&&a.m(u,f),v(u,i,f),o&&o.m(u,f),v(u,s,f),r=!0},p(u,[f]){const c={};f&2&&(c.css=u[1].collapsed?"wxi-angle-right":"wxi-angle-left"),t.$set(c),!u[3]&&!u[1].collapsed?a?a.p(u,f):(a=br(u),a.c(),a.m(i.parentNode,i)):a&&(a.d(1),a=null),u[0]&&!u[4]&&!u[3]&&!u[1].collapsed?o?(o.p(u,f),f&27&&b(o,1)):(o=wr(u),o.c(),b(o,1),o.m(s.parentNode,s)):o&&(W(),p(o,1,1,()=>{o=null}),Y())},i(u){r||(b(t.$$.fragment,u),b(o),r=!0)},o(u){p(t.$$.fragment,u),p(o),r=!1},d(u){u&&(k(e),k(n),k(i),k(s)),A(t),a&&a.d(u),o&&o.d(u)}}}function Qm(l,e,t){let{isMenuVisible:n}=e,{column:i}=e,{columnState:s}=e,{renaming:r=!1}=e,{readonly:a=!1}=e;return l.$$set=o=>{"isMenuVisible"in o&&t(0,n=o.isMenuVisible),"column"in o&&t(1,i=o.column),"columnState"in o&&t(2,s=o.columnState),"renaming"in o&&t(3,r=o.renaming),"readonly"in o&&t(4,a=o.readonly)},[n,i,s,r,a]}class Xm extends ee{constructor(e){super(),x(this,e,Qm,Jm,Z,{isMenuVisible:0,column:1,columnState:2,renaming:3,readonly:4})}}function kr(l){let e,t,n,i,s;return{c(){e=I("div"),g(e,"class",t="wx-collapsed-column"+(l[3].css?" "+l[3].css:"")+" svelte-13iqu94"),ce(e,"left",l[8]+"px")},m(r,a){v(r,e,a),i||(s=[te(e,"click",l[17]),He(n=rd.call(null,e,{container:l[2]}))],i=!0)},p(r,a){a&8&&t!==(t="wx-collapsed-column"+(r[3].css?" "+r[3].css:"")+" svelte-13iqu94")&&g(e,"class",t),a&256&&ce(e,"left",r[8]+"px"),n&&nt(n.update)&&a&4&&n.update.call(null,{container:r[2]})},d(r){r&&k(e),i=!1,De(s)}}}function Zm(l){let e,t;return e=new Xm({props:{column:l[3],columnState:l[4],isMenuVisible:l[5],renaming:l[0],readonly:!l[10].edit}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&8&&(s.column=n[3]),i&16&&(s.columnState=n[4]),i&32&&(s.isMenuVisible=n[5]),i&1&&(s.renaming=n[0]),i&1024&&(s.readonly=!n[10].edit),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function $m(l){let e,t,n;var i=l[6].headerTemplate;function s(r,a){return{props:{api:r[1],column:r[3],columnState:r[4],isMenuVisible:r[5],renaming:r[0],readonly:!r[10].edit}}}return i&&(e=Je(i,s(l))),{c(){e&&P(e.$$.fragment),t=se()},m(r,a){e&&R(e,r,a),v(r,t,a),n=!0},p(r,a){if(a&64&&i!==(i=r[6].headerTemplate)){if(e){W();const o=e;p(o.$$.fragment,1,0,()=>{A(o,1)}),Y()}i?(e=Je(i,s(r)),P(e.$$.fragment),b(e.$$.fragment,1),R(e,t.parentNode,t)):e=null}else if(i){const o={};a&2&&(o.api=r[1]),a&8&&(o.column=r[3]),a&16&&(o.columnState=r[4]),a&32&&(o.isMenuVisible=r[5]),a&1&&(o.renaming=r[0]),a&1024&&(o.readonly=!r[10].edit),e.$set(o)}},i(r){n||(e&&b(e.$$.fragment,r),n=!0)},o(r){e&&p(e.$$.fragment,r),n=!1},d(r){r&&k(t),e&&A(e,r)}}}function xm(l){let e;return{c(){e=I("span"),e.textContent="Trial",g(e,"class","wx-mark svelte-13iqu94"),X(e,"wx-error",qi())},m(t,n){v(t,e,n)},d(t){t&&k(e)}}}function vr(l){let e,t,n,i;return{c(){e=I("input"),g(e,"type","text"),g(e,"class","wx-input svelte-13iqu94"),e.value=t=l[3].label},m(s,r){v(s,e,r),n||(i=[te(e,"input",l[15]),te(e,"keypress",l[16]),te(e,"blur",l[14]),He(t1.call(null,e))],n=!0)},p(s,r){r&8&&t!==(t=s[3].label)&&e.value!==t&&(e.value=t)},d(s){s&&k(e),n=!1,De(i)}}}function e1(l){let e,t,n,i,s,r=!Fi(),a,o,u,f,c,d=l[3].collapsed&&l[7]&&l[2]&&kr(l);const m=[$m,Zm],_=[];function h(S,C){return S[6].headerTemplate?0:1}n=h(l),i=_[n]=m[n](l);let w=r&&xm(),y=l[0]&&vr(l);return{c(){e=I("div"),d&&d.c(),t=H(),i.c(),s=H(),w&&w.c(),a=H(),y&&y.c(),g(e,"class",o=qe(l[9])+" svelte-13iqu94"),g(e,"data-id",u=l[3].id),g(e,"data-column-header",f=l[3].id)},m(S,C){v(S,e,C),d&&d.m(e,null),M(e,t),_[n].m(e,null),M(e,s),w&&w.m(e,null),M(e,a),y&&y.m(e,null),l[19](e),c=!0},p(S,[C]){S[3].collapsed&&S[7]&&S[2]?d?d.p(S,C):(d=kr(S),d.c(),d.m(e,t)):d&&(d.d(1),d=null);let T=n;n=h(S),n===T?_[n].p(S,C):(W(),p(_[T],1,1,()=>{_[T]=null}),Y(),i=_[n],i?i.p(S,C):(i=_[n]=m[n](S),i.c()),b(i,1),i.m(e,s)),S[0]?y?y.p(S,C):(y=vr(S),y.c(),y.m(e,null)):y&&(y.d(1),y=null),(!c||C&512&&o!==(o=qe(S[9])+" svelte-13iqu94"))&&g(e,"class",o),(!c||C&8&&u!==(u=S[3].id))&&g(e,"data-id",u),(!c||C&8&&f!==(f=S[3].id))&&g(e,"data-column-header",f)},i(S){c||(b(i),c=!0)},o(S){p(i),c=!1},d(S){S&&k(e),d&&d.d(),_[n].d(),w&&w.d(),y&&y.d(),l[19](null)}}}function t1(l){l.focus()}function n1(l,e,t){let n,i,s,r,{api:a}=e,{contentEl:o}=e,{column:u}=e,{columnState:f}=e,{isMenuVisible:c}=e,{renaming:d=!1}=e;const m=Ee(),{columnShape:_,cardsMap:h,readonly:w}=a.getReactiveState();he(l,_,D=>t(6,s=D)),he(l,h,D=>t(18,i=D)),he(l,w,D=>t(10,r=D));let y=null,S;function C(){d&&y?.trim()&&m("action",{action:"update-column",data:{id:u.id,column:{label:y}}}),t(0,d=!1),y=null,m("action",{action:"close-column-input",data:{}})}function T(D){y=D.target.value}function L(D){D.charCode===13&&C()}function N(){m("action",{action:"expand-column",data:{id:u.id}})}let E=null;function F(){t(8,E=S?.offsetLeft)}vn(()=>{F()});function G(D,j,z,q){let J="wx-column";return j.collapsed&&(J+=" wx-collapsed"),z&&(J+=" wx-over-limit"),j.css&&(J+=" "+j.css),D&&D.css&&(J+=" "+D.css(j,q)),J}const U=new ResizeObserver(()=>{F()});yn(()=>{U.disconnect()});function O(D){ue[D?"unshift":"push"](()=>{S=D,t(7,S)})}return l.$$set=D=>{"api"in D&&t(1,a=D.api),"contentEl"in D&&t(2,o=D.contentEl),"column"in D&&t(3,u=D.column),"columnState"in D&&t(4,f=D.columnState),"isMenuVisible"in D&&t(5,c=D.isMenuVisible),"renaming"in D&&t(0,d=D.renaming)},l.$$.update=()=>{l.$$.dirty&262232&&t(9,n=G(s,u,f.isOverLimit,i[u.id])),l.$$.dirty&4&&o&&U.observe(o)},[d,a,o,u,f,c,s,S,E,n,r,_,h,w,C,T,L,N,i,O]}class l1 extends ee{constructor(e){super(),x(this,e,n1,e1,Z,{api:1,contentEl:2,column:3,columnState:4,isMenuVisible:5,renaming:0})}}function yr(l,e,t){const n=l.slice();return n[26]=e[t],n}function Sr(l,e){let t,n,i;return n=new l1({props:{column:e[26],isMenuVisible:e[4].menu.show&&!!e[5][e[26].id]?.length,contentEl:e[3],api:e[2],columnState:e[1][e[26].id],renaming:e[6]===e[26].id}}),n.$on("action",e[14]),{key:l,first:null,c(){t=se(),P(n.$$.fragment),this.first=t},m(s,r){v(s,t,r),R(n,s,r),i=!0},p(s,r){e=s;const a={};r&1&&(a.column=e[26]),r&49&&(a.isMenuVisible=e[4].menu.show&&!!e[5][e[26].id]?.length),r&8&&(a.contentEl=e[3]),r&4&&(a.api=e[2]),r&3&&(a.columnState=e[1][e[26].id]),r&65&&(a.renaming=e[6]===e[26].id),n.$set(a)},i(s){i||(b(n.$$.fragment,s),i=!0)},o(s){p(n.$$.fragment,s),i=!1},d(s){s&&k(t),A(n,s)}}}function i1(l){let e,t=[],n=new Map,i,s,r,a,o,u,f=fe(l[0]);const c=_=>_[26].id;for(let _=0;_<f.length;_+=1){let h=yr(l,f,_),w=c(h);n.set(w,t[_]=Sr(w,h))}function d(_){l[17](_)}let m={at:"left-bottom",options:l[8],resolver:l[13],dataKey:"menuId"};return l[7]!==void 0&&(m.handler=l[7]),s=new cn({props:m}),ue.push(()=>Se(s,"handler",d)),s.$on("click",l[12]),{c(){e=I("div");for(let _=0;_<t.length;_+=1)t[_].c();i=H(),P(s.$$.fragment),g(e,"class","wx-header svelte-r4xqi9"),X(e,"fixed",l[9])},m(_,h){v(_,e,h);for(let w=0;w<t.length;w+=1)t[w]&&t[w].m(e,null);v(_,i,h),R(s,_,h),a=!0,o||(u=[te(e,"click",function(){nt(l[7])&&l[7].apply(this,arguments)}),He(Ot.call(null,e,l[15]))],o=!0)},p(_,[h]){l=_,h&16511&&(f=fe(l[0]),W(),t=Oe(t,h,c,1,l,f,n,e,Ue,Sr,null,yr),Y()),(!a||h&512)&&X(e,"fixed",l[9]);const w={};h&256&&(w.options=l[8]),!r&&h&128&&(r=!0,w.handler=l[7],Ie(()=>r=!1)),s.$set(w)},i(_){if(!a){for(let h=0;h<f.length;h+=1)b(t[h]);b(s.$$.fragment,_),a=!0}},o(_){for(let h=0;h<t.length;h+=1)p(t[h]);p(s.$$.fragment,_),a=!1},d(_){_&&(k(e),k(i));for(let h=0;h<t.length;h+=1)t[h].d();A(s,_),o=!1,De(u)}}}function s1(l,e,t){let n,i,s,r,{columns:a}=e,{areasMeta:o}=e,{api:u}=e,{contentEl:f}=e;const c=ve("wx-i18n").getGroup("kanban"),d=Ee(),{showModal:m}=ve("wx-helpers"),{readonly:_,columnShape:h}=u.getReactiveState();he(l,_,j=>t(16,s=j)),he(l,h,j=>t(4,r=j));const w=(j,z,q,J)=>{let V=J.menu.items({columns:q,columnIndex:z,column:j});return!V||!V.length?null:(i&&(V=V.filter(ne=>ne.id!=="add-card")),V.map(ne=>un(ne,c)))};let y={};function S(j,z){const q=a.findIndex(ne=>ne.id===j),J=z==="left"?q-1:q+2,V=a[J]?.id;d("action",{action:"move-column",data:{id:j,before:V}})}let C=null;function T(j){const{action:z,context:q}=j.detail;if(z){if(z.onClick){z.onClick({id:z.id,item:z,column:q});return}switch(z.id){case"add-card":d("action",{action:"add-card",data:{columnId:q.id,card:{label:c("Untitled")}}});break;case"set-edit":s.edit&&t(6,C=q.id);break;case"delete-column":{(r.confirmDeletion??!0?m({message:c("Would you like to delete this column?")}):Promise.resolve()).then(()=>{d("action",{action:"delete-column",data:{id:q.id}})}).catch(()=>{});break}case"move-column:left":S(q.id,"left");break;case"move-column:right":S(q.id,"right");break}}}let L=null,N=[];function E(j){return t(8,N=y[j]||[]),a.find(z=>z.id===j)}const F=({detail:{action:j,data:z}})=>{if(j==="close-column-input")t(6,C=null);else if(j==="expand-column"){G(z.id);return}d("action",{action:j,data:z})};function G(j){const z=a.find(q=>q.id===j);d("action",{action:"update-column",data:{id:j,column:{collapsed:!z.collapsed}}})}function U(j,z){Dn(z.target,"data-action")?.dataset.action!=="rename"||!s.edit||a.find(J=>J.id===j).collapsed||t(6,C=j)}const O={dblclick:U,collapse:G};function D(j){L=j,t(7,L)}return l.$$set=j=>{"columns"in j&&t(0,a=j.columns),"areasMeta"in j&&t(1,o=j.areasMeta),"api"in j&&t(2,u=j.api),"contentEl"in j&&t(3,f=j.contentEl)},l.$$.update=()=>{l.$$.dirty&16&&t(9,n=r.fixedHeaders!==!1),l.$$.dirty&65536&&(i=s.add===!1),l.$$.dirty&17&&a.forEach((j,z)=>{t(5,y[j.id]=w(j,z,a,r),y)})},[a,o,u,f,r,y,C,L,N,n,_,h,T,E,F,O,s,D]}class Cr extends ee{constructor(e){super(),x(this,e,s1,i1,Z,{columns:0,areasMeta:1,api:2,contentEl:3})}}const r1=l=>({startIndex:l[0]&1,endIndex:l[0]&2,byRow:l[0]&8,virtualContentEl:l[0]&16}),Ir=l=>({startIndex:l[0],endIndex:l[1],byRow:l[3],virtualContentEl:l[4]});function o1(l){let e,t,n,i,s;const r=l[30].default,a=be(r,l,l[29],Ir);return{c(){e=I("div"),t=I("div"),a&&a.c(),g(t,"class","wx-content svelte-jco5m0"),ce(t,"padding-top",l[6]+"px"),ce(t,"padding-bottom",l[5]+"px"),g(e,"class","wx-list-wrapper svelte-jco5m0"),g(e,"data-id","virtual-content")},m(o,u){v(o,e,u),M(e,t),a&&a.m(t,null),l[31](t),l[32](e),n=!0,i||(s=[te(e,"scroll",l[14]),He(Un.call(null,e))],i=!0)},p(o,u){a&&a.p&&(!n||u[0]&536870939)&&we(a,r,o,o[29],n?pe(r,o[29],u,r1):ke(o[29]),Ir),(!n||u[0]&64)&&ce(t,"padding-top",o[6]+"px"),(!n||u[0]&32)&&ce(t,"padding-bottom",o[5]+"px")},i(o){n||(b(a,o),n=!0)},o(o){p(a,o),n=!1},d(o){o&&k(e),a&&a.d(o),l[31](null),l[32](null),i=!1,De(s)}}}const a1=10;function u1(l,e,t){let n,i,s,r,a,o,u,f,c,d,m,_,h,w,y,S,C,{$$slots:T={},$$scope:L}=e,{api:N}=e;const{cardsMap:E,columns:F,rows:G,cardHeight:U,cards:O,columnKey:D,scroll:j}=N.getReactiveState();he(l,E,de=>t(28,C=de)),he(l,F,de=>t(27,S=de)),he(l,G,de=>t(26,y=de)),he(l,U,de=>t(25,w=de)),he(l,O,de=>t(24,h=de)),he(l,D,de=>t(23,_=de)),he(l,j,de=>t(22,m=de));let z,q=0,J={},V=0,ne=1;ot(()=>{ie()});function Q(){t(16,q=0),s.reduce((de,Re)=>{if(Re.collapsed)return de;const Ae=i.reduce((Pe,it)=>{const Ze=ze(it.id,Re.id),$e=n[Ze].length;return $e>Pe&&(Pe=$e),Pe},0);return t(3,J[Re.id]={id:Re.id,maxCardsCount:Ae,minIndex:de,maxIndex:de+Ae,startIndex:0,endIndex:0,visible:!0},J),t(16,q+=Ae),de+Ae},0),z&&(t(0,V=0),t(1,ne=0),ie())}async function B(de){if(z.querySelector(`[data-drag-item="${de}"]`))return;const Ae=a.find(tt=>le(tt.id,de)),Pe=rn(Ae,o),it=n[Pe].findIndex(tt=>le(tt.id,de)),Ze=i.findIndex(tt=>tt.id===Pe),$e=parseFloat(getComputedStyle(z).getPropertyValue("--wx-kanban-column-width"))||300;return it>-1?(await ct(),t(2,z.scrollTop=it*u,z),t(2,z.scrollLeft=$e*Ze,z),!0):!1}function ie(){const de=z.scrollTop-f,Re=Math.floor(de<0?0:de/u),Ae=Re+Math.floor((z.offsetHeight+f*2)/u);if(Ae>=q){t(1,ne=q);return}t(0,V=Re),t(1,ne=Ae)}function ge(de,Re){s.forEach(Ae=>{const Pe=J[Ae.id],it=Re-de;let Ze=de-Pe.minIndex,$e=Ze+it;Pe.startIndex=Ze<0?0:Ze,Pe.endIndex=$e>Pe.maxIndex?Pe.maxIndex:$e;const tt=Ze<Pe.maxIndex&&$e>0;Pe.visible=tt,Ae.collapsed&&(Pe.visible=!0),t(3,J[Ae.id]=Pe,J)})}function Fe(){ie()}let me;function Te(de){ue[de?"unshift":"push"](()=>{me=de,t(4,me)})}function Ye(de){ue[de?"unshift":"push"](()=>{z=de,t(2,z)})}return l.$$set=de=>{"api"in de&&t(15,N=de.api),"$$scope"in de&&t(29,L=de.$$scope)},l.$$.update=()=>{l.$$.dirty[0]&268435456&&t(20,n=C),l.$$.dirty[0]&134217728&&t(19,i=S),l.$$.dirty[0]&67108864&&t(17,s=y),l.$$.dirty[0]&33554432&&t(21,r=w),l.$$.dirty[0]&16777216&&(a=h),l.$$.dirty[0]&8388608&&(o=_),l.$$.dirty[0]&2097152&&t(18,u=r+a1),l.$$.dirty[0]&262144&&(f=u*2),l.$$.dirty[0]&1703936&&s&&i&&n&&Q(),l.$$.dirty[0]&4227072&&m&&m.to==="card"&&m.id&&B(m.id).then(de=>{de&&N.exec("scroll",null)}),l.$$.dirty[0]&3&&ge(V,ne),l.$$.dirty[0]&262145&&t(6,c=V*u),l.$$.dirty[0]&327682&&t(5,d=(q-ne)*u)},[V,ne,z,J,me,d,c,E,F,G,U,O,D,j,Fe,N,q,s,u,i,n,r,m,_,h,w,y,S,C,L,T,Te,Ye]}class f1 extends ee{constructor(e){super(),x(this,e,u1,o1,Z,{api:15},null,[-1,-1])}}function Yn(){}function c1(l,e){return l!=l?e==e:l!==e||l&&typeof l=="object"||typeof l=="function"}const Rt=[];function d1(l,e=Yn){let t;const n=new Set;function i(a){if(c1(l,a)&&(l=a,t)){const o=!Rt.length;for(const u of n)u[1](),Rt.push(u,l);if(o){for(let u=0;u<Rt.length;u+=2)Rt[u][0](Rt[u+1]);Rt.length=0}}}function s(a){i(a(l))}function r(a,o=Yn){const u=[a,o];return n.add(u),n.size===1&&(t=e(i)||Yn),a(l),()=>{n.delete(u),n.size===0&&t&&(t(),t=null)}}return{set:i,update:s,subscribe:r}}new Date().valueOf();function m1(l,e){for(const t in e){const n=l[t],i=e[t];if(!dn(n,i))return!1}return!0}function dn(l,e){if(typeof l=="number"||typeof l=="string"||typeof l=="boolean"||l===null)return l===e;if(typeof l!=typeof e||(l===null||e===null)&&l!==e||l instanceof Date&&e instanceof Date&&l.getTime()!==e.getTime())return!1;if(typeof l=="object")if(Array.isArray(l)&&Array.isArray(e)){if(l.length!==e.length)return!1;for(let n=l.length-1;n>=0;n--)if(!dn(l[n],e[n]))return!1;return!0}else return m1(l,e);return l===e}function Gn(l){if(typeof l!="object"||l===null)return l;if(l instanceof Date)return new Date(l);if(l instanceof Array)return l.map(Gn);const e={};for(const t in l)e[t]=Gn(l[t]);return e}function mn(l,e){return e?Gn(l):{...l}}function Mr(l,e,t){const n=t&&t.deepCopy;let i=!1,s=null;const r=d1(l),{set:a}=r;let o=mn(l,n);return r.set=function(u){dn(o,u)||(o=mn(u,n),a(u))},r.update=function(u){const f=u(mn(o,n));dn(o,f)||(o=mn(f,n),a(f))},r.reset=function(u){i=!1,o={},r.set(u)},r.subscribe(u=>{i?u&&(!t||!t.debounce?e(u):(clearTimeout(s),s=setTimeout(()=>e(u),t.debounce))):i=!0}),r}function _1(l,e,t){const n=ve("wx-i18n"),i=n.getGroup("kanban");return[n,i]}class h1 extends ee{constructor(e){super(),x(this,e,_1,null,Z,{locale:0,_:1})}get locale(){return this.$$.ctx[0]}get _(){return this.$$.ctx[1]}}function Tr(l,e,t){const n=l.slice();return n[33]=e[t],n}function g1(l){let e;return{c(){e=I("div"),e.textContent=`${l[8]("No comments yet")}`,g(e,"class","wx-kanban-no-comments svelte-1idrztz")},m(t,n){v(t,e,n)},p:K,i:K,o:K,d(t){t&&k(e)}}}function b1(l){let e,t=[],n=new Map,i,s,r,a=fe(l[0]);const o=u=>u[33].id;for(let u=0;u<a.length;u+=1){let f=Tr(l,a,u),c=o(f);n.set(c,t[u]=Er(c,f))}return{c(){e=I("div");for(let u=0;u<t.length;u+=1)t[u].c();g(e,"class","wx-comment-list svelte-1idrztz")},m(u,f){v(u,e,f);for(let c=0;c<t.length;c+=1)t[c]&&t[c].m(e,null);i=!0,s||(r=te(e,"click",function(){nt(l[6])&&l[6].apply(this,arguments)}),s=!0)},p(u,f){l=u,f[0]&113073&&(a=fe(l[0]),W(),t=Oe(t,f,o,1,l,a,n,e,Ue,Er,null,Tr),Y())},i(u){if(!i){for(let f=0;f<a.length;f+=1)b(t[f]);i=!0}},o(u){for(let f=0;f<t.length;f+=1)p(t[f]);i=!1},d(u){u&&k(e);for(let f=0;f<t.length;f+=1)t[f].d();s=!1,r()}}}function Dr(l){let e,t,n,i;return t=new Me({props:{css:"wxi-dots-v"}}),{c(){e=I("div"),P(t.$$.fragment),g(e,"class","wx-menu svelte-1idrztz"),g(e,"data-menu-id",n=l[33].id)},m(s,r){v(s,e,r),R(t,e,null),i=!0},p(s,r){(!i||r[0]&1&&n!==(n=s[33].id))&&g(e,"data-menu-id",n)},i(s){i||(b(t.$$.fragment,s),i=!0)},o(s){p(t.$$.fragment,s),i=!1},d(s){s&&k(e),A(t)}}}function p1(l){let e,t=l[33].text+"",n;return{c(){e=I("pre"),n=$(t),g(e,"class","wx-text svelte-1idrztz")},m(i,s){v(i,e,s),M(e,n)},p(i,s){s[0]&1&&t!==(t=i[33].text+"")&&re(n,t)},i:K,o:K,d(i){i&&k(e)}}}function w1(l){let e,t=l[33].html+"",n;return{c(){e=new Zt(!1),n=se(),e.a=n},m(i,s){e.m(t,i,s),v(i,n,s)},p(i,s){s[0]&1&&t!==(t=i[33].html+"")&&e.p(t)},i:K,o:K,d(i){i&&(k(n),e.d())}}}function k1(l){let e,t,n,i,s,r,a,o,u,f;function c(m){l[23](m)}let d={};return l[5]!==void 0&&(d.value=l[5]),e=new Ln({props:d}),ue.push(()=>Se(e,"value",c)),r=new Ve({props:{type:"secondary",click:l[16],$$slots:{default:[v1]},$$scope:{ctx:l}}}),u=new Ve({props:{type:"primary",click:l[15],$$slots:{default:[y1]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment),n=H(),i=I("div"),s=I("div"),P(r.$$.fragment),a=H(),o=I("div"),P(u.$$.fragment),g(s,"class","wx-comment-textarea-btn svelte-1idrztz"),g(o,"class","wx-comment-textarea-btn svelte-1idrztz"),g(i,"class","wx-edit-btns svelte-1idrztz")},m(m,_){R(e,m,_),v(m,n,_),v(m,i,_),M(i,s),R(r,s,null),M(i,a),M(i,o),R(u,o,null),f=!0},p(m,_){const h={};!t&&_[0]&32&&(t=!0,h.value=m[5],Ie(()=>t=!1)),e.$set(h);const w={};_[1]&32&&(w.$$scope={dirty:_,ctx:m}),r.$set(w);const y={};_[1]&32&&(y.$$scope={dirty:_,ctx:m}),u.$set(y)},i(m){f||(b(e.$$.fragment,m),b(r.$$.fragment,m),b(u.$$.fragment,m),f=!0)},o(m){p(e.$$.fragment,m),p(r.$$.fragment,m),p(u.$$.fragment,m),f=!1},d(m){m&&(k(n),k(i)),A(e,m),A(r),A(u)}}}function v1(l){let e=l[8]("Cancel")+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p:K,d(n){n&&k(t)}}}function y1(l){let e=l[8]("Save")+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p:K,d(n){n&&k(t)}}}function Er(l,e){let t,n,i,s,r,a,o,u=e[12][e[33].userId].label+"",f,c,d=e[4]!==e[33].id&&le(e[33].userId,e[7]),m,_,h=e[13](e[33].date)+"",w,y,S,C,T,L,N,E;i=new Et({props:{size:"small",border:!1,data:e[12][e[33].userId]}});let F=d&&Dr(e);const G=[k1,w1,p1],U=[];function O(D,j){return j[0]&17&&(S=null),S==null&&(S=!!le(D[4],D[33].id)),S?0:D[33].html&&D[11].html?1:2}return C=O(e,[-1,-1]),T=U[C]=G[C](e),{key:l,first:null,c(){t=I("div"),n=I("div"),P(i.$$.fragment),s=H(),r=I("div"),a=I("div"),o=I("div"),f=$(u),c=H(),F&&F.c(),m=H(),_=I("div"),w=$(h),y=H(),T.c(),L=H(),g(n,"class","wx-comment-icon svelte-1idrztz"),g(o,"class","wx-name svelte-1idrztz"),g(a,"class","wx-comment-header svelte-1idrztz"),g(_,"class","wx-date svelte-1idrztz"),g(r,"class","wx-content svelte-1idrztz"),g(t,"class","wx-comment svelte-1idrztz"),g(t,"data-comment-id",N=e[33].id),this.first=t},m(D,j){v(D,t,j),M(t,n),R(i,n,null),M(t,s),M(t,r),M(r,a),M(a,o),M(o,f),M(a,c),F&&F.m(a,null),M(r,m),M(r,_),M(_,w),M(r,y),U[C].m(r,null),M(t,L),E=!0},p(D,j){e=D;const z={};j[0]&1&&(z.data=e[12][e[33].userId]),i.$set(z),(!E||j[0]&1)&&u!==(u=e[12][e[33].userId].label+"")&&re(f,u),j[0]&145&&(d=e[4]!==e[33].id&&le(e[33].userId,e[7])),d?F?(F.p(e,j),j[0]&145&&b(F,1)):(F=Dr(e),F.c(),b(F,1),F.m(a,null)):F&&(W(),p(F,1,1,()=>{F=null}),Y()),(!E||j[0]&1)&&h!==(h=e[13](e[33].date)+"")&&re(w,h);let q=C;C=O(e,j),C===q?U[C].p(e,j):(W(),p(U[q],1,1,()=>{U[q]=null}),Y(),T=U[C],T?T.p(e,j):(T=U[C]=G[C](e),T.c()),b(T,1),T.m(r,null)),(!E||j[0]&1&&N!==(N=e[33].id))&&g(t,"data-comment-id",N)},i(D){E||(b(i.$$.fragment,D),b(F),b(T),E=!0)},o(D){p(i.$$.fragment,D),p(F),p(T),E=!1},d(D){D&&k(t),A(i),F&&F.d(),U[C].d()}}}function Lr(l){let e,t,n,i,s,r,a,o,u,f,c,d;i=new Et({props:{size:"small",border:!1,data:l[12][l[7]]}});function m(w){l[25](w)}let _={placeholder:l[8]("Add a comment...")};l[3]!==void 0&&(_.value=l[3]),r=new Ln({props:_}),ue.push(()=>Se(r,"value",m)),f=new Ve({props:{disabled:!l[3],type:"primary",click:l[14],$$slots:{default:[S1]},$$scope:{ctx:l}}});let h=l[11].placement==="page"&&l[2]==="modal"&&Rr(l);return{c(){e=I("div"),t=I("div"),n=I("div"),P(i.$$.fragment),s=H(),P(r.$$.fragment),o=H(),u=I("div"),P(f.$$.fragment),c=H(),h&&h.c(),g(n,"class","wx-comment-icon svelte-1idrztz"),g(t,"class","wx-new-comment svelte-1idrztz"),g(u,"class","wx-comment-textarea-btn svelte-1idrztz"),g(e,"class","wx-new-comment-wrapper svelte-1idrztz")},m(w,y){v(w,e,y),M(e,t),M(t,n),R(i,n,null),M(t,s),R(r,t,null),M(e,o),M(e,u),R(f,u,null),M(u,c),h&&h.m(u,null),d=!0},p(w,y){const S={};y[0]&128&&(S.data=w[12][w[7]]),i.$set(S);const C={};!a&&y[0]&8&&(a=!0,C.value=w[3],Ie(()=>a=!1)),r.$set(C);const T={};y[0]&8&&(T.disabled=!w[3]),y[1]&32&&(T.$$scope={dirty:y,ctx:w}),f.$set(T),w[11].placement==="page"&&w[2]==="modal"?h?(h.p(w,y),y[0]&4&&b(h,1)):(h=Rr(w),h.c(),b(h,1),h.m(u,null)):h&&(W(),p(h,1,1,()=>{h=null}),Y())},i(w){d||(b(i.$$.fragment,w),b(r.$$.fragment,w),b(f.$$.fragment,w),b(h),d=!0)},o(w){p(i.$$.fragment,w),p(r.$$.fragment,w),p(f.$$.fragment,w),p(h),d=!1},d(w){w&&k(e),A(i),A(r),A(f),h&&h.d()}}}function S1(l){let e=l[8]("Send")+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p:K,d(n){n&&k(t)}}}function Rr(l){let e,t;return e=new Ve({props:{type:"secondary",click:l[26],$$slots:{default:[C1]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i[0]&2&&(s.click=n[26]),i[1]&32&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function C1(l){let e=l[8]("Back")+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p:K,d(n){n&&k(t)}}}function I1(l){let e,t,n,i,s,r,a,o;const u=[b1,g1],f=[];function c(h,w){return h[0].length?0:h[7]?-1:1}~(t=c(l))&&(n=f[t]=u[t](l));function d(h){l[24](h)}let m={at:"left-bottom",options:l[18],resolver:M1,dataKey:"menuId"};l[6]!==void 0&&(m.handler=l[6]),s=new cn({props:m}),ue.push(()=>Se(s,"handler",d)),s.$on("click",l[17]);let _=l[7]&&!l[4]&&Lr(l);return{c(){e=I("div"),n&&n.c(),i=H(),P(s.$$.fragment),a=H(),_&&_.c(),g(e,"class","wx-comments svelte-1idrztz")},m(h,w){v(h,e,w),~t&&f[t].m(e,null),M(e,i),R(s,e,null),M(e,a),_&&_.m(e,null),o=!0},p(h,w){let y=t;t=c(h),t===y?~t&&f[t].p(h,w):(n&&(W(),p(f[y],1,1,()=>{f[y]=null}),Y()),~t?(n=f[t],n?n.p(h,w):(n=f[t]=u[t](h),n.c()),b(n,1),n.m(e,i)):n=null);const S={};!r&&w[0]&64&&(r=!0,S.handler=h[6],Ie(()=>r=!1)),s.$set(S),h[7]&&!h[4]?_?(_.p(h,w),w[0]&144&&b(_,1)):(_=Lr(h),_.c(),b(_,1),_.m(e,null)):_&&(W(),p(_,1,1,()=>{_=null}),Y())},i(h){o||(b(n),b(s.$$.fragment,h),b(_),o=!0)},o(h){p(n),p(s.$$.fragment,h),p(_),o=!1},d(h){h&&k(e),~t&&f[t].d(),A(s),_&&_.d()}}}const M1=l=>l;function T1(l,e,t){let n,i,s,{api:r}=e,{comments:a=[]}=e,{users:o=[]}=e,{shape:u}=e,{handleViewChange:f=null}=e,{placement:c}=e;const d=ve("wx-i18n"),m=d.getGroup("kanban"),{showModal:_}=ve("wx-helpers"),{currentUser:h,selected:w}=r.getReactiveState();he(l,h,B=>t(7,s=B)),he(l,w,B=>t(22,i=B));const y=u.config||{},S=o.reduce((B,ie)=>(B[ie.id]=ie,B),{}),C=y.format||"%M %d %Y %h:%i",T=Ft(C,d.getRaw().calendar);let L="";function N(){L&&r.exec("add-comment",{id:tn(),cardId:n,comment:{text:L,date:new Date}}).then(()=>{t(0,a=r.getCard(n).comments),t(3,L="")})}let E=null,F=null;function G(B){t(4,E=B.id),t(5,F=B.text)}function U(){F&&r.exec("update-comment",{cardId:n,id:E,comment:{text:F}}).then(()=>{t(0,a=r.getCard(n).comments)}),O()}function O(){t(4,E=null),t(5,F=null)}function D(B){(y.confirmDeletion??!0?_({message:m("Would you like to delete this comment?")}):Promise.resolve()).then(()=>{r.exec("delete-comment",{cardId:n,id:B}).then(()=>{t(0,a=r.getCard(n).comments)})}).catch(()=>{})}function j(B){const{context:ie,action:ge}=B.detail;if(ge)switch(ge.id){case"edit-comment":G(a.find(Fe=>Fe.id==ie));break;case"delete-comment":D(ie);break}}let z=null;const q=[{id:"edit-comment",text:m("Edit"),icon:"wxi-edit-outline"},{id:"delete-comment",text:m("Delete"),icon:"wxi-delete-outline"}];function J(B){F=B,t(5,F)}function V(B){z=B,t(6,z)}function ne(B){L=B,t(3,L)}const Q=()=>f("main");return l.$$set=B=>{"api"in B&&t(19,r=B.api),"comments"in B&&t(0,a=B.comments),"users"in B&&t(20,o=B.users),"shape"in B&&t(21,u=B.shape),"handleViewChange"in B&&t(1,f=B.handleViewChange),"placement"in B&&t(2,c=B.placement)},l.$$.update=()=>{l.$$.dirty[0]&4194304&&(n=i?.[0])},[a,f,c,L,E,F,z,s,m,h,w,y,S,T,N,U,O,j,q,r,o,u,i,J,V,ne,Q]}class Ar extends ee{constructor(e){super(),x(this,e,T1,I1,Z,{api:19,comments:0,users:20,shape:21,handleViewChange:1,placement:2},null,[-1,-1])}}function D1(l,e){const t=["click","contextmenu"],n=i=>{l&&!l.contains(i.target)&&!i.defaultPrevented&&e(i)};return t.forEach(i=>document.addEventListener(i,n,!0)),{destroy(){t.forEach(i=>document.removeEventListener(i,n,!0))}}}let Pr=new Date().valueOf();function Nr(){return Pr+=1,Pr}function zr(l){return l&&typeof l=="object"&&!Array.isArray(l)}function Jn(l,e){for(const t in e){const n=e[t];zr(l[t])&&zr(n)?l[t]=Jn({...l[t]},e[t]):l[t]=e[t]}return l}function Bt(l){return{getGroup(e){const t=l[e];return n=>t&&t[n]||n},getRaw(){return l},extend(e,t){if(!e)return this;let n;return t?n=Jn({...e},l):n=Jn({...l},e),Bt(n)}}}const E1="wx-uploader-api",L1=l=>({}),Hr=l=>({open:l[8]});function R1(l){let e,t,n,i,s,r;const a=l[14].default,o=be(a,l,l[13],Hr),u=o||P1(l);return{c(){e=I("div"),t=I("input"),n=H(),u&&u.c(),g(t,"type","file"),g(t,"class","input svelte-15jokro"),g(t,"accept",l[0]),t.multiple=l[1],t.disabled=l[3],g(e,"class","label svelte-15jokro"),X(e,"active",l[5]),X(e,"wx-disabled",l[3])},m(f,c){v(f,e,c),M(e,t),l[16](t),M(e,n),u&&u.m(e,null),i=!0,s||(r=[te(t,"change",l[7]),He(l[6].droparea(e))],s=!0)},p(f,c){(!i||c&1)&&g(t,"accept",f[0]),(!i||c&2)&&(t.multiple=f[1]),(!i||c&8)&&(t.disabled=f[3]),o&&o.p&&(!i||c&8192)&&we(o,a,f,f[13],i?pe(a,f[13],c,L1):ke(f[13]),Hr),(!i||c&32)&&X(e,"active",f[5]),(!i||c&8)&&X(e,"wx-disabled",f[3])},i(f){i||(b(u,f),i=!0)},o(f){p(u,f),i=!1},d(f){f&&k(e),l[16](null),u&&u.d(f),s=!1,De(r)}}}function A1(l){let e,t,n,i,s;const r=l[14].default,a=be(r,l,l[13],null);return{c(){e=I("input"),t=H(),a&&a.c(),g(e,"type","file"),g(e,"class","input svelte-15jokro"),g(e,"accept",l[0]),e.multiple=l[1],e.disabled=l[3]},m(o,u){v(o,e,u),l[15](e),v(o,t,u),a&&a.m(o,u),n=!0,i||(s=te(e,"change",l[7]),i=!0)},p(o,u){(!n||u&1)&&g(e,"accept",o[0]),(!n||u&2)&&(e.multiple=o[1]),(!n||u&8)&&(e.disabled=o[3]),a&&a.p&&(!n||u&8192)&&we(a,r,o,o[13],n?pe(r,o[13],u,null):ke(o[13]),null)},i(o){n||(b(a,o),n=!0)},o(o){p(a,o),n=!1},d(o){o&&(k(e),k(t)),l[15](null),a&&a.d(o),i=!1,s()}}}function P1(l){let e,t,n,i,s,r;return{c(){e=I("div"),t=I("span"),n=$(`Drop files here or
                                    `),i=I("span"),i.textContent="select files",g(i,"class","action svelte-15jokro"),g(e,"class","dropzone svelte-15jokro")},m(a,o){v(a,e,o),M(e,t),M(t,n),M(t,i),s||(r=te(i,"click",l[8]),s=!0)},p:K,d(a){a&&k(e),s=!1,r()}}}function N1(l){let e,t,n,i;const s=[A1,R1],r=[];function a(o,u){return o[2]?0:1}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=se()},m(o,u){r[e].m(o,u),v(o,n,u),i=!0},p(o,[u]){let f=e;e=a(o),e===f?r[e].p(o,u):(W(),p(r[f],1,1,()=>{r[f]=null}),Y(),t=r[e],t?t.p(o,u):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(n.parentNode,n))},i(o){i||(b(t),i=!0)},o(o){p(t),i=!1},d(o){o&&k(n),r[e].d(o)}}}function z1(l,e,t){let{$$slots:n={},$$scope:i}=e;const s=Ee();let{data:r=[]}=e,{accept:a=""}=e,{multiple:o=!0}=e,{folder:u=!1}=e,{uploadURL:f=""}=e,{apiOnly:c=!1}=e,{disabled:d=!1}=e,{ready:m=new Promise(()=>({}))}=e,_,h,w=0,y={};const S={open:q=>D(q),droparea:(q,J)=>{d||(J=J||{},q.addEventListener("dragenter",()=>{J.dragEnter&&J.dragEnter(),U()}),q.addEventListener("dragleave",()=>{J.dragEnter&&J.dragLeave(),O()}),q.addEventListener("dragover",V=>V.preventDefault(),!0),q.addEventListener("drop",V=>{V.preventDefault(),y=J,T(V),J.dragEnter&&J.dragLeave()},!0))}};ot(()=>{t(4,_.webkitdirectory=u,_)}),_t(E1,S);function C(q){Array.from(q.target.files).forEach(V=>N(V))}function T(q){Array.from(q.dataTransfer.items).forEach(V=>{const ne=V.webkitGetAsEntry();ne&&L(ne)}),t(5,h=!1),w=0}function L(q,J){J=J||"",q.isFile?q.file(V=>{N(V)}):q.isDirectory&&q.createReader().readEntries(ne=>{ne.forEach(Q=>{L(Q,J+Q.name+"/")})})}function N(q){const J={...y,id:Nr(),status:"client",name:q.name,file:q};J.selected&&J.selected(J),s("select",J),o?t(9,r=[...r,J]):t(9,r=[J]),F(J),t(4,_.value="",_)}function E(q){const J=new FormData;return J.append("upload",q.file),fetch(f,{method:"POST",body:J}).then(ne=>ne.json()).then(ne=>({id:q.id,...ne}),()=>({id:q.id,status:"error"})).catch(ne=>console.log(ne))}function F(q){if(!q)return;const J=typeof f=="function"?f(q):E(q);t(10,m=J.then(V=>{V.status=V.status||"server",G(q.id,V)}).catch(V=>{G(q.id,{status:"error",error:V})}))}function G(q,J){const V=r.findIndex(Q=>Q.id==q),ne=t(9,r[V]={...r[V],...J},r);ne&&ne.uploaded&&ne.uploaded(ne),s("upload",r[V]),ne.temp&&t(9,r=r.filter(Q=>Q.id!=q))}function U(){w===0&&t(5,h=!0),w++}function O(){w--,w===0&&t(5,h=!1)}function D(q){y=q||{},_.click()}function j(q){ue[q?"unshift":"push"](()=>{_=q,t(4,_)})}function z(q){ue[q?"unshift":"push"](()=>{_=q,t(4,_)})}return l.$$set=q=>{"data"in q&&t(9,r=q.data),"accept"in q&&t(0,a=q.accept),"multiple"in q&&t(1,o=q.multiple),"folder"in q&&t(11,u=q.folder),"uploadURL"in q&&t(12,f=q.uploadURL),"apiOnly"in q&&t(2,c=q.apiOnly),"disabled"in q&&t(3,d=q.disabled),"ready"in q&&t(10,m=q.ready),"$$scope"in q&&t(13,i=q.$$scope)},[a,o,c,d,_,h,S,C,D,r,m,u,f,i,n,j,z]}class H1 extends ee{constructor(e){super(),x(this,e,z1,N1,Z,{data:9,accept:0,multiple:1,folder:11,uploadURL:12,apiOnly:2,disabled:3,ready:10})}}function Or(l,e,t){const n=l.slice();return n[12]=e[t],n}function Fr(l){let e,t,n,i,s,r=[],a=new Map,o;n=new Me({props:{css:"wxi-close"}}),n.$on("click",l[2]);let u=fe(l[1]);const f=c=>c[12].id;for(let c=0;c<u.length;c+=1){let d=Or(l,u,c),m=f(d);a.set(m,r[c]=Ur(m,d))}return{c(){e=I("div"),t=I("div"),P(n.$$.fragment),i=H(),s=I("div");for(let c=0;c<r.length;c+=1)r[c].c();g(t,"class","wx-header svelte-l8op85"),g(s,"class","wx-list svelte-l8op85"),g(e,"class","wx-layout svelte-l8op85")},m(c,d){v(c,e,d),M(e,t),R(n,t,null),M(e,i),M(e,s);for(let m=0;m<r.length;m+=1)r[m]&&r[m].m(s,null);o=!0},p(c,d){d&250&&(u=fe(c[1]),W(),r=Oe(r,d,f,1,c,u,a,s,Ue,Ur,null,Or),Y())},i(c){if(!o){b(n.$$.fragment,c);for(let d=0;d<u.length;d+=1)b(r[d]);o=!0}},o(c){p(n.$$.fragment,c);for(let d=0;d<r.length;d+=1)p(r[d]);o=!1},d(c){c&&k(e),A(n);for(let d=0;d<r.length;d+=1)r[d].d()}}}function O1(l){let e,t;return e=new Me({props:{css:"wxi-paperclip"}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p:K,i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function F1(l){let e;return{c(){e=I("div"),g(e,"class","wx-thumb svelte-l8op85"),ce(e,"background-image","url('"+(l[12].previewURL||l[12].url)+"')")},m(t,n){v(t,e,n)},p(t,n){n&2&&ce(e,"background-image","url('"+(t[12].previewURL||t[12].url)+"')")},i:K,o:K,d(t){t&&k(e)}}}function qr(l){let e,t=l[4](l[12].file.size)+"",n;return{c(){e=I("div"),n=$(t),g(e,"class","wx-size")},m(i,s){v(i,e,s),M(e,n)},p(i,s){s&2&&t!==(t=i[4](i[12].file.size)+"")&&re(n,t)},d(i){i&&k(e)}}}function q1(l){let e,t,n,i,s,r,a,o,u=l[5](l[12]),f;n=new Me({props:{css:"wxi-external"}});function c(){return l[9](l[12])}a=new Me({props:{css:"wxi-delete-outline"}}),a.$on("click",c);let d=u&&jr(l);return{c(){e=I("div"),t=I("a"),P(n.$$.fragment),r=H(),P(a.$$.fragment),o=H(),d&&d.c(),g(t,"class","wx-upload-link svelte-l8op85"),g(t,"href",i=l[12].url),g(t,"download",s=l[12].name),g(t,"target","_blank"),g(t,"rel","noreferrer nofollow noopener"),g(e,"class","wx-hidden svelte-l8op85")},m(m,_){v(m,e,_),M(e,t),R(n,t,null),M(e,r),R(a,e,null),M(e,o),d&&d.m(e,null),f=!0},p(m,_){l=m,(!f||_&2&&i!==(i=l[12].url))&&g(t,"href",i),(!f||_&2&&s!==(s=l[12].name))&&g(t,"download",s),_&2&&(u=l[5](l[12])),u?d?(d.p(l,_),_&2&&b(d,1)):(d=jr(l),d.c(),b(d,1),d.m(e,null)):d&&(W(),p(d,1,1,()=>{d=null}),Y())},i(m){f||(b(n.$$.fragment,m),b(a.$$.fragment,m),b(d),f=!0)},o(m){p(n.$$.fragment,m),p(a.$$.fragment,m),p(d),f=!1},d(m){m&&k(e),A(n),A(a),d&&d.d()}}}function j1(l){let e,t,n,i;e=new Me({props:{css:"wxi-alert"}});function s(){return l[8](l[12])}return n=new Me({props:{css:"wxi-delete-outline"}}),n.$on("click",s),{c(){P(e.$$.fragment),t=H(),P(n.$$.fragment)},m(r,a){R(e,r,a),v(r,t,a),R(n,r,a),i=!0},p(r,a){l=r},i(r){i||(b(e.$$.fragment,r),b(n.$$.fragment,r),i=!0)},o(r){p(e.$$.fragment,r),p(n.$$.fragment,r),i=!1},d(r){r&&k(t),A(e,r),A(n,r)}}}function U1(l){let e,t;return e=new Me({props:{css:"wxi-loading wx-spin"}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p:K,i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function jr(l){let e,t,n,i;const s=[K1,V1],r=[];function a(o,u){return o[12].isCover?1:0}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=se()},m(o,u){r[e].m(o,u),v(o,n,u),i=!0},p(o,u){let f=e;e=a(o),e===f?r[e].p(o,u):(W(),p(r[f],1,1,()=>{r[f]=null}),Y(),t=r[e],t?t.p(o,u):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(n.parentNode,n))},i(o){i||(b(t),i=!0)},o(o){p(t),i=!1},d(o){o&&k(n),r[e].d(o)}}}function V1(l){let e,t;return e=new Ve({props:{click:l[7],$$slots:{default:[B1]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&32768&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function K1(l){let e,t;function n(){return l[10](l[12])}return e=new Ve({props:{click:n,$$slots:{default:[W1]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(i,s){R(e,i,s),t=!0},p(i,s){l=i;const r={};s&2&&(r.click=n),s&32768&&(r.$$scope={dirty:s,ctx:l}),e.$set(r)},i(i){t||(b(e.$$.fragment,i),t=!0)},o(i){p(e.$$.fragment,i),t=!1},d(i){A(e,i)}}}function B1(l){let e;return{c(){e=$("Remove cover")},m(t,n){v(t,e,n)},d(t){t&&k(e)}}}function W1(l){let e;return{c(){e=$("Make cover")},m(t,n){v(t,e,n)},d(t){t&&k(e)}}}function Ur(l,e){let t,n,i,s,r,a,o,u=e[12].name+"",f,c,d,m,_,h,w,y;const S=[F1,O1],C=[];function T(G,U){return U&2&&(i=null),i==null&&(i=!!G[5](G[12])),i?0:1}s=T(e,-1),r=C[s]=S[s](e);let L=e[12].file&&qr(e);const N=[U1,j1,q1],E=[];function F(G,U){return G[12].status==="client"?0:G[12].status==="error"?1:!G[12].status||G[12].status==="server"?2:-1}return~(_=F(e))&&(h=E[_]=N[_](e)),{key:l,first:null,c(){t=I("div"),n=I("div"),r.c(),a=H(),o=I("div"),f=$(u),c=H(),L&&L.c(),d=H(),m=I("div"),h&&h.c(),w=H(),g(n,"class","wx-file-icon svelte-l8op85"),g(o,"class","wx-name svelte-l8op85"),g(m,"class","wx-controls svelte-l8op85"),g(t,"class","wx-row svelte-l8op85"),this.first=t},m(G,U){v(G,t,U),M(t,n),C[s].m(n,null),M(t,a),M(t,o),M(o,f),M(t,c),L&&L.m(t,null),M(t,d),M(t,m),~_&&E[_].m(m,null),M(t,w),y=!0},p(G,U){e=G;let O=s;s=T(e,U),s===O?C[s].p(e,U):(W(),p(C[O],1,1,()=>{C[O]=null}),Y(),r=C[s],r?r.p(e,U):(r=C[s]=S[s](e),r.c()),b(r,1),r.m(n,null)),(!y||U&2)&&u!==(u=e[12].name+"")&&re(f,u),e[12].file?L?L.p(e,U):(L=qr(e),L.c(),L.m(t,d)):L&&(L.d(1),L=null);let D=_;_=F(e),_===D?~_&&E[_].p(e,U):(h&&(W(),p(E[D],1,1,()=>{E[D]=null}),Y()),~_?(h=E[_],h?h.p(e,U):(h=E[_]=N[_](e),h.c()),b(h,1),h.m(m,null)):h=null)},i(G){y||(b(r),b(h),y=!0)},o(G){p(r),p(h),y=!1},d(G){G&&k(t),C[s].d(),L&&L.d(),~_&&E[_].d()}}}function Y1(l){let e,t,n=l[1].length&&Fr(l);return{c(){n&&n.c(),e=se()},m(i,s){n&&n.m(i,s),v(i,e,s),t=!0},p(i,[s]){i[1].length?n?(n.p(i,s),s&2&&b(n,1)):(n=Fr(i),n.c(),b(n,1),n.m(e.parentNode,e)):n&&(W(),p(n,1,1,()=>{n=null}),Y())},i(i){t||(b(n),t=!0)},o(i){p(n),t=!1},d(i){i&&k(e),n&&n.d(i)}}}function G1(l,e,t){let n,i=K,s=()=>(i(),i=Ge(r,y=>t(1,n=y)),r);l.$$.on_destroy.push(()=>i());let{data:r}=e;s();const a=["b","Kb","Mb","Gb","Tb","Pb","Eb"];function o(){r.set([])}function u(y){r.update(S=>S.filter(C=>C.id!==y))}function f(y){let S=0;for(;y>1024;)S++,y=y/1024;return Math.round(y*100)/100+" "+a[S]}function c(y){const S=y?.url?.split(".").pop(),C=y?.previewURL?.split(".").pop();return $i(C)||$i(S)}function d(y){r.update(S=>S.map(C=>C.id===y?{...C,isCover:!0}:(delete C.isCover,C)))}function m(){r.update(y=>y.map(S=>{const C={...S};return delete C.isCover,C}))}const _=y=>u(y.id),h=y=>u(y.id),w=y=>d(y.id);return l.$$set=y=>{"data"in y&&s(t(0,r=y.data))},[r,n,o,u,f,c,d,m,_,h,w]}class J1 extends ee{constructor(e){super(),x(this,e,G1,Y1,Z,{data:0})}}function Q1(l){let e,t,n,i,s,r;t=new J1({props:{data:l[4]}});const a=[{uploadURL:l[3]},l[0].config];function o(f){l[7](f)}let u={};for(let f=0;f<a.length;f+=1)u=Ce(u,a[f]);return l[2]!==void 0&&(u.data=l[2]),i=new H1({props:u}),ue.push(()=>Se(i,"data",o)),{c(){e=I("div"),P(t.$$.fragment),n=H(),P(i.$$.fragment),g(e,"class","wx-files-control svelte-1vstfbt")},m(f,c){v(f,e,c),R(t,e,null),M(e,n),R(i,e,null),r=!0},p(f,[c]){const d={};c&16&&(d.data=f[4]),t.$set(d);const m=c&9?st(a,[c&8&&{uploadURL:f[3]},c&1&&rt(f[0].config)]):{};!s&&c&4&&(s=!0,m.data=f[2],Ie(()=>s=!1)),i.$set(m)},i(f){r||(b(t.$$.fragment,f),b(i.$$.fragment,f),r=!0)},o(f){p(t.$$.fragment,f),p(i.$$.fragment,f),r=!1},d(f){f&&k(e),A(t),A(i)}}}function X1(l,e,t){let n,i,s,r=K,a=()=>(r(),r=Ge(n,w=>t(2,s=w)),n),o,u=K,f=()=>(u(),u=Ge(d,w=>t(6,o=w)),d);l.$$.on_destroy.push(()=>r()),l.$$.on_destroy.push(()=>u());let{field:c}=e,{values:d}=e;f();let m=!1;function _(w){const y=new FormData;y.append("upload",w.file);const S={method:"POST",body:y};return fetch(c.uploadURL,S).then(C=>C.json()).then(C=>(w.id=C.id,C),()=>({id:w.id,status:"error"})).catch()}function h(w){s=w,n.set(s)}return l.$$set=w=>{"field"in w&&t(0,c=w.field),"values"in w&&f(t(1,d=w.values))},l.$$.update=()=>{l.$$.dirty&37&&(m&&Pt(d,o[c.key]=s,o),t(5,m=!0)),l.$$.dirty&65&&a(t(4,n=Pn(o[c.key]||[]))),l.$$.dirty&1&&t(3,i=typeof c.uploadURL=="function"?c.uploadURL:_)},[c,d,s,i,n,m,o,h]}class Z1 extends ee{constructor(e){super(),x(this,e,X1,Q1,Z,{field:0,values:1})}}function Vr(l){let e,t;return e=new Ci({props:{target:l[5],$$slots:{default:[n_]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&32&&(s.target=n[5]),i&65539&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function $1(l){let e;return{c(){e=$("Clear")},m(t,n){v(t,e,n)},d(t){t&&k(e)}}}function x1(l){let e;return{c(){e=$("Today")},m(t,n){v(t,e,n)},d(t){t&&k(e)}}}function e_(l){let e;return{c(){e=$("Done")},m(t,n){v(t,e,n)},d(t){t&&k(e)}}}function t_(l){let e,t,n,i,s,r,a,o,u,f,c;function d(h){l[11](h)}function m(h){l[12](h)}let _={buttons:!1};return l[0]!==void 0&&(_.start=l[0]),l[1]!==void 0&&(_.end=l[1]),e=new ef({props:_}),ue.push(()=>Se(e,"start",d)),ue.push(()=>Se(e,"end",m)),r=new Ve({props:{type:"link wx-calendar-btn",click:l[8],$$slots:{default:[$1]},$$scope:{ctx:l}}}),o=new Ve({props:{type:"link wx-calendar-btn",click:l[7],$$slots:{default:[x1]},$$scope:{ctx:l}}}),f=new Ve({props:{type:"primary wx-calendar-btn",click:l[6],$$slots:{default:[e_]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment),i=H(),s=I("div"),P(r.$$.fragment),a=H(),P(o.$$.fragment),u=H(),P(f.$$.fragment),g(s,"class","wx-buttons svelte-d1z2io")},m(h,w){R(e,h,w),v(h,i,w),v(h,s,w),R(r,s,null),M(s,a),R(o,s,null),M(s,u),R(f,s,null),c=!0},p(h,w){const y={};!t&&w&1&&(t=!0,y.start=h[0],Ie(()=>t=!1)),!n&&w&2&&(n=!0,y.end=h[1],Ie(()=>n=!1)),e.$set(y);const S={};w&65536&&(S.$$scope={dirty:w,ctx:h}),r.$set(S);const C={};w&65536&&(C.$$scope={dirty:w,ctx:h}),o.$set(C);const T={};w&65536&&(T.$$scope={dirty:w,ctx:h}),f.$set(T)},i(h){c||(b(e.$$.fragment,h),b(r.$$.fragment,h),b(o.$$.fragment,h),b(f.$$.fragment,h),c=!0)},o(h){p(e.$$.fragment,h),p(r.$$.fragment,h),p(o.$$.fragment,h),p(f.$$.fragment,h),c=!1},d(h){h&&(k(i),k(s)),A(e,h),A(r),A(o),A(f)}}}function n_(l){let e,t;return e=new Ct({props:{cancel:l[6],width:"unset",$$slots:{default:[t_]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&65539&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function l_(l){let e,t,n,i,s,r,a,o,u;t=new An({props:{value:l[4],id:l[2],readonly:!0,inputStyle:"cursor: pointer; text-overflow: ellipsis; padding-right: 18px;"}}),s=new Me({props:{css:"wxi-calendar"}});let f=l[3]&&Vr(l);return{c(){e=I("div"),P(t.$$.fragment),n=H(),i=I("div"),P(s.$$.fragment),r=H(),f&&f.c(),g(i,"class","wx-input-icon svelte-d1z2io"),g(e,"class","wx-layout svelte-d1z2io")},m(c,d){v(c,e,d),R(t,e,null),M(e,n),M(e,i),R(s,i,null),M(e,r),f&&f.m(e,null),l[13](e),a=!0,o||(u=[te(window,"scroll",l[6]),te(e,"click",l[9])],o=!0)},p(c,[d]){const m={};d&16&&(m.value=c[4]),d&4&&(m.id=c[2]),t.$set(m),c[3]?f?(f.p(c,d),d&8&&b(f,1)):(f=Vr(c),f.c(),b(f,1),f.m(e,null)):f&&(W(),p(f,1,1,()=>{f=null}),Y())},i(c){a||(b(t.$$.fragment,c),b(s.$$.fragment,c),b(f),a=!0)},o(c){p(t.$$.fragment,c),p(s.$$.fragment,c),p(f),a=!1},d(c){c&&k(e),A(t),A(s),f&&f.d(),l[13](null),o=!1,De(u)}}}function i_(l,e,t){let{start:n=null}=e,{end:i=null}=e,{id:s=xe()}=e,{format:r}=e;const a=ve("wx-i18n").getGroup("calendar");let o,u=typeof r=="function"?r:Ft(r,a);function f(C){C.stopPropagation(),t(3,o=null)}function c(){const C=new Date;t(0,n=C),t(1,i=C)}function d(){t(0,n=null),t(1,i=null)}let m="";function _(){t(3,o=!0)}let h;function w(C){n=C,t(0,n)}function y(C){i=C,t(1,i)}function S(C){ue[C?"unshift":"push"](()=>{h=C,t(5,h)})}return l.$$set=C=>{"start"in C&&t(0,n=C.start),"end"in C&&t(1,i=C.end),"id"in C&&t(2,s=C.id),"format"in C&&t(10,r=C.format)},l.$$.update=()=>{l.$$.dirty&3&&(n?t(4,m=u(n)+(i?` - ${u(i)}`:"")):t(4,m=""))},[n,i,s,o,m,h,f,c,d,_,r,w,y,S]}class s_ extends ee{constructor(e){super(),x(this,e,i_,l_,Z,{start:0,end:1,id:2,format:10})}}function r_(l){let e,t,n,i;function s(o){l[4](o)}function r(o){l[5](o)}let a={id:l[3],format:l[2]};return l[0]!==void 0&&(a.start=l[0]),l[1]!==void 0&&(a.end=l[1]),e=new s_({props:a}),ue.push(()=>Se(e,"start",s)),ue.push(()=>Se(e,"end",r)),{c(){P(e.$$.fragment)},m(o,u){R(e,o,u),i=!0},p(o,[u]){const f={};u&8&&(f.id=o[3]),u&4&&(f.format=o[2]),!t&&u&1&&(t=!0,f.start=o[0],Ie(()=>t=!1)),!n&&u&2&&(n=!0,f.end=o[1],Ie(()=>n=!1)),e.$set(f)},i(o){i||(b(e.$$.fragment,o),i=!0)},o(o){p(e.$$.fragment,o),i=!1},d(o){A(e,o)}}}function o_(l,e,t){let{start:n}=e,{end:i}=e,{format:s}=e,{id:r=xe()}=e;function a(u){n=u,t(0,n)}function o(u){i=u,t(1,i)}return l.$$set=u=>{"start"in u&&t(0,n=u.start),"end"in u&&t(1,i=u.end),"format"in u&&t(2,s=u.format),"id"in u&&t(3,r=u.id)},[n,i,s,r,a,o]}class a_ extends ee{constructor(e){super(),x(this,e,o_,r_,Z,{start:0,end:1,format:2,id:3})}}function u_(l){let e,t,n;return{c(){e=I("input"),g(e,"type","text"),g(e,"class","wx-title svelte-18nf24j"),g(e,"id",l[1]),e.value=l[0],g(e,"placeholder",l[2]),e.readOnly=l[3],e.disabled=l[4]},m(i,s){v(i,e,s),t||(n=[te(e,"input",l[5]),He(l[6].call(null,e))],t=!0)},p(i,[s]){s&2&&g(e,"id",i[1]),s&1&&e.value!==i[0]&&(e.value=i[0]),s&4&&g(e,"placeholder",i[2]),s&8&&(e.readOnly=i[3]),s&16&&(e.disabled=i[4])},i:K,o:K,d(i){i&&k(e),t=!1,De(n)}}}function f_(l,e,t){let{id:n=String(pa())}=e,{value:i=""}=e,{focus:s=!1}=e,{placeholder:r=""}=e,{readonly:a=!1}=e,{disabled:o=!1}=e;function u(c){t(0,i=c.target.value)}function f(c){s&&c.focus()}return l.$$set=c=>{"id"in c&&t(1,n=c.id),"value"in c&&t(0,i=c.value),"focus"in c&&t(7,s=c.focus),"placeholder"in c&&t(2,r=c.placeholder),"readonly"in c&&t(3,a=c.readonly),"disabled"in c&&t(4,o=c.disabled)},[i,n,r,a,o,u,f,s]}class c_ extends ee{constructor(e){super(),x(this,e,f_,u_,Z,{id:1,value:0,focus:7,placeholder:2,readonly:3,disabled:4})}}function Kr(l,e,t){const n=l.slice();return n[34]=e[t],n}function Br(l,e){let t,n,i,s=e[34].label+"",r,a,o,u=e[13](e[34])+"",f,c,d,m,_,h,w;function y(){return e[23](e[34])}function S(){return e[24](e[34])}return m=new Me({props:{css:"wxi-delete-outline"}}),m.$on("click",S),{key:l,first:null,c(){t=I("div"),n=I("div"),i=I("div"),r=$(s),a=H(),o=I("div"),f=$(u),c=H(),d=I("div"),P(m.$$.fragment),g(i,"class","wx-relates svelte-qfer4l"),g(o,"class","wx-task svelte-qfer4l"),g(n,"class","wx-link-content"),g(d,"class","wx-delete-icon svelte-qfer4l"),g(t,"class","wx-link svelte-qfer4l"),this.first=t},m(C,T){v(C,t,T),M(t,n),M(n,i),M(i,r),M(n,a),M(n,o),M(o,f),M(t,c),M(t,d),R(m,d,null),_=!0,h||(w=te(o,"click",y),h=!0)},p(C,T){e=C,(!_||T[0]&2)&&s!==(s=e[34].label+"")&&re(r,s),(!_||T[0]&2)&&u!==(u=e[13](e[34])+"")&&re(f,u)},i(C){_||(b(m.$$.fragment,C),_=!0)},o(C){p(m.$$.fragment,C),_=!1},d(C){C&&k(t),A(m),h=!1,w()}}}function Wr(l){let e,t,n,i,s,r,a,o,u,f,c,d,m,_,h,w,y;function S(N){l[25](N)}let C={options:l[10],placeholder:l[7]("Select a relation"),$$slots:{default:[d_,({option:N})=>({33:N}),({option:N})=>[0,N?4:0]]},$$scope:{ctx:l}};l[2]!==void 0&&(C.value=l[2]),i=new Rn({props:C}),ue.push(()=>Se(i,"value",S)),i.$on("select",l[26]);function T(N){l[27](N)}let L={options:l[4],placeholder:l[7]("Select a task"),disabled:l[5],$$slots:{default:[m_,({option:N})=>({33:N}),({option:N})=>[0,N?4:0]]},$$scope:{ctx:l}};return l[3]!==void 0&&(L.value=l[3]),o=new Rn({props:L}),ue.push(()=>Se(o,"value",T)),m=new Ve({props:{type:"secondary block",click:l[12],$$slots:{default:[__]},$$scope:{ctx:l}}}),w=new Ve({props:{type:"primary block",click:l[28],$$slots:{default:[h_]},$$scope:{ctx:l}}}),{c(){e=I("div"),t=I("div"),n=I("div"),P(i.$$.fragment),r=H(),a=I("div"),P(o.$$.fragment),f=H(),c=I("div"),d=I("div"),P(m.$$.fragment),_=H(),h=I("div"),P(w.$$.fragment),g(n,"class","wx-relates-combo svelte-qfer4l"),g(a,"class","wx-tasks-combo svelte-qfer4l"),g(t,"class","wx-combos-wrapper svelte-qfer4l"),g(d,"class","wx-cancel-btn"),g(h,"class","wx-link-btn svelte-qfer4l"),g(c,"class","wx-btns-wrapper svelte-qfer4l"),g(e,"class","wx-set-link svelte-qfer4l")},m(N,E){v(N,e,E),M(e,t),M(t,n),R(i,n,null),M(t,r),M(t,a),R(o,a,null),M(e,f),M(e,c),M(c,d),R(m,d,null),M(c,_),M(c,h),R(w,h,null),y=!0},p(N,E){const F={};E[1]&68&&(F.$$scope={dirty:E,ctx:N}),!s&&E[0]&4&&(s=!0,F.value=N[2],Ie(()=>s=!1)),i.$set(F);const G={};E[0]&16&&(G.options=N[4]),E[0]&32&&(G.disabled=N[5]),E[1]&68&&(G.$$scope={dirty:E,ctx:N}),!u&&E[0]&8&&(u=!0,G.value=N[3],Ie(()=>u=!1)),o.$set(G);const U={};E[1]&64&&(U.$$scope={dirty:E,ctx:N}),m.$set(U);const O={};E[1]&64&&(O.$$scope={dirty:E,ctx:N}),w.$set(O)},i(N){y||(b(i.$$.fragment,N),b(o.$$.fragment,N),b(m.$$.fragment,N),b(w.$$.fragment,N),y=!0)},o(N){p(i.$$.fragment,N),p(o.$$.fragment,N),p(m.$$.fragment,N),p(w.$$.fragment,N),y=!1},d(N){N&&k(e),A(i),A(o),A(m),A(w)}}}function d_(l){let e=l[33].label+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p(n,i){i[1]&4&&e!==(e=n[33].label+"")&&re(t,e)},d(n){n&&k(t)}}}function m_(l){let e=l[33].label+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p(n,i){i[1]&4&&e!==(e=n[33].label+"")&&re(t,e)},d(n){n&&k(t)}}}function __(l){let e=l[7]("Cancel")+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p:K,d(n){n&&k(t)}}}function h_(l){let e=l[7]("Link Task")+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p:K,d(n){n&&k(t)}}}function g_(l){let e,t=[],n=new Map,i,s,r,a,o,u,f,c,d,m,_=fe(l[1]);const h=y=>y[34].id;for(let y=0;y<_.length;y+=1){let S=Kr(l,_,y),C=h(S);n.set(C,t[y]=Br(C,S))}let w=l[6]&&Wr(l);return a=new Me({props:{css:"wxi-plus"}}),{c(){e=I("div");for(let y=0;y<t.length;y+=1)t[y].c();i=H(),w&&w.c(),s=H(),r=I("div"),P(a.$$.fragment),o=H(),u=I("span"),u.textContent=`${l[7]("Add link")}`,g(r,"class","wx-add-link svelte-qfer4l"),g(e,"class",f="wx-links "+l[0]+" svelte-qfer4l")},m(y,S){v(y,e,S);for(let C=0;C<t.length;C+=1)t[C]&&t[C].m(e,null);M(e,i),w&&w.m(e,null),M(e,s),M(e,r),R(a,r,null),M(r,o),M(r,u),c=!0,d||(m=te(r,"click",l[11]),d=!0)},p(y,S){S[0]&172034&&(_=fe(y[1]),W(),t=Oe(t,S,h,1,y,_,n,e,Ue,Br,i,Kr),Y()),y[6]?w?(w.p(y,S),S[0]&64&&b(w,1)):(w=Wr(y),w.c(),b(w,1),w.m(e,s)):w&&(W(),p(w,1,1,()=>{w=null}),Y()),(!c||S[0]&1&&f!==(f="wx-links "+y[0]+" svelte-qfer4l"))&&g(e,"class",f)},i(y){if(!c){for(let S=0;S<_.length;S+=1)b(t[S]);b(w),b(a.$$.fragment,y),c=!0}},o(y){for(let S=0;S<t.length;S+=1)p(t[S]);p(w),p(a.$$.fragment,y),c=!1},d(y){y&&k(e);for(let S=0;S<t.length;S+=1)t[S].d();w&&w.d(),A(a),d=!1,m()}}}function b_(l,e,t){let n,i,{api:s}=e,{card:r}=e,{shape:a}=e,{fieldPlace:o}=e;const u=ve("wx-i18n").getGroup("kanban"),{showModal:f}=ve("wx-helpers"),{cards:c,links:d}=s.getReactiveState();he(l,c,B=>t(21,n=B)),he(l,d,B=>t(22,i=B));const m=[{id:1,relation:"relatesTo",master:!1,label:u("Relates to")},{id:2,relation:"requiredFor",master:!0,label:u("Is required for")},{id:3,relation:"requiredFor",master:!1,label:u("Depends on")},{id:4,relation:"duplicate",master:!0,label:u("Duplicates")},{id:5,relation:"duplicate",master:!1,label:u("Is duplicated by")},{id:6,relation:"parent",master:!0,label:u("Is parent for")},{id:7,relation:"parent",master:!1,label:u("Is subtask of")}];let _=null,h=null,w=[],y=null,S=!0,C=!1;const T=a.config||{};let L;function N(){t(6,C=!0)}function E(){t(6,C=!1),t(5,S=!0),t(3,h=t(2,_=""))}function F(B){const ie=B.isMaster?"slaveId":"masterId";return n.find(Fe=>le(Fe.id,B[ie])).label}function G(){t(4,w=w.filter(B=>!L.find(ie=>(le(B.id,ie.slaveId)&&le(ie.masterId,r.id)||le(B.id,ie.masterId)&&le(ie.slaveId,r.id))&&ie.relation===y)))}function U(){if(_&&h){const B=m.find(ie=>le(ie.id,_)).master;s.exec("add-link",{link:{masterId:B?r.id:h,slaveId:B?h:r.id,relation:y}}),E()}}function O(B){(T.confirmDeletion??!0?f({message:u("Would you like to delete this link?")}):Promise.resolve()).then(()=>{s.exec("delete-link",{id:B})}).catch(()=>{})}function D(){_&&(y=m.find(B=>le(B.id,_)).relation),G(),t(3,h=""),t(5,S=!1)}function j(B){const ie=B.isMaster?"slaveId":"masterId";s.exec("select-card",{id:B[ie]})}const z=B=>j(B),q=B=>O(B.id);function J(B){_=B,t(2,_)}const V=()=>D();function ne(B){h=B,t(3,h)}const Q=()=>U();return l.$$set=B=>{"api"in B&&t(18,s=B.api),"card"in B&&t(19,r=B.card),"shape"in B&&t(20,a=B.shape),"fieldPlace"in B&&t(0,o=B.fieldPlace)},l.$$.update=()=>{l.$$.dirty[0]&4718592&&t(1,L=i.filter(B=>le(B.masterId,r.id)||le(B.slaveId,r.id)).map(B=>{const ie={...B};return ie.isMaster=le(ie.masterId,r.id),ie.label=m.find(ge=>ge.relation===ie.relation&&(ie.relation==="relatesTo"||ie.isMaster===ge.master)).label,ie})),l.$$.dirty[0]&2621442&&(t(4,w=L&&n.filter(B=>!le(B.id,r.id))),E())},[o,L,_,h,w,S,C,u,c,d,m,N,E,F,U,O,D,j,s,r,a,n,i,z,q,J,V,ne,Q]}class p_ extends ee{constructor(e){super(),x(this,e,b_,g_,Z,{api:18,card:19,shape:20,fieldPlace:0},null,[-1,-1])}}function Yr(l,e,t){const n=l.slice();return n[23]=e[t],n[24]=e,n[25]=t,n}function w_(l){let e,t,n,i,s;return t=new lt({props:{label:l[8](l[23].label),position:"top",$$slots:{default:[z_]},$$scope:{ctx:l}}}),{c(){e=I("div"),P(t.$$.fragment),n=H(),g(e,"class",i="wx-card-comments "+l[6]+" svelte-1c331lt")},m(r,a){v(r,e,a),R(t,e,null),M(e,n),s=!0},p(r,a){const o={};a&1&&(o.label=r[8](r[23].label)),a&268435645&&(o.$$scope={dirty:a,ctx:r}),t.$set(o),(!s||a&64&&i!==(i="wx-card-comments "+r[6]+" svelte-1c331lt"))&&g(e,"class",i)},i(r){s||(b(t.$$.fragment,r),s=!0)},o(r){p(t.$$.fragment,r),s=!1},d(r){r&&k(e),A(t)}}}function k_(l){let e,t,n,i;return t=new lt({props:{label:l[8](l[23].label),position:"top",$$slots:{default:[H_]},$$scope:{ctx:l}}}),{c(){e=I("div"),P(t.$$.fragment),n=H(),g(e,"class","links svelte-1c331lt")},m(s,r){v(s,e,r),R(t,e,null),M(e,n),i=!0},p(s,r){const a={};r&1&&(a.label=s[8](s[23].label)),r&268435533&&(a.$$scope={dirty:r,ctx:s}),t.$set(a)},i(s){i||(b(t.$$.fragment,s),i=!0)},o(s){p(t.$$.fragment,s),i=!1},d(s){s&&k(e),A(t)}}}function v_(l){let e,t;return e=new lt({props:{label:l[8](l[23].label),position:"top",$$slots:{default:[O_]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.label=n[8](n[23].label)),i&268435459&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function y_(l){let e,t;return e=new lt({props:{label:l[8](l[23].label),position:"top",$$slots:{default:[F_,({id:n})=>({26:n}),({id:n})=>n?67108864:0]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.label=n[8](n[23].label)),i&335544449&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function S_(l){let e,t;return e=new lt({props:{label:l[8](l[23].label),position:"top",$$slots:{default:[q_,({id:n})=>({26:n}),({id:n})=>n?67108864:0]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.label=n[8](n[23].label)),i&335544449&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function C_(l){let e,t;return e=new lt({props:{label:l[8](l[23].label),position:"top",$$slots:{default:[K_]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.label=n[8](n[23].label)),i&268435585&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function I_(l){let e,t;return e=new lt({props:{label:l[8](l[23].label),position:"top",$$slots:{default:[B_,({id:n})=>({26:n}),({id:n})=>n?67108864:0]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.label=n[8](n[23].label)),i&335544449&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function M_(l){let e,t;return e=new lt({props:{label:l[8](l[23].label),position:"top",$$slots:{default:[W_,({id:n})=>({26:n}),({id:n})=>n?67108864:0]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.label=n[8](n[23].label)),i&335544449&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function T_(l){let e,t;return e=new lt({props:{label:l[8](l[23].label),position:"top",$$slots:{default:[Q_,({id:n})=>({26:n}),({id:n})=>n?67108864:0]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.label=n[8](n[23].label)),i&335544449&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function D_(l){let e,t;return e=new lt({props:{label:`${l[8](l[23].label)} ${l[7][l[23].key]}%`,position:"top",$$slots:{default:[X_,({id:n})=>({26:n}),({id:n})=>n?67108864:0]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&129&&(s.label=`${n[8](n[23].label)} ${n[7][n[23].key]}%`),i&335544449&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function E_(l){let e,t;return e=new lt({props:{label:l[8](l[23].label),position:"top",$$slots:{default:[Z_,({id:n})=>({26:n}),({id:n})=>n?67108864:0]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.label=n[8](n[23].label)),i&335544449&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function L_(l){let e,t;return e=new lt({props:{$$slots:{default:[$_,({id:n})=>({26:n}),({id:n})=>n?67108864:0]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&335544449&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function R_(l){let e,t;return e=new lt({props:{label:l[8](l[23].label),position:"top",$$slots:{default:[x_,({id:n})=>({26:n}),({id:n})=>n?67108864:0]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.label=n[8](n[23].label)),i&335544449&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function A_(l){let e,t;return e=new Ve({props:{type:"primary block",click:l[22],$$slots:{default:[N_]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&16&&(s.click=n[22]),i&268435464&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function P_(l){let e,t,n;function i(r){l[21](r,l[23])}let s={api:l[2],placement:l[5],users:l[23].values||l[9],shape:l[23]};return l[7][l[23].key]!==void 0&&(s.comments=l[7][l[23].key]),e=new Ar({props:s}),ue.push(()=>Se(e,"comments",i)),{c(){P(e.$$.fragment)},m(r,a){R(e,r,a),n=!0},p(r,a){l=r;const o={};a&4&&(o.api=l[2]),a&32&&(o.placement=l[5]),a&1&&(o.users=l[23].values||l[9]),a&1&&(o.shape=l[23]),!t&&a&129&&(t=!0,o.comments=l[7][l[23].key],Ie(()=>t=!1)),e.$set(o)},i(r){n||(b(e.$$.fragment,r),n=!0)},o(r){p(e.$$.fragment,r),n=!1},d(r){A(e,r)}}}function N_(l){let e=l[8]("Show comments")+"",t,n,i=(l[3].comments?.length||0)+"",s,r;return{c(){t=$(e),n=$(`
                                          (`),s=$(i),r=$(")")},m(a,o){v(a,t,o),v(a,n,o),v(a,s,o),v(a,r,o)},p(a,o){o&8&&i!==(i=(a[3].comments?.length||0)+"")&&re(s,i)},d(a){a&&(k(t),k(n),k(s),k(r))}}}function z_(l){let e,t,n,i;const s=[P_,A_],r=[];function a(o,u){return o[23].config?.placement==="editor"?0:1}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=se()},m(o,u){r[e].m(o,u),v(o,n,u),i=!0},p(o,u){let f=e;e=a(o),e===f?r[e].p(o,u):(W(),p(r[f],1,1,()=>{r[f]=null}),Y(),t=r[e],t?t.p(o,u):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(n.parentNode,n))},i(o){i||(b(t),i=!0)},o(o){p(t),i=!1},d(o){o&&k(n),r[e].d(o)}}}function H_(l){let e,t;return e=new p_({props:{fieldPlace:l[6],shape:l[0].find(Jr),card:l[3],api:l[2]}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&64&&(s.fieldPlace=n[6]),i&1&&(s.shape=n[0].find(Jr)),i&8&&(s.card=n[3]),i&4&&(s.api=n[2]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function O_(l){let e,t,n;return e=new Z1({props:{field:l[23],values:l[1]}}),{c(){P(e.$$.fragment),t=H()},m(i,s){R(e,i,s),v(i,t,s),n=!0},p(i,s){const r={};s&1&&(r.field=i[23]),s&2&&(r.values=i[1]),e.$set(r)},i(i){n||(b(e.$$.fragment,i),n=!0)},o(i){p(e.$$.fragment,i),n=!1},d(i){i&&k(t),A(e,i)}}}function F_(l){let e,t,n,i,s;function r(u){l[19](u,l[23])}function a(u){l[20](u,l[23])}let o={id:l[26],format:l[23].format||"%m/%d/%Y"};return l[7][l[23].key.start]!==void 0&&(o.start=l[7][l[23].key.start]),l[7][l[23].key.end]!==void 0&&(o.end=l[7][l[23].key.end]),e=new a_({props:o}),ue.push(()=>Se(e,"start",r)),ue.push(()=>Se(e,"end",a)),{c(){P(e.$$.fragment),i=H()},m(u,f){R(e,u,f),v(u,i,f),s=!0},p(u,f){l=u;const c={};f&67108864&&(c.id=l[26]),f&1&&(c.format=l[23].format||"%m/%d/%Y"),!t&&f&129&&(t=!0,c.start=l[7][l[23].key.start],Ie(()=>t=!1)),!n&&f&129&&(n=!0,c.end=l[7][l[23].key.end],Ie(()=>n=!1)),e.$set(c)},i(u){s||(b(e.$$.fragment,u),s=!0)},o(u){p(e.$$.fragment,u),s=!1},d(u){u&&k(i),A(e,u)}}}function q_(l){let e,t,n,i;const s=[{id:l[26]},{format:l[23].format||"%m/%d/%Y"},l[23].config];function r(o){l[18](o,l[23])}let a={};for(let o=0;o<s.length;o+=1)a=Ce(a,s[o]);return l[7][l[23].key]!==void 0&&(a.value=l[7][l[23].key]),e=new Qu({props:a}),ue.push(()=>Se(e,"value",r)),{c(){P(e.$$.fragment),n=H()},m(o,u){R(e,o,u),v(o,n,u),i=!0},p(o,u){l=o;const f=u&67108865?st(s,[u&67108864&&{id:l[26]},u&1&&{format:l[23].format||"%m/%d/%Y"},u&1&&rt(l[23].config)]):{};!t&&u&129&&(t=!0,f.value=l[7][l[23].key],Ie(()=>t=!1)),e.$set(f)},i(o){i||(b(e.$$.fragment,o),i=!0)},o(o){p(e.$$.fragment,o),i=!1},d(o){o&&k(n),A(e,o)}}}function j_(l){let e,t;return e=new Et({props:{data:l[27]}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&134217728&&(s.data=n[27]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function U_(l){let e;return{c(){e=I("div"),g(e,"class","wx-color svelte-1c331lt"),ce(e,"background",l[27].color)},m(t,n){v(t,e,n)},p(t,n){n&134217728&&ce(e,"background",t[27].color)},i:K,o:K,d(t){t&&k(e)}}}function V_(l){let e,t,n,i,s,r=l[27].label+"",a,o;const u=[U_,j_],f=[];function c(d,m){return d[27].color?0:d[27].avatar||d[27].avatarColor?1:-1}return~(t=c(l))&&(n=f[t]=u[t](l)),{c(){e=I("div"),n&&n.c(),i=H(),s=I("span"),a=$(r),g(s,"class","wx-multiselect-label svelte-1c331lt"),g(e,"class","wx-multiselect-option svelte-1c331lt")},m(d,m){v(d,e,m),~t&&f[t].m(e,null),M(e,i),M(e,s),M(s,a),o=!0},p(d,m){let _=t;t=c(d),t===_?~t&&f[t].p(d,m):(n&&(W(),p(f[_],1,1,()=>{f[_]=null}),Y()),~t?(n=f[t],n?n.p(d,m):(n=f[t]=u[t](d),n.c()),b(n,1),n.m(e,i)):n=null),(!o||m&134217728)&&r!==(r=d[27].label+"")&&re(a,r)},i(d){o||(b(n),o=!0)},o(d){p(n),o=!1},d(d){d&&k(e),~t&&f[t].d()}}}function K_(l){let e,t,n,i;const s=[{checkboxes:!0},{options:l[23].values},l[23].config];function r(o){l[17](o,l[23])}let a={$$slots:{default:[V_,({option:o})=>({27:o}),({option:o})=>o?134217728:0]},$$scope:{ctx:l}};for(let o=0;o<s.length;o+=1)a=Ce(a,s[o]);return l[7][l[23].key]!==void 0&&(a.value=l[7][l[23].key]),e=new mf({props:a}),ue.push(()=>Se(e,"value",r)),{c(){P(e.$$.fragment),n=H()},m(o,u){R(e,o,u),v(o,n,u),i=!0},p(o,u){l=o;const f=u&1?st(s,[s[0],{options:l[23].values},rt(l[23].config)]):{};u&402653184&&(f.$$scope={dirty:u,ctx:l}),!t&&u&129&&(t=!0,f.value=l[7][l[23].key],Ie(()=>t=!1)),e.$set(f)},i(o){i||(b(e.$$.fragment,o),i=!0)},o(o){p(e.$$.fragment,o),i=!1},d(o){o&&k(n),A(e,o)}}}function B_(l){let e,t,n,i;const s=[{id:l[26]},{colors:l[23].values},l[23].config];function r(o){l[16](o,l[23])}let a={};for(let o=0;o<s.length;o+=1)a=Ce(a,s[o]);return l[7][l[23].key]!==void 0&&(a.value=l[7][l[23].key]),e=new Ba({props:a}),ue.push(()=>Se(e,"value",r)),{c(){P(e.$$.fragment),n=H()},m(o,u){R(e,o,u),v(o,n,u),i=!0},p(o,u){l=o;const f=u&67108865?st(s,[u&67108864&&{id:l[26]},u&1&&{colors:l[23].values},u&1&&rt(l[23].config)]):{};!t&&u&129&&(t=!0,f.value=l[7][l[23].key],Ie(()=>t=!1)),e.$set(f)},i(o){i||(b(e.$$.fragment,o),i=!0)},o(o){p(e.$$.fragment,o),i=!1},d(o){o&&k(n),A(e,o)}}}function W_(l){let e,t,n,i;const s=[{id:l[26]},{options:l[23].values},l[23].config];function r(o){l[15](o,l[23])}let a={};for(let o=0;o<s.length;o+=1)a=Ce(a,s[o]);return l[7][l[23].key]!==void 0&&(a.value=l[7][l[23].key]),e=new ci({props:a}),ue.push(()=>Se(e,"value",r)),{c(){P(e.$$.fragment),n=H()},m(o,u){R(e,o,u),v(o,n,u),i=!0},p(o,u){l=o;const f=u&67108865?st(s,[u&67108864&&{id:l[26]},u&1&&{options:l[23].values},u&1&&rt(l[23].config)]):{};!t&&u&129&&(t=!0,f.value=l[7][l[23].key],Ie(()=>t=!1)),e.$set(f)},i(o){i||(b(e.$$.fragment,o),i=!0)},o(o){p(e.$$.fragment,o),i=!1},d(o){o&&k(n),A(e,o)}}}function Y_(l){let e,t;return e=new Et({props:{data:l[27]}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&134217728&&(s.data=n[27]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function G_(l){let e;return{c(){e=I("div"),g(e,"class","wx-color svelte-1c331lt"),ce(e,"background",l[27].color)},m(t,n){v(t,e,n)},p(t,n){n&134217728&&ce(e,"background",t[27].color)},i:K,o:K,d(t){t&&k(e)}}}function J_(l){let e,t,n,i,s=l[27].label+"",r,a;const o=[G_,Y_],u=[];function f(c,d){return c[27].color?0:c[27].avatar||c[27].avatarColor?1:-1}return~(t=f(l))&&(n=u[t]=o[t](l)),{c(){e=I("div"),n&&n.c(),i=H(),r=$(s),g(e,"class","wx-combo-option svelte-1c331lt")},m(c,d){v(c,e,d),~t&&u[t].m(e,null),M(e,i),M(e,r),a=!0},p(c,d){let m=t;t=f(c),t===m?~t&&u[t].p(c,d):(n&&(W(),p(u[m],1,1,()=>{u[m]=null}),Y()),~t?(n=u[t],n?n.p(c,d):(n=u[t]=o[t](c),n.c()),b(n,1),n.m(e,i)):n=null),(!a||d&134217728)&&s!==(s=c[27].label+"")&&re(r,s)},i(c){a||(b(n),a=!0)},o(c){p(n),a=!1},d(c){c&&k(e),~t&&u[t].d()}}}function Q_(l){let e,t,n,i;const s=[{id:l[26]},{options:l[23].values},l[23].config];function r(o){l[14](o,l[23])}let a={$$slots:{default:[J_,({option:o})=>({27:o}),({option:o})=>o?134217728:0]},$$scope:{ctx:l}};for(let o=0;o<s.length;o+=1)a=Ce(a,s[o]);return l[7][l[23].key]!==void 0&&(a.value=l[7][l[23].key]),e=new Rn({props:a}),ue.push(()=>Se(e,"value",r)),{c(){P(e.$$.fragment),n=H()},m(o,u){R(e,o,u),v(o,n,u),i=!0},p(o,u){l=o;const f=u&67108865?st(s,[u&67108864&&{id:l[26]},u&1&&{options:l[23].values},u&1&&rt(l[23].config)]):{};u&402653184&&(f.$$scope={dirty:u,ctx:l}),!t&&u&129&&(t=!0,f.value=l[7][l[23].key],Ie(()=>t=!1)),e.$set(f)},i(o){i||(b(e.$$.fragment,o),i=!0)},o(o){p(e.$$.fragment,o),i=!1},d(o){o&&k(n),A(e,o)}}}function X_(l){let e,t,n,i;const s=[{id:l[26]},{min:0},l[23].config];function r(o){l[13](o,l[23])}let a={};for(let o=0;o<s.length;o+=1)a=Ce(a,s[o]);return l[7][l[23].key]!==void 0&&(a.value=l[7][l[23].key]),e=new pf({props:a}),ue.push(()=>Se(e,"value",r)),{c(){P(e.$$.fragment),n=H()},m(o,u){R(e,o,u),v(o,n,u),i=!0},p(o,u){l=o;const f=u&67108865?st(s,[u&67108864&&{id:l[26]},s[1],u&1&&rt(l[23].config)]):{};!t&&u&129&&(t=!0,f.value=l[7][l[23].key],Ie(()=>t=!1)),e.$set(f)},i(o){i||(b(e.$$.fragment,o),i=!0)},o(o){p(e.$$.fragment,o),i=!1},d(o){o&&k(n),A(e,o)}}}function Z_(l){let e,t,n,i;const s=[{id:l[26]},l[23].config];function r(o){l[12](o,l[23])}let a={};for(let o=0;o<s.length;o+=1)a=Ce(a,s[o]);return l[7][l[23].key]!==void 0&&(a.value=l[7][l[23].key]),e=new Ln({props:a}),ue.push(()=>Se(e,"value",r)),{c(){P(e.$$.fragment),n=H()},m(o,u){R(e,o,u),v(o,n,u),i=!0},p(o,u){l=o;const f=u&67108865?st(s,[u&67108864&&{id:l[26]},u&1&&rt(l[23].config)]):{};!t&&u&129&&(t=!0,f.value=l[7][l[23].key],Ie(()=>t=!1)),e.$set(f)},i(o){i||(b(e.$$.fragment,o),i=!0)},o(o){p(e.$$.fragment,o),i=!1},d(o){o&&k(n),A(e,o)}}}function $_(l){let e,t,n,i;const s=[{id:l[26]},{focus:!0},l[23].config];function r(o){l[11](o,l[23])}let a={};for(let o=0;o<s.length;o+=1)a=Ce(a,s[o]);return l[7][l[23].key]!==void 0&&(a.value=l[7][l[23].key]),e=new c_({props:a}),ue.push(()=>Se(e,"value",r)),{c(){P(e.$$.fragment),n=H()},m(o,u){R(e,o,u),v(o,n,u),i=!0},p(o,u){l=o;const f=u&67108865?st(s,[u&67108864&&{id:l[26]},s[1],u&1&&rt(l[23].config)]):{};!t&&u&129&&(t=!0,f.value=l[7][l[23].key],Ie(()=>t=!1)),e.$set(f)},i(o){i||(b(e.$$.fragment,o),i=!0)},o(o){p(e.$$.fragment,o),i=!1},d(o){o&&k(n),A(e,o)}}}function x_(l){let e,t,n,i;const s=[{id:l[26]},{focus:!0},l[23].config];function r(o){l[10](o,l[23])}let a={};for(let o=0;o<s.length;o+=1)a=Ce(a,s[o]);return l[7][l[23].key]!==void 0&&(a.value=l[7][l[23].key]),e=new An({props:a}),ue.push(()=>Se(e,"value",r)),{c(){P(e.$$.fragment),n=H()},m(o,u){R(e,o,u),v(o,n,u),i=!0},p(o,u){l=o;const f=u&67108865?st(s,[u&67108864&&{id:l[26]},s[1],u&1&&rt(l[23].config)]):{};!t&&u&129&&(t=!0,f.value=l[7][l[23].key],Ie(()=>t=!1)),e.$set(f)},i(o){i||(b(e.$$.fragment,o),i=!0)},o(o){p(e.$$.fragment,o),i=!1},d(o){o&&k(n),A(e,o)}}}function Gr(l,e){let t,n,i,s,r;const a=[R_,L_,E_,D_,T_,M_,I_,C_,S_,y_,v_,k_,w_],o=[];function u(f,c){return f[23].type==="text"?0:f[23].type==="title"?1:f[23].type==="textarea"?2:f[23].type==="progress"?3:f[23].type==="combo"?4:f[23].type==="select"?5:f[23].type==="color"?6:f[23].type==="multiselect"?7:f[23].type==="date"?8:f[23].type==="dateRange"?9:f[23].type==="files"?10:f[23].type==="links"?11:f[23].type==="comments"&&(f[23].values?.length||f[9]?.length)?12:-1}return~(n=u(e))&&(i=o[n]=a[n](e)),{key:l,first:null,c(){t=se(),i&&i.c(),s=se(),this.first=t},m(f,c){v(f,t,c),~n&&o[n].m(f,c),v(f,s,c),r=!0},p(f,c){e=f;let d=n;n=u(e),n===d?~n&&o[n].p(e,c):(i&&(W(),p(o[d],1,1,()=>{o[d]=null}),Y()),~n?(i=o[n],i?i.p(e,c):(i=o[n]=a[n](e),i.c()),b(i,1),i.m(s.parentNode,s)):i=null)},i(f){r||(b(i),r=!0)},o(f){p(i),r=!1},d(f){f&&(k(t),k(s)),~n&&o[n].d(f)}}}function eh(l){let e=[],t=new Map,n,i,s=fe(l[0]);const r=a=>a[23].id;for(let a=0;a<s.length;a+=1){let o=Yr(l,s,a),u=r(o);t.set(u,e[a]=Gr(u,o))}return{c(){for(let a=0;a<e.length;a+=1)e[a].c();n=se()},m(a,o){for(let u=0;u<e.length;u+=1)e[u]&&e[u].m(a,o);v(a,n,o),i=!0},p(a,[o]){o&201327615&&(s=fe(a[0]),W(),e=Oe(e,o,r,1,a,s,t,n.parentNode,Ue,Gr,n,Yr),Y())},i(a){if(!i){for(let o=0;o<s.length;o+=1)b(e[o]);i=!0}},o(a){for(let o=0;o<e.length;o+=1)p(e[o]);i=!1},d(a){a&&k(n);for(let o=0;o<e.length;o+=1)e[o].d(a)}}}const Jr=l=>l.type==="links";function th(l,e,t){let n,i=K,s=()=>(i(),i=Ge(a,D=>t(7,n=D)),a);l.$$.on_destroy.push(()=>i());let{fields:r=[]}=e,{values:a}=e;s();let{api:o}=e,{editCard:u}=e,{handleViewChange:f}=e,{placement:c}=e,{fieldsPlace:d="right"}=e;const m=ve("wx-i18n").getGroup("kanban"),_=o.getState().cardShape.users?.values;function h(D,j){l.$$.not_equal(n[j.key],D)&&(n[j.key]=D,a.set(n))}function w(D,j){l.$$.not_equal(n[j.key],D)&&(n[j.key]=D,a.set(n))}function y(D,j){l.$$.not_equal(n[j.key],D)&&(n[j.key]=D,a.set(n))}function S(D,j){l.$$.not_equal(n[j.key],D)&&(n[j.key]=D,a.set(n))}function C(D,j){l.$$.not_equal(n[j.key],D)&&(n[j.key]=D,a.set(n))}function T(D,j){l.$$.not_equal(n[j.key],D)&&(n[j.key]=D,a.set(n))}function L(D,j){l.$$.not_equal(n[j.key],D)&&(n[j.key]=D,a.set(n))}function N(D,j){l.$$.not_equal(n[j.key],D)&&(n[j.key]=D,a.set(n))}function E(D,j){l.$$.not_equal(n[j.key],D)&&(n[j.key]=D,a.set(n))}function F(D,j){l.$$.not_equal(n[j.key.start],D)&&(n[j.key.start]=D,a.set(n))}function G(D,j){l.$$.not_equal(n[j.key.end],D)&&(n[j.key.end]=D,a.set(n))}function U(D,j){l.$$.not_equal(n[j.key],D)&&(n[j.key]=D,a.set(n))}const O=()=>f("comments");return l.$$set=D=>{"fields"in D&&t(0,r=D.fields),"values"in D&&s(t(1,a=D.values)),"api"in D&&t(2,o=D.api),"editCard"in D&&t(3,u=D.editCard),"handleViewChange"in D&&t(4,f=D.handleViewChange),"placement"in D&&t(5,c=D.placement),"fieldsPlace"in D&&t(6,d=D.fieldsPlace)},[r,a,o,u,f,c,d,n,m,_,h,w,y,S,C,T,L,N,E,F,G,U,O]}class Qr extends ee{constructor(e){super(),x(this,e,th,eh,Z,{fields:0,values:1,api:2,editCard:3,handleViewChange:4,placement:5,fieldsPlace:6})}}function Xr(l,e,t){const n=l.slice();return n[40]=e[t],n}function Zr(l){let e,t,n,i,s,r,a,o,u,f,c,d=l[13]==="main"&&!l[0].autoSave&&l[21]!=="modal"&&$r(l);const m=[ih,lh],_=[];function h(C,T){return C[13]==="main"?0:1}i=h(l),s=_[i]=m[i](l);const w=[uh,ah],y=[];function S(C,T){return C[13]==="main"?0:C[13]==="comments"?1:-1}return~(o=S(l))&&(u=y[o]=w[o](l)),{c(){e=I("div"),d&&d.c(),t=H(),n=I("div"),s.c(),a=H(),u&&u.c(),f=se(),g(n,"class",r="wx-editor-controls"+(l[20]&&l[13]==="comments"?" comments":"")+" svelte-caibrn"),g(e,"class","wx-editor-controls-wrapper svelte-caibrn")},m(C,T){v(C,e,T),d&&d.m(e,null),M(e,t),M(e,n),_[i].m(n,null),v(C,a,T),~o&&y[o].m(C,T),v(C,f,T),c=!0},p(C,T){C[13]==="main"&&!C[0].autoSave&&C[21]!=="modal"?d?(d.p(C,T),T[0]&8193&&b(d,1)):(d=$r(C),d.c(),b(d,1),d.m(e,t)):d&&(W(),p(d,1,1,()=>{d=null}),Y());let L=i;i=h(C),i===L?_[i].p(C,T):(W(),p(_[L],1,1,()=>{_[L]=null}),Y(),s=_[i],s?s.p(C,T):(s=_[i]=m[i](C),s.c()),b(s,1),s.m(n,null)),(!c||T[0]&8192&&r!==(r="wx-editor-controls"+(C[20]&&C[13]==="comments"?" comments":"")+" svelte-caibrn"))&&g(n,"class",r);let N=o;o=S(C),o===N?~o&&y[o].p(C,T):(u&&(W(),p(y[N],1,1,()=>{y[N]=null}),Y()),~o?(u=y[o],u?u.p(C,T):(u=y[o]=w[o](C),u.c()),b(u,1),u.m(f.parentNode,f)):u=null)},i(C){c||(b(d),b(s),b(u),c=!0)},o(C){p(d),p(s),p(u),c=!1},d(C){C&&(k(e),k(a),k(f)),d&&d.d(),_[i].d(),~o&&y[o].d(C)}}}function $r(l){let e,t,n;return t=new Ve({props:{type:"primary wx-editor-btn",click:l[22],$$slots:{default:[nh]},$$scope:{ctx:l}}}),{c(){e=I("div"),P(t.$$.fragment),g(e,"class","wx-editor-save-button svelte-caibrn")},m(i,s){v(i,e,s),R(t,e,null),n=!0},p(i,s){const r={};s[0]&4096|s[1]&4096&&(r.$$scope={dirty:s,ctx:i}),t.$set(r)},i(i){n||(b(t.$$.fragment,i),n=!0)},o(i){p(t.$$.fragment,i),n=!1},d(i){i&&k(e),A(t)}}}function nh(l){let e=l[12]("Save")+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p(n,i){i[0]&4096&&e!==(e=n[12]("Save")+"")&&re(t,e)},d(n){n&&k(t)}}}function lh(l){let e,t;return e=new Ve({props:{click:l[35],$$slots:{default:[sh]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i[0]&4096|i[1]&4096&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function ih(l){let e,t,n,i=l[19].votes?.show&&xr(l);return t=new Me({props:{css:"wxi-close"}}),t.$on("click",l[24]),{c(){i&&i.c(),e=H(),P(t.$$.fragment)},m(s,r){i&&i.m(s,r),v(s,e,r),R(t,s,r),n=!0},p(s,r){s[19].votes?.show?i?(i.p(s,r),r[0]&524288&&b(i,1)):(i=xr(s),i.c(),b(i,1),i.m(e.parentNode,e)):i&&(W(),p(i,1,1,()=>{i=null}),Y())},i(s){n||(b(i),b(t.$$.fragment,s),n=!0)},o(s){p(i),p(t.$$.fragment,s),n=!1},d(s){s&&k(e),i&&i.d(s),A(t,s)}}}function sh(l){let e=l[12]("Back")+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p(n,i){i[0]&4096&&e!==(e=n[12]("Back")+"")&&re(t,e)},d(n){n&&k(t)}}}function xr(l){let e,t,n,i,s,r,a;n=new Ve({props:{click:l[26],icon:"wxi-like",$$slots:{default:[rh]},$$scope:{ctx:l}}});let o=l[14]&&l[15]?.length&&eo(l);return{c(){e=I("div"),t=I("div"),P(n.$$.fragment),i=H(),o&&o.c(),g(t,"class","wx-kanban-editor-vote svelte-caibrn"),X(t,"wx-kanban-editor-voted",l[9].votes?.includes(l[18])),g(e,"class","wx-kanban-editor-voting svelte-caibrn")},m(u,f){v(u,e,f),M(e,t),R(n,t,null),M(e,i),o&&o.m(e,null),s=!0,r||(a=[te(t,"mouseenter",l[32]),te(t,"mouseleave",l[33])],r=!0)},p(u,f){const c={};f[0]&512|f[1]&4096&&(c.$$scope={dirty:f,ctx:u}),n.$set(c),(!s||f[0]&262656)&&X(t,"wx-kanban-editor-voted",u[9].votes?.includes(u[18])),u[14]&&u[15]?.length?o?(o.p(u,f),f[0]&49152&&b(o,1)):(o=eo(u),o.c(),b(o,1),o.m(e,null)):o&&(W(),p(o,1,1,()=>{o=null}),Y())},i(u){s||(b(n.$$.fragment,u),b(o),s=!0)},o(u){p(n.$$.fragment,u),p(o),s=!1},d(u){u&&k(e),A(n),o&&o.d(),r=!1,De(a)}}}function rh(l){let e=(l[9].votes?.length||0)+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p(n,i){i[0]&512&&e!==(e=(n[9].votes?.length||0)+"")&&re(t,e)},d(n){n&&k(t)}}}function eo(l){let e,t;return e=new Ct({props:{width:"230px",cancel:l[34],align:"end",$$slots:{default:[oh]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i[0]&16384&&(s.cancel=n[34]),i[0]&32768|i[1]&4096&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function to(l,e){let t,n,i,s,r=e[40].label+"",a,o,u;return n=new Et({props:{size:"small",data:e[40]}}),{key:l,first:null,c(){t=I("div"),P(n.$$.fragment),i=H(),s=I("span"),a=$(r),o=H(),g(s,"class","wx-multiselect-label svelte-caibrn"),g(t,"class","wx-multiselect-option svelte-caibrn"),this.first=t},m(f,c){v(f,t,c),R(n,t,null),M(t,i),M(t,s),M(s,a),M(t,o),u=!0},p(f,c){e=f;const d={};c[0]&32768&&(d.data=e[40]),n.$set(d),(!u||c[0]&32768)&&r!==(r=e[40].label+"")&&re(a,r)},i(f){u||(b(n.$$.fragment,f),u=!0)},o(f){p(n.$$.fragment,f),u=!1},d(f){f&&k(t),A(n)}}}function oh(l){let e,t=[],n=new Map,i,s=fe(l[15]);const r=a=>a[40].id;for(let a=0;a<s.length;a+=1){let o=Xr(l,s,a),u=r(o);n.set(u,t[a]=to(u,o))}return{c(){e=I("div");for(let a=0;a<t.length;a+=1)t[a].c();g(e,"class","wx-kanban-voters-list svelte-caibrn")},m(a,o){v(a,e,o);for(let u=0;u<t.length;u+=1)t[u]&&t[u].m(e,null);i=!0},p(a,o){o[0]&32768&&(s=fe(a[15]),W(),t=Oe(t,o,r,1,a,s,n,e,Ue,to,null,Xr),Y())},i(a){if(!i){for(let o=0;o<s.length;o+=1)b(t[o]);i=!0}},o(a){for(let o=0;o<t.length;o+=1)p(t[o]);i=!1},d(a){a&&k(e);for(let o=0;o<t.length;o+=1)t[o].d()}}}function ah(l){let e,t,n;function i(r){l[36](r)}let s={api:l[2],users:l[7],shape:l[1].find(io),placement:l[21],handleViewChange:l[25]};return l[9].comments!==void 0&&(s.comments=l[9].comments),e=new Ar({props:s}),ue.push(()=>Se(e,"comments",i)),{c(){P(e.$$.fragment)},m(r,a){R(e,r,a),n=!0},p(r,a){const o={};a[0]&4&&(o.api=r[2]),a[0]&128&&(o.users=r[7]),a[0]&2&&(o.shape=r[1].find(io)),!t&&a[0]&512&&(t=!0,o.comments=r[9].comments,Ie(()=>t=!1)),e.$set(o)},i(r){n||(b(e.$$.fragment,r),n=!0)},o(r){p(e.$$.fragment,r),n=!1},d(r){A(e,r)}}}function uh(l){let e,t,n,i,s,r,a,o=l[20]&&l[8].length&&no(l);i=new Qr({props:{fields:l[16],fieldsPlace:"right",placement:l[21],values:l[23],api:l[2],editCard:l[3],handleViewChange:l[25]}});let u=l[21]==="modal"&&l[13]==="main"&&!l[0].autoSave&&lo(l);return{c(){e=I("div"),o&&o.c(),t=H(),n=I("div"),P(i.$$.fragment),s=H(),u&&u.c(),r=se(),g(n,"class","wx-kanban-editor-right svelte-caibrn"),g(e,"class","wx-kanban-editor-main svelte-caibrn")},m(f,c){v(f,e,c),o&&o.m(e,null),M(e,t),M(e,n),R(i,n,null),v(f,s,c),u&&u.m(f,c),v(f,r,c),a=!0},p(f,c){f[20]&&f[8].length?o?(o.p(f,c),c[0]&256&&b(o,1)):(o=no(f),o.c(),b(o,1),o.m(e,t)):o&&(W(),p(o,1,1,()=>{o=null}),Y());const d={};c[0]&65536&&(d.fields=f[16]),c[0]&4&&(d.api=f[2]),c[0]&8&&(d.editCard=f[3]),i.$set(d),f[21]==="modal"&&f[13]==="main"&&!f[0].autoSave?u?(u.p(f,c),c[0]&8193&&b(u,1)):(u=lo(f),u.c(),b(u,1),u.m(r.parentNode,r)):u&&(W(),p(u,1,1,()=>{u=null}),Y())},i(f){a||(b(o),b(i.$$.fragment,f),b(u),a=!0)},o(f){p(o),p(i.$$.fragment,f),p(u),a=!1},d(f){f&&(k(e),k(s),k(r)),o&&o.d(),A(i),u&&u.d(f)}}}function no(l){let e,t,n;return t=new Qr({props:{fields:l[8],fieldsPlace:"left",placement:l[21],values:l[23],api:l[2],editCard:l[3],handleViewChange:l[25]}}),{c(){e=I("div"),P(t.$$.fragment),g(e,"class","wx-kanban-editor-left svelte-caibrn")},m(i,s){v(i,e,s),R(t,e,null),n=!0},p(i,s){const r={};s[0]&256&&(r.fields=i[8]),s[0]&4&&(r.api=i[2]),s[0]&8&&(r.editCard=i[3]),t.$set(r)},i(i){n||(b(t.$$.fragment,i),n=!0)},o(i){p(t.$$.fragment,i),n=!1},d(i){i&&k(e),A(t)}}}function lo(l){let e,t,n,i,s,r,a;return n=new Ve({props:{type:"wx-editor-btn",click:l[24],$$slots:{default:[fh]},$$scope:{ctx:l}}}),r=new Ve({props:{type:"primary wx-editor-btn",click:l[22],$$slots:{default:[ch]},$$scope:{ctx:l}}}),{c(){e=I("div"),t=I("div"),P(n.$$.fragment),i=H(),s=I("div"),P(r.$$.fragment),g(t,"class","wx-editor-cancel-button svelte-caibrn"),g(s,"class","wx-editor-save-button svelte-caibrn"),g(e,"class","wx-editor-manual-save svelte-caibrn")},m(o,u){v(o,e,u),M(e,t),R(n,t,null),M(e,i),M(e,s),R(r,s,null),a=!0},p(o,u){const f={};u[0]&4096|u[1]&4096&&(f.$$scope={dirty:u,ctx:o}),n.$set(f);const c={};u[0]&4096|u[1]&4096&&(c.$$scope={dirty:u,ctx:o}),r.$set(c)},i(o){a||(b(n.$$.fragment,o),b(r.$$.fragment,o),a=!0)},o(o){p(n.$$.fragment,o),p(r.$$.fragment,o),a=!1},d(o){o&&k(e),A(n),A(r)}}}function fh(l){let e=l[12]("Cancel")+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p(n,i){i[0]&4096&&e!==(e=n[12]("Cancel")+"")&&re(t,e)},d(n){n&&k(t)}}}function ch(l){let e=l[12]("Save")+"",t;return{c(){t=$(e)},m(n,i){v(n,t,i)},p(n,i){i[0]&4096&&e!==(e=n[12]("Save")+"")&&re(t,e)},d(n){n&&k(t)}}}function dh(l){let e,t,n,i,s,r,a,o;function u(d){l[31](d)}let f={};l[12]!==void 0&&(f._=l[12]),e=new h1({props:f}),ue.push(()=>Se(e,"_",u));let c=l[12]&&l[3]&&Zr(l);return{c(){P(e.$$.fragment),n=H(),i=I("div"),c&&c.c(),g(i,"class",s="wx-editor "+l[21]+" "+l[13]+" svelte-caibrn"),g(i,"data-kanban-id",Xe.editor),g(i,"data-wx-widget",Xe.editor),X(i,"wx-modal-narrow",l[17]),X(i,"wx-editor-open",l[3])},m(d,m){R(e,d,m),v(d,n,m),v(d,i,m),c&&c.m(i,null),r=!0,a||(o=te(i,"click",l[30]),a=!0)},p(d,m){const _={};!t&&m[0]&4096&&(t=!0,_._=d[12],Ie(()=>t=!1)),e.$set(_),d[12]&&d[3]?c?(c.p(d,m),m[0]&4104&&b(c,1)):(c=Zr(d),c.c(),b(c,1),c.m(i,null)):c&&(W(),p(c,1,1,()=>{c=null}),Y()),(!r||m[0]&8192&&s!==(s="wx-editor "+d[21]+" "+d[13]+" svelte-caibrn"))&&g(i,"class",s),(!r||m[0]&139264)&&X(i,"wx-modal-narrow",d[17]),(!r||m[0]&8200)&&X(i,"wx-editor-open",d[3])},i(d){r||(b(e.$$.fragment,d),b(c),r=!0)},o(d){p(e.$$.fragment,d),p(c),r=!1},d(d){d&&(k(n),k(i)),A(e,d),c&&c.d(),a=!1,o()}}}function mh(l){let e,t;return e=new Ai({props:{words:{...bl,...Tn},optional:!0,$$slots:{default:[dh]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i[0]&1045391|i[1]&4096&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}const io=l=>l.type==="comments";function _h(l,e,t){let n,i,s,r=K,a=()=>(r(),r=Ge(z,oe=>t(18,s=oe)),z),o,u=K,f=()=>(u(),u=Ge(U,oe=>t(27,o=oe)),U),c,d=K,m=()=>(d(),d=Ge(D,oe=>t(28,c=oe)),D),_,h=K,w=()=>(h(),h=Ge(j,oe=>t(29,_=oe)),j),y,S=K,C=()=>(S(),S=Ge(O,oe=>t(19,y=oe)),O);l.$$.on_destroy.push(()=>r()),l.$$.on_destroy.push(()=>u()),l.$$.on_destroy.push(()=>d()),l.$$.on_destroy.push(()=>h()),l.$$.on_destroy.push(()=>S());let{config:T=Dt}=e,{shape:L=null}=e,{api:N}=e;const E=T?.placement==="modal",F=T.placement||"sidebar";let G=null,U=null;f();let O=null;C();let D=null;m();let j=null;w();let z=null;a();function q(){N.exec("update-card",{card:{...i},id:i.id})}function J(){q(),F==="modal"&&Q()}const V=Mr({},()=>{T.autoSave&&q()},{debounce:T.debounce});he(l,V,oe=>t(9,i=oe));function ne(oe){const Ne={...oe};return L.forEach(Ke=>{typeof Ne[Ke.key]>"u"&&(Ke.type==="files"||Ke.type==="comments"?Ne[Ke.key]=[]:Ke.type==="date"?Ne[Ke.key]=null:Ke.type==="progress"?Ne[Ke.key]=0:Ne[Ke.key]="")}),Ne}function Q(){N.exec("set-edit",null)}let B,ie="main";function ge(oe){t(13,ie=oe)}let Fe=!1;function me(){const oe=i.id;i.votes?.includes(s)?(N.exec("delete-vote",{cardId:oe}),Pt(V,i.votes=G.votes.filter(Ne=>Ne!==s),i)):(N.exec("add-vote",{cardId:oe}),Pt(V,i.votes=[...i.votes||[],s],i))}let Te=[],Ye=[],de=[],Re=[];function Ae(oe){if(t(8,de=[]),F==="sidebar"){t(16,Re=[...oe]);return}oe.forEach(Ne=>{Ne.modalSection==="left"?de.push(Ne):Re.push(Ne)})}function Pe(oe){ye.call(this,l,oe)}function it(oe){B=oe,t(12,B)}const Ze=()=>t(14,Fe=!0),$e=()=>t(14,Fe=!1),tt=()=>t(14,Fe=!1),ae=()=>ge("main");function Qe(oe){l.$$.not_equal(i.comments,oe)&&(i.comments=oe,V.set(i))}return l.$$set=oe=>{"config"in oe&&t(0,T=oe.config),"shape"in oe&&t(1,L=oe.shape),"api"in oe&&t(2,N=oe.api)},l.$$.update=()=>{if(l.$$.dirty[0]&1&&t(0,T={...Dt,...T}),l.$$.dirty[0]&52&&N&&!U&&!O){const oe=N.getReactiveState();f(t(4,U=oe.edit)),C(t(5,O=oe.cardShape)),m(t(6,D=oe.cards)),w(t(10,j=oe.dragItemId)),a(t(11,z=oe.currentUser))}if(l.$$.dirty[0]&2&&Ae(L),l.$$.dirty[0]&939524162){const oe=!_&&D&&c.find(Ne=>Ne.id===o?.cardId);if(L&&oe?.priority!==void 0){const Ne=L.find(Ke=>Ke.key==="priority");Ne&&!Ne.values.find(Ke=>Ke.id===oe.priority)&&(oe.priority=null)}t(3,G=oe)}if(l.$$.dirty[0]&10&&L&&V.reset(ne(G)),l.$$.dirty[0]&644&&N){const oe=N.getState().cardShape.users;oe?.values?.length&&(t(7,Te=oe.values),i?.votes?.length?t(15,Ye=i.votes.map(Ne=>Te.find(Ke=>Ke.id==Ne))):t(15,Ye=[]))}l.$$.dirty[0]&256&&t(17,n=E&&!de.length)},[T,L,N,G,U,O,D,Te,de,i,j,z,B,ie,Fe,Ye,Re,n,s,y,E,F,J,V,Q,ge,me,o,c,_,Pe,it,Ze,$e,tt,ae,Qe]}let Qn=class extends ee{constructor(e){super(),x(this,e,_h,mh,Z,{config:0,shape:1,api:2},null,[-1,-1])}};function hh(l){let e,t,n,i,s;return t=new Qn({props:{api:l[1],config:l[0],shape:l[2]}}),{c(){e=I("div"),P(t.$$.fragment),g(e,"class","wx-sidebar svelte-10gkwu4"),g(e,"data-kanban-id",Xe.editor),X(e,"wx-sidebar-open",!!l[3])},m(r,a){v(r,e,a),R(t,e,null),n=!0,i||(s=te(e,"click",l[6]),i=!0)},p(r,a){const o={};a&2&&(o.api=r[1]),a&1&&(o.config=r[0]),a&4&&(o.shape=r[2]),t.$set(o),(!n||a&8)&&X(e,"wx-sidebar-open",!!r[3])},i(r){n||(b(t.$$.fragment,r),n=!0)},o(r){p(t.$$.fragment,r),n=!1},d(r){r&&k(e),A(t),i=!1,s()}}}function gh(l){let e,t,n=l[3]&&so(l);return{c(){n&&n.c(),e=se()},m(i,s){n&&n.m(i,s),v(i,e,s),t=!0},p(i,s){i[3]?n?(n.p(i,s),s&8&&b(n,1)):(n=so(i),n.c(),b(n,1),n.m(e.parentNode,e)):n&&(W(),p(n,1,1,()=>{n=null}),Y())},i(i){t||(b(n),t=!0)},o(i){p(n),t=!1},d(i){i&&k(e),n&&n.d(i)}}}function so(l){let e,t;return e=new Ci({props:{$$slots:{default:[ph]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&135&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function bh(l){let e,t;return e=new Qn({props:{api:l[1],config:l[0],shape:l[2]}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&2&&(s.api=n[1]),i&1&&(s.config=n[0]),i&4&&(s.shape=n[2]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function ph(l){let e,t,n;return t=new Vf({props:{$$slots:{default:[bh]},$$scope:{ctx:l}}}),{c(){e=I("div"),P(t.$$.fragment),g(e,"class","wx-editor-modal svelte-10gkwu4"),g(e,"data-kanban-id",Xe.editor)},m(i,s){v(i,e,s),R(t,e,null),n=!0},p(i,s){const r={};s&135&&(r.$$scope={dirty:s,ctx:i}),t.$set(r)},i(i){n||(b(t.$$.fragment,i),n=!0)},o(i){p(t.$$.fragment,i),n=!1},d(i){i&&k(e),A(t)}}}function wh(l){let e,t,n,i;const s=[gh,hh],r=[];function a(o,u){return o[0].placement==="modal"?0:1}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=se()},m(o,u){r[e].m(o,u),v(o,n,u),i=!0},p(o,[u]){let f=e;e=a(o),e===f?r[e].p(o,u):(W(),p(r[f],1,1,()=>{r[f]=null}),Y(),t=r[e],t?t.p(o,u):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(n.parentNode,n))},i(o){i||(b(t),i=!0)},o(o){p(t),i=!1},d(o){o&&k(n),r[e].d(o)}}}function kh(l,e,t){let n,i,{config:s=Dt}=e,{api:r}=e,{shape:a}=e;const{edit:o}=r.getReactiveState();he(l,o,f=>t(5,i=f));function u(f){ye.call(this,l,f)}return l.$$set=f=>{"config"in f&&t(0,s=f.config),"api"in f&&t(1,r=f.api),"shape"in f&&t(2,a=f.shape)},l.$$.update=()=>{l.$$.dirty&34&&t(3,n=r.getCard(i?.cardId)),l.$$.dirty&1&&t(0,s={...Dt,...s})},[s,r,a,n,o,i,u]}class vh extends ee{constructor(e){super(),x(this,e,kh,wh,Z,{config:0,api:1,shape:2})}}function ro(l,e,t){const n=l.slice();return n[52]=e[t],n}function oo(l,e,t){const n=l.slice();return n[55]=e[t],n}function ao(l,e,t){const n=l.slice();return n[52]=e[t],n}function uo(l,e,t){const n=l.slice();return n[55]=e[t],n}function yh(l){let e,t,n,i;e=new Cr({props:{columns:l[12],areasMeta:l[13],contentEl:l[3],api:l[1]}}),e.$on("action",l[41]);let s=l[12].length&&fo(l);return{c(){P(e.$$.fragment),t=H(),s&&s.c(),n=se()},m(r,a){R(e,r,a),v(r,t,a),s&&s.m(r,a),v(r,n,a),i=!0},p(r,a){const o={};a[0]&4096&&(o.columns=r[12]),a[0]&8192&&(o.areasMeta=r[13]),a[0]&8&&(o.contentEl=r[3]),a[0]&2&&(o.api=r[1]),e.$set(o),r[12].length?s?(s.p(r,a),a[0]&4096&&b(s,1)):(s=fo(r),s.c(),b(s,1),s.m(n.parentNode,n)):s&&(W(),p(s,1,1,()=>{s=null}),Y())},i(r){i||(b(e.$$.fragment,r),b(s),i=!0)},o(r){p(e.$$.fragment,r),p(s),i=!1},d(r){r&&(k(t),k(n)),A(e,r),s&&s.d(r)}}}function Sh(l){let e,t;return e=new f1({props:{api:l[1],$$slots:{default:[Mh,({startIndex:n,endIndex:i,byRow:s,virtualContentEl:r})=>({48:n,49:i,50:s,51:r}),({startIndex:n,endIndex:i,byRow:s,virtualContentEl:r})=>[0,(n?131072:0)|(i?262144:0)|(s?524288:0)|(r?1048576:0)]]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i[0]&2&&(s.api=n[1]),i[0]&127043|i[1]&1966080|i[2]&1&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function fo(l){let e=[],t=new Map,n,i,s=fe(l[14]);const r=a=>a[52].id;for(let a=0;a<s.length;a+=1){let o=ro(l,s,a),u=r(o);t.set(u,e[a]=mo(u,o))}return{c(){for(let a=0;a<e.length;a+=1)e[a].c();n=se()},m(a,o){for(let u=0;u<e.length;u+=1)e[u]&&e[u].m(a,o);v(a,n,o),i=!0},p(a,o){o[0]&127043&&(s=fe(a[14]),W(),e=Oe(e,o,r,1,a,s,t,n.parentNode,Ue,mo,n,ro),Y())},i(a){if(!i){for(let o=0;o<s.length;o+=1)b(e[o]);i=!0}},o(a){for(let o=0;o<e.length;o+=1)p(e[o]);i=!1},d(a){a&&k(n);for(let o=0;o<e.length;o+=1)e[o].d(a)}}}function co(l,e){let t,n,i;return n=new gr({props:{api:e[1],column:e[55],row:e[52],columnMeta:e[13][e[55].id],areaMeta:e[13][ze(e[55].id,e[52].id)],cards:e[16][ze(e[55].id,e[52].id)],cardTemplate:e[0],isMenuVisible:e[6]}}),n.$on("action",e[42]),{key:l,first:null,c(){t=se(),P(n.$$.fragment),this.first=t},m(s,r){v(s,t,r),R(n,s,r),i=!0},p(s,r){e=s;const a={};r[0]&2&&(a.api=e[1]),r[0]&4096&&(a.column=e[55]),r[0]&16384&&(a.row=e[52]),r[0]&12288&&(a.columnMeta=e[13][e[55].id]),r[0]&28672&&(a.areaMeta=e[13][ze(e[55].id,e[52].id)]),r[0]&86016&&(a.cards=e[16][ze(e[55].id,e[52].id)]),r[0]&1&&(a.cardTemplate=e[0]),r[0]&64&&(a.isMenuVisible=e[6]),n.$set(a)},i(s){i||(b(n.$$.fragment,s),i=!0)},o(s){p(n.$$.fragment,s),i=!1},d(s){s&&k(t),A(n,s)}}}function Ch(l){let e=[],t=new Map,n,i,s=fe(l[12]);const r=a=>a[55].id;for(let a=0;a<s.length;a+=1){let o=oo(l,s,a),u=r(o);t.set(u,e[a]=co(u,o))}return{c(){for(let a=0;a<e.length;a+=1)e[a].c();n=H()},m(a,o){for(let u=0;u<e.length;u+=1)e[u]&&e[u].m(a,o);v(a,n,o),i=!0},p(a,o){o[0]&94275&&(s=fe(a[12]),W(),e=Oe(e,o,r,1,a,s,t,n.parentNode,Ue,co,n,oo),Y())},i(a){if(!i){for(let o=0;o<s.length;o+=1)b(e[o]);i=!0}},o(a){for(let o=0;o<e.length;o+=1)p(e[o]);i=!1},d(a){a&&k(n);for(let o=0;o<e.length;o+=1)e[o].d(a)}}}function mo(l,e){let t,n,i;return n=new Is({props:{row:e[52],rows:e[14],collapsable:!!e[15],api:e[1],$$slots:{default:[Ch]},$$scope:{ctx:e}}}),n.$on("action",e[43]),{key:l,first:null,c(){t=se(),P(n.$$.fragment),this.first=t},m(s,r){v(s,t,r),R(n,s,r),i=!0},p(s,r){e=s;const a={};r[0]&16384&&(a.row=e[52]),r[0]&16384&&(a.rows=e[14]),r[0]&32768&&(a.collapsable=!!e[15]),r[0]&2&&(a.api=e[1]),r[0]&94275|r[2]&1&&(a.$$scope={dirty:r,ctx:e}),n.$set(a)},i(s){i||(b(n.$$.fragment,s),i=!0)},o(s){p(n.$$.fragment,s),i=!1},d(s){s&&k(t),A(n,s)}}}function _o(l){let e=[],t=new Map,n,i,s=fe(l[14]);const r=a=>a[52].id;for(let a=0;a<s.length;a+=1){let o=ao(l,s,a),u=r(o);t.set(u,e[a]=bo(u,o))}return{c(){for(let a=0;a<e.length;a+=1)e[a].c();n=se()},m(a,o){for(let u=0;u<e.length;u+=1)e[u]&&e[u].m(a,o);v(a,n,o),i=!0},p(a,o){o[0]&127043|o[1]&917504&&(s=fe(a[14]),W(),e=Oe(e,o,r,1,a,s,t,n.parentNode,Ue,bo,n,ao),Y())},i(a){if(!i){for(let o=0;o<s.length;o+=1)b(e[o]);i=!0}},o(a){for(let o=0;o<e.length;o+=1)p(e[o]);i=!1},d(a){a&&k(n);for(let o=0;o<e.length;o+=1)e[o].d(a)}}}function ho(l){let e,t;return e=new Is({props:{row:l[52],rows:l[14],collapsable:!!l[15],api:l[1],$$slots:{default:[Ih]},$$scope:{ctx:l}}}),e.$on("action",l[40]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i[0]&16384&&(s.row=n[52]),i[0]&16384&&(s.rows=n[14]),i[0]&32768&&(s.collapsable=!!n[15]),i[0]&2&&(s.api=n[1]),i[0]&94275|i[1]&917504|i[2]&1&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function go(l,e){let t,n,i;return n=new gr({props:{api:e[1],column:e[55],row:e[52],columnMeta:e[13][e[55].id],areaMeta:e[13][ze(e[55].id,e[52].id)],cards:e[50][e[52].id]?e[16][ze(e[55].id,e[52].id)]?.slice(e[50][e[52].id].startIndex,e[50][e[52].id].endIndex):e[16][ze(e[55].id,e[52].id)]?.slice(e[48],e[49]),cardTemplate:e[0],isMenuVisible:e[6]}}),n.$on("action",e[39]),{key:l,first:null,c(){t=se(),P(n.$$.fragment),this.first=t},m(s,r){v(s,t,r),R(n,s,r),i=!0},p(s,r){e=s;const a={};r[0]&2&&(a.api=e[1]),r[0]&4096&&(a.column=e[55]),r[0]&16384&&(a.row=e[52]),r[0]&12288&&(a.columnMeta=e[13][e[55].id]),r[0]&28672&&(a.areaMeta=e[13][ze(e[55].id,e[52].id)]),r[0]&86016|r[1]&917504&&(a.cards=e[50][e[52].id]?e[16][ze(e[55].id,e[52].id)]?.slice(e[50][e[52].id].startIndex,e[50][e[52].id].endIndex):e[16][ze(e[55].id,e[52].id)]?.slice(e[48],e[49])),r[0]&1&&(a.cardTemplate=e[0]),r[0]&64&&(a.isMenuVisible=e[6]),n.$set(a)},i(s){i||(b(n.$$.fragment,s),i=!0)},o(s){p(n.$$.fragment,s),i=!1},d(s){s&&k(t),A(n,s)}}}function Ih(l){let e=[],t=new Map,n,i,s=fe(l[12]);const r=a=>a[55].id;for(let a=0;a<s.length;a+=1){let o=uo(l,s,a),u=r(o);t.set(u,e[a]=go(u,o))}return{c(){for(let a=0;a<e.length;a+=1)e[a].c();n=H()},m(a,o){for(let u=0;u<e.length;u+=1)e[u]&&e[u].m(a,o);v(a,n,o),i=!0},p(a,o){o[0]&94275|o[1]&917504&&(s=fe(a[12]),W(),e=Oe(e,o,r,1,a,s,t,n.parentNode,Ue,go,n,uo),Y())},i(a){if(!i){for(let o=0;o<s.length;o+=1)b(e[o]);i=!0}},o(a){for(let o=0;o<e.length;o+=1)p(e[o]);i=!1},d(a){a&&k(n);for(let o=0;o<e.length;o+=1)e[o].d(a)}}}function bo(l,e){let t,n,i,s=e[50][e[52].id].visible&&ho(e);return{key:l,first:null,c(){t=se(),s&&s.c(),n=se(),this.first=t},m(r,a){v(r,t,a),s&&s.m(r,a),v(r,n,a),i=!0},p(r,a){e=r,e[50][e[52].id].visible?s?(s.p(e,a),a[0]&16384|a[1]&524288&&b(s,1)):(s=ho(e),s.c(),b(s,1),s.m(n.parentNode,n)):s&&(W(),p(s,1,1,()=>{s=null}),Y())},i(r){i||(b(s),i=!0)},o(r){p(s),i=!1},d(r){r&&(k(t),k(n)),s&&s.d(r)}}}function Mh(l){let e,t,n,i;e=new Cr({props:{columns:l[12],areasMeta:l[13],contentEl:l[51],api:l[1]}}),e.$on("action",l[38]);let s=l[12].length&&_o(l);return{c(){P(e.$$.fragment),t=H(),s&&s.c(),n=se()},m(r,a){R(e,r,a),v(r,t,a),s&&s.m(r,a),v(r,n,a),i=!0},p(r,a){const o={};a[0]&4096&&(o.columns=r[12]),a[0]&8192&&(o.areasMeta=r[13]),a[1]&1048576&&(o.contentEl=r[51]),a[0]&2&&(o.api=r[1]),e.$set(o),r[12].length?s?(s.p(r,a),a[0]&4096&&b(s,1)):(s=_o(r),s.c(),b(s,1),s.m(n.parentNode,n)):s&&(W(),p(s,1,1,()=>{s=null}),Y())},i(r){i||(b(e.$$.fragment,r),b(s),i=!0)},o(r){p(e.$$.fragment,r),p(s),i=!1},d(r){r&&(k(t),k(n)),A(e,r),s&&s.d(r)}}}function po(l){let e,t;return e=new vh({props:{api:l[1],config:l[17],shape:l[18]}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i[0]&2&&(s.api=n[1]),i[0]&131072&&(s.config=n[17]),i[0]&262144&&(s.shape=n[18]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Th(l){let e,t,n,i,s,r,a,o,u,f,c,d,m,_,h,w;const y=[Sh,yh],S=[];function C(E,F){return E[11]==="default:lazy"?0:1}i=C(l),s=S[i]=y[i](l);let T=l[9]&&!l[10]&&l[17].show!==!1&&po(l);function L(E){l[45](E)}let N={at:"left-bottom",options:l[5],resolver:l[32],dataKey:"menuId"};return l[4]!==void 0&&(N.handler=l[4]),d=new cn({props:N}),ue.push(()=>Se(d,"handler",L)),d.$on("click",l[33]),{c(){e=I("div"),t=I("div"),n=I("div"),s.c(),a=H(),T&&T.c(),c=H(),P(d.$$.fragment),g(n,"class","wx-content svelte-1lcmu6i"),g(n,"data-kanban-id",Xe.scrollableContent),X(n,"wx-virtual-content",l[11]==="default:lazy"),X(n,"wx-not-anchored",!!l[10]),g(t,"class","wx-content-wrapper svelte-1lcmu6i"),g(t,"data-kanban-id",Xe.content),X(t,"wx-virtual-content",l[11]==="default:lazy"),g(e,"class","wx-kanban svelte-1lcmu6i"),g(e,"data-wx-widget",Xe.kanban),X(e,"wx-dragged",!!l[10]),X(e,"wx-touch",ls.isMobile||navigator.maxTouchPoints>1)},m(E,F){v(E,e,F),M(e,t),M(t,n),S[i].m(n,null),l[44](n),M(e,a),T&&T.m(e,null),v(E,c,F),R(d,E,F),_=!0,h||(w=[te(n,"click",function(){nt(l[4])&&l[4].apply(this,arguments)}),He(r=$c.call(null,t,{api:l[1],readonly:l[8]===!1})),He(o=Zc.call(null,e,{api:l[1],readonly:l[7]===!1})),He(u=ld.call(null,e,{api:l[1],readonly:l[9]===!1,locale:l[19],confirmDeletion:l[2].confirmDeletion?.show??!0?l[34]:null})),He(f=nd.call(null,e,{api:l[1],tick:ct}))],h=!0)},p(E,F){l=E;let G=i;i=C(l),i===G?S[i].p(l,F):(W(),p(S[G],1,1,()=>{S[G]=null}),Y(),s=S[i],s?s.p(l,F):(s=S[i]=y[i](l),s.c()),b(s,1),s.m(n,null)),(!_||F[0]&2048)&&X(n,"wx-virtual-content",l[11]==="default:lazy"),(!_||F[0]&1024)&&X(n,"wx-not-anchored",!!l[10]),r&&nt(r.update)&&F[0]&258&&r.update.call(null,{api:l[1],readonly:l[8]===!1}),(!_||F[0]&2048)&&X(t,"wx-virtual-content",l[11]==="default:lazy"),l[9]&&!l[10]&&l[17].show!==!1?T?(T.p(l,F),F[0]&132608&&b(T,1)):(T=po(l),T.c(),b(T,1),T.m(e,null)):T&&(W(),p(T,1,1,()=>{T=null}),Y()),o&&nt(o.update)&&F[0]&130&&o.update.call(null,{api:l[1],readonly:l[7]===!1}),u&&nt(u.update)&&F[0]&518&&u.update.call(null,{api:l[1],readonly:l[9]===!1,locale:l[19],confirmDeletion:l[2].confirmDeletion?.show??!0?l[34]:null}),f&&nt(f.update)&&F[0]&2&&f.update.call(null,{api:l[1],tick:ct}),(!_||F[0]&1024)&&X(e,"wx-dragged",!!l[10]),(!_||F&0)&&X(e,"wx-touch",ls.isMobile||navigator.maxTouchPoints>1);const U={};F[0]&32&&(U.options=l[5]),!m&&F[0]&16&&(m=!0,U.handler=l[4],Ie(()=>m=!1)),d.$set(U)},i(E){_||(b(s),b(T),b(d.$$.fragment,E),_=!0)},o(E){p(s),p(T),p(d.$$.fragment,E),_=!1},d(E){E&&(k(e),k(c)),S[i].d(),l[44](null),T&&T.d(),A(d,E),h=!1,De(w)}}}function Dh(l,e,t){let n,i,s,r,a,o,u,f,c,d,m,_,h,w,y,{cardTemplate:S}=e,{api:C}=e,T;const L=ve("wx-i18n").getGroup("kanban");let{columns:N,rowKey:E,rows:F,readonly:G,dragItemId:U,areasMeta:O,cardsMap:D,cardShape:j,cards:z,editorShape:q,editor:J,layout:V}=C.getReactiveState();he(l,N,ae=>t(12,c=ae)),he(l,E,ae=>t(15,_=ae)),he(l,F,ae=>t(14,m=ae)),he(l,G,ae=>t(37,o=ae)),he(l,U,ae=>t(10,u=ae)),he(l,O,ae=>t(13,d=ae)),he(l,D,ae=>t(16,h=ae)),he(l,j,ae=>t(2,r=ae)),he(l,z,ae=>t(36,a=ae)),he(l,q,ae=>t(18,y=ae)),he(l,J,ae=>t(17,w=ae)),he(l,V,ae=>t(11,f=ae));const{showModal:ne}=ve("wx-helpers");let Q=null,B=[];const ie={},ge={};function Fe(ae){const Qe=a.find(oe=>oe.id==ae);return Qe&&(C.getState().selected?.length>1&&C.exec("select-card",{id:parseInt(ae)}),t(5,B=ie[Qe.id]||[])),Qe}const me=(ae,Qe)=>{const oe=Qe.menu.items({card:ae});return!oe||!oe.length?null:oe.map(Ne=>un(Ne,L))};function Te(ae){const{action:Qe,context:oe}=ae.detail;if(Qe){if(Qe.onClick){Qe.onClick({id:Qe.id,item:Qe,card:oe});return}switch(Qe.id){case"delete-card":{(r.confirmDeletion?.show??!0?Ye():Promise.resolve()).then(()=>{C.exec("delete-card",{id:oe.id})}).catch(()=>{});break}case"set-edit":C.exec("set-edit",{cardId:oe.id,eventSource:"ui"});break;case"duplicate-card":C.exec("duplicate-card",{id:oe.id,card:{label:`${L("Duplicate of")} ${oe.label}`}});break}}}function Ye(){return ne({message:L("Would you like to delete this card?")})}function de(ae){ye.call(this,l,ae)}function Re(ae){ye.call(this,l,ae)}function Ae(ae){ye.call(this,l,ae)}function Pe(ae){ye.call(this,l,ae)}function it(ae){ye.call(this,l,ae)}function Ze(ae){ye.call(this,l,ae)}function $e(ae){ue[ae?"unshift":"push"](()=>{T=ae,t(3,T)})}function tt(ae){Q=ae,t(4,Q)}return l.$$set=ae=>{"cardTemplate"in ae&&t(0,S=ae.cardTemplate),"api"in ae&&t(1,C=ae.api)},l.$$.update=()=>{l.$$.dirty[1]&64&&t(9,{edit:n,select:i,dnd:s}=o,n,(t(8,i),t(37,o)),(t(7,s),t(37,o))),l.$$.dirty[0]&4|l.$$.dirty[1]&48&&a.forEach(ae=>{t(35,ie[ae.id]=me(ae,r),ie),t(6,ge[ae.id]=!!(r.menu.show&&ie[ae.id]?.length),ge)})},[S,C,r,T,Q,B,ge,s,i,n,u,f,c,d,m,_,h,w,y,L,N,E,F,G,U,O,D,j,z,q,J,V,Fe,Te,Ye,ie,a,o,de,Re,Ae,Pe,it,Ze,$e,tt]}class Eh extends ee{constructor(e){super(),x(this,e,Dh,Th,Z,{cardTemplate:0,api:1},null,[-1,-1,-1])}}function Lh(l){let e,t;return e=new Eh({props:{api:l[2],cardTemplate:l[1],editor:l[0]}}),e.$on("action",l[3]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&2&&(s.cardTemplate=n[1]),i&1&&(s.editor=n[0]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Rh(l){let e,t;return e=new Hf({props:{$$slots:{default:[Lh]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&16777219&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Ah(l){let e,t;return e=new Ai({props:{words:{...bl,...Tn},optional:!0,$$slots:{default:[Rh]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,[i]){const s={};i&16777219&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Ph(l,e,t){let{columns:n}=e,{rows:i=null}=e,{cards:s}=e,{cardShape:r=jt}=e,{columnShape:a=null}=e,{rowShape:o=null}=e,{editorShape:u=null}=e,{readonly:f=!1}=e,{columnKey:c="column"}=e,{rowKey:d=""}=e,{scrollType:m="default"}=e,{renderType:_="default"}=e,{cardHeight:h=null}=e,{cardTemplate:w=null}=e,{editor:y=Dt}=e,{history:S=!0}=e,{currentUser:C=null}=e,{links:T=null}=e,{dataStore:L=null}=e,{editorAutoSave:N=!0}=e;const E=Ee();let F=new wa(E);L||(L=new Gc(O=>Pn(O),{history:S}),L.out.setNext(F));const G=sd(L,F),U=({detail:{action:O,data:D}})=>{G.exec(O,D)};return l.$$set=O=>{"columns"in O&&t(5,n=O.columns),"rows"in O&&t(6,i=O.rows),"cards"in O&&t(7,s=O.cards),"cardShape"in O&&t(8,r=O.cardShape),"columnShape"in O&&t(9,a=O.columnShape),"rowShape"in O&&t(10,o=O.rowShape),"editorShape"in O&&t(11,u=O.editorShape),"readonly"in O&&t(12,f=O.readonly),"columnKey"in O&&t(13,c=O.columnKey),"rowKey"in O&&t(14,d=O.rowKey),"scrollType"in O&&t(15,m=O.scrollType),"renderType"in O&&t(16,_=O.renderType),"cardHeight"in O&&t(17,h=O.cardHeight),"cardTemplate"in O&&t(1,w=O.cardTemplate),"editor"in O&&t(0,y=O.editor),"history"in O&&t(18,S=O.history),"currentUser"in O&&t(19,C=O.currentUser),"links"in O&&t(20,T=O.links),"dataStore"in O&&t(4,L=O.dataStore),"editorAutoSave"in O&&t(21,N=O.editorAutoSave)},l.$$.update=()=>{l.$$.dirty&2097152&&N===!1&&t(0,y.autoSave=!1,y),l.$$.dirty&1834993&&L.init({columnKey:c,rowKey:d,columns:n,rows:i,cards:s,cardsMap:{},cardsMeta:{},cardShape:r,columnShape:a,editorShape:u,rowShape:o,readonly:f,cardHeight:h,currentUser:C,links:T,editor:y,scrollType:m,renderType:_})},[y,w,G,U,L,n,i,s,r,a,o,u,f,c,d,m,_,h,S,C,T,N]}let Nh=class extends ee{constructor(e){super(),x(this,e,Ph,Ah,Z,{columns:5,rows:6,cards:7,cardShape:8,columnShape:9,rowShape:10,editorShape:11,readonly:12,columnKey:13,rowKey:14,scrollType:15,renderType:16,cardHeight:17,cardTemplate:1,editor:0,history:18,currentUser:19,links:20,dataStore:4,editorAutoSave:21,api:2})}get api(){return this.$$.ctx[2]}};function zh(l){let e,t;return e=new Mi({props:{fonts:l[0]}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.fonts=n[0]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Hh(l){let e,t;return e=new Mi({props:{fonts:l[0],$$slots:{default:[Oh]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.fonts=n[0]),i&8&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Oh(l){let e;const t=l[2].default,n=be(t,l,l[3],null);return{c(){n&&n.c()},m(i,s){n&&n.m(i,s),e=!0},p(i,s){n&&n.p&&(!e||s&8)&&we(n,t,i,i[3],e?pe(t,i[3],s,null):ke(i[3]),null)},i(i){e||(b(n,i),e=!0)},o(i){p(n,i),e=!1},d(i){n&&n.d(i)}}}function Fh(l){let e,t,n,i;const s=[Hh,zh],r=[];function a(o,u){return o[1]&&o[1].default?0:1}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=se()},m(o,u){r[e].m(o,u),v(o,n,u),i=!0},p(o,[u]){t.p(o,u)},i(o){i||(b(t),i=!0)},o(o){p(t),i=!1},d(o){o&&k(n),r[e].d(o)}}}function qh(l,e,t){let{$$slots:n={},$$scope:i}=e;const s=Qt(n);let{fonts:r=!0}=e;const a=s;return l.$$set=o=>{"fonts"in o&&t(0,r=o.fonts),"$$scope"in o&&t(3,i=o.$$scope)},[r,a,n,i]}class jh extends ee{constructor(e){super(),x(this,e,qh,Fh,Z,{fonts:0})}}function Uh(l){let e,t;return e=new Ei({props:{fonts:l[0]}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.fonts=n[0]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Vh(l){let e,t;return e=new Ei({props:{fonts:l[0],$$slots:{default:[Kh]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.fonts=n[0]),i&8&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Kh(l){let e;const t=l[2].default,n=be(t,l,l[3],null);return{c(){n&&n.c()},m(i,s){n&&n.m(i,s),e=!0},p(i,s){n&&n.p&&(!e||s&8)&&we(n,t,i,i[3],e?pe(t,i[3],s,null):ke(i[3]),null)},i(i){e||(b(n,i),e=!0)},o(i){p(n,i),e=!1},d(i){n&&n.d(i)}}}function Bh(l){let e,t,n,i;const s=[Vh,Uh],r=[];function a(o,u){return o[1]&&o[1].default?0:1}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=se()},m(o,u){r[e].m(o,u),v(o,n,u),i=!0},p(o,[u]){t.p(o,u)},i(o){i||(b(t),i=!0)},o(o){p(t),i=!1},d(o){o&&k(n),r[e].d(o)}}}function Wh(l,e,t){let{$$slots:n={},$$scope:i}=e;const s=Qt(n);let{fonts:r=!0}=e;const a=s;return l.$$set=o=>{"fonts"in o&&t(0,r=o.fonts),"$$scope"in o&&t(3,i=o.$$scope)},[r,a,n,i]}class Yh extends ee{constructor(e){super(),x(this,e,Wh,Bh,Z,{fonts:0})}}function Gh(l){let e,t;return e=new Ri({props:{fonts:l[0]}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.fonts=n[0]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Jh(l){let e,t;return e=new Ri({props:{fonts:l[0],$$slots:{default:[Qh]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.fonts=n[0]),i&8&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Qh(l){let e;const t=l[2].default,n=be(t,l,l[3],null);return{c(){n&&n.c()},m(i,s){n&&n.m(i,s),e=!0},p(i,s){n&&n.p&&(!e||s&8)&&we(n,t,i,i[3],e?pe(t,i[3],s,null):ke(i[3]),null)},i(i){e||(b(n,i),e=!0)},o(i){p(n,i),e=!1},d(i){n&&n.d(i)}}}function Xh(l){let e,t,n,i;const s=[Jh,Gh],r=[];function a(o,u){return o[1]&&o[1].default?0:1}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=se()},m(o,u){r[e].m(o,u),v(o,n,u),i=!0},p(o,[u]){t.p(o,u)},i(o){i||(b(t),i=!0)},o(o){p(t),i=!1},d(o){o&&k(n),r[e].d(o)}}}function Zh(l,e,t){let{$$slots:n={},$$scope:i}=e;const s=Qt(n);let{fonts:r=!0}=e;const a=s;return l.$$set=o=>{"fonts"in o&&t(0,r=o.fonts),"$$scope"in o&&t(3,i=o.$$scope)},[r,a,n,i]}class $h extends ee{constructor(e){super(),x(this,e,Zh,Xh,Z,{fonts:0})}}function wo(l,e,t){const n=l.slice();return n[25]=e[t],n}function ko(l){let e,t,n;return t=new Me({props:{css:"wxi-close"}}),t.$on("click",l[13]),{c(){e=I("div"),P(t.$$.fragment),g(e,"class","wx-close-icon svelte-ra2v7s")},m(i,s){v(i,e,s),R(t,e,null),n=!0},p:K,i(i){n||(b(t.$$.fragment,i),n=!0)},o(i){p(t.$$.fragment,i),n=!1},d(i){i&&k(e),A(t)}}}function vo(l){let e,t;return e=new Ct({props:{cancel:l[10],$$slots:{default:[lg]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&4227120&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function yo(l){let e,t;const n=l[17].default,i=be(n,l,l[22],null);return{c(){e=I("div"),i&&i.c(),g(e,"class","wx-settings svelte-ra2v7s")},m(s,r){v(s,e,r),i&&i.m(e,null),t=!0},p(s,r){i&&i.p&&(!t||r&4194304)&&we(i,n,s,s[22],t?pe(n,s[22],r,null):ke(s[22]),null)},i(s){t||(b(i,s),t=!0)},o(s){p(i,s),t=!1},d(s){s&&k(e),i&&i.d(s)}}}function xh(l){let e;return{c(){e=I("div"),e.textContent=`${l[9]("No results")}`,g(e,"class","wx-list-item wx-no-results svelte-ra2v7s")},m(t,n){v(t,e,n)},p:K,i:K,o:K,d(t){t&&k(e)}}}function eg(l){let e,t,n,i,s=fe(l[4]),r=[];for(let o=0;o<s.length;o+=1)r[o]=So(wo(l,s,o));const a=o=>p(r[o],1,1,()=>{r[o]=null});return{c(){e=I("div");for(let o=0;o<r.length;o+=1)r[o].c();g(e,"class","wx-results svelte-ra2v7s")},m(o,u){v(o,e,u);for(let f=0;f<r.length;f+=1)r[f]&&r[f].m(e,null);t=!0,n||(i=He(Ot.call(null,e,l[14])),n=!0)},p(o,u){if(u&48){s=fe(o[4]);let f;for(f=0;f<s.length;f+=1){const c=wo(o,s,f);r[f]?(r[f].p(c,u),b(r[f],1)):(r[f]=So(c),r[f].c(),b(r[f],1),r[f].m(e,null))}for(W(),f=s.length;f<r.length;f+=1)a(f);Y()}},i(o){if(!t){for(let u=0;u<s.length;u+=1)b(r[u]);t=!0}},o(o){r=r.filter(Boolean);for(let u=0;u<r.length;u+=1)p(r[u]);t=!1},d(o){o&&k(e),at(r,o),n=!1,i()}}}function tg(l){let e,t,n=l[25].label+"",i;return{c(){e=I("div"),t=I("span"),i=$(n),g(t,"class","wx-list-item-text svelte-ra2v7s"),g(e,"class","wx-item-inner svelte-ra2v7s")},m(s,r){v(s,e,r),M(e,t),M(t,i)},p(s,r){r&16&&n!==(n=s[25].label+"")&&re(i,n)},i:K,o:K,d(s){s&&k(e)}}}function ng(l){let e,t,n;var i=l[5];function s(r,a){return{props:{result:r[25]}}}return i&&(e=Je(i,s(l))),{c(){e&&P(e.$$.fragment),t=se()},m(r,a){e&&R(e,r,a),v(r,t,a),n=!0},p(r,a){if(a&32&&i!==(i=r[5])){if(e){W();const o=e;p(o.$$.fragment,1,0,()=>{A(o,1)}),Y()}i?(e=Je(i,s(r)),P(e.$$.fragment),b(e.$$.fragment,1),R(e,t.parentNode,t)):e=null}else if(i){const o={};a&16&&(o.result=r[25]),e.$set(o)}},i(r){n||(e&&b(e.$$.fragment,r),n=!0)},o(r){e&&p(e.$$.fragment,r),n=!1},d(r){r&&k(t),e&&A(e,r)}}}function So(l){let e,t,n,i,s,r;const a=[ng,tg],o=[];function u(f,c){return f[5]?0:1}return t=u(l),n=o[t]=a[t](l),{c(){e=I("div"),n.c(),i=H(),g(e,"class","wx-list-item svelte-ra2v7s"),g(e,"data-id",s=l[25].id)},m(f,c){v(f,e,c),o[t].m(e,null),M(e,i),r=!0},p(f,c){let d=t;t=u(f),t===d?o[t].p(f,c):(W(),p(o[d],1,1,()=>{o[d]=null}),Y(),n=o[t],n?n.p(f,c):(n=o[t]=a[t](f),n.c()),b(n,1),n.m(e,i)),(!r||c&16&&s!==(s=f[25].id))&&g(e,"data-id",s)},i(f){r||(b(n),r=!0)},o(f){p(n),r=!1},d(f){f&&k(e),o[t].d()}}}function lg(l){let e,t,n,i,s,r=l[15]?.default&&yo(l);const a=[eg,xh],o=[];function u(f,c){return f[4]?0:1}return n=u(l),i=o[n]=a[n](l),{c(){e=I("div"),r&&r.c(),t=H(),i.c(),g(e,"class","wx-search-popup svelte-ra2v7s")},m(f,c){v(f,e,c),r&&r.m(e,null),M(e,t),o[n].m(e,null),s=!0},p(f,c){f[15]?.default?r?(r.p(f,c),c&32768&&b(r,1)):(r=yo(f),r.c(),b(r,1),r.m(e,t)):r&&(W(),p(r,1,1,()=>{r=null}),Y());let d=n;n=u(f),n===d?o[n].p(f,c):(W(),p(o[d],1,1,()=>{o[d]=null}),Y(),i=o[n],i?i.p(f,c):(i=o[n]=a[n](f),i.c()),b(i,1),i.m(e,null))},i(f){s||(b(r),b(i),s=!0)},o(f){p(r),p(i),s=!1},d(f){f&&k(e),r&&r.d(),o[n].d()}}}function ig(l){let e,t,n,i,s,r,a,o,u,f,c;n=new Me({props:{css:"wxi-search"}});let d=!!l[0]&&ko(l),m=l[6]&&vo(l);return{c(){e=I("div"),t=I("div"),P(n.$$.fragment),i=H(),s=I("input"),a=H(),d&&d.c(),o=H(),m&&m.c(),g(t,"class","wx-search-icon svelte-ra2v7s"),g(s,"id",r=`${l[1]}`),s.readOnly=l[2],g(s,"placeholder",l[3]),g(s,"class","svelte-ra2v7s"),g(e,"class","wx-search svelte-ra2v7s"),g(e,"tabindex",1),g(e,"data-wx-widget",Xe.search)},m(_,h){v(_,e,h),M(e,t),R(n,t,null),M(e,i),M(e,s),je(s,l[0]),l[20](s),M(e,a),d&&d.m(e,null),M(e,o),m&&m.m(e,null),l[21](e),u=!0,f||(c=[te(s,"input",l[19]),te(s,"focus",l[11]),te(s,"blur",l[12]),te(e,"click",l[18])],f=!0)},p(_,[h]){(!u||h&2&&r!==(r=`${_[1]}`))&&g(s,"id",r),(!u||h&4)&&(s.readOnly=_[2]),(!u||h&8)&&g(s,"placeholder",_[3]),h&1&&s.value!==_[0]&&je(s,_[0]),_[0]?d?(d.p(_,h),h&1&&b(d,1)):(d=ko(_),d.c(),b(d,1),d.m(e,o)):d&&(W(),p(d,1,1,()=>{d=null}),Y()),_[6]?m?(m.p(_,h),h&64&&b(m,1)):(m=vo(_),m.c(),b(m,1),m.m(e,null)):m&&(W(),p(m,1,1,()=>{m=null}),Y())},i(_){u||(b(n.$$.fragment,_),b(d),b(m),u=!0)},o(_){p(n.$$.fragment,_),p(d),p(m),u=!1},d(_){_&&k(e),A(n),l[20](null),d&&d.d(),m&&m.d(),l[21](null),f=!1,De(c)}}}function sg(l,e,t){let{$$slots:n={},$$scope:i}=e;const s=Qt(n),r=ve("wx-i18n").getGroup("kanban");let{value:a=""}=e,{id:o=xe()}=e,{readonly:u=!1}=e,{focus:f=!1}=e,{placeholder:c=r("Search")}=e,{searchResults:d=null}=e,{resultTemplate:m}=e;const _=Ee();let h=!1,w;f&&ot(()=>w.focus());let y;function S(D){y.contains(D.target)||(t(6,h=!1),t(0,a=""))}function C(){t(6,h=!0),_("action",{action:"search-focus"})}function T(){_("action",{action:"search-blur"})}function L(D){_("action",{action:"result-click",id:D}),t(6,h=!1)}function N(){t(0,a="")}const E={click:D=>L(D)};function F(D){ye.call(this,l,D)}function G(){a=this.value,t(0,a)}function U(D){ue[D?"unshift":"push"](()=>{w=D,t(7,w)})}function O(D){ue[D?"unshift":"push"](()=>{y=D,t(8,y)})}return l.$$set=D=>{"value"in D&&t(0,a=D.value),"id"in D&&t(1,o=D.id),"readonly"in D&&t(2,u=D.readonly),"focus"in D&&t(16,f=D.focus),"placeholder"in D&&t(3,c=D.placeholder),"searchResults"in D&&t(4,d=D.searchResults),"resultTemplate"in D&&t(5,m=D.resultTemplate),"$$scope"in D&&t(22,i=D.$$scope)},[a,o,u,c,d,m,h,w,y,r,S,C,T,N,E,s,f,n,F,G,U,O,i]}class rg extends ee{constructor(e){super(),x(this,e,sg,ig,Z,{value:0,id:1,readonly:2,focus:16,placeholder:3,searchResults:4,resultTemplate:5})}}function Co(l){let e,t,n,i,s,r;function a(u){l[12](u)}let o={options:l[1]};return l[4].by!==void 0&&(o.value=l[4].by),i=new ci({props:o}),ue.push(()=>Se(i,"value",a)),{c(){e=I("div"),t=I("div"),t.textContent=`${l[6]("Search in")}:`,n=H(),P(i.$$.fragment),g(t,"class","wx-title svelte-4fxaw7"),g(e,"class","wx-select svelte-4fxaw7")},m(u,f){v(u,e,f),M(e,t),M(e,n),R(i,e,null),r=!0},p(u,f){const c={};f&2&&(c.options=u[1]),!s&&f&16&&(s=!0,c.value=u[4].by,Ie(()=>s=!1)),i.$set(c)},i(u){r||(b(i.$$.fragment,u),r=!0)},o(u){p(i.$$.fragment,u),r=!1},d(u){u&&k(e),A(i)}}}function og(l){let e,t,n=l[0]&&Co(l);return{c(){n&&n.c(),e=se()},m(i,s){n&&n.m(i,s),v(i,e,s),t=!0},p(i,s){i[0]?n?(n.p(i,s),s&1&&b(n,1)):(n=Co(i),n.c(),b(n,1),n.m(e.parentNode,e)):n&&(W(),p(n,1,1,()=>{n=null}),Y())},i(i){t||(b(n),t=!0)},o(i){p(n),t=!1},d(i){i&&k(e),n&&n.d(i)}}}function ag(l){let e,t,n;function i(r){l[13](r)}let s={searchResults:l[3],resultTemplate:l[2],$$slots:{default:[og]},$$scope:{ctx:l}};return l[4].value!==void 0&&(s.value=l[4].value),e=new rg({props:s}),ue.push(()=>Se(e,"value",i)),e.$on("action",l[8]),{c(){P(e.$$.fragment)},m(r,a){R(e,r,a),n=!0},p(r,[a]){const o={};a&8&&(o.searchResults=r[3]),a&4&&(o.resultTemplate=r[2]),a&16403&&(o.$$scope={dirty:a,ctx:r}),!t&&a&16&&(t=!0,o.value=r[4].value,Ie(()=>t=!1)),e.$set(o)},i(r){n||(b(e.$$.fragment,r),n=!0)},o(r){p(e.$$.fragment,r),n=!1},d(r){A(e,r)}}}function ug(l,e,t){let n,i,s,r=K,a=()=>(r(),r=Ge(n,C=>t(11,s=C)),n);l.$$.on_destroy.push(()=>r());const o=ve("wx-i18n").getGroup("kanban");let{api:u}=e,{showOptions:f=!0}=e,{options:c=[{id:null,label:o("Everywhere")},{id:"label",label:o("Label")},{id:"description",label:o("Description")}]}=e,{resultTemplate:d}=e,m=null;const _=Mr({value:"",by:c[0].id},({value:C,by:T})=>{T||(T=null);const L=c.find(N=>N.id===T);u?.exec("set-search",{value:C,by:T,searchRule:L?.searchRule})});he(l,_,C=>t(4,i=C));let h;function w({detail:C}){const{id:T,action:L}=C;switch(L){case"result-click":u?.exec("select-card",{id:T});break;case"search-focus":i.value&&u?.exec("set-search",{value:i.value,by:i.by});break}}function y(C){l.$$.not_equal(i.by,C)&&(i.by=C,_.set(i))}function S(C){l.$$.not_equal(i.value,C)&&(i.value=C,_.set(i))}return l.$$set=C=>{"api"in C&&t(9,u=C.api),"showOptions"in C&&t(0,f=C.showOptions),"options"in C&&t(1,c=C.options),"resultTemplate"in C&&t(2,d=C.resultTemplate)},l.$$.update=()=>{l.$$.dirty&512&&a(t(5,n=u?.getReactiveState().cardsMeta)),l.$$.dirty&2568&&s&&(t(3,m=Object.keys(s).reduce((C,T)=>(s[T].found&&C.push(u?.getCard(T)),C),[])),m.length||t(3,m=null)),l.$$.dirty&1552&&(h||(t(10,h=C=>{(C?.value!==i.value||C?.by!==i?.by)&&_.reset(C)}),u?.on("set-search",h)))},[f,c,d,m,i,n,o,_,w,u,h,s,y,S]}class fg extends ee{constructor(e){super(),x(this,e,ug,ag,Z,{api:9,showOptions:0,options:1,resultTemplate:2})}}function cg(l){let e,t;return e=new Me({props:{title:l[0]("Add new row"),css:"wxi-table-row-plus-after"}}),e.$on("click",l[1]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p:K,i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function dg(l,e,t){let{api:n}=e;const i=ve("wx-i18n").getGroup("kanban");function s(){n?.exec("add-row",{id:tn(),row:{label:i("Untitled")}})}return l.$$set=r=>{"api"in r&&t(2,n=r.api)},[i,s,n]}class mg extends ee{constructor(e){super(),x(this,e,dg,cg,Z,{api:2})}}function _g(l){let e,t;return e=new Me({props:{title:l[0]("Add new column"),css:"wxi-table-column-plus-after"}}),e.$on("click",l[1]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p:K,i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function hg(l,e,t){let{api:n}=e;const i=ve("wx-i18n").getGroup("kanban");function s(){n?.exec("add-column",{id:tn(),column:{label:i("Untitled")}})}return l.$$set=r=>{"api"in r&&t(2,n=r.api)},[i,s,n]}class gg extends ee{constructor(e){super(),x(this,e,hg,_g,Z,{api:2})}}function Io(l){let e,t,n,i=l[2].text+"",s,r;return t=new Me({props:{css:"wxi-close"}}),t.$on("click",l[5]),{c(){e=I("div"),P(t.$$.fragment),n=H(),s=$(i),g(e,"class","wx-preserve svelte-r6cslo")},m(a,o){v(a,e,o),R(t,e,null),M(e,n),M(e,s),r=!0},p(a,o){(!r||o&4)&&i!==(i=a[2].text+"")&&re(s,i)},i(a){r||(b(t.$$.fragment,a),r=!0)},o(a){p(t.$$.fragment,a),r=!1},d(a){a&&k(e),A(t)}}}function bg(l){let e,t,n;return t=new Me({props:{css:"wxi-sort"}}),{c(){e=I("div"),P(t.$$.fragment),g(e,"class","wx-control svelte-r6cslo"),g(e,"title",l[3]("Sort"))},m(i,s){v(i,e,s),R(t,e,null),n=!0},p:K,i(i){n||(b(t.$$.fragment,i),n=!0)},o(i){p(t.$$.fragment,i),n=!1},d(i){i&&k(e),A(t)}}}function pg(l){let e,t,n,i=l[2]&&Io(l);return t=new Yd({props:{options:l[0],at:"left-bottom",$$slots:{default:[bg]},$$scope:{ctx:l}}}),t.$on("click",l[4]),{c(){i&&i.c(),e=H(),P(t.$$.fragment)},m(s,r){i&&i.m(s,r),v(s,e,r),R(t,s,r),n=!0},p(s,[r]){s[2]?i?(i.p(s,r),r&4&&b(i,1)):(i=Io(s),i.c(),b(i,1),i.m(e.parentNode,e)):i&&(W(),p(i,1,1,()=>{i=null}),Y());const a={};r&1&&(a.options=s[0]),r&256&&(a.$$scope={dirty:r,ctx:s}),t.$set(a)},i(s){n||(b(i),b(t.$$.fragment,s),n=!0)},o(s){p(i),p(t.$$.fragment,s),n=!1},d(s){s&&k(e),i&&i.d(s),A(t,s)}}}function wg(l,e,t){let n,i=K,s=()=>(i(),i=Ge(f,m=>t(7,n=m)),f);l.$$.on_destroy.push(()=>i());const r=ve("wx-i18n").getGroup("kanban");let{api:a}=e,{options:o=null}=e,u=null,f;function c(m){const _=m?.detail.action;if(_){const h=o.find(w=>w.id===_.id);h&&a.exec("set-sort",{by:h.by,dir:h.dir})}}function d(){a.exec("set-sort",null)}return l.$$set=m=>{"api"in m&&t(6,a=m.api),"options"in m&&t(0,o=m.options)},l.$$.update=()=>{l.$$.dirty&1&&(t(0,o=Array.isArray(o)?o:_c()),t(0,o=o.map(m=>{let{id:_,text:h,label:w,dir:y}=m;return{...m,id:_||xe(),text:r(h||w),icon:y==="asc"?"wxi-asc":"wxi-desc"}}))),l.$$.dirty&195&&a&&(f||s(t(1,f=a.getReactiveState().sort)),n?.preserve?t(2,u=o.find(m=>m.by===n.by&&m.dir===n.dir)):t(2,u=null))},[o,f,u,r,c,d,a,n]}class kg extends ee{constructor(e){super(),x(this,e,wg,pg,Z,{api:6,options:0})}}function vg(l){let e,t;return e=new Me({props:{title:l[2]("Undo"),css:`wxi-undo ${l[1]?"":"wx-disabled"}`}}),e.$on("click",l[3]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,[i]){const s={};i&2&&(s.css=`wxi-undo ${n[1]?"":"wx-disabled"}`),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function yg(l,e,t){let n,i=K,s=()=>(i(),i=Ge(u,c=>t(5,n=c)),u);l.$$.on_destroy.push(()=>i());let{api:r}=e;const a=ve("wx-i18n").getGroup("kanban");function o(){r.getStores().data.undo()}let u,f;return l.$$set=c=>{"api"in c&&t(4,r=c.api)},l.$$.update=()=>{l.$$.dirty&48&&r&&(s(t(0,u=r.getReactiveState().history)),t(1,f=n.undo.length>0))},[u,f,a,o,r,n]}class Sg extends ee{constructor(e){super(),x(this,e,yg,vg,Z,{api:4})}}function Cg(l){let e,t;return e=new Me({props:{title:l[2]("Redo"),css:`wxi-redo ${l[1]?"":"wx-disabled"}`}}),e.$on("click",l[3]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,[i]){const s={};i&2&&(s.css=`wxi-redo ${n[1]?"":"wx-disabled"}`),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Ig(l,e,t){let n,i=K,s=()=>(i(),i=Ge(u,c=>t(5,n=c)),u);l.$$.on_destroy.push(()=>i());let{api:r}=e;const a=ve("wx-i18n").getGroup("kanban");function o(){r?.getStores().data.redo()}let u,f;return l.$$set=c=>{"api"in c&&t(4,r=c.api)},l.$$.update=()=>{l.$$.dirty&48&&r&&(s(t(0,u=r.getReactiveState().history)),t(1,f=n.redo?.length>0))},[u,f,a,o,r,n]}class Mg extends ee{constructor(e){super(),x(this,e,Ig,Cg,Z,{api:4})}}function Xn(l,e,t){const n=document.createElement("DIV");n.className="wx-theme",l.appendChild(n);let i=window.getComputedStyle(n).getPropertyValue("--wx-theme-name");return l.removeChild(n),(e&&e!==i||!e&&!i&&t)&&(i&&l.classList.remove(`wx-${i}-theme`),i=e||t,l.classList.add(`wx-${i}-theme`)),i}function Mo(l){let e,t;return{c(){e=new Zt(!1),t=se(),e.a=t},m(n,i){e.m(l[0],n,i),v(n,t,i)},p(n,i){i&1&&e.p(n[0])},d(n){n&&(k(t),e.d())}}}function Tg(l){let e,t=l[0]&&Mo(l);return{c(){t&&t.c(),e=se()},m(n,i){t&&t.m(n,i),v(n,e,i)},p(n,[i]){n[0]?t?t.p(n,i):(t=Mo(n),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},i:K,o:K,d(n){n&&k(e),t&&t.d(n)}}}function Dg(l,e,t){let n;const i=["template"];let s=rl(e,i),{template:r}=e;return l.$$set=a=>{e=Ce(Ce({},e),We(a)),t(2,s=rl(e,i)),"template"in a&&t(1,r=a.template)},l.$$.update=()=>{t(0,n=typeof r=="function"?r({...s}):r)},[n,r]}class Zn extends ee{constructor(e){super(),x(this,e,Dg,Tg,Z,{template:1})}}class $n{_api;constructor(e){this._api=e}on(e,t){this._api.on(e,t)}exec(e,t){this._api.exec(e,t)}}const To={material:jh,willow:Yh,"willow-dark":$h};class Eg{api;export;events;config;container;_kanban;constructor(e,t){this.container=typeof e=="string"?document.querySelector(e):e,this.config=t,this._init()}destructor(){this._kanban.$destroy(),this._kanban=this.api=this.events=null}setConfig(e){this._storeConfig(e),typeof e.history<"u"&&console.debug("history cannot be reset at runtime"),typeof e.theme<"u"?this.setTheme(e.theme):this.api.getStores().data.init({...this.config})}parse(e){const{cards:t,links:n,columns:i,rows:s}=e;(t||n||i||s)&&(t&&(this.config.cards=t),n&&(this.config.links=n),i&&(this.config.columns=i),s&&(this.config.rows=s),this._kanban.$set(this._configToProps(this.config)))}serialize(){const{cards:e,links:t,columns:n,rows:i}=this.api.getState();return{cards:e,links:t,columns:n,rows:i}}undo(){this.api.undo()}redo(){this.api.redo()}getCard(e){return this.api.getCard(e)}getAreaCards(e,t){return this.api.getAreaCards(e,t)}getSelection(){return this.api.getState().selected}addCard(e){this.api.exec("add-card",e)}updateCard(e){this.api.exec("update-card",e)}duplicateCard(e){this.api.exec("duplicate-card",e)}deleteCard(e){this.api.exec("delete-card",e)}moveCard(e){this.api.exec("move-card",e)}addColumn(e){this.api.exec("add-column",e)}updateColumn(e){this.api.exec("update-column",e)}addRow(e){this.api.exec("add-row",e)}updateRow(e){this.api.exec("update-row",e)}moveColumn(e){this.api.exec("move-column",e)}moveRow(e){this.api.exec("move-row",e)}deleteColumn(e){this.api.exec("delete-column",e)}deleteRow(e){this.api.exec("delete-row",e)}addLink(e){this.api.exec("add-link",e)}deleteLink(e){this.api.exec("delete-link",e)}addComment(e){this.api.exec("add-comment",e)}updateComment(e){this.api.exec("update-comment",e)}deleteComment(e){this.api.exec("delete-comment",e)}selectCard(e){this.api.exec("select-card",e)}unselectCard(e){this.api.exec("unselect-card",e)}setSearch(e){this.api.exec("set-search",e)}setSort(e){this.api.exec("set-sort",e)}setEdit(e){this.api.exec("set-edit",{...e})}scroll(e){this.api.exec("scroll",e)}setLocale(e){this._reset({locale:e})}setTheme(e){this._reset({theme:e})}_init(e){this._kanban&&this.destructor();const t=Xn(this.container,this.config.theme?.name,"material"),n=new Map([["wx-i18n",Bt(this.config.locale)],["wx-theme",t]]);To[t]&&new To[t]({target:this.container,props:{fonts:this.config.theme?.fonts}}),e&&(this.config.dataStore=e),this._kanban=new Nh({target:this.container,props:this.config,context:n}),this.api=this._kanban.api,this.events=new $n(this.api),this.export=this.api.export}_reset(e){const t=this.api.getStores().data;this._storeConfig(e),this._init(t)}_storeConfig(e){const t=this.serialize();this.config={...this.config,...t,...e}}_configToProps(e){return e}}function Lg(l){return new Proxy(Zn,{construct(e,[t]){const n=t.props||{};return n.template=l,t.props=n,new e(t)}})}function Rg(l){let e,t;return e=new Qn({props:{api:l[0],config:l[1],shape:l[2]}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,[i]){const s={};i&1&&(s.api=n[0]),i&2&&(s.config=n[1]),i&4&&(s.shape=n[2]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Ag(l,e,t){let{api:n}=e,{config:i}=e,{shape:s}=e;return l.$$set=r=>{"api"in r&&t(0,n=r.api),"config"in r&&t(1,i=r.config),"shape"in r&&t(2,s=r.shape)},[n,i,s]}class Pg extends ee{constructor(e){super(),x(this,e,Ag,Rg,Z,{api:0,config:1,shape:2})}}class Ng{api;config;container;events;_component;constructor(e,t){this.container=typeof e=="string"?document.querySelector(e):e,this.config=t,this._init()}destructor(){this._component.$destroy(),this._component=this.api=this.events=null}setConfig(e){e&&(this.config={...this.config,...e},this._init())}setLocale(e,t){this.setConfig({locale:e,api:t})}_init(){this._component&&this.destructor();const e=new Map([["wx-i18n",Bt(this.config.locale)],["wx-theme",Xn(this.container,this.config.theme,"material")]]);this._component=new Pg({target:this.container,props:this._configToProps(this.config),context:e}),this.events=new $n(this.api)}_configToProps(e){return e}}const Do={};function zg(l){return Do[l]||l}function At(l,e){Do[l]=e}function Hg(l){let e,t,n;return{c(){e=I("div"),t=$(" "),g(e,"class",n="wx-separator"+(l[0]?"-menu":"")+" svelte-1eu7qav")},m(i,s){v(i,e,s),M(e,t)},p(i,[s]){s&1&&n!==(n="wx-separator"+(i[0]?"-menu":"")+" svelte-1eu7qav")&&g(e,"class",n)},i:K,o:K,d(i){i&&k(e)}}}function Og(l,e,t){let{menu:n=!1}=e;return l.$$set=i=>{"menu"in i&&t(0,n=i.menu)},[n]}class Eo extends ee{constructor(e){super(),x(this,e,Og,Hg,Z,{menu:0})}}function Fg(l){let e;return{c(){e=I("div"),g(e,"class","wx-spacer svelte-1mbb7ow")},m(t,n){v(t,e,n)},p:K,i:K,o:K,d(t){t&&k(e)}}}class Lo extends ee{constructor(e){super(),x(this,e,null,Fg,Z,{})}}function qg(l){let e,t,n,i,s,r;const a=[{text:l[3]},{menu:l[1]},l[0]];function o(c){l[8](c)}var u=l[2];function f(c,d){let m={};for(let _=0;_<a.length;_+=1)m=Ce(m,a[_]);return d!==void 0&&d&11&&(m=Ce(m,st(a,[d&8&&{text:c[3]},d&2&&{menu:c[1]},d&1&&rt(c[0])]))),c[4]!==void 0&&(m.value=c[4]),{props:m}}return u&&(t=Je(u,f(l)),ue.push(()=>Se(t,"value",o)),t.$on("click",l[5])),{c(){e=I("div"),t&&P(t.$$.fragment),g(e,"class",i="wx-tb-element "+(l[0].css||"")+" svelte-ptl7r2"),g(e,"data-id",s=l[0].id),X(e,"wx-spacer",l[0].spacer),X(e,"wx-menu",l[1])},m(c,d){v(c,e,d),t&&R(t,e,null),r=!0},p(c,d){if(d&4&&u!==(u=c[2])){if(t){W();const m=t;p(m.$$.fragment,1,0,()=>{A(m,1)}),Y()}u?(t=Je(u,f(c,d)),ue.push(()=>Se(t,"value",o)),t.$on("click",c[5]),P(t.$$.fragment),b(t.$$.fragment,1),R(t,e,null)):t=null}else if(u){const m=d&11?st(a,[d&8&&{text:c[3]},d&2&&{menu:c[1]},d&1&&rt(c[0])]):{};!n&&d&16&&(n=!0,m.value=c[4],Ie(()=>n=!1)),t.$set(m)}(!r||d&1&&i!==(i="wx-tb-element "+(c[0].css||"")+" svelte-ptl7r2"))&&g(e,"class",i),(!r||d&1&&s!==(s=c[0].id))&&g(e,"data-id",s),(!r||d&1)&&X(e,"wx-spacer",c[0].spacer),(!r||d&3)&&X(e,"wx-menu",c[1])},i(c){r||(t&&b(t.$$.fragment,c),r=!0)},o(c){t&&p(t.$$.fragment,c),r=!1},d(c){c&&k(e),t&&A(t)}}}function jg(l){let e,t;return e=new Eo({props:{menu:l[1]}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&2&&(s.menu=n[1]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Ug(l){let e,t;return e=new Lo({props:{menu:l[1]}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&2&&(s.menu=n[1]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Vg(l){let e,t,n,i;const s=[Ug,jg,qg],r=[];function a(o,u){return o[0].comp=="spacer"?0:o[0].comp=="separator"?1:2}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=se()},m(o,u){r[e].m(o,u),v(o,n,u),i=!0},p(o,[u]){let f=e;e=a(o),e===f?r[e].p(o,u):(W(),p(r[f],1,1,()=>{r[f]=null}),Y(),t=r[e],t?t.p(o,u):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(n.parentNode,n))},i(o){i||(b(t),i=!0)},o(o){p(t),i=!1},d(o){o&&k(n),r[e].d(o)}}}function Kg(l,e,t){let n;const i=Ee();let{item:s={}}=e,{menu:r=!1}=e,{values:a}=e,o;function u(){s.handler&&s.handler(s),i("click",{item:s})}let f=Pn(null);he(l,f,_=>t(4,n=_));let c=!1;f.subscribe(_=>{c||i("change",{value:_,item:s})});let d="";function m(_){n=_,f.set(n)}return l.$$set=_=>{"item"in _&&t(0,s=_.item),"menu"in _&&t(1,r=_.menu),"values"in _&&t(7,a=_.values)},l.$$.update=()=>{l.$$.dirty&1&&t(2,o=zg(s.comp||"label")),l.$$.dirty&129&&s.key&&(c=!0,a?Pt(f,n=a[s.key],n):Pt(f,n=void 0,n),c=!1),l.$$.dirty&3&&t(3,d=r&&s.menuText||s.text)},[s,r,o,d,n,u,f,a,m]}class xn extends ee{constructor(e){super(),x(this,e,Kg,Vg,Z,{item:0,menu:1,values:7})}}function Ro(l){let e,t;return{c(){e=I("i"),g(e,"class",t=qe(l[0])+" svelte-16d1jeh")},m(n,i){v(n,e,i)},p(n,i){i&1&&t!==(t=qe(n[0])+" svelte-16d1jeh")&&g(e,"class",t)},d(n){n&&k(e)}}}function Bg(l){let e;return{c(){e=$(l[3])},m(t,n){v(t,e,n)},p(t,n){n&8&&re(e,t[3])},i:K,o:K,d(t){t&&k(e)}}}function Wg(l){let e;const t=l[11].default,n=be(t,l,l[10],null);return{c(){n&&n.c()},m(i,s){n&&n.m(i,s),e=!0},p(i,s){n&&n.p&&(!e||s&1024)&&we(n,t,i,i[10],e?pe(t,i[10],s,null):ke(i[10]),null)},i(i){e||(b(n,i),e=!0)},o(i){p(n,i),e=!1},d(i){n&&n.d(i)}}}function Yg(l){let e,t,n,i,s,r,a,o,u=l[0]&&Ro(l);const f=[Wg,Bg],c=[];function d(m,_){return m[5]?0:1}return n=d(l),i=c[n]=f[n](l),{c(){e=I("button"),u&&u.c(),t=H(),i.c(),g(e,"title",l[2]),g(e,"class",s=qe(l[4])+" svelte-16d1jeh"),e.disabled=l[1],X(e,"icon",l[0]&&(!l[5]||!l[5].default))},m(m,_){v(m,e,_),u&&u.m(e,null),M(e,t),c[n].m(e,null),r=!0,a||(o=te(e,"click",l[6]),a=!0)},p(m,[_]){m[0]?u?u.p(m,_):(u=Ro(m),u.c(),u.m(e,t)):u&&(u.d(1),u=null),i.p(m,_),(!r||_&4)&&g(e,"title",m[2]),(!r||_&16&&s!==(s=qe(m[4])+" svelte-16d1jeh"))&&g(e,"class",s),(!r||_&2)&&(e.disabled=m[1]),(!r||_&49)&&X(e,"icon",m[0]&&(!m[5]||!m[5].default))},i(m){r||(b(i),r=!0)},o(m){p(i),r=!1},d(m){m&&k(e),u&&u.d(),c[n].d(),a=!1,o()}}}function Gg(l,e,t){let{$$slots:n={},$$scope:i}=e,{type:s=""}=e,{css:r=""}=e,{click:a}=e,{icon:o=""}=e,{disabled:u=!1}=e,{title:f=""}=e,{text:c=""}=e;const d=e.$$slots;let m;const _=Ee(),h=w=>{u||(_("click"),a&&a(w))};return l.$$set=w=>{t(13,e=Ce(Ce({},e),We(w))),"type"in w&&t(7,s=w.type),"css"in w&&t(8,r=w.css),"click"in w&&t(9,a=w.click),"icon"in w&&t(0,o=w.icon),"disabled"in w&&t(1,u=w.disabled),"title"in w&&t(2,f=w.title),"text"in w&&t(3,c=w.text),"$$scope"in w&&t(10,i=w.$$scope)},l.$$.update=()=>{l.$$.dirty&384&&t(4,m=r+(s?(r?" ":"")+s:""))},e=We(e),[o,u,f,c,m,d,h,s,r,a,i,n]}class el extends ee{constructor(e){super(),x(this,e,Gg,Yg,Z,{type:7,css:8,click:9,icon:0,disabled:1,title:2,text:3})}}const{document:Jg}=ea;function Qg(l){let e,t,n,i,s,r;const a=l[8].default,o=be(a,l,l[7],null);return{c(){e=H(),t=I("div"),o&&o.c(),g(t,"class",n=`dropdown ${l[0]}-${l[1]} svelte-nevbmr`),ce(t,"width",l[2])},m(u,f){v(u,e,f),v(u,t,f),o&&o.m(t,null),l[9](t),i=!0,s||(r=te(Jg.body,"click",l[4]),s=!0)},p(u,[f]){o&&o.p&&(!i||f&128)&&we(o,a,u,u[7],i?pe(a,u[7],f,null):ke(u[7]),null),(!i||f&3&&n!==(n=`dropdown ${u[0]}-${u[1]} svelte-nevbmr`))&&g(t,"class",n),(!i||f&4)&&ce(t,"width",u[2])},i(u){i||(b(o,u),i=!0)},o(u){p(o,u),i=!1},d(u){u&&(k(e),k(t)),o&&o.d(u),l[9](null),s=!1,r()}}}function Xg(l,e,t){let{$$slots:n={},$$scope:i}=e,{position:s="bottom"}=e,{align:r="start"}=e,{autoFit:a=!0}=e,{cancel:o=null}=e,{width:u="100%"}=e,f;vn(()=>{if(a){const _=f.getBoundingClientRect(),h=document.body.getBoundingClientRect();return _.right>=h.right&&t(1,r="end"),_.bottom>=h.bottom&&t(0,s="top"),`${s}-${r}`}});function c(_){new Date-d<200||f.contains(_.target)||o&&o(_)}const d=new Date;function m(_){ue[_?"unshift":"push"](()=>{f=_,t(3,f)})}return l.$$set=_=>{"position"in _&&t(0,s=_.position),"align"in _&&t(1,r=_.align),"autoFit"in _&&t(5,a=_.autoFit),"cancel"in _&&t(6,o=_.cancel),"width"in _&&t(2,u=_.width),"$$scope"in _&&t(7,i=_.$$scope)},[s,r,u,f,c,a,o,i,n,m]}class Ao extends ee{constructor(e){super(),x(this,e,Xg,Qg,Z,{position:0,align:1,autoFit:5,cancel:6,width:2})}}const Po={core:{ok:"OK",cancel:"Cancel"},calendar:{monthFull:["January","February","March","April","May","June","July","August","September","October","November","December"],monthShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayFull:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],hours:"Hours",minutes:"Minutes",done:"Done",clear:"Clear",today:"Today",am:["am","AM"],pm:["pm","PM"],weekStart:7,timeFormat:24}};function Zg(l){let e;const t=l[3].default,n=be(t,l,l[2],null);return{c(){n&&n.c()},m(i,s){n&&n.m(i,s),e=!0},p(i,[s]){n&&n.p&&(!e||s&4)&&we(n,t,i,i[2],e?pe(t,i[2],s,null):ke(i[2]),null)},i(i){e||(b(n,i),e=!0)},o(i){p(n,i),e=!1},d(i){n&&n.d(i)}}}function $g(l,e,t){let{$$slots:n={},$$scope:i}=e,{words:s=null}=e,{optional:r=!1}=e,a=ve("wx-i18n");return a||(a=Bt(Po)),a=a.extend(s,r),_t("wx-i18n",a),l.$$set=o=>{"words"in o&&t(0,s=o.words),"optional"in o&&t(1,r=o.optional),"$$scope"in o&&t(2,i=o.$$scope)},[s,r,i,n]}class xg extends ee{constructor(e){super(),x(this,e,$g,Zg,Z,{words:0,optional:1})}}function No(l,e,t){const n=l.slice();return n[11]=e[t],n}function eb(l){let e,t,n,i,s=fe(l[0].items),r=[];for(let u=0;u<s.length;u+=1)r[u]=zo(No(l,s,u));const a=u=>p(r[u],1,1,()=>{r[u]=null});let o=l[0].text&&Ho(l);return{c(){e=I("div");for(let u=0;u<r.length;u+=1)r[u].c();t=H(),o&&o.c(),n=se(),g(e,"class","wx-tb-body svelte-urlj1k")},m(u,f){v(u,e,f);for(let c=0;c<r.length;c+=1)r[c]&&r[c].m(e,null);v(u,t,f),o&&o.m(u,f),v(u,n,f),i=!0},p(u,f){if(f&19){s=fe(u[0].items);let c;for(c=0;c<s.length;c+=1){const d=No(u,s,c);r[c]?(r[c].p(d,f),b(r[c],1)):(r[c]=zo(d),r[c].c(),b(r[c],1),r[c].m(e,null))}for(W(),c=s.length;c<r.length;c+=1)a(c);Y()}u[0].text?o?o.p(u,f):(o=Ho(u),o.c(),o.m(n.parentNode,n)):o&&(o.d(1),o=null)},i(u){if(!i){for(let f=0;f<s.length;f+=1)b(r[f]);i=!0}},o(u){r=r.filter(Boolean);for(let f=0;f<r.length;f+=1)p(r[f]);i=!1},d(u){u&&(k(e),k(t),k(n)),at(r,u),o&&o.d(u)}}}function tb(l){let e,t,n,i,s,r,a,o,u=l[0].icon&&Oo(l),f=l[0].text&&Fo(l),c=l[0].text&&!l[0].icon&&qo(),d=!l[3]&&jo(l);return{c(){e=I("div"),u&&u.c(),t=H(),f&&f.c(),n=H(),c&&c.c(),i=H(),d&&d.c(),s=se(),g(e,"class","wx-collapsed svelte-urlj1k")},m(m,_){v(m,e,_),u&&u.m(e,null),M(e,t),f&&f.m(e,null),M(e,n),c&&c.m(e,null),v(m,i,_),d&&d.m(m,_),v(m,s,_),r=!0,a||(o=te(e,"click",l[5]),a=!0)},p(m,_){m[0].icon?u?u.p(m,_):(u=Oo(m),u.c(),u.m(e,t)):u&&(u.d(1),u=null),m[0].text?f?f.p(m,_):(f=Fo(m),f.c(),f.m(e,n)):f&&(f.d(1),f=null),m[0].text&&!m[0].icon?c||(c=qo(),c.c(),c.m(e,null)):c&&(c.d(1),c=null),m[3]?d&&(W(),p(d,1,1,()=>{d=null}),Y()):d?(d.p(m,_),_&8&&b(d,1)):(d=jo(m),d.c(),b(d,1),d.m(s.parentNode,s))},i(m){r||(b(d),r=!0)},o(m){p(d),r=!1},d(m){m&&(k(e),k(i),k(s)),u&&u.d(),f&&f.d(),c&&c.d(),d&&d.d(m),a=!1,o()}}}function nb(l){let e,t;return e=new xn({props:{item:l[11],values:l[1]}}),e.$on("click",l[4]),e.$on("change",l[9]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.item=n[11]),i&2&&(s.values=n[1]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function lb(l){let e,t;return e=new _n({props:{item:l[11],values:l[1]}}),e.$on("click",l[4]),e.$on("change",l[8]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.item=n[11]),i&2&&(s.values=n[1]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function zo(l){let e,t,n,i;const s=[lb,nb],r=[];function a(o,u){return o[11].items?0:1}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=se()},m(o,u){r[e].m(o,u),v(o,n,u),i=!0},p(o,u){let f=e;e=a(o),e===f?r[e].p(o,u):(W(),p(r[f],1,1,()=>{r[f]=null}),Y(),t=r[e],t?t.p(o,u):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(n.parentNode,n))},i(o){i||(b(t),i=!0)},o(o){p(t),i=!1},d(o){o&&k(n),r[e].d(o)}}}function Ho(l){let e,t=l[0].text+"",n;return{c(){e=I("div"),n=$(t),g(e,"class","wx-label svelte-urlj1k")},m(i,s){v(i,e,s),M(e,n)},p(i,s){s&1&&t!==(t=i[0].text+"")&&re(n,t)},d(i){i&&k(e)}}}function Oo(l){let e,t;return{c(){e=I("i"),g(e,"class",t="icon "+l[0].icon+" svelte-urlj1k")},m(n,i){v(n,e,i)},p(n,i){i&1&&t!==(t="icon "+n[0].icon+" svelte-urlj1k")&&g(e,"class",t)},d(n){n&&k(e)}}}function Fo(l){let e,t=l[0].text+"",n;return{c(){e=I("div"),n=$(t),g(e,"class","wx-label-text")},m(i,s){v(i,e,s),M(e,n)},p(i,s){s&1&&t!==(t=i[0].text+"")&&re(n,t)},d(i){i&&k(e)}}}function qo(l){let e;return{c(){e=I("i"),g(e,"class","wx-label-arrow wxi-angle-down")},m(t,n){v(t,e,n)},d(t){t&&k(e)}}}function jo(l){let e,t;return e=new Ao({props:{width:"",cancel:l[6],$$slots:{default:[ib]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&16391&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function ib(l){let e,t,n;return t=new _n({props:{item:{...l[0],text:"",collapsed:!1},values:l[1],menu:l[2]}}),t.$on("change",l[7]),t.$on("click",l[4]),{c(){e=I("div"),P(t.$$.fragment),g(e,"class","wx-drop-group")},m(i,s){v(i,e,s),R(t,e,null),n=!0},p(i,s){const r={};s&1&&(r.item={...i[0],text:"",collapsed:!1}),s&2&&(r.values=i[1]),s&4&&(r.menu=i[2]),t.$set(r)},i(i){n||(b(t.$$.fragment,i),n=!0)},o(i){p(t.$$.fragment,i),n=!1},d(i){i&&k(e),A(t)}}}function sb(l){let e,t,n,i,s;const r=[tb,eb],a=[];function o(u,f){return u[0].collapsed&&!u[2]?0:1}return t=o(l),n=a[t]=r[t](l),{c(){e=I("div"),n.c(),g(e,"class",i="wx-tb-group "+(l[0].css||"")+" svelte-urlj1k"),X(e,"wx-column",l[0].layout=="column"),X(e,"wx-group-collapsed",l[0].collapsed&&!l[2])},m(u,f){v(u,e,f),a[t].m(e,null),s=!0},p(u,[f]){let c=t;t=o(u),t===c?a[t].p(u,f):(W(),p(a[c],1,1,()=>{a[c]=null}),Y(),n=a[t],n?n.p(u,f):(n=a[t]=r[t](u),n.c()),b(n,1),n.m(e,null)),(!s||f&1&&i!==(i="wx-tb-group "+(u[0].css||"")+" svelte-urlj1k"))&&g(e,"class",i),(!s||f&1)&&X(e,"wx-column",u[0].layout=="column"),(!s||f&5)&&X(e,"wx-group-collapsed",u[0].collapsed&&!u[2])},i(u){s||(b(n),s=!0)},o(u){p(n),s=!1},d(u){u&&k(e),a[t].d()}}}function rb(l,e,t){const n=Ee();let{item:i}=e,{values:s=null}=e,{menu:r=!1}=e,a=!1;const o=_=>{f(),n("click",_.detail)},u=()=>t(3,a=!1),f=()=>t(3,a=!0);function c(_){ye.call(this,l,_)}function d(_){ye.call(this,l,_)}function m(_){ye.call(this,l,_)}return l.$$set=_=>{"item"in _&&t(0,i=_.item),"values"in _&&t(1,s=_.values),"menu"in _&&t(2,r=_.menu)},l.$$.update=()=>{l.$$.dirty&1&&i.collapsed&&t(3,a=!0)},[i,s,r,a,o,u,f,c,d,m]}class _n extends ee{constructor(e){super(),x(this,e,rb,sb,Z,{item:0,values:1,menu:2})}}function Uo(l,e,t){const n=l.slice();return n[14]=e[t],n}function Vo(l){let e,t;return e=new Ao({props:{width:l[3]+"px",cancel:l[6],$$slots:{default:[ub]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&8&&(s.width=n[3]+"px"),i&131077&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function ob(l){let e,t;return e=new xn({props:{item:l[14],values:l[2],menu:!0}}),e.$on("click",l[10]),e.$on("change",l[11]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.item=n[14]),i&4&&(s.values=n[2]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function ab(l){let e,t;return e=new _n({props:{item:l[14],values:l[2],menu:!0}}),e.$on("click",l[8]),e.$on("change",l[9]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.item=n[14]),i&4&&(s.values=n[2]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Ko(l){let e,t,n,i;const s=[ab,ob],r=[];function a(o,u){return o[14].items?0:1}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=se()},m(o,u){r[e].m(o,u),v(o,n,u),i=!0},p(o,u){let f=e;e=a(o),e===f?r[e].p(o,u):(W(),p(r[f],1,1,()=>{r[f]=null}),Y(),t=r[e],t?t.p(o,u):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(n.parentNode,n))},i(o){i||(b(t),i=!0)},o(o){p(t),i=!1},d(o){o&&k(n),r[e].d(o)}}}function ub(l){let e,t,n=fe(l[0]),i=[];for(let r=0;r<n.length;r+=1)i[r]=Ko(Uo(l,n,r));const s=r=>p(i[r],1,1,()=>{i[r]=null});return{c(){e=I("div");for(let r=0;r<i.length;r+=1)i[r].c();g(e,"class","wx-drop-menu svelte-5toy9z")},m(r,a){v(r,e,a);for(let o=0;o<i.length;o+=1)i[o]&&i[o].m(e,null);t=!0},p(r,a){if(a&5){n=fe(r[0]);let o;for(o=0;o<n.length;o+=1){const u=Uo(r,n,o);i[o]?(i[o].p(u,a),b(i[o],1)):(i[o]=Ko(u),i[o].c(),b(i[o],1),i[o].m(e,null))}for(W(),o=n.length;o<i.length;o+=1)s(o);Y()}},i(r){if(!t){for(let a=0;a<n.length;a+=1)b(i[a]);t=!0}},o(r){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)p(i[a]);t=!1},d(r){r&&k(e),at(i,r)}}}function fb(l){let e,t,n,i,s,r,a;t=new el({props:{icon:"wxi-dots-h",click:l[7]}});let o=l[4]&&Vo(l);return{c(){e=I("div"),P(t.$$.fragment),n=H(),o&&o.c(),g(e,"class",i="wx-menu "+(l[1]||"")+" svelte-5toy9z"),g(e,"data-id","$menu")},m(u,f){v(u,e,f),R(t,e,null),M(e,n),o&&o.m(e,null),l[12](e),s=!0,r||(a=He(D1.call(null,e,l[13])),r=!0)},p(u,[f]){u[4]?o?(o.p(u,f),f&16&&b(o,1)):(o=Vo(u),o.c(),b(o,1),o.m(e,null)):o&&(W(),p(o,1,1,()=>{o=null}),Y()),(!s||f&2&&i!==(i="wx-menu "+(u[1]||"")+" svelte-5toy9z"))&&g(e,"class",i)},i(u){s||(b(t.$$.fragment,u),b(o),s=!0)},o(u){p(t.$$.fragment,u),p(o),s=!1},d(u){u&&k(e),A(t),o&&o.d(),l[12](null),r=!1,a()}}}function cb(l,e,t){let{items:n=[]}=e,{css:i}=e,{values:s}=e,{width:r}=e,a,o;function u(){t(4,a=null)}function f(){setTimeout(()=>t(4,a=!0))}function c(y){ye.call(this,l,y)}function d(y){ye.call(this,l,y)}function m(y){ye.call(this,l,y)}function _(y){ye.call(this,l,y)}function h(y){ue[y?"unshift":"push"](()=>{o=y,t(5,o)})}const w=()=>u;return l.$$set=y=>{"items"in y&&t(0,n=y.items),"css"in y&&t(1,i=y.css),"values"in y&&t(2,s=y.values),"width"in y&&t(3,r=y.width)},[n,i,s,r,a,o,u,f,c,d,m,_,h,w]}class db extends ee{constructor(e){super(),x(this,e,cb,fb,Z,{items:0,css:1,values:2,width:3})}}function Bo(l,e,t){const n=l.slice();return n[20]=e[t],n[22]=t,n}function mb(l){let e,t;return e=new xn({props:{item:l[20],values:l[0]}}),e.$on("click",l[10]),e.$on("change",l[7]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&64&&(s.item=n[20]),i&1&&(s.values=n[0]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function _b(l){let e,t;return e=new _n({props:{item:l[20],values:l[0]}}),e.$on("click",l[9]),e.$on("change",l[7]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&64&&(s.item=n[20]),i&1&&(s.values=n[0]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Wo(l){let e,t,n,i;const s=[_b,mb],r=[];function a(o,u){return o[20].items?0:1}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=se()},m(o,u){r[e].m(o,u),v(o,n,u),i=!0},p(o,u){let f=e;e=a(o),e===f?r[e].p(o,u):(W(),p(r[f],1,1,()=>{r[f]=null}),Y(),t=r[e],t?t.p(o,u):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(n.parentNode,n))},i(o){i||(b(t),i=!0)},o(o){p(t),i=!1},d(o){o&&k(n),r[e].d(o)}}}function Yo(l){let e,t;return e=new db({props:{items:l[5],css:l[1],values:l[0]}}),e.$on("click",l[11]),e.$on("change",l[7]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&32&&(s.items=n[5]),i&2&&(s.css=n[1]),i&1&&(s.values=n[0]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function hb(l){let e,t,n,i,s=fe(l[6]),r=[];for(let u=0;u<s.length;u+=1)r[u]=Wo(Bo(l,s,u));const a=u=>p(r[u],1,1,()=>{r[u]=null});let o=l[5].length&&Yo(l);return{c(){e=I("div");for(let u=0;u<r.length;u+=1)r[u].c();t=H(),o&&o.c(),g(e,"class",n="wx-toolbar "+l[2]+" svelte-b19ms9"),X(e,"wx-wrap",l[3]==="wrap")},m(u,f){v(u,e,f);for(let c=0;c<r.length;c+=1)r[c]&&r[c].m(e,null);M(e,t),o&&o.m(e,null),l[12](e),i=!0},p(u,[f]){if(f&193){s=fe(u[6]);let c;for(c=0;c<s.length;c+=1){const d=Bo(u,s,c);r[c]?(r[c].p(d,f),b(r[c],1)):(r[c]=Wo(d),r[c].c(),b(r[c],1),r[c].m(e,t))}for(W(),c=s.length;c<r.length;c+=1)a(c);Y()}u[5].length?o?(o.p(u,f),f&32&&b(o,1)):(o=Yo(u),o.c(),b(o,1),o.m(e,null)):o&&(W(),p(o,1,1,()=>{o=null}),Y()),(!i||f&4&&n!==(n="wx-toolbar "+u[2]+" svelte-b19ms9"))&&g(e,"class",n),(!i||f&12)&&X(e,"wx-wrap",u[3]==="wrap")},i(u){if(!i){for(let f=0;f<s.length;f+=1)b(r[f]);b(o),i=!0}},o(u){r=r.filter(Boolean);for(let f=0;f<r.length;f+=1)p(r[f]);p(o),i=!1},d(u){u&&k(e),at(r,u),o&&o.d(),l[12](null)}}}function gb(l,e,t){let{items:n=[]}=e,{menuCss:i=""}=e,{css:s}=e,{values:r=null}=e,{overflow:a="menu"}=e;const o=Ee();function u(E){r&&(t(0,r[E.detail.item.key]=E.detail.value,r),t(0,r)),o("change",E.detail)}let f,c=-1,d=[],m;function _(){if(a==="wrap")return;const E=f.clientWidth;if(f.scrollWidth>E){if(a==="collapse")return w();const U=f.children;let O=0;for(let D=0;D<n.length;D++){if(O+=U[D].clientWidth,n[D].comp=="separator"&&(O+=8),O>E-40){if(c===D)return;c=D,t(5,d=[]);for(let j=D;j<n.length;j++)d.push(n[j]),U[j].style.visibility="hidden";D>0&&n[D-1].comp=="separator"&&(U[D-1].style.visibility="hidden");break}U[D].style.visibility=""}}else{const U=E-h();if(U<=0)return;if(a==="collapse")return y(U);if(d.length){c=null;const O=f.children;for(let D=0;D<n.length;D++)O[D].style.visibility="";t(5,d=[])}}}function h(){const E=f.children;let F=0;for(let G=0;G<n.length;G++)n[G].comp!="spacer"&&(F+=E[G].clientWidth,n[G].comp=="separator"&&(F+=8));return F}function w(){for(let E=n.length-1;E>=0;E--)if(n[E].items&&!n[E].collapsed){t(8,n[E].collapsed=!0,n),t(8,n[E].$width=f.children[E].offsetWidth,n),ct().then(_);return}}function y(E){for(let F=0;F<n.length;F++)if(n[F].collapsed&&n[F].$width){n[F].$width-f.children[F].offsetWidth<E+10&&(t(8,n[F].collapsed=!1,n),ct().then(_));return}}function S(E){E.forEach(F=>{F.id||(F.id=Nr())})}ot(()=>{const E=new ResizeObserver(()=>_());return E.observe(f),()=>{E&&E.unobserve(f)}});function C(E){ye.call(this,l,E)}function T(E){ye.call(this,l,E)}function L(E){ye.call(this,l,E)}function N(E){ue[E?"unshift":"push"](()=>{f=E,t(4,f)})}return l.$$set=E=>{"items"in E&&t(8,n=E.items),"menuCss"in E&&t(1,i=E.menuCss),"css"in E&&t(2,s=E.css),"values"in E&&t(0,r=E.values),"overflow"in E&&t(3,a=E.overflow)},l.$$.update=()=>{l.$$.dirty&256&&(S(n),t(6,m=n))},[r,i,s,a,f,d,m,u,n,C,T,L,N]}let bb=class extends ee{constructor(e){super(),x(this,e,gb,hb,Z,{items:8,menuCss:1,css:2,values:0,overflow:3})}};function pb(l){let e,t;return e=new el({props:{icon:l[0],type:l[3],css:l[2],text:l[1],disabled:l[4]}}),e.$on("click",l[7]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.icon=n[0]),i&8&&(s.type=n[3]),i&4&&(s.css=n[2]),i&2&&(s.text=n[1]),i&16&&(s.disabled=n[4]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function wb(l){let e,t,n,i,s,r,a;return{c(){e=I("div"),t=I("i"),i=H(),s=$(l[1]),g(t,"class",n=(l[0]||"wxi-empty")+" "+(l[2]||"")+" svelte-b4dkf1"),g(e,"class","wx-item svelte-b4dkf1")},m(o,u){v(o,e,u),M(e,t),M(e,i),M(e,s),r||(a=te(e,"click",l[6]),r=!0)},p(o,u){u&5&&n!==(n=(o[0]||"wxi-empty")+" "+(o[2]||"")+" svelte-b4dkf1")&&g(t,"class",n),u&2&&re(s,o[1])},i:K,o:K,d(o){o&&k(e),r=!1,a()}}}function kb(l){let e,t,n,i;const s=[wb,pb],r=[];function a(o,u){return o[5]?0:1}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=se()},m(o,u){r[e].m(o,u),v(o,n,u),i=!0},p(o,[u]){let f=e;e=a(o),e===f?r[e].p(o,u):(W(),p(r[f],1,1,()=>{r[f]=null}),Y(),t=r[e],t?t.p(o,u):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(n.parentNode,n))},i(o){i||(b(t),i=!0)},o(o){p(t),i=!1},d(o){o&&k(n),r[e].d(o)}}}function vb(l,e,t){let{icon:n}=e,{text:i=""}=e,{css:s}=e,{type:r}=e,{disabled:a}=e,{menu:o}=e;function u(c){ye.call(this,l,c)}function f(c){ye.call(this,l,c)}return l.$$set=c=>{"icon"in c&&t(0,n=c.icon),"text"in c&&t(1,i=c.text),"css"in c&&t(2,s=c.css),"type"in c&&t(3,r=c.type),"disabled"in c&&t(4,a=c.disabled),"menu"in c&&t(5,o=c.menu)},[n,i,s,r,a,o,u,f]}class yb extends ee{constructor(e){super(),x(this,e,vb,kb,Z,{icon:0,text:1,css:2,type:3,disabled:4,menu:5})}}function Sb(l){let e,t=(l[1]||l[0])+"",n;return{c(){e=I("div"),n=$(t),g(e,"class","wx-label svelte-agyr5c")},m(i,s){v(i,e,s),M(e,n)},p(i,s){s&3&&t!==(t=(i[1]||i[0])+"")&&re(n,t)},i:K,o:K,d(i){i&&k(e)}}}function Cb(l){let e,t;const n=l[4].default,i=be(n,l,l[3],null);return{c(){e=I("div"),i&&i.c(),g(e,"class","wx-label svelte-agyr5c")},m(s,r){v(s,e,r),i&&i.m(e,null),t=!0},p(s,r){i&&i.p&&(!t||r&8)&&we(i,n,s,s[3],t?pe(n,s[3],r,null):ke(s[3]),null)},i(s){t||(b(i,s),t=!0)},o(s){p(i,s),t=!1},d(s){s&&k(e),i&&i.d(s)}}}function Ib(l){let e,t,n,i;const s=[Cb,Sb],r=[];function a(o,u){return o[2]?0:1}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=se()},m(o,u){r[e].m(o,u),v(o,n,u),i=!0},p(o,[u]){t.p(o,u)},i(o){i||(b(t),i=!0)},o(o){p(t),i=!1},d(o){o&&k(n),r[e].d(o)}}}function Mb(l,e,t){let{$$slots:n={},$$scope:i}=e,{text:s}=e,{value:r}=e;const a=e.$$slots;return l.$$set=o=>{t(5,e=Ce(Ce({},e),We(o))),"text"in o&&t(0,s=o.text),"value"in o&&t(1,r=o.value),"$$scope"in o&&t(3,i=o.$$scope)},e=We(e),[s,r,a,i,n]}class Tb extends ee{constructor(e){super(),x(this,e,Mb,Ib,Z,{text:0,value:1})}}function Db(l){let e,t;return e=new el({props:{icon:l[0],type:l[3],css:l[2],title:l[1],disabled:l[4]}}),e.$on("click",l[7]),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.icon=n[0]),i&8&&(s.type=n[3]),i&4&&(s.css=n[2]),i&2&&(s.title=n[1]),i&16&&(s.disabled=n[4]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Eb(l){let e,t,n,i,s,r=l[0]&&Go(l);return{c(){e=I("div"),r&&r.c(),t=H(),n=$(l[1]),g(e,"class","wx-item svelte-ng2v87")},m(a,o){v(a,e,o),r&&r.m(e,null),M(e,t),M(e,n),i||(s=te(e,"click",l[6]),i=!0)},p(a,o){a[0]?r?r.p(a,o):(r=Go(a),r.c(),r.m(e,t)):r&&(r.d(1),r=null),o&2&&re(n,a[1])},i:K,o:K,d(a){a&&k(e),r&&r.d(),i=!1,s()}}}function Go(l){let e,t;return{c(){e=I("i"),g(e,"class",t=l[0]+" "+l[2]+" svelte-ng2v87")},m(n,i){v(n,e,i)},p(n,i){i&5&&t!==(t=n[0]+" "+n[2]+" svelte-ng2v87")&&g(e,"class",t)},d(n){n&&k(e)}}}function Lb(l){let e,t,n,i;const s=[Eb,Db],r=[];function a(o,u){return o[5]?0:1}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=se()},m(o,u){r[e].m(o,u),v(o,n,u),i=!0},p(o,[u]){let f=e;e=a(o),e===f?r[e].p(o,u):(W(),p(r[f],1,1,()=>{r[f]=null}),Y(),t=r[e],t?t.p(o,u):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(n.parentNode,n))},i(o){i||(b(t),i=!0)},o(o){p(t),i=!1},d(o){o&&k(n),r[e].d(o)}}}function Rb(l,e,t){let{icon:n}=e,{text:i}=e,{css:s}=e,{type:r}=e,{disabled:a}=e,{menu:o}=e;function u(c){ye.call(this,l,c)}function f(c){ye.call(this,l,c)}return l.$$set=c=>{"icon"in c&&t(0,n=c.icon),"text"in c&&t(1,i=c.text),"css"in c&&t(2,s=c.css),"type"in c&&t(3,r=c.type),"disabled"in c&&t(4,a=c.disabled),"menu"in c&&t(5,o=c.menu)},[n,i,s,r,a,o,u,f]}class Ab extends ee{constructor(e){super(),x(this,e,Rb,Lb,Z,{icon:0,text:1,css:2,type:3,disabled:4,menu:5})}}function Jo(l){let e,t;return{c(){e=I("i"),g(e,"class",t=qe(l[2])+" svelte-g7c8cw")},m(n,i){v(n,e,i)},p(n,i){i&4&&t!==(t=qe(n[2])+" svelte-g7c8cw")&&g(e,"class",t)},d(n){n&&k(e)}}}function Pb(l){let e,t,n,i,s,r,a=l[2]&&Jo(l);return{c(){e=I("div"),a&&a.c(),t=H(),n=$(l[0]),g(e,"class",i="wx-label "+l[1]+" svelte-g7c8cw")},m(o,u){v(o,e,u),a&&a.m(e,null),M(e,t),M(e,n),s||(r=te(e,"click",l[3]),s=!0)},p(o,[u]){o[2]?a?a.p(o,u):(a=Jo(o),a.c(),a.m(e,t)):a&&(a.d(1),a=null),u&1&&re(n,o[0]),u&2&&i!==(i="wx-label "+o[1]+" svelte-g7c8cw")&&g(e,"class",i)},i:K,o:K,d(o){o&&k(e),a&&a.d(),s=!1,r()}}}function Nb(l,e,t){const n=Ee();let{id:i=""}=e,{text:s=""}=e,{css:r=""}=e,{icon:a=""}=e;function o(){n("click",{id:i})}return l.$$set=u=>{"id"in u&&t(4,i=u.id),"text"in u&&t(0,s=u.text),"css"in u&&t(1,r=u.css),"icon"in u&&t(2,a=u.icon)},[s,r,a,o,i]}class zb extends ee{constructor(e){super(),x(this,e,Nb,Pb,Z,{id:4,text:0,css:1,icon:2})}}At("button",yb),At("separator",Eo),At("spacer",Lo),At("label",Tb),At("item",zb),At("icon",Ab);function Hb(l){let e,t;return e=new bb({props:{api:l[0],items:l[1]}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,i){const s={};i&1&&(s.api=n[0]),i&2&&(s.items=n[1]),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Ob(l){let e,t;return e=new xg({props:{words:{...Po,...Tn},optional:!0,$$slots:{default:[Hb]},$$scope:{ctx:l}}}),{c(){P(e.$$.fragment)},m(n,i){R(e,n,i),t=!0},p(n,[i]){const s={};i&7&&(s.$$scope={dirty:i,ctx:n}),e.$set(s)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){p(e.$$.fragment,n),t=!1},d(n){A(e,n)}}}function Fb(l,e,t){let{api:n}=e,{items:i}=e;return i.forEach(s=>{s.api=n,s.type==="search"?s.comp=fg:s.type==="undo"?s.comp=Sg:s.type==="redo"?s.comp=Mg:s.type==="spacer"?s.comp="spacer":s.type==="sort"?s.comp=kg:s.type==="addColumn"?s.comp=gg:s.type==="addRow"?s.comp=mg:s.type==="template"&&s.template?s.comp=Zn:s&&(s.comp=Zn,s.template=s.type)}),l.$$set=s=>{"api"in s&&t(0,n=s.api),"items"in s&&t(1,i=s.items)},[n,i]}class qb extends ee{constructor(e){super(),x(this,e,Fb,Ob,Z,{api:0,items:1})}}class jb{api;events;config;container;_toolbar;constructor(e,t){this.container=typeof e=="string"?document.querySelector(e):e,this.config=t,this._init()}destructor(){this._toolbar.$destroy(),this._toolbar=this.api=this.events=null}setConfig(e){e&&(this.config={...this.config,...e},this._init())}setLocale(e,t){const n={locale:e};t&&(n.api=t),this.setConfig(n)}_init(){this._toolbar&&this.destructor();const e=new Map([["wx-i18n",Bt(this.config.locale)],["wx-theme",Xn(this.container,this.config.theme,"material")]]);this._toolbar=new qb({target:this.container,props:this._configToProps(this.config),context:e}),this.events=new $n(this.api)}_configToProps(e){let t=[{type:"search"},{type:"spacer"},{type:"undo"},{type:"redo"},{type:"sort"},{type:"addColumn"},{type:"addRow"}];return e.items&&(t=this._normalizeItems(e.items)),{...e,items:t}}_normalizeItems(e){return e.map(t=>typeof t=="string"?{type:t}:typeof t=="function"?{type:"template",template:t}:"template"in t&&!t.type?{type:"template",template:t.template}:t)}}new Date().valueOf();function tl(l){return typeof l=="string"&&l.length===20&&parseInt(l.substr(7))>1e12}class Ub{constructor(){this._nextHandler=null,this._handlers={},this._tag=new WeakMap,this.exec=this.exec.bind(this)}on(e,t,n){let i=this._handlers[e];i?n&&n.intercept?i.unshift(t):i.push(t):i=this._handlers[e]=[t],n&&n.tag&&this._tag.set(t,n.tag)}intercept(e,t,n){this.on(e,t,{...n,intercept:!0})}detach(e){for(const t in this._handlers){const n=this._handlers[t];for(let i=n.length-1;i>=0;i--)this._tag.get(n[i])===e&&n.splice(i,1)}}async exec(e,t){const n=this._handlers[e];if(n)for(let i=0;i<n.length;i++){const s=n[i](t);if(s===!1||s&&s.then&&await s===!1)return}return this._nextHandler&&await this._nextHandler.exec(e,t),t}setNext(e){return this._nextHandler=e}}const hn=Symbol(),Qo=0,Vb=1,Kb=2;class Bb{constructor(){this.reset(!0)}reset(e=!1){this._awaitAddingQueue=[],this._queue={},this._waitPull={},this._status=[],e&&(this._idPool={},this._backId=[])}resolve(e,t){const n=this._backId[t];if(typeof n>"u")return e;const i=n[e];return typeof i>"u"?e:i}getSync(){const e=this._awaitAddingQueue;if(!e.length)return Qo;for(let t=0;t<e.length;t++)if(!e[t].sent)return Vb;return Kb}waitSync(){return new Promise(e=>{this.getSync()===Qo?e():this._status.push(e)})}getId(e){return this._idPool[e]||(tl(e)?null:e)}waitId(e){return new Promise(t=>{const n=this.getId(e);n!==null&&t(n);const i=this._waitPull[e]||[];i.push(t),this._waitPull[e]=i})}add(e,t,n){return new Promise((i,s)=>{if(n={...n,resolve:i,reject:s},n.debounce){const r=`${e}"/"${t.id}`,a=this._queue[r];a&&(n.resolve=o=>{a.resolve(o),i(o)},n.reject=()=>{a.reject(),s()},clearTimeout(a.timer)),this._queue[r]=n,n.timer=setTimeout(()=>{this.tryExec(e,t,n)},n.debounce);return}this.tryExec(e,t,n)})}tryExec(e,t,n,i){const s=this.exec(e,t,n,i);return s===null?(i||this._awaitAddingQueue.push({action:e,data:t,proc:n}),!1):(s.then(r=>{const a=r&&r.id&&r.id!=t.id&&tl(t.id);if(a&&(this._idPool[t.id]=r.id,this._waitPull[t.id]&&(this._waitPull[t.id].forEach(o=>o(r.id)),delete this._waitPull[t.id]),n.kind)){let o=this._backId[n.kind];o||(o=this._backId[n.kind]={}),o[r.id]=t.id}t.response=r,n.resolve(!0),i&&i(),a&&this.execQueue()},()=>{i&&i(),n.reject()}),!0)}exec(e,t,n,i){const s=this.correctID(t,n.ignoreID?t.id:null);if(s===hn)return null;let r;try{r=n.handler(s,e,t)}catch(a){i(),n.reject(a)}return r}correctID(e,t){let n=null;for(const i in e){const s=e[i];if(typeof s=="object"){const r=this.correctID(s,t);if(r!==s){if(r===hn)return hn;n===null&&(n={...e}),n[i]=r}}else if(s!==t&&tl(s)){const r=this._idPool[s];if(!r)return hn;n===null&&(n={...e}),n[i]=r}}return n||e}execQueue(){this._awaitAddingQueue.forEach(e=>{if(!e.sent){const t=()=>this._finishQueue(e);this.tryExec(e.action,e.data,e.proc,t)&&(e.sent=!0)}})}_finishQueue(e){if(this._awaitAddingQueue=this._awaitAddingQueue.filter(t=>t!==e),!this._awaitAddingQueue.length&&this._status.length){const t=[...this._status];this._status=[],t.forEach(n=>n())}}}class Wb extends Ub{constructor(e){super(),this._customHeaders={},this._url=e,this._queue=new Bb;const t=this.getHandlers();for(const n in t)this.on(n,i=>{if(!i.skipProvider)return this._queue.add(n,i,t[n])})}getHandlers(){return{}}setHeaders(e){this._customHeaders=e}getQueue(){return this._queue}send(e,t,n,i={}){const s={"Content-Type":"application/json",...i,...this._customHeaders},r={method:t,headers:s};return n&&(r.body=typeof n=="object"?JSON.stringify(n):n),fetch(`${this._url}${e||""}`,r).then(a=>a.json())}}const Wt=1,gn=2,bn=3,Xo=4;class Yb extends Wb{constructor(e){super(e)}getHandlers(){return{"add-card":{ignoreID:!0,kind:Wt,handler:e=>(e.card=this.prepareCard(e.card),this.send("cards","POST",e))},"update-card":{debounce:500,handler:e=>(e.card=this.prepareCard(e.card),this.send(`cards/${e.id}`,"PUT",e))},"move-card":{handler:e=>this.send(`cards/${e.id}/move`,"PUT",e)},"delete-card":{handler:e=>this.send(`cards/${e.id}`,"DELETE")},"add-column":{ignoreID:!0,kind:bn,handler:e=>this.send("columns","POST",e)},"update-column":{debounce:500,handler:e=>this.send(`columns/${e.id}`,"PUT",e)},"move-column":{handler:e=>this.send(`columns/${e.id}/move`,"PUT",e)},"delete-column":{handler:e=>this.send(`columns/${e.id}`,"DELETE")},"add-row":{ignoreID:!0,kind:gn,handler:e=>this.send("rows","POST",e)},"update-row":{debounce:500,handler:e=>this.send(`rows/${e.id}`,"PUT",e)},"move-row":{handler:e=>this.send(`rows/${e.id}/move`,"PUT",e)},"delete-row":{handler:e=>this.send(`rows/${e.id}`,"DELETE")},"add-vote":{handler:e=>this.send(`cards/${e.cardId}/vote`,"POST")},"delete-vote":{handler:e=>this.send(`cards/${e.cardId}/vote`,"DELETE")},"add-comment":{ignoreID:!0,handler:e=>this.send(`cards/${e.cardId}/comments`,"POST",e.comment)},"update-comment":{handler:e=>this.send(`cards/${e.cardId}/comments/${e.id}`,"PUT",e.comment)},"delete-comment":{handler:e=>this.send(`cards/${e.cardId}/comments/${e.id}`,"DELETE")},"add-link":{ignoreID:!0,kind:Xo,handler:e=>this.send("links","POST",e)},"delete-link":{handler:e=>this.send(`links/${e.id}`,"DELETE")}}}getCards(){return this.send("cards","GET").then(this.parseCards)}getColumns(){return this.send("columns","GET")}getRows(){return this.send("rows","GET")}getUsers(){return this.send("users","GET")}getLinks(){return this.send("links","GET")}getIDResolver(){return this.getQueue().resolve.bind(this.getQueue())}send(e,t,n,i={}){return this._url.charAt(-1)!=="/"&&e[0]!=="/"&&(e="/"+e),super.send(e,t,n,i)}parseCards(e){return e.forEach(t=>(t.end_date&&(t.end_date=new Date(t.end_date)),t.start_date&&(t.start_date=new Date(t.start_date)),t)),e}prepareCard(e){return e?{...e,users:e.users||null}:null}}function Gb(l,e){function t(f){return"id"in f&&(f.id=e(f.id,Wt)),"column"in f&&(f.column=e(f.column,bn)),"row"in f&&(f.row=e(f.row,gn)),f}function n(f){return"id"in f&&(f.id=e(f.id,gn)),f}function i(f){return"id"in f&&(f.id=e(f.id,bn)),f}function s(f){return"id"in f&&(f.id=e(f.id,Xo)),"masterId"in f&&(f.masterId=e(f.masterId,Wt)),"slaveId"in f&&(f.slaveId=e(f.slaveId,Wt)),f}function r(f){const c=t(f.card);switch(c.start_date=c.start_date?new Date(c.start_date):null,c.end_date=c.end_date?new Date(c.end_date):null,f.type){case"add-card":l.exec(f.type,{skipProvider:!0,card:c,select:!1});break;case"update-card":l.exec("update-card",{skipProvider:!0,id:c.id,card:c});break;case"delete-card":l.exec("delete-card",{skipProvider:!0,id:c.id});break;case"move-card":{l.exec("move-card",{skipProvider:!0,id:c.id,rowId:c.row,columnId:c.column,before:e(f.before,Wt)});break}}}function a(f){const c=i(f.column);switch(f.type){case"add-column":l.exec("add-column",{skipProvider:!0,column:c});break;case"delete-column":l.exec("delete-column",{skipProvider:!0,id:c.id});break;case"update-column":l.exec("update-column",{skipProvider:!0,id:c.id,column:c});break;case"move-column":l.exec("move-column",{skipProvider:!0,id:c.id,before:e(f.before,bn)})}}function o(f){const c=n(f.row);switch(f.type){case"add-row":l.exec("add-row",{skipProvider:!0,row:c});break;case"delete-row":l.exec("delete-row",{skipProvider:!0,id:c.id});break;case"update-row":l.exec("update-row",{skipProvider:!0,id:c.id,row:c});break;case"move-row":l.exec("move-row",{skipProvider:!0,id:c.id,before:e(f.before,gn)})}}function u(f){const c=s(f.link);switch(f.type){case"add-link":l.exec("add-link",{skipProvider:!0,id:c.id,link:c});break;case"delete-link":l.exec("delete-link",{skipProvider:!0,id:c.id})}}return{cards:r,columns:a,rows:o,links:u}}class Jb{constructor(e){const{url:t,token:n}=e;this._url=t,this._token=n,this._mode=1,this._seed=1,this._queue=[],this.data={},this.api={},this._events={}}headers(){return{Accept:"application/json","Content-Type":"application/json","Remote-Token":this._token}}fetch(e,t){const n={credentials:"include",headers:this.headers()};return t&&(n.method="POST",n.body=t),fetch(e,n).then(i=>i.json())}load(e){return e&&(this._url=e),this.fetch(this._url).then(t=>this.parse(t))}parse(e){const{key:t,websocket:n}=e;t&&(this._token=e.key);for(const i in e.data)this.data[i]=e.data[i];for(const i in e.api){const s=this.api[i]={},r=e.api[i];for(const a in r)s[a]=this._wrapper(i+"."+a)}return n&&this.connect(),this}connect(){const e=this._socket;e&&(this._socket=null,e.onclose=function(){},e.close()),this._mode=2,this._socket=function(t,n,i,s){let r=n;r[0]==="/"&&(r=document.location.protocol+"//"+document.location.host+n),r=r.replace(/^http(s|):/,"ws$1:");const a=r.indexOf("?")!=-1?"&":"?";r=`${r}${a}token=${i}&ws=1`;const o=new WebSocket(r);return o.onclose=()=>setTimeout(()=>t.connect(),2e3),o.onmessage=u=>{const f=JSON.parse(u.data);switch(f.action){case"result":t.result(f.body,[]);break;case"event":t.fire(f.body.name,f.body.value);break;case"start":s();break;default:t.onError(f.data)}},o}(this,this._url,this._token,()=>(this._mode=3,this._send(),this._resubscribe(),this))}_wrapper(e){return function(){const t=[].slice.call(arguments);let n=null;const i=new Promise((s,r)=>{n={data:{id:this._uid(),name:e,args:t},status:1,resolve:s,reject:r},this._queue.push(n)});return this.onCall(n,i),this._mode===3?this._send(n):setTimeout(()=>this._send(),1),i}.bind(this)}_uid(){return(this._seed++).toString()}_send(e){if(this._mode==2)return void setTimeout(()=>this._send(),100);const t=e?[e]:this._queue.filter(i=>i.status===1);if(!t.length)return;const n=t.map(i=>(i.status=2,i.data));this._mode!==3?this.fetch(this._url,JSON.stringify(n)).catch(i=>this.onError(i)).then(i=>this.result(i,n)):this._socket.send(JSON.stringify({action:"call",body:n}))}result(e,t){const n={};if(e)for(let i=0;i<e.length;i++)n[e[i].id]=e[i];else for(let i=0;i<t.length;i++)n[t[i].id]={id:t[i].id,error:"Network Error",data:null};for(let i=this._queue.length-1;i>=0;i--){const s=this._queue[i],r=n[s.data.id];r&&(this.onResponse(s,r),r.error?s.reject(r.error):s.resolve(r.data),this._queue.splice(i,1))}}on(e,t){const n=this._uid();let i=this._events[e];const s=!!i;return s||(i=this._events[e]=[]),i.push({id:n,handler:t}),s||this._mode!=3||this._socket.send(JSON.stringify({action:"subscribe",name:e})),{name:e,id:n}}_resubscribe(){if(this._mode==3)for(const e in this._events)this._socket.send(JSON.stringify({action:"subscribe",name:e}))}detach(e){if(!e){if(this._mode==3)for(const s in this._events)this._socket.send(JSON.stringify({action:"unsubscribe",key:s}));return void(this._events={})}const{id:t,name:n}=e,i=this._events[n];if(i){const s=i.filter(r=>r.id!=t);s.length?this._events[n]=s:(delete this._events[n],this._mode==3&&this._socket.send(JSON.stringify({action:"unsubscribe",name:n})))}}fire(e,t){const n=this._events[e];if(n)for(let i=0;i<n.length;i++)n[i].handler(t)}onError(e){return null}onCall(e,t){}onResponse(e,t){}}class Qb{_remote;_ready;constructor(e,t){const n=new Jb({url:e,token:t});n.fetch=function(i,s){const r={headers:this.headers()};return s&&(r.method="POST",r.body=s),fetch(i,r).then(a=>a.json())},this._ready=n.load().then(i=>this._remote=i)}ready(){return this._ready}on(e,t){this.ready().then(n=>{if(typeof e=="string")n.on(e,t);else for(const i in e)n.on(i,e[i])})}}return Be.Editor=Ng,Be.Kanban=Eg,Be.RemoteEvents=Qb,Be.RestDataProvider=Yb,Be.Toolbar=jb,Be.defaultCardShape=jt,Be.defaultEditorConfig=Dt,Be.defaultEditorShape=zn,Be.getDefaultCardMenuItems=ji,Be.getDefaultColumnMenuItems=Ui,Be.getDefaultRowMenuItems=Vi,Be.kanbanUpdates=Gb,Be.locateID=Vt,Be.tempID=tn,Be.template=Lg,Object.defineProperty(Be,Symbol.toStringTag,{value:"Module"}),Be}({});
      