.copy-bom-detail {
  max-height: 1050px;
  overflow-y: auto;
  display: block;
  font-size: 14px;

  .table {
    width: 100%;
    border: 1px solid #E9ECEF;
    border-radius: 6px;
    display: block;
  }

  .table-head {
    background-color: #F1F3F5;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 2;
  }

  .table-row {
    display: flex;
    align-items: center;
    min-height: 45px;
    border-bottom: 1px solid #E9ECEF;

    &:nth-child(odd) {
      background-color: rgba(#E9ECEF, 0.05);
    }

    &:hover {
      background-color: #F1F3F5;
      transition: background 0.2s ease-in-out;
      box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
    }
  }

  .th,
  .td {
    flex: 1;
    padding: 10px 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 13px;
    font-weight: 400;
    max-width: 200px;

    &.center {
      text-align: center;
    }

    &.narrow {
      flex: 0 0 100px;
    }

    img {
      width: 17px;
      height: 17px;
      margin: 0 3px;
    }
  }

  .th {
    font-weight: 600;
    font-size: 14px;
    background-color: #F1F3F5;
  }

  /* Responsive Breakpoints */
  @media screen and (max-width: 1366px) {
    .th, .td {
      font-size: 12px;
      padding: 8px 10px;
    }

    .table-row {
      min-height: 40px;
    }
  }

  @media screen and (max-width: 1200px) {
    .th, .td {
      font-size: 11px;
      padding: 6px 8px;
    }
  }

  @media screen and (max-width: 1024px) {
    .th, .td {
      font-size: 10px;
      padding: 5px 6px;
    }
  }

  @media screen and (max-width: 663px) {
    .th, .td {
      font-size: 9px;
      padding: 4px 5px;
    }
  }
}
