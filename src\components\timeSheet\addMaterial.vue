<template>
    <div>
        <div class="bom-detail-item dtx-table header">
        <table>
          <thead v-if="selectedMaterial.length > 0">
            <tr>
              <th>Material ID</th>
              <th>Quantity</th>
              <th>Type</th>
            </tr>
          </thead>
          <tbody  v-for="(material, index) in selectedMaterial" :key="material.id">
            <tr v-if="material.tag !== 'delete'"
            >
              <td>{{ material.material_name }}</td>
              <td>
                <input
                  type="number"
                  v-model="material.quantity"
                  @blur="onChangeQuantity($event, index)"
                  :disabled="disableEdit"
                  @keydown="changeNumber"
                />
              </td>
              <td>
                <select class="custom-list-select" v-model="material.type" @blur="addType($event, index)" >
                <option v-for="type in types" :value="type.id" :key="type.id" :disabled="disableEdit">{{ type.name }}</option>
              </select>
            </td>
              <td v-if="!disableEdit">
                <div class="action" @click="removeMaterial(material, index)">
                  <img class="pointer" src="~@/assets/images/close.png" width="16px" alt="" />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
        <div class="material" v-if="!disableEdit" >
            <span class="timeSheet-modal-content-select w-100 elipsis-text" v-if="selectMaterialData.length > 0">
                <span class=" v-center pointer" v-if="!materialEditMode" @click="materialEditMode= !materialEditMode">
                <img src="~@/assets/images/icons/add-new-icon.svg" alt="" width="20px"/>
                Add Material
            </span>
                <select v-else-if="materialEditMode"  @change="addTaskMaterial($event)">
                    <option value="">Not Selected</option>
                    <option v-for="material in selectMaterialData" :value="material.material_id" :key="material.material_id">{{ material.material_name }}</option>

                </select> </span>
        </div>
    </div>
</template>
<script>
import { restrictKeys } from '@/utils/validations'

export default {
  props: {
    filterMaterial: { type: Object },
    existingMaterials: { type: Object },
    attachedMaterialData: { type: Array },
    disableEdit: { type: Boolean }
  },
  data: () => ({
    materialData: null,
    selectedMaterial: [],
    materialEditMode: false,
    types: [{ id: 1, name: 'production' }, { id: 2, name: 'consumption' }]
  }),
  methods: {
    changeNumber (event) {
      if (['e', 'E', '+', '-'].includes(event.key)) {
        restrictKeys(event)
      }
    },
    addTaskMaterial (e) {
      for (const data of this.selectMaterialData) {
        if (e.target.value === data.material_id) {
          this.selectedMaterial.push({
            material_id: data.material_id,
            material_name: data?.material_name,
            quantity: data?.quantity,
            tag: 'new',
            type: 1
          })
        }
      }
      this.$emit('addMaterial', this.selectedMaterial)
    },
    addType (e, index) {
      this.selectedMaterial[index].type = e.target.value
      if (this.selectedMaterial[index].tag === 'none') {
        this.selectedMaterial[index].tag = 'update'
      }
      this.$emit('addMaterial', this.selectedMaterial)
    },
    onChangeQuantity (e, index) {
      const newQuantity = e.target.value
      this.selectedMaterial[index].quantity = newQuantity
      if (this.selectedMaterial[index].tag === 'none') {
        this.selectedMaterial[index].tag = 'update'
      }
      this.$emit('addMaterial', this.selectedMaterial)
    },
    removeMaterial (material, index) {
      this.$set(this.selectedMaterial[index], 'tag', 'delete')
      this.$emit('addMaterial', this.selectedMaterial)
    },
    fetchMaterialData () {
      this.materialData = JSON.parse(JSON.stringify(this.filterMaterial))
      if (this.existingMaterials.timesheet_material_logs) {
        this.existingMaterials.timesheet_material_logs.forEach((item) => {
          this.selectedMaterial.push({
            material_id: item.material_id,
            material_name: item?.core_material_master.material_name,
            quantity: item?.quantity,
            type: item.type,
            tag: 'none'
          })
        })
      }
      if (this.existingMaterials.materialData) {
        this.existingMaterials.materialData.forEach((item) => {
          if (!this.selectedMaterial.some(mat => mat.material_id === item.material_id)) {
            this.selectedMaterial.push({
              material_id: item.material_id,
              material_name: item?.material_name,
              quantity: item?.quantity,
              type: item.type,
              tag: item.tag
            })
          }
        })
      }
    }
  },
  computed: {
    selectMaterialData () {
      const materialMap = {}
      if (this.materialData) {
        const materialIdsSet = new Set()
        this.selectedMaterial.forEach((element) => {
          if (!element.tag || element.tag !== 'delete') { materialIdsSet.add(element.material_id) }
        })
        this.materialData.data.bom_items.forEach((item) => {
          if (!materialIdsSet.has(item.material_id)) {
            if (materialMap[item.material_id]) {
              materialMap[item.material_id].quantity += item.quantity
            } else {
              materialMap[item.material_id] = {
                material_id: item.material_id,
                quantity: item.quantity,
                material_name: item?.core_material.material_name
              }
              materialIdsSet.add(item.material_id)
            }
          }
        })
      }
      return Object.values(materialMap)
    }
  },
  created () {
    // Call a method to fetch the latest data when the component is created
    this.fetchMaterialData()
  }
}
</script>
<style lang="scss">
.grid-1-2 {
    display: grid;
    grid-template-columns: 2fr 3fr;
    grid-gap: 3px;
    align-items: center;
}
.header{
    padding-left: 228px;
    padding-top: 10px;
}
.custom-list-select {
  margin-right: auto;
  font-size: 12px;
  background-color: var(--brand-light-color);
  line-height: 1;
  border: 1px solid var(--brand-color);
  border-radius: 4px;
  padding: 2px 1px;
  }
  .material {
    padding-left: 226px;
    padding-top: 10px;
  }
</style>
