import { GQL } from '../graphQl'

export const timeSheetDataBasedOnWeek = () => GQL`query timeSheetDataBasedOnWeek($startingDate: timestamptz, $endingDate: timestamptz,$userId:uuid) {
    user_timesheet(
      where: {_and: [{entry_date: {_gte: $startingDate}}, {entry_date: {_lte: $endingDate}},{user_id:{_eq:$userId}},{core_project: {name: {_is_null: false}}}]}    ) {
      id
      entry_date
      duration
      description
      approved
      is_absent
      is_holiday
      project_id
      tag_id
      task_id
      tenant_id
      time_end
      time_start
      user_id
      rework
      time_remaining
      projected_end_date
      core_project {
        name
      }
      timesheet_material_logs {
        type
        material_id
        quantity
        core_material_master{
          id
          material_name
          product_code
        }
      }
    }
  }
  `
export const insertTimeSheetDataQuery = () => GQL`mutation insertTimeSheetDataQuery($inputsheetData:[user_timesheet_insert_input!]!){
  insert_user_timesheet( objects:$inputsheetData) {
    affected_rows
  }
}
`
export const updateTimeSheetDataQuery = () => GQL`mutation updateTimeSheetDataQuery(
  $duration: float8 ,
   $description: String ,
    $entryDate: timestamptz,
     $isAbsent: Boolean ,
      $isHoliday: Boolean,
       $id: uuid!,
       $tagId:uuid,
       $taskId:uuid,
       $rework:Boolean, 
      $timeRemaining:float8,
      $projectedEndDate:timestamptz


    ) {
  update_user_timesheet_by_pk(
    pk_columns: {id: $id}
    _set: {duration: $duration, description: $description, entry_date: $entryDate,is_holiday:$isHoliday,is_absent:$isAbsent,tag_id:$tagId,task_id:$taskId,rework: $rework, time_remaining: $timeRemaining,projected_end_date:$projectedEndDate}
  ){
    id
    user_id
  }
}
`
export const getTaskDataFromProjectQuery = () => GQL`query getTaskDataFromProjectQuery($projectId: uuid, $userId: uuid) {
  core_tasks(where: {project_id: {_eq: $projectId}, task_assignees: {user_id: {_eq: $userId}}, type: {_eq: 1}}) {
    id
    name
    progress
    project_id
    duration
    parent_task_id
    task_material_associations : associated_links_by_source_id(where:{target_feature_id:{_eq: 2}}) {
      metadata
      target_bom {
        bom_versions(where: {active: {_eq: true}}) {
          id
          bom_items {
            material_id
            core_material {
              material_name
              type
            }
            quantity
          }
        }
      }
    }
  }
}`

// this is for getting unique project ids used in last week
export const getlastweekUsedProjectIdsQuery = () => GQL`query getlastweekUsedProjectIdsQuery($userId: uuid, $startDate: timestamptz, $endDate: timestamptz) {
  user_timesheet_aggregate(
    where: {user_id: {_eq: $userId}, _and: {entry_date: {_gte: $startDate, _lte: $endDate}}}
    distinct_on: project_id
  ) {
    nodes {
      core_project {
        name
        id
      }
    }
  }
}
`
// soft delete time sheet entries
// export const removeTimeSheetEntriesMutation = () => GQL`mutation removeTimeSheetEntries($removedEntries: [Int!]) {
//   update_user_timesheet(where: {id: {_in: $removedEntries}}, _set: {deleted: true})
//   {
//     affected_rows
// }
// }
// `
// hard delete time sheet entries
export const removeTimeSheetEntriesMutation = () => GQL`mutation removeTimeSheetEntriesMutation($removedEntries: [uuid!] = 10) {
  delete_user_timesheet(where: {id: {_in: $removedEntries}}) {
    affected_rows
  }
}
`
export const totalTimeSpentInTaskQuery = () => GQL`query totalTimeSpentInTaskQuery($taskId: uuid ) {
  user_timesheet_aggregate(where: {task_id: {_eq: $taskId}}) {
    aggregate {
      sum {
        duration
      }
    }
  }
}`
export const approveTimesheetQuery = () => GQL`mutation approveTimesheetQuery($endDate: Date! , $projectId: uuid! , $startDate: Date! ,  $userIds: [uuid!] ) {
  approve_timesheet(
    end_date: $endDate
    project_id: $projectId
    start_date: $startDate
    user_ids: $userIds
  ) {
    message
  }
}`
export const insertMaterialDataQuery = () => GQL`mutation insertMaterialDataQuery($inputMaterialData:[timesheet_material_log_insert_input!]!){
  insert_timesheet_material_log( objects:$inputMaterialData) {
    affected_rows
  }
}
`
export const UpdateMaterialQuery = () => GQL`mutation UpdateMaterialQuery($materialId: uuid!, $quantity: Int!, $type: Int!, $timesheetId: uuid!) {
  update_timesheet_material_log(
    _set: {quantity: $quantity, type: $type}
    where: {material_id: {_eq: $materialId}, timesheet_id: {_eq: $timesheetId}}
  ) {
    affected_rows
  }
}
`
export const DeleteMaterialQuery = () => GQL`mutation DeleteMaterialQuery($material_id: uuid!, $timesheet_id: uuid!) {
  delete_timesheet_material_log(
    where: {material_id: {_eq: $material_id}, timesheet_id: {_eq: $timesheet_id}}
  ) {
    affected_rows
  }
}
`
