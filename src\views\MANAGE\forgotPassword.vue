<template>
  <section class="forgot-password text v-center column p-5">
    <template v-if="isToken">
      <h1 class="py-6">Reset Password</h1>
    </template>
    <template v-else>
      <h1>Trouble logging in?</h1>
      <p class="py-5">Enter your email and we'll send you a link to get back into your account</p>
    </template>
    <div class="forgot-password-group bg py-5 px-20 v-center column">
      <img class="my-10" src="~@/assets/images/brand-transparent.png" alt="">
      <div v-if="isToken">
        <div class="input-group imp m my-4" data-validation="Password">
          <label>Enter your new password</label>
          <input v-model="newPassword" placeholder="Enter your Password" type="password">
        </div>
        <div class="input-group imp m my-4" data-validation="ConfirmPassword">
          <label>Confirm your password</label>
          <input v-model="confirmPassword" placeholder="confirm your Password" type="password">
        </div>
        <div class="flex-end my-4 l">
          <button @click="tryResetPassword" class="btn">Confirm</button>
        </div>
      </div>
      <div v-else>
        <div class="input-group imp m my-4">
          <label>Enter your Email</label>
          <input v-model="email" placeholder="Enter your email" type="text">
        </div>
        <div class="flex-end my-4 l">
          <button @click="sendEmail" class="btn">Send Link</button>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import Config from '../../config'
import Loader from '@/plugins/loader'
import signupValidation from '@/helper/formValidation/signupValidation.js'
import { forgotPassword, resetPassword } from '../../api/apis/forgotPassword'
export default {
  name: 'Forgot Password Page',
  data () {
    return {
      email: '',
      isToken: false,
      newPassword: '',
      confirmPassword: ''
    }
  },
  methods: {
    sendEmail () {
      const loader = new Loader()
      loader.show()
      forgotPassword(this.email).then((res) => {
        loader.hide()
        if (res.message === 'Reset Password Email Sent') {
          this.$notify.success(res.message)
        } else {
          this.$notify.alert(res.message)
        }
      }).catch(() => {
        this.$notify.alert('Something went wrong')
      })
    },
    tryResetPassword () {
      const loader = new Loader()
      loader.show()
      const body = { password: this.newPassword, confirmPassword: this.confirmPassword }
      if (signupValidation(body)) {
        resetPassword(this.newPassword).then((res) => {
          if (res.message === 'Password Updated Successfully') {
            localStorage.removeItem(Config.localstorageKeys.AUTH)
            this.$router.push('/login')
          } else {
            this.$notify.alert(res.message)
          }
        }).catch(() => {
          this.$notify.alert('Something went wrong')
        }).finally(() => {
          loader.hide()
        })
      } else {
        loader.hide()
      }
    }
  },
  mounted () {
    if (this.$route.query?.token) {
      localStorage.setItem(Config.localstorageKeys.AUTH, this.$route.query?.token)
      this.isToken = true
    }
  }
}
</script>

<style lang="scss" scoped >
.forgot-password {
  h1 {
    font-size: 36px;
    font-weight: 500;
  }

  &-group {
    max-width: 700px;
    width: 100%;
    border-radius: 4px;
    min-height: calc(100vh - 300px);

    &>div {
      width: 100%;

      button {
        width: 140px;
      }
    }
  }
}
</style>
