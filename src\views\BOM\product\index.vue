<template>
  <div class="bom">
    <div class="bom-bar v-center space-between px-3">
      <h1 class="weight-500 xxl">Bill of Materials</h1>
      <div class="bom-action m flex">
        <create-bom v-if="showCreateBom"
         @updatebomlist="updatebomlist"
         :bomlistLength="bomList?.length" />
        <button
          :disabled="!selectedProductCode || bomList.length === 0 "
          @click="gotoBomCompare"
          class="btn btn-black mr-2"
        >
          Compare BOM
        </button>
        <button
          :disabled="!canCompare"
          @click="gotoVersionCompare"
          class="btn btn-black mr-2"
        >
          Compare BOM Version
        </button>
      </div>
    </div>
    <div class="bom-container">
      <transition name="slide">
      <div v-show="!showProductCode" class="bom-product-code">
        <product-code-list />
      </div>
    </transition>
      <div :class="showProductCode ? 'bom-full-width':'bom-content'">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
import productCodeList from '@/components/bom/product/productCodeList.vue'
import CreateBom from '../../../components/bom/common/createBom.vue'
import { mapGetters } from 'vuex'
import { GetAllBomVersionListByBomId } from '@/api'
export default {
  components: { productCodeList, CreateBom },
  data () {
    return {
      canCompare: false
    }
  },
  computed: {
    ...mapGetters('productBom', ['selectedBomId', 'selectedProductCode', 'bomList']),
    ...mapGetters(['user', 'collaborator']),
    showProductCode () {
      return ((this.$route.path.includes('/bom') && this.$route.path.includes('/edit')) || (this.$route.path.includes('/bom') && this.$route.path.includes('/new')))
    },
    showCreateBom () {
      return ((this.user.tenantLevelRole === 'ADMIN' ||
      this.user.tenantLevelRole === 'COLLABORATOR') && !this.collaborator)
    }
  },
  methods: {
    async checkBomCompare () {
      if (this.selectedBomId) {
        const res = await GetAllBomVersionListByBomId(this.selectedBomId)
        this.canCompare = res.bom_versions[0].version_no > 1
      }
    },
    gotoVersionCompare () {
      this.$router.push(`/bom/compare/${this.selectedBomId}`)
    },
    gotoBomCompare () {
      this.$router.push(`/bom/compare/product/${this.selectedProductCode}`)
    },
    setBaseData () {
      const { productCode, bomId } = this.$route.params
      this.$store.dispatch('productBom/setBaseData', {
        product_code: productCode || null,
        bom_id: bomId || null
      })
    },
    updatebomlist () {
      this.$store.dispatch('productBom/getAllBomList')
    }
  },
  mounted () {
    console.log('mounted')
    this.checkBomCompare()
  },
  watch: {
    selectedBomId (newVal) {
      if (newVal) {
        this.checkBomCompare()
      }
    },
    '$route.params.productCode' () {
      this.setBaseData()
    },
    '$route.params.bomId' (val) {
      this.setBaseData()
    },
    '$route.query.bomVersionId' (val) {
      this.$store.dispatch('productBom/bomVersionIdChanged', +val || null)
    }
  },
  created () {
    this.$store.dispatch('productBom/getAllProductCode')
    this.setBaseData()
  }
}
</script>

<style lang="scss" scoped >
.slide-enter-active {
  transition: transform 0.5s ease-out;
}
.slide-enter {
  transform: translateX(100%);
}

/* Sliding transition styles for leaving */
.slide-leave-active {
  transition: transform 0.5s ease-out;
}
.slide-leave-to /* .slide-leave-active below version 2.1.8 */ {
  transform: translateX(-100%);
}

.bom {
  height: 100%;
  &-bar {
    height: 60px;
    margin: -12px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  &-container {
    height: calc(100% - 60px);
    display: flex;
    padding-top: 10px;
    & > div {
      height: 100%;
    }
  }
  &-product-code {
    width: 20%;
    min-width: 20rem;
    @media screen and (max-width: 1366px) {
      width: 18%;
      min-width: 10rem;
    }
  }
  &-content {
    width: 85%;
    padding-left: 10px;
    overflow-y: auto;
  }
  &-full-width {
    width: 100%;
    padding-left: 10px;
  }
}
</style>
