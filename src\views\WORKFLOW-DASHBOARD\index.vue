<template>
  <div class="task-carousel">
    <!-- HEADER SECTION -->
    <div class="header-section">
      <div class="xxxl weight-600">Workflow Dashboard</div>
      <!-- <div class="right-controls">
        <input type="text" placeholder="Search..." class="search-input" />
        <button class="btn btn-black">+ New Task</button>
      </div> -->
    </div>

    <div class="vh vw" v-if="loading">
      <loading-circle />
    </div>

    <!-- CAROUSEL TABLES -->
    <div v-else>
      <div
        v-for="(group, index) in taskGroups"
        :key="index"
        class="task-group"
      >
        <div class="header" @click="toggle(index)">
          <div class="weight-500 xxl">{{ group.name }}</div>
          <span>{{ expanded[index] ? '−' : '+' }}</span>
        </div>

        <div v-if="expanded[index]" class="table-wrapper">
          <table>
            <thead>
              <tr>
                <th>Workflow</th>
                <th>Step Name</th>
                <th>Planned Start Date</th>
                <th>Status</th>
                <th>Due On</th>
                <th>Link</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(task, i) in group.tasks" :key="i">
                <td>{{ task.description }}</td>
                <td>{{ task.name }}</td>
                <td>{{ task.estimation | genericFormatDate }}</td>
                <td>
                  <span
                    :class="['status', getStatusClass(task.dueOn)]"
                  >
                    {{ getStatusLabel(task.dueOn) }}
                  </span>
                </td>
                <td>{{ task.dueOn | genericFormatDate }}</td>
                <td>
                  <img @click="goTo(task.link)" class="edit url pointer" v-tooltip="'URL'" src="~@/assets/images/url.svg" alt=""/>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import loadingCircle from '@/components/common/loadingCircle.vue'
import { genericFormatDate } from '@/utils/date'
import { getWorkflowDashbordData } from '@/api'
import { mapGetters } from 'vuex'

export default {
  name: 'WorkflowDashboard',
  components: {
    loadingCircle
  },
  filters: {
    genericFormatDate
  },
  data () {
    return {
      loading: false,
      taskGroups: [
        {
          name: 'Actionable Workflows',
          tasks: []
        },
        {
          name: 'Upcoming This Week',
          task: []
        }
      ],
      expanded: [true, false, false]
    }
  },
  computed: {
    ...mapGetters(['user', 'isOnProjectLevel'])
  },
  mounted () {
    this.fetchDashboardData()
    this.upcomingDashboardData()
  },
  methods: {
    goTo (link) {
      this.$router.push(link)
    },
    toggle (index) {
      this.$set(this.expanded, index, !this.expanded[index])
    },
    fetchDashboardData () {
      this.loading = true
      getWorkflowDashbordData(this.user?.userId, 'actionable', this.isOnProjectLevel).then(res => {
        const apiData = res?.user_steps_view || []

        const mappedTasks = apiData.map(item => ({
          name: item?.step_name || '',
          description: item?.workflow_name || '',
          estimation: item?.planned_start_date || '',
          type: '',
          dueOn: item?.due_date || '',
          assignees: '',
          link: `/form/viewform/${item?.core_form?.template_version?.core_form_template?.id}/${item?.core_form?.template_version?.core_form_template?.name}/${item?.core_form?.id}`
        }))

        this.taskGroups[0].tasks = mappedTasks
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    upcomingDashboardData () {
      this.loading = true
      getWorkflowDashbordData(this.user?.userId, 'upcoming').then(res => {
        const apiData = res?.user_steps_view || []

        const mappedTasks = apiData.map(item => ({
          name: item?.step_name || '',
          description: item?.workflow_name || '',
          estimation: item?.planned_start_date || '',
          type: '',
          dueOn: item?.due_date || '',
          assignees: '',
          link: `/form/viewform/${item?.core_form?.template_version?.core_form_template?.id}/${item?.core_form?.template_version?.core_form_template?.name}/${item?.core_form?.id}`
        }))

        this.taskGroups[1].tasks = mappedTasks
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    getStatusLabel (dueDate) {
      return new Date(dueDate) < new Date() ? 'Overdue' : 'Open'
    },
    getStatusClass (dueDate) {
      return new Date(dueDate) < new Date() ? 'status-inactive' : 'status-active'
    }
  }
}
</script>

<style scoped>
/* HEADER SECTION */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 10%;
  padding: 1rem 0;
}

.right-controls {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.search-input {
  padding: 0.4rem 0.75rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 14px;
}

/* TASK CAROUSEL STYLES */
.task-group {
  border: 1px solid #ccc;
  border-radius: 8px;
  margin-bottom: 1rem;
  overflow: hidden;
}

.header {
  background: #f5f5f5;
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}

.table-wrapper {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
  font-size: 15px;
  table-layout: auto;
}

th, td {
  border: 1px solid #ddd;
  padding: 0.75rem;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

th {
  font-size: 18px;
  font-weight: 600;
}

tbody th {
  height: 60px;
}
tbody tr {
  height: 60px;
}

th:nth-child(1) { width: 25%; }
th:nth-child(2) { width: 25%; }
th:nth-child(3) { width: 15%; }
th:nth-child(4) { width: 10%; }
th:nth-child(5) { width: 10%; }
th:nth-child(6) { width: 10%; }

/* STATUS STYLES */
.status {
  font-size: 13px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: fit-content;
  height: fit-content;
  padding: 3px 8px;
  border-radius: 3px;
  font-weight: 500;
}
.url {
  width: 20px;
  height: 20px;
}

.status-active {
  background-color: #d4edda; /* light green */
  color: #155724;
}

.status-inactive {
  background-color: #ffcccc; /* light red */
  color: #d60000;
}

/* LINK CELL STYLING */
.link-blue {
  color: #1976d2;
  cursor: pointer;
}
.link-blue:hover {
  text-decoration: underline;
}
</style>
