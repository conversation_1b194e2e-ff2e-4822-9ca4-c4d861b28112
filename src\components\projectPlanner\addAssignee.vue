<template>
  <div>
    <div class="container">
      <form>
      <div>
        <div class="grid-2 mt-2"  >
          <select class="tenant-list" v-model="selectedTenant">
            <option v-for="tenant in tenantsListToShare" :key="tenant.id" :value="tenant.id">
              {{ tenant.name }}
            </option>
          </select>
          <div class="input-group  add-user__search add-user__body" ref="addUserBody">
          <div class=" add-user__search_box">
            <input
              placeholder="Search for users"
              type="text"
              v-model="searchKeyword"
              @click="openPopup = true"
            />
            <div v-if="searchKeyword" class="add-user__search_close v-center h-center">
              <img   c src="~@/assets/images/icons/close-icon.svg" width="20px" @click="searchKeyword=''" alt="">
            </div>
          </div>
          <div class="add-user__list" v-if="openPopup" >
            <div class="add-user__list-no-result" v-if="!nonAddedUsers.length">
              No users found
            </div>
            <div
              class="add-user__list-item"
              v-for="user in nonAddedUsers"
              :key="user.associated_user.id"
              tabindex="0"
              @click="updateSelectedUsers(user.associated_user)"
            >
              <div class="add-user__list-item__avatar">
                <avatar :user="user.associated_user" size="24px" />
              </div>
              <div class="add-user__list-item__name">
                {{ user.associated_user.first_name }}
                {{ user.associated_user.last_name }}
              </div>
              <div class="add-user__list-item__action"></div>
            </div>
          </div>
        </div>
        </div>
        <div class="add-user__selected mt-4 ">
              <div
                class="assignees-box"
                v-for="(user, index) in selectedAssignees"
                :key="user.id"
              >
                <div class="add-user__list-item__avatar">
                  <avatar :user="user" size="24px" />
                </div>
                <div class="add-user__list-item__name">
                  {{ user.first_name }}
                  {{ user.last_name }}
                </div>
                <img
                  class="pointer ml-1"
                  @click="removeAssignee(index,user)"
                  src="~@/assets/images/icons/close-icon.svg"
                  width="16px"
                  alt=""
                />
              </div>
            </div>
      </div>
    </form>
    </div>
    <div class="gap-1 flex-end">
      <button class="btn btn-black" @click="$emit('close')">Cancel</button>
      <button class="btn" @click="$emit('update-and-close', taskData)" v-if="showAddAssignee">Save</button>
    </div>
  </div>
</template>

<script>
import { GetAllCollabaratorList } from '@/api'
import { mapGetters } from 'vuex'
import Avatar from '../common/avatar.vue'
import { alert } from '@/plugins/notification'

export default {
  name: 'AddAssigneeBody',
  props: {
    task: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      tenantsListToShare: [],
      selectedTenant: '',
      openPopup: false,
      selectedAssignees: [],
      searchKeyword: '',
      taskData: {
        task_assignees: []
      }
    }
  },
  components: { Avatar },
  mounted () {
    this.$el.addEventListener('click', this.handleOutsideClick)
    this.tenantList.forEach((item) => {
      if (item.id === this.openTenantId) {
        this.tenantsListToShare.push({
          id: item.id,
          name: item.company_name,
          collaborator: false
        })
      }
    })
    this.selectedTenant = this.tenantsListToShare[0]?.id
    GetAllCollabaratorList().then((res) => {
      res.tenant_company_association.map((item) => {
        this.tenantsListToShare.push({
          id: item?.target_tenant?.id,
          name: item?.target_tenant?.company_name,
          collaborator: true,
          users: item?.target_tenant?.tenant_users
        })
      })
    }).catch((err) => {
      console.log(err)
    })
    const task = JSON.parse(JSON.stringify(this.$props.task))
    // while mounting the component setting assigned users into selectedAssignees and submission data (taskData)
    if (!Array.isArray(task.task_assignees)) {
      task.task_assignees = []
    }
    for (const element of task.task_assignees) {
      if (element?.status !== 'deleted') {
        this.selectedAssignees.push({
          id: element?.user_id ?? element?.id,
          first_name: element?.assignee?.first_name ?? element?.first_name,
          last_name: element?.assignee?.last_name ?? element?.last_name,
          status: element?.status ?? null
        })
      }
    }
    this.taskData.task_assignees = task?.task_assignees?.map((element) => {
      return {
        id: element?.id,
        first_name: element?.first_name,
        last_name: element?.last_name,
        status: element?.status ?? null
      }
    })
  },
  methods: {
    updateSelectedUsers (user) {
      let flag = false
      this.selectedAssignees.forEach((element, index) => {
        if (element?.id === user.id) {
          flag = true
        }
      })
      if (!flag) {
        const existingRemovedUser = this.$props.task.task_assignees.find(assigneeObj => assigneeObj.id === user.id)
        if (existingRemovedUser) {
          this.taskData.task_assignees.forEach(assigneeObj => {
            if (assigneeObj.id === user.id) {
              assigneeObj.status = null
            }
          })
        } else {
          const assigneeObj = { id: user?.id, first_name: user?.first_name, last_name: user?.last_name, status: 'new' }
          if (this.selectedTenant !== this.openTenantId) {
            assigneeObj.tenantId = this.selectedTenant
          }
          this.taskData.task_assignees.push(assigneeObj)
        }
      } else return alert('User already part of task!')
      this.selectedAssignees.push(user)
      this.openPopup = false
      this.searchKeyword = ''
    },
    removeAssignee (index, user) {
      let assigneeIndex = -1
      this.taskData.task_assignees.forEach((element, index) => {
        if (element?.id === user?.id) {
          if (element.status === 'new') {
            assigneeIndex = index // if it is a newly added assignee no need to remove from the backend
          } else {
            this.taskData.task_assignees[index].status = 'deleted'
          }
        }
      })
      if (assigneeIndex !== -1) {
        this.taskData.task_assignees.splice(assigneeIndex, 1)
      }
      this.selectedAssignees.splice(index, 1)
    }
  },
  computed: {
    ...mapGetters(['tenantUsersList', 'currentProject', 'user', 'tenantList', 'openTenantId']),

    showAddAssignee () {
      return (
        (this.user.projectLevelRole === 'ADMIN' ||
          this.user.projectLevelRole === 'EDITOR' ||
          this.user.projectLevelRole === 'COLLABORATOR')
      )
    },
    nonAddedUsers () {
      if (this.tenantsListToShare[0]?.id !== this.selectedTenant) {
        return this.tenantsListToShare.find((tenant) =>
          tenant.id === this.selectedTenant
        )?.users ?? []
      }
      return this.tenantUsersList
        .filter((user) => {
          return (
            this.currentProject.project_user_associations.find(
              (item) =>
                item.user_id === user.associated_user.id && user.status === 1 && item.role_id !== 4
            ) &&
            !this.selectedAssignees.find(
              (item) => item?.id === user.associated_user.id
            )
          )
        })
        .filter((user) => {
          return (
            user.associated_user.first_name
              .toLowerCase()
              .includes(this.searchKeyword.toLowerCase()) ||
            user.associated_user.last_name
              .toLowerCase()
              .includes(this.searchKeyword.toLowerCase())
          )
        })
    }
  }

}

</script>

<style lang="scss" scoped>

.container {
  min-height: 20vh;
}

.tenant-list {
  padding: 0.5rem;
  border-radius: 0.25rem;
  width: 100%;
  border: 1px solid rgba(59, 59, 59, 0.4666666667);
  background-color: transparent;
}

form {
  width: 40vw;
}

.add-user {
  &__body {
    position: relative;
  }
  &__search {
    &_box{
      position:relative;
    }
  input {
      padding: 0.5rem;
      border-radius: 0.25rem;
      width: 100%;
      margin-right: 10px;
    }
    &_close{
      position: absolute;
      width:30px;
      background-color: var(--bg-color);
      height:90%;
      border-radius: 0.25rem;
    /* left: 0; */
    right: 1px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    }
  }
  &__selected {
display: grid;
grid-template-columns: repeat(3,1fr);
gap:4px
  }
  &__list {
    width: 100%;
    max-height: 11rem;
    overflow-y: auto;
    position: absolute;
    background: white;
    left: 0px;
    right: 0px;
    z-index: 1;
    top: 40px;
    padding: 10px;
    box-shadow: 2px 2px 4px rgb(0 0 0 / 20%);
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    &-no-result {
      padding: 1rem;
      text-align: center;
    }
    &-item {
      display: flex;
      align-items: center;
      padding: 0.5rem 0;
      border-bottom: 1px solid #e8e8e8;
      cursor: pointer;
      &:hover {
        background: #f8f8f8;
      }
      &__avatar {
        margin-right: 1rem;
      }
      &__name {
        flex: 1;
        white-space: nowrap;
      }
      &__action {
        &__btn {
          padding: 0.25rem;
          border: 1px solid #e8e8e8;
          border-radius: 0.25rem;
          background: #fff;
          cursor: pointer;
          i {
            font-size: 1.25rem;
          }
        }
      }
    }
  }
}

.assignees-box{
  display: flex;
  align-items: center;
  background-color: var(--brand-color);
  padding: 0.6rem;
  font-size: small;
  border-radius: 0.4rem;
}
.button-box{
  padding: 10px 5px 5px 0;
  position: sticky;
  width: 100%;
  background-color:var(--bg-color);
  right:10px;
  bottom:0;
  border-top: .1px solid rgb(0, 0,0,.1);
  margin-top: 10px ;
}
</style>
