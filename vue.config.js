// /** @type import('@vue/cli-service').ProjectOptions */
module.exports = {
  devServer: {
    host: '0.0.0.0',
    public: '0.0.0.0:8080',
    disableHostCheck: true
  },
  configureWebpack: {
    devtool: 'source-map'
  }
  // chainWebpack: (config) => {
  //   config.devtool('source-map')
  //   config.module
  //     .rule('vue')
  //     .use('vue-loader')
  //     .loader('vue-loader')
  //     .options({
  //       // ... other options
  //       sourceMap: true
  //     })
  // }
}
