<template>
  <div v-if="loadingBomData" class="bom-diff center">
    <div class="v-center column">
      <h4 class="mb-4">Analyzing BOM</h4>
      <loading-circle />
    </div>
  </div>
  <div v-else-if="canStartCompare && !loadingBomData" class="bom-diff">
    <compare-summary :diffList="diffList" />
    <div class="bom-diff-table flex space-between p-2">
      <div class="bom-diff-table-title">Detail Comparision</div>
      <span class="button-box-dwld-upd">
        <button class="btn" @click="downloadCSV">Download CSV</button>
      <button class="btn" v-if="versionNoRight && compareAndUpdate" @click="UpdateBomVersion">Update to {{ active? 'V-'+versionNoRight+' (Latest)' :'V-'+versionNoRight}}</button>
      </span>
    </div>
    <bom-detail-head class="bom-diff-table-head" :headings="visibleHeadings" :compareBom="true" />
    <item-compare-row
      v-for="item in diffList"
      :key="item.id"
      :diffItem="item"
    />
  </div>
  <div v-else class="bom-diff center">
    <div>
      <h4 class="text-center">
        Please select two different versions to compare
      </h4>
    </div>
  </div>
</template>

<script>
import { GetAllBomItemsByBomVersionId, updateAssociatedBomVersion, getBomDatawithAssociatedBoms } from '@/api'
import { generateComparisonMap } from './comparisionMap'
import CompareSummary from './compareSummary.vue'
import BomDetailHead from '../common/bomDetailHead.vue'
import ItemCompareRow from './itemCompareRow.vue'
import LoadingCircle from '../../common/loadingCircle.vue'
import { warning, alert } from '@/plugins/notification'
import { arrayToCsv } from '@/helper/file/arrayToCsv'
import Loader from '@/plugins/loader'
export default {
  components: { CompareSummary, BomDetailHead, ItemCompareRow, LoadingCircle },
  name: 'CompareBom',
  props: {
    rightVersion: {
      type: [Number, String],
      default: ''
    },
    leftVersion: {
      type: [Number, String],
      default: ''
    },
    versionNoRight: {
      type: [Number, String],
      default: null
    },
    active: {
      type: [Number, String, Boolean],
      default: false
    },
    compareAndUpdate: {
      type: [Number, String, Boolean],
      default: false
    }
  },
  data () {
    return {
      bomItems: [],
      bomItemsFrom: [],
      bomItemsTo: [],
      loadingBomData: false,
      diffList: [],
      materialId: null,
      parentBomId: null,
      unitSize: null,
      parentProdCode: null
    }
  },
  watch: {
    leftVersion () {
      if (this.rightVersion) { this.startCompare() }
    },
    rightVersion () {
      if (this.leftVersion) { this.startCompare() }
    }
  },
  computed: {
    canStartCompare () {
      return (
        this.leftVersion &&
        this.rightVersion &&
        this.leftVersion !== this.rightVersion
      )
    },
    visibleHeadings () {
      return [
        'Product Code',
        'Material ID',
        'PLM ID',
        'ERP ID',
        'UOM',
        'Description',
        'Unit Size',
        'Quantity',
        'Material Name',
        'Lead Time',
        'Material/Resource Group',
        'Cost',
        'Sale Price',
        'Remarks'
      ]
    }
  },
  methods: {
    downloadCSV () {
      const bomItems = this.diffList.map((item) => {
        const bomItem = item.isDeleted ? item.left : item.right
        return {
          Status: item.isAdded
            ? 'Added'
            : item.isDeleted
              ? 'Deleted'
              : item.isUpdated
                ? 'Updated'
                : '',
          'Product Code': bomItem.material_product_code?.product_code || '',
          'Material ID': bomItem.custom_material_id || '',
          'PLM ID': bomItem.plm_material_id,
          'ERP ID': bomItem.plm_record_id,
          UOM: bomItem.material_unit_details.name,
          'Material Description': bomItem.material_description || '',
          'Associated BOM': bomItem.associated_bom_id || '',
          'Unit Size': bomItem.unit_size || '',
          Quantity: bomItem.quantity || '',
          'Material Name': bomItem.material_name || '',
          'Lead Time': bomItem.lead_time,
          'Material/Resource Group': bomItem.material_group_details?.name || bomItem.resource_group_details?.name || '',
          cost: bomItem.unit_cost || '',
          'Sale price': bomItem.total_price || '',
          Remarks: bomItem.remarks || '',
          messages: item.message.join(', ') || ''
        }
      })
      arrayToCsv(bomItems, [], 'bom-diff')
    },
    startCompare () {
      if (this.leftVersion === null || this.rightVersion === null) return
      if (this.leftVersion === this.rightVersion) {
        warning('Please select two different versions.')
        return
      }
      this.loadingBomData = true
      const formApi = GetAllBomItemsByBomVersionId(this.leftVersion)
      const toApi = GetAllBomItemsByBomVersionId(this.rightVersion)
      Promise.all([formApi, toApi])
        .then((res) => {
          this.loadingBomData = false
          const [from, to] = res
          this.bomItemsFrom = from.bom_items.map((item) => {
            return {
              ...(item.core_material || {}),
              material_id: item.material_id,
              quantity: item.quantity,
              total_price: item.total_price,
              unit_size: item.unit_size,

              associated_bom_id:
                item?.associated_bom_version?.core_bom?.id || null,
              associated_bom_name:
                item?.associated_bom_version?.core_bom?.name || null,
              associated_bom_version:
                item?.associated_bom_version?.version_no || null,
              associated_bom_version_id:
                item?.associated_bom_version?.id || null,

              open: false
            }
          })
          this.bomItemsTo = to.bom_items.map((item) => {
            return {
              ...(item.core_material || {}),
              material_id: item.material_id,
              quantity: item.quantity,
              total_price: item.total_price,
              unit_size: item.unit_size,

              associated_bom_id:
                item?.associated_bom_version?.core_bom?.id || null,
              associated_bom_name:
                item?.associated_bom_version?.core_bom?.name || null,
              associated_bom_version:
                item?.associated_bom_version?.version_no || null,
              associated_bom_version_id:
                item?.associated_bom_version?.id || null,

              open: false
            }
          })
          this.diffList = generateComparisonMap(
            this.bomItemsFrom,
            this.bomItemsTo
          )
        })
        .finally(() => {
          this.loadingBomData = false
        })
    },
    UpdateBomVersion () {
      const loader = new Loader()
      loader.show()
      this.checkforCyclicDependency1(this.rightVersion, this.parentProdCode).then((response) => {
        loader.hide()
        if (response) { alert('Bom triggered with cyclic dependency') } else {
          updateAssociatedBomVersion(this.parentBomVersionId, this.materialId, this.unitSize, this.rightVersion).then(res => {
            window.history.back()
          })
        }
      })
    },
    // as per the written logic it will check if a current bom's product code is coming  or not  as a  material in any associated bom
    checkforCyclicDependency1 (bomVersionId, parentProdCode) {
      return new Promise((resolve, reject) => {
        async function checkforCyclicDependency (bomVersionId, index) {
          await getBomDatawithAssociatedBoms(bomVersionId, false).then(async data => {
            for (let i = 0; i < data.bom_items.length; i++) {
              if (data.bom_items[i]?.core_material?.product_code === parentProdCode) {
                resolve(true)
                return true
              } else if (data.bom_items[i]?.associated_bom_version?.id) {
                await checkforCyclicDependency(data.bom_items[i]?.associated_bom_version?.id, index + 1)
              }
            }
            if (index === 1) {
              resolve(false)
            }
          })
        }
        checkforCyclicDependency(bomVersionId, 1)
      })
    }
  },
  created () {
    this.materialId = this.$route?.query?.mtlid
    this.parentBomVersionId = this.$route?.query?.bPvid
    this.unitSize = this.$route?.query?.usize
    // this parentProdCode refers to the  product code id of parent bom for checking cylcic dependency
    this.parentProdCode = this.$route?.query?.prodCode
  }
}
</script>

<style lang="scss" scoped>
.bom-diff {
  width: 100%;
  height: 100%;
  padding: 12px 20px;
  overflow: auto;
  h4 {
    font-size: 24px;
    font-weight: 400;
  }
  .button-box-dwld-upd{
display: flex;
gap: 10px;
  }
  &-table {
    &-title {
      font-size: 18px;
      font-weight: 500;
    }
    &-head {
      top: -12px !important;
    }
  }
}
</style>
