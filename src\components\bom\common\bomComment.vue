<template>
  <div class="bom-comment">
    <button class="comment-btn" v-if="!openComment" @click="openComment = true">
      <img src="~@/assets/images/icons/comment-icon.svg" alt="" />
    </button>
    <div v-else class="bom-comment-panel">
      <div class="bom-comment-panel-header">
        <div class="bom-comment-panel-header--title">Comments</div>
        <img
          src="~@/assets/images/icons/close-icon.svg"
          @click="openComment = false"
          width="20px"
          alt=""
        />
      </div>
      <div class="bom-comment-panel-container">
        <div class="fh fw center" v-if="loading">
          <loading-circle />
        </div>
        <template v-else>
          <comment-card
            v-for="comment in comments"
            :commentData="comment"
            :key="comment.id"
            :level="3"
            @updateComment="getComments"
            :showEdit="showAddComment"
          />
        </template>
      </div>
      <div class="bom-comment-panel-comment-box flex">
        <div
          contenteditable="true"
          class="bom-comment-panel-comment-box--editor"
          @keyup="setMessage"
          ref="editor"
        ></div>
        <spinner class="spinner" v-if="commentSending"></spinner>
        <img
          v-else
          @click="sendComment"
          src="~@/assets/images/icons/send-icon.svg"
          alt=""
        />
      </div>
    </div>
  </div>
</template>

<script>
import { addComments, getAllBomComments } from '@/api'
import loadingCircle from '../../common/loadingCircle.vue'
import commentCard from './bomCommentCard.vue'
import { mapGetters } from 'vuex'
import Spinner from '@/components/common/spinner.vue'

export default {
  components: { commentCard, loadingCircle, Spinner },
  name: 'bom-comment',
  props: ['closeOnOutsideClick'],
  data () {
    return {
      commentSending: false,
      openComment: false,
      comment: '',
      loading: false,
      comments: []
    }
  },
  computed: {
    ...mapGetters(['user', 'isOnProjectLevel', 'collaborator']),
    showAddComment () {
      if (this.collaborator) {
        return true
      } else if (this.isOnProjectLevel) {
        return this.user.projectLevelRole !== 'VIEWER'
      } else {
        return this.user.tenantLevelRole !== 'VIEWER'
      }
    }
  },
  methods: {
    setMessage (e) {
      if (e.keyCode === 13 && e.shiftKey) {
        e.target.innerText += '\n'
        return
      }
      if (e.keyCode === 13) {
        this.sendComment()
        return
      }
      this.comment = e.target.innerText
    },
    sendComment () {
      if (this.commentSending) return
      this.commentSending = true
      if (!this.comment.trim()) {
        this.commentSending = false
        return
      }
      const bomId = this.$route.params.bomId
      const bomVersionId = this.$route.query.bomVersionId
      addComments('bom', bomId, this.comment, null, bomVersionId).finally(
        () => {
          this.comment = ''
          this.$refs.editor.innerText = ''
          this.getComments()
          this.commentSending = false
        }
      )
    },
    getComments () {
      this.loading = true
      const bomId = this.$route.params.bomId
      getAllBomComments(bomId, null)
        .then((res) => {
          this.comments = res.core_comments || []
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleClickOutside (event) {
      if (!this.$el.contains(event.target)) {
        this.openComment = false
      }
    }
  },
  watch: {
    'this.$route.params.bomId' () {
      this.openComment = false
    },
    openComment (val) {
      if (val) {
        this.getComments()
      }
    }
  },
  mounted () {
    if (this.openComment) {
      this.getComments()
    }
    if (this.closeOnOutsideClick) {
      document.addEventListener('mousedown', this.handleClickOutside)
    }
  },
  beforeDestroy () {
    document.removeEventListener('click', this.handleClickOutside)
  }
}
</script>

<style lang="scss" scoped >
.bom-comment {

  .spinner {
    margin-bottom: 4px;
    padding: 4px;
    width: 32px;
    height: 32px;
  }

  &-panel {
    position: fixed;
    bottom: 16px;
    right: 16px;
    width: 400px;
    height: 80vh;
    box-shadow: 0 0px 10px 0 rgba(0, 0, 0, 0.25);
    z-index: 100;
    border-radius: 4px;
    padding: 6px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 6px;
    background-color: var(--bg-color);
    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px;
      margin: -6px;
      margin-bottom: 12px;
      background-color: var(--brand-color);
      &--title {
        font-size: 14px;
        font-weight: 500;
        color: var(--text-color);
      }
      img {
        cursor: pointer;
        opacity: 0.8;
        &:hover {
          opacity: 1;
        }
      }
    }
    &-container {
      flex-grow: 1;
      overflow-y: auto;
      margin-right: -6px;
      padding-right: 6px;
    }
    &-comment-box {
      padding: 10px;
      margin: -6px;
      margin-top: 0px;
      width: calc(100% + 12px);
      background-color: var(--bg-color);
      align-items: flex-end;
      gap: 10px;
      &--editor {
        width: 100%;
        min-height: 40px;
        padding: 10px;
        outline: none;
        border: 1px solid var(--brand-color);
        border-radius: 4px;
        resize: none;
        max-height: 100px;
        overflow: auto;
        font-size: 12px;
        font-weight: 400;
        line-height: 1.2;
        background-color: var(--white);
        color: var(--text-color);
        &:focus {
          outline: none;
        }
      }
      img {
        width: 32px;
        height: 32px;
        cursor: pointer;
        opacity: 0.8;
        margin-bottom: 4px;
        padding: 4px;
        border-radius: 50%;
        &:hover {
          background-color: rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
  .comment-btn {
    position: fixed;
    bottom: 0;
    right: 0;
    margin: 12px;
    background: var(--white);
    cursor: pointer;
    border-radius: 50%;
    height: 45px;
    width: 45px;
    border: none;
    box-shadow: 4px 4px 4px 0 rgba(0, 0, 0, 0.25);
    & img {
      width: 30px;
      height: 30px;
    }
    &:hover {
      background: var(--brand-color);
      & img {
        filter: invert(1);
      }
    }
    &:active {
      box-shadow: inset 4px 4px 4px 0 rgba(0, 0, 0, 0.25);
    }
  }
}
</style>
