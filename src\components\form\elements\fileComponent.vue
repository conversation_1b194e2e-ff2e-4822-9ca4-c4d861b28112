<template>
  <div
    :class="{
      'form-input': true,
      'form-input--required': data.required,
      'form-document': true,
    }"
  >
    <div class="v-center space-between">
      <label>{{ data.caption }}
        <img
      v-if="data.visibility"
      class="mx-1"
      width="15"
      src="~@/assets/images/eye.svg"
      alt="view"
      />
      </label>
      <input ref="docsInput" type="file" :id="uid" @click="resetInputValue" @change="onChange" />
      <div class="flex gap-1 mr-1">
<img width="25px" src="~@/assets/images/icon-system.png" alt="Add Document" v-tooltip="'Add Documents from Local.'"
      @click="triggerFileInput"
      v-if="mode !== 'TEMPLATE' && !isExternalCollaborator && mode !== 'VIEW'" />
      <img width="25px" src="~@/assets/images/icons-cloud.png" alt="Add Document" v-tooltip="'Add Documents from DTX.'"
      @click="openDocumentDialog"
      v-if="mode !== 'TEMPLATE' && !isExternalCollaborator && mode !== 'VIEW'" />
      </div>
    </div>
    <div class="form-document--document">
      <div v-if="!attachedDocs.length" class="form-document--document__no-document">
        No document added.
      </div>
      <div
        class="form-document--document__item"
        v-for="(item, index) in attachedDocs"
        :key="item.id"
      >
        <img src="~@/assets/images/documents-icon.png" width="20px" />
        <div class="form-document--document__item--name mx-2">
          <div>{{ item.doc_name }}</div>
          <small><b class="weight-500">Size:</b> {{getFileSize(item.file_size)}}</small>
        </div>
        <div class="form-document--document__item--action" v-if="item.loading">
          <img
            class="loading-icon"
            src="~@/assets/images/icons/progress-icon.svg"
            width="20px"
          />
        </div>
        <div class="form-document--document__item--action" v-else>
          <div>
            <img  v-tooltip="'View Document'" class="mr-2" width="18px" src="~@/assets/images/eye.svg" alt=""  @click="openDocs(item)"/>
          <img
          v-if="mode !== 'TEMPLATE' && mode !== 'VIEW' && !isExternalCollaborator"
            class="mr-2"
            src="~@/assets/images/icons/download-icon.svg"
            width="20px"
            @click="download(item)"
          />
          <img  v-if="mode !== 'TEMPLATE' && mode !== 'VIEW' && !isExternalCollaborator" src="~@/assets/images/delete-gray-icon.svg" width="20px" @click="!viewOnly && remove(index)" />
        </div></div>
      </div>
      <div class="center p-6" v-if="Loading" >
            <loading-circle/>
          </div>
    </div>
    <modal
    :open="documentSelectDialog"
    @close="documentSelectDialog = false"
    :closeOnOutsideClick="true"
    title=""
    >
    <document-selector @close="documentSelectDialog = false" @files-selected="handleFileSelection" v-if="documentSelectDialog" :linkedDocs="dtxSelectedFile" :uploadDocButton="uploadDocButton"/>
  </modal>
  <view-attached v-if="detailObject.open" :fileId="detailObject.fileId" :fileDetails="fileDetails" :view_only="detailObject.view_only" @close="closeDetail"/>
  </div>
</template>

<script>
import { warning, alert } from '../../../plugins/notification'
import { generateS3SubmittingUrl, generateS3DownloadingUrlSingle } from '@/api'
import { mapGetters } from 'vuex'
import Modal from '../../common/modal.vue'
import DocumentSelector from '../../document/documentSelectorDialog.vue'
import ViewAttached from '../../projectPlanner/viewAttachedDocs.vue'
import LoadingCircle from '../../common/loadingCircle.vue'
export default {
  name: 'fileComponent',
  components: { DocumentSelector, Modal, ViewAttached, LoadingCircle },
  props: {
    mode: {
      type: String,
      default: 'TEMPLATE'
    },
    data: {
      type: Object,
      default: () => {}
    },
    value: {
      type: Array,
      default: () => ([])
    },
    viewOnly: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      componentValue: [],
      delete: [],
      insert: [],
      documentSelectDialog: false,
      attachedDocs: [],
      detailObject: {
        open: false,
        fileId: '',
        view_only: false
      },
      fileDetails: [],
      dtxSelectedFile: [],
      Loading: false,
      initialValue: [],
      uploadDocButton: false
    }
  },
  computed: {
    ...mapGetters(['openTenantId']),
    ...mapGetters(['isExternalCollaborator']),
    uid () {
      return this._uid
    }
  },
  methods: {
    resetInputValue () {
      this.$refs.docsInput.value = null
    },
    triggerFileInput () {
      this.$refs.docsInput.click()
    },
    openDocumentDialog () {
      this.documentSelectDialog = true
    },
    handleFileSelection (selectedFileData) {
      this.documentSelectDialog = false
      this.Loading = true
      this.dtxSelectedFile = selectedFileData
      selectedFileData.forEach((item) => {
        const documentId = item.core_document.id
        const isExisting = this.attachedDocs.some(doc => doc.id === documentId)
        if (!isExisting && (item.flag === 'new' || item.flag === 'existing')) {
          this.attachedDocs.push(item)
          this.componentValue.push({ documents_reference_key: documentId })
          const indexInInitialArray = this.initialValue.findIndex((val) => val === documentId)
          if (indexInInitialArray === -1) {
            this.insert.push({ documents_reference_key: documentId })
          } else {
            const indexInDeleteArray = this.delete.findIndex((val) => val.docId === documentId)
            if (indexInDeleteArray !== -1) {
              this.delete.splice(indexInDeleteArray, 1)
            }
          }
        } else if (item.flag === 'deleted') {
          this.attachedDocs = this.attachedDocs.filter(doc => doc.id !== documentId)
          this.componentValue = this.componentValue.filter(doc => doc.documents_reference_key !== documentId)
          const indexInInitialArray = this.initialValue.findIndex((val) => val === documentId)
          if (indexInInitialArray !== -1) {
            this.delete.push({ localAttachment: false, id: this.initialValue[indexInInitialArray] })
          } else {
            const indexInInsertArray = this.insert.findIndex((doc) => doc.documents_reference_key === documentId)
            if (indexInInsertArray !== -1) {
              this.insert.splice(indexInInsertArray, 1)
            }
          }
        }
        this.emitChanges()
      })
      this.Loading = false
    },
    emitChanges () {
      const value = {}
      if (this.insert.length) {
        value.insert = this.insert
      }
      if (this.delete.length) {
        value.delete = this.delete.map(file => file.id)
      }
      this.$emit('customChange', {
        field_id: this.$props.data.field_id,
        value
      })
    },
    getFileSize (bytes, decimals = 2) {
      if (!+bytes) return '0 Bytes'

      const k = 1024
      const dm = decimals < 0 ? 0 : decimals
      const sizes = [
        'Bytes',
        'KiB',
        'MiB',
        'GiB',
        'TiB',
        'PiB',
        'EiB',
        'ZiB',
        'YiB'
      ]

      const i = Math.floor(Math.log(bytes) / Math.log(k))

      return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`
    },
    onChange (e) {
      if (this.mode === 'TEMPLATE') {
        warning('Document upload is not allowed on template mode.')
        return
      }
      const file = e.target.files[0]
      const fileType = file.name.split('.').pop()
      const fileObject = {
        file_name: file.name,
        file_type: fileType,
        file_size: file.size,
        fileData: file
      }
      if (this.attachedDocs.some(f => f.doc_name === fileObject.file_name)) {
        alert('This file name has already exist.')
        return
      }
      this.Loading = true
      this.componentValue.push(fileObject)
      const fileReader = new FileReader()
      fileReader.readAsDataURL(file)
      fileReader.onload = async (e) => {
        const { url } = await generateS3SubmittingUrl({
          tenantId: this.openTenantId,
          feature: 'form',
          featureId: 'formDocument',
          fileName: fileObject.file_name
        })
        await fetch(url, {
          method: 'PUT',
          body: fileObject.fileData,
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        const blobkey = url.split('?')[0].split('.com/').pop()
        fileObject.blob_key = blobkey
        fileObject.fileData = undefined
        this.insert.push(fileObject)
        this.attachedDocs.push({ doc_name: file.name, doc_ext: fileType, doc_size: file.size, blob_key: fileObject.blob_key })
        this.emitChanges()
        this.Loading = false
        this.$refs.docsInput.values = ''
      }
    },
    remove (index) {
      const removeDocs = this.attachedDocs[index]
      if (this.attachedDocs[index].status === 'NONE') {
        this.delete.push({ id: this.attachedDocs[index].fileId, docId: removeDocs.id })
      }
      this.componentValue = this.componentValue.filter(doc => {
        return doc.documents_reference_key !== removeDocs.id
      })
      this.insert = this.insert.filter(doc => {
        return doc.documents_reference_key !== removeDocs.id
      })
      this.dtxSelectedFile = this.dtxSelectedFile.filter(doc => {
        return doc.core_document.id !== removeDocs.id
      })
      this.attachedDocs.splice(index, 1)
      this.emitChanges()
    },
    download (docs) {
      docs.loading = true
      generateS3DownloadingUrlSingle({
        fileName: encodeURIComponent(docs.doc_name),
        S3Key: docs.blob_key
      }).then((resp) => {
        fetch(resp.url)
          .then((res) => {
            return res.blob()
          })
          .then((res) => {
            const blob = new Blob([res])
            const link = document.createElement('a')
            link.href = URL.createObjectURL(blob)
            link.download = docs.doc_name
            link.click()
            URL.revokeObjectURL(link.href)
            docs.loading = false
          })
          .catch((err) => {
            alert(err)
            docs.loading = false
          })
      })
    },
    setValue () {
      const tempValue = []
      this.value.forEach((item) => {
        if (item.core_document) {
          this.dtxSelectedFile.push({ core_document: item?.core_document })
        }
        const fileAttachment = item.core_attachment || item.core_document
        const fileType = (fileAttachment.file_type || fileAttachment.doc_ext)
        const fileExtension = fileType.includes('/') ? fileType.split('/')[1] : fileType
        if (fileAttachment) {
          this.initialValue.push(fileAttachment.id)
          tempValue.push({
            id: fileAttachment.id,
            fileId: item.id,
            doc_name: fileAttachment.file_name || fileAttachment.doc_name,
            doc_ext: fileExtension,
            doc_size: fileAttachment.file_size || fileAttachment.doc_size,
            loading: false,
            blob_key: fileAttachment.blob_key,
            field_id: item.field_id,
            attachments_reference_key: item.attachments_reference_key,
            version_of: fileAttachment.version_of || null,
            created_by: fileAttachment.created_by || null,
            created_on: fileAttachment.created_on || null,
            thumbnail_blob_key: fileAttachment.thumbnail_blob_key || null,
            status: 'NONE'
          })
        }
      })
      this.initalValue = tempValue
      this.attachedDocs = tempValue
    },
    closeDetail () {
      this.detailObject = {
        open: false,
        fileId: '',
        view_only: false
      }
      this.fileDetails = []
    },
    openDocs (fileData) {
      this.detailObject = {
        open: true,
        fileId: fileData.doc_id || fileData.id,
        view_only: false
      }
      this.fileDetails.push(fileData)
    }
  },
  created () {
    this.setValue()
  }
}
</script>

<style lang="scss" scoped >
.form-document {
  input[type="file"] {
    display: none;
  }
  &--label {
    background-color: var(--brand-color);
    font-size: 12px;
    padding: 4px 10px;
    border-radius: 4px;
    box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
  }
  &--document {
    position: relative;
    display: inline-block;
    width: 100%;
    margin-top: 10px;
    border: 1px solid rgb(179, 179, 179);
    min-height: 60px;
    max-height: 200px;
    overflow-y: auto;
    border-radius: 4px;
    &__no-document {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: rgb(179, 179, 179);
    }
    &__item {
      padding: 4px 10px;
      margin: 4px;
      background-color: rgba(var(--brand-rgb), 0.2);
      display: flex;
      align-items: center;
      font-size: 12px;
      &:hover {
        box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.25);
      }
      &--name {
        flex-grow: 1;
        & > div {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
        & small {
          opacity: 0.7;
        }
      }
      .loading-icon {
        animation: rotate 0.8s linear infinite;
      }
    }
  }
}
</style>
