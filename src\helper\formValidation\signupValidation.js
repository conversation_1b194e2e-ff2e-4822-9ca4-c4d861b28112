import {
  emptyString,
  length,
  MatchString,
  password
} from './index'
export default (body) => {
  if (!emptyString(body.password, 'Password', 'Password')) {
    return false
  } if (!length(body.password, 'Password', 6, undefined, 'Password')) {
    return false
  } if (!password(body.password, 'Password', 'Password')) {
    return false
  } if (!MatchString(
    body.password,
    body.confirmPassword,
    'Password and Confirm Password are not same',
    ['Password', 'ConfirmPassword'])) {
    return false
  }
  return true
}
