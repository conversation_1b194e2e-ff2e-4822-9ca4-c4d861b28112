<template>
  <div>
    <div class="material-master-detail">
      <div class="grid-2">
        <div class="key-value">
          <span class="key"> {{ type === 'material' ?  'Material' : 'Resource'}} Id:</span>
          <span class="value">{{ materialMaster.custom_material_id || '--' }}</span>
        </div>
        <div class="key-value">
          <span class="key">{{ type === 'material' ?  'Material' : 'Resource'}} Name:</span>
          <span class="value">{{ materialMaster.material_name || '--' }}</span>
        </div>
      </div>
      <div class="grid-2 mt-2">
        <div class="key-value">
          <span class="key">{{ type === 'material' ?  'Material' : 'Resource'}} Description:</span>
          <span class="value">{{ materialMaster.material_description || '--' }}</span>
        </div>
        <div class="key-value">
          <span class="key">Product Code Name:</span>
          <span class="value">{{ materialMaster.material_product_code?.product_code || '--' }}</span>
        </div>
      </div>
      <div class="grid-2 mt-2">
        <div class="key-value">
          <span class="key">Quantity:</span>
          <span class="value">{{ materialMaster.inventory || '--' }}</span>
        </div>
        <div class="key-value">
          <span class="key">Custom {{ type === 'material' ?  'Material' : 'Resource'}} Id:</span>
          <span class="value">{{ materialMaster.custom_material_id || '--' }}</span>
        </div>
      </div>
      <div class="grid-2 mt-2">
        <div class="key-value">
          <span class="key">ERP {{ type === 'material' ?  'Material' : 'Resource'}} Id:</span>
          <span class="value">{{ materialMaster.erp_material_id || '--' }}</span>
        </div>
        <div class="key-value">
          <span class="key">Gross Weight:</span>
          <span class="value">{{ materialMaster.gross_weight || '--' }}</span>
        </div>
      </div>
      <div class="grid-2 mt-2">
        <div class="key-value">
          <span class="key">{{ type === 'material' ?  'Material' : 'Resource'}} Group:</span>
          <span class="value">{{  type === 'material' ? (materialMaster.material_group_details?.name || '--') : (materialMaster.resource_group_details?.name || '--')}}</span>
        </div>
      </div>
      <div class="grid-2 mt-2">
        <div class="key-value">
          <span class="key">Storage Loc:</span>
          <span class="value">{{ materialMaster.material_storage_location?.name || '--' }}</span>
        </div>
        <div class="key-value">
          <span class="key">Unit Of {{ type === 'material' ?  'Material' : 'Resource'}}:</span>
          <span class="value">{{ materialMaster.material_unit_details?.name || '--' }}</span>
        </div>
      </div>
      <div class="grid-2 mt-2">
        <div class="key-value">
          <span class="key">Weight Unit:</span>
          <span class="value">{{ materialMaster.material_weight_details?.name || '--' }}</span>
        </div>
        <div class="key-value">
          <span class="key">PLM {{ type === 'material' ?  'Material' : 'Resource'}} Id:</span>
          <span class="value">{{ materialMaster.plm_material_id || '--' }}</span>
        </div>
      </div>
      <div class="key-value mt-2">
        <span class="key">PLM Record Id:</span>
        <span class="value">{{ materialMaster.plm_record_id || '--' }}</span>
      </div>
      <div class="grid-2 mt-2">
        <div class="key-value">
          <span class="key">Lead Time:</span>
          <span class="value">{{ materialMaster.lead_time || '--' }}</span>
        </div>
        <div class="key-value">
          <span class="key">Unit Cost:</span>
          <span class="value">{{ materialMaster.unit_cost || '--' }}</span>
        </div>
      </div>
      <div class="grid-2 mt-2">
        <div class="key-value">
          <span class="key">Unit Sale Price:</span>
          <span class="value">{{ materialMaster.unit_sale_price || '--' }}</span>
        </div>
        <div class="key-value">
          <span class="key">Effective Date:</span>
          <span class="value">{{ materialMaster.effective_date || '--' }}</span>
        </div>
      </div>

      <div v-if="materialMaster.tag_materials.length" class="flex key-value mt-2">
        <span class="key">Tags:</span>
        <div class="tags">
          <span v-for="tag_material in materialMaster.tag_materials" :key="tag_material.tag.name">{{ tag_material.tag.name
          }}</span>
        </div>
      </div>
      <div v-else class="key-value mt-2">
        <span class="key">Tags:</span>
        <span class="value">No tags are attached to this material
        </span>
      </div>
      <div v-if="materialMaster.material_document_associations?.length" class="flex key-value mt-2">
        <span class="key">Documents:</span>
        <div class="tags">
          <span v-for="documents in materialMaster.material_document_associations" :key="documents.id">{{ documents.core_document?.doc_name }}</span>
        </div>
      </div>
      <!--  -->
      <div class="mt-6">
        <h5>Form Details</h5>
        <div class="grid-2 mt-2"  v-if="materialMaster.core_form">
      <div v-for="field in templateFields.filter(item=>!item.autogenerated) ?? []" :key="field.field_id" class="key-value">
          <span class="key">{{field?.caption ?? '--' }}  :  </span>
          <span class="value"> {{ materialCustomFieldData[field?.field_id]?.value ?? '--' }} </span>
      </div>
    </div>
    <div v-else>
No forms data available
    </div>
      </div>
      <!--  -->
      <div class=" mt-2">
        <div class="key-value">
          <div><span class="key">Products and BOMS where this material is used:</span></div>
         <associated-bom-table :productsAndBoms="productsAndBoms" :loading="loading" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { GetProductsUsingMaterialQuery } from '../../api/apis/materialMaster'
import AssociatedBomTable from './associatedBomTable.vue'
import { alert } from '../../plugins/notification'
import { GetFormDataByFormId } from '@/api'
import { mapGetters } from 'vuex'
export default {
  name: 'material-master-detail',
  components: { AssociatedBomTable },
  props: {
    type: {
      type: String,
      default: 'material'
    },
    materialMaster: {
      type: Object,
      default: () => ({
        id: 29,
        blob_reference_key: null,
        custom_material_id: '10097',
        erp_material_id: '10097',
        gross_weight: null,
        material_description: "'M12 HEX NUT",
        material_group: null,
        material_name: 'random',
        material_type: '1',
        parent_id: null,
        plm_material_id: '101000615',
        plm_record_id: 'a8df87c4-0709-446d-b0d4-f3266372dcc9',
        quantity: null,
        status: 1,
        material_weight_details: null,
        storage_loc: 0,
        unit_of_material: 'NOS',
        weight_unit: null,
        tenant_id: 1,
        __typename: 'core_material_master'
      })
    }
  },
  computed: {
    ...mapGetters(['isOnProjectLevel'])
  },
  data () {
    return {
      productsAndBoms: {},
      loading: false,
      templateFields: [],
      materialCustomFieldData: {}
    }
  },
  mounted () {
    this.loading = true
    if (this.materialMaster?.core_form?.id) {
      this.getMaterialCustomFieldData()
    }
    const productsAndBomss = {}
    GetProductsUsingMaterialQuery(this.materialMaster.id).then((res) => {
      if (this.isOnProjectLevel) {
        productsAndBomss.projectBom = []
      }
      res.core_material_master[0].bom_material_items.forEach(item => {
        // this condtion has added beacause ,  for project level boms there is no product code , the data is to be  handled where it showing
        if (this.isOnProjectLevel) {
          productsAndBomss.projectBom.push(item.core_bom.name)
        } else {
          const productCode = item.core_bom.product_bom ? item.core_bom.product_bom.product_code : ''
          if (productCode) {
            if (productsAndBomss[productCode]) {
              productsAndBomss[productCode].push(item.core_bom.name)
            } else {
              productsAndBomss[productCode] = [item.core_bom.name]
            }
          }
        }
      })
      this.productsAndBoms = productsAndBomss
    }).catch(() => {
      alert('Failed to fetch data')
    })
      .finally(() => {
        this.loading = false
      })
  },
  methods: {
    getMaterialCustomFieldData () {
      this.templateFields = this.materialMaster?.core_form?.template_version?.template_fields
      GetFormDataByFormId(this.materialMaster?.core_form?.id, false).then(res => {
        res.core_forms_by_pk.forms_metadata_by_id.forEach((item) => {
          this.materialCustomFieldData[item?.field_id] = {
            id: item.field_id,
            value: item.string_value ??
                    item.time_value?.split('+')[0] ??
                    item.int_value ??
                    item.date_value ??
                    item.bool_value ??
                    item.point_value ??
                    item.string_value ??
                    '--'
          }
        })
        res.core_forms_by_pk.forms_config_lists.forEach((item) => {
          this.materialCustomFieldData[item.field_id] = {
            field_id: item.field_id,
            value: item.custom_list_value
          }
        })
        this.materialCustomFieldData = { ...this.materialCustomFieldData }
      })
    }
  },
  watch: {
    'this.materialMaster.id' () {
      this.getMaterialCustomFieldData()
      this.templateFields = this.materialMaster?.core_form?.template_version?.template_fields
    }
  }
}
</script>

<style lang="scss" scoped >
.material-master-detail {
  max-height: 76vh;
  overflow-y: auto;
  width: 600px;
}

.key-value {
  .key {
    color: var(--text-color-1);
    font-weight: 500;
    font-size: 0.9em;
  }

  .value {
    margin: 0 4px;
    font-size: 0.9em;
    color: var(--text-color);
  }
}

.tags {
  display: flex;
  flex-wrap: wrap;

  span {
    margin-left: 0.4rem;
    background-color: rgba(var(--brand-rgb), 0.5);
    padding: 0.1rem 0.4rem;
    font-size: small;
    max-height: 3rem;
    border-radius: 0.3rem;
  }
}
.material-customList{
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr ;
}
</style>
