<template>
  <div class="bom-detail">
    <div class="bom-detail-loading fh center" v-if="loadingBomDetail">
      <loading-circle />
    </div>
    <template v-else>
      <div class="bom-detail-bar v-center space-between">
        <div class="bom-detail-bar-title v-center">
          <img
            class="mr-3"
            src="~@/assets/images/icons/arrow-back.svg"
            width="20px"
            alt=""
            @click="goBack"
          />
          <span
            class="elipsis-text"
            style="max-width: 250px"
            v-overflow-tooltip
          >
            {{ bomName }}
          </span>
          <select
            class="bom-detail-version"
            v-if="getBomVersionsList.length"
            v-model="bomVersionId"
            @change="changeBomVersion"
          >
            <option
              v-for="bomVersion in getBomVersionsList"
              :key="bomVersion.id"
              :value="bomVersion.id"
            >
              V-{{ bomVersion.version_no }}
              {{
                bomVersion.state === 3
                  ? "(Checkout)"
                  : bomVersion.released
                  ? "(Released)"
                  : bomVersion.active
                  ? "(Latest)"
                  : ""
              }}
            </option>
          </select>
          <div class="inherit" v-if="inheritedFrom">
            <img
              v-if="inheritedFrom?.bomVersionActive"
              class="mx-2"
              src="@/assets/images/icons/info-icon.svg"
              width="22px"
            />
            <img
              v-else
              class="mx-2"
              src="@/assets/images/icons/alert-info-icon.svg"
              width="22px"
              @click="
                showCheckInButton &&
                  updateInheritedBom(
                    inheritedFrom?.bomName,
                    inheritedFrom?.bomVersion
                  )
              "
            />
            <div class="inherit-message">
              Bom
              {{ bomName }}
              is inherited from
              <router-link
                :to="`/bom/product/${selectedProductCode}/bom/${inheritedFrom?.bomId}?bomVersionId=${inheritedFrom.bomVersionId}`"
              >
                {{ inheritedFrom?.bomName }} {{ inheritedFrom?.bomVersion }}
                {{ inheritedFrom?.bomVersionActive ? "( Latest )" : "" }}
              </router-link>
              {{ inheritedFrom?.bomVersionActive ? "." : " is outdated." }}
              <span
                v-if="!inheritedFrom?.bomVersionActive && showCheckInButton"
                @click="
                  showCheckInButton &&
                    updateInheritedBom(
                      inheritedFrom?.bomName,
                      inheritedFrom?.bomVersion
                    )
                "
                class="update_button"
                >update</span
              >
            </div>
          </div>
          <div class="bom-state-chip" :class="getBomState">
            {{ getBomState }}
            {{
              bomState === 3 && getCheckedOutBy !== user.userId
                ? `by ${userName(getCheckedOutBy)}`
                : ""
            }}
          </div>
        </div>
        <div class="v-center s">
          <button
            class="btn ml-3"
            @click="handleShareBom"
            v-if="showShareBomButton"
          >
            Share BOM
          </button>
          <button
            class="btn ml-3"
            @click="openNotificationModal('checkin')"
            v-if="showCheckInButton"
          >
            Checkin
          </button>
          <button
            v-if="showCheckoutButton"
            class="btn ml-3"
            @click="openNotificationModal('checkout')"
          >
            Checkout
          </button>
          <button
            v-if="showUndoCheckoutButton"
            class="btn ml-3"
            @click="openNotificationModal('undo checkout')"
            :disabled="bomVersionList.length === 1"
          >
            Undo Checkout
          </button>
          <button v-if="showLockButton" class="btn ml-3" @click="openNotificationModal('lock')">
            Lock
          </button>
          <button v-if="showObsoleteBom" class="btn ml-3" @click="openNotificationModal('obsolete')">
            Obsolete
          </button>
          <button v-if="showReleaseButton" class="btn ml-3" @click="releaseBom">
            Release
          </button>
          <button
            v-if="showEditButton"
            class="btn btn-black ml-3"
            @click="editSelected"
          >
            Edit
          </button>
          <button
            v-if="showNonObsoleteBom"
            class="btn ml-3"
            @click="nonObsoleteBom"
          >
            Non Obsolete
          </button>
          <button
            v-if="showUnlockButton"
            class="btn btn-black ml-3"
            @click="unLockBom"
          >
            Unlock
          </button>
        </div>
      </div>
      <div class="bom-detail-item">
        <div class="bom-detail">
          <bom-detail-head />
          <bom-detail-body
            :bomId="bomId || null"
            :bomVersionId="bomVersionId"
            :showEdit="showEditButton"
            @bomItemLength="checkBomItemLength"
          />
          <div class="table-footer">
            <div v-if="showTotalCost" class="footer-right">
              <div class="footer-field">
                <label>Total Cost:</label>
                <span>{{ total_cost === 0 ? "0" : total_cost || "--" }}</span>
              </div>
              <div class="footer-field">
                <label>Sale Price:</label>
                <span>{{ sale_price === 0 ? "0" : sale_price || "--" }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <bom-comment :closeOnOutsideClick="true"/>
    <modal
      @close="closeNotificationModal"
      :closeOnOutsideClick="true"
      :open="materialNotification.isOpen"
      title="Products that will be impacted by this change : "
    >
      <div>
        <div class="thiss product-bom">
          <div
            v-if="productCodeData[0]?.core_bom?.product_bom?.product_code"
            :class="{ 'scroll-box': hasData }"
            class=""
          >
            <div
              class="product-bom-card"
              v-for="product in productCodeData"
              :key="product?.id"
            >
              <div v-overflow-tooltip class="elipsis-text">
                {{ product?.core_bom?.product_bom?.product_code }}
              </div>
            </div>
          </div>
          <div v-else>
            <p class="pb-3">No Products will be affected by this change!</p>
          </div>
        </div>
      </div>
      <div class="modal-buttons mt-5">
        <button
          @click="closeNotificationModal"
          class="btn btn-black dialog-cancel mr-2"
        >
          Cancel
        </button>
        <button @click="confirmBomAction" class="btn dialog-ok">Confirm</button>
      </div>
    </modal>
    <modal
      :open="updateInheritedBomModal"
      @close="updateInheritedBomModal = false"
      :closeOnOutsideClick="true"
      title="update to latest version"
    >
      <table class="update-bom-table">
        <thead>
          <th>
            <img
              v-if="selectedAll"
              src="~@/assets/images/icons/checked-icon.svg"
              alt=""
              @click="deselectAll"
            />
            <img
              v-else
              src="~@/assets/images/icons/unchecked-icon.svg"
              alt=""
              @click="selectAll"
            />
          </th>
          <th>Product Code</th>
          <th>Material Id</th>
          <th>Material Name</th>
          <th>Quantity</th>
          <th>Unit Size</th>
          <th>Type</th>
          <th>Material Cost</th>
          <th>Material Price</th>
          <th>Associated Bom</th>
        </thead>
        <tbody>
          <tr
            v-for="(material, index) in newBomData?.bom"
            :key="index"
            :class="{
              deleted: material?.quantity < 1,
              existing: material.state === 'old',
            }"
          >
            <td>
              <span v-if="material.state !== 'old'">
                <img
                  v-if="material.selected"
                  src="~@/assets/images/icons/checked-icon.svg"
                  alt=""
                  @click="deselect(index)"
                />
                <img
                  v-else
                  src="~@/assets/images/icons/unchecked-icon.svg"
                  alt=""
                  @click="selectMaterial(index)"
                />
              </span>
            </td>
            <td>
              {{ material?.core_material?.material_product_code?.product_code }}
            </td>
            <td>
              {{ material?.core_material?.custom_material_id }}
            </td>

            <td>{{ material?.core_material?.material_name }}</td>
            <td>{{ material?.quantity }}</td>
            <td>{{ material?.unit_size }}</td>
            <td><span v-if="material?.core_material.type === 1">{{ material?.core_material?.material_group_details?.name }}</span>
            <span v-else>{{ material?.core_material?.resource_group_details?.name }}</span></td>
            <td>{{ material?.material_unit_cost }}</td>
            <td>{{ material?.material_unit_sale_price }}</td>
            <td>
              <span v-if="material?.associated_bom_version?.core_bom?.name"
                >{{ material?.associated_bom_version?.core_bom?.name }} <br />
                Version-{{ material?.associated_bom_version?.version_no }}</span
              >
            </td>
          </tr>
        </tbody>
      </table>
      <div class="accordion">
        <div>
          <div
            class="accordion-header flex v-center space-between"
            @click="expandSummary = !expandSummary"
          >
            <h4>Summary</h4>
            <span>
              <img
                v-if="!expandSummary"
                src="~@/assets/images/icons/arrow-down-icon.svg"
                height="15px"
                alt=""
              />
              <img
                v-else
                src="~@/assets/images/icons/arrow-up-icon.svg"
                height="15px"
                alt=""
              />
            </span>
          </div>
          <div v-if="expandSummary" class="accordion-content">
            <ul class="message-ul">
              <li v-for="(message, index) in newBomData?.message" :key="index">
                {{ message }}
              </li>
              <!-- <li class="deleted-msg">
          All the materials with quantity less than zero will be deleted
        </li> -->
            </ul>
          </div>
        </div>
      </div>
      <div class="update-btn-box">
        <button class="btn btn-black ml-3" @click="closeModal">Cancel</button>
        <button class="btn ml-3 update-btn" @click="compareAndUpdate">
          Update
        </button>
      </div>
    </modal>
    <modal
      :open="shareBomModal"
      @close="shareBomModal = false"
      :closeOnOutsideClick="true"
      title="Share BOM"
    >
      <share-bom
        v-if="shareBomModal"
        @close="shareBomModal = false"
        :id="bomId"
        :versionId="getLatestVersionId"
        item="bom"
      />
    </modal>
  </div>
</template>

<script>
import {
  ProductCodeStatus,
  GetBomDetailById,
  GetAllBomVersionListByBomId,
  GetBomDetailByIdAndBomVersion,
  UpdateBomState,
  GetAllBomItemsByBomVersionId,
  UpdateProductBom,
  addComments,
  ReleaseBomVersion,
  DeleteBomVersion,
  DeleteBomItemsByVersionId,
  setLastBomVersionActive,
  updateBomVersion,
  deleteAllBomItemAndUpdate
} from '@/api'
import { mapGetters } from 'vuex'
import Modal from '../../../components/common/modal.vue'
import { alert, success } from '@/plugins/notification'
import bomDetailHead from '../../../components/bom/common/bomDetailHead.vue'
import BomDetailBody from '../../../components/bom/common/bomDetailBody.vue'
import LoadingCircle from '../../../components/common/loadingCircle.vue'
import BomComment from '../../../components/bom/common/bomComment.vue'
import ShareBom from '../../../components/common/share.vue'
import { GetAllImpactedProductCode } from '../../../api/apis/productCode'
import { compareAndUpdate } from '../../../components/bom/compare/compareAndUpdate'
import ConfirmationDialog from '@/plugins/confirmationDialog'
import Loader from '@/plugins/loader'
import Config from '@/config'

export default {
  components: {
    bomDetailHead,
    Modal,
    BomDetailBody,
    LoadingCircle,
    BomComment,
    ShareBom
  },
  name: 'bomDetail',
  data: () => ({
    obseleteCheck: false,
    showTotalCost: false, // show/hide total cost based on bom Items
    loadingBomDetail: false,
    bomName: '',
    bomVersionList: [],
    bomVersion: '',
    bomVersionReleased: false,
    bomId: null,
    bomVersionId: '',
    inheritedFrom: null,
    bomState: null,
    productCodeData: [],
    activeBomversion: false,
    materialNotification: {
      isOpen: false,
      action: '',
      productsAndBoms: null
    },
    updateInheritedBomModal: false,
    newBomData: [],
    newInheritedVersionId: null, // saves the bom version id of latest parent
    total_cost: null,
    sale_price: null,
    shareBomModal: false,
    collabarotor: false,
    activeBomversionId: null, // active bom version id ,  used at the time of  update inherited bom,
    selectedAll: true,
    expandSummary: false, // for accordian in update inherit modal modal
    activeBomVersionItems: null //  stores bom items (materials) of latest version of child bom
  }),
  computed: {
    hasData () {
      return (
        this.productCodeData[0]?.core_bom?.product_bom?.product_code ||
        this.productCodeData?.associated_bom_version?.core_bom?.product_bom
          ?.product_code
      )
    },
    ...mapGetters('productBom', [
      'selectedProductCode',
      'selectedBomId',
      'obsoleteProductCodes'
    ]),
    ...mapGetters(['user', 'getUserById', 'collaborator', 'isOnProjectLevel']),
    getBomState () {
      return Config.STATE_MAP[this.bomState]
    },
    getBomVersionsList () {
      return this.bomVersionList.filter((bomVersion) => {
        if (bomVersion.state === 3) {
          if (bomVersion.checked_out_by !== this.user.userId) {
            return false
          }
        }
        return true
      })
    },
    checkoutVersion () {
      return (
        this.bomVersionList.find((bomVersion) => bomVersion.state === 3) || {}
      )
    },
    getCheckedOutBy () {
      return this.checkoutVersion.checked_out_by || null
    },
    showCheckInButton () {
      return (
        this.getCheckedOutBy === this.user.userId &&
        this.bomState === 3 &&
        this.productCodeStatus === undefined && this.user.tenantLevelRole !== 'VIEWER'
      )
    },
    showShareBomButton () {
      return (
        this.bomState !== Config.BOM_STATE_MAP.OBSOLETE && // this condtion is to ensure the given bom has min one active version if it has onl one version
        !this.collabarotor &&
        (this.user.tenantLevelRole === 'ADMIN' ||
          this.user.tenantLevelRole === 'EDITOR' ||
          this.user.tenantLevelRole === 'COLLABORATOR') && this.productCodeStatus === undefined && this.checkVersionList
      )
    },
    // this condtion is to ensure the given bom has min one active version if it has onl one version
    checkVersionList () {
      if (
        this.bomVersionList.length === 1 &&
        this.bomVersionList[0].state === 3
      ) { return false }
      return true
    },

    showCheckoutButton () {
      return (
        this.activeBomversion &&
        this.bomState === 2 &&
        (this.user.tenantLevelRole === 'ADMIN' ||
          this.user.tenantLevelRole === 'EDITOR' ||
          this.user.tenantLevelRole === 'COLLABORATOR') &&
        this.productCodeStatus === undefined
      )
    },
    showLockButton () {
      return (
        this.bomState === 2 &&
        (this.user.tenantLevelRole === 'ADMIN' ||
          this.user.tenantLevelRole === 'COLLABORATOR') &&
        this.productCodeStatus === undefined &&
        !this.collabarotor
      )
    },
    showObsoleteBom () {
      return (
        this.bomState === 2 &&
        this.user.tenantLevelRole === 'ADMIN' &&
        this.productCodeStatus === undefined &&
        !this.collabarotor
      )
    },
    showUnlockButton () {
      return (
        this.bomState === Config.BOM_STATE_MAP.LOCK &&
        (this.user.tenantLevelRole === 'ADMIN' ||
          this.user.tenantLevelRole === 'COLLABORATOR') && !this.collabarotor &&
        this.productCodeStatus === undefined
      )
    },
    showEditButton () {
      return (
        this.bomState === 3 &&
        this.getCheckedOutBy === this.user.userId &&
        this.productCodeStatus === undefined && this.user.tenantLevelRole !== 'VIEWER'
      )
    },
    showReleaseButton () {
      return (
        this.bomState === 2 &&
        !this.bomVersionReleased &&
        this.activeBomversion &&
        (this.user.tenantLevelRole === 'ADMIN' || this.user.tenantLevelRole === 'COLLABORATOR') &&
        this.productCodeStatus === undefined &&
        !this.collaborator
      )
    },
    showNonObsoleteBom () {
      return (
        this.bomState === Config.BOM_STATE_MAP.OBSOLETE &&
        this.user.tenantLevelRole === 'ADMIN' &&
        this.productCodeStatus === undefined
      )
    },
    showUndoCheckoutButton () {
      return (
        (this.getCheckedOutBy === this.user.userId ||
         (this.user.tenantLevelRole === 'ADMIN' && !this.collabarotor)) &&
        this.bomState === 3 &&
        this.productCodeStatus === undefined && this.user.tenantLevelRole !== 'VIEWER'
      )
    },
    productCodeStatus () {
      return this.obsoleteProductCodes[this.selectedProductCode]
    },
    getLatestVersionId () {
      for (const version of this.bomVersionList) {
        if (version.active) {
          return version.id
        }
      }
      return null
    }
  },
  watch: {
    '$route.params.bomId': {
      handler (newVal, oldVal) {
        if (newVal !== oldVal) {
          this.setup()
        }
      },
      immediate: true
    },
    bomVersionId () {
      this.activeBomversion = false
      for (const version of this.bomVersionList) {
        if (version.id === this.bomVersionId && version.active) {
          this.activeBomversion = true
        }
      }
    }
  },
  methods: {
    deselect (index) {
      this.selectedAll = false
      this.newBomData.bom[index].selected = false
      this.newBomData = { ...this.newBomData }
    },
    selectMaterial (index) {
      this.newBomData.bom[index].selected = true
      let flag = true
      this.newBomData.bom.forEach((material) => {
        if (material?.state !== 'old' && material.quantity > 0) {
          if (material?.selected) {
          } else {
            flag = false
          }
        }
      })
      if (flag) {
        this.selectedAll = true
      }
      this.newBomData = { ...this.newBomData }
    },
    deselectAll () {
      this.selectedAll = false
      this.newBomData.bom = this.newBomData.bom.map((material) => {
        if (material?.state !== 'old' && material.quantity > 0) {
          return { ...material, selected: false }
        } else {
          return material
        }
      })
    },
    selectAll () {
      this.selectedAll = true
      this.newBomData.bom = this.newBomData.bom.map((material) => {
        if (material?.state !== 'old' && material.quantity > 0) {
          return { ...material, selected: true }
        } else {
          return material
        }
      })
    },
    checkBomItemLength (length) {
      if (length > 0) {
        this.showTotalCost = true
      }
    },
    openNotificationModal (status) {
      this.materialNotification.isOpen = true
      this.materialNotification.action = status
      GetAllImpactedProductCode(this.bomId).then((data) => {
        this.productCodeData = data.bom_versions
      })
    },
    closeNotificationModal () {
      this.materialNotification.isOpen = false
    },
    confirmBomAction () {
      if (this.materialNotification.action === 'checkin') {
        this.checkInBom()
      } else if (this.materialNotification.action === 'checkout') {
        this.checkoutBom()
      } else if (this.materialNotification.action === 'undo checkout') {
        this.undoCheckout()
      } else if (this.materialNotification.action === 'obsolete') {
        this.obsoleteBom()
      } else if (this.materialNotification.action === 'lock') {
        this.lockBom()
      }
      this.materialNotification.isOpen = false
    },
    userName (userId) {
      const user = this.getUserById(userId)
      const userDetail = user?.associated_user
      return (
        (
          (userDetail?.first_name || '') +
          ' ' +
          (userDetail?.last_name || '')
        ).trim() || 'DTX User'
      )
    },
    checkInBom () {
      updateBomVersion(this.checkoutVersion.id, 2)
        .then(() => {
          addComments(
            'bom',
            this.bomId,
            'BOM Checked In',
            null,
            this.bomVersionId
          )
          this.setup()
        })
        .catch((error) => {
          alert(error.message ?? 'failed to fetch the data')
        })
    },
    undoCheckout () {
      if (this.checkoutVersion.id) {
        DeleteBomItemsByVersionId(this.checkoutVersion.id).then(() => {
          DeleteBomVersion(this.checkoutVersion.id).then(() => {
            UpdateBomState(this.selectedBomId, 2).then(() => {
              addComments('bom', this.bomId, 'BOM Undo Checkout', null, null)
              this.setup()
              success('BOM Undo Checkout Successfully')
            })
          })
        })
      }
    },
    checkoutBom () {
      const latestBomVersion = this.bomVersionList.sort((a, b) => {
        return b.version - a.version
      })[0]
      GetAllBomItemsByBomVersionId(latestBomVersion.id).then((res) => {
        const customField = res.bom_items?.[0]?.bom_version?.custom_fields || null
        const bomItems = res.bom_items || []
        bomItems.forEach((bomItem) => {
          delete bomItem.core_material
          delete bomItem.__typename
          delete bomItem.associated_bom_version
          delete bomItem.bom_version
          bomItem.bom_id = this.selectedBomId
        })

        UpdateProductBom(this.bomId, bomItems, customField).then(() => {
          UpdateBomState(this.selectedBomId, 3).then(() => {
            addComments(
              'bom',
              this.bomId,
              'BOM Checked Out',
              null,
              this.bomVersionId
            )
            setLastBomVersionActive(latestBomVersion?.id, 2, this.isOnProjectLevel).then(() => {
              this.setup()
            })
          })
        })
      })
    },
    lockBom () {
      UpdateBomState(this.selectedBomId, Config.BOM_STATE_MAP.LOCK).then(() => {
        addComments('bom', this.bomId, 'BOM Locked', null, this.bomVersionId)
        this.setup()
      })
    },
    unLockBom () {
      UpdateBomState(this.selectedBomId, 2).then(() => {
        addComments('bom', this.bomId, 'BOM Unlocked', null, this.bomVersionId)
        this.setup()
      })
    },
    obsoleteBom () {
      UpdateBomState(this.selectedBomId, Config.BOM_STATE_MAP.OBSOLETE).then(() => {
        addComments(
          'bom',
          this.bomId,
          'BOM Obsoleted',
          null,
          this.bomVersionId
        )
        this.setup()
      })
    },
    nonObsoleteBom () {
      UpdateBomState(this.selectedBomId, 2).then(() => {
        addComments(
          'bom',
          this.bomId,
          'BOM Non-Obsoleted',
          null,
          this.bomVersionId
        )
        this.setup()
      })
    },
    goBack () {
      this.$router.push(`/bom/product/${this.selectedProductCode}`)
    },
    goToBomDetail (bom) {
      this.$router
        .push(
          `/bom/product/${this.selectedProductCode}/bom/${bom.id}${
            bom.bom_versions?.[0].id
              ? `?bomVersionId=${bom.bom_versions?.[0].id}`
              : ''
          }`
        )
        .catch(() => {})
    },
    editSelected () {
      this.$router.push(
        `/bom/product/${this.selectedProductCode}/bom/${this.selectedBomId}/edit`
      )
    },
    changeBomVersion () {
      const bomVersion = this.bomVersionList.find(
        (bomVersion) => bomVersion.id === this.bomVersionId
      )
      if (bomVersion.active) {
        this.loadingBomDetail = true
        //    GetBomDetailById(this.selectedBomId) this was the previous query by this query , if the bom in checkout // state not showing latest version with previous query
        GetBomDetailByIdAndBomVersion(
          this.selectedBomId,
          this.bomVersionId
        ).then((res) => {
          const bomDetail = res.core_bom_by_pk
          this.setBomDetail(bomDetail)
          this.loadingBomDetail = false
        })
      } else {
        this.loadingBomDetail = true
        GetBomDetailByIdAndBomVersion(
          this.selectedBomId,
          this.bomVersionId
        ).then((res) => {
          const bomDetail = res.core_bom_by_pk
          this.setBomDetail(bomDetail)
          this.loadingBomDetail = false
        })
      }
    },
    setBomDetail (bomDetail) {
      this.bomName = bomDetail.name
      this.bomId = bomDetail.id
      this.sale_price = bomDetail.bom_versions?.[0]?.sale_price
      this.total_cost = bomDetail.bom_versions?.[0]?.total_cost
      this.bomVersionId = bomDetail.bom_versions?.[0]?.id
      this.bomVersion = bomDetail.bom_versions?.[0]?.version_no
      this.bomState = bomDetail.state || 2
      this.bomVersionReleased = bomDetail.bom_versions?.[0]?.released || false
      if (bomDetail.inherited_bom_version) {
        this.inheritedFrom = {
          bomId: bomDetail.inherited_bom_version?.core_bom?.id,
          bomName: bomDetail.inherited_bom_version?.core_bom?.name,
          bomVersionId: bomDetail.inherited_bom_version?.id,
          bomVersion: bomDetail.inherited_bom_version?.version_no,
          bomVersionActive: bomDetail.inherited_bom_version?.active
        }
      } else {
        this.inheritedFrom = null
      }
      this.$router
        .push(
          `/bom/product/${this.selectedProductCode}/bom/${this.selectedBomId}${
            this.bomVersionId ? `?bomVersionId=${this.bomVersionId}` : ''
          }`
        )
        .catch(() => {})
    },
    releaseBom () {
      ReleaseBomVersion(this.bomVersionId).then(() => {
        addComments('bom', this.bomId, 'BOM Released', null, this.bomVersionId)
        this.setup()
      })
    },
    setup () {
      this.loadingBomDetail = true
      this.bomVersionList = []
      this.bomVersionId = ''
      this.bomVersion = ''
      this.inheritedFrom = null
      this.bomState = 2
      const productCode = this.$route.params.productCode
      GetBomDetailById(this.selectedBomId).then((res) => {
        const bomDetail = res.core_bom_by_pk
        bomDetail.bom_versions = bomDetail.bom_versions.filter((bomVersion) => {
          if (bomVersion.state === 3) {
            if (bomVersion.checked_out_by !== this.user.userId) {
              return false
            }
          }
          return true
        })
        ProductCodeStatus(productCode).then(res => {
          if (res?.core_material_master[0]?.status === 2) {
            this.obseleteCheck = true
          } else this.obseleteCheck = false
        })
        this.setBomDetail(bomDetail)
        this.loadingBomDetail = false
        GetAllBomVersionListByBomId(this.selectedBomId).then((res) => {
          this.bomVersionList = res.bom_versions || []
          if (this.bomVersionId) {
            this.activeBomversion = false
            for (const version of this.bomVersionList) {
              if (version.id === this.bomVersionId && version.active) {
                this.activeBomversion = true
              }
            }
          }
        })
      })
    },
    updateInheritedBom (bomName, bomVersion) {
      // bomName, bomVersion are parent details
      if (this.bomVersion === 1) {
        alert(
          `There is no active verison for  " ${this.bomName.toUpperCase()} " to update `
        )
        return
      }
      ConfirmationDialog(
        `Are you sure to update  from  V-${bomVersion} of  ${bomName} to latest version`,
        (res) => {
          if (res) {
            this.checkandUpdateBOM()
          }
        },
        'Update',
        'Cancel',
        'Update Bom'
      )
    },
    // this function is to check inherited (current-bom) to its parent bom(from where current bom get inherited) and updte
    checkandUpdateBOM () {
      // getting parent bom itmes data
      const loader = new Loader()
      try {
        loader.show()
        GetBomDetailById(this.inheritedFrom.bomId).then((data) => {
          for (let i = 0; i < this.bomVersionList.length; i++) {
            if (this.bomVersionList[i]?.active) {
              this.activeBomversionId = this.bomVersionList[i]?.id
              break
            }
          }
          const child = GetAllBomItemsByBomVersionId(this.activeBomversionId)
          // this loop to get the latest  active version of parent  bom and to exclude if latest version is in check out state
          for (let i = 0; i < data?.core_bom_by_pk?.bom_versions.length; i++) {
            if (data?.core_bom_by_pk?.bom_versions[i]?.active) {
              this.newInheritedVersionId =
                data?.core_bom_by_pk?.bom_versions[i]?.id
              break
            }
          }
          const parentLatest = GetAllBomItemsByBomVersionId(
            // data?.core_bom_by_pk?.bom_versions[0]?.id
            this.newInheritedVersionId
          )
          const parentInitail = GetAllBomItemsByBomVersionId(
            this.inheritedFrom?.bomVersionId
          )
          Promise.allSettled([parentLatest, parentInitail, child]).then(
            (data) => {
              const [parentBomLatest, parentBomInitail, childBom] = data
              this.activeBomVersionItems = JSON.parse(JSON.stringify(childBom?.value?.bom_items))
              this.newBomData = compareAndUpdate(
                parentBomLatest?.value?.bom_items,
                parentBomInitail?.value?.bom_items,
                childBom?.value?.bom_items
              )
              this.newBomData.bom = this.newBomData.bom.map((mat) => {
                mat.selected = true
                return mat
              })
              this.updateInheritedBomModal = true
              loader.hide()
            }
          )
        })
      } catch (error) {
        loader.hide()
        alert('something went wrong')
      }
    },
    compareAndUpdate () {
      const loader = new Loader()
      loader.show()
      try {
        this.updateInheritedBomModal = false
        const bomItems = []
        this.newBomData.bom.forEach((bomItem) => {
          if (bomItem?.quantity > 0 && bomItem.selected) {
            // to remove all material with quantity greater than zero
            bomItems.push({
              material_id: bomItem.material_id,
              quantity: bomItem?.quantity,
              total_price: bomItem?.total_price,
              unit_size: bomItem?.unit_size,
              material_group: bomItem?.material_group,
              material_unit_cost: bomItem?.material_unit_cost,
              material_unit_sale_price: bomItem?.material_unit_sale_price,
              associated_product_code_bom: bomItem.associated_product_code_bom,
              bom_id: this.bomId,
              bom_version_id: this.bomVersionId
            })
          } else if (!bomItem.selected) {
            let revisedData = null
            for (const element of this.activeBomVersionItems) {
              if (element.material_id === bomItem.material_id) {
                revisedData = element
                break
              }
            }
            if (revisedData) {
              bomItems.push({
                material_id: revisedData.material_id,
                quantity: revisedData?.quantity,
                total_price: revisedData?.total_price,
                unit_size: revisedData?.unit_size,
                material_group: revisedData?.material_group,
                material_unit_cost: revisedData?.material_unit_cost,
                material_unit_sale_price: revisedData?.material_unit_sale_price,
                associated_product_code_bom: revisedData.associated_product_code_bom,
                bom_id: this.bomId,
                bom_version_id: this.bomVersionId
              })
            }
          }
        })
        deleteAllBomItemAndUpdate(
          this.bomId,
          this.bomVersionId,
          bomItems,
          this.newInheritedVersionId,
          false
        )
          .then((response) => {
            this.checkInBom()
            loader.hide()
            success('BOM updated Successfully')
          })
          .catch((error) => {
            console.log(error)
            loader.hide()
            alert('Something went wrong')
          })
      } catch (error) {
        loader.hide()
        alert('Something went wrong')
        console.log(error)
      }
    },
    closeModal () {
      this.updateInheritedBomModal = false
    },
    handleShareBom () {
      if (this.getLatestVersionId) {
        this.shareBomModal = true
      } else {
        alert(`There is no active verison for  " ${this.bomName.toUpperCase()} " to share `)
      }
    }
  },
  created () {
    this.setup()
    this.collabarotor =
      localStorage.getItem(Config.localstorageKeys.COLLABORATOR) === 'true'
  }
}
</script>

<style lang="scss" scoped>
.product-bom {
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-content: center;
  align-items: center;
  &-card {
    position: relative;
    height: 40px;
    width: 80px;
    font-size: 14px;
    background-color: var(--brand-light-color);
    line-height: 1;
    border: 1px solid var(--brand-color);
    border-radius: 8px;
    margin: 10px;
    font-weight: 500;
    padding: 10px;
    // word-break: break-word;
    text-align: center;
  }
}
.thiss {
  padding: 20px;
}

// .scroll-box {
//   padding: 8px;
//   max-height: 150px;
//   overflow-y: auto;
// }
.modal-buttons {
  display: flex;
  justify-content: flex-end;
}
.bom-detail {
  height: 100%;
  &-version {
    margin: 0 0 0 10px;
    font-size: 12px;
    background-color: var(--brand-light-color);
    line-height: 1;
    border: 1px solid var(--brand-color);
    border-radius: 4px;
    padding: 4px 12px;
  }
  table {
    width: 100%;
    position: relative;
    border-collapse: collapse;

    th {
      position: sticky;
      top: -1px;
      font-weight: 500;
      background-color: var(--brand-color);
    }

    th,
    td {
      text-align: left;
      font-size: 12px;
      padding: 8px 4px;
    }

    tr:nth-child(odd) {
      background-color: rgba(var(--brand-rgb), 0.05);
      border: 1px solid var(--brand-color);
    }
    tr:last-child {
      border-bottom: 1px solid var(--brand-color);
    }
  }
  &-bar {
    padding: 4px 12px;
    padding-left: 0;
    &-title {
      font-size: 16px;
      font-weight: 500;
    }
  }
  .bom-state-chip {
    font-size: 12px;
    line-height: 1;
    padding: 6px 12px;
    border-radius: 2em;
    font-weight: 500;
    &.CHECKIN {
      border: 1px solid var(--success);
      color: var(--success);
      background: #00ff002a;
      margin-inline: 1em;
    }
    &.CHECKOUT {
      border: 1px solid var(--warning);
      color: var(--warning);
      background: #ffff002a;
      margin-inline: 1em;
    }
    &.LOCK {
      border: 1px solid var(--alert);
      color: var(--alert);
      background: #ff00002a;
      margin-inline: 1em;
    }
    &.OBSOLETE {
      border: 1px solid var(--alert);
      color: var(--alert);
      background: #ff00002a;
      margin-inline: 1em;
    }
  }
  &-item {
    height: calc(100% - 40px);
    overflow-y: auto;
  }
  .inherit {
    cursor: pointer;
    position: relative;
    height: 22px;
    &:hover {
      .inherit-message {
        display: block;
      }
    }
    &-message {
      width: 200px;
      position: absolute;
      display: none;
      top: calc(100% + 10px);
      left: 0;
      background-color: var(--bg-color);
      color: #000;
      font-size: 12px;
      font-weight: 400;
      z-index: 1;
      padding: 4px 12px;
      transform: translate(-50%, 0);
      filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 50%;
        transform: translate(50%, -100%);
        border: 5px solid transparent;
        border-bottom-color: var(--bg-color);
      }

      & a {
        color: rgb(0, 140, 255);
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}
table {
  width: 100%;
  position: relative;
  border-collapse: collapse;

  th {
    position: sticky;
    top: -1px;
    font-weight: 500;
    background-color: var(--brand-color);
  }

  th,
  td {
    text-align: center;
    font-size: 16px;
    padding: 8px 4px;
  }

  tr:nth-child(odd) {
    background-color: rgba(var(--brand-rgb), 0.05);
    border: 1px solid var(--brand-color);
  }
  .deleted {
    color: red;
  }
}
.message-ul {
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding-inline: 20px;
  & li {
    list-style-type: disc;
  }
}
.update-btn-box {
  display: flex;
  justify-content: flex-end;
}
.deleted-msg {
  color: red;
}
.table-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  .footer-right {
    display: flex;
    flex-direction: row-reverse;
  }
  .footer-field {
    margin-left: 10px;
    label {
      margin-right: 5px;
      font-weight: bold;
    }
  }
}
.update_button {
  color: #008cff;
}
.update_button:hover {
  text-decoration-line: underline;
}
.elipsis-text {
  text-wrap: nowrap;
}
.existing {
  background-color: rgb(219 212 212) !important;
}
.accordion {
  width: 100%;
  border: 1px groove var(--brand-color);
  border-radius: 6px;
  margin: 10px 0px;
  max-height: 22vh;
  animation: myAnim 2s ease 0s 1 normal forwards;
}

.accordion-item {
  margin-bottom: 1rem;
  border-radius: 6px;
}

.accordion-header {
  padding-inline: 1.5rem;
  height: 2.5rem;
  cursor: pointer;
  border-bottom: 0.8px solid rgba(199, 197, 194, 0.5);
}

.accordion-content {
  padding: 0.5rem 0 1rem 1rem;
  animation: myAnim 2s ease 0s 1 normal forwards;
}

.bounce-enter-active {
  animation: bounce-in 0.3s;
}
.bounce-leave-active {
  animation: bounce-in 0.3s reverse;
}

@keyframes myAnim {
  0% {
    opacity: 0;
    transform: rotateX(-100deg);
    transform-origin: top;
  }

  100% {
    opacity: 1;
    transform: rotateX(0deg);
    transform-origin: top;
  }
}
@keyframes bounce-in {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}
</style>
