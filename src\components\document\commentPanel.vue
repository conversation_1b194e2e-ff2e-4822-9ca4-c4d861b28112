<template>
  <div class="comment-panel">
    <div class="comment-panel-header">
      <div class="comment-panel-header--title">Comments</div>
      <img src="~@/assets/images/icons/close-icon.svg" @click="$emit('closeComment')" width="20px" alt="" />
    </div>
    <div class="comment-panel-container">
      <div class="fh fw center" v-if="loading">
        <loading-circle />
      </div>
      <template v-else>
        <comment-card
          v-for="comment in comments"
          :commentData="comment"
          :key="comment.id"
          :level="3"
          @updateComment="getComments"
          :showEdit="showAddComment"
        />
      </template>
    </div>
    <div class="comment-panel-comment-box flex">
      <div
        contenteditable="true"
        class="comment-panel-comment-box--editor"
        @keyup="setMessage"
        ref="editor"
      ></div>
      <spinner class="spinner" v-if="commentSending"></spinner>
      <img
        v-else
        @click="sendComment"
        src="~@/assets/images/icons/send-icon.svg"
        alt=""
      />
    </div>
  </div>
</template>

<script>
import { addComments, getAllDocumentsComments } from '@/api'
import commentCard from './commentCard.vue'
import LoadingCircle from '../common/loadingCircle.vue'
import { mapGetters } from 'vuex'
import Spinner from '@/components/common/spinner.vue'

export default {
  components: { commentCard, LoadingCircle, Spinner },
  props: {
    versionDocId: {
      type: Object
    },
    document: {
      type: Object,
      default: () => {}
    }
  },
  data () {
    return {
      commentSending: false,
      comment: '',
      loading: false,
      comments: []
    }
  },
  computed: {
    ...mapGetters(['user', 'isOnProjectLevel', 'collaborator']),
    showAddComment () {
      if (this.collaborator) {
        return true
      } else if (this.isOnProjectLevel) {
        return this.user.projectLevelRole !== 'VIEWER'
      } else {
        return this.user.tenantLevelRole !== 'VIEWER'
      }
    }
  },
  methods: {
    setMessage (e) {
      if (e.keyCode === 13 && e.shiftKey) {
        e.target.innerText += '\n'
        return
      }
      if (e.keyCode === 13) {
        this.sendComment()
        return
      }
      this.comment = e.target.innerText
    },
    sendComment () {
      if (this.commentSending) return
      this.commentSending = true
      if (!this.comment.trim()) {
        this.commentSending = false
        return
      }
      addComments('document', this.document.id, this.comment, null).finally(
        () => {
          this.comment = ''
          this.$refs.editor.innerText = ''
          this.getComments()
          this.commentSending = false
        }
      )
    },
    getComments () {
      this.loading = true
      getAllDocumentsComments(this.versionDocId.id, null)
        .then((res) => {
          this.comments = res.core_comments || []
        })
        .finally(() => {
          this.loading = false
        })
    },
    keyPress (e) {
      if (e instanceof KeyboardEvent && e.code === 'Enter') {
        this.sendComment()
      }
    }
  },
  watch: {
    document () {
      this.getComments()
    }
  },
  mounted () {
    this.getComments()
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
  }
}
</script>

<style lang="scss" scoped>
.comment-panel {
  .spinner {
    margin-bottom: 4px;
    padding: 4px;
    width: 32px;
    height: 32px;
  }

  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 6px;
  background-color: var(--bg-color);
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px;
    margin: -6px;
    margin-bottom: 12px;
    background-color: var(--brand-color);
    &--title {
      font-size: 14px;
      font-weight: 500;
      color: var(--text-color);
    }
    img {
      cursor: pointer;
      opacity: 0.8;
      &:hover {
        opacity: 1;
      }
    }
  }
  &-container {
    flex-grow: 1;
    overflow-y: auto;
    margin-right: -6px;
    padding-right: 6px;
  }
  &-comment-box {
    padding: 10px;
    margin: -6px;
    margin-top: 0px;
    width: calc(100% + 12px);
    background-color: var(--bg-color);
    align-items: flex-end;
    gap: 10px;
    &--editor {
      width: 100%;
      min-height: 40px;
      padding: 10px;
      outline: none;
      border: 1px solid var(--brand-color);
      border-radius: 4px;
      resize: none;
      max-height: 100px;
      overflow: auto;
      font-size: 12px;
      font-weight: 400;
      line-height: 1.2;
      background-color: var(--white);
      color: var(--text-color);
      &:focus {
        outline: none;
      }
    }
    img {
      width: 32px;
      height: 32px;
      cursor: pointer;
      opacity: 0.8;
      margin-bottom: 4px;
      padding: 4px;
      border-radius: 50%;
      &:hover {
        background-color: rgba(0, 0, 0, 0.1);
      }
    }
  }
}
</style>
