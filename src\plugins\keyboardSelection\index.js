import './keyboardSelection.scss'

const keyboardSelection = {
  inserted: function (el, binding, vnode) {
    const items = el.querySelectorAll('li')
    let index = -1
    function highlightItem (newIndex) {
      items.forEach((item, i) => {
        if (i === newIndex) {
          item.classList.add('highlight')
        } else {
          item.classList.remove('highlight')
        }
      })
    }

    function handleKeydown (event, binding) {
      event.preventDefault()
      if (event.key === 'ArrowDown') {
        index = (index + 1) % items.length
        highlightItem(index)
      } else if (event.key === 'ArrowUp') {
        index = (index - 1 + items.length) % items.length
        highlightItem(index)
      } else if (event.key === 'Enter' && index !== -1) {
        items[index].click()
      }
    }

    // Ensure the element is focusable
    if (!el.hasAttribute('tabindex')) {
      el.setAttribute('tabindex', '0')
    }

    el.addEventListener('keydown', handleKeydown)

    el.__vueKeydownHandler__ = handleKeydown
  },

  beforeUnmount: function (el) {
    el.removeEventListener('keydown', el.__vueKeydownHandler__)
    delete el.__vueKeydownHandler__
  }
}
export default keyboardSelection
