import { runMutation } from './graphQl'
import { insertMaterialMasterDataQuery } from './query/materialMaster'
export const bulkUpload = (data, uploadingCallback) => {
  const total = data.length
  let count = 0
  let success = 0
  let error = 0
  data.forEach((item) => {
    item.loading = true
    runMutation(insertMaterialMasterDataQuery(), {
      objects: item.data
    }).then((res) => {
      count++
      item.loading = false
      item.done = true
      item.error = false
      item.errorMessage = ''
      uploadingCallback({
        count,
        total,
        success: ++success,
        error,
        data
      })
    }).catch(() => {
      count++
      item.loading = false
      item.done = true
      item.error = true
      item.errorMessage = 'Failed to upload'
      uploadingCallback({
        count,
        total,
        success,
        error: ++error,
        data
      })
    })
  })
  uploadingCallback({
    count,
    total,
    success,
    error,
    data
  })
}
