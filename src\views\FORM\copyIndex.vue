<template>
    <div class="form fh flex">
      <div class="form-template-list">
        <div class="form-template-list__header">
          <div class=" s p-2 v-center space-between">
            <h2 class="weight-500 l">Form Templates </h2>
          </div>
          <div class="input-group s">
            <input class="template-search" placeholder="Search Templates" v-model="searchKeyword"/>
          </div>
        </div>
        <RouterLink
          v-for="template in computedTemplateList"
          :key="template.id"
          :to="`/form/${template.id}/${template.name}`"
          :class= "{'router-link-active' : template.id == templateId }"

        >
          <div class="form-template-item v-center" v-if="Config.STANDARD_MATERIAL_FORM.form_type!==template?.form_type"     @click="templateClick(template)">
            <div class="form-template-item__name v-center s elipsis-text">
              <span>{{ template.name }} </span>
            </div>
            <div class="toggle-button center">
              <img src="~@/assets/images/down-arrow-icon.svg" alt=""/>
            </div>
          </div>
        </RouterLink>
      </div>
      <div class="form-components flex-grow">
        <router-view></router-view>
      </div>
    </div>
  </template>

<script>
import { getFormTemplates } from '@/api'
import Config from '@/config'
export default {
  name: 'FORM',
  data () {
    return {
      templateList: [],
      Config: Config,
      searchKeyword: '',
      templateId: this.$route.params.templateId
    }
  },
  computed: {
    computedTemplateList () {
      const computedTemplateListBasedOnKeyword = []
      for (let i = 0; i < this.templateList.length; i++) {
        if (this.templateList[i].name.toLowerCase().includes(this.searchKeyword.toLowerCase())) {
          computedTemplateListBasedOnKeyword.push(this.templateList[i])
        }
      }
      return computedTemplateListBasedOnKeyword
    }
  },
  watch: {
    '$store.state.projectIdForCollaborator' () {
      this.$router.replace('/form')
      this.fetchFormTemplates()
    }
  },
  methods: {
    // clears the old data while moving to different templates
    templateClick (template) {
      this.templateId = template.id
      this.$store.commit('form/setFormFieldsTypeMap', {})
    },
    fetchFormTemplates () {
      getFormTemplates().then((res) => {
        this.templateList = res.core_form_templates
        if (this.templateList.length > 0 && this.$route.path === '/form') {
          this.$router.push(
            `/form/${this.templateList[0].id}/${this.templateList[0].name}`
          )
        }
      })
    }
  },
  created () {
    this.fetchFormTemplates()
  }
}
</script>

  <style lang="scss" scoped >
  .form-template-list {

    .template-search {
      height: 2.5em;
      background-color: white;
    }

    width: 20%;
    min-width: 250px; // Adjust as needed
    height: 100%;
    overflow: auto;
    transition: width 0.7s ease-in-out, min-width 0.7s ease-in-out;
     background-color: var(--bg-color);
    // background-color: green;
    border-right: 1px solid var(--brand-color);
    position: relative;
    a {
      text-decoration: none;
      color: var(--text-color);
    }
    &__header {
      position: sticky;
      z-index: 1;
      top: 0;
      background-color: var(--brand-color);
    }
  }
  .form-template-item {
    border: 1px solid var(--brand-color);
    background-color: rgba(var(--brand-rgb), 0.2);
    margin: 6px 6px;
    padding-left: 6px;
    cursor: pointer;
    &__name {
      width: calc(100% - 30px);
      height: 30px;
    }
    .selected {
      background-color: rgba(var(--brand-rgb), 0.6);
    }
    .obsolete {
      border-color: var(--alert);
      color: var(--alert);
    }
    & .toggle-button {
      height: 30px;
      width: 30px;
      background-color: rgba(var(--brand-rgb), 1);
      cursor: not-allowed;
      pointer-events: none;
      opacity: 0.5;
      & img {
        height: 16px;
        width: 16px;
        transform: rotate(-90deg);
      }
    }
  }
  .router-link-active {
    .form-template-item {
      background-color: rgba(var(--brand-rgb), 0.6);
    }
    & .toggle-button {
      opacity: 1;
      pointer-events: all;
    }
  }
  @media screen and (max-width: 1366px) {
      .form-template-list {
          min-width: 200px;
          width: 15%;
      }
  }

  @media screen and (max-width: 1200px) {
      .form-template-list {
          min-width: 180px;
      }
  }

  @media screen and (max-width: 1024px) {
      .form-template-list {
          min-width: 150px;
      }
  }

  </style>
