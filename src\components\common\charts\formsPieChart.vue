<template>
  <div class="flex-column v-center h-center column">
<h4>{{label}}</h4>

    <div class="apexChart">
      <apexchart type="donut" :options="chartOptions" :series="chartSeries" @dataPointSelection="handleChartClick"/>
    </div>
  </div>
  </template>

<script>
import VueApexCharts from 'vue-apexcharts'

export default {
  components: {
    apexchart: VueApexCharts
  },
  props: {
    activeProjects: {
      type: Array,
      required: true
    },
    label: {
      type: String
    }
  },
  data () {
    return {
      chartOptions: {
        labels: [],
        plotOptions: {
          pie: {
            expandOnClick: false,
            donut: {
              labels: {
                show: true,
                name: {
                  show: false,
                  color: '#000000',
                  fontSize: '12px'
                },
                total: {
                  show: true,
                  showAlways: true,
                  label: 'Total',
                  color: '#000000',
                  fontSize: '12px'
                }
              }
            }
          },
          noData: {
            text: 'No Data Available',
            align: 'center',
            verticalAlign: 'middle',
            offsetX: 0,
            offsetY: 0,
            style: {
              color: 'black',
              fontSize: '14px'
            }
          }
        },
        legend: {
          show: false
        }
      },
      chartSeries: []
    }
  },

  watch: {
    activeProjects: {
      handler (newVal) {
        this.chartOptions.labels = newVal.map(item => item.template_name)
        this.chartSeries = newVal.map(item => parseInt(item.count))
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleChartClick (event, chartContext, config) {
      if (this.activeProjects[0].template_name === 'IDLE' || this.activeProjects[0].template_name === '"MAINTENANCE"' || this.activeProjects[0].template_name === 'RUNNING' || this.activeProjects[0].template_name === 'DOWN') {
        return
      }
      const labelName = this.chartOptions.labels[config.dataPointIndex]
      for (const form of this.activeProjects) {
        if (form.template_name === labelName) {
          this.$emit('goInside', `/form/${form.template_id}/${form.template_name}`)
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.apexChart {
    width:70%;
    height: 70%;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.v-center {
  justify-content: center;
}
.h-center {
  align-items: center;
}
</style>
