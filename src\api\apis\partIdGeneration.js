import { runMutation, runQuery } from '../graphQl'
import * as partIdQuery from '@/api/query/partIdGeneration'
// import store from '../../store'

export const getPartIdDataTypes = () => {
  return runQuery(partIdQuery.getPartIdDataTypesQuery(), {}, 'tenant')
}

export const saveSequenceTemplateGeneration = (templateInput, rules) => {
  // if (templateInput !== rules) return
  return runMutation(partIdQuery.saveSequenceTemplateGenerationQuery(), { template_input: templateInput, rules }, 'tenant')
}

export const GetTemplateData = (offset, limit, search) => {
  const conditions = {}
  if (search?.length) {
    conditions.name = { _ilike: `%${search}%` }
  }
  return runQuery(partIdQuery.getTemplateDataQuery(), { conditions, limit, offset }, 'tenant')
}

export const getTemplateById = (templateId) => {
  return runQuery(partIdQuery.getTemplateDetailsQuery(), { templateId }, 'tenant')
}

export const getTenantDefaults = () => {
  return runQuery(partIdQuery.getTenantDefaultsQuery(), {}, 'tenant')
}

export const GetIdGenerationRulesByFeatureId = (id) => {
  return runQuery(partIdQuery.GetIdGenerationRulesByFeatureIdQuery(), { id }, 'tenant')
}
