import { GQL } from '../graphQl'

export const GetAllStorageLocations = () => GQL`query GetAllStorageLocations($conditions:address_locations_bool_exp, $offset: Int!, $limit: Int!) {
    address_locations(where: $conditions, order_by: {name: asc},
    offset: $offset,
    limit: $limit) {
        address_line_1
        address_line_2
        city
        deleted
        id
        location
        created_on
        created_by_user {
          first_name
          last_name
        }
        state
        pincode
        name
      }
      address_locations_aggregate(where: $conditions) {
    aggregate {
      count
    }
  }
}`

export const CreateStorageLocation = () => GQL`mutation CreateStorageLocation($data: address_locations_insert_input!) {
  insert_address_locations_one(object: $data, on_conflict: {constraint: address_locations_name_tenant_id_key, update_columns: deleted, where: {deleted: {_eq: true}}}) {
    id
  }
}
`

export const UpdateStorageLocationById = () => GQL`mutation UpdateAddressLocations($data: address_locations_set_input!,$id:Int!) {
    update_address_locations_by_pk(_set: $data,pk_columns:{id:$id}) {
      id
      created_on
      created_by_user {
          first_name
          last_name
        }
    }
  }
  
`
