<template>
    <!--container  -->
    <div class="insights">
    <div class="insights-container">
      <!-- loop start here  -->
      <div
        class="insights-container-single_project p-2"
        v-for="(project, index) in dashBoardData"
        :key="index"
      >
        <div
          class="insights-container-projectbox v-center h-center p-2 text-center"
        >
          <div class="elipsis-text" v-overflow>
            {{ project.name }}
          </div>
        </div>
        <div class="insights-container-projectlist">
          <div
            class="v-center h-center column insights-container-databox gap-1"
          >
            <p>Project Value</p>
            <h3>₹ {{ project.cost }}</h3>
          </div>
          <div
            class="v-center h-center column insights-container-databox gap-1"
          >
            <p>Earned Value</p>
            <h3>₹ {{ (project.progress * project.cost) / 100 }}</h3>
          </div>
          <div
            class="v-center h-center column insights-container-databox gap-1"
          >
            <p>Project Duration</p>
            <h3>{{ project.duration  }}{{project.duration === 1?' Day': ' Days'  }}</h3>
            <p>
              {{ project.formattedStartDate }} to {{ project.formattedEndDate }}
            </p>
          </div>
          <div class="v-center h-center column insights-container-databox gap-1">
            <p>SPI value</p>
            <h3>{{ project.spi }}</h3>
          </div>
          <div
            class="v-center h-center column insights-container-databox gap-1"
          >
            <p>Total overdue forms</p>
            <h3>{{ project.overDueForms }}</h3>
          </div>
          <div
            class="v-center h-center column insights-container-databox gap-1"
          >
            <p>Total active forms</p>
            <h3>{{ project.activeForms }}</h3>
          </div>
          <div class="v-center h-center column insights-container-databox">
            <progressbar
              :percentage="project.progress"
              :value="project.progress"
            >
            </progressbar>
            <p>Progress</p>
          </div>
          <div class="v-center h-center insights-container-databox column">
            <progressbar
              :percentage="
                (project.completedMilestones / project.totalMileStones) * 100
              "
              :value="project.completedMilestones"
            >
            </progressbar>

            <p>Of {{ project.totalMileStones }} milestomes</p>
          </div>
          <div class="v-center h-center insights-container-databox column">
            <progressbar
              :percentage="(project.completedTasks / project.totalTasks) * 100"
              :value="project.completedTasks"
            >
            </progressbar>
            <p>Of {{ project.totalTasks }} tasks</p>
          </div>
          <div class="v-center h-center column insights-container-databox gap-1">
            <div class="container2">
              <formsPieChartVue v-if="project.chart.length > 0" :active-projects="project.chart" :label="'Resource Status'"
                      @goInside="goToSelectedView" />
            </div>
          </div>
        </div>
      </div>
      <!-- loop end here  -->
      <burnout-chart />
    </div>
  </div>
</template>

<script>
import progressbar from '@/components/common/progressbarCircle.vue'
import formsPieChartVue from '@/components/common/charts/formsPieChart.vue'
import {
  getinsightsDataApi,
  getDashBoardFormData,
  getProjectUserExceptViewer,
  getDashBoardResourceStatus
} from '@/api'
import { getDuration } from './insightsHelper'
import { mapGetters } from 'vuex'
import Loader from '@/plugins/loader'
export default {
  name: 'insights',
  components: { progressbar, formsPieChartVue },
  data: function () {
    return {
      dashBoardData: [],
      projectIds: [],
      resourceChartData: {}
    }
  },
  computed: {
    ...mapGetters(['user', 'adminProjetcts'])
  },
  methods: {
    async getinsightsData () {
      const loader = new Loader()
      loader.show()
      try {
        const associatedProjects = await getProjectUserExceptViewer(
          this.user.userId
        )
        this.projectIds = associatedProjects?.project_user_association.map(
          (obj) => {
            return obj.associated_project.id
          }
        )
        const formData = await getDashBoardFormData(this.projectIds)
        const formDataMapOverDue = {}
        const formDataMapActive = {}
        for (const overDue of formData.message.overdue) {
          if (Object.hasOwn(formDataMapOverDue, overDue.project_name)) {
            formDataMapOverDue[overDue.project_name] += parseInt(
              overDue?.count
            )
          } else {
            formDataMapOverDue[overDue.project_name] = parseInt(overDue?.count)
          }
        }
        for (const active of formData.message.active) {
          if (Object.hasOwn(formDataMapOverDue, active?.project_name)) {
            formDataMapActive[active.project_name] += parseInt(active.count)
          } else {
            formDataMapActive[active.project_name] = parseInt(active.count)
          }
        }
        const resourceData = await getDashBoardResourceStatus()
        this.resourceChartData = this.changeResourceArray(resourceData?.message)
        const tenantId = localStorage.getItem('tenantId')
        getinsightsDataApi(tenantId)
          .then((res) => {
            this.dashBoardData = []
            for (const project of res.core_tasks) {
              project.chart = this.resourceChartData[project?.core_project?.id] || []
              const projectData = { ...project }
              projectData.duration = getDuration(
                project.planned_start_date,
                project.planned_end_date
              )
              projectData.formattedStartDate = new Date(
                project.planned_start_date
              )
                .toLocaleDateString('en-US')
                .replaceAll('/', '-')
              projectData.formattedEndDate = new Date(project.planned_end_date)
                .toLocaleDateString('en-US')
                .replaceAll('/', '-')
              projectData.totalMileStones =
                project?.core_project?.total_milestones?.aggregate?.count || 0
              projectData.totalTasks =
                project?.core_project?.total_tasks?.aggregate?.count || 0
              projectData.completedMilestones =
                project?.core_project?.completed_milestones?.aggregate?.count ||
                0
              projectData.completedTasks =
                project?.core_project?.completed_tasks?.aggregate?.count || 0
              projectData.overDueForms = formDataMapOverDue[project.name] ?? 0
              projectData.activeForms = formDataMapActive[project.name] ?? 0
              this.dashBoardData.push(projectData)
            }
            loader.hide()
          })
          .catch((err) => {
            console.log(err)
            loader.hide()
          })
      } catch (error) {
        loader.hide()
      }
    },
    changeResourceArray (resourceArray) {
      const transformedData = resourceArray.reduce((acc, item) => {
        if (!acc[item.project_id]) {
          acc[item.project_id] = []
        }
        acc[item.project_id].push({
          template_name: item.state,
          count: item.count
        })
        return acc
      }, {})
      return transformedData
    }
  },
  mounted () {
    this.getinsightsData()
  }
}
</script>

<style lang="scss" scoped>
.container2 {
  padding-top: 10px;
  width: 100%;
  height: 100%;
}
.insights {
  &-bar {
    height: 60px;
    margin: -12px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  &-container {
    height: calc(100% - 37px);
    overflow-y: auto;
    padding: 12px;
    &-single_project {
      display: grid;
      grid-template-columns: 15% 85%;
      grid-column-gap: 12px;
      grid-row-gap: 12px;
      margin-top: 1rem;
      &:hover {
        background-color: rgba(var(--brand-color), 0.2);
        cursor: pointer;
      }
    }
    &-projectbox {
      border: 1px groove rgb(194, 188, 188, 0.4);
      border-radius: 6px;
      height: 100%;
      animation: myAnim 0.6s ease-in 0s 1 normal forwards;
      font-size: 1.3rem;
      text-wrap: wrap;
    }
    &-projectlist {
      display:grid;
      grid-template-columns: repeat(auto-fill, minmax(16rem, 1fr));
      column-gap: 1rem;
      row-gap: .5rem;
    }
    &-databox {
      border-radius: 6px;
      border: 1px groove rgb(194, 188, 188, 0.4);
 width:100%;
 height:12rem;
      animation: myAnim 0.6s ease-in 0s 1 normal forwards;

      & p {
        font-size: smaller;
        font-weight: 200;
        margin-top: 0.3rem;
      }
    }
    &-progressbox {
      position: relative;
      height: 100%;
      &-inner {
        height: 7rem;
        width: 7rem;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        position: absolute;
        border-radius: 100%;
      }
      &-outer {
        height: 6rem;
        width: 6rem;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        position: absolute;
        border-radius: 100%;
        background-color: rgb(169, 209, 25);
        z-index: 2;
      }
    }
  }
}
@keyframes myAnim {
  0% {
    opacity: 0;
    transform: scale(0.6);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}
.toggle {
  font-size: 1em;
  padding: 0.5em 1.2em;
  color: var(--black);
  font-weight: 500;
  border: none;
  box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
  background-color: var(--side-bar-color);

  &:active {
    box-shadow: inset 0.2em 0.2em 0.2em rgba(0, 0, 0, 0.25);
  }

  &-left {
    border-radius: 0.3rem 0px 0px 0.3rem;
  }

  &-right {
    border-radius: 0px 0.3rem 0.3rem 0px;
  }

  &-selected {
    background-color: var(--brand-color);
  }
}
</style>
