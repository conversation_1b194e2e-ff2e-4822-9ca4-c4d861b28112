<template>
    <div class="copy-dtx-table">
    <table class="" :class="{ admin: isTenantAdmin }">
        <thead v-if="showHeader">
      <tr class="m">
        <th>Name</th>
        <th>Contact No</th>
        <th>Email</th>
        <th>Industry Vertical</th>
        <th v-if="beaconAdmin">Type</th>
        <th v-show="(isTenantAdmin | beaconAdmin) && !isOnProjectLevel">Action</th>
      </tr>
    </thead>
      <tbody v-if="!loading && displayData.length">
        <tr class="s" :class="{'deactivatedRow':row.status===2 || row.associationStatus === 2}" v-for="(row,index) in displayData" :key="row.id">
        <td>
          <a style="display: inline-block; width: 25px; height: 20px;">
            <img v-if="row.company_logo_blob_key" :src="row.company_logo_blob_key"  alt=""/>
          </a>
          {{ row.company_name || '--' }}</td>
        <td>{{ row.company_phone || '--' }}</td>
        <td>{{ row.company_email || '--' }}</td>
        <td>{{ row.industry_vertical_value.name || '--' }}</td>
        <td v-if="beaconAdmin">{{ row.tenant_type_details.type || '--' }}</td>
        <td v-show="(isTenantAdmin || beaconAdmin) && !isOnProjectLevel" class="action-column">
        <div class="action-icons">
          <a style="display: inline-block; width: 25px;" :href="row.company_url" target="_blank">
          <img class="edit pointer" v-if="row.company_url"  v-tooltip="'Company URL'"  src="~@/assets/images/url.svg" alt=""/> </a>
          <span v-tooltip="'Deactivate tenant'" class="btn pointer" v-if="row.status===1 && beaconAdmin" @click="updateTenantStatus(index, 2)">
            <img src="~@/assets/images/delete-icon.svg" alt="" width="20" height="20"/>
          </span>
            <img v-tooltip="'To De-activate tenant'" class="edit pointer" v-show="row.associationStatus===1 && row.status === 1 && !beaconAdmin" src="~@/assets/images/deactive.svg" alt=""  @click="deactivateCollaboratingTenant(row, index)"/>
            <img v-tooltip="'To activate tenant'" class="edit pointer" v-show="row.associationStatus===2 && !beaconAdmin" src="~@/assets/images/active.svg" alt="" @click="activateCollaboratingTenant(row, index)"/>
            <img v-tooltip="'Re-Invite tenant'" class="edit pointer" v-if="row.status===4" src="~@/assets/images/icons/invite.svg" alt=""  @click="tenantReInvite(row.id)"/>
            <img v-else-if="row.status!==1 && beaconAdmin" src="~@/assets/images/icons/undo-icon.svg" class="edit pointer" alt="" @click="updateTenantStatus(index, 1)"/>
                <img  v-tooltip="'Edit'" src="~@/assets/images/pencil.svg" alt=""  class="edit pointer" @click="EditCompany(row.id)"/>
              </div>
        </td>
      </tr>
      </tbody>
      <tbody v-else-if="!loading && displayData.length === 0">
        <tr>
      <td :colspan="8" class="no-data">No Data Found</td>
    </tr>
      </tbody>
      <tbody v-else>
        <tr>
          <td colspan="6">
            <div class="center">
              <loading-circle />
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  </template>

<script>
import LoadingCircle from './loadingCircle.vue'
import { alert, success } from '@/plugins/notification'
import ConfirmationDialog from '@/plugins/confirmationDialog'
import { ReInviteTenant, UpdateTenantStatusBeaconAdmin, updateCollaboratingTenantStatus } from '@/api'
import { mapGetters, mapMutations } from 'vuex'
export default {
  components: { LoadingCircle },
  name: 'company-table',
  props: {
    companyList: {
      type: Array,
      default: () => []
    },
    pageNumber: {
      type: [Number, String],
      default: 1
    },
    perPage: {
      type: [Number, String],
      default: 10
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    beaconAdmin: {
      type: Boolean,
      default: false
    },
    selectedTab: {
      type: String
    }
  },
  data () {
    return {
      showTooltip: false,
      disableReIniviteButton: false
    }
  },
  methods: {
    EditCompany (rowId) {
      this.$router.push(`/edit-user/${rowId}`)
    },
    ...mapMutations(['setTenentList', 'setChildTenantList']),
    async updateTenantStatus (index, status) {
      ConfirmationDialog(`Are you sure you want to ${status === 1 ? 'activate' : 'deactivate'} the tenant ${this.companyList[index].company_name}`, async (res) => {
        if (res) {
          try {
            await UpdateTenantStatusBeaconAdmin(this.companyList[index].id, status)
            this.$notify.success('Successfully updated the tenant status')
            const tenantList = JSON.parse(JSON.stringify(this.companyList))
            tenantList[index].status = status
            this.setTenentList(tenantList)
          } catch (err) {
            this.$notify.alert(err?.message ?? 'Something went wrong')
          }
        }
      })
    },
    deactivateCollaboratingTenant (tenant, index) {
      ConfirmationDialog(`Are you sure you want to deactivate the tenant ${tenant.company_name}`, (res) => {
        if (res) {
          updateCollaboratingTenantStatus(tenant.id, 2).then((res) => {
            tenant.associationStatus = 2
            const tenantList = JSON.parse(JSON.stringify(this.companyList))
            tenantList[((this.pageNumber - 1) * this.perPage) + index].status = 2
          }).catch((err) => {
            console.log(err)
            alert('Unable to deactivate tenant')
          })
        }
      })
    },
    activateCollaboratingTenant (tenant, index) {
      ConfirmationDialog(`Are you sure you want to activate the tenant ${tenant.company_name}`, (res) => {
        if (res) {
          updateCollaboratingTenantStatus(tenant.id, 1).then((res) => {
            tenant.associationStatus = 1
            const tenantList = JSON.parse(JSON.stringify(this.companyList))
            tenantList[((this.pageNumber - 1) * this.perPage) + index].status = 1
          }).catch(() => {
            alert('Unable to activate tenant')
          })
        }
      })
    },
    tenantReInvite (tenantId) {
      if (this.disableReIniviteButton) return
      this.disableReIniviteButton = true
      const body = {
        tenant_id: tenantId
      }
      ReInviteTenant(body).then(data => {
        if (data.message === 'Invitation Resent') {
          success('Tenant Re-invited successfully')
        } else {
          alert('There was an error, re-inviting the Tenant.')
        }
      }).catch(() => {
        alert('There was an error, re-inviting the Tenant. Please try again')
      }).finally(() => {
        this.disableReIniviteButton = false
      })
    }
  },
  computed: {
    ...mapGetters(['isTenantAdmin', 'isOnProjectLevel']),
    ...mapGetters(['tenantList', 'childTenantsList', 'tenantType']),

    ...mapGetters(['user']),
    displayData () {
      return this.companyList.slice(
        (this.pageNumber - 1) * this.perPage,
        this.pageNumber * this.perPage
      )
    }
  }
}
</script>
  <style lang="scss" scoped>
  .no-data {
    text-align: center;
    vertical-align: middle;
  }
  </style>
