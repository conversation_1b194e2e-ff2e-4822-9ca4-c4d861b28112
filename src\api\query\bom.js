import { GQL } from '../graphQl'

export const GetAllBomListByProductCodeQuery = () => GQL`query GetAllBomListByProductCodeQuery($conditions: core_bom_bool_exp) {
  core_bom(where: $conditions, order_by: {generated_on: desc}) {
    id
    name
    state
    checked_out_by
    generated_by
    bom_versions {
      id
      version_no
      active
      released
      total_cost
      sale_price
    }
  }
}
`
export const GetBomForSwitching = () => GQL`query GetBomForSwitchingQuery($product_code: uuid, $name: String){
  core_bom(where: {product_code: {_eq: $product_code}, name: {_eq: $name}}) {
    id
    bom_versions {
      id
    }
  }
}
`

export const GetBomForSwitchingProject = () => GQL`query GetBomForSwitchingProjectQuery($projectId: uuid, $name: String) {
  core_bom(where: {name: {_eq: $name}, project_id: {_eq: $projectId}}) {
    id
    bom_versions {
      id
    }
  }
}`

export const CreateProductBomQuery = () => GQL`mutation CreateProductBomQuery(  
  $product_code_id: uuid
  $name: String!
  $items: [bomItemsArray]!
  $inherited_from_bom_id: uuid
  $inherited_from_bom_version_id: uuid
  $effective_date: Date
  $custom_field_data: [customFieldData]
  $form_template_version_id: uuid) {
  insert_bom(
    product_code_id: $product_code_id
    name: $name
    items: $items
    inherited_from_bom_id: $inherited_from_bom_id
    inherited_from_bom_version_id: $inherited_from_bom_version_id
    effective_date: $effective_date
    custom_field_data: $custom_field_data
    form_template_version_id: $form_template_version_id
  ) {
    message
  }
}`
export const UpdateProductBomQuery = () => GQL`mutation UpdateProductBomQuery(
  $bom_items: [bom_items_insert_input!]!,
  $bom_id: uuid, $custom_fields: jsonb) {
  insert_bom_versions(
    objects: {
      bom_id: $bom_id,
      bom_items: {data: $bom_items},
      custom_fields: $custom_fields
    }
  ) {
    affected_rows
  }
}`

export const UpdateBomStateQuery = () => GQL`mutation UpdateBomStateQuery($id: uuid, $state: Int) {
  update_core_bom(where: {id: {_eq: $id}}, _set: {state: $state}) {
    affected_rows
  }
}
`
// this query to update last version before checkouted bom will set as active
export const setLastBomVersionActiveQuery = () => GQL`mutation setLastBomVersionActiveQuery($id: uuid, $state: Int) {
  update_bom_versions(where: {id: {_eq: $id}}, _set: {state:$state}) {
    affected_rows
  }
}
`

export const GetAllBomVersionListByBomIdQuery = () => GQL`query GetAllBomVersionListByBomIdQuery($bomId: uuid!) {
  bom_versions(where: {bom_id: {_eq: $bomId}}, order_by: {version_no: desc}) {
    id
    custom_fields
    version_no
    active
    state
    checked_out_by
    released
    total_cost
    sale_price
  }
}`

export const GetBomDetailByIdQuery = () => GQL`query GetBomDetailByIdQuery($id: uuid!) {
  core_bom_by_pk(id: $id) {
    name
    effective_date
    id
    state
    checked_out_by
    generated_by
    bom_versions(order_by: {version_no: desc}) {
      active
      version_no
      id
      checked_out_by
      state
      released
      sale_price
      total_cost
    }
    inherited_bom_version {
      version_no
      core_bom {
        name
        id
      }
      id
      active
    }
  }
}`

export const GetBomDetailByIdAndBomVersionQuery = () => GQL`query GetBomDetailByIdAndBomVersionQuery($id: uuid!, $versionId: uuid!) {
  core_bom_by_pk(id: $id) {
    name
    id
    checked_out_by
    state
    bom_versions(where: {id: {_eq: $versionId}}, order_by: {active: desc}) {
      active
      version_no
      id
      checked_out_by
      state
      released
      total_cost
      sale_price
    }
  }
}`

export const GetAllBomItemsByBomVersionIdQuery = () => GQL`query GetAllBomItemsByBomVersionIdQuery($bom_version: uuid) {
  bom_items(where: {bom_version_id: {_eq: $bom_version}}) {
    associated_product_code_bom
    overridden_material_fields
    bom_version{
      custom_fields
    }
    associated_bom_version {
      version_no
      id
      active
      released
      total_cost
      sale_price
      core_bom {
        id
        name
        state
      }
    }
    material_id
    quantity
    total_price
    unit_size
    remarks
    material_group
    resource_group
    material_unit_cost
    material_unit_sale_price
    material_values_overridden
    core_material {
      material_group
      resource_group
      type
      custom_material_id
      erp_material_id
      blob_reference_key
      id
      gross_weight
      inventory
      lead_time
      material_description
      material_group
      material_name
      material_type
      others
      parent_id
      plm_material_id
      plm_record_id
      quantity
      status
      storage_loc
      unit_cost
      unit_of_material
      core_form {
      id
      }
      material_unit_details {
        name
        id
      }
      unit_sale_price
      weight_unit
      product_code
      material_product_code {
        id
        product_code
      }
      material_group_details{
        name
        id
    }
    resource_group_details {
      name
      id
    }
    }
  }
}`

export const GetBomForCopyAndInheritQuery = () => GQL`query GetBomForCopyAndInheritQuery($id: uuid!) {
  core_bom_by_pk(id: $id) {
    name
    product_code
    bom_versions(where: {active: {_eq: true}}) {
      id
      custom_fields
      released
      bom_items {
        material_id
        quantity
        total_price
        unit_size
        material_group
        material_unit_cost
        material_unit_sale_price
        associated_product_code_bom
        associated_bom_version {
          id
        }
      }
    }
  }
}
`

export const InserBomItemsQuery = () => GQL`mutation InserBomItemsQuery(
  $bom_items: [bom_items_insert_input!]!) {
  insert_bom_items(objects: $bom_items) {
    affected_rows
  }
}
`
export const updateBomNameQuery = () => GQL`mutation updateBomNameQuery($bomId: uuid, $name: String) {
  update_core_bom(where: {id: {_eq: $bomId}}, _set: {name: $name}){
    affected_rows
  }
}
`
export const UpdateBomItemsQuery = () => GQL`mutation UpdateBomItems(
  $bom_version_id: uuid!,
  $material_id: uuid!,
  $unit_size: float8!,
  $previous_unit_size: float8!,
  $total_price: Int,
  $remarks: String,
  $quantity: float8,
  $material_unit_sale_price: float8,
  $material_unit_cost: float8,
  $material_group: Int,
  $material_values_overridden: Boolean,
  $overridden_material_fields: jsonb,
  $associated_product_code_bom: uuid) {
  update_bom_items_by_pk(
    pk_columns: {
      bom_version_id: $bom_version_id,
      material_id: $material_id,
      unit_size: $previous_unit_size
    }
    _set: {
      material_group: $material_group,
      material_values_overridden: $material_values_overridden,
      overridden_material_fields: $overridden_material_fields,
      material_unit_cost: $material_unit_cost,
      material_unit_sale_price: $material_unit_sale_price,
      associated_product_code_bom: $associated_product_code_bom,
      quantity: $quantity,
      remarks: $remarks,
      total_price: $total_price,
      unit_size: $unit_size}
  ) {
    quantity
    remarks
    total_price
    unit_size
    bom_id
    material_unit_sale_price
    material_unit_cost
    material_group
    material_values_overridden
  }
}
`

export const DeleteBomItemsQuery = () => GQL`mutation DeleteBomItemsQuery($bom_version_id: uuid, $material_id: uuid, $unit_size: float8) {
  delete_bom_items(
    where: {bom_version:{id: {_eq: $bom_version_id}}, material_id: {_eq: $material_id}, unit_size: {_eq: $unit_size}}
  ){
    affected_rows
  }
}
`

export const ReleaseBomVersionQuery = () => GQL`mutation ReleaseBomVersionQuery($id: uuid) {
  update_bom_versions(where: {id: {_eq: $id}}, _set: {released: true}) {
    affected_rows
  }
}
`
export const DeleteBomVersionQuery = () => GQL`mutation DeleteBomVersionQuery($id: uuid!) {
  delete_bom_versions_by_pk(id: $id) {
    id
  }
}
`
export const DeleteBomItemsByVersionIDQuery = () => GQL`mutation DeleteBomItemsByVersionIDQuery($bom_version_id: uuid) {
  delete_bom_items(where: {bom_version_id: {_eq: $bom_version_id}}) {
    affected_rows
  }
}`
// Project Bom

export const CreateProjectBomQuery = () => GQL`mutation CreateProjectBomQuery(
  $name: String!,
  $items: [bomItemsArray],
  $inherited_from_bom_id: uuid,
  $inherited_from_bom_version_id: uuid,
  $effective_date: Date,
 $custom_field_data: [customFieldData],
 $form_template_version_id: uuid
  ) {
  insert_bom(
    name: $name,
    items: $items,
    inherited_from_bom_id: $inherited_from_bom_id,
    inherited_from_bom_version_id: $inherited_from_bom_version_id,
    effective_date: $effective_date
    custom_field_data: $custom_field_data,
    form_template_version_id: $form_template_version_id
    ) {
    message
  }
}`
export const GetAllProjectBomListQuery = () => GQL`query GetAllProjectBomListQuery($conditions: core_bom_bool_exp)  {
  core_bom(where: $conditions, order_by: {generated_on: desc}) {
    id
    name
    state
    checked_out_by
    generated_by
    bom_versions {
      id
      version_no
      active
      state
    }
    project_id
  }
}`

export const GetProjectBomDetailByIdQuery = () => GQL`query GetProjectBomDetailByIdQuery($id: uuid!) {
  core_bom_by_pk(id: $id) {
    name
    effective_date
    id
    state
    checked_out_by
    bom_versions(order_by: {version_no: desc}) {
      active
      version_no
      id
      checked_out_by
      state
      sale_price
      total_cost
      released
    }
    inherited_bom_version {
      version_no
      core_bom {
        name
        id
        checked_out_by
        state
      }
      id
      active
    }
  }
}`
export const GetProjectBomDetailByIdAndBomVersionQuery = () => GQL`query GetProjectBomDetailByIdAndBomVersionQuery($id: uuid!, $versionId: uuid!) {
  core_bom_by_pk(id: $id) {
    name
    id
    bom_versions(where: {id: {_eq: $versionId}}) {
      active
      version_no
      id
    }
  }
}`
export const GetAllProjectBomItemsByBomVersionIdQuery = () => GQL`query GetAllProjectBomItemsByBomVersionIdQuery($bom_version: uuid) {
  bom_items(where: {bom_version: {id: {_eq: $bom_version}}}) {
    associated_product_code_bom
    overridden_material_fields
    associated_bom_version {
      version_no
      id
      active
      core_bom {
        id
        name
        state
      }
      sale_price
      total_cost
    }
    material_id
    quantity
    total_price
    unit_size
    material_group
    material_unit_cost
    material_unit_sale_price
    remarks
    core_material {
      custom_material_id
      erp_material_id
      blob_reference_key
      id
      gross_weight
      inventory
      lead_time
      material_description
      material_group
      material_name
      material_type
      others
      parent_id
      plm_material_id
      plm_record_id
      quantity
      status
      storage_loc
      unit_cost
      unit_of_material
      unit_sale_price
      weight_unit
      product_code
      type
      core_form {
      id
      }
      material_product_code {
        id
        product_code
      }
      material_unit_details {
        name
        id
      }
    }
    associated_product_code_bom
  }
}
`
export const GetAllChildProductForProjectBomQuery = () => GQL`query GetAllChildProductForProjectBomQuery($id: uuid!) {
  bom_versions_by_pk(id: $id) {
    bom_items(where: {core_material: {product_code: {_is_null: false}}}) {
      associated_bom_version {
        core_bom {
          id
          name
        }
        active
        bom_id
        id
        version_no
      }
      core_material {
        material_product_code {
          product_code
          id
        }
      }
    }
  }
}
`
export const updateEffectiveDateQuery = () => GQL`mutation updateEffectiveDateByID($id: uuid, $effective_date: date) {
  update_core_bom_by_pk(
    pk_columns: {id: $id}
    _set: {effective_date: $effective_date}
  ) {
    effective_date
  }
}`
export const updateAssociatedBomversionMutate = () => GQL`mutation updateAssociatedBomversionMutate($bom_version_id: uuid!, $material_id: uuid!, $unit_size: float8!, $associated_product_code_bom: Int!) {
  update_bom_items_by_pk(
    pk_columns: {bom_version_id: $bom_version_id, material_id: $material_id, unit_size: $unit_size}
    _set: {associated_product_code_bom: $associated_product_code_bom}
  ) {
    associated_product_code_bom
  }
}
`
export const updateBomVersionQuery = () => GQL`mutation updateBomVersionQuery($bomVersion: uuid, $state: Int) {
  update_bom_versions(where: {id: {_eq: $bomVersion}}, _set: {state: $state}) {
    affected_rows
  }
}
`
export const deleteAllBomItemAndUpdateQuery = () => GQL`mutation deleteAllBomItemAndUpdateQuery($bom_version_id: uuid!, $bom_id: uuid!, $bom_items: [bom_items_insert_input]!, $inherited_from_bom_version_id: uuid!) {
  delete_bom_items(
    where: {_and: {bom_version: {id: {_eq: $bom_version_id}, bom_id: {_eq: $bom_id}}}}
  ) {
    affected_rows
  }
  insert_bom_items(objects: $bom_items) {
    affected_rows
  }
  update_core_bom(
    where: {_and: {bom_versions: {id: {_eq: $bom_version_id}}, id: {_eq: $bom_id}}}
    _set: {inherited_from_bom_version_id: $inherited_from_bom_version_id}
  ) {
    affected_rows
  }
}
`

export const deleteAllBomItemsMutation = () => GQL`mutation deleteAllBomItemsMutation($bom_version_id: uuid!, $bom_id: uuid!) {
  delete_bom_items(
    where: {_and: {bom_version: {id: {_eq: $bom_version_id}, bom_id: {_eq: $bom_id}}}}
  ) {
    affected_rows
  }
}
`

export const deleteAllBomItemsAndInsertMutation = () => GQL`mutation deleteAllBomItemsMutation($bom_version_id: uuid!, $bom_id: uuid!, $bom_items: [bom_items_insert_input!]!) {
  delete_bom_items(
    where: {_and: {bom_version: {id: {_eq: $bom_version_id}, bom_id: {_eq: $bom_id}}}}
  ) {
    affected_rows
  }
  insert_bom_items(objects: $bom_items) {
    affected_rows
  }
}
`
// this query is used to check cyclic dependency
export const getBomDatawithAssociatedBoms = () => GQL`query getBomDatawithAssociatedBoms($bom_version_id: uuid!) {
  bom_items(
    where: {bom_version_id: {_eq: $bom_version_id}, associated_product_code_bom: {_is_null: false}, associated_bom_version: {id: {_is_null: false}}}
  ) {
    bom_id
    bom_version_id
    material_id
    core_material {
      product_code
      id
    }
    associated_product_code_bom
    associated_bom_version {
      total_cost
      sale_price
      active
      bom_id
      state
      version_no
      id
    }
  }
}
`
export const updateStdBomVersion = () => GQL`mutation updateStdBomVersion($bom_version_id: uuid!, $custom_fields_data: jsonb) {
  update_bom_versions(
    where: {id: {_eq: $bom_version_id}}
    _set: {custom_fields: $custom_fields_data}
  ) {
    affected_rows
  }
}`

export const shareBomToCollaborator = () => GQL`mutation shareBomToCollaborator ( $data: [collaborator_boms_insert_input!]! ){
  insert_collaborator_boms(objects: $data) {
    returning{
      id
    }
  }
}`

export const removeSharedBomAccessOfCollaborator = () => GQL`mutation deleteCollaborator($id: Int!){
  delete_collaborator_boms_by_pk(id: $id) {
    bom_id
  }
}
`

export const getUsersWithAccessToShared = () => GQL`query getUsersWithAccessToSharedBom($bomId: uuid!) {
  collaborator_boms(where: {bom_id: {_eq: $bomId}}) {
    id
    tenant_user_association_by_target {
      associated_user {
        first_name
        last_name
        id
      }
    }
    created_by_user {
      id
      first_name
      last_name
    }
  }
}`
