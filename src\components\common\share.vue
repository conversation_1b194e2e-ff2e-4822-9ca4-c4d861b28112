<template>
  <div class="shareBody">
    <div v-if="peopleWithAccess.length">
      <h3>People with access</h3>
      <div class="my-2 mt-4">
        <div class="accessList" v-for="client in peopleWithAccess" :key="client.id">
          <span>{{ client.name }}</span>
          <div class="s">
            <button v-if="isOnProjectLevel && (user.projectLevelRole === 'ADMIN'|| ( user.userId===client.sharedById && (user.projectLevelRole === 'COLLABORATOR' || user.projectLevelRole === 'EDITOR')))" class="btn mr-3 pointer" @click="removeAccess(client.id, client.name, client.assocId)">Remove access</button>
            <button v-if="!isOnProjectLevel && (user.tenantLevelRole === 'ADMIN'|| ( user.userId===client.sharedById && (user.tenantLevelRole === 'COLLABORATOR' || user.tenantLevelRole === 'EDITOR')))" class="btn mr-3 pointer" @click="removeAccess(client.id, client.name, client.assocId)">Remove access</button>
          </div>
        </div>
      </div>
    </div>
    <div class="shareBody-select">
      <dropdown
        :list="collaborators"
        :selected="selectedTenant"
        @selected="selectTenant"
      />
      <div class="emptyList" v-if="selectedTenant.id===-1">Please select any tenant</div>
      <div v-else-if="tenantUsers.length" class="usersList">
        <div class="boxes">
          <multiselect-dropdown label="Select users" :options="tenantUsers" @selected="setSelectedUsersToShare" :initiallySelected="initiallySelectedUsers"/>
        </div>
        <div class="s flex-end">
          <button class="btn btn-black mr-2" @click="$emit('close')">Cancel</button>
          <button class="btn" @click="handleShareBom" :disabled="selectedUsersToShareMap.length===0">Share</button>
        </div>
      </div>
      <div v-else class="emptyList" >
         There is no users to share in this tenant
      </div>
    </div>
  </div>
</template>

<script>
import Dropdown from './dropdown.vue'
import { mapGetters } from 'vuex'
import { alert, success } from '../../plugins/notification'
import Loader from '@/plugins/loader'
import { GetAllCollabaratorList, ShareBom, GetUsersWithAccessToSharedBom, RemoveSharedBomAccess, removeDocAccess, ShareDocument, GetUsersWithAccessToShareDocument, addComments } from '@/api'
import MultiselectDropdown from '../common/multiselectDropdown.vue'

export default {
  name: 'shareBomBody',
  data: () => ({
    collaborators: {},
    selectedTenant: { id: -1, name: 'Select tenant' },
    tenantUsers: [],
    selectedUsersToShareMap: {},
    peopleWithAccess: [],
    initiallySelectedUsers: []
  }),
  props: {
    id: {
      // this is bom id
      required: true
    },
    versionId: {
      type: String,
      required: true
    },
    item: {
      type: String,
      required: true
    },
    level: {
      type: String,
      default: 'master'
    }
  },
  components: {
    Dropdown,
    MultiselectDropdown
  },
  computed: {
    ...mapGetters(['user', 'tenantList', 'isOnProjectLevel']),
    showRemoveButtonProject () {
      return this.isOnProjectLevel && (this.user.projectLevelRole === 'ADMIN' || this.user.projectLevelRole === 'COLLABORATOR' || this.user.projectLevelRole === 'EDITOR')
    },
    showRemoveButtonMaster () {
      return !this.isOnProjectLevel && (this.user.projectLevelRole === 'ADMIN' || this.user.projectLevelRole === 'COLLABORATOR' || this.user.projectLevelRole === 'EDITOR')
    }

  },
  methods: {
    removeAccess (id, name, assocId) {
      const loader = new Loader()
      loader.show()
      if (this.item === 'document') {
        removeDocAccess(assocId).then(() => {
          this.peopleWithAccess = this.peopleWithAccess.filter((item) => item.id !== id)
          success(`Removed the access of ${ name }`)
        }).catch(() => {
          alert('Failed to remove access')
        }).finally(() => {
          loader.hide()
        })
      } else if (this.item === 'bom') {
        RemoveSharedBomAccess(assocId).then(() => {
          this.peopleWithAccess = this.peopleWithAccess.filter((item) => item.id !== id)
          success(`Removed the access of ${ name }`)
          addComments(
            'bom',
            id,
            `Removed the access of ${ name }`,
            null,
            null
          )
        }).catch(() => {
          alert('Failed to remove access')
        }).finally(() => {
          loader.hide()
        })
      }
    },
    handleShareBom () {
      const loader = new Loader()
      loader.show()
      let token = 'tenant'
      if (this.level === 'project') {
        token = 'project'
      }
      const usersToShare = []
      for (const users of Object.values(this.selectedUsersToShareMap)) {
        for (const user of users) {
          usersToShare.push(user)
        }
      }
      if (this.item === 'bom') {
        ShareBom(usersToShare, token).then((res) => {
          success('Successfully shared')
        }
        ).catch(() => {
          alert('Failed to share')
        }).finally(() => {
          loader.hide()
          this.$emit('close')
        })
      } else if (this.item === 'document') {
        let level = 'tenant'
        if (this.isOnProjectLevel) {
          level = 'project'
        }
        ShareDocument(usersToShare, level).then((res) => {
          success('Successfully shared the document')
        }).catch(() => {
          alert('Failed to share')
        }).finally(() => {
          loader.hide()
          this.$emit('close')
        })
      }
    },
    setSelectedUsersToShare (users) {
      if (this.item === 'bom') {
        this.selectedUsersToShareMap[this.selectedTenant.id] = users.map((item) => {
          return {
            target_tenant_user_id: item,
            target_tenant_id: this.selectedTenant.id,
            bom_id: this.$props.id,
            bom_version_id: this.$props.versionId
          }
        })
      } else if (this.item === 'document') {
        this.selectedUsersToShareMap[this.selectedTenant.id] = users.map((item) => {
          return {
            target_tenant_user_id: item,
            target_tenant_id: this.selectedTenant.id,
            document_id: this.id
          }
        })
      }
    },
    selectTenant (tenant) {
      this.tenantUsers = []
      this.selectedTenant = tenant
      if (this.selectedUsersToShareMap[this.selectedTenant.id]) {
        this.initiallySelectedUsers = this.selectedUsersToShareMap[this.selectedTenant.id].map((item) => item.target_tenant_user_id)
      } else {
        this.initiallySelectedUsers = []
      }
      this.getUsersOfTheTenant(this.selectedTenant.id)
    },
    getUsersOfTheTenant (id) {
      this.tenantUsers = this.collaborators.find((item) => item.id === id)?.users.map((item) => {
        return {
          value: item?.associated_user?.id,
          label: item?.associated_user?.first_name + ' ' + item?.associated_user?.last_name
        }
      }).filter((item) => {
        if (this.peopleWithAccess.find((user) => user.id === item.value)) {
          return false
        } else return true
      }
      )
    }
  },
  mounted () {
    const loader = new Loader()
    loader.show()
    if (this.item === 'bom') {
      const getUsersWithAccessToSharedBom = GetUsersWithAccessToSharedBom(this.$props.id, this.isOnProjectLevel)
      const getAllCollabaratorList = GetAllCollabaratorList(this.isOnProjectLevel)
      Promise.all([
        getUsersWithAccessToSharedBom,
        getAllCollabaratorList
      ]).then(([peopleWithAccess, collaboratorList]) => {
        this.peopleWithAccess = peopleWithAccess.collaborator_boms.map((item) => {
          return {
            assocId: item.id,
            id: item.tenant_user_association_by_target.associated_user.id,
            name: item.tenant_user_association_by_target.associated_user.first_name + ' ' +
            item.tenant_user_association_by_target.associated_user.last_name,
            sharedById: item.created_by_user.id // storing the id who shared to the user
          }
        })

        const ChildCollaborator = collaboratorList.tenant_company_association.filter((item) => item.target_tenant.status === 1)
        this.collaborators = ChildCollaborator.map((item) => {
          return {
            id: item?.target_tenant?.id,
            name: item?.target_tenant?.company_name,
            users: item?.target_tenant?.tenant_users
          }
        })
      }).catch(() => {
        alert('Failed to fetch')
      }).finally(() => {
        loader.hide()
      })
    } else if (this.item === 'document') {
      const getUsersWithAccessToSharedDocument = GetUsersWithAccessToShareDocument(this.$props.id)
      const getAllCollabaratorList = GetAllCollabaratorList()
      Promise.all([
        getUsersWithAccessToSharedDocument,
        getAllCollabaratorList
      ]).then(([peopleWithAccess, collaboratorList]) => {
        this.peopleWithAccess = peopleWithAccess.collaborator_documents.map((item) => {
          return {
            assocId: item.id,
            id: item.tenant_user_association_by_target.associated_user.id,
            name: item.tenant_user_association_by_target.associated_user.first_name + ' ' +
            item.tenant_user_association_by_target.associated_user.last_name,
            sharedById: item.created_by_user.id // storing  the id who shared to the user
          }
        })
        const ChildCollaborator = collaboratorList.tenant_company_association
        this.collaborators = ChildCollaborator.map((item) => {
          return {
            id: item?.target_tenant?.id,
            name: item?.target_tenant?.company_name,
            users: item?.target_tenant?.tenant_users
          }
        })
      }).catch(() => {
        alert('Failed to fetch')
      }).finally(() => {
        loader.hide()
      })
    }
  }
}
</script>

<style lang="scss" scoped>

.usersList {
  height:24rem;
  padding: 1rem 0;
  .boxes{
     height: 90%;
  }
}

.shareBody {
  padding: 5px;
  min-width: 29rem;
  h3 {
    font-weight: 400;
  }
  &-select {
    margin-top: 15px;
  }
}

.emptyList {
  text-align: center;
  height:24rem;
  padding: 11rem 0
}

.accessList{
    border-radius:0.3rem;
    padding: 5px;
    margin-top: 3px;
    margin-bottom: 3px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: blanchedalmond;
}

$color_1: #ddd;
$border-top-color_1: transparent;
$border-left-color_1: transparent;

/*Checkboxes styles*/
input[type="checkbox"] {
  display: none;
  + {
    label {
      display: block;
      position: relative;
      padding-left: 35px;
      margin-bottom: 20px;
      font: 14px/20px "Open Sans", Arial, sans-serif;
      cursor: pointer;
      -webkit-user-select: none;
      user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      &:last-child {
        margin-bottom: 0;
      }
      &:before {
        content: "";
        display: block;
        width: 20px;
        height: 20px;
        border: 2px solid var(--brand-color);
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0.6;
        -webkit-transition: all 0.12s, border-color 0.08s;
        transition: all 0.12s, border-color 0.08s;
      }
    }
  }
  &:checked {
    + {
      label {
        &:before {
          width: 10px;
          top: -5px;
          left: 5px;
          border-radius: 0;
          opacity: 1;
          border-top-color: $border-top-color_1;
          border-left-color: $border-left-color_1;
          -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
        }
      }
    }
  }
}
</style>
