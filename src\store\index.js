import Vue from 'vue'
import Vuex from 'vuex'
import { tokenAction, logout } from '@/api/session'
import {
  GetAllTenantsListData,
  GetAllTenantsListDataForBeaconAdmin,
  GetAllUsersList,
  GetUserDetailDataByUserId,
  GetAllProjectsList,
  GetCurrentProjectData,
  getProjectUserAssociation
} from '@/api'
import Config from '@/config.js'
import productBom from './productBom'
import projectBom from './projectBom'
import form from './form'
import tag from './tag'
import timeLine from './timeLine'
import document from './documents'
import jwtDecode from 'jwt-decode'
import material from './material'

Vue.use(Vuex)
export default new Vuex.Store({
  state: {
    appLoaded: false,
    openTenantId: null,
    openProjectId: null,
    collaboratorId: null,
    collaborator: false,
    isExternalCollaborator: false,
    user: {
      tenantLevelRole: '',
      projectLevelRole: '',
      userId: '',
      email: '',
      first_name: '',
      last_name: '',
      phone: '',
      status: '',
      beacon_admin: false
    },
    tenantList: [],
    openTenantDetails: {},
    tenantType: null,
    parentTenantsList: [],
    childTenantsList: [],
    tenantUsersList: [],
    tenantProjectList: [],
    rolesList: [],
    currentProject: {},
    projectIdForCollaborator: null,
    adminProjetcts: [], // projects with  current user has admin roles,
    tenantDefaultsData: {
      bom_temp_id: null,
      material_temp_id: null
    }
  },
  getters: {
    isBeaconAdmin: state => state.user.beacon_admin,
    tenantType: state => state.tenantType,
    isExternalCollaborator: state => state.isExternalCollaborator,
    isTenantAdmin: state => state.user.tenantLevelRole === 'ADMIN',
    isProjectAdmin: state => state.user.projectLevelRole === 'ADMIN',
    isTenantCollaborator: state => state.user.tenantLevelRole === 'COLLABORATOR',
    isProjectCollaborator: state => state.user.projectLevelRole === 'COLLABORATOR',
    isTenantViewer: state => state.user.tenantLevelRole === 'VIEWER',
    isProjectViewer: state => state.user.projectLevelRole === 'VIEWER',
    isProjectEditor: state => state.user.projectLevelRole === 'EDITOR',
    appLoaded: state => state.appLoaded,
    openTenantId: state => state.openTenantId,
    openProjectId: state => state.openProjectId,
    collaborator: state => state.collaborator,
    collaboratorId: state => state.collaboratorId, // source tenant id for collaborator
    user: state => state.user,
    tenantList: state => state.tenantList,
    openTenantDetails: state => state.openTenantDetails,
    parentTenantsList: state => state.parentTenantsList,
    childTenantsList: state => state.childTenantsList,
    tenantUsersList: state => state.tenantUsersList,
    tenantProjectList: state => state.tenantProjectList,
    rolesList: state => state.rolesList,
    currentProject: state => state.currentProject,
    adminProjetcts: state => state.adminProjetcts,
    getUserById: state => id => {
      return state.tenantUsersList.filter(user => user.associated_user.id === id)[0] || {}
    },
    isOnProjectLevel: state => !!state.currentProject?.id,
    projectIdForCollaborator: state => state.projectIdForCollaborator,
    tenantDefaultsData: state => state.tenantDefaultsData
  },
  mutations: {
    setAppLoaded (state, bool) {
      state.appLoaded = bool
    },
    setUserFromToken (state, { tenantId, userId, projectId, beaconAdmin, collaborator, collaboratorId }) {
      state.user.beacon_admin = beaconAdmin
      if (beaconAdmin) {
        state.user.tenantId = null
        state.openProjectId = null
        state.openTenantId = null
        state.user.userId = userId
      } else {
        state.user.tenantId = tenantId
        state.openProjectId = projectId
        state.openTenantId = tenantId
        state.user.userId = userId
        state.collaborator = collaborator || false
        state.collaboratorId = collaboratorId || null
      }
    },
    setUserDetail (state, user) {
      if (state.user.beacon_admin) {
        state.user.tenantLevelRole = null
        state.user.projectLevelRole = null
        state.user.email = user?.email
        state.user.first_name = user?.first_name
        state.user.last_name = user?.last_name
        state.user.phone = user?.phone
        state.user.status = user?.status
        return
      }
      state.user.tenantLevelRole = user?.associated_role?.name
      state.user.projectLevelRole = null
      state.user.email = user?.associated_user?.email
      state.user.first_name = user?.associated_user?.first_name
      state.user.last_name = user?.associated_user?.last_name
      state.user.phone = user?.associated_user?.phone
      state.user.status = user?.associated_user?.status
    },
    setExternalCollaborator (state, isCollaborator) {
      state.isExternalCollaborator = isCollaborator
    },
    setUserProjectLevelRole (state, role) {
      state.user.projectLevelRole = role
    },
    setTenentList (state, TenentList) {
      state.tenantList = TenentList
    },
    setOpenTenantDetails (state, tenantDetails) {
      state.openTenantDetails = tenantDetails
    },
    setTenantType (state, type) {
      state.tenantType = type
    },
    setParentTenantList (state, parentTenantsList) {
      state.parentTenantsList = parentTenantsList
    },
    setChildTenantList (state, childTenantsList) {
      state.childTenantsList = childTenantsList
    },
    setTenantUserList (state, TenantUserList) {
      state.tenantUsersList = TenantUserList
    },
    setTenantProjectList (state, TenantProjectList) {
      state.tenantProjectList = TenantProjectList
    },
    setCurrentProject (state, project) {
      state.currentProject = project
      state.openProjectId = project.id
    },
    setProjectIdForCollaborator (state, projectId) {
      state.projectIdForCollaborator = projectId
    },
    removeProjectIdForCollaborator (state) {
      state.projectIdForCollaborator = null
    },
    setProjectsWithAdminRoles (state, projects) {
      state.adminProjetcts = projects
    },
    setTenentDefaults (state, data) {
      state.tenantDefaultsData = {
        bom_temp_id: data.tenant_feature_configuration?.BOM.FORM_TEMPLATE_DEFAULT,
        material_temp_id: data.tenant_feature_configuration?.MATERIAL.FORM_TEMPLATE_DEFAULT
      }
    }
  },
  actions: {
    async fetchProjectList ({ commit }) {
      // Your API call to fetch project list
      const allProjectsList = await GetAllProjectsList()
      commit('setTenantProjectList', allProjectsList?.core_projects || [])
    },
    getCurrentProject ({ commit, state }, cb) {
      if (localStorage.getItem(Config.localstorageKeys.PROJECT) !== null &&
        localStorage.getItem(Config.localstorageKeys.LAST_OPENED_PROJECT) !== '0') {
        const projectToken = localStorage.getItem(Config.localstorageKeys.PROJECT)
        const dtxProjectTokenObject = jwtDecode(projectToken)
        let projectLevelRole = dtxProjectTokenObject['x-hasura-default-role']
        if (projectLevelRole === 'project-admin') {
          projectLevelRole = 'ADMIN'
        } else if (projectLevelRole === 'project-editor') {
          projectLevelRole = 'EDITOR'
        } else if (projectLevelRole === 'project-collaborator') {
          projectLevelRole = 'COLLABORATOR'
        } else {
          projectLevelRole = 'VIEWER'
        }

        commit('setUserProjectLevelRole', projectLevelRole)

        GetCurrentProjectData()
          .then((projectData) => {
            commit('setCurrentProject', projectData?.core_projects?.[0])
            commit('setAppLoaded', true)
            cb && cb()
          })
      } else {
        commit('setAppLoaded', true)
        cb && cb()
      }
    },
    setup ({ commit, dispatch }, cb) {
      tokenAction(async (tenantId, userId, projectId, beaconAdmin) => {
        try {
          commit('setAppLoaded', false)
          if (beaconAdmin) {
            commit('setUserFromToken', { tenantId, userId, projectId: null, beaconAdmin: true, collaborator: null, collaboratorId: null })
            const getAllTenantsListData = GetAllTenantsListDataForBeaconAdmin()
            const getUserDetails = GetUserDetailDataByUserId(userId, 'auth')
            const [
              allTenantsListData,
              userDetails
            ] = await Promise.all([getAllTenantsListData, getUserDetails])

            commit('setTenentList', allTenantsListData.core_tenants || [])
            commit('setTenantUserList', [])
            commit('setUserDetail', userDetails.core_users[0])
            commit('setProjectsWithAdminRoles', [])
            commit('setAppLoaded', true)
            cb && cb()
          } else {
            const collaborator = JSON.parse(localStorage.getItem(Config.localstorageKeys.COLLABORATOR))
            const collaboratorId = localStorage.getItem(Config.localstorageKeys.COLLABORATOR_ID)
            const tenantToken = localStorage.getItem(Config.localstorageKeys.TENANT)
            const decodedTenantToken = jwtDecode(tenantToken)
            commit('setUserFromToken', { tenantId, userId, projectId, beaconAdmin, collaborator, collaboratorId })
            const getAllTenantsListData = GetAllTenantsListData(userId, tenantId, 'tenant')
            const getAllUsersList = GetAllUsersList(localStorage.getItem('tenantId') || tenantId)
            const fetchingQueries = [getAllTenantsListData, getAllUsersList]
            const isCollaborator = decodedTenantToken['x-hasura-allowed-roles'].includes('external-collaborator')
            commit('setExternalCollaborator', isCollaborator)
            if (!decodedTenantToken['x-hasura-default-role'].includes('collaborator-tenant-')) {
              const getAllProjectsList = GetAllProjectsList()
              fetchingQueries.push(getAllProjectsList)
            }
            if (!collaborator && !decodedTenantToken['x-hasura-default-role'].includes('collaborator-tenant-')) {
              const getAdminlevelProjects = getProjectUserAssociation(userId)
              fetchingQueries.push(getAdminlevelProjects)
            }
            const [
              allTenantsListData,
              allUsersList,
              allProjectsList,
              adminlevelProjects
            ] = await Promise.all(fetchingQueries)

            commit('setTenentList', allTenantsListData.core_tenants || [])
            const openTenantDetails = allTenantsListData.core_tenants.find(tenant => tenant.id === tenantId)
            commit('setOpenTenantDetails', openTenantDetails || {})
            let selectedTenantType
            if (collaborator) {
              selectedTenantType = allTenantsListData.parent_tenants.find((tenant) => tenant.source_tenant.id === collaboratorId).source_tenant.tenant_type
            } else {
              selectedTenantType = allTenantsListData.core_tenants.find((tenant) => tenant.id === tenantId).tenant_type
            }
            localStorage.setItem(Config.localstorageKeys.TENANT_TYPE, selectedTenantType)
            commit('setTenantType', selectedTenantType)
            commit('setParentTenantList', allTenantsListData.parent_tenants || [])
            commit('setChildTenantList', allTenantsListData.child_tenants || [])
            commit('setTenantUserList', allUsersList.tenant_user_association || [])
            commit('setTenantProjectList', allProjectsList?.core_projects || [])
            const adminProjectList = []
            if (adminlevelProjects) {
              adminlevelProjects.project_user_association.map(element => {
                if (element.associated_project && element.associated_role.id === 1) {
                  adminProjectList.push({
                    id: element?.associated_project?.id,
                    name: element?.associated_project?.name
                  })
                }
              })
            }
            commit('setProjectsWithAdminRoles', adminProjectList)
            commit('setUserDetail', allUsersList
              .tenant_user_association
              .filter(
                user => user
                  .associated_user.id === userId
              )[0] || {})
            dispatch('getCurrentProject', cb)
          }
        } catch (err) {
          console.log(err)
          setTimeout(() => {
            logout()
          }, 1000)
        }
      })
    }
  },
  modules: {
    projectBom,
    productBom,
    form,
    tag,
    timeLine,
    document,
    material
  }
})
