.dtx-table {
  table {
    width: 100%;
    font-size: 12px;
    border-collapse: collapse;
    position: relative;
    th {
      background: var(--brand-color);
      font-weight: 500;
      padding: 12px 4px;
      position: sticky;
      top: 0;
    }
    td {
      border-bottom: 1px solid var(--brand-color);
    }
    th,
    td {
      text-align: left;
      padding: 8px 4px;
      input[type="number"] {
        width: 50px;
        background-color: transparent;
        border: 1px solid var(--brand-color-1);
        padding: 2px 4px;
        border-radius: 4px;
      }
    }

  }
}