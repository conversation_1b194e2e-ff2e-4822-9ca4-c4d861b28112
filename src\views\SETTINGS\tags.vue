<template>
  <div>
    <div class="tags__list">
      <div class="tags_button">
        <button class="btn btn-black" :disabled="isButtonDisabled" @click="editTagName" v-if="isTenantAdmin">
          Edit Tag
        </button>
        <button class="btn btn-black" :disabled="createButtonDisabled" @click="addNewTag" v-if="isTenantAdmin">
          Create Tag
        </button>
      </div>
      <div v-if="loading" class="center">
        <loading-circle />
      </div>
      <div class="flex gap-2 tag-container" ref="tag">
        <tag-component
        v-for="(tag,index) of tags"
        :key="tag[0]?.id"
        :items="tag"
        :tagIndex="index"
        :parentIndex="tagIndexParents[index] ?? {}"
        :tagStyleData="tagStyleData || null"
        @tag-selected="fetchChildTags($event)"
        @name="fetchName($event)"
        />
      </div>

      <modal :open="openEditTagModal" @close="openEditTagModal = false" :closeOnOutsideClick="true" title="Update TAG Name">
        <div class="create-new-input s p-4">
          <div class="input-group imp">
            <label>Upating Tag Name For: <b> {{ this.selectedTagName }}</b></label>
            <label class="key">TAG Name:</label>
            <input v-model="value"  class="searchInput input-group relative mt-1" :isEmpty="true" type="text" placeholder="Enter TAG Name" />
          </div>
          <div class="flex flex-end py-3 l">
            <button @click="saveEditTag" :disabled="EmptyInputError" class="btn">SAVE</button>
            <button @click="cancelEditTag" class="btn btn-black mr-3">CANCEL</button>
          </div>
        </div>
      </modal>

      <modal :open="openCreateTagModal" @close="openCreateTagModal = false" :closeOnOutsideClick="true" title="Create New TAG">
        <div class="create-new-input s p-4">
          <div class="input-group imp">
            <div v-if="checkRootLevelTag">
              <label>Creating a Root Level Tag for <b>{{ checkRootLevelTag }}</b></label>
            </div>
            <div v-else>
              <label>Creating Child Tag For: <b> {{ this.selectedTagName }} </b></label>
            </div>
            <label class="key">TAG Name:</label>
            <input v-model="value" class="searchInput input-group relative mt-1" :isEmpty="true" type="text" placeholder="Enter TAG Name" />
          </div>
          <div class="flex flex-end py-3 l">
            <button @click="saveCreateTag" :disabled="EmptyInputError" class="btn">SAVE</button>
            <button @click="cancelCreateTag" class="btn btn-black mr-3">CANCEL</button>
          </div>
        </div>
      </modal>

    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import loadingCircle from '../../components/common/loadingCircle.vue'
import Modal from '../../components/common/modal.vue'
import { success, alert } from '@/plugins/notification'
import { displayrootTagName, createNewTag, rootTagName, ToUpdateTagName, checkTagName, GetChildLevelTags } from '@/api'
import tagComponent from '../../components/settings/Tags/tagComponent.vue'
import config from '@/config'
export default {
  data () {
    return {
      tagType: null,
      loading: false,
      tags: [],
      tagIndexParents: [],
      value: '',
      selectedTagName: '',
      openCreateTagModal: false,
      openEditTagModal: false,
      tagStyleData: {
        prevTagIndex: null,
        prevMargin: 0
      },
      buttonLoading: false
    }
  },
  components: {
    tagComponent,
    loadingCircle,
    Modal
  },
  computed: {
    ...mapGetters(['user']),
    isButtonDisabled () {
      // this is disabling edit for tagtypes , means form tags , task tags and material tags etc..
      if (this.tagIndexParents.length === 1) {
        return true
      }
      return this.tagIndexParents[this.tagIndexParents.length - 1]?.id === undefined
    },
    createButtonDisabled () {
      if (this.tagIndexParents.length <= 0) {
        return true
      }
      return false
    },
    EmptyInputError () {
      return this.value.trim() === ''
    },
    isTenantAdmin () {
      return this.user.tenantLevelRole === 'ADMIN'
    },
    checkRootLevelTag () {
      // retrun true if its a rootlevel tag
      if (!this.selectedTagName && this.tagIndexParents.length === 1) {
        const tagtypeindex = this.tagIndexParents[0].index
        return this.tags?.[0][tagtypeindex]?.name
      }
      return false
    }
  },
  methods: {
    fetchName (data) {
      if (data.id === config.TAG_TYPE_MAP.MATERIAL || data.id === config.TAG_TYPE_MAP.TASK || data.id === config.TAG_TYPE_MAP.FORM) {
        this.selectedTagName = ''
        this.tagType = data.id
        return
      }
      this.selectedTagName = data?.name
    },
    fetchRootTags (type) {
      displayrootTagName(type)
        .then((res) => {
          this.tags.push(res.tag)
        }
        )
        .catch((err) =>
          console.log(err)
        ).finally(() => {
          this.loading = false
        })
    },
    fetchChildTags ({ id, index, tagIndex, prevMargin }) {
      if (id === config.TAG_TYPE_MAP.MATERIAL || id === config.TAG_TYPE_MAP.TASK || id === config.TAG_TYPE_MAP.FORM) {
        this.tags = [[{ id: config.TAG_TYPE_MAP.MATERIAL, name: 'Material Tags' }, { id: config.TAG_TYPE_MAP.TASK, name: 'Task Tags' }, { id: config.TAG_TYPE_MAP.FORM, name: 'Form Tags' }]]
        this.tagIndexParents = [{ id, index }]
        this.tagStyleData.prevTagIndex = index
        this.tagStyleData.prevMargin = prevMargin
        this.fetchRootTags(id)
        return
      }
      if (this.tags[tagIndex + 1]) {
        this.tagIndexParents = this.tagIndexParents.slice(0, tagIndex)
        this.tags = this.tags.slice(0, tagIndex + 1)
      }
      GetChildLevelTags(id, this.tagType)
        .then((res) => {
          this.tags.push(res.tag)
          this.tagIndexParents.push({ id, index })
          this.tagStyleData.prevTagIndex = index
          this.tagStyleData.prevMargin = prevMargin
        }
        )
        .catch((err) =>
          console.log(err)
        ).finally(() => {
          this.loading = false
        })
      // this.$nextTick(() => {
      //   this.$refs.tag.scrollTop = 0
      // })
    },
    editTagName () {
      this.openEditTagModal = true
    },
    addNewTag () {
      this.openCreateTagModal = true
    },
    cancelCreateTag () {
      this.value = ''
      this.openCreateTagModal = false
    },
    cancelEditTag () {
      this.value = ''
      this.openEditTagModal = false
    },
    saveEditTag () {
      const value = this.value.toUpperCase()
      rootTagName(value, this.tagType).then((data) => {
        if (data.tag.length === 0) {
          ToUpdateTagName(this.tagIndexParents[this.tagIndexParents.length - 1]?.id, value, this.tagType)
            .then((data) => {
              success('Tag Name Changed successfully')
              this.selectedTagName = value
              const foundTag = this.tags[this.tagIndexParents.length - 1].find(tag => tag?.id === this.tagIndexParents[this.tagIndexParents.length - 1]?.id)
              foundTag.name = value
              this.$set(this.tags, this.tagIndexParents.length - 1, this.tags[this.tagIndexParents.length - 1])
              this.value = ''
              this.openEditTagModal = false
            }).catch((err) => {
              if (err.message.includes('Uniqueness violation')) {
                alert('Tag Name Already exists')
              } else {
                alert('Something went wrong')
              }
              this.value = ''
            })
        } else {
          alert('Tag Name Already exists')
          this.value = ''
        }
      })
    },
    saveCreateTag () {
      if (this.tagIndexParents.length === 1) {
        this.TagDataForRoot()
      } else {
        this.TagDataForChild()
      }
      this.value = ''
    },
    TagDataForRoot () {
      const value = this.value.toUpperCase()
      rootTagName(value, this.tagType).then((data) => {
        if (data.tag.length === 0) {
          createNewTag(value, null, this.tagType)
            .then((data) => {
              success('New Tag Created successfully')
              this.tags[this.tags.length - 1].push(data?.insert_tag_one)
              this.value = ''
              this.openCreateTagModal = false
            })
            .catch(() => {
            })
        } else {
          alert('Already exist! Failed to create new one.')
        }
      })
    },
    TagDataForChild () {
      const value = this.value.toUpperCase()
      checkTagName(value, this.tagIndexParents[this.tagIndexParents.length - 1]?.id, this.tagType).then((data) => {
        if (data.tag.length === 0) {
          this.tagIndexParents[this.tagIndexParents.length - 1]?.id && createNewTag(value, this.tagIndexParents[this.tagIndexParents.length - 1]?.id, this.tagType)
            .then((data) => {
              success('New Tag Created successfully')
              this.tags[this.tags.length - 1].push(data?.insert_tag_one)
              this.value = ''
              this.openCreateTagModal = false
            })
            .catch(() => {
            })
        } else {
          alert('Already exist! Failed to create new one.')
        }
      })
    },
    keyPress (e) {
      if (this.EmptyInputError) {} else if (e instanceof KeyboardEvent && e.code === 'Enter' && this.openCreateTagModal) {
        this.saveCreateTag()
      } else if (e instanceof KeyboardEvent && e.code === 'Enter' && this.openEditTagModal) {
        this.saveEditTag()
      }
    }
  },
  mounted () {
    this.tags = [[{ id: config.TAG_TYPE_MAP.MATERIAL, name: 'Material Tags' }, { id: config.TAG_TYPE_MAP.TASK, name: 'Task Tags' }, { id: config.TAG_TYPE_MAP.FORM, name: 'Form Tags' }]]
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
  }
}
</script>

<style scoped>
.tag-container{
  gap:7px;
  min-width: 140px;
  max-height: calc(120vh - 45vh);
  overflow: auto;
}
.create-new-input {
  width: 600px;
  max-height: 700px;
  overflow: auto;
  margin-right: 3px;
}
.error-text {
  color: red;
  padding-top: 12px;
}
.error-test-default{
  color: red;
  padding-top: 12px;
  visibility: hidden;
}
.btn {
  margin-right: 12px;
}
.tags_button{
  display: flex;
  justify-content: flex-end;
  gap: 7px;
  margin-bottom: 3px;
}
</style>
