import { GQL } from '../graphQl'

export const addCommentsQuery = () => GQL`mutation addCommentsQuery(
  $insertObject: [core_comments_insert_input!]!
  ) {
  insert_core_comments(
    objects: $insertObject
  ) {
    affected_rows
  }
}
`
export const addTaskCommentQuery = () => GQL`mutation addTaskCommentsQuery(
  $comment: String,
  $parent_id: uuid,
  $taskId: uuid!
  ) {
  insert_core_comments(
    objects: {comment: $comment,
      parent_id: $parent_id,
      task_id: $taskId
    }
  ) {
    affected_rows
  }
}
`
export const addTaskCommentsMutation = () => GQL`mutation addTaskComments ( $comments: [core_comments_insert_input!]!) {
  insert_core_comments(objects: $comments){
    affected_rows
  }
}`

export const updateCommentQuery = () => GQL`mutation updateCommentQuery(
  $id: uuid!,
  $comment: String
  ) {
  update_core_comments(
    where: {id: {_eq: $id}},
    _set: {comment: $comment}
  ) {
    affected_rows
  }
}`

export const deleteCommentQuery = () => GQL`mutation deleteCommentQuery(
  $id: uuid!
  ) {
  update_core_comments(
    where: {id: {_eq: $id}},
    _set: {deleted: true}
  ) {
    affected_rows
  }
}`

export const getAllDocumentsCommentsQuery = () => GQL`query getAllDocumentsCommentsQuery ($conditions: core_comments_bool_exp) {
  core_comments(
    where: $conditions
    order_by: {created_on: desc}
  ) {
    comment
    document_id
    core_document{
      version_no
    }
    comments_aggregate{
      aggregate {
        count(columns: comment)
      }
    }
    parent_id
    created_on
    created_by
    id
  }
}
`
export const getAllBomCommentsQuery = () => GQL`query getAllBomCommentsQuery ($conditions: core_comments_bool_exp) {
  core_comments(
    where: $conditions
    order_by: {created_on: desc}
  ) {
    comment
    bom_version_id
    bom_id
    parent_id
    created_on
    created_by
    id
    comments_aggregate{
      aggregate {
        count(columns: comment)
      }
    }
    bom_version {
      active
      bom_id
      id
      version_no
    }
  }
}
`
export const getAllFormCommentsQuery = (formId, parentId) => GQL`query getAllFormCommentsQuery {
  core_comments(
    where: {
      deleted: { _eq: false },
      document_id: { _is_null: true },
      form_id: { _eq: "${formId}" },
      bom_id: { _is_null: true },
      parent_id: ${parentId ? `{ _eq: "${parentId}" }` : '{ _is_null: true }'}
    },
    order_by: { created_on: desc }
  ) {
    comment
    form_id
    parent_id
    created_on
    created_by
    id
    comments_aggregate {
      aggregate {
        count(columns: comment)
      }
    }
  }
}`

export const getAllTaskCommentsQuery = () => GQL`query getAllTaskCommentsQuery ($conditions: core_comments_bool_exp) {
  core_comments(
    where: $conditions
    order_by: {created_on: desc}
  ) {
    comment
    task_id
    parent_id
    created_on
    created_by
    id
    comments_aggregate{
      aggregate {
        count(columns: comment)
      }
    }
  }
}`
