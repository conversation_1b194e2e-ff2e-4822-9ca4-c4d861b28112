<template>
  <div class="beaconsales">
    <div class="beaconsales-bar v-center space-between px-3">
      <h1 class="weight-600 xxxl">Beacon Dashboard</h1>
      <button v-if="adminlevel" class="beaconsales-bar-filter-btn btn" @click="filterBox=!filterBox">filter
        <img  v-if="filterBox" src="~@/assets/images/icons/arrow-up-icon.svg"  width="15px"  height="15" alt="" >
        <img  v-else src="~@/assets/images/icons/arrow-down-icon.svg"  width="15px"  height="15" alt="" > </button>
    </div>
<!-- filter section starts  -->
    <div  v-if="adminlevel" :class="{'beaconsales-filter':true, 'beaconsales-filter-expand': filterBox}">
      <multiselect-dropdown label="Select users" :options="userList" @selected="setSelectedUsers" :initiallySelected="selectedUserIds"/>

      <div class="prod-code-search-btnbox">
          <div ref="selectedBg" class="prod-code-search-selectedbg"></div>
          <div class="prod-code-search-prodbtn" @click="cumulativeData"  title="Cumulative Data" >Cum</div>
          <div class="prod-code-search-bombtn" @click="individualData" title="Individual user">Ind</div>
        </div>
        <button class="btn " @click="setup">Apply</button>
    </div>
<!--  filter section ends -->
    <div class="beaconsales-row1">
      <div class="beaconsales-row1-container">
        <div class="beaconsales-row1-container-heading">
          <div>
            <h2 class="mt-2">Summary</h2>
            <p class="mt-2">Sales Summary</p>
          </div>
          <div>
            <select name="" id="" v-model="selecedFilterSales" @change="getDataValue">
              <option value="today">Today</option>
              <option value="day" >Last Day</option>
              <option value="week">This week</option>
              <option value="month">This month</option>
              <option value="year">This Year</option>
            </select>
          </div>
        </div>
        <div class="beaconsales-row1-salesbox">
          <div class="beaconsales-row1-salesbox-box-1">
            <img src="~@/assets/images/cash.svg" alt="" />
            <h2 class="">{{ salesSummary.revenue/1000 || 0 }} k</h2>
            <p class="mt-1">Total Sales</p>
          </div>
          <div class="beaconsales-row1-salesbox-box-2">
            <img src="~@/assets/images/sales.svg" alt="" />
            <h2 class="">{{ salesSummary.liscenceSold }}</h2>
            <p class="mt-1">Licences sold</p>
          </div>
          <div class="beaconsales-row1-salesbox-box-3">
            <img src="~@/assets/images/newcustomers.svg" alt="" />
            <h2 class="">{{ salesSummary.newCustomers }}</h2>
            <p class="mt-1">New Customers</p>
          </div>
          <div class="beaconsales-row1-salesbox-box-4">
            <img src="~@/assets/images/oftapproval.svg" alt="" />
            <h2 class="">{{ salesSummary.oftapprovals }}</h2>
            <p class="mt-1">OFT approvals</p>
          </div>
        </div>
      </div>
      <div class="beaconsales-row1-revenuebox"  :class="{ 'expanded': expanded==='total-revenue' }">
          <category-wise-chart
          :graphSeries="totalRevenueSeries"
          :xaxisCategories="totalRevenueXaxis"
          :curve="'smooth'"
          :marker="true"
          :chartType="'bar'"
          :label="'Total Revenue'"
          :key="totalRevenueXaxis[0]"
          @expand="expanded='total-revenue'"
          @collapse="expanded=''"
        />
      </div>
    </div>
    <div class="beaconsales-row2">
      <div class="beaconsales-row2-revenue "  :class="{'expanded': expanded==='revenue-box' }">

        <category-wise-chart
          :graphSeries="revenueBreakupSeries"
          :xaxisCategories="monthlyXaxis"
          :curve="'smooth'"
          :marker="true"
          :chartType="'line'"
          :label="'Revenue breakup'"
          :graphcolors="Config.COLORS"
          @expand="expanded='revenue-box'"
          @collapse="expanded=''"
        />
      </div>

      <div class="beaconsales-row2-newleads"  :class="{'expanded': expanded==='new-leads' }">
        <category-wise-chart
          :graphSeries="newvsOftapprovalSeries"
          :xaxisCategories="monthlyXaxis"
          :curve="'smooth'"
          :marker="true"
          :chartType="'area'"
          :label="'New Leads Vs OFT Approvals'"
          @expand="expanded='new-leads'"
          @collapse="expanded=''"
        />
      </div>

      <div class="beaconsales-row2-target" :class="{'beaconsales-row2-target':true, 'expanded': expanded==='traget' }">
        <category-wise-chart
          :graphSeries="targetVsRealitySeries"
          :xaxisCategories="monthlyXaxis"
          :curve="'smooth'"
          :marker="true"
          :chartType="'bar'"
          :label="'Target VS Reality'"
          @expand="expanded='traget'"
          @collapse="expanded=''"
        />
      </div>

      <div class="beaconsales-row2-topselling">
        <h2>Top products</h2>
        <div class="beaconsales-row2-topselling-table">
          <div class="beaconsales-row2-topselling-table-heading">
            <div class="">#</div>
            <div class="">Name</div>
            <div class="">Popularity</div>
            <div class="">Sales</div>
          </div>
          <div v-for="(product, index) in topProductsList" :key="index">
            <dashborad-table-row :product="product" :index="index" />
          </div>
          <div></div>
        </div>
      </div>
      <div class="beaconsales-row2-margin" :class="{'expanded': expanded==='margin' }">
        <category-wise-chart
          :graphSeries="marginGraphSeries"
          :xaxisCategories="monthlyXaxis"
          :curve="'smooth'"
          :marker="true"
          :chartType="'line'"
          :label="'Beacon Margin'"
          :graphcolors="Config.COLORS"
          @expand="expanded='margin'"
          @collapse="expanded=''"
        />
      </div>
    </div>
  </div>
</template>
<script>
import CategoryWiseChart from '@/components/common/charts/categorywisechart.vue'
import DashboradTableRow from '@/components/beaconDashboard/tablerow.vue'
import { alert } from '@/plugins/notification'

import {
  getBeaconMarginData,
  getBeaconTemplateVersionIds,
  getTopProductsData,
  getTotalRevenueData,
  getRevenueBreakupData,
  getNewLeadOftLeadData,
  getTargetRealityData,
  getBeaconSalesData,
  GetAssociatedUsersList
} from '@/api'
import { getStartAndEndDateOfWeek, getWeekNumber, getStartAndEndDateOfMonth } from '@/utils/date'
import MultiselectDropdown from '../../components/common/multiselectDropdown.vue'
import { mapGetters } from 'vuex'
import Config from '@/config.js'

// import { mapGetters } from 'vuex'
export default {
  name: 'insights',
  components: {
    DashboradTableRow,
    CategoryWiseChart,
    MultiselectDropdown
  },
  data: function () {
    return {
      filterBox: false,
      templateId: null,
      targetTemplateId: null,
      monthMap: {
        January: 0,
        February: 1,
        March: 2,
        April: 3,
        May: 4,
        June: 5,
        July: 6,
        August: 7,
        September: 8,
        October: 9,
        November: 10,
        December: 11
      },
      weekmap: {
        Monday: 0,
        Tuesday: 1,
        Wednesday: 2,
        Thursday: 3,
        Friday: 4,
        Saturday: 5,
        Sunday: 6
      },
      monthlyXaxis: [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec'
      ],
      daysOfWeek: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      marginGraphSeries: [],
      topProductsSeries: [],
      totalRevenueSeries: [],
      revenueBreakupSeries: [],
      newvsOftapprovalSeries: [],
      topProductsList: [],
      targetVsRealitySeries: [],
      salesSummary: {},
      totalRevenueXaxis: [],
      selecedFilterSales: 'day',
      expanded: '',
      userMap: {},
      Config: Config,
      userList: [],
      selectedUserIds: [],
      cumulativeGraphs: true
    }
  },
  computed: {
    // ...mapGetters(['user', 'adminProjetcts', 'currentProject']),
    ...mapGetters(['user', 'isOnProjectLevel', 'tenantUsersList', 'openProjectId', 'isTenantAdmin', 'isProjectAdmin']),
    adminlevel () {
      return (this.isTenantAdmin || (this.isOnProjectLevel && this.isProjectAdmin))
    }
  },
  methods: {
    setup () {
      getBeaconTemplateVersionIds().then(async (res) => {
        this.templateId = res.sales_template[0].id
        this.targetTemplateId = res.target_template[0]?.id
        if (!this.targetTemplateId) {
          alert('there is no target form template')
        }
        const year = new Date().getFullYear()
        const endDate = new Date(year, 11, 31).toLocaleDateString('en-US')
        const startDate = new Date(year, 0, 1).toLocaleDateString('en-US')
        if (this.openProjectId !== 0) { await this.getProjectUsersList(this.openProjectId) } else this.createUserMap()
        this.userAssociation()
        this.getSalesData(
          this.templateId,
          new Date(
            new Date().setDate(new Date().getDate() + 1)
          ).toLocaleDateString('en-US'),
          new Date(
            new Date().setDate(new Date().getDate() - 1)
          ).toLocaleDateString('en-US')
        )
        this.getRevenueBreakup(this.templateId, startDate, endDate)
        this.getTotalRevenue(this.templateId, startDate, endDate, this.selecedFilterSales)
        this.getNewLeadOftLead(this.templateId, startDate, endDate)
        this.getTargetVsReality({ templateVersionId: this.templateId, targetSalesVersionId: this.targetTemplateId, startDate, endDate })
        this.getTopProducts(this.templateId, startDate, endDate)
        this.margingraphdata(this.templateId, startDate, endDate)
      })
    },
    margingraphdata (templateId, startDate, endDate) {
      this.marginGraphSeries = []
      getBeaconMarginData(templateId, startDate, endDate, this.selectedUserIds)
        .then((res) => {
          if (this.cumulativeGraphs) {
            const beaconRevenue = {
              name: 'Beacon Revenue',
              data: new Array(12).fill(0)
            }
            const dsRevenue = { name: 'DS Revenue', data: new Array(12).fill(0) }
            for (const data of res.message.beaconRevenue) {
              const index = this.monthMap[data.month]
              beaconRevenue.data[index] = beaconRevenue.data[index] + parseInt(data.revenue)
            }
            for (const data of res.message.dsRevenue) {
              const index = this.monthMap[data.month]
              dsRevenue.data[index] = dsRevenue.data[index] + parseInt(data.revenue)
            }
            this.marginGraphSeries = [beaconRevenue, dsRevenue]
            return
          }
          const graphDataBeacon = {}
          const graphDataDs = {}
          for (const item of res.message.beaconRevenue) {
            if (!graphDataBeacon[item.user_id]) {
              graphDataBeacon[item.user_id] = {
                name: `Beacon Revenue (${this.userMap[item.user_id].label})`,
                data: new Array(12).fill(0)
              }
            }
            if (!graphDataDs[item.user_id]) {
              graphDataDs[item.user_id] = {
                name: `DS Revenue (${this.userMap[item.user_id].label})`,
                data: new Array(12).fill(0)
              }
            }
            const index = this.monthMap[item.month]
            graphDataBeacon[item.user_id].data[index] = item.revenue
          }
          for (const item of res.message.dsRevenue) {
            if (!graphDataBeacon[item.user_id]) {
              graphDataBeacon[item.user_id] = {
                name: `Beacon Revenue (${this.userMap[item.user_id].label})`,
                data: new Array(12).fill(0)
              }
            }
            if (!graphDataDs[item.user_id]) {
              graphDataDs[item.user_id] = {
                name: `DS Revenue (${this.userMap[item.user_id].label})`,
                data: new Array(12).fill(0)
              }
            }
            const index = this.monthMap[item.month]
            graphDataDs[item.user_id].data[index] = item.revenue
          }

          this.marginGraphSeries = [...Object.values(graphDataDs), ...Object.values(graphDataBeacon)]
        })
        .catch((err) => {
          console.log(err)
        })
    },
    getTopProducts (templateId, startDate, endDate) {
      getTopProductsData(templateId, startDate, endDate, this.selectedUserIds)
        .then((res) => {
          const totalSalesCount = res.message.reduce((total, obj) => {
            return total + parseInt(obj.count)
          }, 0)
          this.topProductsList = res.message.map((obj) => {
            return {
              percentage:
                Math.round((obj.count / totalSalesCount) * 10000) / 100,
              name: obj.product
            }
          })
        })
        .catch((err) => {
          console.log(err)
        })
    },
    getTotalRevenue (templateId, startDate, endDate, type) {
      if (type === 'day' || type === 'week' || type === 'month') type = 'day'
      if (type === 'year') type = 'month'
      getTotalRevenueData(templateId, startDate, endDate, this.selectedUserIds, type)
        .then((res) => {
          this.totalRevenueXaxis = this.daysOfWeek
          const dataSeries = {}
          if (this.cumulativeGraphs) {
            switch (this.selecedFilterSales) {
            case 'today':
            case 'day':
            case 'week': {
              for (const data of res.message) {
                const { day, revenue, product } = data
                const index = new Date(day).getDay()

                if (Object.hasOwn(dataSeries, product)) {
                  dataSeries[product].data[index] = parseInt(revenue) + parseInt(dataSeries[product].data[index])
                } else {
                  dataSeries[product] = {
                    data: new Array(7).fill(0),
                    name: product
                  }
                  dataSeries[product].data[index] = parseInt(revenue)
                }
              }
              this.totalRevenueSeries = Object.values(dataSeries)
              break
            }
            case 'month': {
              const year = new Date(res.message?.[0]?.day).getFullYear()
              const month = new Date(res.message?.[0]?.day).getMonth()
              const noOfDays = new Date(year, month + 1, 0).getDate()

              for (const data of res.message) {
                const { day, revenue, product } = data
                const index = new Date(day).getDate()

                if (Object.hasOwn(dataSeries, product)) {
                  dataSeries[product].data[index] = parseInt(revenue) + parseInt(dataSeries[product].data[index])
                } else {
                  dataSeries[product] = {
                    data: new Array(noOfDays).fill(0),
                    name: product
                  }
                  dataSeries[product].data[index] = parseInt(revenue)
                }
              }
              this.totalRevenueXaxis = []
              for (let i = 1; i <= noOfDays ?? 0; i++) {
                this.totalRevenueXaxis.push(`${i}/${month + 1}`)
              }
              this.totalRevenueSeries = Object.values(dataSeries)
              break }
            case 'year': {
              for (const data of res.message) {
                const { day, revenue, product } = data
                const index = new Date(day).getMonth()

                if (Object.hasOwn(dataSeries, product)) {
                  dataSeries[product].data[index] = parseInt(revenue) + parseInt(dataSeries[product].data[index])
                } else {
                  dataSeries[product] = {
                    data: new Array(12).fill(0),
                    name: product
                  }
                  dataSeries[product].data[index] = parseInt(revenue)
                }
              }
              this.totalRevenueXaxis = this.monthlyXaxis
              this.totalRevenueSeries = Object.values(dataSeries) }
            }
          } else {
            switch (this.selecedFilterSales) {
            case 'today':
            case 'day':
            case 'week': {
              for (const data of res.message) {
                const { user_id: userId, day, revenue, product } = data
                if (!dataSeries[userId]) {
                  dataSeries[userId] = {}
                }
                const index = new Date(day).getDay()
                if (Object.hasOwn(dataSeries[userId], product)) {
                  dataSeries[userId][product].data[index] = revenue
                } else {
                  dataSeries[userId][product] = {
                    data: new Array(7).fill(0),
                    name: product + `(${this.userMap[userId].label})`
                  }
                  dataSeries[userId][product].data[index] = revenue
                }
              }
              this.totalRevenueXaxis = this.daysOfWeek
              this.totalRevenueSeries = Object.values(dataSeries).map((item) => { return Object.values(item) })
              this.totalRevenueSeries = this.totalRevenueSeries.flat()
              break
            }
            case 'month': {
              const year = new Date(res.message?.[0]?.day).getFullYear()
              const month = new Date(res.message?.[0]?.day).getMonth()
              const noOfDays = new Date(year, month + 1, 0).getDate()
              for (const data of res.message) {
                const { user_id: userId, day, revenue, product } = data
                if (!dataSeries[userId]) {
                  dataSeries[userId] = {}
                }
                const index = new Date(day).getDay()
                if (Object.hasOwn(dataSeries[userId], product)) {
                  dataSeries[userId][product].data[index] = revenue
                } else {
                  dataSeries[userId][product] = {
                    data: new Array(noOfDays).fill(0),
                    name: product + `(${this.userMap[userId].label})`
                  }
                  dataSeries[userId][product].data[index] = revenue
                }
              }
              for (let i = 1; i <= noOfDays ?? 0; i++) {
                this.totalRevenueXaxis.push(`${i}/${month + 1}`)
              }
              this.totalRevenueSeries = Object.values(dataSeries).map((item) => { return Object.values(item) })
              this.totalRevenueSeries = this.totalRevenueSeries.flat()
              break }
            case 'year': {
              for (const data of res.message) {
                const { user_id: userId, day, revenue, product } = data
                if (!dataSeries[userId]) {
                  dataSeries[userId] = {}
                }
                const index = new Date(day).getMonth()
                if (Object.hasOwn(dataSeries[userId], product)) {
                  dataSeries[userId][product].data[index] = revenue
                } else {
                  dataSeries[userId][product] = {
                    data: new Array(12).fill(0),
                    name: product + `(${this.userMap[userId].label})`
                  }
                  dataSeries[userId][product].data[index] = revenue
                }
              }
              this.totalRevenueSeries = Object.values(dataSeries).map((item) => { return Object.values(item) })
              this.totalRevenueSeries = this.totalRevenueSeries.flat()
              this.totalRevenueXaxis = this.monthlyXaxis
              break }
            }
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    getRevenueBreakup (templateId, startDate, endDate) {
      this.revenueBreakupSeries = []
      getRevenueBreakupData(templateId, startDate, endDate, this.selectedUserIds)
        .then((res) => {
          const revenueExisting = new Array(12).fill(0)
          const revenueNew = new Array(12).fill(0)
          if (this.cumulativeGraphs) {
            for (const data of res.message) {
              const index = this.monthMap[data.month]
              if (data.name === 'Existing') {
                revenueExisting[index] = revenueExisting[index] + parseInt(data.revenue)
              } else {
                revenueNew[index] = revenueNew[index] + parseInt(data.revenue)
              }
            }
            this.revenueBreakupSeries = [{ name: 'New Customers', data: revenueNew }, { name: 'Loyal Customers', data: revenueExisting }]
            return
          }
          const graphData = this.createGraphDataforAnnual(res.message)
          Object.keys(graphData).forEach((key) => {
            this.revenueBreakupSeries.push({ name: `New Customers - ${this.userMap[key].label}`, data: graphData[key].revenueExisting })
            this.revenueBreakupSeries.push({ name: `Loyal Customers - ${this.userMap[key].label}`, data: graphData[key].revenueNew })
          })
        })
        .catch((err) => {
          console.log(err)
        })
    },
    getNewLeadOftLead (templateId, startDate, endDate) {
      this.newvsOftapprovalSeries = []
      getNewLeadOftLeadData(templateId, startDate, endDate, this.selectedUserIds)
        .then((res) => {
          if (this.cumulativeGraphs) {
            const leads = { name: 'New Leads ', data: new Array(12).fill(0) }
            const oft = { name: 'OFT Approval', data: new Array(12).fill(0) }
            for (const data of res.message.leads) {
              const index = this.monthMap[data.month]
              leads.data[index] = leads.data[index] + parseInt(data.count)
            }
            for (const data of res.message.oft) {
              const index = this.monthMap[data.month]
              oft.data[index] = oft.data[index] + parseInt(data.count)
            }
            this.newvsOftapprovalSeries = [leads, oft]
            return
          }
          const graphDataLeads = {}
          const graphDataOft = {}
          for (const item of res.message.leads) {
            if (!graphDataLeads[item.user_id]) {
              graphDataLeads[item.user_id] = { name: `New Leads (${this.userMap[item.user_id].label})`, data: new Array(12).fill(0) }
            }
            if (!graphDataOft[item.user_id]) {
              graphDataOft[item.user_id] = { name: `OFT Approval (${this.userMap[item.user_id].label})`, data: new Array(12).fill(0) }
            }
            const index = this.monthMap[item.month]
            graphDataLeads[item.user_id].data[index] = item.count
          }
          for (const item of res.message.oft) {
            if (!graphDataLeads[item.user_id]) {
              graphDataLeads[item.user_id] = { name: `New Leads (${this.userMap[item.user_id].label})`, data: new Array(12).fill(0) }
            }
            if (!graphDataOft[item.user_id]) {
              graphDataOft[item.user_id] = { name: `OFT Approval (${this.userMap[item.user_id].label})`, data: new Array(12).fill(0) }
            }
            const index = this.monthMap[item.month]
            graphDataOft[item.user_id].data[index] = item.count
          }
          this.newvsOftapprovalSeries = [...Object.values(graphDataOft), ...Object.values(graphDataLeads)]
        })
        .catch((err) => {
          console.log(err)
        })
    },
    getTargetVsReality ({ templateVersionId, targetSalesVersionId, startDate, endDate }) {
      this.targetVsRealitySeries = []
      getTargetRealityData({ templateVersionId, targetSalesVersionId, startDate, endDate }, this.selectedUserIds)
        .then((res) => {
          const monthMapForTargetSales = {
            jan: 0,
            feb: 1,
            mar: 2,
            apr: 3,
            may: 4,
            june: 5,
            july: 6,
            aug: 7,
            sep: 8,
            oct: 9,
            nov: 10,
            dec: 11
          }

          if (this.cumulativeGraphs) {
            const reality = {
              name: 'Reality Sales',
              group: 'reality',
              data: new Array(12).fill(0)
            }
            const target = {
              name: 'Target Sales',
              group: 'target',
              data: new Array(12).fill(0)
            }
            for (const data of res.message.realitySales) {
              const index = this.monthMap[data.month]
              reality.data[index] = reality.data[index] + parseInt(data.count)
            }
            for (const data of res.message.targetSales) {
              const index = monthMapForTargetSales[data.month]
              target.data[index] = target.data[index] + parseInt(data.target)
            }
            this.targetVsRealitySeries = [target, reality]
            return
          }

          const graphDataReality = {}
          const graphDataTarget = {}
          for (const item of res.message.realitySales) {
            if (!graphDataReality[item.user_id]) {
              graphDataReality[item.user_id] = {
                name: `Reality Sales (${(this.userMap[item.user_id].label)})`,
                group: 'reality',
                data: new Array(12).fill(0)
              }
            }
            if (!graphDataTarget[item.user_id]) {
              graphDataTarget[item.user_id] = {
                name: `Target Sales (${this.userMap[item.user_id].label})`,
                group: 'target',
                data: new Array(12).fill(0)
              }
            }
            const index = this.monthMap[item.month]
            graphDataReality[item.user_id].data[index] = item.count
          }

          for (const item of res.message.targetSales) {
            if (!graphDataTarget[item.user_id]) {
              graphDataTarget[item.user_id] = {
                name: `Target Sales (${this.userMap[item.user_id].label})`,
                group: 'target',
                data: new Array(12).fill(0)
              }
            }
            if (!graphDataReality[item.user_id]) {
              graphDataReality[item.user_id] = {
                name: `Reality Sales (${(this.userMap[item.user_id].label)})`,
                group: 'reality',
                data: new Array(12).fill(0)
              }
            }

            const index = monthMapForTargetSales[item.month]
            graphDataTarget[item.user_id].data[index] = item.target
          }
          this.targetVsRealitySeries = [...Object.values(graphDataTarget), ...Object.values(graphDataReality)]
        })
        .catch((err) => {
          console.log(err)
        })
    },
    getSalesData (templateId, startDate, endDate) {
      getBeaconSalesData(templateId, startDate, endDate, this.selectedUserIds)
        .then((res) => {
          this.salesSummary = {
            revenue: res.message.revenue?.[0].revenue,
            liscenceSold: res.message.revenue?.[0].license_sold,
            newCustomers: res.message.newCustomers?.[0].new_customers,
            oftapprovals: res.message.oftApprovals?.[0].oft_approvals
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    getDataValue () {
      const year = new Date().getFullYear()
      switch (this.selecedFilterSales) {
      case 'today' : {
        this.getSalesData(this.templateId, new Date().toLocaleDateString('en-US'), undefined)
        const { weekStartDate, weekEndDate } = getStartAndEndDateOfWeek(year, getWeekNumber(new Date()))
        this.getTotalRevenue(this.templateId, weekStartDate.toLocaleDateString('en-US'), weekEndDate.toLocaleDateString('en-US'), 'day')
        break }
      case 'day' : {
        this.getSalesData(this.templateId, new Date(
          new Date().setDate(new Date().getDate() - 1)
        ).toLocaleDateString('en-US'),
        undefined)
        const { weekStartDate, weekEndDate } = getStartAndEndDateOfWeek(year, getWeekNumber(new Date()))
        this.getTotalRevenue(this.templateId, weekStartDate.toLocaleDateString('en-US'), weekEndDate.toLocaleDateString('en-US'), 'day')
        break
      }
      case 'week': {
        const { weekStartDate, weekEndDate } = getStartAndEndDateOfWeek(
          year,
          getWeekNumber(new Date())
        )
        this.getSalesData(this.templateId, weekStartDate.toLocaleDateString('en-US'), weekEndDate.toLocaleDateString('en-US'))
        this.getTotalRevenue(this.templateId, weekStartDate.toLocaleDateString('en-US'), weekEndDate.toLocaleDateString('en-US'), 'day')
        break
      }
      case 'month': {
        const { startDate, endDate } = getStartAndEndDateOfMonth(year, new Date().getMonth())
        this.getSalesData(this.templateId, startDate.toLocaleDateString('en-US'), endDate.toLocaleDateString('en-US'))
        this.getTotalRevenue(this.templateId, startDate.toLocaleDateString('en-US'), endDate.toLocaleDateString('en-US'), 'day')
        break
      }
      case 'year': {
        const endDate = new Date(year, 11, 31).toLocaleDateString('en-US')
        const startDate = new Date(year, 0, 1).toLocaleDateString('en-US')
        this.getSalesData(this.templateId, startDate, endDate)
        this.getTotalRevenue(this.templateId, startDate, endDate, 'month')
      }
        break
      default:
        this.getSalesData(this.templateId, new Date(
          new Date().setDate(new Date().getDate() - 1)
        ).toLocaleDateString('en-US'),
        undefined)
      }
    },
    setSelectedUsers (selectedUsers) {
      this.selectedUserIds = [...selectedUsers]
    },
    createGraphDataforAnnual (apiData) {
      const graphData = {}
      for (const data of apiData) {
        const userId = data.user_id
        if (!graphData[userId]) {
          graphData[userId] = {
            revenueExisting: new Array(12).fill(0),
            revenueNew: new Array(12).fill(0)
          }
        }
        const index = this.monthMap[data.month]
        if (data.name === 'Existing') {
          graphData[userId].revenueExisting[index] = data.revenue
        } else {
          graphData[userId].revenueNew[index] = data.revenue
        }
      }
      return graphData
    },
    createUserMap () {
      this.userMap = {}
      this.tenantUsersList.forEach((user) => {
        this.userMap[user?.associated_user.id] = { label: user?.associated_user.first_name + ' ' + user?.associated_user.last_name, ...user?.associated_user }
      })
    },
    getProjectUsersList (projectId) {
      return new Promise((resolve, reject) => {
        this.userMap = {}
        GetAssociatedUsersList(projectId).then((res) => {
          this.associatedUsers = res.project_user_association.map((user) => {
            this.userMap[user.associated_user.id] = {
              label:
              user.associated_user.first_name +
              ' ' +
              user.associated_user.last_name,
              ...user.associated_user
            }
          })
          this.userAssociation()
          resolve()
        })
      })
    },
    userAssociation () {
      if (this.isOnProjectLevel) {
        this.userList = Object.values(this.userMap).map((user) => { return { label: user?.label, value: user?.id } })
      } else {
        this.userList = this.tenantUsersList.filter((el) => el.associated_user.status === 1).map((user) => { return { label: user?.associated_user.first_name + ' ' + user?.associated_user.last_name, value: user?.associated_user.id } })
      }
    },
    cumulativeData () {
      this.$refs.selectedBg.style.left = '0'
      this.cumulativeGraphs = true
    },
    individualData () {
      this.$refs.selectedBg.style.left = '50%'
      this.cumulativeGraphs = false
    },
    keyPress (e) {
      if (e instanceof KeyboardEvent && e.code === 'Escape') {
        this.expanded = ''
      }
    }
  },
  mounted () {
    this.setup()
  },
  watch: {
    cumulativeData () {
      if (this.cumulativeData) {
        this.$refs.selectedBg.style.left = '0'
      } else {
        this.$refs.selectedBg.style.left = '50%'
      }
    }
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
  }
}
</script>
<style lang="scss" scoped>
.beaconsales {
  &-bar {
    height: 60px;
    margin: -12px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
    &-filter-btn{
      border: 1px solid rgba(81, 80, 80, 0.489);
      padding-inline:5px;
      border-radius:5px;
      display: flex;
      justify-content: content;
      align-items: center;
      gap:5px;

    }
  }
  &-row1 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
    height: fit-content;
    &-container {
      width: 100%;
      box-shadow: rgba(149, 157, 165, 0.2) 0px 0px 16px;
      border-radius: 1rem;
      background-color: white;
      padding: 1rem;
      &-heading {
        padding-inline: 2rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        & select{
          padding: 10px;
          border: 0.1px solid #dcd8d8;
          border-radius: 8px;
        }
      }
    }
    &-salesbox {
      width:100%;
      display: grid;
      align-content: center;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      margin-top: 2rem;
      padding: 2rem;
      & > div {
        padding: 2rem;
        height: 10rem;
        flex-grow: 1;
        border-radius: 6px;
        & > img {
          height: 2rem;
          width: 2rem;
        }
        & > h2 {
          margin-top: 1rem;
        }
      }

      &-box-1 {
        background-color: rgba(135, 244, 208, 0.3);
      }
      &-box-2 {
        background-color: rgba(241, 195, 166, 0.3);
      }
      &-box-3 {
        background-color: rgba(172, 167, 240, 0.3);
      }
      &-box-4 {
        background-color: rgba(234, 125, 234, 0.3);
      }
    }
    &-revenuebox {
      width:100%;
      height: 100%;
      box-shadow: rgba(149, 157, 165, 0.2) 0px 0px 16px;
      border-radius: 1rem;
      background-color: white;
      padding: 2rem;
    }
  }
  &-row2 {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 1rem;
    margin-top: 1rem;
    height: fit-content;
    & > div {
      width: 100%;
      box-shadow: rgba(149, 157, 165, 0.2) 0px 0px 16px;
      border-radius: 1rem;
      background-color: white;
      padding: 2rem;
    }
    &-revenue {
      grid-area: 1 / 1 / 2 / 3;
    }
    &-newleads {
      grid-area: 1 / 3 / 2 / 5;
    }
    &-target {
      grid-area: 1 / 5 / 2 / 7;
    }
    &-topselling {
      grid-area: 2 / 1 / 3 / 4;
      width: 100%;
      height: 600px;
      overflow: auto;
      &-table {
        &-heading {
          display: grid;
          grid-template-columns: 1fr 4fr 4fr 1fr;
          border-bottom: 1px solid rgb(132, 130, 130, 0.2);
          padding: 1rem;
          width: 100%;
          font-size: medium;
          color: rgb(143, 140, 140, 0.8);
          & > div {
            text-align: center;
          }
        }
      }
    }
    &-margin {
      grid-area: 2 / 4 / 3 / 7;
    }
  }
  &-filter{
    height:0;
    background-color: rgba(249, 249, 249, 0.922);
    position: sticky;
    top: -12px;
    z-index: 12; // cheked based on graph pan tool box
    overflow: hidden;
    transition: height .8s ease-in-out;
    display: flex;
    align-items: center;
    gap:1rem;
    border-radius: 10px;
    padding:0px 10px;
    border: 0;
    &-expand{
      height: 50px;
      overflow: visible;
      border: 1px solid rgba(81, 80, 80, 0.489);

    }
  }
}
@media screen and (min-width: 1400px) {
  // .beaconsales-row2 {
  //   grid-template-columns: 1fr 1fr 1fr;
  // }
}

// Media Query for screen size below 1000px
@media screen and (max-width: 1100px) {
  .beaconsales-row2 {
    &-revenue {
      grid-area: 1 / 1 / 2 / 4;
    }
    &-newleads {
      grid-area: 1 / 4 / 2 / 7;
    }
    &-target {
      grid-area: 2 / 1 / 3 / 4;
    }
    &-topselling {
      grid-area: 2 / 4 / 3 / 7;
    }
    &-margin {
      grid-area: 3 / 1 / 4 / 7;
    }
  }
  .beaconsales-row1 {
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 800px) {
  .beaconsales-row2 {
    &-revenue {
      grid-area: 1 / 1 / 2 / 7;
    }
    &-newleads {
      grid-area: 2 / 1 / 3/ 7;
    }
    &-target {
      grid-area: 3 / 1 / 4 / 7;
    }
    &-topselling {
      grid-area: 4 / 1 / 5 / 7;
    }
    &-margin {
      grid-area: 5 / 1 / 6 / 7;
    }
  }
  .beaconsales-row1 {
    grid-template-columns: 1fr;
  }
}
.expanded{
  position: absolute;
  inset:0px;
  background-color: rgb(0, 0, 0, .7) !important;
  padding: 5rem!important;
  border-radius: 0 !important;;
  box-shadow: none;
  z-index: 15;
  & div{
    background-color: white;
  }
}
.prod-code-search{
  &-btnbox{
    position: relative;
    cursor: pointer;
    display: flex;
    background-color: var(--bg-color);
    border: 1px solid var(--brand-color);
    border-radius: 5px;

  }
  &-prodbtn{
    background: transparent;
    padding: 2px 6px;
    z-index: 2;
  }
  &-bombtn{
  background: transparent;
    padding: 2px 6px;
    z-index: 2;
  }
  &-selectedbg{
    position: absolute;
    background-color: var(--brand-color);
    border-radius: 5px;
left: 0;
top: 0;
z-index: 1;
right: 0;
width: 50%;
height: 100%;
transition: .5s;
padding-inline: 5;
  }
}
</style>
