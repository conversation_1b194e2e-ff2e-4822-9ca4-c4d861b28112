import {
  runMutation,
  runQuery,
  G<PERSON>
} from '../graphQl'
import * as timeLine from '../query/timeLine'
import config from '../../config'

// time line
export const getTimeLineDataApi = (filter, resourceMode) => {
  let skipResourceFetching = true
  const conditions = {
    _and: [
      {
        core_project: {
          deleted: {
            _eq: false
          }
        }
      },
      // this condition is  to  avoid parent level task in  timeline view data
      {
        type: { _eq: 1 }
      }
    ]
  }
  if (filter.upto) {
    conditions._and.push({
      planned_end_date: { _lte: filter.upto }
    })
  }

  if (filter.from) {
    conditions._and.push({
      planned_start_date: { _gte: filter.from }
    })
  }
  if (filter.projectIds.length > 0) {
    conditions._and.push({
      project_id: { _in: filter.projectIds }
    })
  }
  if (filter.userIds.length > 0) {
    conditions._and.push({
      task_assignees: { user_id: { _in: filter.userIds } }
    })
  } else {
    conditions._and.push({

      task_assignees: {
        user_id: {
          _is_null: false
        }
      }

    })
  }
  // here need to add tags also
  if (filter.tags.length > 0) {
    conditions._and.push(
      { tag_tasks: { tag_id: { _in: filter.tags } } }
    )
  }
  if (filter.resourceIds.length > 0) {
    conditions._and.push(
      // { task_material_associations: { core_bom: { bom_items: { bom_version: { active: { _eq: true } }, core_material: { id: { _in: filter.resourceIds } } } } } }
      {
        associated_links_by_source_id: {
          target_feature_id: { _eq: config.CORE_FEATURES.BOM },
          target_bom: { bom_items: { bom_version: { active: { _eq: true } }, core_material: { id: { _in: filter.resourceIds } } } }
        }
      }
    )
  }
  if (resourceMode) {
    // for only fetching tasks with material associations
    conditions._and.push(
      // { task_material_associations: { core_bom: { bom_items: { bom_version: { active: { _eq: true } }, core_material: { type: { _eq: 2 } } } } } }
      {
        associated_links_by_source_id: {
          target_feature_id: { _eq: config.CORE_FEATURES.BOM },
          target_bom: { bom_items: { bom_version: { active: { _eq: true } }, core_material: { type: { _eq: 2 } } } }
        }
      }
    )
    skipResourceFetching = false
  }
  return runQuery(timeLine.getTimeLineDataQuery(), { conditions, skipResourceFetching }, 'tenant')
}

export const updateTimeLineMutation = (taskId, startDate, endDate, deletedAssignees, token) => {
  return runMutation(timeLine.updateTimeLineMutationQuery(), { taskId, startDate, endDate, deletedAssignees }, 'current', token)
}

export const getProjectsUserAsViewer = (userId) => {
  return runQuery(timeLine.getProjectsUserAsViewerQuery(), { userId }, 'tenant')
}
export const addTaskAssignees = (newAssigneeArray, token) => {
  return runMutation(timeLine.addTaskAssigneesQuery(), { newAssigneeArray }, 'current', token)
}
export const deleteChanagesInTimeLine = (query, token) => {
  return runMutation(GQL`${query}`, {}, 'current', token)
}
export const insertTasksData = (query, token) => {
  return runMutation(GQL`${query}`, {}, 'current', token)
}
export const getCalenderDataByids = (projectIds) => {
  return runMutation(timeLine.getCalenderDataByidsQuery(), { projectIds }, 'tenant')
}
export const GetProjectAssocResources = (projectIds, token = 'tenant') => {
  const coreBomConditions = {}
  if (projectIds.length) {
    coreBomConditions.task_material_associations = { project_id: { _in: projectIds } }
  }
  return runMutation(timeLine.getProjectAssocResourcesQuery(), { coreBomConditions }, token)
}
