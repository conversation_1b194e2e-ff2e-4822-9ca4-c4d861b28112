<template>
    <div>
      <div class="v-center">
          <img
          class="mr-3 pointer"
          src="~@/assets/images/icons/arrow-back.svg"
          width="25px"
          alt=""
          @click="goBack"
        />
        <h3>Go back</h3>
        </div>
    <div class="tenant-invite py-3">
      <h2>Edit company details</h2>
      <div class="tenant-invite--form mt-3 s">
        <h3>Company Admin detail</h3>
        <div class="grid-2">
          <div data-validation="FirstName" class="input-group imp my-2">
            <label>Enter First Name</label>
            <input
            :style="disabledStyle"
            :disabled="true"
              v-model="firstName"
              placeholder="Enter your First Name"
              type="text"
            />
          </div>
          <div data-validation="LastName" class="input-group my-2 imp">
            <label>Enter Last Name</label>
            <input
            :style="disabledStyle"
            :disabled="true"
              v-model="lastName"
              placeholder="Enter your Last Name"
              type="text"
            />
          </div>
        </div>
        <div data-validation="Email" class="input-group imp my-2">
            <label>Enter Email ID</label>
            <input :style="disabledStyle" :disabled="true" v-model="email" placeholder="Enter Email ID" type="text" />
          </div>
        <h3 class="my-3">Company detail</h3>
        <div class="grid-2">
          <div data-validation="CompanyGstNumber" class="input-group imp my-2">
            <label>Enter Company GST Number</label>
            <input
            :style="disabledStyle"
             :disabled="true"
              v-model="GSTIN"
              placeholder="Enter Company GST Number"
              type="text"
            />
          </div>
          <div data-validation="CompanyPanNumber" class="input-group imp my-2">
            <label>Enter Company PAN Number</label>
            <input
            :disabled="true"
            :style="disabledStyle"
              v-model="PAN"
              placeholder="Enter Company PAN Number"
              type="text"
            />
          </div>
        </div>
        <div :class="isBeaconAdmin ? 'grid-2':''">
        <div data-validation="CompanyName" class="input-group imp my-2">
          <label>Enter Company Name</label>
          <input
          :style="disabledStyle"
             :disabled="true"
            v-model="companyName"
            placeholder="Enter Company Name"
            type="text"
          />
        </div>
        <div v-if="isBeaconAdmin" class="input-group imp my-2">
        <label class="key">Tenant Type:</label>
        <select v-model="tenant_type">
          <option v-for="item in roles" :value="item.key" :key="item.key">{{ item.value }}</option>
        </select>
      </div>
      </div>
        <div class="grid-2">
          <div data-validation="CompanyEmail" class="input-group imp my-2">
            <label>Enter Email</label>
            <input v-model="companyEmail" placeholder="Enter Email" type="text" />
          </div>
          <div data-validation="CompanyPhoneNumber" class="input-group imp my-2">
            <label>Enter Company Phone Number</label>
            <input
              v-model="companyPhone"
              placeholder="Enter Company Phone Number"
              type="text"
            />
          </div>
        </div>
        <div class="grid-2">
          <div data-validation="Latitude" class="input-group my-2 imp">
          <label>Company Location latitude</label>
      <input
        v-model.number="latitude"
        placeholder="Company Location latitude"
        type="number"
      />
        </div>
        <div data-validation="Longitude" class="input-group my-2 imp">
          <label>Company Location longitude</label>
      <input
        v-model.number="longitude"
        placeholder="Company Location longitude"
        type="number"
      />
        </div>
          <div  class="input-group my-2">
            <label>Company Description</label>
            <textarea
              v-model="description"
              placeholder="Additional Notes "
              type="text"
            />
          </div>
          <div  class="input-group my-2 imp">
          <label>Industry vertical</label>
          <input :disabled="true" v-model="selectedIndustry" :style="disabledStyle">
        </div>
      </div>
      <div class="grid-2">
        <div data-validation="Address" class="input-group my-2">
          <label>Enter URL</label>
          <input
            placeholder="Enter URL "
            type="text"
            v-model="companyUrl"
          />
        </div>
        <div class="input-group my-2">
          <label>Upload Image</label>
          <input type="file" accept="image/jpeg, image/png, image/jpg" @change=uploadImage>
        </div>
        </div>
        <div class="flex-end">
          <button @click="resetForm" class="btn btn btn-black-outline mx-3">Reset</button>
          <button @click="updateTenantDetails" :disabled="disableSaveButton" class="btn">Save Changes</button>
        </div>
      </div>
    </div>
  </div>
  </template>

<script>
import InviteTenantValidation from '@/helper/formValidation/inviteTenant'
import { GetAllTenantsListDataForBeaconAdmin, GetTenantDetails, updateTenantDetails, generateS3SubmittingUrl, updateTenantCompanyAssciation } from '@/api'
import { alert, success } from '@/plugins/notification'
import Loader from '@/plugins/loader'
import { mapGetters } from 'vuex'

export default {
  components: {},
  name: 'InviteTenant',
  props: {
    collaborator: Boolean
  },
  async mounted () {
    this.tenantId = this.$route.params.tenantId
    GetTenantDetails(this.tenantId, this.isBeaconAdmin, this.openTenantId).then(res => {
      this.firstName = res.core_tenants[0].tenant_users[0].associated_user.first_name
      this.lastName = res.core_tenants[0].tenant_users[0].associated_user.last_name
      this.email = res.core_tenants[0].tenant_users[0].associated_user.email
      this.companyName = res.core_tenants[0].company_name
      this.PAN = res.core_tenants[0].PAN
      this.GSTIN = res.core_tenants[0].GSTIN
      this.companyLocation = res.core_tenants[0].company_location

      this.tenant_type = res.core_tenants[0].tenant_type
      this.companyLogo = res.core_tenants[0].company_logo_blob_key
      this.companyUrl = res.core_tenants[0].company_url
      let companyLocation = null
      // if the user is a beacon admin, we fetch the details from core_tenants directly
      // otherwise, we fetch the details from tenant_company_association
      if (this.isBeaconAdmin) {
        companyLocation = res.core_tenants[0].company_location
        this.description = res.core_tenants[0].company_description
        this.companyEmail = res.core_tenants[0].company_email
        this.companyPhone = res.core_tenants[0].company_phone ?? ''
        this.selectedIndustry = res.core_tenants[0].industry_vertical_value.name
      } else {
        companyLocation = res.core_tenants[0]?.associated_tenants_by_target_tenant_id?.[0].company_location
        this.description = res.core_tenants[0]?.associated_tenants_by_target_tenant_id?.[0].company_description
        this.companyEmail = res.core_tenants[0]?.associated_tenants_by_target_tenant_id?.[0].company_email
        this.companyPhone = res.core_tenants[0]?.associated_tenants_by_target_tenant_id?.[0].company_phone ?? ''
        this.selectedIndustry = res.core_tenants[0]?.associated_tenants_by_target_tenant_id?.[0].industry_vertical_value.name
      }
      const locationArray = companyLocation ? companyLocation.replace(/[()]/g, '').split(',') : null
      this.latitude = !locationArray ? '' : locationArray[0]
      this.longitude = !locationArray ? '' : locationArray[1]
    })
  },
  data () {
    return {
      disableSaveButton: false,
      tenant_type: null,
      roles: [{ key: 1, value: 'Admin' }, { key: 2, value: 'Collaborator' }],
      tenantId: null,
      firstName: '',
      lastName: '',
      email: '',
      companyName: '',
      PAN: '',
      GSTIN: '',
      companyPhone: '',
      companyEmail: '',
      description: null,
      companyLocation: '',
      disabledStyle: {
        backgroundColor: '#f0f0f0',
        color: '#a0a0a0'
      },
      companyUrl: null,
      companyLogo: null,
      latitude: null,
      longitude: null,
      selectedIndustry: null
    }
  },
  computed: {
    ...mapGetters(['isBeaconAdmin', 'openTenantId'])
  },
  methods: {
    resetForm () {
      this.companyPhone = ''
      this.companyEmail = ''
      this.description = null
      this.companyUrl = null
      this.companyLogo = null
    },
    async updateTenantDetails () {
      if (this.disableSaveButton) return
      this.disableSaveButton = true
      const tempbody = {
        firstName: this.firstName,
        lastName: this.lastName,
        email: this.email,
        companyName: this.companyName,
        PAN: this.PAN,
        GSTIN: this.GSTIN,
        companyPhone: this.companyPhone,
        companyEmail: this.companyEmail,
        description: this.description,
        industryVertical: this.selectedIndustry,
        latitude: this.latitude,
        longitude: this.longitude
      }
      const loader = new Loader()
      if (InviteTenantValidation(tempbody)) {
        loader.show()
        let payload = {} // payload for tenant_company_association
        let body = {} // body for core_tenants
        if (this.isBeaconAdmin) {
          body = {
            company_email: this.companyEmail,
            company_url: this.companyUrl,
            company_logo_blob_key: this.companyLogo,
            company_location: `(${this.latitude}, ${this.longitude})`,
            company_phone: this.companyPhone,
            company_description: this.description
          }
        } else {
          payload = {
            company_phone: this.companyPhone,
            company_description: this.description,
            company_location: `(${this.latitude}, ${this.longitude})`,
            company_email: this.companyEmail
          }
        }

        try {
          if (this.isBeaconAdmin) {
            await updateTenantDetails(this.tenantId, body, this.isBeaconAdmin)
          } else {
            updateTenantCompanyAssciation(this.tenantId, payload, this.isBeaconAdmin)
          }
          loader.hide()
          success('Successfully updated the details')
          this.resetForm()
          if (this.isBeaconAdmin) {
            this.getAllTenantList()
          }
          this.$router.go(-1)
        } catch (err) {
          loader.hide()
          alert(err?.message ?? 'There was an error, updating the tenant. Please try again')
        } finally {
          loader.hide()
          this.disableSaveButton = false
        }
        // updateTenantDetails(this.tenantId, body, this.isBeaconAdmin).then(res => {
        //   success('Successfully updated the details')
        //   this.resetForm()
        //   this.getAllTenantList()
        //   this.$router.go(-1)
        // }).catch((err) => {
        //   alert(err?.message ?? 'There was an error, updating the tenant. Please try again')
        // }).finally(() => {
        //   loader.hide()
        // })
      }
    },
    goBack () {
      this.$router.go(-1)
    },
    getAllTenantList () {
      GetAllTenantsListDataForBeaconAdmin().then((res) => {
        this.$store.commit('setTenentList', res?.core_tenants)
      })
    },
    uploadImage (e) {
      const file = e.target.files[0]
      const fileReader = new FileReader()
      fileReader.readAsDataURL(file)
      fileReader.onload = async (e) => {
        const { url } = await generateS3SubmittingUrl({
          tenantId: this.openTenantId,
          feature: 'metaData',
          featureId: 0,
          fileName: file.name
        })
        await fetch(url, {
          method: 'PUT',
          body: file,
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        const blobkey = url.split('?')[0].split('.com/').pop()
        this.companyLogo = blobkey
      }
    }
  }
}
</script>

  <style lang="scss" scoped>
  .tenant-invite {
    max-width: 800px;
    margin: auto;
    & h2, & h3 {
      font-weight: 500;
    }
    & h3 {
      font-size: 16px;
    }
    &--form {
      background: var(--white);
      padding: 20px;
      & > div {
        margin: auto;
      }
    }
  }
  .input-group {
  input, select ,textarea {
    font-size: 1.11em;
  }
}
  </style>
