import config from '../../config.js'
import { runMutation, runQuery } from '../graphQl'
import {
  GetUserDetailByUserId,
  GetAllTenantsList,
  GetAllUsers,
  updateTenanUser,
  GetUserDetailsByIds,
  getProjectUserAssociationQuery,
  GetAllCollabaratorTenantList,
  GetAllTenantsListForBeaconAdmin,
  UpdateTenantStatus,
  getProjectUserExceptViewerQuery,
  updateCollaboratingTenantStatusMutation,
  GetTenantDetailsQuery,
  updateTenantDetailQuery,
  getProjectUserForCollaboratorQuery,
  getIndustryVerticalQuery,
  updateTenantCompanyAssciationQuery,
  GetUserGroupsQuery,
  CreateNewUserGroupMutation,
  DeleteUserGroupMutation,
  GetUserGroupDetailQuery,
  AddUsersToUserGroupMutation,
  RemoveUsersFromUserGroupMutation,
  UpdateUserGroupMutation,
  findExistingUserGroupQuery,
  restoreUserGroupMutation,
  GetUserGroupsWithoutLimitQuery,
  GetTenantDetailsForDtxAdminQuery,
  GetTenantDetailsByGSTIN
} from '../query/user'
import http from '../http'
import { GetUsersRoles } from '../query/config'
import store from '@/store/index.js'

export const InviteTenants = (body) => {
  return http.POST(config.serverEndpoint + '/tenant', body)
}

export const updateTenantDetails = (id, body, beaconAdmin = false) => {
  return runMutation(updateTenantDetailQuery(), { id, body }, beaconAdmin ? 'auth' : 'tenant')
}

export const updateTenantCompanyAssciation = (id, payload, beaconAdmin = false) => {
  return runMutation(updateTenantCompanyAssciationQuery(), { id, payload }, beaconAdmin ? 'auth' : 'tenant')
}
export const ReInviteTenant = (body) => {
  return http.POST(config.serverEndpoint + '/tenant/resendInvite', body)
}

export const InviteCollaborator = (body) => {
  return http.POST(config.serverEndpoint + '/tenant/collaborator', body, 'tenant')
}
export const InviteUser = (body, token = 'tenant') => {
  return http.POST(config.serverEndpoint + '/user/inviteToTenant', body, token)
}
export const ReInviteUser = (body) => {
  return http.POST(config.serverEndpoint + '/user/resendInvite', body)
}

export const GetUsersRolesData = () => {
  return runQuery(GetUsersRoles())
}

export const GetUserDetailDataByUserId = (userId, token = null) => {
  return runQuery(GetUserDetailByUserId(userId), {}, token)
}

export const GetUserDetailsByUserIds = (ids) => {
  return runQuery(GetUserDetailsByIds(), ids, 'tenant')
}

export const GetAllTenantsListData = (userId, targetTenantId, token = 'tenant') => {
  return runQuery(GetAllTenantsList(), { userId, targetTenantId }, token)
}

export const GetAllTenantsListDataForBeaconAdmin = (token = 'auth') => {
  return runQuery(GetAllTenantsListForBeaconAdmin(), { }, token)
}

export const UpdateTenantStatusBeaconAdmin = (tenantId, status, token = 'auth') => {
  return runQuery(UpdateTenantStatus(), { tenantId, status }, token)
}

export const GetTenantDetails = (tenantId, beaconAdmin = false, parentTenantId) => {
  if (beaconAdmin) {
    return runQuery(GetTenantDetailsQuery(), { tenantId }, 'auth')
  } else {
    return runQuery(GetTenantDetailsForDtxAdminQuery(), { tenantId, parentTenantId }, 'tenant')
  }
}
export const FetchTenantDetailsByGSTIN = (GSTIN) => {
  return runQuery(GetTenantDetailsByGSTIN(), { GSTIN }, 'tenant')
}
export const GetAllUsersList = (tenant) => {
  let tenantId = null
  const collaboratorId = store.getters?.collaboratorId
  if (collaboratorId) {
    tenantId = collaboratorId
  } else {
    tenantId = tenant
  }
  return runQuery(GetAllUsers(), { tenantId }, 'tenant')
}

export const UpdateUserRoleForTenantUser = (variables) => {
  return runMutation(updateTenanUser(), variables, 'tenant')
}
export const getProjectUserAssociation = (userId) => {
  return runQuery(getProjectUserAssociationQuery(), { userId }, 'tenant')
}
export const getProjectUserExceptViewer = (userId, projectId = null) => {
  if (projectId === null) {
    return runQuery(getProjectUserExceptViewerQuery(), { userId }, 'tenant')
  } else {
    return runQuery(getProjectUserExceptViewerQuery(), { userId, projectId }, 'project')
  }
}

export const GetAllCollabaratorList = (project = false) => {
  const sourceTenantId = localStorage.getItem(config.localstorageKeys.LAST_OPENED_TENANT)
  return runQuery(GetAllCollabaratorTenantList(), { sourceTenantId }, project ? 'project' : 'tenant')
}

export const updateCollaboratingTenantStatus = (tenantId, status) => {
  return runMutation(updateCollaboratingTenantStatusMutation(), { tenantId, status }, 'tenant')
}

export const getProjectUserForCollaborator = (userId) => {
  return runQuery(getProjectUserForCollaboratorQuery(), { userId, collaboratorId: store.getters?.collaboratorId }, 'tenant')
}

export const GetAllIndustryVertical = (name) => {
  const beaconAdmin = store.getters.user.beacon_admin
  return runMutation(getIndustryVerticalQuery(), { name }, beaconAdmin ? 'auth' : 'tenant')
}

export const GetUserGroups = (offset = 0, limit = 10, search) => {
  const conditions = { deleted: { _eq: false } }
  if (search?.length) {
    conditions.name = { _ilike: `%${search}%` }
  }
  return runQuery(GetUserGroupsQuery(), { offset, limit, conditions }, 'tenant')
}
export const GetUserGroupsWithoutLimit = () => {
  return runQuery(GetUserGroupsWithoutLimitQuery(), { }, 'tenant')
}

export const CreateNewUserGroup = (data) => {
  return runMutation(CreateNewUserGroupMutation(), { data }, 'tenant')
}

export const DeleteUserGroup = (id) => {
  return runMutation(DeleteUserGroupMutation(), { id }, 'tenant')
}

export const GetUserGroupDetail = (id) => {
  return runQuery(GetUserGroupDetailQuery(), { id }, 'tenant')
}

export const AddUsersToUserGroup = (objects) => {
  return runMutation(AddUsersToUserGroupMutation(), { objects }, 'tenant')
}

export const RemoveUsersFromUserGroup = (userIds, groupId) => {
  return runMutation(RemoveUsersFromUserGroupMutation(), { userIds, groupId }, 'tenant')
}

export const UpdateUserGroup = (id, name, description) => {
  return runMutation(UpdateUserGroupMutation(), { id, name, description }, 'tenant')
}

export const findExistingUserGroup = (name) => {
  return runQuery(findExistingUserGroupQuery(), { name }, 'tenant')
}

export const restoreUserGroup = (name) => {
  return runMutation(restoreUserGroupMutation(), { name }, 'tenant')
}
