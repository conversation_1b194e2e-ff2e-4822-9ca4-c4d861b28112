<template>
  <div class="select-bom">
    <div v-if="bomListLoading" class="select-bom-container">
      <loading-circle />
    </div>
    <div
      v-else-if="bomList.length === 0"
      class="select-bom-container no-bom-found"
    >
      <span class="text-muted">No BOM found</span>
    </div>
    <div v-else class="select-bom-container">
      <div
        :class="{
          selected: bom.id === selectedBom?.id,
          disable: !(bom.state === CHECK_IN  || bom.state === LOCK )
        }"
        class="select-bom-option"
        v-for="bom in bomList"
        :key="bom.id"
        @click="selectBom(bom)"
        v-overflow-tooltip
        :title="bom.state === 2 ? '' : 'BOM is not approved yet'"
      >
      <img
          v-if="bom.state === 2"
          src="~@/assets/images/icons/checkin-icon.svg"
          class="select-bom-option--icon"
          v-tooltip="'Bom is checked in'"
        />
        <img
          v-if="bom.state === 3"
          src="~@/assets/images/icons/checkout-icon.svg"
          class="select-bom-option--icon"
          v-tooltip="'Bom is checked out'"
        />
        <img
          v-if="bom.state === LOCK"
          src="~@/assets/images/icons/locked-icon.svg"
          class="select-bom-option--icon"
          v-tooltip="'Bom is locked'"
        />
        {{ bom.name }}
      </div>
    </div>
    <div class="v-center flex-end m" v-if="showCloseButton">
      <button
        class="btn btn-black mt-3"
        @click="$emit('close')"
      >
        Close
      </button>
    </div>
  </div>
</template>

<script>
import { GetAllBomList } from '@/api'
import loadingCircle from '../../common/loadingCircle.vue'
import Config from '@/config'
export default {
  components: { loadingCircle },
  props: {
    product_code: {
      type: [String, Number],
      default: null
    },
    selectedBomId: {
      type: [String, Number],
      default: null
    },
    showCloseButton: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      bomList: [],
      bomListLoading: false,
      selectedBom: null,
      LOCK: Config.BOM_STATE_MAP.LOCK,
      CHECK_IN: Config.BOM_STATE_MAP.CHECKIN
    }
  },
  methods: {
    selectBom (bom) {
      if (this.selectedBom === bom.id) {
        this.selectedBom = null
        this.$emit('selectBom', {})
        return
      }
      this.selectedBom = bom.id
      this.$emit('selectBom', bom)
    },
    getBomList () {
      if (!this.product_code) return
      this.bomListLoading = true
      GetAllBomList(this.product_code)
        .then((res) => {
          this.bomList = res.core_bom.map((bom) => {
            return {
              id: bom.id,
              name: bom.name,
              version: bom.bom_versions?.find((version) => version.active)?.version_no,
              versionId: bom.bom_versions?.find((version) => version.active)?.id,
              state: bom?.state
            }
          })
          this.bomListLoading = false
        })
        .catch(() => {
          this.bomListLoading = false
        })
    }
  },
  created () {
    this.getBomList()
    this.selectedBom = this.selectedBomId
  }
}
</script>

<style lang="scss" scoped >
.select-bom {
  width: 600px;
  // background-color: rgba(var(--brand-rgb), 0.3);
  margin: -10px;
  padding: 10px;
  &-container {
    overflow: auto;
    max-height: 200px;
    min-height: 200px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    grid-gap: 10px;
    &.no-bom-found {
      font-size: 1.2rem;
    }
  }
  &-option {
    position: relative;
    width: 120px;
    height: 40px;
    padding: 10px 10px;
    margin: 5px;
    border-radius: 8px;
    font-size: 12px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    cursor: pointer;
    text-align: center;
    background: rgba(var(--brand-rgb), 0.3);
    &.selected {
      background-color: rgba(var(--brand-rgb), 1) !important;
    }
    &:hover {
      background-color: rgba(var(--brand-rgb), 0.7);
    }
    &.disable {
      background-color: rgba(var(--brand-rgb), 0.3) !important;
      cursor: not-allowed;
      pointer-events: none;
      opacity: 0.5;
    }
    &--icon {
      position: absolute;
      top: 2px;
      right: 2px;
      width: 12px;
      height: 12px;
      z-index: 1;
    }
  }
}
</style>
