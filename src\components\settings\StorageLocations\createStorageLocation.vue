
<template>
  <div class="storage-location-create s p-4">
    <div class="grid-2 mt-2">
      <div class="input-group imp" >
        <label class="key">Name:</label>
        <input type="text" v-model="storageLocation.name" />
      </div>
      <div class="input-group imp">
        <label class="key">City:</label>
        <input type="text" v-model="storageLocation.city" />
      </div>
    </div>
    <div class="grid-2 mt-2">
      <div class="input-group imp">
        <label class="key">State:</label>
        <input type="text" v-model="storageLocation.state" />
      </div>
      <div class="input-group imp">
        <label class="key">Pincode:</label>
        <input type="number" v-model="storageLocation.pincode" @keydown="changeNumber"/>
      </div>
    </div>
    <div class="grid-1 mt-2">
      <div class="input-group imp">
        <label class="key">Adrress Line 1:</label>
        <textarea v-model="storageLocation.address_line_1" />
      </div>
    </div>
    <div class="grid-1 mt-2">
      <div class="input-group imp">
        <label class="key">Adrress Line 2:</label>
        <textarea v-model="storageLocation.address_line_2" />
      </div>
    </div>
    <div class="grid-2 mt-2">
      <div class="input-group imp">
        <label class="key">Location Longitude:</label>
        <input type="number" v-model.number="storageLocation.longitude" @keydown="changeNumber" />
      </div>
      <div class="input-group imp">
        <label class="key">Location Latitude:</label>
        <input type="number" v-model.number="storageLocation.latitude" @keydown="changeNumber" />
      </div>
    </div>
    <div class="flex flex-end py-3 l">
      <button class="btn btn-black mr-3 pointer" @click="$emit('close')">
        CANCEL
      </button>
      <button
        v-if="!forEdit"
        class="btn pointer"
        @click="createLocation"
        :disabled="buttonDisabled"
      >
        SAVE
      </button>
      <button
        v-if="forEdit"
        class="btn pointer"
        @click="updateLocation"
      >
        UPDATE
      </button>
    </div>
  </div>
</template>

<script>
import Loader from '@/plugins/loader'
import { createStorageLocation, updateStorageLocations } from '@/api'
import { isValidLatitude, isValidLongitude, restrictKeys } from '@/utils/validations'
import { alert } from '@/plugins/notification'
export default {
  name: 'CreateStorageLocation',
  props: {
    forEdit: {
      default: false,
      type: Boolean
    },
    open: {
      default: false,
      type: Boolean
    },
    locationObj: {
      default: null,
      type: Object
    }
  },
  data: () => ({
    storageLocation: {
      name: '',
      address_line_1: '',
      address_line_2: '',
      city: '',
      latitude: null,
      longitude: null,
      pincode: '',
      state: ''
    },
    buttonDisabled: false,
    window: window
  }),
  methods: {
    resetForm () {
      this.storageLocation = {
        name: '',
        address_line_1: '',
        address_line_2: '',
        city: '',
        location: '',
        pincode: '',
        state: ''
      }
    },
    changeNumber (event) {
      if (['e', 'E', '+', '-'].includes(event.key)) {
        restrictKeys(event)
      }
    },
    async createLocation () {
      this.buttonDisabled = true
      const loader = new Loader()

      try {
        const insertObj = {
          name: this.storageLocation.name,
          address_line_1: this.storageLocation.address_line_1,
          address_line_2: this.storageLocation.address_line_2,
          city: this.storageLocation.city,
          pincode: this.storageLocation.pincode,
          state: this.storageLocation.state,
          location: `(${this.storageLocation.latitude}, ${this.storageLocation.longitude})`
        }
        for (const key of Object.keys(insertObj)) {
          if (!insertObj[key]) {
            let errMsg = `Please input ${key}`
            if (key.includes('_')) {
              errMsg = `Please input ${key.split('_').map(str => str[0].toUpperCase() + str.slice(1, str.length)).join(' ')}`
            }
            this.$notify.alert(errMsg)
            return
          } else if (!this.storageLocation.latitude) {
            this.$notify.alert('Please input location latitude')
            return
          } else if (!this.storageLocation.longitude) {
            this.$notify.alert('Please input location longitude')
            return
          }
        }
        const err = []
        if (!isValidLatitude(this.storageLocation.latitude)) {
          err.push(' Latitude')
        }
        if (!isValidLongitude(this.storageLocation.longitude)) {
          err.push(' Longitude')
        }
        if (err.length > 0) {
          err.length === 1 ? alert('Invalid ' + err[0]) : alert('Invalid' + err.join(' and '))
          return
        }
        loader.show()
        const res = await createStorageLocation(insertObj)
        if (!res.insert_address_locations_one) {
          this.$notify.alert('Storage location name already exists')
          return
        }
        insertObj.id = res.insert_address_locations_one?.id
        insertObj.created_on = res.insert_address_locations_one?.created_on
        insertObj.created_by_user = res.insert_address_locations_one?.created_by_user
        this.$emit('save-and-close', JSON.parse(JSON.stringify(insertObj)))
        this.$notify.success('Storage Location Created successfully')
        this.resetForm()
        this.$emit('update')
      } catch (err) {
        this.$notify.alert(err[0]?.message ?? 'Something went wrong')
      } finally {
        loader.hide()
        this.buttonDisabled = false
      }
    },
    async updateLocation () {
      this.buttonDisabled = true
      const loader = new Loader()
      const err = []
      if (!isValidLatitude(this.storageLocation.latitude)) {
        err.push(' Latitude')
      }
      if (!isValidLongitude(this.storageLocation.longitude)) {
        err.push(' Longitude')
      }
      if (err.length > 0) {
        err.length === 1 ? alert('Invalid ' + err[0]) : alert('Invalid' + err.join(' and '))
        return
      }
      loader.show()
      try {
        const updateObj = {
          name: this.storageLocation.name,
          address_line_1: this.storageLocation.address_line_1,
          address_line_2: this.storageLocation.address_line_2,
          city: this.storageLocation.city,
          pincode: `${this.storageLocation.pincode}`,
          state: this.storageLocation.state,
          location: `(${this.storageLocation.latitude}, ${this.storageLocation.longitude})`
        }
        await updateStorageLocations(this.locationObj.id, updateObj)
        this.$notify.success('Updated data successfully')
        updateObj.id = this.locationObj.id
        this.$emit('update-and-close', JSON.parse(JSON.stringify(updateObj)))
      } catch (err) {
        this.buttonDisabled = false
        if (err[0]?.message?.includes('Uniqueness violation. duplicate key value violates unique constraint')) {
          this.$notify.alert('Storage location name already exists')
        } else {
          this.$notify.alert(err?.message ?? 'Something went wrong')
        }
      } finally {
        loader.hide()
        this.buttonDisabled = false
      }
    },
    keyPress (e) {
      const activeElement = this.window.document.activeElement.tagName.toLowerCase()
      const activeElementCheck = ['input', 'select', 'textarea'].includes(activeElement)
      if (e instanceof KeyboardEvent && e.code === 'Enter' && activeElement !== 'button') {
        this.window.document.activeElement.blur()
      }
      if (!open || activeElementCheck || this.buttonDisabled) {
        return
      }
      if (e instanceof KeyboardEvent && e.code === 'Enter') {
        !this.forEdit ? this.createLocation() : this.updateLocation()
      }
    }
  },
  mounted () {
    if (this.forEdit) {
      const locationObj = JSON.parse(JSON.stringify(this.locationObj))
      this.storageLocation.name = locationObj.name
      this.storageLocation.address_line_1 = locationObj.address_line_1
      this.storageLocation.address_line_2 = locationObj.address_line_2
      this.storageLocation.city = locationObj.city
      this.storageLocation.pincode = locationObj.pincode
      this.storageLocation.state = locationObj.state
      const location = locationObj.location
        ? locationObj.location.replace('(', '').replace(')', '').split(',')
        : []
      this.storageLocation.latitude = Number(location[0]) ?? null
      this.storageLocation.longitude = Number(location[1]) ?? null
    }
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
  }

}
</script>

<style lang="scss" scoped>
.storage-location-create{
    min-width: 40vw;
}
</style>
