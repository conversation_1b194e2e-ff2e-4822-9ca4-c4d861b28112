
<template>
    <div id="timeSheeEditModal" class="timeSheet flex column fw fh" :key="componentKey">

  <div class="flex space-between v-center">
  <span class="timeSheet-projectName" v-overflow-tooltip >{{ timeSheetModalData?.selectedProject?.name}}</span>
   <span class="timeSheet-date flex-end">{{ timeSheetModalData?.selectedDate }}</span>
</div>
<div class="flex-grow timeSheet-modal-content mt-5">
  <div class=" grid-1-2">
    <label for="">Total working Hours  : </label>
    <span class="flex v-center">
      <input v-if="entryData?.subTaskData?.length > 0" class="v-center h-center " type="datetime" v-model="entryData.totalDuration"  @blur="validateEnteredValue($event)" :disabled="true" v-on:keyup.enter="validateEnteredValue($event)" >
      <input v-else class="v-center h-center " type="datetime" v-model="entryData.duration"  @blur="validateEnteredValue($event)" @keydown="preventInvalidChars"  :disabled="disableTimeInput" v-on:keyup.enter="validateEnteredValue($event)" >
     &nbsp; hrs
    </span>
  </div>
  <div class=" grid-1-2 mt-5">
    <label for="">Mark day type </label>
    <select class="timeSheet-modal-content-select" :class="(!editMode&& entryData.approved) ? 'disabel-arrow':''" @change="updateDaytype($event)" :disabled="!editMode || entryData.approved">
      <option :value="1">Working Day</option>
      <option :value="2" :selected="entryData.is_holiday">Holiday</option>
      <option :value="3" :selected="entryData.is_absent">Absent</option>
    </select>
  </div>
  <div class=" grid-1-2 mt-5 mb-4">
    <label for="" class="">Tag :  </label>
    <select class="timeSheet-modal-content-select w-100 elipsis-text" :class="(!editMode&& entryData.approved)? 'disabel-arrow':''" @change="updateTag($event)" :disabled="!editMode || entryData.approved ||entryData.is_holiday ||entryData.is_absent">
      <option value="">Not selected</option>
      <option v-for="tag in tagsData" :value="tag.id" :key="tag.id" :selected="entryData.tag_id===tag.id">{{ tag?.name }}</option>

    </select>
  </div>
  <div class=" grid-1-2 mt-2" v-if="entryData?.subTaskData?.length > 0">
    <div></div>
    <div class="task-entry-box" >
      <span class="">Spent</span>
      <span class="">Remaining <span class="task-entry-box-mandatory">*</span>  </span>
      <span class="align-center">Rework</span>
      <span class="align-center"></span>
   </div>
  </div>
  <div  v-for="(taskentry, index) in entryData.subTaskData" :key="taskentry.id">
    <div class=" grid-1-2 mt-2">
    <label for=""  v-overflow-tooltip class="w-100 elipsis-text"> {{ taskNames[taskentry.task_id] }} &nbsp; <img v-if="taskentry.approved" width="20px" src="~@/assets/images/icons/approved.svg" alt="" /></label>
    <span  class="task-entry-box">
      <span class="flex v-center  ">
        <input class="v-center h-center  " type="datetime" v-model="taskentry.duration" @keydown="preventInvalidChars" @blur="validateEnteredValueWithTask($event,index)" :disabled="disableTimeInput" v-on:keyup.enter="validateEnteredValueWithTask($event,index)" >
        hrs
      </span>
      <span class="flex v-center ">
        <input class="v-center h-center " type="datetime" v-model="taskentry.time_remaining" @keydown="preventInvalidChars"  @blur="addTimeRemaining($event,index)" :disabled="disableTimeInput" v-on:keyup.enter="addTimeRemaining($event,index)">
     hrs</span>
        <img  class="align-center " v-if="taskentry.rework===true"  @click=" editMode && reworkChanged(index, false)" src="~@/assets/images/icons/check-circle.svg" />
        <img   class="align-center " v-else  @click=" editMode && reworkChanged(index, true)"  src="~@/assets/images/icons/circle.svg" />
       <!-- <input type="checkbox"  class="rework-check-box align-center" v-model="taskentry.rework" :checked="entryData.rework" :value="entryData.rework" :disabled="!editMode" @change="reworkChanged(index)"><label for="cb1"></label> -->
     <img class="align-center " v-if="editMode&& !taskentry.approved"  width="18" src="~@/assets/images/delete-icon.svg" @click="removeSelectedTask(taskentry.task_id)" />
    </span>
  </div>
      <add-material :filterMaterial="filterMaterial" :existingMaterials="taskentry" @addMaterial="addMaterials($event,index)" :disableEdit="disableTimeInput"/>
  </div>
  <div class=" grid-1-2 mt-2" v-if="(editMode&&!entryData.approved) && !(entryData.is_holiday || entryData.is_absent)" >
    <label for="">Select Sub Task :  </label>
    <select  class="timeSheet-modal-content-select w-100 elipsis-text" @change="addNewTask($event)" >
      <option value=" w-100  elipsis-text">Not selected</option>
      <option  v-overflow-tooltip  v-for="task in taskFilterd" :value="task.id" :key="task.id">{{ task.name }}</option>

    </select>
  </div>
  <div class=" flex column mt-5">
    <label for="">Description : </label>
    <textarea class="mt-1" v-model="entryData.description" @keydown.stop :disabled="!editMode">

</textarea>
  </div>

</div>

<div class="flex-end time-sheet-footer v-center">
  <button class="btn btn-black mr-3 pointer" @click="$emit('close')">Close</button>
      <button class="btn btn-brand pointer" @click="saveModalEntryChanges"  v-if="editMode">Add</button>
</div>
</div>
  </template>

<script>
import { convertToTimeFormat, calculateTotalDuration, convertToTimeFormatWithoutLimit } from '@/utils/timeSheetHelper'
import { GetAllTaskTags, getProjectExchangeToken, getTaskDataFromProject, GetCalendarBasedOnProjectId } from '@/api'
import { alert } from '@/plugins/notification'
import { mapGetters, mapMutations } from 'vuex'
import AddMaterial from './addMaterial.vue'
export default {
  name: 'timeSheeEditModal',
  components: {
    AddMaterial
  },
  props: {
    timeSheetModalData: {
      type: Object,
      default: () => ({
        selectedProject: { name: 'no name' }
      })
    },
    editMode: { type: Boolean, required: true },
    open: { type: Boolean, required: true }
  },
  data: () => ({
    tagsData: [],
    entryData: {},
    taskData: [], // it hgs all; task data asscoiated with a project
    taskIdsUsedInEntry: [], // contains the id of  each task data which is used in time sheet
    taskFilterd: [], // contains the data of task which is not yet used in time sheet
    taskNames: {}, // contains all taskid as property and as value name of task has been added
    componentKey: -1,
    calenderData: 1,
    filterMaterial: null
  }),
  methods: {
    ...mapMutations('timeLine', ['addNewToken', 'clearTokenHashMap']),
    validateEnteredValue (e) {
      const newtime = convertToTimeFormat(String(String(e.target.value)))
      this.entryData.duration = newtime
      if (this.entryData.id) {
        this.entryData.change = 'update'
      } else {
        this.entryData.change = 'new'
      }
    },
    preventInvalidChars (event) {
      const allowedKeys = /[0-9:]|Backspace|Tab|ArrowLeft|ArrowRight/

      // If the key doesn't match the allowed characters, prevent it
      if (!allowedKeys.test(event.key)) {
        event.preventDefault()
      }
    },
    addMaterials (data, index) {
      this.entryData.subTaskData[index].materialData = data
    },
    validateEnteredValueWithTask (e, index) {
      const newtime = convertToTimeFormat(String(String(e.target.value)))
      this.entryData.subTaskData[index].duration = newtime
      const totalDuration = calculateTotalDuration(this.entryData.subTaskData)
      // this condtion is to checking weather the  iuputing time is goung beyond 24 hrs
      // if totalduration > 23.99 the we wil get a false boolean at the place totalDuration
      if (!totalDuration) {
        alert('Exceeding 23:59 hours within the same day is not permissible.')
        this.entryData.subTaskData[index].duration = '00:00:00'
      } else {
        if (this.entryData.subTaskData[index].id) {
          this.entryData.subTaskData[index].change = 'update'
        } else {
          this.entryData.subTaskData[index].change = 'new'
        }
        this.entryData.totalDuration = totalDuration
      }
    },
    async addTimeRemaining (e, index) {
      let newtime = convertToTimeFormatWithoutLimit(String(e.target.value || 0))
      newtime = newtime === '00:00' ? '00:00' : newtime
      this.entryData.subTaskData[index].time_remaining = newtime
      if (this.entryData.subTaskData[index].id) {
        this.entryData.subTaskData[index].change = 'update'
      } else {
        this.entryData.subTaskData[index].change = 'new'
      }
    },
    getTagsData () {
      GetAllTaskTags().then(response => {
        this.tagsData = response.tag
      })
    },
    // function setting the day type  , for aansent and holiday there is no need for duration which is disabled here
    updateDaytype (e) {
      if (e.target.value === '1') {
        this.entryData.is_absent = false
        this.entryData.is_holiday = false
      } else if (e.target.value === '2') {
        this.entryData.is_absent = false
        this.entryData.is_holiday = true
        this.entryData.duration = '00:00:00'
      } else if (e.target.value === '3') {
        this.entryData.is_absent = true
        this.entryData.is_holiday = false
        this.entryData.duration = '00:00:00'
      }
      if (this.entryData.id) {
        this.entryData.change = 'update'
      } else {
        this.entryData.change = 'new'
      }
    },
    updateTag (e) {
      this.entryData.tag_id = e.target.value
      if (this.entryData.id) {
        this.entryData.change = 'update'
      } else {
        this.entryData.change = 'new'
      }
    },
    reworkChanged (index, boolean) {
      const updatedSubTaskData = [...this.entryData.subTaskData]
      updatedSubTaskData[index].rework = boolean
      if (updatedSubTaskData[index]?.id) {
        updatedSubTaskData[index].change = 'update'
      } else {
        updatedSubTaskData[index].change = 'new'
      }
      this.entryData.subTaskData = updatedSubTaskData
    },
    async getTaskData () {
      const projectId = this.entryData.project_id
      if (!this.getProjectTokens[projectId]) {
        // creating project token for saving the data , and storing the value as
        const response = await getProjectExchangeToken(projectId)
        this.addNewToken({ token: response.message, projectId: projectId })
      }
      // localStorage.setItem('project_esp', response.message)
      this.getProjectTokens[projectId] && getTaskDataFromProject(this.entryData.project_id, this.user.userId, this.getProjectTokens[projectId]).then((response) => {
        this.taskData = response.core_tasks
        this.filterUsedTasks()
        this.taskData.forEach((element) => {
          this.taskNames[element.id] = element.name
        })
        this.forceRender()
      })
    },
    getCalenderData () {
      this.entryData.project_id && GetCalendarBasedOnProjectId(this.entryData.project_id).then((response) => {
        this.calenderData = response.core_project_calendar[0]
        this.calenderData.work_time_hours = JSON.parse(this.calenderData?.work_time_hours)
      })
    },
    filterUsedTasks () {
      this.taskFilterd = this.taskData.filter((task, index) => {
        if (this.taskIdsUsedInEntry.includes(task.id)) {
          task.task_material_associations.filter((element) => {
            element.target_bom.bom_versions.map((item) => {
              this.filterMaterial = { data: item, projectId: task.project_id }
            })
          })
        } else {
          return task
        }
      })
    },
    setUsedTasks () {
      this.entryData.subTaskData.forEach((element) => {
        this.taskIdsUsedInEntry.push(element.task_id)
      })
    },
    addNewTask (e) {
      //  if  we are going to update time sheet data wchich is already there in db with out any taskData , and we are adding new task as update ath this time, we need to remove the existing data, (which doesn't have a task id)
      if (this.entryData.subTaskData.length === 0 && this.entryData?.id) {
        this.entryData.removedTaskIds.push(this.entryData.id)
      }
      // create a new object and push into the subtaskData
      this.entryData.subTaskData.push({
        approved: false,
        rework: false,
        description: this.entryData.description,
        duration: '0',
        entry_date: this.entryData.entry_date,
        is_absent: false,
        is_holiday: false,
        project_id: this.entryData.project_id,
        tag_id: this.entryData.tag_id,
        task_id: e.target.value
      })
      this.taskIdsUsedInEntry.push(e.target.value)
      this.entryData.totalDuration = calculateTotalDuration(this.entryData.subTaskData)
    },
    // this function is to remove a task from time sheet entry data
    removeSelectedTask (taskId) {
      this.entryData.subTaskData = this.entryData.subTaskData.filter(taskData => {
        // this conditon to  add the id of  time sheet entry object from removed object list, if there is no id means which is a  new one , so no need to do  delete api call
        if (taskData?.task_id !== taskId) {
          return taskData
        }
        if (taskData?.id && !this.entryData.removedTaskIds.includes(taskData.id)) {
          this.entryData.removedTaskIds.push(taskData.id)
        }
      })
      // need to remove  thi sid from selected taskid => then only it will show in task selection options
      const index = this.taskIdsUsedInEntry.indexOf(taskId)
      this.taskIdsUsedInEntry.splice(index, 1)
      // this function will again resetup the given task ids
      this.filterUsedTasks()
      this.entryData.totalDuration = calculateTotalDuration(this.entryData.subTaskData)
      // if there  was no task data when the time sheet data fetched and we added new task data to it,  for this conditon we have written code for removing  the particular object from db ( instead of this we are adding data with task ids )
      //  need to remove the incomming id  from the removedTaskIds array
      if (this.entryData.subTaskData.length === 0) {
        if (this.entryData.removedTaskIds.includes(this.entryData.id) && this.entryData.id) {
          // const index = this.entryData.removedTaskIds.indexOf(this.entryData.id)
          // this.entryData.removedTaskIds.splice(index, 1)
          this.entryData.task_id = this.entryData.id = undefined
          this.entryData.totalDuration = this.entryData.duration = '00:00:00'
          this.entryData.change = 'new'
          this.entryData.rework = false
        }
      }
    },
    saveModalEntryChanges () {
      // if there is anuy subtask data then any chnages  in  day type,description  are common to all subtasks
      let verifyTimeRemaining = true
      if (this.entryData.subTaskData?.length > 0) {
        this.entryData.subTaskData.forEach((obj) => {
          obj.is_holiday = this.entryData.is_holiday
          obj.is_absent = this.entryData.is_absent
          obj.description = this.entryData.description
          obj.tag_id = this.entryData.tag_id
          obj.projectToken = this.entryData.projectToken
          if (obj.id) {
            obj.change = 'update'
          } else {
            obj.change = 'new'
          }
          // to make time remainig mandatory for each task
          if (!obj.time_remaining) {
            verifyTimeRemaining = false
            alert(`add the time remaining for the task ${this.taskNames[obj.task_id]}`)
          }
        })
      }
      verifyTimeRemaining && this.$emit('addMoreTimeData', { entryData: this.entryData, timeSheetModalData: this.timeSheetModalData })
    },
    forceRender () {
      this.componentKey += -1
    },
    keyPress (e) {
      if (!this.open) return
      if (e instanceof KeyboardEvent && e.code === 'Escape') {
        this.$emit('close')()
      } else if (e instanceof KeyboardEvent && e.code === 'Enter') {
        this.saveModalEntryChanges()
      }
    }
  },
  computed: {
    ...mapGetters(['user', 'collaborator']),
    ...mapGetters('timeLine', ['getProjectTokens']), // this is for to  store toekns each project
    disableTimeInput () {
      if (this.entryData.approved || this.entryData.is_absent || this.entryData.is_holiday || !this.editMode) { return true } else return false
    }
  },
  mounted () {
    this.getTagsData()
    if (this.timeSheetModalData.entry) { this.entryData = JSON.parse(JSON.stringify(this.timeSheetModalData.entry)) }
    this.getTaskData()
    this.setUsedTasks()
  },
  watch: {
    'timeSheetModalData.entry' () {
      this.taskData = []
      this.taskIdsUsedInEntry = []
      this.taskFilterd = []
      this.getTagsData()
      this.entryData = JSON.parse(JSON.stringify(this.timeSheetModalData.entry))
      this.getCalenderData()
      this.getTaskData()
      this.setUsedTasks()
      this.forceRender()
    },
    taskIdsUsedInEntry () {
      this.filterUsedTasks()
    }
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
  }
}

</script>
  <style lang="scss" scoped>
  .timeSheet{
    width: 600px;
    // height:400px;
    &-projectName{
    font-size: 1.5rem;
    border:1px solid #e5e5e5;
    padding: 10px;
    max-width: 50%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    cursor: pointer;
    }
    &-date{
      padding: 10px;
      max-width: 50%;
    }
    &-modal
    {
    &-content{
    border-bottom: 1px solid #e5e5e5;
    max-height: 60vh;
    padding-right: 2px;
    overflow-y: auto;
    & input[type=datetime]{
      border:  1px solid #e5e5e5;
      width:65px;
      padding: 5px;
    }
    & :disabled{
color:black;
background-color: white;
    }
    & .rework-check-box{
      border:  1px solid #e5e5e5;
      padding:0px
    }
    & textarea{
      border:  1px solid #e5e5e5;
      width:100%;
      height: 6rem;
      padding: 5px;
    }
    &-select{
padding:5px;
border:  1px solid #e5e5e5;

    }
    }
  }
  & .grid-1-2{
    display: grid;
  grid-template-columns: 2fr 3fr ;
  grid-gap: 3px;
  align-items: center;
  }
  & .align-center{
    align-self: center;
    justify-self: center;
  }
  & .task-entry-box{
    width: 100%;
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1fr;
    grid-gap: 10px 9px;
    &-mandatory{
      color: red;
    }
  }
  }
  .time-sheet-footer{
height: 60px;
    }
    input[disabled]{
      color:black;
background-color: white;
}
.w-100{
  width: 100%;
}
.disabel-arrow{
  -webkit-appearance: none;
  -moz-appearance: none;
}
  </style>
