<template>
    <div>
      <div class="input-group mt-2">
        <label for="dateInput">Effective Date</label>
        <input type="date" id="dateInput" v-model="selectedDate" :min="getTodayDate()" @keydown="preventTyping">
      </div>
    </div>
</template>

<script>
export default {
  name: 'effectiveDate',
  props: {
    date: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      selectedDate: ''
    }
  },
  watch: {
    selectedDate (newValue) {
      this.$emit('date-selected', newValue)
    },
    date () {
      this.selectedDate = this.$props.date
    }
  },
  methods: {
    getTodayDate () {
      const today = new Date()
      const year = today.getFullYear()
      const month = String(today.getMonth() + 1).padStart(2, '0')
      const day = String(today.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    preventTyping (event) {
      event.preventDefault()
    }
  },
  created () {
    this.selectedDate = this.$props.date
  }
}
</script>

<style>
</style>
