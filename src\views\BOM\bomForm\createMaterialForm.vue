<template>
    <div class="project-form" >
      <h3 v-if="!bomList">{{ formTemplateData.name }}</h3>
    <div>
        <div class="create-form--container">
        <div class="create-form--elements">
          <div class="create-form-autogenerated">
            <div>
                <span class="label">Material name:</span>
                <span class="value">{{ createMaterialValues.material_name }}</span>
              </div>
            <div>
                <span class="label">Material id:</span>
                <span class="value">{{ createMaterialValues.custom_material_id }}</span>
              </div>
          </div>
          <div class="fh center" v-if="loading">
                      <loading-circle />
                    </div>
          <template v-for="(ele, index) in templateField">
            <component
              v-if="!ele.autogenerated"
              :ref="ele.field_id"
              :key="index"
              :is="ele.form_field.key + '_COMPONENT'"
              :mode="!formValueData ? 'CREATE' : 'EDIT'"
              :data="ele"
              :viewOnly="bomList"
              :value="!formValueData ? null : formValueData[ele.field_id]"
            />
          </template>
        </div>
      </div>
  </div>
      <div class="action-btn" v-if="!bomList">
        <button type="button" class="btn btn-black mx-2" @click="cancel">
          Cancel
        </button>
        <button type="button" class="btn"  @click="submitForm"> {{ formValueData === null ? 'SAVE' : 'UPDATE' }}</button>
      </div>
    </div>
  </template>

<script>
import { mapGetters } from 'vuex'
import { getDetailFormTemplate, GetFormDataByFormId } from '@/api'
import spinner from '@/components/common/spinner.vue'
import timeComponent from '@/components/form/elements/timeComponent.vue'
import configurationList from '@/components/form/elements/configurationList.vue'
import fileComponent from '@/components/form/elements/fileComponent.vue'
import dateComponent from '@/components/form/elements/dateComponent.vue'
import longTextComponent from '@/components/form/elements/longTextComponent.vue'
import numberComponent from '@/components/form/elements/numberComponent.vue'
import switchComponent from '@/components/form/elements/switchComponent.vue'
import textComponent from '@/components/form/elements/textComponent.vue'
import companyComponent from '@/components/form/elements/companyComponent.vue'
import userComponent from '@/components/form/elements/userComponent.vue'
import multiUserComponent from '@/components/form/elements/multiUserComponent.vue'
import materialComponent from '@/components/form/elements/materialComponent.vue'
import locationComponent from '@/components/form/elements/locationComponent.vue'
import productCodeComponent from '@/components/form/elements/productCodeComponent.vue'
import LoadingCircle from '../../../components/common/loadingCircle.vue'
import { alert } from '@/plugins/notification'
import { GetFormValueMap } from '@/helper/formValue.js'
import { GetTenantConfigTypes } from '@/api/apis/tenantConfigFeature'
// import { GetTenantConfigTypes } from '@/api/apis/tenantConfigFeature'

export default {
  name: 'create-material-form',
  components: {
    spinner,
    BOOLEAN_COMPONENT: switchComponent,
    NUMBER_COMPONENT: numberComponent,
    TEXT_COMPONENT: textComponent,
    LONG_TEXT_COMPONENT: longTextComponent,
    DATE_COMPONENT: dateComponent,
    TIME_COMPONENT: timeComponent,
    CONFIGURATION_LIST_COMPONENT: configurationList,
    ATTACHMENT_COMPONENT: fileComponent,
    COMPANY_COMPONENT: companyComponent,
    USER_COMPONENT: userComponent,
    MULTI_USER_COMPONENT: multiUserComponent,
    MATERIALS_COMPONENT: materialComponent,
    LOCATION_COMPONENT: locationComponent,
    PRODUCT_CODE_COMPONENT: productCodeComponent,
    LoadingCircle
  },
  data () {
    return {
      templateId: null,
      dueDate: null,
      formTemplateData: {},
      loading: true,
      formValueData: null,
      validationErrors: '' // this is for getting valiadtion errors
    }
  },
  props: {
    createMaterialValues: {
      type: Object,
      default: null
    },
    bomList: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters(['isOnProjectLevel', 'tenantDefaultsData']),
    templateField () {
      if (!this.formTemplateData.template_versions) return []
      const activeVersion =
        this.formTemplateData.template_versions.find((item) => item.active) ||
        {}
      return activeVersion.template_fields || []
    }
  },
  methods: {
    cancel () {
      this.$emit('cancel')
    },
    getFormTemplateData () {
      this.loading = true
      getDetailFormTemplate(this.templateId).then((res) => {
        this.formTemplateData = res.core_form_templates_by_pk
        this.loading = false
      }).catch((err) => {
        console.error('Error fetching template data', err)
        this.loading = false
      })
    },
    submitForm () {
      const form = this.$refs
      const formValue = {}
      this.validationErrors = []
      Object.keys(form).forEach((key) => {
        this.validateInputData(form[key][0]?.data, form)
        if (form[key].length && form[key][0]?.data?.autogenerated === false) {
          formValue[key] = form[key][0].componentValue
        }
      })
      if (this.validationErrors.length > 0) {
        this.validationErrors.forEach(error => alert(error))
        return
      }
      const inputPayload = {}
      Object.keys(formValue).forEach((key) => {
        if (formValue[key] !== undefined && formValue[key] !== null) {
          if (formValue[key] !== 0 && formValue[key].length !== 0) {
            inputPayload[key] = formValue[key]
          }
        }
      })
      this.$emit('create-material', inputPayload)
    },
    validateInputData (formInput, form) {
      if (formInput?.form_field?.key === 'USER') {
        if (formInput.required && (!form[formInput.field_id][0].componentValue || !form[formInput.field_id][0].componentValue.length)) {
          this.validationErrors.push(`${formInput.caption} is a mandatory field`)
        }
      }
      if (formInput?.form_field?.key === 'BOOLEAN') {
        // we are comparing the value to null instead of checking if it is a falsy value cause the boolean component value will false in one case and it is valid value
        if (formInput.required && form[formInput.field_id][0].componentValue === null) {
          this.validationErrors.push(`${formInput.caption} is a mandatory field`)
        }
        return
      } else if (formInput?.form_field?.key === 'MATERIALS') {
        if (formInput.required && form[formInput.field_id][0].componentValue.length < 1) {
          this.validationErrors.push(`${formInput.caption} is a mandatory field`)
        }
        return
      }
      if (formInput.required && !form[formInput.field_id][0].componentValue) {
        this.validationErrors.push(`${formInput.caption} is a mandatory field`)
      }
    },
    async getSTFtempId () {
      if (this.tenantDefaultsData?.material_temp_id) {
        this.templateId = this.tenantDefaultsData.material_temp_id
        return
      }
      const res = await GetTenantConfigTypes()
      const tenantDefaults = res.tenant_defaults?.[0]
      this.$store.commit('setTenentDefaults', tenantDefaults)
      this.templateId = tenantDefaults.tenant_feature_configuration.MATERIAL?.FORM_TEMPLATE_DEFAULT
    }
  },

  async mounted () {
    await this.getSTFtempId()
    await this.getFormTemplateData()
    if (this.createMaterialValues.overridden_material_fields) {
      this.formValueData = this.createMaterialValues.overridden_material_fields
    } else if (this.createMaterialValues.formId !== null) {
      await GetFormDataByFormId(this.createMaterialValues.formId, this.isOnProjectLevel).then(
        (res) => {
          const formData = GetFormValueMap(res.core_forms_by_pk)
          this.formValueData = formData
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped >
  .project-form {
    padding: 12px;
    font-size: 12px;
    overflow-y: auto;
    overflow-x: hidden;
    margin-top: 10px;
    h3 {
      font-size: 20px;
      font-weight: 500;
    }
  }
  .action-btn {
    display: flex;
    padding: 20px;
    padding-left: 14rem;
  }
  .create-form {
  &--nav {
    height: 60px;
    margin: -12px;
    padding: 0 20px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
    & input {
      border: var(--border);
      background-color: transparent;
      border-radius: 4px;
      padding: 4px 8px;
      height: 30px;
      width: 500px;
      outline: none;
      &:focus {
        border-color: var(--brand-color);
      }
    }
  }
  &--container {
    margin: 0 auto;
    overflow-y: auto;
    border-radius: 6px;

  @media (max-height: 650px) {
    height: 362px;
  }

  @media (min-height: 651px) and (max-height: 750px) {
    height: 434px;
  }

  @media (min-height: 751px) and (max-height: 850px) {
    height: 492px;
  }

  @media (min-height: 851px) {
    height: 300px;
  }
  }
  &--elements {
    overflow-y: auto;
    padding: 4px;
  }
  &-autogenerated {
    background-color: rgba(var(--brand-rgb), 0.2);
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
    grid-gap: 10px;
    & .label {
      font-size: 14px;
      color: var(--brand-color-1);
      font-weight: 500;
    }
    & .value {
      margin-left: 6px;
      font-size: 16px;
      color: var(--text-color);
    }
  }
}
  </style>
