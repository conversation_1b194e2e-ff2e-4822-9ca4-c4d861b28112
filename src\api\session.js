import Config from '../config.js'
import jwtDecode from 'jwt-decode'
import { alert } from '../plugins/notification/index.js'

export const login = async (email, password) => {
  try {
    const rawResponse = await fetch(Config.serverEndpoint + '/user/login', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email, password })
    })
    const response = await rawResponse.json()
    if (!rawResponse.ok) {
      return {
        loggedIn: false,
        message: response.message
      }
    } else {
      localStorage.setItem(Config.localstorageKeys.AUTH, response.message)
      return {
        loggedIn: true,
        message: 'User logged in successfully'
      }
    }
  } catch (error) {
    return {
      loggedIn: false,
      message: error.message
    }
  }
}

export const SignupUser = (password, token) => {
  return new Promise((resolve, reject) => {
    fetch(Config.serverEndpoint + '/user/signup', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + token
      },
      body: JSON.stringify({ password: password })
    }).then(e => e.json())
      .then(resolve)
      .catch(reject)
  })
}

const getTenantToken = async ({ dtxTenantToken, payload }) => {
  const rawResponse = await fetch(Config.serverEndpoint + '/user/tokenExchange', {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      Authorization: 'Bearer ' + dtxTenantToken,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(payload)
  })
  const response = await rawResponse.json()
  return { rawResponse, response }
}

const checkIfCollabTenant = async (targetTenantId) => {
  const dtxAuthToken = localStorage.getItem(Config.localstorageKeys.AUTH)
  const payload = { tenantId: targetTenantId, collaborator: false }
  const { response, rawResponse } = await getTenantToken({ payload, dtxTenantToken: dtxAuthToken })
  if (rawResponse.status !== 200) {
    localStorage.removeItem(Config.localstorageKeys.REDIRECT_FROM)
    localStorage.removeItem(Config.localstorageKeys.TARGET_TENANT_ID)
    localStorage.removeItem(Config.localstorageKeys.REDIRECT_FROM)
    return
  }
  const dtxTokenObject = jwtDecode(response.message)
  const sourceTenantId = localStorage.getItem(Config.localstorageKeys.LAST_OPENED_TENANT)
  localStorage.clear()
  localStorage.setItem(Config.localstorageKeys.AUTH, dtxAuthToken)
  localStorage.setItem(Config.localstorageKeys.LAST_OPENED_TENANT, dtxTokenObject['x-hasura-tenant-id'])
  localStorage.setItem(Config.localstorageKeys.TENANT, response.message)
  localStorage.setItem(Config.localstorageKeys.COLLABORATOR, true)
  localStorage.setItem(Config.localstorageKeys.COLLABORATOR_ID, sourceTenantId)
}

export const tenantExchangeToken = async (cb) => {
  try {
    const targetTenantId = localStorage.getItem(Config.localstorageKeys.TARGET_TENANT_ID)
    if (targetTenantId) {
      await checkIfCollabTenant(targetTenantId)
    }
    const localTenantId = localStorage.getItem(Config.localstorageKeys.LAST_OPENED_TENANT)
    const dtxtoken = localStorage.getItem(Config.localstorageKeys.AUTH)
    const dtxTenantToken = localStorage.getItem(Config.localstorageKeys.TENANT) ?? dtxtoken
    const dtxTokenObject = jwtDecode(dtxtoken)
    const collaborator = JSON.parse(localStorage.getItem(Config.localstorageKeys.COLLABORATOR))
    const tenantId = localTenantId || dtxTokenObject['x-hasura-tenant-id']
    const payload = { tenantId: tenantId, collaborator }
    if (collaborator) {
      payload.sourceTenantId = localStorage.getItem(Config.localstorageKeys.COLLABORATOR_ID)
    }
    const { response, rawResponse } = await getTenantToken({ payload, dtxTenantToken })
    if (rawResponse.status !== 200) {
      if (response.message === 'Token expired') {
        alert('Session expired')
      } else {
        alert(response.message)
      }
      setTimeout(() => {
        logout()
      }, 1000)
      return
    }
    localStorage.setItem(Config.localstorageKeys.TENANT, response.message)
    localStorage.setItem(Config.localstorageKeys.LAST_OPENED_TENANT, tenantId)
    cb && cb(tenantId)
  } catch (error) {
    window.location.href = '/login'
  }
}

export const getAllProjectsList = async (cb) => {
  try {
    const dtxtoken = localStorage.getItem(Config.localstorageKeys.TENANT)
    const rawResponse = await fetch(Config.graphQLEndpoint, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + dtxtoken
      },
      body: JSON.stringify({ operationName: 'GetAllProjects', variables: {}, query: 'query GetAllProjects {core_projects(where: {deleted: {_eq: false}}) {id}}' })
    })
    const response = await rawResponse.json()
    const projectIds = response?.data.core_projects?.map(project => project.id + '') || []
    cb && cb(projectIds)
  } catch (error) {
    window.location.href = '/login'
  }
}

export const projectExchangeToken = async (cb, projectIdd = null) => {
  try {
    const dtxTenantToken = localStorage.getItem(Config.localstorageKeys.TENANT)
    let projectId = projectIdd ?? (localStorage.getItem(Config.localstorageKeys.LAST_OPENED_PROJECT) || 0)
    // need 0 should be in Number type for master level
    if (projectId === '0') { projectId = +projectId }
    if (projectId === 0) {
      cb && cb(projectId)
      return
    }
    const rawResponse = await fetch(Config.serverEndpoint + '/user/tokenExchange', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + dtxTenantToken
      },
      body: JSON.stringify({ projectId: projectId })
    })
    const projectExchangeResponce = await rawResponse.json()

    if (projectExchangeResponce.message === 'Invalid project') {
      projectId = 0
      localStorage.setItem(Config.localstorageKeys.LAST_OPENED_PROJECT, projectId)
    }
    const projectToken = projectExchangeResponce.message === 'Invalid project'
      ? null : projectExchangeResponce.message
    localStorage.setItem(Config.localstorageKeys.PROJECT, projectToken)

    cb && cb(projectId)
  } catch (error) {
    window.location.href = '/login'
  }
}

export const logout = () => {
  if (localStorage.getItem(Config.localstorageKeys.REDIRECT_FROM) === 'email') {
    localStorage.removeItem(Config.localstorageKeys.AUTH)
    localStorage.removeItem(Config.localstorageKeys.TENANT)
    localStorage.removeItem(Config.localstorageKeys.PROJECT)
    localStorage.removeItem(Config.localstorageKeys.TENANT_TYPE)
  } else {
    localStorage.removeItem(Config.localstorageKeys.AUTH)
    localStorage.removeItem(Config.localstorageKeys.TENANT)
    localStorage.removeItem(Config.localstorageKeys.PROJECT)
    localStorage.removeItem(Config.localstorageKeys.LAST_OPENED_TENANT)
    localStorage.removeItem(Config.localstorageKeys.LAST_OPENED_PROJECT)
    localStorage.removeItem(Config.localstorageKeys.COLLABORATOR)
    localStorage.removeItem(Config.localstorageKeys.COLLABORATOR_ID)
    localStorage.removeItem(Config.localstorageKeys.TENANT_TYPE)
    localStorage.removeItem(Config.localstorageKeys.REDIRECT_FROM)
    localStorage.removeItem('lastPath')
  }
  window.location.href = '/login'
}

export const tokenAction = (cb) => {
  const dtxtoken = localStorage.getItem(Config.localstorageKeys.AUTH)

  if (!dtxtoken) {
    logout()
  }
  const dtxTokenObject = jwtDecode(dtxtoken)
  if (dtxTokenObject['beacon-admin']) {
    cb && cb(dtxTokenObject['x-hasura-tenant-id'], dtxTokenObject['x-hasura-user-id'], null, dtxTokenObject['beacon-admin'])
    return
  }

  tenantExchangeToken((tenantId) => {
    projectExchangeToken((projectId) => (cb && cb(
      tenantId,
      dtxTokenObject['x-hasura-user-id'],
      projectId,
      dtxTokenObject['beacon-admin'])))
  })
}
export const updatePassword = async (currentPassword, newPassword) => {
  const dtxtoken = localStorage.getItem(Config.localstorageKeys.AUTH)
  try {
    const rawResponse = await fetch(Config.serverEndpoint + '/user/updatePassword', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + dtxtoken

      },
      body: JSON.stringify({ currentPassword, newPassword })
    })
    const response = await rawResponse.json()
    if (!rawResponse.ok) {
      return {
        update: false,
        message: response.message
      }
    } else {
      localStorage.setItem(Config.localstorageKeys.AUTH, response.message)
      return {
        update: true,
        message: 'Password Updated Successfully'
      }
    }
  } catch (error) {
    return {
      loggedIn: false,
      message: error.message
    }
  }
}
