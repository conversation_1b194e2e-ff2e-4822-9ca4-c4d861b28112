<template>
  <div v-if="loading" class="fh fw center">
    <loadingCircle/>
  </div>
  <div v-else class="part-id-generation">
    <div class="new-component-box">
      <div class="flex">
        <img
          class="mr-3"
          src="~@/assets/images/icons/arrow-back.svg"
          width="30px"
          alt=""
          @click="goBack"
        />
    <div class="xxxl weight-500">Sequence Generation</div>
      </div>
    </div>
    <div class="new-component-header">
    <div class="flex gap-1" style="gap: 10px; align-items: center;">
      <div v-if="!viewMode" class="create-header flex gap-1 center">
        <label style="font-weight: 500;">Template Name:</label>
        <input
        v-if="!viewMode"
          type="text"
          class="sort"
          placeholder="Template Name"
          v-model="templateName"
        />
            <label style="font-weight: 500;" for="">Link To:</label>
<select
 v-if="!viewMode"
  class="sort"
  v-model="selectedFeature"
>
  <option disabled value='null'>Select Feature</option>
  <option
    v-for="(value, key) in filteredCoreFeatures"
    :key="value"
    :value="value"
  >
    {{ key }}
  </option>
</select>
      </div>
      <div v-else class="header-view center flex gap-1">
    <label style="font-weight: 500;">Template Name:</label>
    <div class="status status-inprogress">
      {{ templateName || '--' }}
    </div>
     <label style="font-weight: 500;">Linked To:</label>
    <div class="status status-inprogress">
      {{ linkedTo }}
    </div>
  </div>
  </div>
      <button v-if="!viewMode" class="btn" @click="saveComponent">+ Save</button>
      </div>
          <!-- Generated ID Preview -->
    <div class="generated-id-preview">
      <span style="font-size: 18px;" class=" weight-500">Generated ID : </span>{{ generatedId }}
    </div>
    <!-- Data Type Dropdown Below Table -->
    <div v-if="!viewMode" class="type-selector">
      <select :disabled="viewMode" class="sort" v-model="selectedType" @change="addComponent">
        <option disabled value="">+ Select Data Type</option>
        <option v-for="type in dataTypes" :key="type.id" :value="type.caption">
          {{ type.caption }}
        </option>
      </select>
    </div>
    <!-- Component Cards -->
    <div class="component-cards">
      <div
        class="component-card"
        v-for="(component, index) in [...components].reverse()"
        :key="component.id"
      >
        <!-- Header Row -->
        <div v-if="component.type === 'Auto Sequence'" class="component-row component-header">
          <div v-if="!viewMode" class="col delete-col"></div>
          <div class="col">Data Type</div>
          <div class="col">Padding</div>
          <div class="col">Last Counter Value</div>
          <!-- <div class="col">Upper Limit</div> -->
          <div v-if="!viewMode" class="col col-small">Reset Counter</div>
          <div v-if="!viewMode && component.isChecked" class="col">Select Field</div>
          <div v-else class="col">Select Field</div>
          <div class="col">Delimiter</div>
          <!-- <div class="col" >Order</div> -->
        </div>
        <div v-else class="component-row component-header">
          <div v-if="!viewMode" class="col delete-col"></div>
          <div class="col">Data Type</div>
          <div class="col">Value</div>
          <div v-if="component.type === 'Tags'" class="col">Tag Seperator</div>
          <div class="col">Delimiter</div>
          <!-- <div class="col">Order</div> -->
        </div>

        <!-- Data Row -->
         <div v-if="component.type === 'Auto Sequence'" class="component-row">
          <div v-if="!viewMode" class="col delete-col">
            <img
            v-if="(component?.isDelete && !viewMode)"
              src="~@/assets/images/trash-2.svg"
              alt="Delete"
              class="delete-icon"
              @click="removeComponent(getOriginalIndex(index))"
            />
            <div v-else-if="viewMode" class="img"></div>
          </div>
          <div class="col">{{ component.type }}</div>
          <div class="col">
                  <input
                  min="0"
                  :disabled="viewMode"
                  type="number"
                  class="component-input"
                  placeholder="Padding"
                  v-model="component.padding_length"
                  @input="validateAutoSequence(component)"
                />
          </div>
          <div class="col">
                <input
                min="0"
                :disabled="viewMode"
                  type="number"
                  class="component-input"
                  placeholder="Last counter value"
                  v-model="component.lower"
                  @input="validateAutoSequence(component)"
                />
           </div>
           <div v-if="!viewMode" class="col">
            <input
            type="checkbox"
            v-model="component.isChecked"
            @change="updateGeneratedId"
            />
          </div>
          <div v-if="!viewMode && component.isChecked" class="col">
            <!-- View Mode Display -->
            <div>
              <select
                v-model="component.select_field"
                class="component-input"
                @change="handleResetDependencyChange(component)"
              >
                <option disabled value="">Select Reset Type</option>
                <option
                  v-for="option in resetDependencyOptions"
                  :key="option.id"
                  :value="option.sequence_order"
                >
                  {{ option.name }}
                </option>
              </select>
            </div>
          </div>
          <div v-else class="col">
          <div>
            <input
              type="text"
              :value="resetDependencyOptions.find(opt => opt.sequence_order === component.select_field)?.name || 'No Reset Dependency'"
              disabled
              class="component-input bg-gray-50 border border-gray-300 rounded p-2 text-sm cursor-not-allowed w-full"
            />
          </div>
          </div>
          <!-- Check here -->
          <div class="col">
            <!-- View Mode: Show Delimiter in Readonly Input -->
            <input
              v-if="viewMode"
              class="component-input"
              :value="component.delimiter || 'Not Selected'"
              readonly
            />

            <!-- Edit Mode: Delimiter Dropdown -->
            <select
              v-else
              class="delimiter-dropdown"
              v-model="component.delimiter"
              @change="updateGeneratedId"
            >
              <option value="">Not Selected</option>
              <option value="-">-</option>
              <option value=".">.</option>
              <option value="_">_</option>
            </select>
          </div>
          <!-- <div v-if="!viewMode" class="col move-col">
            <img
            src="~@/assets/images/icons/arrow-up-icon.svg"
            class="move-icon"
            @click="moveComponentUp(getOriginalIndex(index))"
            />
            <img
            class="move-icon"
            src="~@/assets/images/icons/arrow-down-icon.svg"
            @click="moveComponentDown(getOriginalIndex(index))"
          />
          </div> -->
         </div>
        <div v-else class="component-row">
          <div v-if="!viewMode" class="col delete-col">
            <img
            v-if="!viewMode"
              src="~@/assets/images/trash-2.svg"
              alt="Delete"
              class="delete-icon"
              @click="removeComponent(getOriginalIndex(index))"
            />
          </div>

          <div class="col">{{ component.type }}</div>

          <div class="col">
<!-- <select :disabled="viewMode" v-if="component.type.toLowerCase() === 'tags'" v-model="component.value">
  <option v-for="option in component.options" :key="option" :value="tagTypeMap[option]">
    {{ option }}1
  </option>
</select> -->

            <template v-if="component.type.toLowerCase() === 'auto sequence'">
              <div class="auto-sequence">
                <input
                :disabled="viewMode"
                  type="number"
                  class="component-input"
                  placeholder="Last counter value"
                  v-model="component.lower"
                  @input="validateAutoSequence(component)"
                />
                <input
                :disabled="viewMode"
                  type="number"
                  class="component-input"
                  placeholder="Upper Limit"
                  v-model="component.upper"
                  @input="validateAutoSequence(component)"
                />
              </div>
              <div>
              </div>
            </template>
<!-- View Mode -->
<input
  v-if="viewMode && component.options && component.options.length"
  class="component-input"
  :value="getOptionLabel(component)"
  readonly
/>

<!-- Edit Mode -->
<select
  v-else-if="component.options && component.options.length"
  v-model="component.value"
  class="component-input"
  @change="onComponentValueChange(component)"
>
  <option disabled value="">Select {{ component.type }}</option>
  <option
    v-for="option in component.options"
    :key="option.value"
    :value="option.value"
  >
    {{ option.name }}
  </option>
</select>
            <!-- User Initials Field (Disabled Input) -->
            <input
              v-else-if="component.type.toLowerCase() === 'user initials'"
              class="component-input"
              :value="userInitials"
              disabled
            />

            <input
              v-else-if="component.is_dynamic"
              class="component-input"
              :value="getDynamicDisplayLabel(component.component_type)"
              disabled
            />

            <input
            :disabled="viewMode"
              v-else
              class="component-input"
              type="text"
              v-model="component.value"
              :placeholder="`Enter ${component.type}`"
              @input="validateInput(component)"
            />
          </div>
          <div v-if="component.type === 'Tags' && !viewMode" class="col">
            <select
            :disabled="viewMode"
              class="delimiter-dropdown tag-separator"
              v-model="component.suffix"
              @change="updateGeneratedId"
            >
              <option value="">Not Selected</option>
              <option value="-">-</option>
              <option value=".">.</option>
              <option value="_">_</option>
            </select>
          </div>
          <!-- Tag Separator Display for View Mode -->
          <div
          v-else-if="viewMode && component.type === 'Tags'"
          class="col">
          <input
            class="component-input"
            :value="component.suffix || 'Not Selected'"
            readonly
          />
        </div>
          <!-- <div v-if="!viewMode" class="col move-col">
            <img
            src="~@/assets/images/icons/arrow-up-icon.svg"
            class="move-icon"
            @click="moveComponentUp(getOriginalIndex(index))"
            />
            <img
            class="move-icon"
            src="~@/assets/images/icons/arrow-down-icon.svg"
            @click="moveComponentDown(getOriginalIndex(index))"
          />
          </div> -->
<div class="col">
  <!-- View Mode: Show Delimiter in Readonly Input -->
  <input
    v-if="viewMode"
    class="component-input"
    :value="component.delimiter || 'Not Selected'"
    readonly
  />

  <!-- Edit Mode: Delimiter Dropdown -->
  <select
    v-else
    class="delimiter-dropdown"
    v-model="component.delimiter"
    @change="updateGeneratedId"
  >
    <option value="">Not Selected</option>
    <option value="-">-</option>
    <option value=".">.</option>
    <option value="_">_</option>
  </select>
</div>

        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import config from '@/config'
import loadingCircle from '@/components/common/loadingCircle.vue'
import { alert, success } from '@/plugins/notification'
import { getPartIdDataTypes, GetActiveCustomLists, saveSequenceTemplateGeneration, getTemplateById } from '@/api'

// function generateUUID () {
//   return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
//     const r = Math.random() * 16 | 0
//     const v = c === 'x' ? r : (r & 0x3 | 0x8)
//     return v.toString(16)
//   })
// }
const AUTO_SEQUENCE_ID = 3
export default {
  name: 'PartIdGeneration',
  components: {
    loadingCircle
  },
  data () {
    return {
      linkedTo: '',
      loading: false,
      viewMode: false,
      recordedId: null,
      templateName: '',
      selectedType: '',
      components: [],
      dataTypes: [],
      generatedId: '',
      customLists: [],
      tagTypeMap: config.TAG_TYPE_MAP,
      coreFeatures: config.CORE_FEATURES,
      selectedFeature: null
    }
  },
  computed: {
    ...mapGetters(['user']),
    userInitials () {
      const firstInitial = this.user?.first_name?.charAt(0) || ''
      const lastInitial = this.user?.last_name?.charAt(0) || ''
      return (firstInitial + lastInitial).toUpperCase()
    },
    resetDependencyOptions () {
      return this.components
        .filter(c =>
          ['Date', 'Month', 'Year', 'Config List', 'Tags'].includes(c.type)
        )
        .map((c, index) => ({
          id: c.id,
          name: c.type,
          sequence_order: this.components.indexOf(c) + 1
        }))
    },
    filteredCoreFeatures () {
      return Object.fromEntries(
        Object.entries(this.coreFeatures)
          .filter(([key]) =>
            ['MATERIAL', 'PRODUCT_CODE', 'FORMS'].includes(key)
          )
          .map(([key, value]) => {
            const formattedKey = key
              .toLowerCase()
              .split('_')
              .map(word => word.charAt(0).toUpperCase() + word.slice(1))
              .join(' ')
            return [formattedKey, value]
          })
      )
    }
  },
  mounted () {
    this.loading = true
    const path = this.$route.path
    this.viewMode = path.includes('/view')
    this.recordedId = this.viewMode ? this.$route.params.id || null : null
    this.selectedFeature = this.coreFeatures.MATERIAL
    Promise.all([this.getCustomList(), this.fetchPartIdDataTypes()])
      .then(() => {
        if (this.viewMode && this.recordedId) {
          this.fetchTemplateById()
        }
      })
      .finally(() => {
        this.loading = false
      })
  },
  methods: {
    handleResetDependencyChange (component) {
      const selectedOption = this.resetDependencyOptions.find(
        opt => opt.sequence_order === component.select_field
      )

      if (!selectedOption) return

      // Example handling:
      if (selectedOption.name === 'Config List') {
        component.reset_on_custom_list_field_id = selectedOption.sequence_order
      } else if (selectedOption.name === 'Month') {
        component.reset_on_month_field_id = selectedOption.sequence_order
      } else if (selectedOption.name === 'Year') {
        component.reset_on_year_field_id = selectedOption.sequence_order
      } else if (selectedOption.name === 'Tags') {
        component.reset_on_tag_field_id = selectedOption.sequence_order
      }

      // Call your ID update logic if needed
      this.updateGeneratedId()
    },
    moveComponentUp (index) {
      if (index > 0) {
        const temp = this.components[index]
        this.$set(this.components, index, this.components[index - 1])
        this.$set(this.components, index - 1, temp)
      }
    },
    moveComponentDown (index) {
      if (index < this.components.length - 1) {
        const temp = this.components[index]
        this.$set(this.components, index, this.components[index + 1])
        this.$set(this.components, index + 1, temp)
      }
    },
    getDynamicDisplayLabel (type) {
      switch (type.toUpperCase()) {
      case 'DATE': return 'DD'
      case 'MONTH': return 'MM'
      case 'YEAR': return 'YYYY'
      case 'UUID': return 'XXXX'
      case 'USER_INITALS': return this.userInitials
      default: return ''
      }
    },
    getOptionLabel (component) {
    // For Tags
      if (component.component_type?.toLowerCase() === 'tags') {
        return Object.keys(this.tagTypeMap).find(
          key => this.tagTypeMap[key] === component.value
        ) || component.value
      }

      // For Config List
      if (component.component_type?.toLowerCase() === 'config list') {
        const list = this.customLists.find(item => item.id === component.value)
        return list?.name || component.value
      }

      return component.value
    },
    onComponentValueChange (component) {
      const type = component.type.toLowerCase()
      if (type === 'tags') {
        component.tag_type_id = component.value
      } else if (type === 'config list') {
        component.custom_list_id = component.value
      }
      this.updateGeneratedId()
    },
    goBack () {
      this.$router.go(-1)
    },
    getOptionsForType (type) {
      const typeLower = type?.toLowerCase()
      if (typeLower === 'tags') {
        return Object.keys(this.tagTypeMap).map(key => ({
          id: this.tagTypeMap[key],
          name: key
        }))
      } else if (typeLower === 'config list') {
        return this.customLists.map(item => ({
          id: item.id,
          name: item.name
        }))
      }
      return []
    },
    fetchTemplateById () {
      this.loading = true
      getTemplateById(this.recordedId)
        .then(res => {
          const rules = res.core_sequence_id_template_by_pk.id_generation_rules
          const linkedTo = res.core_sequence_id_template_by_pk.feature_id
          this.linkedTo = Object.keys(this.coreFeatures).find(key => this.coreFeatures[key] === linkedTo)
          this.templateName = res.core_sequence_id_template_by_pk.name

          this.components = rules.map((rule, index) => {
            const typeInfo = this.dataTypes.find(dt => dt.id === rule.component_id)
            const typeCaption = typeInfo?.caption || 'Unknown'
            const captionLower = typeCaption.toLowerCase()

            let value = rule.default_value ?? ''
            let options = []
            let originalValue = null

            if (captionLower === 'tags') {
              options = Object.entries(this.tagTypeMap).map(([name, val]) => ({ name, value: val }))
              const tagName = Object.keys(this.tagTypeMap).find(name => this.tagTypeMap[name] === rule.tag_type_id)
              if (tagName) {
                value = tagName
                originalValue = rule.tag_type_id
              }
            } else if (captionLower === 'config list') {
              options = this.customLists.map(item => ({ name: item.name, value: item.id }))
              const list = this.customLists.find(item => item.id === rule.custom_list_id)
              if (list) {
                value = list.name
                originalValue = rule.custom_list_id
              }
            }

            // Determine the reset dependency field from the GraphQL response
            let selectField = ''
            if (rule.custom_list_reset_governed_by?.sequence_order) {
              selectField = rule.custom_list_reset_governed_by.sequence_order
            } else if (rule.month_reset_governed_by?.sequence_order) {
              selectField = rule.month_reset_governed_by.sequence_order
            } else if (rule.tag_reset_governed_by?.sequence_order) {
              selectField = rule.tag_reset_governed_by.sequence_order
            } else if (rule.year_reset_governed_by?.sequence_order) {
              selectField = rule.year_reset_governed_by.sequence_order
            }

            return {
              id: rule.component_id,
              type: typeCaption,
              component_type: typeCaption,
              is_dynamic: typeInfo?.is_dynamic || false,
              value,
              originalValue,
              delimiter: rule.delimiter_after || '',
              options,
              lower: captionLower === 'auto sequence' ? rule.start_value : undefined,
              upper: captionLower === 'auto sequence' ? rule.start_value + (rule.auto_increment || 1) - 1 : undefined,
              suffix: captionLower === 'tags' ? rule.suffix || '' : undefined,
              padding_length: captionLower === 'auto sequence' ? rule.padding_length : undefined,
              isDelete: !(index === 0 && captionLower === 'auto sequence'),
              select_field: selectField
            }
          })

          this.components = [...this.components]
          this.updateGeneratedId()
        })
        .catch(err => {
          console.error('Error fetching template:', err)
        })
        .finally(() => {
          this.loading = false
        })
    },

    getCustomList () {
      return GetActiveCustomLists().then(res => {
        this.customLists = res.core_custom_list
      })
    },
    fetchPartIdDataTypes () {
      return getPartIdDataTypes().then(res => {
        this.dataTypes = res.id_generation_components.map(item => ({
          caption: item.caption,
          component_type: item.component_type,
          is_dynamic: item.is_dynamic,
          id: item.id
        }))
        if (!this.viewMode) {
          const autoSeqData = this.dataTypes.find(item => item.caption === 'Auto Sequence')
          if (autoSeqData) {
            this.components.push({
              id: autoSeqData.id,
              type: autoSeqData.caption,
              component_type: autoSeqData.component_type,
              is_dynamic: autoSeqData.is_dynamic,
              value: '',
              start_value: '',
              padding_length: '1',
              delimiter: '',
              lower: '1',
              upper: '',
              isDelete: false
            })
          }
          this.autoSequeneceDeleteCheck()
          this.updateGeneratedId()
        }
      }).catch(err => {
        console.error('Error fetching part ID data types:', err)
      })
    },
    validateInput (component) {
      component.value = component.value.toUpperCase()
      const type = component.type.toLowerCase()
      if (type === 'text' && /[^a-zA-Z]/.test(component.value)) {
        alert('Only alphabetic characters allowed for Text type.')
        component.value = ''
      }
      if (type === 'number' && /[^0-9]/.test(component.value)) {
        alert('Only numbers allowed for Number type.')
        component.value = ''
      }
      this.updateGeneratedId()
    },
    validateAutoSequence (component) {
      if (/[^0-9]/.test(component.lower)) {
        alert('Lower Limit must be numeric.')
        component.lower = ''
      }
      if (/[^0-9]/.test(component.upper)) {
        alert('Upper Limit must be numeric.')
        component.upper = ''
      }
      if (/[^0-9]/.test(component.value)) {
        alert('Start value must be numeric.')
        component.value = ''
      }
      this.updateGeneratedId()
    },
    addComponent () {
      if (!this.selectedType) return
      const last = this.components[this.components.length - 1]
      if (last) {
        if (!last.is_dynamic && !last.value) {
          alert('Please fill in the value of the last added component before proceeding.')
          this.selectedType = ''
          return
        }
      }
      console.log(this.dataTypes, 'this is the data types')
      const selectedData = this.dataTypes.find(d => d.caption === this.selectedType)
      const typeLower = selectedData.caption.toLowerCase()

      let dummyOptions = []
      if (typeLower === 'tags') {
        dummyOptions = Object.entries(this.tagTypeMap).map(([name, value]) => ({ name, value }))
      } else if (typeLower === 'config list') {
        dummyOptions = this.customLists.map(item => ({ name: item.name, value: item.id }))
      }

      let value = ''
      if (typeLower === 'user initials') {
        value = this.userInitials
      } else if (selectedData.is_dynamic) {
        value = this.getDynamicValue(selectedData.component_type)
      }

      this.components.push({
        id: selectedData.id,
        type: selectedData.caption,
        component_type: selectedData.component_type,
        is_dynamic: selectedData.is_dynamic,
        value,
        delimiter: '',
        options: dummyOptions,
        lower: typeLower === 'auto sequence' ? '' : undefined,
        suffix: typeLower === 'tags' ? '' : undefined,
        upper: typeLower === 'auto sequence' ? '' : undefined,
        padding_length: typeLower === 'auto sequence' ? 0 : undefined
      })
      this.autoSequeneceDeleteCheck()
      this.selectedType = ''
      this.updateGeneratedId()
    },
    removeComponent (index) {
      this.components.splice(index, 1)
      this.autoSequeneceDeleteCheck()
      this.updateGeneratedId()
    },
    getDynamicValue (type) {
      const now = new Date()
      switch (type.toUpperCase()) {
      case 'DATE': return String(now.getDate()).padStart(2, '0')
      case 'YEAR': return String(now.getFullYear())
      case 'MONTH': return String(now.getMonth() + 1).padStart(2, '0')
      case 'USER_INITALS': return this.userInitials
      default: return ''
      }
    },
    updateGeneratedId () {
      const parts = this.components.map(c => {
        let val = ''
        if (c.type.toLowerCase() === 'auto sequence') {
          const raw = String(c.lower || '')
          val = c.padding_length
            ? '0'.repeat(c.padding_length) + raw
            : raw
        } else if (c.type.toLowerCase() === 'user initials') {
          val = this.userInitials
        } else if (['config list', 'tags'].includes(c.type.toLowerCase())) {
          val = 'XXXX'
        } else {
          val = c.is_dynamic ? this.getDynamicDisplayLabel(c.component_type) : (c.value || '')
        }
        return c.delimiter ? val + c.delimiter : val
      })
      this.generatedId = parts.join('').toUpperCase()
    },
    getOriginalIndex (reversedIndex) {
      return this.components.length - 1 - reversedIndex
    },
    saveComponent () {
      const last = this.components[this.components.length - 1]
      if (last?.delimiter) {
        return alert('The last component should not have a delimiter.')
      }
      if (!this.selectedFeature) {
        return alert('Feature is required')
      }

      const isInvalid = this.components.some(component => {
        if (component?.type === 'Tags' && !component?.tag_type_id) {
          return alert('Tag type is mandatory!')
        }
        if (component.type === 'Tags' && !component.suffix) {
          alert('Tag separator is required')
          return true
        }
        if (component?.type === 'Config List' && !component?.custom_list_id) {
          alert('Config List value is mandatory!')
          return true
        }
        return false
      })
      if (isInvalid) return
      if (!this.templateName) {
        return alert('Template name is required')
      }
      const templateInput = {
        name: this.templateName,
        feature_id: this.selectedFeature
      }

      const rules = this.components.map((c, index) => {
        const base = {
          component_id: c.id,
          sequence_order: index + 1,
          validation_type: null,
          validation_value: null,
          component_name: c.type,
          delimiter_after: c.delimiter || null
        }

        switch (c.type) {
        case 'Text':
          return { ...base, default_value: c.value }

        case 'Number':
          return { ...base, default_value: Number(c.value) }

        case 'Auto Sequence': {
          const autoSequenceRule = {
            ...base,
            start_value: Number(c.lower),
            padding_length: Number(c?.padding_length)
          }

          // Updated: Save dependency as sequence_order, not id
          if (c.select_field) {
            const targetDependency = this.components.find((comp, idx) => (idx + 1) === Number(c.select_field))
            if (targetDependency) {
              const dependencySequenceOrder = this.components.findIndex(comp => comp.id === targetDependency.id) + 1
              console.log(targetDependency, 'target dependency', dependencySequenceOrder, 'dependencySequenceOrder')
              if (targetDependency.type === 'Month') {
                autoSequenceRule.reset_on_month_field_id = dependencySequenceOrder
              } else if (targetDependency.type === 'Config List') {
                autoSequenceRule.reset_on_custom_list_field_id = dependencySequenceOrder
              } else if (targetDependency.type === 'Year') {
                autoSequenceRule.reset_on_year_field_id = dependencySequenceOrder
              } else if (targetDependency.type === 'Tags') {
                autoSequenceRule.reset_on_tags_field_id = dependencySequenceOrder
              }
            }
          }
          return autoSequenceRule
        }
        case 'Config List':
          return { ...base, custom_list_id: c.value }
        case 'Tags':
          return { ...base, tag_type_id: c.value, suffix: c.suffix }
        default:
          return base
        }
      })
      saveSequenceTemplateGeneration(templateInput, rules)
        .then(res => {
          if (res) success(res.create_sequence_template.message)
          this.$router.push('/settings/sequence-generator')
        })
        .catch(err => {
          alert('Error saving template!/')
          console.log(err)
          console.error('Error saving template:', err)
        })
    },
    autoSequeneceDeleteCheck () {
      // finding whether the  auto-sequence has to be eneabled with  delete  option or not -- start ---
      const autoSequnceIndexes = []
      this.components.forEach((element, index) => {
        if (element.id === AUTO_SEQUENCE_ID) {
          autoSequnceIndexes.push(index)
        }
      })
      if (autoSequnceIndexes.length > 1) {
        for (const index of autoSequnceIndexes) {
          this.components[index].isDelete = true
        }
      } else if (autoSequnceIndexes.length === 1) {
        const index = autoSequnceIndexes[0]
        this.components[index].isDelete = false
      }
      // finding whether the  auto-sequence has to be eneabled with  delete  option or not -- end ---
    }
  }
}
</script>

<style lang="scss" scoped>
.part-id-generation {
  padding: 8px;
}

.new-component-box {
  border-bottom: 2px solid #ddd;
  padding-block: 10px;
}

.new-component-header {
  padding-top: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.generated-id-preview {
  margin-top: 20px;
  font-size: 16px;
  font-weight: 600;
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 6px;
}

.component-cards {
  margin-top: 20px;
}

.component-card {
  display: table;
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #ccc;
  table-layout: fixed;
}

.component-row {
  display: table-row;
}

.component-header {
  font-weight: 500;
  background-color: #f3f3f3;
}

.col {
  display: table-cell;
  padding: 8px 8px 8px 8px;
  border: 1px solid #ccc;
  vertical-align: middle;
  word-wrap: break-word;
  overflow-wrap: break-word;
  overflow: hidden;
}
.col-small {
  display: table-cell;
  padding: 4px 6px;
  width: 11%;
  text-align: center;
  border: 1px solid #ccc;
  vertical-align: middle;
}
.auto-sequence-col {
  width: auto; /* Let it grow if needed */
}

.delete-col {
  width: 40px;
  text-align: center;
}

.component-input,
.delimiter-dropdown {
  width: 100%; /* Fill the cell but not exceed it */
  box-sizing: border-box;
  font-size: 14px;
  padding: 4px 6px;
  border: 1px solid var(--brand-color);
  border-radius: 4px;
  background-color: var(--brand-light-color);
}
.auto-padding {
  padding: 4px 6px;
  border: 1px solid var(--brand-color);
  border-radius: 4px;
  background-color: var(--brand-light-color);
  margin-bottom: 4px;
}

.delete-icon {
  cursor: pointer;
  width: 16px;
  height: 16px;
}

.type-selector {
  margin-top: 20px;
}

.sort {
  height: 2em;
  width: 11.7rem;
  font-size: 14px;
  background-color: var(--brand-light-color);
  line-height: 1;
  border: 1px solid var(--brand-color);
  border-radius: 4px;
  padding: 4px 10px;
}
  .status {
    font-size: 13px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
    height: fit-content;
    padding: 3px 4px;
    border-radius: 3px;
    background-color: orange;
    &-inprogress {
        background-color: #fff3cd; /* Light yellow */
        color: #856404; /* Dark yellow */
    }
  }
  .move-icon {
    width: 20px;
    height: 20px;
  }

.auto-sequence {
  display: flex;
  gap: 5px;
}
</style>
