<template>
  <div class="gantt-edit-form">
    <form>
      <div class="grid-2 mt-2">
        <div class="input-group mt-2">
          <label class="key">Name:</label>
          <input autofocus type="text" v-model="taskData.name" :disabled="task.$level === 0 || collaborator"/>
        </div>
        <div class="input-group mt-2">
          <label class="key">Description:</label>
          <input type="text" v-model="taskData.description" :disabled = "collaborator"/>
        </div>
      </div>
      <div class="grid-2 mt-2">
        <div class="input-group mt-2">
          <label class="key">Duration (hours):</label>
          <input type="number" v-model="taskData.duration" @paste="onPaste" min='0' :disabled="collaborator || task?.hasChild || disableValue" @input="calculateEndDate(taskData.duration, taskData.start_date)" @keydown="changeNumber"/>
        </div>
        <div class="input-group mt-2">
          <label class="key">Progress:</label>
          <input type="number" v-model.number="taskData.progress" @paste="onPaste" min='0' max='100' :disabled="task?.hasChild || disableValue" @input="validateProgress" @keydown="changeNumber"/>
        </div>
      </div>
      <div class="grid-2 mt-2">
        <div class="input-group mt-2">
          <label class="key">Start Date:</label>
          <input type="date" v-model="taskData.start_date" :disabled="task?.hasChild || collaborator" @change=" calculateEndDate(taskData.duration, taskData.start_date)" />
        </div>
        <div class="input-group mt-2">
          <label class="key">End Date:</label>
          <input type="date" v-model="taskData.end_date" disabled/>
        </div>
      </div>
      <div class="grid-2 mt-2">
        <div class="input-group mt-2">
          <label class="key">Type</label>
          <select v-model="taskData.type" :disabled="taskData.type === 2 || task?.hasChild || collaborator" @change="onChangeType($event)">
            <option value="1">Task</option>
            <option value="2" v-if="taskData.type === 2">Project</option>
            <option value="3">Milestone</option>
          </select>
        </div>
        <div class="input-group mt-2">
          <label class="key">Cost:</label>
          <input type="number" v-model.number="taskData.cost" disabled="true" @keydown="changeNumber"/>
        </div>
      </div>
     <div class="grid-2 mt-2">
       <div class="input-group mt-2">
           <label class="key">AttachBom:</label>
           <div @click="openBomInNewTab(taskData.attached_bom)" class="selected-bom" v-if="taskData.attached_bom">
            <div class="elipsis-text bom-name" v-overflow-tooltip>
               {{ taskData.attached_bom.name }}
            </div>
            <div class="flex">
              <img class="pointer"
              v-if="!collaborator"
              @click="deselectBom"
              src="~@/assets/images/icons/close-icon.svg"
              width="16px"/>
            </div>
           </div>
           <custom-dropdown v-else :list="bomList" :selected="taskData.attached_bom" @select="handleBomSelection"/>
           <!-- <select v-model="taskData.attached_bom" @change="() => this.taskData.attached_bom_quantity = 1" v-else :disabled="disableValue || collaborator">
           <option v-for="bom in bomList" :key="bom.id" :value="bom">{{ bom.name }}</option>
           </select> -->
          </div>
         <div class="input-group mt-2" v-if="taskData.attached_bom">
          <label class="key">Quantity for attached BOM:</label>
          <input type="number" v-model.number="taskData.attached_bom_quantity" min="1"  @change="validateBomQuantity" @keydown="changeNumber" :disabled="collaborator"/>
        </div>
        </div>
        <div class="input-group mt-2 flex">
          <label class="key">Assignee</label>
        </div>
        <div class="grid-2 mt-2"  v-if="!collaborator">
          <select class="tenant-list" v-model="selectedTenant" :disabled="disableValue">
            <option v-for="tenant in tenantsListToShare" :key="tenant.id" :value="tenant.id">
              {{ tenant.name }}
            </option>
          </select>
          <div class="input-group  add-user__search add-user__body" ref="addUserBody">
          <div class=" add-user__search_box">
            <input
              placeholder="Search for users"
              type="text"
              v-model="searchKeyword"
              @click="openPopup = true"
              :disabled="disableValue"
            />
            <div v-if="searchKeyword" class="add-user__search_close v-center h-center">
              <img   c src="~@/assets/images/icons/close-icon.svg" width="20px" @click="searchKeyword=''" alt="">
            </div>
          </div>
          <div class="add-user__list" v-if="openPopup" >
            <div class="add-user__list-no-result" v-if="!nonAddedUsers.length">
              No users found
            </div>
            <div
              class="add-user__list-item"
              v-for="user in nonAddedUsers"
              :key="user.associated_user.id"
              tabindex="0"
              @click="updateSelectedUsers(user.associated_user)"
            >
              <div class="add-user__list-item__avatar">
                <avatar :user="user.associated_user" size="24px" />
              </div>
              <div class="add-user__list-item__name">
                {{ user.associated_user.first_name }}
                {{ user.associated_user.last_name }}
              </div>
              <div class="add-user__list-item__action"></div>
            </div>
          </div>
        </div>
        </div>
        <div class="add-user__selected mt-4 ">
              <div
                class="assignees-box"
                v-for="(user, index) in selectedAssignees"
                :key="user.id"
              >
                <div class="add-user__list-item__avatar">
                  <avatar :user="user" size="24px" />
                </div>
                <div class="add-user__list-item__name">
                  {{ user.first_name }}
                  {{ user.last_name }}
                </div>
                <img
                  v-if="!collaborator"
                  class="pointer ml-1"
                  @click="removeAssignee(index,user)"
                  src="~@/assets/images/icons/close-icon.svg"
                  width="16px"
                  alt=""
                />
              </div>
            </div>
      <div class="grid-2 mt-2">
        <div class="grid-1 mt-2">
          <div>
        <label class="key"  >Tags</label>
        <tagInput
        v-if="!disableValue  && !collaborator"
        title="Add tags to task"
         :type="2"
         :lastParentId="tagGroupData?.tagArray[selected_tag_line]?.at(-1)?.id"
         :tagGroupData="tagGroupData"
          @addNewTag="addnewTag"
          @parentLevelSelected="changeSelectedTagLine"/>
        <div class="tag-container">
          <div class="tags" v-for="tag in selectedTags" :key="tag.id">
            <div class="selected-tags">
              {{ tag.name }}
            </div>
          </div>
          <div class="tag-container"   >
            <label class="key" v-if="tagGroupData?.tagArray?.length> 0" >Attached Tags:</label>
            <div v-else>
              <b class="no-tag-found">No tag is Attached!</b>
            </div>
            <div :class="{'tags mt-2 p-1':true, 'tags-line-selected p-1': selected_tag_line===index }"  v-for="(tagParentwise,index) in tagGroupData.tagArray" :key="index" @click="selected_tag_line=index">
              <div v-for="(tag, cindex) in tagParentwise" :key="tag.id "  class="flex v-center tags-line ">
                  <img width="8px" src="~@/assets/images/right-arrow-icon.svg" alt="" :class="{ 'tags-line-first-img': cindex === 0 }" />
                <div :class="{
                  'attached-tags v-center h-center ': true
              }"

                >
                {{ tag.name }}
                    <img
                :class="{
                  'pointer ml-1 close-icon': true,
                  }"
                @click="removeAttachedTags(tag.id)"
                v-if="!collaborator"
                @mouseover="sethoveredtag(cindex,index)"
                @mouseleave="sethoveredtag(null,null)"
                src="~@/assets/images/icons/close-icon.svg" width="16px"
              />
              <div :class="{'attachedTags_overLay': cindex>=hoverd_tag?.cindex && index===hoverd_tag?.index}">
              </div>
              </div>

              </div>
            </div>
          </div>
          <div class="grid-1 mt-2">
        <label class="key" >
        Attach Documents</label>
        <div class="document-attach">
          <div v-if="attachedDocs?.length > 0" class="weight-500 document-attach-view input-group imp "><span v-for="file in attachedDocs" :key="file.id">
            <span v-if="file.flag!=='deleted'" class="document-attach-list" v-overflow-tooltip @click="$emit('openTaskDocument',file)">  {{ file.core_document.doc_name}}</span></span></div>
          <span v-if="!collaborator" v-tooltip="' Link Documents '" class="document-attach-edit"  @click="documentSelectDialog = true"> <img src="~@/assets/images/icons/file-icon.svg" width="10px" height="10px" alt="">
          </span>
          </div>
    </div>
        </div>
      </div>
        </div>
      </div>
    </form>
    <div class="flex space-between pt-3 m">
      <div>
        <button
        class="btn btn-error btn-white-text pointer"
        :disabled="restrictDeletion"
        v-if="showDeleteButton"
        v-tooltip="deleteButtonTooltip"
        @click="deleteTask">
     Delete
        </button>
      </div>
      <div>
        <button
          class="btn btn-black mr-3"
          @click="$emit('close')"
          type="button"
        >
          CANCEL
        </button>
        <button
          class="btn"
          type="button"
          @click="saveAndClose()"
        >
          SAVE
        </button>
      </div>
      </div>
      <modal
      v-if="!disableValue"
    :open="documentSelectDialog"
    @close="documentSelectDialog = false"
    :closeOnOutsideClick="true"
    title=""
    >
    <document-selector @close="documentSelectDialog = false" @files-selected="handleFileSelection" v-if="documentSelectDialog"  :linkedDocs="attachedDocs" :uploadDocButton="uploadDocButton"/>
  </modal>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import avatar from '../common/avatar.vue'
import { getGanttTypeFromString } from '@/helper/gantt/getGanttType'
import { GetTaskStatuses } from '../../api/apis/projectPlanner'
import tagInput from '../common/tagInput-edit-material.vue'
import { GetAllCollabaratorList, GetCurrentCalendarList, GetAllTaskDocs, GetAllProjectBomList, checkTimesheetEntry, checkIfTaskIsParent } from '@/api'
import { TagTrie } from '@/utils/tagsHelper'
import Modal from '../common/modal.vue'
import DocumentSelector from '../document/documentSelectorDialog.vue'
import ConfirmationDialog from '@/plugins/confirmationDialog'
import customDropdown from '../common/customDropdown.vue'
import { alert } from '@/plugins/notification'
import { restrictKeys } from '@/utils/validations'

export default {
  components: { avatar, tagInput, DocumentSelector, Modal, customDropdown },
  props: {
    task: {
      type: Object,
      default: () => ({})
    },
    view: {
      type: String,
      default: 'chart'
    }
  },
  data () {
    return {
      tenantsListToShare: [],
      selectedTenant: '',
      restrictDeletion: false,
      showDeleteButton: false,
      deleteButtonTooltip: 'Delete task',
      haveDependencies: false,
      taskData: {
        name: '',
        description: '',
        duration: null,
        start_date: '',
        end_date: '',
        progress: 0,
        type: '',
        timeSpent: '',
        status: null,
        task_assignees: [],
        attached_bom: null,
        tags: null,
        cost: 0,
        actualStartDate: '',
        actualEndDate: '',
        attached_bom_quantity: ''
      },
      bomList: [],
      selectedAssignees: [],
      searchKeyword: '',
      openPopup: false,
      tasks: [],
      attachedTags: [],
      selectedTags: [],
      calendarLists: [],
      calendarHolidays: '',
      calendarWorkHours: '',
      calendarWorkDays: '',
      tagGroupData: {
        firstLevelParents: [],
        tagArray: []
      },
      hoverd_tag: {
        cindex: null,
        index: null
      },
      selected_tag_line: 0,
      tagTrie: new TagTrie(),
      lastParentId: null,
      documentSelectDialog: false,
      attachedDocs: [],
      uploadDocButton: false,
      disableValue: false,
      newProgressValue: 0,
      detailObject: {
        open: false,
        fileId: '',
        view_only: false

      },
      fileDetails: []
    }
  },
  computed: {
    ...mapGetters(['tenantList', 'tenantUsersList', 'currentProject', 'user', 'openTenantId', 'collaborator', 'isExternalCollaborator']),
    nonAddedUsers () {
      if (this.tenantsListToShare[0]?.id !== this.selectedTenant) {
        return this.tenantsListToShare.find((tenant) =>
          tenant.id === this.selectedTenant
        )?.users ?? []
      }
      return this.tenantUsersList
        .filter((user) => {
          return (
            this.currentProject.project_user_associations.find(
              (item) =>
                item.user_id === user.associated_user.id && item.status === 1 && item.role_id !== 4
            ) &&
            !this.selectedAssignees.find(
              (item) => item?.id === user.associated_user.id
            )
          )
        })
        .filter((user) => {
          return (
            user.associated_user.first_name
              .toLowerCase()
              .includes(this.searchKeyword.toLowerCase()) ||
            user.associated_user.last_name
              .toLowerCase()
              .includes(this.searchKeyword.toLowerCase())
          )
        })
    },
    getAppliedTags () {
      return this.attachedTags.filter((item) => item.status !== 'deleted')
    }
  },
  mounted () {
    this.$el.addEventListener('click', this.handleOutsideClick)
    this.tenantList.forEach((item) => {
      if (item.id === this.openTenantId) {
        this.tenantsListToShare.push({
          id: item.id,
          name: item.company_name,
          collaborator: false
        })
      }
    })
    this.selectedTenant = this.tenantsListToShare[0]?.id
    GetAllCollabaratorList().then((res) => {
      res.tenant_company_association.map((item) => {
        this.tenantsListToShare.push({
          id: item?.target_tenant?.id,
          name: item?.target_tenant?.company_name,
          collaborator: true,
          users: item?.target_tenant?.tenant_users
        })
      })
    }).catch((err) => {
      console.log(err)
    })
    const task = JSON.parse(JSON.stringify(this.$props.task))
    this.taskData.name = task.text
    this.taskData.description = task.description
    this.taskData.duration = task.duration
    this.taskData.status = task.status
    this.taskData.cost = task?.cost ?? 0
    this.taskData.attached_bom = task?.attached_bom ?? null
    this.taskData.attached_bom_quantity = task?.attached_bom?.quantity ?? null
    if (this.$props.view === 'chart') {
      this.taskData.progress = Math.round(task?.progress * 100)
    } else {
      this.taskData.progress = task.progress
    }
    this.newProgressValue = this.taskData.progress
    this.taskData.start_date = new Date(task.start_date ?? task.planned_start_date)
      .toISOString()
      .substr(0, 10)
    this.taskData.end_date = new Date(task.end_date ?? task.planned_end_date)
      .toISOString()
      .substr(0, 10)
    this.taskData.timeSpent = task?.timeSpent ?? 0
    this.taskData.actualStartDate = task?.actualStartDate
    this.taskData.actualEndDate = task?.actualEndDate
    this.taskData.tags = task?.tag_tasks ?? []
    if (task?.task_assignees) {
      this.disableProgress(task.task_assignees)
    }
    if (!Array.isArray(task.task_assignees)) {
      task.task_assignees = []
    }
    for (const element of task.task_assignees) {
      if (element?.status !== 'deleted') {
        this.selectedAssignees.push({
          id: element?.user_id ?? element?.id,
          first_name: element?.assignee?.first_name ?? element?.first_name,
          last_name: element?.assignee?.last_name ?? element?.last_name,
          status: element?.status ?? null
        })
      }
    }
    this.taskData.task_assignees = task?.task_assignees?.map((element) => {
      return {
        id: element?.id,
        first_name: element?.first_name,
        last_name: element?.last_name,
        status: element?.status ?? null
      }
    })
    if (!task.task_docs && !this.isNewTask()) {
      const taskId = task.id
      GetAllTaskDocs(taskId).then((res) => {
        if (res.task_document_association) {
          this.attachedDocs = res.task_document_association
        }
      })
    } else {
      this.attachedDocs = task.task_docs?.map(doc => ({ core_document: doc })) ?? []
    }
    const day = new Date()
    day.setUTCHours(0)
    day.setUTCMinutes(0)
    day.setUTCSeconds(0)
    day.setUTCMilliseconds(0)
    if (!this.isNewTask() && this.user.projectLevelRole === 'ADMIN') {
      if (this.view === 'chart') {
        if (task.src_task_links.length) {
          this.haveDependencies = true
        } else if (task.target_task_links.length) {
          this.haveDependencies = true
        }
      } else {
        if (task.predecessors.length) {
          this.haveDependencies = true
        } else if (task.successors.length) {
          this.haveDependencies = true
        }
      }
      if (task.$level === 0) {
        this.showDeleteButton = false
      } else {
        Promise.all([checkIfTaskIsParent(task.id), checkTimesheetEntry(task.id)]).then(([, checkTimesheetEntryRes]) => {
          if (checkTimesheetEntryRes.user_timesheet.length) {
            this.restrictDeletion = true
            this.deleteButtonTooltip = 'There are timesheet entries on this task'
          }
          if (task.type === 'project') {
            this.restrictDeletion = true
            this.deleteButtonTooltip = 'This is a parent task'
          }
        }).catch(() => {
          alert('Something went wrong')
        }).finally(() => {
          this.showDeleteButton = true
        })
      }
    }
    // while mounting the component setting assigned users into selectedAssignees and submission data (taskData)
    GetAllProjectBomList(true)
      .then((res) => {
        // this.bomList = res.core_bom.filter(bom => bom.id !== this.taskAssociatedBom)
        this.bomList = res.core_bom
      })
      .catch(() => {
        alert('Failed to fetch bom')
      })
    this.taskData.type = getGanttTypeFromString(task.type)
    if (parseInt(this.taskData.type) === 3) {
      this.disableValue = true
    }
    this.taskStatusList()
    this.getAttachedTags()
  },
  beforeDestroy () {
    this.$el.removeEventListener('click', this.handleOutsideClick)
    document.body.removeEventListener('keydown', this.keyPress)
  },
  methods: {
    changeNumber (event) {
      if (['e', 'E', '+', '-'].includes(event.key)) {
        restrictKeys(event)
      }
    },
    onPaste (event) {
      const pastedData = event.clipboardData.getData('Text')
      if (/[^0-9.]/.test(pastedData)) {
        event.preventDefault()
      }
    },
    isNewTask () {
      return !isNaN(Number(this.$props.task.id))
    },
    handleBomSelection (bom) {
      this.taskData.attached_bom = bom
      this.taskData.attached_bom_quantity = 1
    },
    onChangeType (e) {
      if (parseInt(e.target.value) !== 3) {
        this.disableValue = false
        this.taskData.progress = this.newProgressValue
      } else {
        this.disableValue = true
        this.taskData.progress = 0
      }
    },
    openBomInNewTab (bom) {
      const bomId = bom.id
      const versionId = bom.versionId
      const url = this.getBomUrl(bomId, versionId)
      window.open(url, '_blank')
    },
    getBomUrl (bomId, versionId) {
    // Construct and return the URL based on the bom object
      return `/bom/project/bom/${bomId}?bomVersionId=${versionId}` // Example URL construction
    },
    disableProgress (taskAssignee) {
      const newSelectedAssignee = taskAssignee?.map((item) => {
        return item.id
      })
      if (newSelectedAssignee.includes(this.user.userId)) {
        this.enableProgress = true
      }
      if (this.user.projectLevelRole === 'ADMIN') this.enableProgress = true
    },
    deleteTask () {
      ConfirmationDialog(`${this.haveDependencies ? 'This task has dependencies.' : ''}
      Are you sure you want to delete this task`, (res) => {
        if (res) {
          this.$emit('delete-task', this.$props.task.id)
        }
      })
    },
    deselectBom () {
      this.taskData.attached_bom = null
    },
    closeAssigneePopup () {
      this.openPopup = false
    },
    getCalendarsList () {
      GetCurrentCalendarList().then((res) => {
        this.calendarLists = res?.core_project_calendar.map((item) => {
          this.calendarHolidays = item.calendar_holidays
          this.calendarWorkHours = item.working_hours
          this.calendarWorkDays = item.calendar_working_days
        })
      }).catch(err => console.log(err.message)).finally(() => {
        this.loading = false
      })
    },
    handleTagSelection (selectedTag) {
      const newTags = selectedTag.filter(tag => !this.selectedTags.some(selected => selected.id === tag.id))
      this.selectedTags.push(...newTags)
    },
    handleOutsideClick (e) {
      if (!this?.$refs?.addUserBody?.contains(e.target)) {
        this.openPopup = false
      }
    },
    updateSelectedUsers (user) {
      const assigneeObj = { id: user?.id, first_name: user?.first_name, last_name: user?.last_name, status: 'new' }
      if (this.selectedTenant !== this.openTenantId) {
        assigneeObj.tenantId = this.selectedTenant
      }
      this.taskData.task_assignees.push(assigneeObj)
      this.selectedAssignees.push(user)
      this.openPopup = false
      this.searchKeyword = ''
    },
    removeAssignee (index, user) {
      let assigneeIndex = -1
      this.taskData.task_assignees.forEach((element, index) => {
        if (element?.id === user?.id) {
          if (element.status === 'new') {
            assigneeIndex = index // if it is a newly added assignee no need to remove from the backend
          } else {
            this.taskData.task_assignees[index].status = 'deleted'
          }
        }
      })
      if (assigneeIndex !== -1) {
        this.taskData.task_assignees.splice(assigneeIndex, 1)
      }
      this.selectedAssignees.splice(index, 1)
    },
    taskStatusList () {
      GetTaskStatuses().then((res) => {
        this.tasks = res.custom_list_values.map((item) => {
          return {
            label: item.name,
            id: item.id
          }
        })
        if (this.taskData.status === null || this.taskData.status === undefined) {
          this.updateStatusBasedOnProgress(this.taskData.progress)
        }
      })
    },
    validateProgress () {
      if (this.taskData.progress > 100) {
        this.taskData.progress = 100
      } else if (this.taskData.progress < 0) {
        this.taskData.progress = 0
      }
    },
    updateStatusBasedOnProgress (progress) {
      const todoStatus = this.tasks.find((task) => task.label === 'TO DO')
      const doneStatus = this.tasks.find((task) => task.label === 'DONE')
      const inProgressStatus = this.tasks.find((task) => task.label === 'IN PROGRESS')
      if (progress <= 0 && todoStatus) {
        this.taskData.status = todoStatus.id
      } else if (progress >= 100 && doneStatus) {
        this.taskData.status = doneStatus.id
      } else if (inProgressStatus) {
        this.taskData.status = inProgressStatus.id
      }
    },
    saveAndClose () {
      this.taskData.tags = this.tagGroupData.tagArray
      let previouslyAttachedBom = null
      if (this.$props.task.attached_bom) {
        previouslyAttachedBom = JSON.parse(JSON.stringify(this.$props.task.attached_bom))
      }
      const currentlyAttachedBom = JSON.parse(JSON.stringify(this.taskData.attached_bom)) ?? {}
      if (parseInt(this.taskData.attached_bom_quantity)) {
        currentlyAttachedBom.quantity = parseInt(this.taskData.attached_bom_quantity)
      }
      if (previouslyAttachedBom && currentlyAttachedBom?.id) {
        if (previouslyAttachedBom?.id !== currentlyAttachedBom?.id) {
          previouslyAttachedBom.tag = 'deleted'
          currentlyAttachedBom.tag = 'new'
          this.taskData.attachedBom = [previouslyAttachedBom, currentlyAttachedBom]
        } else {
          if (previouslyAttachedBom.quantity === currentlyAttachedBom.quantity) {
            this.taskData.attachedBom = null
          } else {
            currentlyAttachedBom.tag = 'updated'
            currentlyAttachedBom.associationId = previouslyAttachedBom.associationId
            this.taskData.attachedBom = [currentlyAttachedBom]
          }
        }
      } else if (!previouslyAttachedBom && currentlyAttachedBom.id) {
        currentlyAttachedBom.tag = 'new'
        this.taskData.attachedBom = [currentlyAttachedBom]
      } else if (previouslyAttachedBom && !currentlyAttachedBom.id) {
        previouslyAttachedBom.tag = 'deleted'
        this.taskData.attachedBom = [previouslyAttachedBom]
      }
      const attchedDocs = this.attachedDocs.map((doc) => { return { ...doc.core_document, flag: doc.flag } })
      this.$emit('update-and-close', this.taskData, attchedDocs)
    },
    calculateEndDate (duration, startDate) {
      if (duration <= 0) {
        this.taskData.end_date = startDate
        return startDate
      } else {
        const holidays = []
        const days = {}
        this.calendarHolidays.map((holiday) => {
          holidays.push(new Date(holiday.date).toDateString())
        })
        this.calendarWorkDays.forEach((day) => {
          days[day.work_day] = this.calendarWorkHours
        })
        const endDate = new Date(startDate)
        if (holidays.includes(endDate)) {
          const task = JSON.parse(JSON.stringify(this.$props.task))
          this.taskData.start_date = new Date(task.start_date ?? task.planned_start_date)
            .toISOString()
            .substr(0, 10)
          alert('Its holiday in calendar please choose another date')
        } else {
          if (!holidays.includes(endDate.toDateString())) {
            duration = duration - (days[endDate.getDay()])
          }
          while (duration > 0) {
            endDate.setDate(endDate.getDate() + 1)
            if (!holidays.includes(endDate.toDateString())) {
              duration = duration - (days[endDate.getDay()])
            }
          }
          const newEndDate = new Date(endDate).toISOString().substr(0, 10)
          this.taskData.end_date = newEndDate
          return newEndDate
        }
      }
    },
    getAttachedTags () {
      this.attachedTags = this.$props.task.tag_tasks?.map(item => {
        return {
          // after updating the all tags will come as  tag object else it comes inside an object like  {tag:{}}
          id: item.tag?.id ?? item.id,
          name: item.tag?.name ?? item.name,
          parentId: item.tag?.parent_id ?? item.parentId
        }
      }) ?? []
      if (this.attachedTags) {
        this.tagTrie._generateTreeFromUnorderedList(this.attachedTags)
        this.tagGroupData = this.tagTrie.groupTagInArray()
      }
    },
    removeAttachedTags (tagId) {
      this.tagTrie.deleteTagById2(tagId)
      this.tagGroupData = this.tagTrie.groupTagInArray()
    },
    sethoveredtag (cindex, index) {
      this.hoverd_tag.cindex = cindex
      this.hoverd_tag.index = index
    },
    addnewTag (tag) {
      this.tagTrie.insertTag({ name: tag.name, id: tag.id, parentId: tag.parent_id })
      this.tagGroupData = this.tagTrie.groupTagInArray()
    },
    changeSelectedTagLine (id) {
      if (id === null) {
        this.selected_tag_line = id
      } else {
        this.tagGroupData.tagArray.forEach((element, index) => {
          if (element[0].id === id) { this.selected_tag_line = index }
        })
      }
    },
    handleFileSelection (selectedFileData) {
      // the selectedfiles data is comming from modal (contains  all the data related to selected docs)
      this.documentSelectDialog = false
      this.attachedDocs = selectedFileData
    },
    validateBomQuantity () {
      if (parseInt(this.taskData.attached_bom_quantity) <= 0) {
        alert('Quantity should be greater than or equal to 1')
        this.taskData.attached_bom_quantity = 1
      }
    },
    keyPress (e) {
      if (e instanceof KeyboardEvent && e.code === 'Escape') {
        this.$emit('close')
      } else if (e instanceof KeyboardEvent && e.code === 'Enter') {
        this.saveAndClose()
      }
    }
  },
  watch: {
    'taskData.progress': function (newProgress) {
      this.updateStatusBasedOnProgress(newProgress)
    },
    selectedCalendar () {
      this.getCalendarsList()
    },
    'task.tag_tasks': function () {
      this.getAttachedTags()
    }
  },
  created () {
    this.getCalendarsList()
    document.body.addEventListener('keydown', this.keyPress)
  }
}

</script>
<style lang="scss" scoped>

.tenant-list {
  padding: 0.5rem;
  border-radius: 0.25rem;
  width: 100%;
  border: 1px solid rgba(59, 59, 59, 0.4666666667);
  background-color: transparent;
}

.bom-name {
  max-width: 20rem;
}

form {
  width: 40vw;
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 8px;
}
.gantt-edit-form{
  position: relative;
}

.selected-bom {
    border: 1px solid rgba(59, 59, 59, 0.4666666667);
    width: 100%;
    border-radius: 0.285em;
    font-size: 1em;
    padding: 0.7em;
    display: flex;
    justify-content: space-between;
}

.add-user {
  &__body {
    position: relative;
  }
  &__search {
    &_box{
      position:relative;
    }
  input {
      padding: 0.5rem;
      border-radius: 0.25rem;
      width: 100%;
      margin-right: 10px;
    }
    &_close{
      position: absolute;
      width:30px;
      background-color: var(--bg-color);
      height:90%;
      border-radius: 0.25rem;
    /* left: 0; */
    right: 1px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    }
  }
  &__selected {
display: grid;
grid-template-columns: repeat(3,1fr);
gap:4px
  }
  &__list {
    width: 100%;
    max-height: 20rem;
    overflow-y: auto;
    position: absolute;
    background: white;
    left: 0px;
    right: 0px;
    z-index: 1;
    top: 40px;
    padding: 10px;
    box-shadow: 2px 2px 4px rgb(0 0 0 / 20%);
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    &-no-result {
      padding: 1rem;
      text-align: center;
    }
    &-item {
      display: flex;
      align-items: center;
      padding: 0.5rem 0;
      border-bottom: 1px solid #e8e8e8;
      cursor: pointer;
      &:hover {
        background: #f8f8f8;
      }
      &__avatar {
        margin-right: 1rem;
      }
      &__name {
        flex: 1;
        white-space: nowrap;
      }
      &__action {
        &__btn {
          padding: 0.25rem;
          border: 1px solid #e8e8e8;
          border-radius: 0.25rem;
          background: #fff;
          cursor: pointer;
          i {
            font-size: 1.25rem;
          }
        }
      }
    }
  }
}
.tags {
  display: flex;
flex-wrap: wrap;
margin-left: 5px;
  margin-right: 5px;
  row-gap: 5px;
   &-line-selected{
    background-color: var(--brand-light-color);
   border: .4px solid var(--brand-color);
   border-radius: 5px;
   min-width: min-content;
   }
}
.tag-container {
  margin-top: 4px;
}

.no-tag-found{
  margin-left: 5px;
}
.attached-tags{
  cursor:pointer;
  background-color: var(--brand-color);
  padding: 0.6rem;
  font-size: small;
  border-radius: 0.4rem;
position:relative;
  &-fade{
background-color: rgb(0, 0,0,.6) ;
color:white
}
  & img{
 z-index:4;
  }
  & img{
    display: none;
  }
}
.tags-line{
  text-overflow: ellipsis;
  overflow: hidden;
   white-space: nowrap;
  & img{
    margin-inline: .5rem;
  }
  &-first-img{
    display: none;
  }
  .attached-tags:hover > img {
display: block;
scale: 1.2;
  }
  .attachedTags_overLay{
    background-color: black;
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 5px;
    opacity: .3;
    z-index: 1;
  }
}
.assignees-box{
  display: flex;
  align-items: center;
  background-color: var(--brand-color);
  padding: 0.6rem;
  font-size: small;
  border-radius: 0.4rem;
}
.button-box{
  padding: 10px 5px 5px 0;
  width: 100%;
  background-color:var(--bg-color);
  right:10px;
  bottom:0;
  border-top: .1px solid rgb(0, 0,0,.1);
  margin-top: 10px ;
}
.document-attach{
    display: flex;
    align-items: center;
    justify-content: start;
    flex-wrap: wrap;
    width: 100%;
    // max-height: 100px;
    // min-height: 50px;
    height: auto;
    border-radius: 4px;
    padding: 0.6rem;
    // border: 2px solid rgba(var(--brand-rgb),0.4);
    cursor: pointer;
    &-view{
      display: flex;
      flex-wrap: wrap;
      gap: 14px;

    }
    &-edit{
      border: 2px solid rgba(41, 39, 39, 0.2);
      border-radius: 3px;
      padding: 4px 6px;
      width: 25px;
      display: flex;
      justify-content: center;
      margin-top: 8px;
    }
    &-list{
      border: 2px solid rgba(var(--brand-rgb));
      border-radius: 3px;
      background-color: var(--brand-color);
      padding: 5px 5px 5px 5px ;
    width: 100px;
    text-overflow: ellipsis;
    overflow-y: auto;
    white-space: nowrap;
    cursor:context-menu;
    }
    span{
      cursor: pointer;
      user-select: none;
      background-color: transparent;
    }
  }
</style>
