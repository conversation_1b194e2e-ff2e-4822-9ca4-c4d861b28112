<template>
  <div class="project-list">
    <div class="project-list-nav v-center space-between">
      <h2 class="weight-500 xxxl v-center">
        Manage Your Projects Here
      </h2>
      <div class="v-center">
        <div class="input-group search mx-4">
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="Search by Name"
          />
        </div>
        <button v-if="isTenantAdminOrCollaborator" @click="openDrawer" class="btn">
          Create New Project
        </button>
      </div>
    </div>
    <div class="project-list-container my-5">
      <project-table
        :projectList="getSearchResult"
        :perPage="perPage"
        :pageNumber="pageNumber"
        :showHeader="true"
        :loading="loading"
        :collaborator="collaborator"
        @delete="deleteProject"
        @edit="editProject"
      ></project-table>
      <pagination
        :length="getSearchResult.length"
        :perPage="perPage"
        :pageNumber="pageNumber"
        @selectPage="selectPage"
        class="mt-3 mx-2"
      />
    </div>
    <div v-if="showProjectAction" class="drawer" :class="drawer ? 'open' : 'close'">
      <div class="project-form-container">
        <project-form
          ref="projectForm"
          :isUpdate="openId !== null"
          :drawer="drawer"
          @cancel="closeDrawer"
          @save="saveProject"
          @update="updateProject"
          :closeOnOutsideClick="true"
          :buttonDisabled="buttonDisabled"
        ></project-form>
      </div>
    </div>
  </div>
</template>
<script>
import {
  GetAllProjectsList,
  CreateNewProject,
  DeleteProjectById,
  UpdateProjectById,
  AddNewTask
} from '@/api'
import projectTable from '../../components/common/projectTable.vue'
import ProjectForm from '@/components/manage/projectForm.vue'
import { arraySearch } from '@/utils/array'
import Pagination from '../../components/common/pagination.vue'
import { mapGetters } from 'vuex'
import { projectExchangeToken } from '../../api/session'
import { GetTaskStatuses } from '../../api/apis/projectPlanner'
import config from '../../config'
import http from '../../api/http'
import { alert } from '@/plugins/notification'

export default {
  name: 'Projects',
  components: { projectTable, ProjectForm, Pagination },
  data () {
    return {
      drawer: false,
      openId: null,
      searchKeyword: '',
      projectList: [],
      pageNumber: 1,
      perPage: 10,
      loading: false,
      status: '',
      buttonDisabled: false // this for disabling update button  of project updation

    }
  },
  computed: {
    ...mapGetters(['user', 'getUserById', 'collaborator']),
    getSearchResult () {
      return arraySearch(this.projectList, this.searchKeyword, { fieldsToSearch: ['name'] })
    },
    isTenantAdminOrCollaborator () {
      return (
        this.user.tenantLevelRole === 'ADMIN' && !this.collaborator)
    },
    showProjectAction () {
      return (
        this.user.tenantLevelRole === 'ADMIN' ||
          this.user.tenantLevelRole === 'EDITOR' ||
          this.user.tenantLevelRole === 'COLLABORATOR'
      )
    }
  },
  methods: {
    selectPage (pageNumber) {
      this.pageNumber = pageNumber
    },
    openDrawer () {
      this.drawer = true
    },
    closeDrawer () {
      this.$refs.projectForm.resetForm()
      this.openId = null
      this.drawer = false
    },
    editProject (obj) {
      this.openId = obj.id
      this.$refs.projectForm.name = obj.name
      this.$refs.projectForm.latitude = obj.latitude
      this.$refs.projectForm.longitude = obj.longitude
      this.$refs.projectForm.selectedTenant = obj.associated_tenant?.id
      this.$refs.projectForm.startDate = obj.planned_start_date
      this.$refs.projectForm.endDate = obj.planned_end_date
      this.$refs.projectForm.cost = obj.project_cost
      this.$refs.projectForm.revenue = obj.project_revenue
      this.$refs.projectForm.address = obj.address?.address
      this.$refs.projectForm.state = obj.address?.state
      this.$refs.projectForm.city = obj.address?.city
      this.$refs.projectForm.pincode = obj.address?.pincode
      this.openDrawer()
    },
    saveProject (obj) {
      this.buttonDisabled = true
      const data = {
        name: obj.name.trim(),
        planned_start_date: obj.planned_start_date,
        planned_end_date: obj.planned_end_date,
        project_cost: obj.project_cost,
        project_revenue: obj.project_revenue,
        address: obj.address,
        location:
          obj.latitude && obj.longitude
            ? `(${obj.latitude}, ${obj.longitude})`
            : null
      }
      CreateNewProject(data).then((res) => {
        this.getAllProjectsList()
        projectExchangeToken(null, res.insert_core_projects_one.id).then(() => {
          if (obj.copyProjectPlan) {
            const copyProjectId = obj.copyProjectPlan
            http.POST(config.serverEndpoint + '/project/copy-project-plan', {
              copyProjectId,
              assignees: obj.assignees,
              boms: obj.boms,
              bomTaskAssoc: obj.bomTaskAssoc,
              docTaskAssoc: obj.docTaskAssoc,
              documents: obj.documents
            }, 'project').then((res) => {
              console.log(res)
              this.$notify.success('Copy Project Plan Successful.')
              this.closeDrawer()
              this.buttonDisabled = false
            }).catch(() => {
              this.$notify.alert('Failed to copy project plan')
              this.buttonDisabled = false
            })
          } else {
            AddNewTask({
              name: data.name,
              planned_end_date: data.planned_end_date,
              planned_start_date: data.planned_start_date,
              type: 2,
              level: 0,
              status: this.status,
              order_index: '1'
            }).then(() => {
              this.getAllProjectsList()
              this.$notify.success('Project Created Successfully')
              this.closeDrawer()
              this.buttonDisabled = false
            }).catch(() => {
              this.$notify.alert('Failed to create project task')
              this.buttonDisabled = false
            })
          }
        })
      }).catch((err) => {
        this.buttonDisabled = false
        if (err?.message?.includes('Uniqueness violation')) {
          return this.$notify.alert('Project name already exists')
        }
        this.$notify.alert(err?.message ?? 'Something went wrong')
      })
    },
    deleteProject (obj) {
      DeleteProjectById({ id: obj.id }).then((res) => {
        this.getAllProjectsList()
        this.$notify.success('Project Deleted Successfully')
      })
    },
    updateProject (obj) {
      this.buttonDisabled = true
      const data = {
        name: obj.name?.trim(),
        planned_start_date: obj.planned_start_date,
        planned_end_date: obj.planned_end_date,
        project_cost: obj.project_cost,
        project_revenue: obj.project_revenue,
        address: obj.address,
        location:
          obj.latitude && obj.longitude
            ? `(${obj.latitude}, ${obj.longitude})`
            : null
      }
      UpdateProjectById({ id: this.openId, data }).then((res) => {
        this.getAllProjectsList()
        this.closeDrawer()
        this.$notify.success('Project Updated Successfully')
        this.buttonDisabled = false
      }).catch(err => {
        this.buttonDisabled = false
        if (err?.message?.includes('Uniqueness violation')) {
          return this.$notify.alert('Project name already exists')
        }
        this.$notify.alert(err?.message ?? 'Something went wrong')
      })
    },
    getAllProjectsList () {
      this.loading = true
      GetAllProjectsList()
        .then((res) => {
          this.$store.commit('setTenantProjectList', res?.core_projects)
          this.loading = false
          this.projectList = res.core_projects?.map((item) => {
            const location = item.location
              ? item.location.replace('(', '').replace(')', '').split(',')
              : []
            return {
              ...item,
              latitude: location[0] || '',
              longitude: location[1] || ''
            }
          })
        })
        .catch((error) => {
          console.log(error)
          alert('Something went wrong')
          this.loading = false
        })
    },
    taskStatus () {
      GetTaskStatuses().then((res) => {
        this.tasks = res.custom_list_values.map((item) => {
          return {
            label: item.name,
            id: item.id
          }
        })
        const todoId = this.tasks.find((task) => task.label === 'TO DO')?.id
        this.status = todoId
      })
    }
  },
  mounted () {
    this.taskStatus()
  },
  created () {
    this.getAllProjectsList()
  }
}
</script>
<style lang="scss" scoped>
.project-form-container {
}
.drawer {
  top:60px;
  overflow-y: auto;
}
</style>
