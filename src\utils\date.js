export const Duration = (pastDate) => {
  if (!pastDate) return '--'
  const now = new Date()
  const date = new Date(pastDate)
  const diff = now.valueOf() - date.valueOf()
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  const months = Math.floor(days / 30)
  const years = Math.floor(months / 12)
  if (seconds < 60) {
    return `${seconds} seconds ago`
  } else if (minutes < 60) {
    return `${minutes} minutes ago`
  } else if (hours < 24) {
    return `${hours} hours ago`
  } else if (hours >= 24 && hours < 36) {
    return 'yesterday'
  } else if (days < 30) {
    return `${days} days ago`
  } else if (months < 12) {
    return `${months} months ago`
  } else {
    return `${years} years ago`
  }
}

// this function intakes parameter as 2023-W28 and returns first date of that week
export const startAndEndDateFinder = (week, year) => {
  week = parseFloat(week)
  year = parseFloat(year)
  if (week < 1 || week > 53) {
    throw new RangeError('ISO 8601 weeks are numbered from 1 to 53')
  } else if (!Number.isInteger(week)) {
    throw new TypeError('Week must be an integer')
  } else if (!Number.isInteger(year)) {
    throw new TypeError('Year must be an integer')
  }

  const simple = new Date(year, 0, 1 + (week - 1) * 7)
  const dayOfWeek = simple.getDay()
  const isoWeekStart = simple

  // Get the Monday past, and add a week if the day was
  // Friday, Saturday or Sunday.

  isoWeekStart.setDate(simple.getDate() - dayOfWeek + 1)
  if (dayOfWeek > 4) {
    isoWeekStart.setDate(isoWeekStart.getDate() + 7)
  }

  // The latest possible ISO week starts on December 28 of the current year.
  if (isoWeekStart.getFullYear() > year ||
        (isoWeekStart.getFullYear() === year &&
         isoWeekStart.getMonth() === 11 &&
         isoWeekStart.getDate() > 28)) {
    throw new RangeError(`${year} has no ISO week ${week}`)
  }
  const oneWeekDates = []
  oneWeekDates.push(new Date(isoWeekStart.setDate(isoWeekStart.getDate())))
  for (let i = 0; i < 6; i++) {
    oneWeekDates.push(new Date(isoWeekStart.setDate(isoWeekStart.getDate() + 1)))
  }
  return oneWeekDates
}

export const getMonthInString = (num) => {
  switch (num) {
  case 0:
    return 'Jan'
  case 1:
    return 'Feb'
  case 2:
    return 'Mar'
  case 3:
    return 'Apr'
  case 4:
    return 'May'
  case 5:
    return 'Jun'
  case 6:
    return 'Jul'
  case 7:
    return 'Aug'
  case 8:
    return 'Sep'
  case 9:
    return 'Oct'
  case 10:
    return 'Nov'
  case 11:
    return 'Dec'
  default:
    return null
  }
}

export const getDayToString = (num) => {
  switch (num) {
  case 0:
    return 'Sun'
  case 1:
    return 'Mon'
  case 2:
    return 'Tue'
  case 3:
    return 'Wed'
  case 4:
    return 'Thur'
  case 5:
    return 'Fri'
  case 6:
    return 'Sat'
  default:
    return null
  }
}

export const getWeekNumber = (date) => {
  date = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()))
  date.setUTCDate(date.getUTCDate() + 4 - (date.getUTCDay() || 7))
  const yearStart = new Date(Date.UTC(date.getUTCFullYear(), 0, 1))
  const weekNumber = Math.ceil(((date - yearStart) / 86400000 + 1) / 7)
  return weekNumber
}
// usually day numbers will come like sun=0 mon=1...
// this fn converts in into mon=0, tues=1..sun=6
export const getOrderdDayNumbers = (day) => {
  switch (day) {
  case 0:
    return 6
  case 1:
    return 0
  case 2:
    return 1
  case 3:
    return 2
  case 4:
    return 3
  case 5:
    return 4
  case 6:
    return 5
  default:
    return null
  }
}

export function getStartAndEndDateOfWeek (year, week) {
  const januaryFirst = new Date(year, 0, 1)
  const dayOfWeek = januaryFirst.getDay()
  const firstMonday = new Date(januaryFirst)

  // Calculate the first Monday of the year
  if (dayOfWeek !== 1) {
    firstMonday.setDate(8 - dayOfWeek)
  }

  // Calculate the start date of the specified week
  const startDate = new Date(firstMonday.getTime() + ((week - 1) * 7 * 24 * 60 * 60 * 1000))

  // Adjust startDate to Monday of the week
  startDate.setDate(startDate.getDate() + (1 - startDate.getDay()))

  // Calculate the end date of the specified week (Sunday)
  const endDate = new Date(startDate.getTime() + (6 * 24 * 60 * 60 * 1000))
  // Return the start and end dates of the week
  return {
    weekStartDate: startDate,
    weekEndDate: endDate
  }
}

export function getStartAndEndDateOfMonth (year, month) {
  month = Math.max(0, Math.min(11, month))
  const startDate = new Date(year, month, 1)
  const endDate = new Date(year, month + 1, 0)
  return { startDate, endDate }
}
// this is a function to get number of days in between two given dates
export function getDayDifference (startDate, EndDate) {
  const date1 = new Date(startDate)
  const date2 = new Date(EndDate)
  const timeDifference = date2.getTime() - date1.getTime()
  return timeDifference / (1000 * 60 * 60 * 24)
}

export function getWeeksInRangeFormatted (startDate, endDate) {
  const weeks = {}
  const currentDate = new Date(startDate)

  // Set the currentDate to the start of the week (Monday)
  if (currentDate.getDay() !== 1) {
    currentDate.setDate(currentDate.getDate() - (currentDate.getDay() || 7) + 1)
  }

  // eslint-disable-next-line no-unmodified-loop-condition
  while (currentDate <= endDate) {
    const weekStart = new Date(currentDate)
    const weekEnd = new Date(currentDate)
    weekEnd.setDate(weekEnd.getDate() + 6)

    const startString = formatDate(weekStart)
    const endString = formatDate(weekEnd > endDate ? endDate : weekEnd)
    const dateRange = `${startString} - ${endString}`
    const weekNumber = getWeekNumber(weekStart)

    weeks[weekNumber] = dateRange

    currentDate.setDate(currentDate.getDate() + 7)
  }

  return weeks
}

function formatDate (date) {
  const options = { weekday: 'short', day: '2-digit', month: 'short' }
  return date.toLocaleDateString('en-US', options)
}
export function formatFullDate (date) {
  date = new Date(date)
  const options = { day: '2-digit', month: 'short', year: 'numeric' }
  return date.toLocaleDateString('en-US', options)
}

export function genericFormatDate (isoString) {
  if (!isoString) return '--'
  const date = new Date(isoString)
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric'
  })
}
