export const getDuration = (startDate, endDate) => {
  startDate = new Date(startDate)
  endDate = new Date(endDate)
  const differenceInMilliseconds = endDate - startDate
  const millisecondsInADay = 1000 * 60 * 60 * 24
  return Math.floor(differenceInMilliseconds / millisecondsInADay) + 1
}

export const getDateAgo = (duration, currentDate = new Date()) => {
  // year, month,day,week
  switch (duration) {
  case 'year':
    currentDate.setFullYear(currentDate.getFullYear() - 1)
    break
  case 'month':
    currentDate.setMonth(currentDate.getMonth() - 1)
    break
  case '6months':
    currentDate.setMonth(currentDate.getMonth() - 6)
    break
  case 'day':
    currentDate.setDate(currentDate.getDate())
    break
  default:
    return null
  }
  return new Date(currentDate.setUTCHours(0, 0, 0, 0)).toLocaleDateString('en-US')
}
export const getDayDifference = (date1, date2) => {
  const startOfDay1 = new Date(date1).setHours(0, 0, 0, 0)
  const startOfDay2 = new Date(date2).setHours(0, 0, 0, 0)
  const differenceMs = Math.abs(startOfDay1 - startOfDay2)

  const msInDay = 24 * 60 * 60 * 1000
  const dayDifference = differenceMs / msInDay
  const result = {
    days: dayDifference
  }
  return JSON.stringify(result)
}
