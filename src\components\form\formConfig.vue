<template>
  <div class="form-config">
    <div class="l text-center bg-brand py-1 weight-500">
      Element Configuration
    </div>
    <div class="form-config-element-type" v-if="selectedFormElementToEdit !== null" >
      <span class="mr-1 weight-500" >Selected Element:</span> {{ selectedFormElement.key }}
    </div>
    <div class="form-config--element"  v-if="selectedFormElementToEdit !== null">
      <div class="input-group">
        <label>Caption:</label>
        <input type="text" :disabled="isFixed" v-model="configData.caption" />
      </div>
      <div v-if="configData.caption !== 'Bom'" class="input-group flex">
        <label class="mt-2" >Required:</label>
        <input type="checkbox" v-model="configData.required" :disabled="isFixed" />
      </div>
      <div class="input-group flex">
        <label class="mt-2" >Visibility on table:</label>
        <input type="checkbox" v-model="configData.visibility" :disabled="visibilityLimit && !configData.visibility"/>
        <img
                v-if="visibilityLimit"
                class="pointer ml-2"
                src="@/assets/images/icons/info-icon.svg"
                width="15"
                height="35"
                alt="info icon"
                v-tooltip="
                  'This feature is limited to two form fields only.'"
              />
      </div>
      <div v-if="configData.key === 'CONFIGURATION_LIST'" >
        <custom-list :listId="configData.custom_list_id"  @input="setListId"/>
      </div>
    </div>
    <div class="form-config--elements">
      <h3>Elements</h3>
      <div
        class="form-config--elements-item"
        :class="{
          selected: selectedFormElementToEdit === index,
        }"
        v-for="(element, index) in formTemplateBody"
        :key="element.id"
      >
        <div class="form-config--elements-item-name" :style="{
          color: element.fixed ? '#777' : '#000'
        }">{{ element.caption }}</div>
        <div class="v-center">
          <img
            :class="{
              disabled: index === formTemplateBody.length - 1,
            }"
            width="16"
            src="~@/assets/images/icons/arrow-down-icon.svg"
            @click="moveElementDown(index)"
          />
          <img
            :class="{
              disabled: index === 0,
            }"
            width="16"
            src="~@/assets/images/icons/arrow-up-icon.svg"
            @click="moveElementUp(index)"
          />
          <img :class="{
              disabled: element.fixed
          }" width="20" src="~@/assets/images/delete-icon.svg" @click="deleteElement(index)" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import CustomList from './customList.vue'
export default {
  name: 'FormConfig',
  components: {
    CustomList
  },
  data () {
    return {
      configData: null
    }
  },
  computed: {
    ...mapGetters('form', ['formTemplateBody', 'selectedFormElementToEdit']),
    selectedFormElement () {
      return this.formTemplateBody[this.selectedFormElementToEdit]
    },
    isFixed () {
      return this.selectedFormElement?.fixed
    },
    // checks total no. of visibility count if its >2 then disable visibility
    visibilityLimit () {
      return this.formTemplateBody.filter(ele => ele.visibility).length >= 2 && this.configData?.visibility !== true
    }
  },
  watch: {
    selectedFormElementToEdit (val) {
      if (val !== null) {
        this.configData = JSON.parse(JSON.stringify(this.selectedFormElement))
      }
    },
    configData: {
      handler (val) {
        this.$store.dispatch('form/updateElement', {
          index: this.selectedFormElementToEdit,
          data: val
        })
      },
      deep: true
    }
  },
  methods: {
    moveElementUp (index) {
      this.$store.dispatch('form/moveElementUp', index)
    },
    moveElementDown (index) {
      this.$store.dispatch('form/moveElementDown', index)
    },
    deleteElement (index) {
      this.$store.dispatch('form/deleteElement', index)
    },
    setListId (item) {
      this.configData.custom_list_id = item.id
      this.$store.dispatch('form/changePreventElementSelection', false)
    }
  }
}
</script>

<style lang="scss" scoped >
.form-config {
  &-element-type {
    padding: 10px;
    font-size: 14px;
    font-weight: 400;
  }
  &--element {
    border-top: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    padding: 10px;
    margin-bottom: 10px;
    & .input-group {
      margin-bottom: 10px;
      font-size: 12px;
      & label {
        margin-bottom: 5px;
        font-weight: 500;
        width: auto;
      }
      & input {
        width: 100%;
        padding: 5px;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-weight: 500;
        &[type="checkbox"] {
          width: auto;
          margin: 0;
          margin-left: 10px;
          &:focus {
            outline: none !important;
            border: none !important;
          }
        }
      }
    }
  }
  &--elements {
    padding: 10px;
    h3 {
      margin-top: 0;
      font-size: 14px;
      font-weight: 500;
    }
    &-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 6px;
      background: rgba(var(--brand-rgb), 0.4);
      margin-top: 10px;
      &:last-child {
        border-bottom: none;
      }
      &-name {
        font-size: 12px;
        font-weight: 500;
        flex-grow: 1;
      }
      & img {
        cursor: pointer;
        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
          pointer-events: none;
        }
      }
      &.selected {
        border: 1px solid rgba(var(--brand-rgb), 1);
      }
    }
  }
}
</style>
