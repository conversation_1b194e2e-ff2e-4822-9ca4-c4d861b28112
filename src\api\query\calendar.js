import { GQL } from '../graphQl'

export const GetCalendarListQuery = () => GQL`query GetCalendarListQuery($isCollaborator: Boolean!, $conditions: core_project_calendar_bool_exp!) {
  core_project_calendar(where: $conditions) {
    id
    name
    description
    created_by
    created_at
    updated_by
    updated_at
    core_project {
      id
      name
    }
    working_hours
    working_hours_end
    working_hours_start
    calendar_holidays(order_by: {date: asc}) {
      date
      id @skip(if: $isCollaborator)
      name
    }
    calendar_working_days(order_by: {id: asc}) {
      id
      work_day
      day_name
      deleted
    }
  }
}`

export const GetCurrentCalendarListQuery = () => GQL`query GetCurrentCalendarListQuery ($isCollaborator: Boolean!){
  core_project_calendar {
    id
    name
    description
    created_by
    created_at
    updated_by
    updated_at
    core_project {
      id
      name
    }
    working_hours
    working_hours_end
    working_hours_start
    calendar_holidays(order_by: {date: asc}) {
      date
      id @skip(if: $isCollaborator)
      name
    }
    calendar_working_days(order_by: {id: asc}) {
      id
      work_day
      day_name
      deleted
    }
  }
}
`

export const CalendarExistCheck = () => GQL`query CheckCalendar($calendar: String!) {
    core_project_calendar(where: {name: {_eq: $calendar}}) {
      id
    }
}`

export const CalendarHolidayExists = () => GQL`query CheckCalendarHoliday($id: Int){
  core_project_calendar(where: {id: {_eq: $id}}) {
    # holidays
  }
}`

export const AddCalendar = () => GQL`mutation createCalendar($data: core_project_calendar_insert_input!){
  insert_core_project_calendar_one(object: $data) {
  id
  }
}`

export const UpdateHolidayQuery = () => GQL`mutation UpdateHolidays($data: calendar_holidays_set_input, $id: Int!) {
  update_calendar_holidays_by_pk(pk_columns: {id: $id}, _set: $data) {
    id
  }
}`

export const UpdateWorkingDaysQuery = () => GQL`mutation UpdateWorkingDays($data: [calendar_working_days_updates!]!) {
  update_calendar_working_days_many(updates: $data) {
    affected_rows
  }
}`

export const UpdateCalendar = () => GQL`mutation editCalendar(
  $name: String,
  $description: String,
  $working_hours: Float,
  $working_hours_start: time,
  $working_hours_end: time,
  $id: Int!
  ) {
  update_core_project_calendar_by_pk(pk_columns: {id: $id}, 
  _set: {
    name: $name,
    description: $description,
    working_hours: $working_hours,
    working_hours_start: $working_hours_start,
    working_hours_end: $working_hours_end
  }
  ) {
    id
  }
}`

export const updateCalendarWorkDays = () => GQL`mutation AddHolidays($data: Boolean, $id: Int!) {
  update_calendar_working_days(where: {id: {_eq: $id}}, _set: {deleted: $data}) {
    affected_rows
  }
}`

export const AddHolidaysInCalendar = () => GQL`mutation AddHolidays($data: calendar_holidays_insert_input!) {
  insert_calendar_holidays_one(object: $data) {
    name
  }
}`

//   export const DeleteHolidayInCalendar = () => GQL`mutation deleteCalendar($data: )`

export const DeleteCalendarById = () => GQL`mutation AddHolidays($id: Int!) {
  delete_calendar_holidays_by_pk(id: $id) {
    id
  }
}`
export const GetCalendarBasedOnProjectIdQuery = () => GQL`query GetCalendarBasedOnProjectIdQuery ($projectId: uuid){
  core_project_calendar(where: {project_id: {_eq: $projectId}}) {
    # holidays
    id
    name
    project_id
    tenant_id
    # work_time_days
    # work_time_hours
  }
}`

export const GetCalendarDataByProjectIdQuery = () => GQL`query GetCalendarDataByProjectIdQuery ($project_id: uuid!){
  core_project_calendar (where: {project_id: {_eq: $project_id}}) {
    id
    name
    description
    created_by
    created_at
    updated_by
    updated_at
    core_project {
      id
      name
    }
    working_hours
    working_hours_end
    working_hours_start
    calendar_holidays(order_by: {date: asc}) {
      date
      
    }
    calendar_working_days(order_by: {id: asc}) {
      id
      work_day
      day_name
      deleted
    }
  }
}`
