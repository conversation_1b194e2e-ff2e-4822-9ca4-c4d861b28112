import './loader.scss'

const loader = document.createElement('div')

loader.innerHTML = `
<div class="three-quarter-spinner"></div>
`
loader.classList.add('asw-loader')

class Loader {
  constructor (selector) {
    this.parent = (selector && document.querySelector(selector)) || document.body
    this.element = loader.cloneNode(true)
  }

  show () {
    this.parent.appendChild(this.element)
  }

  hide () {
    this.element.remove()
  }
}

export default Loader
