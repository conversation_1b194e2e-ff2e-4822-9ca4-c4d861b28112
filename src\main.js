import Vue from 'vue'
import App from './App.vue'
import { router } from './router'
import store from './store'
import '@/assets/scss/index.scss'
import clickoutside from '@/plugins/clickoutside'
import tooltip from '@/plugins/tooltip'
import notify from '@/plugins/notification'
import overflowTooltip from '@/directive/overflowTooltip'
import VueApexCharts from 'vue-apexcharts'
import keyboardSelection from '@/plugins/keyboardSelection'
import * as Sentry from '@sentry/vue'

Sentry.init({
  Vue,
  dsn: 'https://<EMAIL>/****************',
  integrations: [
    new Sentry.BrowserTracing({
      // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
      tracePropagationTargets: ['https://dev.beacon-dtx.com', 'https://stg.beacon-dtx.com/', 'https://beacon-dtx.com/'],
      routingInstrumentation: Sentry.vueRouterInstrumentation(router)
    }),
    new Sentry.Replay()
  ],
  // Performance Monitoring
  tracesSampleRate: 1.0, // Capture 100% of the transactions, reduce in production!
  // Session Replay
  replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
  replaysOnErrorSampleRate: 1.0 // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
})

Vue.component('apexchart', VueApexCharts)
Vue.use(notify)
Vue.directive('tooltip', tooltip)
Vue.directive('click-outside', clickoutside)
Vue.directive('overflow-tooltip', overflowTooltip)
Vue.directive('keyboard-selection', keyboardSelection)

Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
