import { GQL } from '../graphQl'
export const GetAllProjects = () => GQL`query GetAllProjects($conditions: core_projects_bool_exp, $orderBy: [core_projects_order_by!]) {
  core_projects(where: $conditions, order_by: $orderBy) {
    name
    active
    created_by
    created_on
    deleted
    id
    associated_tenant {
      id
    }
    location
    #company
    address
    planned_start_date
    planned_end_date
    project_cost
    project_revenue
    tenant_associated_created_by_user {
      associated_user {
        first_name
        last_name
        id
      }
    }
    tenant_associated_updated_by_user {
      associated_user {
        first_name
        last_name
        id
      }
    }
  }
  core_projects_aggregate(where: $conditions) {
    aggregate {
      count
    }
  }
}`

export const CreateProject = () => GQL`mutation CreateProject($data: core_projects_insert_input!) {
  insert_core_projects_one(object: $data) {
    id
  }
}`

export const DeleteProject = () => GQL`mutation DeleteProject($id: uuid!) {
  update_core_projects_by_pk(pk_columns: {id: $id}, _set: {deleted: true}) {
    deleted
    id
    name
  }
}`

export const UpdateProject = () => GQL`mutation UpdateProject($data: core_projects_set_input!, $id: uuid!) {
  update_core_projects_by_pk(_set: $data, pk_columns: {id: $id}) {
    id
  }
}`

export const GetCurrentProject = () => GQL`query GetCurrentProject {
  core_projects {
    name
    #company
    location
    address
    project_cost
    project_revenue
    planned_start_date
    planned_end_date
    project_user_associations {
      user_id
      status
      role_id
      system_added
      associated_user{
        email
        first_name
        last_name
      }
    }
    created_on
    created_by
    active
    id
    deleted
    updated_by
    updated_on
  }

}`

export const AddUserToProject = () => GQL`
mutation AddUserToProject($user_id: uuid!, $role_id: Int!) {
  insert_project_user_association_one(object: {role_id: $role_id, user_id: $user_id}) {
    user_id
    status
    role_id
  }
}`

export const DeleteUserFromProject = () => GQL`mutation DeleteUserFromProject($user_id: uuid!) {
  update_project_user_association(where: {user_id: {_eq: $user_id}}, _set: {status: 3}) {
    affected_rows
  }
}`

export const RestoreUserFormProject = () => GQL`mutation RestoreUserFormProject($user_id: uuid!, $role_id: Int!) {
  update_project_user_association(where: {user_id: {_eq: $user_id}}, _set: {status: 1, role_id: $role_id}) {
    affected_rows
  }
}`

export const UpdateUserRoleForProject = () => GQL`mutation UpdateUserRoleForProject($user_id: uuid!, $role_id: Int!) {
  update_project_user_association(where: {user_id: {_eq: $user_id}}, _set: {role_id: $role_id}) {
    affected_rows
  }
}`

export const GetProjectLevelRole = () => GQL`query GetProjectUserRole {
  project_user_association {
    associated_role {
      name
    }
  }
}
`
export const GetAllProjectsForTsQuery = () => GQL`query GetAllProjectsForTsQuery {
  core_projects {
    active
    name
    id
  }
}`
export const GetAssociatedUsersListQuery = () => GQL`query GetAssociatedUsersListQuery($projectId: uuid!) {
  project_user_association(where: {associated_project: {id: {_eq: $projectId}}}) {
    associated_user {
      first_name
      last_name
      id
    }
  }
}`
export const GetUserListByPojIdsQuery = () => GQL`query GetUserListByPojIdsQuery($conditions: project_user_association_bool_exp) {
  project_user_association(where: $conditions) {
    associated_project {
      id
    }
    associated_user {
      first_name
      last_name
      email
      id
    }
  }
}`
export const tenantUserAssociation = () => GQL`query tenantUserAssociation($id: uuid!) {
  tenant_user_association(where: {tenant_id: {_eq: $id}, status: {_eq: 1}}) {
    user_id
    tenant_id
    associated_user {
      id
      first_name
      last_name
      email
    }
  }
}
`
