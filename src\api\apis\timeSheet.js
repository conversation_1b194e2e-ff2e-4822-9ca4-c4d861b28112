import config from '../../config'
import {
  runMutation,
  runQuery
} from '../graphQl'
import http from '../http'
import store from '../../store'
import * as timeSheetQuery from '../query/timeSheet'

export const timeSheetDataBasedOnWeek = (startingDate, endingDate, userId) => {
  return runQuery(timeSheetQuery.timeSheetDataBasedOnWeek(), {
    userId,
    startingDate,
    endingDate
  }, 'tenant')
}

export const updateTimeSheetData = (data) => {
  return runQuery(timeSheetQuery.updateTimeSheetDataQuery(), data, 'tenant')
}

export const insertTimeSheetData = (inputsheetData) => {
  return runQuery(timeSheetQuery.insertTimeSheetDataQuery(), {
    inputsheetData: inputsheetData
  }, 'tenant')
}

export const getTaskDataFromProject = (projectId, userId, token) => {
  if (store.getters.collaborator) {
    return runQuery(timeSheetQuery.getTaskDataFromProjectQuery(), {
      projectId,
      userId
    }, 'tenant')
  } else {
    return runQuery(timeSheetQuery.getTaskDataFromProjectQuery(), {
      projectId,
      userId
    }, 'current', token)
  }
}

export const getlastweekUsedProjectIds = (startDate, endDate, userId) => {
  return runQuery(timeSheetQuery.getlastweekUsedProjectIdsQuery(), {
    startDate,
    endDate,
    userId
  }, 'tenant')
}

export const removeTimeSheetEntries = (removedEntries) => {
  // here removedEntries are comming as array of ids
  return runMutation(timeSheetQuery.removeTimeSheetEntriesMutation(), {
    removedEntries
  }, 'tenant')
}

export const GetReportsData = (filters) => {
  const body = {}
  if (filters.from) {
    body.startDate = filters.from
  }
  if (filters.upto) {
    body.endDate = filters.upto
  }
  if (filters.userIds) {
    body.userIds = filters.userIds
  }
  if (filters.projectIds) {
    body.projectIds = filters.projectIds
  }
  if (filters.tags) {
    body.tagIds = filters.tags
  }
  return http.POST(config.serverEndpoint + '/timesheet/reports', body, 'tenant')
}

// export const approveSelectedTimes = (filters) => {
//   return runMutation(timeSheetQuery.approveTimeSheetQuery(), { user_ids: filters.userIds, project_id: filters.projectId, start_date: filters.startDate, end_date: filters.endDate })
// }

export const totalTimeSpentInTask = (taskId) => {
  return runQuery(timeSheetQuery.totalTimeSpentInTaskQuery(), {
    taskId
  }, 'tenant')
}
export const approveTimesheet = (projectId, startDate, endDate, userIds, token) => {
  return runQuery(timeSheetQuery.approveTimesheetQuery(), { projectId, startDate, endDate, userIds }, 'current', token)
}
export const insertMaterialsData = (inputMaterialData) => {
  return runQuery(timeSheetQuery.insertMaterialDataQuery(), {
    inputMaterialData
  }, 'tenant')
}
export const UpdatetimesheetMaterial = async (data) => {
  return runMutation(timeSheetQuery.UpdateMaterialQuery(), data, 'tenant')
}
export const DeleteTimesheetMaterial = async (data) => {
  return runMutation(timeSheetQuery.DeleteMaterialQuery(), data, 'tenant')
}
