import * as productTree from './bomImpl'
import {
  GetAllProductCode,
  GetAllBomList
} from '@/api'
export default {
  namespaced: true,
  state: {
    isLoadingProductTree: false,
    selectedProductCode: null,
    productCodeTree: {
      items: {},
      tree: []
    },
    selectedBomId: null,
    bomList: [],
    obsoleteProductCodes: {},
    bomProdSearchKeyword: ''
  },
  getters: {
    isLoadingProductTree: state => state.isLoadingProductTree,
    selectedProductCode: state => state.selectedProductCode,
    productCodeTree: state => state.productCodeTree,

    selectedBomId: state => state.selectedBomId,
    bomList: state => state.bomList,
    obsoleteProductCodes: state => state.obsoleteProductCodes,
    bomProdSearchKeyword: state => state.bomProdSearchKeyword
  },
  mutations: {
    SET_LOADING_PRODUCT_TREE (state, payload) {
      state.isLoadingProductTree = payload
    },
    SET_SELECTED_PRODUCT_CODE (state, payload) {
      state.selectedProductCode = payload.product_code || null
    },
    SET_PRODUCT_CODE_TREE (state, payload) {
      state.productCodeTree = payload
    },
    SET_SELECTED_BOM_ID (state, payload) {
      state.selectedBomId = payload.bom_id || null
    },
    SET_BOM_LIST (state, payload) {
      state.bomList = payload
    },
    SET_OBSOLETE_PRODUCT_STATUS (state, payload) {
      state.obsoleteProductCodes = payload
    },
    SET_PROD_BOM_SEARCH_KEYWORD (state, payload) {
      state.bomProdSearchKeyword = payload
    }
  },
  actions: {
    setBaseData ({ commit }, payload) {
      commit('SET_SELECTED_PRODUCT_CODE', payload)
      commit('SET_SELECTED_BOM_ID', payload)
    },
    getAllBomList ({ commit, state }, cb) {
      GetAllBomList(state.selectedProductCode).then((response) => {
        commit('SET_BOM_LIST', response.core_bom)
        cb && cb()
      }).catch((error) => {
        console.log(error)
        cb && cb()
      })
    },
    async getAllProductCode ({ commit, dispatch }, payload) {
      commit('SET_LOADING_PRODUCT_TREE', true)
      try {
        const response = await GetAllProductCode(payload?.searchType, payload?.searchKeyword)
        const obsoleteProductCodes = {}
        const productCodes = (response.product_code || []).map((item) => {
          if (item?.linked_material?.status === 2) {
            obsoleteProductCodes[item.id] = 'inactive'
          }
          return {
            product_code_name: item.product_code,
            product_code: item.id,
            active: item?.linked_material?.status !== 2,
            open: false
          }
        })
        dispatch('initProductTree', productCodes)
        commit('SET_OBSOLETE_PRODUCT_STATUS', obsoleteProductCodes)
      } catch (error) {
        console.log(error)
      }
      commit('SET_LOADING_PRODUCT_TREE', false)
    },
    initProductTree ({ commit }, productCodeList) {
      const productTreeData = productTree.initProductTree(productCodeList)
      commit('SET_PRODUCT_CODE_TREE', productTreeData)
    },
    gotoProduct ({ state, commit }, payload) {
      const [productTreeData, node] = productTree.goto(payload, state.productCodeTree)
      commit('SET_SELECTED_PRODUCT_CODE', node)
      commit('SET_PRODUCT_CODE_TREE', productTreeData)
    },
    expandProduct ({ state, commit }, payload) {
      const productTreeData = productTree.expand(payload, state.productCodeTree, (productTree) => {
        commit('SET_PRODUCT_CODE_TREE', productTree)
      })
      commit('SET_PRODUCT_CODE_TREE', productTreeData)
    },
    bomVersionIdChanged ({ commit, state }) {
      const productTreeData = productTree.bomVersionIdChanged(state.productCodeTree)
      commit('SET_PRODUCT_CODE_TREE', productTreeData)
    },
    addProdBomSearchKeyword ({ commit }, payload) {
      commit('SET_PROD_BOM_SEARCH_KEYWORD', payload)
    }
  }
}
