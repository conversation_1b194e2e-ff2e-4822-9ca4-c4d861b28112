// this is to create project token map for smooth flow in  time line scheduler
export default {
  namespaced: true,
  state: {
    projectTokensMap: {},
    calenderHashMap: {}
  },
  getters: {
    getProjectTokens: state => state.projectTokensMap,
    getProjectTokenById: projectId => state => state.projectTokensMap[projectId],
    getCalederHashMap: state => state.calenderHashMap
  },
  mutations: {
    addNewToken (state, { token, projectId }) {
      state.projectTokensMap[projectId] = token
    },
    clearTokenHashMap (state, action) {
      state.projectTokensMap = {}
    },
    addCalenderHashMap (state, { calenderData, projectId }) {
      state.calenderHashMap[projectId] = calenderData
    },
    clearCalenderHashMap (state, action) {
      state.getCalederHashMap = {}
    }
  }
}
