<template>
  <div>
    <div
      :class="{
        'form-location': true,
        'form-input--required': data.required,
      }"
    >
      <label class="form-location--label">{{ data.caption }}:
        <img
      v-if="data.visibility"
      class="mx-1"
      width="15"
      src="~@/assets/images/eye.svg"
      alt="view"
      />
      </label>
    </div>
    <div class="form-location--grid-2">
      <div
        :class="{
          'form-input': true,
        }"
      >
        <label><small>Longitude:</small></label>
        <input v-model.number="longitude" :disabled="viewOnly" type="number" />
      </div>
      <div
        :class="{
          'form-input': true,
        }"
      >
        <label> <small>Latitude:</small></label>
        <input :disabled="viewOnly" v-model.number="latitude" type="number" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'textComponent',
  props: {
    mode: {
      type: String,
      default: 'TEMPLATE'
    },
    data: {
      type: Object,
      default: () => {}
    },
    value: {
      type: Number,
      default: 0
    },
    viewOnly: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      componentValue: '',
      longitude: '',
      latitude: ''
    }
  },
  watch: {
    value (val) {
      this.componentValue = +val
    },
    longitude () {
      this.componentValue = `(${this.longitude}, ${this.latitude})`
      this.emitChange()
    },
    latitude (val) {
      this.componentValue = `(${this.longitude}, ${this.latitude})`
      this.emitChange()
    }
  },
  created () {
    if (this.value) {
      this.componentValue = this.value || ''
      // get the longitude and latitude from the value
      const [longitude, latitude] = this.value
        .replace('(', '')
        .replace(')', '')
        .split(',')
      this.longitude = longitude
      this.latitude = latitude
    }
  },
  methods: {
    emitChange () {
      this.$emit('customChange', {
        field_id: this.$props.data.field_id,
        value: this.componentValue
      })
    }
  }
}
</script>

<style lang="scss" scoped >
.form-location {
  font-size: 12px;
  &--label {
    margin-bottom: 0.5rem;
    font-size: 1.3em;
  }
  &--grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 1rem;
  }
}
</style>
