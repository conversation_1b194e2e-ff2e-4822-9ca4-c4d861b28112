<template>
    <div class="insights fh">
    <div class="insights-bar v-center space-between">
      <h1 class="weight-500 xxxl pl-3">Insights</h1>
      <span class="mx-3" v-if="!collaborator">
        <router-link
          :to="toCumulative"
          >
          <button
            :class="{'toggle toggle-left pointer' :true ,'toggle-selected':(currentPath==='product' || currentPath==='project')}"
          >
            Cumulative
          </button>
        </router-link>
          <router-link
          :to="toPersonal"
          >
            <button
            :class="{'toggle toggle-left pointer' :true ,'toggle-selected':(currentPath==='personal' )}"

          >
            Personal
          </button>
          </router-link>
        </span>
    </div>
    <router-view/>
    </div>
</template>
<script>
import { mapGetters } from 'vuex'
export default ({
  data () {
    return { currentPath: '' }
  },
  computed: {
    ...mapGetters(['isOnProjectLevel', 'collaborator']),
    toPersonal () {
      if (this.isOnProjectLevel) {
        return '/insights/project/personal'
      }
      return '/insights/product/personal'
    },
    toCumulative () {
      if (this.isOnProjectLevel) {
        return '/insights/project'
      }
      return '/insights/product'
    }
  },
  mounted () {
    const segments = this.$route.path.split('/')
    this.currentPath = segments[segments.length - 1] || '/'
  },
  watch: {
    '$route.path' () {
      const segments = this.$route.path.split('/')
      this.currentPath = segments[segments.length - 1] || '/'
    }
  }

})
</script>
<style lang="scss" scoped>
.insights {
  &-bar {
    height: 60px;
    margin: -12px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  }
  .toggle {
  font-size: 1em;
  padding: 0.5em 1.2em;
  color: var(--black);
  font-weight: 500;
  border: none;
  box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
  background-color: var(--side-bar-color);

  &:active {
    box-shadow: inset 0.2em 0.2em 0.2em rgba(0, 0, 0, 0.25);
  }

  &-left {
    border-radius: 0.3rem 0px 0px 0.3rem;
  }

  &-right {
    border-radius: 0px 0.3rem 0.3rem 0px;
  }

  &-selected {
    background-color: var(--brand-color);
  }
}
</style>
