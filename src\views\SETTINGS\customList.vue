<template>
  <div class="tenant-custom-list">
    <div v-if="loading" class="center loader">
      <loading-circle />
    </div>
    <div class="custom-list-data" v-else>
      <div class="custom-list-data-bar v-center space-between p-3">
        <h3 class="weight-500 xl">Custom List</h3>
      </div>
      <div class="custom-list-data-container mt-2">
        <list-items
          :list="customLists"
          :selected="selectedList"
          @update="getCustomList"
          @select="getListDetails"
        />
        <div>
          <copy-list-detail
          :selected="selectedList"
          @update="getListDetails"/>
        </div>
        <!-- <div v-else>
        <list-detail
          :selected="selectedList"
          @update="getListDetails"
        />
      </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import ConfirmationDialog from '@/plugins/confirmationDialog'
import LoadingCircle from '@/components/common/loadingCircle.vue'
import { success, alert } from '@/plugins/notification'
import Loader from '@/plugins/loader'
import { GetActiveCustomLists, GetCustomList, DeleteCustomList } from '@/api'
import ListItems from '../../components/settings/customLists/listItems.vue'
// import ListDetail from '../../components/settings/customLists/listDetail.vue'
import copyListDetail from '@/components/settings/customLists/copyListDetail.vue'
export default {
  components: {
    LoadingCircle,
    ListItems,
    // ListDetail,
    copyListDetail
  },
  data () {
    return {
      loading: false,
      customLists: [],
      selectedList: {
        id: null,
        index: null,
        loading: false,
        data: null
      }
    }
  },
  computed: {
    isCopyList () {
      return (
        this.$route.fullPath.includes('copy-custom-list')
      )
    }
  },
  methods: {
    getCustomList () {
      this.selectedList.id = null
      this.selectedList.index = null
      this.selectedList.data = null
      this.loading = true
      GetActiveCustomLists()
        .then((res) => {
          this.customLists = res.core_custom_list
        })
        .finally(() => {
          this.loading = false
        })
    },
    getListDetails ({ id, index }) {
      if (!id) {
        this.selectedList.id = null
        this.selectedList.data = null
        return
      }
      this.selectedList.id = id
      this.selectedList.index = index ?? null
      this.selectedList.loading = true
      GetCustomList(id)
        .then((res) => {
          this.selectedList.data = res.core_custom_list_by_pk
        })
        .finally(() => {
          this.selectedList.loading = false
        })
    },
    handleListCreated () {
      this.getCustomList()
    },
    handleListEdited () {
      this.getCustomList()
    },
    deleteSelectedList () {
      ConfirmationDialog(
        `Are you sure you want to delete the list titled ${
          this.customLists[this.selectedList.index].name
        }`,
        (res) => {
          if (res) {
            const loader = new Loader()
            loader.show()
            DeleteCustomList(this.customLists[this.selectedList.index].id)
              .then((res) => {
                success('Deleted the selected list')
                this.customLists.splice(this.selectedList.index, 1)
                this.selectedListDetails = null
                this.selectedList.id = null
                this.selectedList.index = null
              })
              .catch((err) => {
                alert(err[0]?.message ?? 'Something went wrong')
              })
              .finally(() => {
                loader.hide()
              })
          }
        }
      )
    }
  },
  mounted () {
    this.getCustomList()
  }
}
</script>

<style scoped lang="scss">
.tenant-custom-list {
  width: 100%;
  height: 100%;
}
.custom-list-data {
  height: 100%;
  &-bar {
    width: 100%;
    height: 40px;
    padding: 16px;
    margin-top: -12px;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  &-container {
    width: 100%;
    height: calc(100% - 50px);
    display: grid;
    grid-template-columns: 1fr 2.5fr;
    grid-gap: 10px;
  }
}
</style>
