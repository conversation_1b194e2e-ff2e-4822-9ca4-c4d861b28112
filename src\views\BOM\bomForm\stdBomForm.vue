<template>
    <div>
        <div  class="mt-4">
            <h3>
                    {{ formTemplateData.name }}
                </h3>
        </div>
                    <!-- if its standard BOM form need to show material data -->
                    <div class="fh center" v-if="loading">
                      <loading-circle />
                    </div>
                    <div  class="input-group mt-3">
                        <template v-for="(ele, index) in templateField">
                        <component
      v-if="!ele.autogenerated"
      :ref="ele.field_id"
      :key="index"
      :is="ele.form_field.key + '_COMPONENT'"
      :mode="!formValueData ? 'CREATE' : 'EDIT'"
      :data="ele"
      :value="!formValueData ? null : formValueData[ele.field_id]"
      @customChange= "handleInputChange"
    />
                    </template>
                </div>
</div>
</template>

<script>
import { getDetailFormTemplate, GetStandardBomFormTemplate, getGivenCustomListsData } from '@/api'
import { GetTenantConfigTypes } from '@/api/apis/tenantConfigFeature'
import timeComponent from '@/components/form/elements/timeComponent.vue'
import configurationList from '@/components/form/elements/configurationList.vue'
import fileComponent from '@/components/form/elements/fileComponent.vue'
import dateComponent from '@/components/form/elements/dateComponent.vue'
import longTextComponent from '@/components/form/elements/longTextComponent.vue'
import numberComponent from '@/components/form/elements/numberComponent.vue'
import switchComponent from '@/components/form/elements/switchComponent.vue'
import textComponent from '@/components/form/elements/textComponent.vue'
import companyComponent from '@/components/form/elements/companyComponent.vue'
import userComponent from '@/components/form/elements/userComponent.vue'
import multiUserComponent from '@/components/form/elements/multiUserComponent.vue'
import materialComponent from '@/components/form/elements/materialComponent.vue'
import locationComponent from '@/components/form/elements/locationComponent.vue'
import productCodeComponent from '@/components/form/elements/productCodeComponent.vue'
import LoadingCircle from '../../../components/common/loadingCircle.vue'
import { mapGetters } from 'vuex'
import config from '@/config'
import { alert } from '@/plugins/notification'
export default {
  name: 'std-Bom-Form',
  components: {
    BOOLEAN_COMPONENT: switchComponent,
    NUMBER_COMPONENT: numberComponent,
    TEXT_COMPONENT: textComponent,
    LONG_TEXT_COMPONENT: longTextComponent,
    DATE_COMPONENT: dateComponent,
    TIME_COMPONENT: timeComponent,
    CONFIGURATION_LIST_COMPONENT: configurationList,
    ATTACHMENT_COMPONENT: fileComponent,
    COMPANY_COMPONENT: companyComponent,
    USER_COMPONENT: userComponent,
    MULTI_USER_COMPONENT: multiUserComponent,
    MATERIALS_COMPONENT: materialComponent,
    LOCATION_COMPONENT: locationComponent,
    PRODUCT_CODE_COMPONENT: productCodeComponent,
    LoadingCircle
  },
  props: {
    stdFormValues: {
      default: null
    },
    createForm: {
      type: Boolean,
      default: false
    },
    stdBomVersionId: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      validationErrors: [],
      templateId: '',
      formTemplateData: {},
      loading: true,
      formValueData: null,
      config: config,
      formValue: {} // Holds the updated form values
    }
  },
  computed: {
    ...mapGetters(['tenantDefaultsData']),
    templateField () {
      if (!this.formTemplateData.template_versions) return []
      const activeVersion =
        this.formTemplateData.template_versions.find((item) => item.active) ||
        {}
      return activeVersion.template_fields || []
    }
  },
  methods: {
    validateInputData () {
      const form = this.$refs
      this.validationErrors = []
      Object.keys(form).forEach((key) => {
        const formInput = form[key][0]?.data
        if (formInput?.form_field?.key === 'USER') {
          if (formInput.required && (!form[formInput.field_id][0].componentValue || !form[formInput.field_id][0].componentValue.length)) {
            this.validationErrors.push(`${formInput.caption} is a mandatory field`)
          }
        }
        if (formInput?.form_field?.key === 'BOOLEAN') {
        // we are comparing the value to null instead of checking if it is a falsy value cause the boolean component value will false in one case and it is valid value
          if (formInput.required && form[formInput.field_id][0].componentValue === null) {
            this.validationErrors.push(`${formInput.caption} is a mandatory field`)
          }
          return
        } else if (formInput?.form_field?.key === 'MATERIALS') {
          if (formInput.required && form[formInput.field_id][0].componentValue.length < 1) {
            this.validationErrors.push(`${formInput.caption} is a mandatory field`)
          }
          return
        }
        if (formInput.required && !form[formInput.field_id][0].componentValue) {
          this.validationErrors.push(`${formInput.caption} is a mandatory field`)
        }
      })
      // Emit errors if any
      if (this.validationErrors.length > 0) {
        this.$emit('validationErrors', this.validationErrors) // Ensure this is being emitted
      }
      return this.validationErrors
    },
    getFormTemplateData () {
      this.loading = true
      getDetailFormTemplate(this.templateId)
        .then((res) => {
          this.formTemplateData = res.core_form_templates_by_pk
          this.loading = false
        })
        .catch((err) => {
          console.error('Error fetching template data', err)
          this.loading = false
        })
    },
    async getGivenCustomList (ids) {
      const customListData = await getGivenCustomListsData(ids)
      const customListMap = {}
      for (const customList of customListData.core_custom_list) {
        customListMap[customList.id] = customList.custom_list_values
      }
      this.$store.dispatch('form/saveCustomListMap', customListMap)
    },
    handleInputChange () {
      const form = this.$refs
      const formValue = {}
      Object.keys(form).forEach((key) => {
        if (form[key].length && form[key][0]?.data?.autogenerated === false) {
          formValue[key] = form[key][0].componentValue
        }
      })
      const inputPayload = this.createForm ? [] : {}
      Object.keys(formValue).forEach((key) => {
        if (formValue[key] !== undefined && formValue[key] !== null) {
          if (formValue[key] !== 0 && formValue[key].length !== 0) {
            if (this.createForm) {
              inputPayload.push({
                field_id: key,
                value: formValue[key]
              })
            } else {
              inputPayload[key] = formValue[key]
            }
          }
        }
      })
      this.$emit('stdBomForm', inputPayload)
    },
    getBomFormTemaplate () {
      if (this.tenantDefaultsData.bom_temp_id) {
        this.getStandardBomFormTemplate(this.tenantDefaultsData.bom_temp_id)
      } else {
        GetTenantConfigTypes().then((res) => {
          const tenantDefaults = res.tenant_defaults?.[0]
          this.$store.commit('setTenentDefaults', tenantDefaults)
          this.getStandardBomFormTemplate(tenantDefaults?.tenant_feature_configuration?.BOM?.FORM_TEMPLATE_DEFAULT)
        })
      }
    },
    getStandardBomFormTemplate (tempId) {
      GetStandardBomFormTemplate(tempId, this.stdBomVersionId).then(async (res) => {
        this.templateId = res.core_form_templates[0].id
        this.formTemplateData = res.core_form_templates[0]
        const customListData = this.findUsedCustomListIds(res.core_form_templates[0].template_versions)
        if (customListData.size > 0) {
          await this.getGivenCustomList([...customListData])
        }
        this.formValueData = this.stdFormValues
      }).catch(() => {
        alert('Failed to fetch standard BOM form')
      }).finally(() => {
        this.loading = false
      })
    },
    findUsedCustomListIds (templateVersions) {
      const configIdSet = new Set()
      for (const templateVersion of templateVersions) {
        for (const field of templateVersion.template_fields) {
          if (field.field_type_id === config.FORM_TYPE.CONFIGRATION_LIST) {
            configIdSet.add(field.custom_list_id)
          }
        }
      }
      return configIdSet
    }
  },
  mounted () {
    this.loading = true
    this.getBomFormTemaplate()
  },
  watch: {
    stdFormValues () {
      this.formValueData = this.stdFormValues
    },
    stdBomVersionId () {
      this.getStandardBomFormTemplate(this.tenantDefaultsData.bom_temp_id)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
