import { alert } from '@/plugins/notification'
import errorBorder from './errorBorder'
import { isValidLatitude, isValidLongitude } from '@/utils/validations'
export const emptyString = (str, field, dataValidation, showAlert = true) => {
  if (str === '' || str === null || str === undefined) {
    showAlert && alert(`${field} is required`)
    errorBorder(dataValidation, true)
    return false
  }
  errorBorder(dataValidation, false)
  return true
}

export const length = (str, field, min, max, dataValidation) => {
  if (str.length < min || str.length > max) {
    if (max === undefined) {
      alert(`${field} must be greater than or equal to ${min}`)
    } else if (min === undefined) {
      alert(` must be less than or equal to ${max}`)
    } else if (min === max) {
      alert(`${field} must be ${max} characters`)
    } else {
      alert(`${field} must be between ${min} and ${max} characters`)
    }
    errorBorder(dataValidation, true)
    return false
  }
  errorBorder(dataValidation, false)
  return true
}

export const email = (str, field, dataValidation, showAlert = true) => {
  const re = /^(([^<>()\\[\]\\.,;:\s@"]+(\.[^<>()\\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  if (!re.test(str)) {
    showAlert && alert(`${field} must be a valid email address`)
    errorBorder(dataValidation, true)
    return false
  }
  errorBorder(dataValidation, false)
  return true
}

export const noSpecialChars = (str, field, dataValidation, showAlert = true) => {
  const re = /^[a-zA-Z0-9_-\s]+$/
  if (!re.test(str)) {
    showAlert && alert(`${field} must be alphanumeric`)
    errorBorder(dataValidation, true)
    return false
  }
  errorBorder(dataValidation, false)
  return true
}

export const password = (str, field, dataValidation) => {
  const re = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{6,}$/
  if (!re.test(str)) {
    alert(`${field} must be at least 6 characters, contain at least one lowercase letter, one uppercase letter, one number and one special character`)
    errorBorder(dataValidation, true)
    return false
  }
  errorBorder(dataValidation, false)
  return true
}

export const emptylist = (str, field, dataValidation) => {
  if (str.length === null || str.length === 0) {
    alert(`${field} is required`)
    errorBorder(dataValidation, true)
    return false
  }
  errorBorder(dataValidation, false)
  return true
}

export const checkTrue = (boolValue, message, dataValidation, showAlert = true) => {
  if (!boolValue) {
    showAlert && alert(message)
    errorBorder(dataValidation, true)
    return false
  }
  return true
}

export const RegexValidator = (value, regx, message, dataValidation, showAlert = true) => {
  if (!(new RegExp(regx).test(value))) {
    showAlert && alert(message)
    errorBorder(dataValidation, true)
    return false
  }
  errorBorder(dataValidation, false)
  return true
}

export const MatchString = (str1, str2, message, dataValidation) => {
  if (str1 !== str2) {
    alert(message)
    errorBorder(dataValidation, true)
    return false
  }
  errorBorder(dataValidation, false)
  return true
}

export const positiveFloat = (num, field, dataValidation, showAlert = true) => {
  if (typeof num && num > 0) {
    errorBorder(dataValidation, false)
    return true
  }
  showAlert && alert(`${field} must be a positive number`)
  errorBorder(dataValidation, true)
  return false
}
export const isValidLatitudeTenant = (latitude, field, dataValidation, showAlert = true) => {
  const isValid = isValidLatitude(latitude)
  if (!isValid) {
    showAlert && alert(`${field} must be a valid latitude`)
    errorBorder(dataValidation, true)
    return false
  }
  errorBorder(dataValidation, false)
  return true
}

export const isValidLongitudeTenant = (longitude, field, dataValidation, showAlert = true) => {
  const isValid = isValidLongitude(longitude)
  if (!isValid) {
    showAlert && alert(`${field} must be a valid longitude`)
    errorBorder(dataValidation, true)
    return false
  }
  errorBorder(dataValidation, false)
  return true
}
