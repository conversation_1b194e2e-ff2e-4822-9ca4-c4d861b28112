export const arrayToCsv = (array = [], ignoreKey = [], fileName = 'export.csv', heading = null) => {
  const newArray = JSON.parse(JSON.stringify(array))
  newArray.forEach(item => {
    ignoreKey.forEach(key => {
      delete item[key]
    })
  })
  const columnNo = Object.keys(newArray[0]).length
  const emptyArray = new Array(columnNo).fill('')
  const finalArray = []
  if (heading) {
    for (const key in heading) {
      const newArray = new Array(length).fill('')
      newArray[0] = key
      newArray[1] = heading[key]
      finalArray.push(newArray)
    }
    finalArray.push(emptyArray.join(','))
  }
  const headers = Object.keys(newArray[0])
  let csvContent = null
  if (finalArray.length) {
    csvContent = [
      ...finalArray,
      headers.join(','),
      ...newArray.map(row => headers.map(header => row[header]).join(','))
    ].join('\n')
  } else {
    csvContent = [
      headers.join(','),
      ...newArray.map(row => headers.map(header => row[header]).join(','))
    ].join('\n')
  }

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })

  const link = document.createElement('a')
  if (window.navigator.msSaveBlob) {
    window.navigator.msSaveBlob(blob, fileName)
  } else {
    link.href = window.URL.createObjectURL(blob)
    link.download = fileName
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}
