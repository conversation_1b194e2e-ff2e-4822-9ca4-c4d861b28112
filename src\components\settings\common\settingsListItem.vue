<template>
            <div class="tenant-settings-item v-center" @click="$emit('click')"
            :class="{ 'highlight-bg':highLightItem }"
            >
                <div class="tenant-settings-item__name v-center s elipsis-text">
                     <span><slot /></span>
                </div>
                <div class="toggle-button center " v-show="!iconDisabled">
                    <img src="~@/assets/images/down-arrow-icon.svg" alt="" />
                </div>
            </div>
</template>

<script>
export default {
  name: 'settings-list-item',
  props: {
    iconDisabled: {
      type: Boolean,
      default: false
    },
    highLightItem: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped >
    .tenant-settings-item {
        border: 1px solid var(--brand-color);
        background-color: rgba(var(--brand-rgb), 0.2);
        margin: 6px 6px;
        padding-left: 6px;
        cursor: pointer;

        &.highlight-bg{
            background-color: rgba(var(--brand-rgb), 0.4);
        }

        &__name {
            width: calc(100% - 30px);
            height: 30px;
        }

        .selected {
            background-color: rgba(var(--brand-rgb), 0.6);
        }

        .obsolete {
            border-color: var(--alert);
            color: var(--alert)
        }

        & .toggle-button {
            height: 30px;
            width: 30px;
            background-color: rgba(var(--brand-rgb), 1);
            cursor: not-allowed;
            pointer-events: none;
            opacity: 0.5;

            & img {
                height: 16px;
                width: 16px;
                transform: rotate(-90deg);
            }
        }
}</style>
