<template>
    <div class="copy-dtx-table">
    <table >
        <thead v-if="showHeader" >
      <tr class="s">
        <th>Name</th>
        <th>Role</th>
        <th>Contact No</th>
        <th>Email </th>
        <th>Escalate To</th>
        <th v-if="isTenantAdminOrCollaborator">Action</th>
      </tr>
    </thead>
      <tbody>
        <tr :class="{'s':true,'deactivatedRow':row.status===Config.USER_STATUS_MAP.INACTIVE}" v-for="(row, index) in displayData" :key="row.id" >
        <td>{{row.associated_user?.first_name + ' ' + row.associated_user?.last_name}}</td>
        <td  v-if="userEditObject?.editIndex === index && !userEditObject?.editOnlyReportingManager" class="td-user-role-edit">
              <select v-model="userEditObject.userRole">
                <option v-for="(role, key) in roleMap" :key="key" :value="key" >{{ role }}</option>
              </select>
            </td>
            <td v-else class="td-user-role">
              {{ row.associated_role.name }}
           </td>
        <td v-overflow-tooltip>{{row.associated_user?.phone || '--'}}</td>
        <td  class="flex v-center elipsis-text" style="display: inline-block;" v-overflow-tooltip>{{row.associated_user?.email || '--'}}</td>
        <td v-if="userEditObject?.editIndex === index">
          <custom-dropdown :list="formattedUserListForReportingManagers"
          @select="selectReportingManager"
          @toggle="userEditObject.searchTextForReportingManager = ''"
          :showDescription="true"
          @deselect="deselectReportingManager"
          :searchText="userEditObject.searchTextForReportingManager"
          :selected="userEditObject.reportingManager">
            <div slot="before-items" class="p-2 input-group xs">
            <input placeholder="Search Users" type="text" v-model="userEditObject.searchTextForReportingManager" />
            </div>
          </custom-dropdown>
        </td>
        <td v-else>{{row.reporting_manager_user ? row.reporting_manager_user.first_name + row.reporting_manager_user.last_name  : '--'}}</td>
       <td v-if="isTenantAdminOrCollaborator" class="action-column">
        <div class="action-icons">
        <span v-if=" row.associated_user.id!==user.userId"> <!-- checking same logined user and given user id are same -->
        <img v-tooltip="'Re-invite user'" name="reinvite"  v-if="row.status=== Config.USER_STATUS_MAP.INVITED" @click="reInviteTenantUser(row.associated_user?.email)"  src="~@/assets/images/reinvite.png"  height="15px" width="15px" alt=""/>
        <img v-tooltip="'Activate user'"  name="activate" v-if="row.status=== Config.USER_STATUS_MAP.INACTIVE" src="~@/assets/images/icons/undo-icon.svg"   height="20px" alt="" @click="openConfirmModal(row.associated_user,'Activate')"/>
        <span v-else-if="row.status!== Config.USER_STATUS_MAP.INVITED" class="v-center">
              <img name="cancel" v-if="userEditObject.editIndex === index" v-tooltip="'Cancel'"  src="~@/assets/images/icons/close-icon.svg" height="20px"  @click="cancelUpdateUserRole" >
              <img name="save" v-if="userEditObject.editIndex === index" :disabled="disableSaveButton" class="pointer" src="~@/assets/images/icons/save-icon.svg" height="20px" v-tooltip="'Save'"  @click="updateUserRole" >
              <img name="edit"  v-else-if="row.status===Config.USER_STATUS_MAP.ACTIVE" v-tooltip="'Edit User'"  class="pointer" src="~@/assets/images/pencil.svg" alt="" height="20px"  @click="editUserRole(row, index)" >
              <img name="deactivate" v-if="userEditObject.editIndex === index && row.status!==Config.USER_STATUS_MAP.INACTIVE" class="ml-2"  src="~@/assets/images/deactivate_user-icon.svg" v-tooltip="'Deactivate'"  height="14px"  @click="openConfirmModal(row.associated_user,'Deactivate')"  >
            </span>
          </span>
          <span v-else-if="row.associated_user.id===user.userId && row.associated_role.name === 'ADMIN'">
            <img name="cancel" v-show="userEditObject.editIndex === index" v-tooltip="'Cancel'"  src="~@/assets/images/icons/close-icon.svg" height="20px"  @click="cancelUpdateUserRole" >
              <img name="save" v-show="userEditObject.editIndex === index" :disabled="disableSaveButton" class="pointer" src="~@/assets/images/icons/save-icon.svg" height="20px" v-tooltip="'Save'"  @click="updateUserRole" >
              <img name="edit" v-show="userEditObject.editIndex !== index" v-tooltip="'Edit User'"  class="pointer" src="~@/assets/images/pencil.svg" alt="" height="20px"  @click="editUserReportingUser(row, index)">
            </span>
          </div>
             </td>
      </tr>
      </tbody>
      <modal
    @close="isOpen=false"
      :open="isOpen"
      title="Deactivate the user"
      >
      <div class="px-5 py-9">{{ selectedUser?.action }}</div>
      <div class="flex-end ">
        <button  class="btn btn-black mr-2 p-1" @click="isOpen=false">
          Cancel
        </button>
        <button v-if="selectedUser?.actionBtn==='Deactivate'"  class="btn " @click="deactivateUser">{{ selectedUser?.actionBtn }}</button>
        <button v-if="selectedUser?.actionBtn==='Activate'"  class="btn " @click="activateUser">{{ selectedUser?.actionBtn }}</button>
      </div>
    </modal>
    </table>
  </div>
  </template>

<script>
import { mapGetters } from 'vuex'
import Modal from '../../components/common/modal.vue'
import { ReInviteUser, UpdateUserRoleForTenantUser } from '@/api'
import { alert, success } from '@/plugins/notification'
import Config from '../../config'
import { GetAllUsersList } from '../../api/apis/userFlow'
import CustomDropdown from './customDropdown.vue'
export default {
  components: {
    Modal,
    CustomDropdown
  },
  name: 'user-table',
  data () {
    return {
      disableSaveButton: false,
      Config,
      isOpen: false,
      roleMap: Config.userRole,
      selectedUser: '',
      userEditObject: {
        editIndex: -1,
        userId: '',
        userRole: '',
        reportingManager: null,
        searchTextForReportingManager: '',
        editOnlyReportingManager: false
      },
      formattedUserListForReportingManagers: []
    }
  },
  props: {
    userList: {
      type: Array,
      default: () => ([])
    },
    pageNumber: {
      type: [Number, String],
      default: 1
    },
    perPage: {
      type: [Number, String],
      default: 10
    },
    showHeader: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    pageNumber () {
      this.resetEditObject()
    },
    userList () {
      this.resetEditObject()
    }
  },
  mounted () {
    for (const user of this.userList) {
      this.formattedUserListForReportingManagers.push({
        id: user.associated_user?.id,
        name: user.associated_user?.first_name + ' ' + user.associated_user?.last_name,
        description: user.associated_user?.email
      })
    }
  },
  methods: {
    resetEditObject () {
      this.userEditObject = {
        editIndex: -1,
        userId: '',
        userRole: '',
        reportingManager: null,
        searchTextForReportingManager: '',
        editOnlyReportingManager: false,
        disableSaveButton: false,
        disableReInviteButton: false
      }
    },
    reInviteTenantUser (email) {
      if (this.disableReInviteButton) return
      this.disableReInviteButton = true
      const body = {
        email
      }
      ReInviteUser(body).then(data => {
        if (data.message === 'Invitation Resent') {
          success('User Re-invited successfully')
        } else {
          alert('There was an error, re-inviting the user.')
        }
      }).catch(() => {
        alert('There was an error, re-inviting the user. Please try again')
      }).finally(() => {
        this.disableReInviteButton = false
      })
    },
    selectReportingManager (item) {
      this.userEditObject.reportingManager = item
    },
    deselectReportingManager () {
      this.userEditObject.reportingManager = null
    },
    editUserReportingUser (raw, index) {
      this.userEditObject = {
        editIndex: index,
        userId: raw.associated_user?.id,
        userRole: raw?.associated_role?.id,
        reportingManager: raw.reporting_manager_user
          ? { id: raw.reporting_manager_user.id, name: raw.reporting_manager_user.first_name + ' ' + raw.reporting_manager_user.last_name } : null,
        searchTextForReportingManager: '',
        editOnlyReportingManager: true
      }
    },
    editUserRole (raw, index) {
      this.userEditObject = {
        editIndex: index,
        userId: raw.associated_user?.id,
        userRole: raw?.associated_role?.id,
        reportingManager: raw.reporting_manager_user
          ? { id: raw.reporting_manager_user.id, name: raw.reporting_manager_user.first_name + ' ' + raw.reporting_manager_user.last_name } : null,
        searchTextForReportingManager: '',
        editOnlyReportingManager: false
      }
    },
    cancelUpdateUserRole () {
      this.resetEditObject()
    },
    updateUserRole () {
      if (this.disableSaveButton) return
      this.disableSaveButton = true
      const { userId, userRole, reportingManager } = this.userEditObject
      const payload = {
        user_id: userId,
        role_id: parseInt(userRole),
        status: undefined,
        reportingManager: reportingManager ? reportingManager.id : null
      }
      if (this.userEditObject.editOnlyReportingManager) {
        payload.role_id = undefined
      }
      UpdateUserRoleForTenantUser(payload)
        .then((res) => {
          this.getAllTenantsListData()
          if (this.userEditObject.editOnlyReportingManager) {
            this.$notify.success('Reporting manager updated successfully')
          } else {
            this.$notify.success('User role updated successfully')
          }
          this.resetEditObject()
        })
        .catch(() => {
          this.$notify.alert('Something went wrong')
        }).finally(() => {
          this.disableSaveButton = false
        })
    },
    getAllTenantsListData () {
      const tenantId = localStorage.getItem('tenantId')
      GetAllUsersList(tenantId).then((res) => {
        this.$store.commit('setTenantUserList', res.tenant_user_association)
      })
    },
    openConfirmModal (user, action) {
      this.selectedUser = user
      if (action === 'Activate') {
        this.selectedUser.actionBtn = 'Activate'
        this.selectedUser.action = `Are sure to activate the ${ user?.first_name }   ${ user.last_name }`
      } else {
        this.selectedUser.action = `Are sure to deactivate the ${user?.first_name }   ${ user.last_name }`
        this.selectedUser.actionBtn = 'Deactivate'
      }
      this.isOpen = true
    },
    deactivateUser () {
      UpdateUserRoleForTenantUser({
        user_id: this.selectedUser.id,
        role_id: undefined,
        status: Config.USER_STATUS_MAP.INACTIVE // for deactivate
      })
        .then((res) => {
          this.getAllTenantsListData()
          this.$notify.success('Deactivated successfully')
          this.resetEditObject()
          this.isOpen = false
        })
        .catch((err) => {
          this.$notify.alert(err?.message ?? 'Something went wrong')
        })
    },
    activateUser () {
      UpdateUserRoleForTenantUser({
        user_id: this.selectedUser.id,
        role_id: undefined,
        status: Config.USER_STATUS_MAP.ACTIVE // for activate
      })
        .then((res) => {
          this.getAllTenantsListData()
          this.$notify.success('Activated successfully')
          this.resetEditObject()
          this.isOpen = false
        })
        .catch(() => {
          this.$notify.alert('Something went wrong')
        })
    },
    deleteUser () {
      UpdateUserRoleForTenantUser({
        user_id: this.selectedUser.id,
        role_id: undefined,
        status: Config.USER_STATUS_MAP.DELETED // for delete
      })
        .then((res) => {
          this.getAllTenantsListData()
          this.$notify.success('Deleted successfully')
          this.resetEditObject()
          this.isOpen = false
        })
        .catch(() => {
          this.$notify.alert('Something went wrong')
        })
    }
  },
  computed: {
    ...mapGetters(['user', 'getUserById', 'collaborator']),
    displayData () {
      return this.userList.slice((this.pageNumber - 1) * this.perPage, this.pageNumber * this.perPage)
    },
    isTenantAdminOrCollaborator () {
      return (
        this.user.tenantLevelRole === 'ADMIN' && !this.collaborator
      )
    }
  }
}
</script>
  <style lang="scss" scoped>
  .no-data {
    text-align: center;
    vertical-align: middle;
  }
  </style>
