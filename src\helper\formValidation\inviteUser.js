import {
  emptyString,
  noSpecialChars,
  email,
  checkTrue
} from './index'
export default (body) => {
  if (!emptyString(body.firstName, 'First Name', 'FirstName')) {
    return false
  }
  if (!noSpecialChars(body.firstName, 'First Name', 'FirstName')) {
    return false
  }
  if (!emptyString(body.lastName, 'Last Name', 'LastName')) {
    return false
  }
  if (!emptyString(body.email, 'Email', 'Email')) {
    return false
  }
  if (!email(body.email, 'Email', 'Email')) {
    return false
  }
  if (!checkTrue(body.agreeTearms, 'Please agree to the following terms and conditions', 'TearmsAndCondition')) {
    return false
  }
  return true
}
