<template>
  <table class="project-table" :class="{ admin: isTenantAdmin }">
    <thead v-if="showHeader">
      <tr class="m">
        <th></th>
        <th>Project Name</th>
        <th>Location</th>
        <th>Created On</th>
        <th>Created By</th>
        <th>Updated By</th>
        <th>Project address</th>
        <th>Start Date</th>
        <th>End Date</th>
        <th>Revenue</th>
        <th>Latitude & Longitude</th>
        <th v-if="showProjectAction">Action</th>
      </tr>
    </thead>
    <tbody v-if="!loading">
      <tr class="s" v-for="row in displayData" :key="row.id">
        <td>
          <img
            v-if="row.active"
            src="~@/assets/images/active-icon.svg"
            alt=""
          />
          <img v-else src="~@/assets/images/inactive-icon.svg" alt="" />
        </td>
        <td>{{ row.name || "--" }}</td>
        <td>{{ row.location || "--" }}</td>
        <td>{{ row.created_on | timeStampToDateTime }}</td>
        <td>
          {{
            getFullName(row.tenant_associated_created_by_user?.associated_user)
          }}
        </td>
        <td>
          {{
            getFullName(row.tenant_associated_updated_by_user?.associated_user)
          }}
        </td>
        <td> {{ row?.address?.address }} {{ row?.address?.city }}, {{ row?.address?.state }},  {{ row?.address?.pincode }}</td>
        <td>{{ row.planned_start_date }}</td>
        <td>{{ row.planned_end_date}}</td>
        <td>{{ row.project_revenue }}</td>
        <td><a :href="'https://www.google.com/maps?q=' + row.longitude + ',' + row.latitude" target="_blank">Open Maps</a></td>
        <td v-if="showProjectAction">
          <img
            class="pointer"
            src="~@/assets/images/edit-icon.svg"
            alt=""
            title="Edit"
            @click="$emit('edit', row)"
          />
          <img
            class="pointer"
            src="~@/assets/images/delete-icon.svg"
            @click="openDeleteConfirmModal(row)"
            alt=""
            title="Delete"
            v-if="isTenantAdmin"
          />
        </td>
      </tr>
    </tbody>
    <tbody v-else>
      <tr>
        <td colspan="6">
          <div class="center">
            <loading-circle />
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</template>

<script>
import LoadingCircle from './loadingCircle.vue'
import { timeStampToDateTime } from '@/filters/dateFilter'
import { mapGetters } from 'vuex'
import ConfirmationDialog from '@/plugins/confirmationDialog'

export default {
  components: { LoadingCircle },
  filters: {
    timeStampToDateTime
  },
  name: 'project-table',
  props: {
    projectList: {
      type: Array,
      default: () => []
    },
    pageNumber: {
      type: [Number, String],
      default: 1
    },
    perPage: {
      type: [Number, String],
      default: 10
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    externalCollaborator: {
      type: String
    }
  },
  computed: {
    ...mapGetters(['user', 'getUserById']),
    ...mapGetters(['isTenantAdmin']),
    displayData () {
      return this.projectList.slice(
        (this.pageNumber - 1) * this.perPage,
        this.pageNumber * this.perPage
      )
    },
    isTenantAdmin () {
      return (
        this.user.tenantLevelRole === 'ADMIN' && !this.externalCollaborator
      )
    },
    showProjectAction () {
      return (
        this.user.tenantLevelRole === 'ADMIN' && !this.externalCollaborator)
    }
  },
  methods: {
    getFullName (obj) {
      if (obj) {
        return obj?.first_name + ' ' + obj?.last_name
      }
      return '--'
    },
    openDeleteConfirmModal (project) {
      ConfirmationDialog(`Are you sure to delete the project ${project.name}`, (res) => {
        if (res) {
          this.$emit('delete', project)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped >
.project-table {
  width: 100%;
  font-size: 16px;
  border-collapse: collapse;
  position: relative;
  th {
    background: var(--brand-color);
    font-weight: 500;
    padding: 12px 4px;
    position: sticky;
    top: 0;
  }
  td {
    border-bottom: 1px solid var(--brand-color);
  }
  th,
  td {
    text-align: left;
    padding: 8px 4px;
    &:nth-child(1) {
      width: 50px;
      & > img {
        width: 24px;
      }
    }
    & > img {
          width: 24px;
          margin-right: 8px;
    }
  }
  &.admin {
    th,
    td {
      &:last-child {
        width: 80px;
        & > img {
          width: 24px;
          margin-right: 8px;
          &.disabled {
            opacity: 0.5;
            pointer-events: none;
          }
        }
      }
    }
  }
}
</style>
