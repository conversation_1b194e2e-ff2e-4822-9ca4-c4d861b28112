class TreeNode {
    data = null;
    children = null;
    constructor (data) {
      this.data = data
      this.children = {}
      this.parent = null
    }
}

// Helper class for building a tree structure
export class TreeBuilder {
    root = null;
    idKey = null;
    parentFieldKey = null;
    _items = [];
    _leafTasks = [];

    /**
     *
     * @param {Array<object>} items Items to build the tree with
     * @param {String} idKey The object key name for the id field in the object
     * @param {String} parentFieldKey The object key name for the parent id field in the object
     * @param {Boolean} orderd Whether the items passed as arguments is in order or not
     */
    constructor ({ items, idKey, parentFieldKey, ordered = true }) {
      this._items = items
      this.idKey = idKey
      this.parentFieldKey = parentFieldKey
      this.root = new TreeNode({})
      if (ordered) {
        this._generateTree()
      } else {
        this._generateTreeFromUnorderedList()
      }
    }

    // function for generating a tree with ordered items
    _generateTree () {
      for (const item of this._items) {
        this.insertItem(item)
      }
    }

    // function for generating a tree with items which are not ordered
    _generateTreeFromUnorderedList () {
      const mappedArr = new Map()

      // Build a hash table and map items to objects
      for (const item of this._items) {
        const id = item[this.idKey]

        // to ignore in case of duplicates
        if (!mappedArr.has(id)) {
          mappedArr.set(id, new TreeNode(item))
        }
      }

      // looping over the hashmap
      for (var value of mappedArr.values()) {
        const id = value.data[this.idKey] // getting the id of the item
        const parentId = value.data[this.parentFieldKey] // getting the parent id of the item
        /* If the element is not at the root level,
           add it to its parent array of children */
        if (parentId) {
          value.parent = mappedArr.get(parentId)
          const parentItem = mappedArr.get(parentId)
          parentItem.children[id] = value
        } else {
          // If the element is at the root level, directly add to the tree
          this.root.children[id] = value
        }
      }
    }

    _getLeafTasks (node = this.root) {
      const children = Object.values(node.children)
      if (!children.length) {
        this._leafTasks.push(node)
      } else {
        for (const child of children) {
          this._getLeafTasks(child)
        }
      }
    }

    _rollUpCost (node) {
      const children = Object.values(node.children)
      let cost = node.data.task_material_associations[0]?.core_bom?.bom_versions[0]?.total_cost ?? 0
      for (const child of children) {
        cost += child.data.cost
      }
      node.data.cost = cost
      if (node.parent) {
        this._rollUpCost(node.parent)
      }
    }

    computeCost () {
      this._getLeafTasks()
      const restrictDuplicate = new Set()
      this._leafTasks.forEach((node) => {
        node.data.cost = node.data.task_material_associations[0]?.core_bom?.bom_versions[0]?.total_cost ?? 0
      })
      this._leafTasks.forEach((node) => {
        if (!restrictDuplicate.has(node[this.parentFieldKey])) {
          restrictDuplicate.add(node[this.parentFieldKey])
          if (node.parent) {
            this._rollUpCost(node.parent)
          }
        }
      })
    }
}
