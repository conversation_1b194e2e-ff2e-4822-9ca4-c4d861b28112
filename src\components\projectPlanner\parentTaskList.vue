<template>
    <div>
      <div class="container">
        <div class="add-user__body" ref="addUserBody">
                  <div
                    class="tags mt-3"
                    v-for="task in reversedCoreTasks"
                    :key="task.id"
                  >
                      {{ task.text }}
                    </div>
            </div>
      </div>
    </div>
  </template>

<script>

export default {
  name: 'ParentTaskBody',
  props: {
    tasks: {
      type: Array,
      default: () => ([])
    }
  },
  data () {
    return {
      openPopup: false,
      taskAssociatedBom: null,
      taskAssociatedId: null,
      selectedBom: [],
      bomList: [],
      searchKeyword: ''
    }
  },
  computed: {
    reversedCoreTasks () {
      return this.tasks.slice().reverse()
    }
  }
}

</script>

  <style lang="scss" scoped>

  .container {
    padding: 17px 17px 17px 17px;
    width: 400px;
    height: auto;
  }

  .tags {
    display: flex;
    background-color: var(--brand-color);
    padding: 0.6rem;
    font-size: small;
    border-radius: 0.3rem;
    max-width: fit-content;
  }

  </style>
