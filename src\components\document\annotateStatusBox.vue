<template>
  <div class="status-container">
    <div class="status-container-heading" @click="open = !open">
      <label for="" class="status-container-label">
     {{ obj.type | findAnnotationName }}</label
      >
      <img
        v-if="!open"
        src="~@/assets/images/icons/arrow-down-icon.svg"
        width="15px"
        alt=""
      />
      <img
        v-else
        src="~@/assets/images/icons/arrow-up-icon.svg"
        width="15"
        alt=""
      />
    </div>
    <div
      :class="{
        'status-container-box': true,
        'status-container-box-expanded': open,
      }"
    >
      <p>Creted by  </p> <p> : </p><p>{{obj.id ? obj.createdBy?.first_name +" "+ obj.createdBy?.last_name: 'You' }}</p>
      <p>Creted on  </p> <p> : </p> <p>{{ obj.createdOn | duration }}</p>
      <p>Updated by </p> <p> : </p> <p>{{obj.updatedBy ? obj.updatedBy?.first_name +" "+obj.updatedBy?.last_name : '--'}}</p>
      <p>Doc version </p> <p> : </p> <p v-if="obj.docId">{{version}}</p> <p v-else>--</p>
      <p>Status </p>     <p> : </p>
      <div class="switch">
        <input id="switch-a" name="selector" type="radio" value="Y" class="switch-input" :checked="obj.status===customList.todo" @change="changeStatus(customList.todo)" />
        <label for="switch-a" class="switch-label switch-label-a" >Nill</label>
        <input id="switch-b" name="selector" type="radio" value="I" class="switch-input" :checked="obj.status===customList.progress" @change="changeStatus(customList.progress)"  />
        <label for="switch-b" class="switch-label switch-label-b" >Progress</label>
        <input id="switch-c" name="selector" type="radio" value="N" class="switch-input"  :checked="obj.status===customList.done" @change="changeStatus(customList.done)"/>
        <label for="switch-c" class="switch-label switch-label-c" >Done</label>
        <span class="switch-selector"></span>
      </div>
    </div>
  </div>
</template>
<script>
import { Duration } from '@/utils/date'
export default {
  name: 'statusbox',
  props: {
    obj: {
      type: Object
    },
    customList: {
      type: Object
    },
    index: {
      type: Number
    },
    revisionList: {
      type: Array
    }
  },
  data () {
    return {
      open: false
    }
  },
  filters: {
    findAnnotationName (value) {
      switch (value) {
      case 'path':
        return 'Free Sketch'

      case 'rect':
        return 'Rectangle'

      case 'lineArrow':
        return 'Arrow'

      case 'image':
        return 'Image/Icon'

      case 'textbox':
      case 'Textbox': {
        return 'Text'
      }

      default:
        return ''
      }
    },
    duration (value) {
      if (!value) return '--'
      return Duration(value)
    }
  },
  methods: {
    changeStatus (status) {
      this.$emit('updateStatus', status)
    }
  },
  computed: {
    version () {
      let version = '--'
      this.revisionList.forEach((item, index) => {
        if (item.id === this.obj.docId) {
          version = 'Version ' + (index + 1)
        }
      }
      )
      return version
    }
  },
  mounted () {
  }
}
</script>

<style lang="scss" scoped>
.status-container {
  width: 100%;
  background-color: var(--brand-color);
  border: 1px solid rgba(83, 78, 78, 0.687);
  border-radius: 5px;
  box-shadow: rgba(17, 12, 46, 0.15) 0px 48px 100px 0px;
  transform-origin: right;
  transform: scaleX(0);
  animation: openNotification 1s;
  animation-fill-mode: forwards;
  overflow: hidden;
  &-heading {
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
  }
  &-label {
    font-weight: 400;
  }
  &-box {
    font-size: .8rem;
    height: 0;
    overflow: hidden;
    background-color: white;
    opacity: 0;
    transition: all .7s ease;
    border-radius: 0 0 5px 5px;
    display: grid;
    grid-template-columns: 40% 10% 50%;
    & p {
      opacity: 0;
    }
    &-expanded {
      padding: 10px 15px;
      height: 8rem;
      border: 1px solid rgba(83, 78, 78, 0.687);
      width: 100%;
      opacity: 1;
      & p {
        opacity: 1;
        // transition: opacity .1s ease-in-out 1s ;
      }
    }
  }
}
@keyframes openNotification {
  from {
    transform-origin: right;

    transform: scaleX(0);
  }
  to {
    transform-origin: right;

    transform: scaleX(1);
  }
}
.switch {
  position: relative;
  height: 15px;
  width: 120px;
  // margin: 20px auto;
  border: 1px solid rgba(0, 0, 0, 0.3803921569);
  background:inherit;
  border-radius: 15px;
  display: flex;
  &-label {
  position: relative;
  z-index: 2;
  float: left;
  width:39.55px;
  // line-height: 32px;
  font-size: 9px;
  color: #676a6c;
  text-align: center;
  cursor: pointer;
}
&-input {
  display: none;
}
&-selector {
  position: absolute;
  z-index: 1;
  top: 0px;
  left: 0px;
  bottom:0px;
  display: block;
  width:39.55px;
  border-radius: 15px;
  background-color: #1ab394;
  transition: all 0.8s cubic-bezier(0.18, 0.89, 0.32, 1.28);
}
}
.switch-input:checked + .switch-label {
font-weight: 500;
  transition: 0.15s ease-out;
  transition-property: color, text-shadow;
}

.switch-input:checked + .switch-label-a ~ .switch-selector {
  transform: translateX(0%);
  background-color: #d4dddbd8;
}
.switch-input:checked + .switch-label-b ~ .switch-selector {
  transform: translateX(100%);
  background-color: #fcd927;
}
.switch-input:checked + .switch-label-c ~ .switch-selector {
  transform: translateX(200%);
  background-color: #5deb3d;
}
</style>
