<template>
  <div class="bom">
    <div class="bom-bar v-center space-between px-3">
      <h1 class="weight-500 xxl">Bill of Materials</h1>
      <div class="bom-action m flex">
        <create-bom
          @updatebomlist="updatebomlist"
          v-if="showCreateBom"
          :bomlistLength="bomList?.length ?? 0"
        />
        <button @click="gotoBomCompare" class="btn btn-black mr-2" :disabled="bomList?.length === 0">
          Compare BOM
        </button>
        <button
          :disabled="!canCompare"
          @click="gotoVersionCompare"
          class="btn btn-black mr-2"
        >
          Compare BOM Version
        </button>
      </div>
    </div>
    <div class="bom-container">
      <transition name="slide">
      <div v-if="!showProductCode" class="bom-product-code">
        <project-product-list />
      </div>
      </transition>
      <div :class="showProductCode ? 'bom-full-width':'bom-content'">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import createBom from '../../../components/bom/common/createBom.vue'
import ProjectProductList from '../../../components/bom/project/projectProductList.vue'
import { GetAllBomVersionListByBomId } from '@/api'
export default {
  components: { createBom, ProjectProductList },
  data () {
    return {
      canCompare: false
    }
  },
  computed: {
    ...mapGetters('projectBom', ['selectedBomId']),
    ...mapGetters(['user', 'collaborator']),
    ...mapGetters('projectBom', ['bomList']),
    showProductCode () {
      return ((this.$route.path.includes('/bom') && this.$route.path.includes('/edit')) || (this.$route.path.includes('/bom') && this.$route.path.includes('/new')))
    },
    showCreateBom () {
      return ((this.user.projectLevelRole === 'ADMIN' ||
      this.user.projectLevelRole === 'COLLABORATOR') && !this.collaborator)
    }
  },
  methods: {
    async checkBomCompare () {
      if (this.selectedBomId) {
        const res = await GetAllBomVersionListByBomId(this.selectedBomId)
        this.canCompare = res.bom_versions[0].version_no > 1
      }
    },
    gotoBomCompare () {
      this.$router.push('/bom/compare/project')
    },
    gotoVersionCompare () {
      this.$router.push(`/bom/compare/${this.selectedBomId}`)
    },
    setBaseData () {
      const { bomId } = this.$route.params
      const { bomVersionId } = this.$route.query
      this.$store.dispatch('projectBom/setBaseData', {
        bom_id: bomId || null,
        bom_version_id: bomVersionId || null
      })
    },
    updatebomlist () {
      this.$store.dispatch('projectBom/getBomList')
    }
  },
  mounted () {
    this.checkBomCompare()
  },
  watch: {
    selectedBomId (newVal) {
      if (newVal) {
        this.checkBomCompare()
      }
    },
    '$store.state.projectIdForCollaborator' () {
      this.updatebomlist()
      if (this.$store.projectIdForCollaborator === null) {
        this.updatebomlist()
      }
    },
    '$route.params.bomId' (val) {
      this.setBaseData()
    },
    '$route.query.bomVersionId' (val) {
      this.setBaseData()
    },
    '$route.query.name' () {
      this.setBaseData()
      this.updatebomlist()
    }
  },
  created () {
    this.setBaseData()
    this.$store.dispatch('projectBom/getBomList')
  }
}
</script>

<style lang="scss" scoped >
.slide-enter-active {
  transition: transform 0.5s ease-out;
}
.slide-enter {
  transform: translateX(100%);
}

/* Sliding transition styles for leaving */
.slide-leave-active {
  transition: transform 0.5s ease-out;
}
.slide-leave-to /* .slide-leave-active below version 2.1.8 */ {
  transform: translateX(-100%);
}
.bom {
  height: 100%;
  &-bar {
    height: 60px;
    margin: -12px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  &-container {
    height: calc(100% - 60px);
    display: flex;
    padding-top: 10px;
    & > div {
      height: 100%;
    }
  }
  &-product-code {
    width: 20%;
    @media screen and (max-width: 1366px) {
      width: 15%;
  }
  }
  &-content {
    width: 85%;
    padding-left: 10px;
    overflow-y: auto;
  }
  &-full-width {
    width: 100%;
    padding-left: 10px;
  }
}
</style>
