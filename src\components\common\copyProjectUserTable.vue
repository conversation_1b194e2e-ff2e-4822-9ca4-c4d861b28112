<template>
    <div class="copy-dtx-table">
        <table >
        <thead >
      <tr class="m">
        <th>Name</th>
        <th>Email </th>
        <th>Role</th>
        <th v-if="isTenantAdminOrCollaborator">Action</th>
      </tr>
    </thead>
      <tbody>
        <tr v-for="(user, index) in displayData" :key="user.id" :style="{ backgroundColor: user.status === 2 ? '#E9ECEF' : '' }">
            <!-- <div :style="{ color: user.status === 2 ? 'red' : '' }"
              :key="user.id"
              v-if="user.status !== 3"> -->
        <td>{{ getUserName(user.user_id) }}</td>
        <td> {{ getUserById(user.user_id).associated_user?.email }} </td>
        <td v-if="userEditObject.editIndex === index">
            <select v-model="userEditObject.userRole">
                  <option v-for="(role, key) in roleMap" :key="key" :value="key">
                    {{ role }}
                  </option>
                </select>
            </td>
            <td v-else>
                {{ roleMap[user.role_id] }}
           </td>
       <td v-if="isTenantAdminOrCollaborator && !user.system_added" class="action-column">
        <div class="action-icons">
            <img
                  v-show="userEditObject.editIndex === index"
                  v-tooltip="'Cancel'"
                   class="pointer"
                   height="20px"
                  src="~@/assets/images/icons/close-icon.svg"
                  @click="cancelUpdateUserRole"
                />
                <img
                  v-show="userEditObject.editIndex === index"
                  v-tooltip="'Save'"
                  height="20px"
                   class="pointer"
                  src="~@/assets/images/icons/save-icon.svg"
                  @click="updateUserRole"
                />
                <img
                  v-show="userEditObject.editIndex !== index"
                  v-tooltip="'Edit User Role'"
                  class="pointer" :class="{ 'disabled-action': user.status === 2 }" src="~@/assets/images/pencil.svg" alt=""
                  @click="()=>{if (user.status !== 2) editUserRole(user, index)}"
                />
                <img
                  v-tooltip="'Remove User'"
                  class="pointer" src="~@/assets/images/trash-2.svg" alt=""
                  v-if="isTenantAdmin"
                  @click="deleteUser(user)"
                />
          </div>
             </td>
      </tr>
      </tbody>
    </table>
      <div class="drawer" :class="drawer ? 'open' : 'close'">
        <project-form
          ref="projectForm"
          :isUpdate="true"
          :buttonDisabled="buttonDisabled"
          @cancel="closeDrawer"
          @update="updateProject"
        ></project-form>
      </div>
      <div
        v-if="currentProject.id"
        class="drawer"
        :class="addUserDrawer ? 'open' : 'close'"
      >
        <add-user-to-project
          :projectId="currentProject.id"
          @close="addUserDrawer = false"
        ></add-user-to-project>
      </div>
    </div>
  </template>

<script>
import {
  GetCurrentProjectData,
  UpdateProjectById,
  DeleteUserFromProjectData,
  UpdateUserRoleForProjectData
} from '@/api'
import { mapGetters } from 'vuex'
import { timeStampToDateTime } from '@/filters/dateFilter'
import ProjectForm from '../../components/manage/projectForm.vue'
import AddUserToProject from '../../components/manage/addUserToProject.vue'
import Config from '@/config.js'
import ConfirmationDialog from '@/plugins/confirmationDialog'
import { arraySearchForUser } from '@/utils/array'
import { alert } from '@/plugins/notification'
export default {
  name: 'Project',
  filters: {
    timeStampToDateTime
  },
  props: {
    searchKeyword: {
      type: String,
      default: null
    },
    pageNumber: {
      type: [Number, String],
      default: 1
    },
    perPage: {
      type: [Number, String],
      default: 10
    }
  },
  components: { ProjectForm, AddUserToProject },
  data () {
    return {
      drawer: false,
      addUserDrawer: false,
      roleMap: Config.userRole,
      userEditObject: {
        editIndex: -1,
        userId: '',
        userRole: ''
      },
      diableSaveButton: false,
      buttonDisabled: false // this for disabling update button  of project updation
    }
  },
  computed: {
    ...mapGetters(['currentProject', 'getUserById']),
    ...mapGetters(['user', 'getUserById', 'collaborator']),
    getLocationObject () {
      const location = this.currentProject.location
        ? this.currentProject.location
          .replace('(', '')
          .replace(')', '')
          .split(',')
        : []
      return {
        latitude: location[0] || '',
        longitude: location[1] || ''
      }
    },
    isTenantAdminOrCollaborator () {
      return (
        this.user.tenantLevelRole === 'ADMIN' ||
             this.user.projectLevelRole === 'ADMIN') && !this.collaborator
    },
    isTenantAdmin () {
      return (
        this.user.tenantLevelRole === 'ADMIN' || this.user.projectLevelRole === 'ADMIN')
    },
    displayData () {
      const filtered = this.currentProject.project_user_associations
        .filter(user => user.status !== 3)
      return arraySearchForUser(filtered, this.searchKeyword).slice(
        (this.pageNumber - 1) * this.perPage,
        this.pageNumber * this.perPage
      )
    },
    filteredUserData () {
      if (this.searchKeyword) {
        return arraySearchForUser(this.currentProject.project_user_associations, this.searchKeyword)
      } else {
        return this.currentProject.project_user_associations
      }
    }
  },
  methods: {
    editUserRole (user, index) {
      this.userEditObject = {
        editIndex: index,
        userId: user.user_id,
        userRole: user.role_id
      }
    },
    cancelUpdateUserRole () {
      this.userEditObject = {
        editIndex: -1,
        userId: '',
        userRole: ''
      }
    },
    updateUserRole () {
      if (this.disableSaveButton) return
      this.disableSaveButton = true
      const { userId, userRole } = this.userEditObject
      UpdateUserRoleForProjectData({
        user_id: userId,
        role_id: userRole
      })
        .then((res) => {
          this.getCurrentProject()
          this.$notify.success('User role updated successfully')
          this.userEditObject = {
            editIndex: -1,
            userId: '',
            userRole: ''
          }
        })
        .catch(() => {
          this.$notify.alert('Something went wrong')
        }).finally(() => {
          this.disableSaveButton = false
        })
    },
    deleteUser (user) {
      ConfirmationDialog(
        'Are you sure you want to remove this user?',
        (res) => {
          if (res) {
            DeleteUserFromProjectData({
              user_id: user.user_id
            })
              .then((res) => {
                this.getCurrentProject()
                this.$notify.success('User removed successfully')
              })
              .catch(() => {
                this.$notify.alert('Something went wrong')
              })
          }
        }
      )
    },
    openAddUserDrawer () {
      this.addUserDrawer = true
    },
    getUserName (id) {
      const user = this.getUserById(id)
      return user
        ? `${user.associated_user?.first_name || ''} ${
              user.associated_user?.last_name || ''
            }`
        : '--'
    },
    openDrawer () {
      this.drawer = true
    },
    closeDrawer () {
      this.drawer = false
    },
    editProject () {
      this.$refs.projectForm.name = this.currentProject.name
      this.$refs.projectForm.latitude = this.getLocationObject.latitude
      this.$refs.projectForm.longitude = this.getLocationObject.longitude
      this.$refs.projectForm.selectedTenant = this.currentProject?.company
      this.$refs.projectForm.startDate = this.currentProject?.planned_start_date
      this.$refs.projectForm.endDate = this.currentProject?.planned_end_date
      this.$refs.projectForm.cost = this.currentProject?.project_cost
      this.$refs.projectForm.revenue = this.currentProject?.project_revenue
      this.$refs.projectForm.address = this.currentProject?.address?.address
      this.$refs.projectForm.state = this.currentProject?.address?.state
      this.$refs.projectForm.city = this.currentProject?.address?.city
      this.$refs.projectForm.pincode = this.currentProject?.address?.pincode
      this.openDrawer()
    },
    updateProject (obj) {
      this.buttonDisabled = true
      const body = {
        name: obj.name,
        planned_end_date: obj?.planned_end_date,
        planned_start_date: obj?.planned_start_date,
        project_cost: obj?.project_cost,
        project_revenue: obj.project_revenue,
        address: obj.address,
        location:
            obj.latitude.length && obj.longitude.length
              ? `(${obj.latitude}, ${obj.longitude})`
              : null
      }
      UpdateProjectById({ id: this.currentProject.id, data: body }).then((res) => {
        this.closeDrawer()
        this.getCurrentProject()
        this.$notify.success('Project Updated Successfully')
        this.buttonDisabled = false
      })
        .catch(() => {
          this.buttonDisabled = false
          alert('Project updation failed!')
        })
    },
    getCurrentProject () {
      GetCurrentProjectData().then((res) => {
        this.$store.commit('setCurrentProject', res?.core_projects?.[0])
      })
    }
  }
}
</script>

  <style lang="scss" scoped >
    .disabled-action {
      opacity: 0.5 !important;
      cursor: not-allowed !important;
    }
  .project {
    margin: -12px;
    height: 100%;
    &-bar {
      background-color: var(--bg-color);
      padding: 20px;
    }
    &-row {
      display: grid;
      grid-template-columns: 120px 1fr;
      grid-gap: 20px;
      margin: 10px 0;
      font-size: 16px;
      padding: 0 20px;
    }
    &-user-table {
      margin: 20px 0;
      &__header {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 80px;
        margin: 0 20px;
        font-size: 14px;
        font-weight: 500;
        border-bottom: var(--border);
        background-color: var(--brand-color);
        &__item {
          padding: 8px;
        }
      }
      &__body {
        &__row {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr 80px;
          margin: 0 20px;
          font-size: 13px;
          border-bottom: var(--border);
          &__item {
            padding: 8px;
            & > img {
              cursor: pointer;
              width: 20px;
              margin-right: 4px;
            }
          }
        }
      }
    }
  }
  </style>
