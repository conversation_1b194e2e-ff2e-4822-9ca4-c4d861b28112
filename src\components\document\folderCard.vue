<template>
  <div class="folder-card v-center">
    <img @click="openFolder" src="~@/assets/images/icons/folder-icon.svg" width="20px" alt="">
    <div @click="openFolder" v-overflow-tooltip class="folder-card-name mx-2 elipsis-text" >
      {{ folder.doc_name }}
    </div>
    <img v-if="!useStateRouting && showMoreOptionsButton " src="~@/assets/images/icons/more-icon.svg" @click="openOptions" width="20px" alt="" class="more-button">
    <div class="folder-card-options" v-if="showOptions" ref="options">
      <div class="folder-card-option" @click="openRenameModal">
        <img src="~@/assets/images/edit-icon.svg" width="20px" alt="">
        <span>Rename</span>
      </div>
      <div v-if="isTenantAdminOrCollaborator" class="folder-card-option" @click="tryDeleteFolder">
        <img src="~@/assets/images/delete-gray-icon.svg" width="20px" alt="">
        <span>Delete</span>
      </div>
    </div>
    <modal :open="renameModal" @close="closeRenameModal" title="Rename Folder">
      <div class="rename-modal s">
        <div class="input-group">
          <label>Folder Name</label>
          <input type="text" v-model="renameFolderName" placeholder="Folder Name">
        </div>
        <div class="flex flex-end mt-3">
          <button class="btn btn-black" @click="renameFolder">Rename</button>
        </div>
      </div>
    </modal>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { updateDocumentName, deleteDocument, CheckDuplicateFolderName } from '@/api'
import { alert } from '@/plugins/notification'
import modal from '../common/modal.vue'
import confirmationDialog from '@/plugins/confirmationDialog'
export default {
  components: { modal },
  name: 'folderCard',
  props: {
    useStateRouting: {
      type: Boolean,
      default: false
    },
    routeHistory: {
      type: Array,
      default: () => ([])
    },
    folder: {
      type: Object,
      default: () => ({})
    }
  },
  data: () => ({
    showOptions: false,
    renameModal: false,
    renameFolderName: ''
  }),
  computed: {
    ...mapGetters(['user', 'getUserById', 'isOnProjectLevel']),
    showMoreOptionsButton () {
      if (this.isOnProjectLevel) {
        return (this.user.projectLevelRole === 'ADMIN' ||
        this.user.projectLevelRole === 'EDITOR' ||
        this.user.projectLevelRole === 'COLLABORATOR')
      } else {
        return (
          this.user.tenantLevelRole === 'ADMIN' ||
        this.user.tenantLevelRole === 'EDITOR' ||
          this.user.tenantLevelRole === 'COLLABORATOR')
      }
    },
    isTenantAdminOrCollaborator () {
      if (this.user.projectLevelRole === null) {
        return (
          this.user.tenantLevelRole === 'ADMIN'
        )
      } else {
        return (this.user.projectLevelRole === 'ADMIN')
      }
    }
  },
  methods: {
    openOptions () {
      this.showOptions = true
      setTimeout(() => {
        document.addEventListener('click', this.clickOutside)
      }, 500)
    },
    closeOptions () {
      this.showOptions = false
      document.removeEventListener('click', this.clickOutside)
    },
    clickOutside (e) {
      if (!this.$refs.options?.contains(e.target)) {
        this.closeOptions()
      }
    },
    openRenameModal () {
      this.renameModal = true
      this.renameFolderName = this.folder.doc_name
    },
    closeRenameModal () {
      this.renameModal = false
    },
    renameFolder () {
      const folderName = this.renameFolderName.toUpperCase()
      CheckDuplicateFolderName(folderName).then((data) => {
        if (data.core_documents.length !== 0) {
          alert('Folder Name Already exists!')
        } else if (this.renameFolderName.trim() === '') {
          alert('Folder name cannot be empty. Please provide a valid name.')
          this.renameFolderName = ''
        } else {
          updateDocumentName(this.folder.id, this.renameFolderName.toUpperCase()).then(res => {
            this.$emit('rename')
          })
          this.closeRenameModal()
        }
      })
    },
    deleteFolder (res) {
      if (res) {
        deleteDocument(this.folder.id).then(res => {
          this.$emit('delete')
        })
      }
    },
    tryDeleteFolder () {
      confirmationDialog(
        'Are you sure you want to delete this folder?',
        this.deleteFolder,
        'Delete',
        'Cancel',
        'Delete Folder'
      )
    },
    openFolder () {
      if (this.$props.useStateRouting) {
        this.$emit('push-route', this.folder.id)
      } else {
        this.$router.push(`/document-view/${this.folder.id}`).catch(() => {})
      }
    }
  }
}
</script>

<style lang="scss" scoped >
.folder-card {
  position: relative;
  background-color: var(--bg-color);
  border-radius: 5px;
  padding: 10px;
  user-select: none;
  &:hover {
    box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
  }
  &-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    width: calc(100% - 40px);
    cursor: pointer;
  }
  .more-button {
    cursor: pointer;
    border-radius: 50%;
    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
  }
  &-options {
    position: absolute;
    top: 100%;
    right: 0;
    width: 140px;
    background-color: var(--white);
    border-radius: 5px;
    padding: 10px 2px;
    filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.3));
    z-index: 1;
    &:before {
      content: "";
      position: absolute;
      top: -10px;
      right: 10px;
      width: 0;
      height: 0;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-bottom: 10px solid var(--white);
    }
  }
  &-option {
    display: flex;
    align-items: center;
    padding: 5px 8px;
    cursor: pointer;
    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
    img {
      width: 20px;
      margin-right: 8px;
    }
    span {
      font-size: 14px;
      font-weight: 500;
      line-height: 1;
      color: var(--text-color);
    }
  }
}
</style>
