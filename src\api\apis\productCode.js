import * as productCode from '../query/productCode'
import { runQuery, runMutation } from '../graphQl'
import store from '../../store'

export const CreateProductCodeData = (variables) => {
  return runMutation(productCode.CreateProductCode(), variables, 'tenant')
}

export const GetAllProductCode = (searchType, searchKeyword) => {
  // here serachtype and search keyword are for searching product code based on given bom names
  let conditions = { }
  if (store.getters?.collaborator) {
    const userId = store.getters?.user?.userId
    const collaboratorId = store.getters?.collaboratorId
    conditions = { core_bom: { collaborator_boms: { source_tenant_id: { _eq: collaboratorId }, target_tenant_user_id: { _eq: userId } } } }
    // condition for  searching product code based on bom names
  }
  if (searchType === 'bomLevel' && searchKeyword) {
    if (conditions.core_bom) {
      conditions.core_bom.name = { _regex: `(?i).*${searchKeyword}.*` }
    } else {
      conditions = { core_bom: { name: { _regex: `(?i).*${searchKeyword}.*` } } }
    }
  }
  return runQuery(productCode.getAllProductCodeQuery(), { conditions }, 'tenant')
}
export const GetProductCode = (id) => {
  return runQuery(productCode.getProductCodeQuery(), { id }, 'tenant')
}

export const GetAllChildProductCodeByBomVersion = (bomVersionId) => {
  return runQuery(productCode.getAllChildProductCodeByBomVersionQuery(), { bomVersionId }, 'tenant')
}

export const GetAllImpactedProductCode = (bomId) => {
  return runQuery(productCode.getAllImpactedProductCodeQuery(), { bomId }, 'tenant')
}

export const UpdateProductCode = (id, pCode) => {
  return runMutation(productCode.updateProductCode(), { id, product_code: pCode }, 'tenant')
}

export const ProductCodeStatus = (id) => {
  return runQuery(productCode.productCodeStatusQuery(), { id }, 'tenant')
}
