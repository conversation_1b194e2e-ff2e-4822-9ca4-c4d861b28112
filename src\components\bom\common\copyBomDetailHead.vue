<template>
    <div class="table">
      <div class="copy-dtx-table">
      <div class="table-head">
        <div class="table-row">
          <div class="table-cell" v-overflow-tooltip
      v-for="(label, index) in headings" :key="index">
      {{ label }}
          </div>
        </div>
      </div> </div>
    </div>
</template>

<script>
export default {
  props: {
    headings: { type: Array, required: true }
  }
}
</script>

<style scoped>
/* If you want to tweak BOM-specific stuff later, add it here */
</style>
