<template>
 <div> <div ref="schedulerContainer" class="schedulerContainer" id="schedulerContainer" style="width: 100%;">
  </div>
  <modal
      :open="modalOpen"
      @close="modalOpen=false"
      :closeOnOutsideClick="true"
      title="Task Details"
    >
      <task-edit-form
        v-if="selectedTask && modalOpen"
        :task="selectedTask"
        :open="selectedTask && modalOpen"
        :assigneeMap="assigneeMap"
        @close="modalOpen=false"
        @update-and-close="updateAndCloseModal"
      />
    </modal>
</div>
</template>
<script>
import '@/assets/scheduler/dhtmlxscheduler'
import { alert } from '../../plugins/notification'
import Modal from '../common/modal.vue'
import TaskEditForm from '../timeLine/taskEditForm.vue'
import { mapGetters } from 'vuex'
let scheduler
export default {
  name: 'TimeLineEdit',
  props: {
    usersData: { type: Object, required: true, default: () => { } }, // users list in hashmap
    timeLineData: { type: Array, required: true, default: () => [] },
    assigneeMap: { type: Object, required: true, default: () => { } },
    generalTaskData: { type: Object, required: true, default: () => { } },
    currentDate: { type: Date },
    viewModeParent: { type: String, default: 'weekly' }
  },
  components: {
    Modal,
    TaskEditForm
  },
  data () {
    return {
      groupColors: {
        1: '#20B2AA', // LightSeaGreen
        2: '#008B8B', // DarkCyan
        3: '#2E8B57', // SeaGreen
        4: '#00CED1', // DarkTurquoise
        5: '#008000', // Green
        6: '#ADFF2F', // GreenYellow
        7: '#5F9EA0', // CadetBlue
        8: '#9ACD32', // YellowGreen
        9: '#40E0D0', // Turquoise
        10: '#48D1CC', // MediumTurquoise
        11: '#66CDAA', // MediumAquaMarine
        12: '#7FFF00', // Chartreuse
        13: '#00FF7F', // SpringGreen
        14: '#D2691E', // Chocolate
        15: '#00FA9A', // MediumSpringGreen
        16: '#CD853F', // Peru
        17: '#3CB371', // MediumSeaGreen
        18: '#008080', // Teal
        19: '#556B2F', // DarkOliveGreen
        20: '#A0522D', // Sienna
        21: '#00FF00', // Lime
        22: '#228B22', // ForestGreen
        23: '#808000', // Olive
        24: '#008000', // Green
        25: '#8FBC8B', // DarkSeaGreen
        26: '#008B8B', // DarkCyan
        27: '#32CD32', // LimeGreen
        28: '#20B2AA', // LightSeaGreen
        29: '#006400', // DarkGreen
        30: '#CD853F' // Peru
      },
      modalOpen: false,
      timeLineDataTemp: JSON.parse(JSON.stringify(this.timeLineData)),
      viewMode: this.viewModeParent,
      usersList: Object.values(this.usersData),
      selectedTask: {},
      generalTaskDataTemp: JSON.parse(JSON.stringify(this.generalTaskData)),
      updatedTasksArray: []
    }
  },
  mounted () {
    // this.modalOpen = true
    this.initScheduler(this.usersList, this.timeLineDataTemp, this.currentDate)
  },
  computed: { ...mapGetters('timeLine', ['getCalederHashMap']) },
  methods: {

    initScheduler (userData, taskData, date = new Date()) {
      if (scheduler) {
        scheduler.destructor()
        scheduler = null
      }
      scheduler = window.Scheduler.getSchedulerInstance()
      scheduler.plugins({
        readonly: true,
        timeline: true,
        tooltip: true,
        minical: true,
        treetimeline: true,
        limit: true
      })
      scheduler.attachEvent('onLoad', (id, ev, render) => {
      })
      // scheduler.skin = 'skin'
      // this is configuration for tooltip for task
      scheduler.config.className = 'dhtmlXTooltip tooltip'
      scheduler.config.full_day = true
      scheduler.config.timeout_to_display = 50
      scheduler.config.timeout_to_hide = 50
      scheduler.config.delta_x = 15
      scheduler.config.delta_y = -20
      scheduler.config.dblclick_create = false
      scheduler.config.drag_create = false
      scheduler.config.date_format = '%d-%m-%Y' // '%Y-%m-%d %H:%i'
      const format = scheduler.date.date_to_str('%d-%m-%Y') // '%Y-%m-%d %H:%i'
      scheduler.templates.tooltip_text = function (start, end, event) {
        start = new Date(new Date(start).setUTCHours(0, 0, 0, 0))
        end = new Date(new Date(end).setUTCHours(0, 0, 0, 0))
        return '<b>Task name</b> ' + event.text + '<br/><b>Start date:</b> ' +
          format(start) + '<br/><b>End date:</b> ' + format(end) + '<br/><b>Project Name</b> ' + event.project_name
      }
      // window.addEventListener('DOMContentLoaded', function () {
      scheduler.locale.labels.timeline_tab = 'Timeline'
      scheduler.locale.labels.section_custom = 'Section'
      scheduler.locale.labels.timeline_scale_header = 'Users' // users cell header

      scheduler.config.details_on_create = true
      scheduler.config.details_on_dblclick = false

      scheduler.config.multisection_shift_all = true
      var sections = userData
      const monthlyClass = this.viewMode === 'monthly' ? 'activeButton' : null
      const weeklyClass = this.viewMode === 'weekly' ? 'activeButton' : null
      scheduler.config.header = [
        // 'day',
        // 'week',

        { html: 'Monthly', click: () => { this.changeMode('monthly') }, css: monthlyClass },
        { html: 'Weekly', click: () => { this.changeMode('weekly') }, css: weeklyClass },
        {
          view: 'minicalendar',
          click: function () {
            if (scheduler.isCalendarVisible()) {
              scheduler.destroyCalendar()
            } else {
              scheduler.renderCalendar({
                position: this,
                date: scheduler.getState().date,
                navigation: true,
                handler: function (date, calendar) {
                  scheduler.setCurrentView(date)
                  scheduler.destroyCalendar()
                }
              })
            }
          }
        },
        'prev',
        { view: 'date', css: 'date_box' },
        'next',
        'spacer',
        { html: 'Cancel', click: () => { this.$emit('disableEditMode', { date: scheduler._date, viewMode: this.viewMode }) }, css: 'edit-button' },
        { html: 'Save', click: () => { this.$emit('saveData', { date: scheduler._date, viewMode: this.viewMode, updatedTasks: this.updatedTasksArray, generalTaskDataTemp: this.generalTaskDataTemp }) }, css: 'save-button' }
      ]
      // scheduler.config.start_on_monday = true
      // create a timeline in the scheduler
      if (this.viewMode === 'monthly') {
        scheduler.createTimelineView({
          name: 'timeline',
          render: 'tree',
          month: 12,
          folder_dy: 20,
          x_unit: 'month',
          x_date: '%F',
          x_step: 1,
          x_size: 12,
          x_start: 0,
          x_length: 12,
          y_unit: sections,
          y_property: 'section_id',
          scrollable: true
        })
      } else {
        scheduler.createTimelineView({
          name: 'timeline',
          render: 'tree',
          days: 7,
          folder_dy: 20,
          x_unit: 'day',
          x_date: '%D %j %F',
          x_step: 1,
          x_size: 7,
          x_start: 0,
          x_length: 7,
          y_unit: sections,
          y_property: 'section_id',
          scrollable: true,
          round_position: true // To make an event occupy the entire cell width, no matter how long this event lasts, use the round_position parameter
        })
      }

      // scheduler.date.timeline_start = scheduler.date.week_start
      // modal

      let currentTask
      let originalSection = {}
      scheduler.attachEvent('onBeforeDrag', (id, mode, e) => {
        currentTask = scheduler.getEvent(id)
        // this is to disable the editing  if the user is in  viewer role
        if (currentTask.readonly) {
          return
        }
        if (mode !== 'create') {
          originalSection = { ...currentTask }
        }

        return true
      })
      scheduler.attachEvent('onBeforeEventChanged', (ev, e, isNew, original) => {
        // given below code prevents changing the resource, if the new assignee is not the part of the given project
        if (!this.assigneeMap[ev.projectId]?.includes(ev.section_id)) {
          alert(this.usersData[ev.section_id]?.label + ' is not associated with this ' + ev.project_name + ' project')
          ev.section_id = originalSection.section_id
          ev.start_date = originalSection.start_date
          ev.end_date = originalSection.end_date
          return true
        }

        // this condition to check whether new assigned user has already have  the same task
        const eventsOfGivenUser = scheduler.getView().selectEvents({ section: ev.section_id })
        let count = 0 // from last update of scheduler, we are getting the same event inside the new allocation user , so we need to check the count of the same event comming twise

        for (const event of eventsOfGivenUser) {
          if (event.taskId === originalSection.taskId && ev.section_id !== originalSection.section_id) {
            count++
            if (count === 2) {
              alert(this.usersData[ev.section_id]?.label + ' already a part of   ' + ev.text + ' task')
              ev.section_id = originalSection.section_id
              ev.start_date = originalSection.start_date
              ev.end_date = originalSection.end_date
              return true
            }
          }
        }
        // these below code statements helps to adjust all the tasks with same id  will automatically adjusts (start and end Date only ) if any if the task (same id ) get altered
        // in order to reduce the  iterartion we initially selelct all the task which has similar end date and start date then checks the task id
        const evs = scheduler.getEvents(new Date(originalSection.start_date), new Date(originalSection.end_date))// will return all events
        for (const taskEvent of evs) {
          if (taskEvent.taskId === ev.taskId) {
            scheduler.getEvent(taskEvent.id).end_date = new Date(ev.end_date) // changes event's data
            scheduler.getEvent(taskEvent.id).start_date = new Date(ev.start_date) // sets new start date
            scheduler.updateEvent(taskEvent.id) // renders the updated event
          }
        }
        let flag = false
        if (ev.section_id !== originalSection.section_id) {
          for (const [index, user] of this.generalTaskDataTemp[ev.taskId].task_assignees.entries()) {
            if (user.id === ev.section_id) {
              if (!this.generalTaskDataTemp[ev.taskId].assigneeIds.includes(ev.section_id)) {
                this.generalTaskDataTemp[ev.taskId].task_assignees[index].status = 'new'
              } else {
                this.generalTaskDataTemp[ev.taskId].task_assignees[index].status = null
              }
              flag = true
            }
            if (user.id === originalSection.section_id) {
              this.generalTaskDataTemp[ev.taskId].task_assignees[index].status = 'deleted'
            }
          }

          if (!flag) {
            this.generalTaskDataTemp[ev.taskId].task_assignees.push(
              {
                first_name: this.usersData[ev.section_id].label.split(' ')[0],
                last_name: this.usersData[ev.section_id].label.split(' ')[1],
                id: ev.section_id,
                status: 'new'
              })
          }
        }

        //  given code is for updating duration
        const startDate = new Date(ev.start_date)
        let totalDuration = null
        while (startDate < new Date(ev.end_date)) {
          const dayId = startDate.getDay()
          if (this.getCalederHashMap[ev.projectId]?.workDays?.includes(dayId) &&
          !this.getCalederHashMap[ev.projectId]?.holidays?.includes(startDate)
          ) { totalDuration += this.getCalederHashMap[ev.projectId]?.working_hours }
          startDate.setDate(startDate.getDate() + 1)
        }
        this.generalTaskDataTemp[ev.taskId].duration = totalDuration || 0
        return true
      })
      scheduler.attachEvent('onDragEnd', (id, mode, e) => {
        const ev = scheduler.getEvent(id)
        this.generalTaskDataTemp[ev.taskId].planned_start_date = currentTask.start_date
        this.generalTaskDataTemp[ev.taskId].planned_end_date = currentTask.end_date
        if (!this.updatedTasksArray.includes(ev.taskId)) {
          this.updatedTasksArray.push(ev.taskId)
        }
        return true
      })
      scheduler.attachEvent('onEventLoading', (ev) => {
        if (ev.readonly) {
          ev.color = '#c2c0ba'
          ev.textColor = '#000000'
        }
        return true
      })
      scheduler.attachEvent('onCellClick', function (e) {
        return false
      })
      scheduler.attachEvent('onDblClick', (id, e) => {
        const ev = scheduler.getEvent(id)
        // if the task is readonly , then need to restrict opening edit modal
        if (ev.readonly) {
          return
        }
        this.selectedTask = this.generalTaskDataTemp[ev.taskId]
        this.modalOpen = true
        return false
      })
      scheduler.init('schedulerContainer', date, 'timeline')
      scheduler.parse(taskData) // task data
    },
    changeMode (mode) {
      const date = scheduler._date
      this.viewMode = mode
      scheduler.destructor()
      this.updateTimeLineDataTemp()
      this.initScheduler(this.usersList, this.timeLineDataTemp, date)
    },
    updateAndCloseModal (taskData) {
      const evs = scheduler.getEvents(new Date(this.generalTaskDataTemp[taskData.id].planned_start_date), new Date(this.generalTaskDataTemp[taskData.id].planned_end_date))
      const assignees = []
      const deletedAssignees = []
      taskData.end_date = new Date(taskData.end_date)
      taskData.start_date = new Date(taskData.start_date)

      for (const user of taskData.task_assignees) {
        if (user.status === 'deleted') {
          deletedAssignees.push(user.id)
        } else {
          assignees.push(user.id)
        }
      }
      if (taskData.attachedBom) {
        for (const item of taskData.attachedBom) {
          item.taskId = this.selectedTask.id
          // this.attachBomUpdates.push(item)
          if (item.tag === 'deleted') {
            this.generalTaskDataTemp[taskData.id].attached_bom = null
          }
          if (item.tag === 'new') {
            this.generalTaskDataTemp[taskData.id].attached_bom = {
              id: item.id,
              name: item.name
            }
          }
        }
      }
      this.generalTaskDataTemp[taskData.id] = {
        ...this.generalTaskDataTemp[taskData.id],
        description: taskData.description,
        attached_bom: taskData.attached_bom,
        cost: taskData.cost,
        duration: taskData.duration,
        planned_end_date: taskData.end_date,
        planned_start_date: taskData.start_date,
        text: taskData.name,
        progress: taskData.progress,
        status: taskData.staus,
        tag_tasks: taskData.tags.flat(1),
        task_assignees: taskData.task_assignees,
        timeSpent: taskData.timeSpent,
        task_docs: taskData.task_docs,
        type: taskData.type
      }
      // deleting assignees
      evs.forEach(event => {
        if (event.taskId === taskData.id) {
          if (deletedAssignees.includes(event.section_id)) {
            scheduler.deleteEvent(event.id)
          }
        }
      })
      // adding new assigness
      taskData.task_assignees.forEach(user => {
        if (user.status !== 'deleted') {
          let flag = false
          for (const ev in evs) {
            if (ev.section_id === user.id) {
              flag = ev.id
              break
            }
          }
          if (flag) {
            const event = scheduler?.getEvent(flag)
            event.start_date = taskData.start_date
            event.end_date = taskData.end_date
            event.text = taskData.name
            scheduler.updateEvent(flag)
          } else {
            if (!this.usersData[user.id]) {
              this.usersData[user.id] = { key: user.id, label: user.first_name.toUpperCase() + ' ' + user.last_name.toUpperCase() }
              scheduler.addSection({ key: user.id, label: user.first_name.toUpperCase() + ' ' + user.last_name.toUpperCase() }, null)
            }
            let colorCode = 0
            if (this.generalTaskDataTemp[taskData.id].core_project.id <= 30) {
              colorCode = this.generalTaskDataTemp[taskData.id].core_project.id
            } else {
              colorCode = this.generalTaskDataTemp[taskData.id].core_project.id % 30
            }
            scheduler.addEvent(
              {
                start_date: taskData.start_date,
                end_date: taskData.end_date,
                text: taskData.name,
                section_id: user.id,
                id: taskData.id + user.id,
                taskId: taskData.id,
                color: taskData.is_critical ? 'red' : this.groupColors[Math.abs(colorCode)],
                projectId: this.generalTaskDataTemp[taskData.id].core_project.id,
                project_name: this.generalTaskDataTemp[taskData.id].core_project.name
              }
            )
          }
        }
      })
      if (!this.updatedTasksArray.includes(taskData.id)) {
        this.updatedTasksArray.push(taskData.id)
      }
      this.modalOpen = false
    },
    updateTimeLineDataTemp () {
      this.timeLineDataTemp = []
      for (const key in this.generalTaskDataTemp) {
        const task = this.generalTaskDataTemp[key]
        const data = {
          start_date: task.planned_start_date,
          end_date: task.planned_end_date,
          text: task?.text,
          project_name: task.core_project?.name,
          projectId: task.core_project?.id,
          projectColor: task.projectColor,
          taskId: task.id,
          readonly: task.readonly,
          parentTask: task.parentTask
        }
        for (const assignee of task.task_assignees) {
          this.timeLineDataTemp.push({
            ...data,
            id: task.id + assignee.id,
            section_id: assignee.id

          })
        }
      }
    }

  },
  watch: {
    timeLineData () {
      this.initScheduler(this.usersList, this.timeLineDataTemp, scheduler?._date)
    }
  }
}
</script>
<style lang="scss" scoped>
@import "../../assets/scheduler/dhtmlxscheduler.css";

.dhx_cal_tab.active {
  background-color: var(--brand-light-color);
  border: 1px solid var(--brand-color);
  text-shadow: none;
}

.dhx_cal_navline {
  z-index: 2;
}

.main {
  overflow: hidden !important;
}

.dhx_cal_container {
  overflow: hidden;
}

.date-range {
  margin: 0;

  input {
    height: 100%;
    border: 0.7px solid var(--brand-color);
    border-radius: 5px;
    padding-inline: 4px;
    background: var(--brand-light-color);
  }
}

.cancel-button {
  margin-right: 2rem !important;
  background-color: rgb(38 32 32);
  color: white;
  border-radius: 5px;
}

.edit-button {
  margin-right: 2rem !important;
  background-color: rgb(38 32 32);
  color: white;
  border-radius: 5px;
  box-shadow: rgba(246, 255, 117, 0.2) 0px 2px 8px 0px;
}

.activeButton {
  // margin-right: 2rem !important;
  background-color: var(--brand-light-color);
  color: black;
  border-radius: 5px;
  // margin-inline: 2rem;
  border: 0.3px solid var(--brand-color);
}

.save-button {
  margin-right: 2rem !important;
  background-color: var(--brand-color);
  color: rgb(12, 12, 12);
  border-radius: 5px;
}

.date_box {
  flex-grow: 0 !important;
  margin-inline: 10px;
}

.dhx_cal_prev_button {
  margin-left: 10px;
}

.dhx_cal_navline div.dhx_minical_icon {
  margin-left: 10px;
}
.dhx_matrix_cell div.load-marker{
        position: absolute;
        width: 40%;
        height: 25px;
        transform: translate(70%, 20%);
        line-height: 25px;
        text-align: center;
        border-radius: 7px;
        color: white;
    }
    .load-marker-no{
        background: #e0e0e0;
    }
    .load-marker-light{
        background: #aed581;
    }
    .load-marker-high{
        background: #ff8a65;
    }
    .schedulerContainer{
      height: calc(100vh - 260px);
    }
</style>
