<template>
    <div class="document-view">
      <div class="document-view-bar v-center space-between px-3">
        <h1 class="weight-600 xxxl">Select Document</h1>
        <div class="v-center">
          <div class="v-center" v-if="uploadDocButton">
            <upload-document
              v-if="parentFolder && purpose === 'attachment'"
              :folder="parentFolder"
              :is_revision="false"
                :open="uploadDocButton"
              @uploaded="getAllDcoument"
            >
              <div v-if="!restrictUpload" class="upload-btn">
                Upload Document
              </div>
            </upload-document>
            <button @click="openFolderPopup" v-if="canCreate && !restrictUpload" class="btn btn-black mx-3">
              Create Folder
            </button>
          </div>
          <div class="input-group search m">
            <input
              type="text"
              v-model="searchKeyWord"
              placeholder="Search by Folder,File name"
            />
          </div>
        </div>
      </div>
      <div v-if="loading" class="document-view-container center">
        <loading-circle />
      </div>
      <div v-else class="document-view-container">
        <div class="go-back v-center mb-3" :class="!parentFolder ?'disabled':''">
          <div class="flex v-center" @click="goBack">
            <img
            class="mr-1"
            src="~@/assets/images/icons/arrow-back.svg"
            width="20px"
            />
            <p>Go back</p>
          </div>
        </div>
        <div class="document-view-folder">
          <p>Folders</p>
          <div v-if="filteredDocuments.length" class="document-view-card-container">
            <template v-for="folder in filteredDocuments">
              <folder-card
                @push-route="routeHistory.push($event)"
                :key="folder.id"
                v-if="folder.folder"
                :useStateRouting="true"
                :routeHistory="routeHistory"
                :folder="folder"
                @rename="getAllDcoument"
                @delete="getAllDcoument"
              />
            </template>
          </div>
          <span v-else>No folders were found</span>
        </div>
        <div class="break"/>
        <div v-if="parentFolder"  class="document-view-folder mt-3">
          <p>Files</p>
          <div v-if="filteredDocuments.length" class="document-view-card-container">
            <template v-for="file in filteredDocuments" >
              <file-card
                @addToAttachedList="addToAttachedList"
                :key="file.id"
                :selectedFile="selectedFile"
                :parentFolder="parentFolder"
                :file="file"
                :fileSelect="true"
                v-if="!file.folder && (file.state === 1 || file.state === 2 || file.state === 3)"
                :activeFileId="activeFileId"
                @delete="getAllDcoument"
                @uploaded="getAllDcoument"
                @openDetail="openDetail(file)"
                @updated="getAllDcoument"
              />
            </template>
          </div>
          <span v-else>No files were found</span>
        </div>
      </div>
      <div>
        <div  class="footer">
          <div class="footer-box" v-if="purpose==='attachment'">
            <div  v-for="(doc, index) in attachedList" :key="doc?.id">
              <div class="footer-box-list" v-if="doc?.flag!=='deleted'"
               v-overflow-tooltip> {{doc?.core_document?.doc_name || doc.doc_name}}
                <img src="~@/assets/images/icons/close-icon.svg" class="footer-box-list-close" width="18px" alt=""  @click="deleteDocAttach(index,doc)">
              </div>
            </div>
        </div>
        <div class="footer-box" v-else-if="purpose==='copy'">
          <div class="footer-box-list" v-if="documentToBeCopied" v-overflow-tooltip>
              {{ documentToBeCopied.doc_name }}
          </div>
        </div>
        <div class="footer-box" v-else>
          <div class="footer-box-list" v-if="documentToBeInherited" v-overflow-tooltip>
              {{ documentToBeInherited.doc_name }}
          </div>
        </div>
        <div class="flex flex-end py-3 l mr-4">
          <button class="btn btn-black mr-3 pointer" @click="closeDocs">CANCEL</button>
      <button v-if="purpose === 'attachment'" class="btn pointer" :disabled="attachedList <= 0" @click="$emit('files-selected',attachedList)">
        SELECT
      </button>
      <button v-else-if="purpose === 'inherit'" class="btn pointer"
      @click="inheritDoc" :disabled="disableInheritButton">
        INHERIT
        </button>
      <button v-else class="btn pointer" @click="copyDoc" :disabled="disableCopyButton">
        COPY
        </button>
        </div>
      </div>
      </div>
      <modal
        :open="newFolderPopup"
        @close="closeFolderPopup"
        title="Create New Folder"
      >
        <create-folder-form v-if="newFolderPopup"  :buttonDisabled="loading"   :open="newFolderPopup" @close="closeFolderPopup" @create="getAllDcoument" />
      </modal>
    </div>
  </template>

<script>
import Modal from '../common/modal.vue'
import CreateFolderForm from './createFolderForm.vue'
import {
  getAllParentFolders,
  getDocumentsByParentIdMatNull,
  getDocumentById,
  getDocumentsByParentIdMatNullWithToken,
  getAllParentFoldersWithToken,
  getDocumentByIdWithToken
} from '@/api'
import LoadingCircle from '../common/loadingCircle.vue'
import FolderCard from './folderCard.vue'
import FileCard from './fileCardForMaterialAttach.vue'
import UploadDocument from './uploadDocument.vue'
import { alert } from '../../plugins/notification'
import { mapGetters } from 'vuex'
export default {
  name: 'documentSelector',
  props: {
    restrictUpload: {
      type: Boolean,
      default: false
    },
    showOnlyLockedDocs: {
      type: Boolean,
      default: false
    },
    linkedDocs: {
      type: Array,
      default: () => ([])
    },
    purpose: {
      type: String,
      default: 'attachment'
    },
    uploadDocButton: {
      type: Boolean,
      default: true
    },
    // this is coming only from scheduler,
    token: {
      type: String,
      default: null
    },
    // this is coming only from scheduler,
    projectId: {
      type: Number,
      default: null
    },
    materialAttachment: {
      type: Boolean,
      default: false
    }
  },
  components: {
    Modal,
    CreateFolderForm,
    LoadingCircle,
    FolderCard,
    UploadDocument,
    FileCard
  },
  data: () => ({
    routeHistory: [],
    searchKeyWord: '',
    newFolderPopup: false,
    documents: [],
    selectedFile: {},
    loading: false,
    parentFolder: null,
    activeFileId: null,
    documentToBeCopied: null,
    documentToBeInherited: null,
    detailObject: {
      open: false,
      fileId: ''
    },
    attachedList: [],
    disableInheritButton: false,
    disableCopyButton: false,
    initialDocs: []
  }),
  computed: {
    ...mapGetters(['user', 'collaborator', 'isOnProjectLevel']),
    canCreate () {
      if (this.purpose === 'attachment' && !this.collaborator) {
        if (this.isOnProjectLevel) {
          if (this.user.projectLevelRole === 'ADMIN' || this.user.projectLevelRole === 'COLLABORATOR') {
            return true
          }
        } else {
          if (this.user.tenantLevelRole === 'ADMIN' || this.user.tenantLevelRole === 'COLLABORATOR') {
            return true
          }
        }
      }
      return false
    },
    filteredDocuments () {
      return this.documents.filter((doc) => {
        return (
          doc?.doc_name
            ?.toLowerCase()
            .includes(this.searchKeyWord?.toLowerCase()) ||
          doc?.description
            ?.toLowerCase()
            .includes(this.searchKeyWord?.toLowerCase())
        )
      })
    },
    getAllFolders () {
      return this.documents.filter((doc) => {
        return doc.folder && (doc.state === 1 || doc.state === 2 || doc.state === 3)
      })
    },
    getAllFiles () {
      return this.documents.filter((doc) => {
        return !doc.folder && (doc.state === 1 || doc.state === 2 || doc.state === 3)
      })
    },
    latestActiveFileId () {
      return (this.activeFiledId)
    }
  },

  watch: {
    routeHistory: {
      handler () {
        this.getAllDcoument()
      },
      immediate: true
    }
  },
  methods: {
    inheritDoc () {
      this.disableInheritButton = true
      this.$emit('inherit-doc', this.documentToBeInherited)
    },
    copyDoc () {
      this.disableCopyButton = true
      this.$emit('copy-doc', this.documentToBeCopied)
    },
    goBack () {
      this.routeHistory.pop()
    },
    openFolderPopup () {
      this.newFolderPopup = true
    },
    closeFolderPopup () {
      this.newFolderPopup = false
    },
    openDetail (file) {
      this.detailObject = {
        open: true,
        fileId: file.id
      }
    },
    closeDetail () {
      this.detailObject = {
        open: false,
        fileId: ''
      }
    },
    getAllDcoument () {
      const documentId = this.routeHistory.at(-1)
      this.loading = true
      let apiCall = null
      // for creating a document in project level, show both locked and checkin documents for inherit and copy, for attachments only need to show locked documents
      let showOnlyLocked = true
      if (this.purpose === 'copy' || this.purpose === 'inherit') {
        showOnlyLocked = false
      }
      if (!this.token) {
        apiCall = documentId
          ? getDocumentsByParentIdMatNull(documentId, this.purpose !== 'attachment' ? 'tenant' : null, this.materialAttachment, showOnlyLocked)
          : getAllParentFolders(this.purpose !== 'attachment' ? 'tenant' : null)
      } else {
        apiCall = documentId
          ? getDocumentsByParentIdMatNullWithToken(documentId, this.projectId, this.token)
          : getAllParentFoldersWithToken(this.projectId, this.token)
      }
      apiCall
        .then((res) => {
          this.documents = res.core_documents
        })
        .finally(() => {
          this.loading = false
        })
      this.getParentFolder()
    },
    getParentFolder () {
      const documentId = this.routeHistory.at(-1)
      if (documentId) {
        const apicall = this.token
          ? getDocumentByIdWithToken(documentId, this.projectId, this.token)
          : getDocumentById(documentId, this.purpose !== 'attachment' ? 'tenant' : null)
        apicall.then((res) => {
          this.parentFolder = res.core_documents?.[0] || null
        })
          .catch((err) => {
            console.log(err)
            this.parentFolder = null
          })
      } else {
        this.parentFolder = null
      }
    },
    // here the file contain full data of selected document
    addToAttachedList (file) {
      if (this.purpose === 'copy') {
        this.documentToBeCopied = file
        return
      }
      if (this.purpose === 'inherit') {
        this.documentToBeInherited = file
        return
      }
      // checking the given selelcted material is added or not
      const duplicate = this.attachedList.filter((element, index) => {
        if (element.core_document?.id === file.id) {
          element.index = index
          return (element)
        }
      })
      if (duplicate?.length > 0) {
        // if the flag is in deleted state then no need to add the doc again
        if (duplicate[0]?.flag === 'deleted') {
          // duplicate[0]?.index shows where the doc data belongs in attachedlist
          this.attachedList[duplicate[0]?.index].flag = 'existing'
          this.$set(this.attachedList, duplicate[0]?.index, this.attachedList[duplicate[0]?.index])
        } else { alert(`${file?.doc_name} has  already attached`) }
      } else {
        file.flag = 'new'
        file.core_document = { ...file }
        this.attachedList.push(file)
      }
    },
    deleteDocAttach (index, doc) {
      this.attachedList[index].flag = 'deleted'
      this.$set(this.attachedList, index, this.attachedList[index])
    },
    closeDocs () {
      this.attachedList.forEach((item) => {
        if (item.flag) {
          delete item.flag
        }
      })
      this.attachedList = [...this.initialDocs]
      this.$emit('close')
    },
    keyPress (e) {
      if (e instanceof KeyboardEvent && e.code === 'Enter') {
        switch (this.purpose) {
        case 'copy':
          this.copyDoc()
          break
        case 'inherit':
          this.inheritDoc()
          break
        default:
          break
        }
      }
    }
  },
  mounted () {
    this.getAllDcoument()
    // this.attched list stores the selelcted docs, for editing linkedDocs carries doc data from edit materail to here
    this.attachedList = [...this.linkedDocs]
    this.initialDocs = [...this.linkedDocs]
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
  }
}
</script>

  <style lang="scss" scoped>
  .document-view {
    min-width: 70vw;
    height: 70vh;
    background-color: var(--white);
    &-image {
      width: 100%;
      height: 125px;
      object-fit: contain;
      object-position: center;
    }
    &-bar {
      background-color: red;
      height: 60px;
      margin-bottom: 0;
      background: var(--bg-color);
      border-bottom: var(--border);
    }
    .break {
      margin-left: -12px;
      margin-right: -12px;
    }
    .upload-btn {
      font-size: 1em;
        padding: 0.35em 1.2em;
        border-radius: 0.3em;
        color: var(--black);
        font-weight: 500;
        border: none;
        box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
        background-color: var(--brand-color);
    }
    &-container {
      height: 52vh;
      overflow-y: auto;
      padding: 12px;
    }
    &-folder {
      p {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
      }
    }
    &-card-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      grid-gap: 12px;
    }
    .go-back {
      cursor: pointer;
      width: fit-content;
      p {
        font-size: 16px;
        font-weight: 600;
      }
    }
    .disabled{
      cursor: not-allowed;
      opacity: 0.5;
      div {
        pointer-events: none;
        }
      }
    .footer{
  &-box{
    display:flex;
    justify-content: end;
    padding-inline:40px ;
    gap:10px;
    flex-wrap: wrap;
    &-list{
      border-radius: 3px 15px 15px 3px;
      background-color: var(--brand-color);
      padding: 5px 25px 5px 5px;
    max-width: 150px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    cursor:context-menu;
    position: relative;
    &-close{
      position: absolute;
      top:5px;
      right:4px;
      cursor: pointer;
    }
    }
  }
    }
  }
  </style>
