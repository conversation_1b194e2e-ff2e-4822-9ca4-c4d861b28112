/* eslint-disable no-unused-vars */
let isConfigUpdate = false
const reader = new FileReader()

async function uploadToS3Bucket (stream, credential, cd) {
  try {
    if (!window.AWS) {
      return
    }
    if (!isConfigUpdate) {
      window.AWS.config.update(({ region: credential.region }))
      isConfigUpdate = true
    }

    const s3 = new window.AWS.S3({
      credentials: new window.AWS.Credentials({
        apiVersion: 'latest',
        accessKeyId: credential.accressKeyId,
        secretAccessKey: credential.secretAccessKey,
        signatureVersion: credential.signatureVersion,
        region: credential.region,
        Bucket: credential.Bucket
      })
    })
    const uploadItem = await s3.upload({
      Bucket: credential.Bucket,
      Key: '888888', // name for the bucket file
      ContentType: document.getElementById('fileToUpload').files[0].type,
      Body: stream
    }).on('httpUploadProgress', function (progress) {
      console.log('progress=>', progress)
      cd(getUploadingProgress(progress.loaded, progress.total))
    }).promise()
    console.log('uploadItem=>', uploadItem)
    return uploadItem
  } catch (error) {
    console.log(error)
  }
}

function getUploadingProgress (uploadSize, totalSize) {
  const uploadProgress = (uploadSize / totalSize) * 100
  return Number(uploadProgress.toFixed(0))
}

async function uploadMedia () {
  const credentialRequest = {
    accressKeyId: '',
    secretAccessKey: '',
    signatureVersion: 'v4',
    region: 'us-east-1',
    Bucket: 'large-files-0'
  }
  const mediaStreamRequest = getFile(document.getElementById('fileToUpload').files[0])
  const [mediaStream] = await Promise.all([
    mediaStreamRequest
  ])
  await uploadToS3Bucket(mediaStream, credentialRequest, (progress) => {
    console.log(progress)
  })
}

async function getFile (file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      resolve(e.target.result)
    }
    reader.onerror = (_err) => {
      // eslint-disable-next-line prefer-promise-reject-errors
      reject(false)
    }
    reader.readAsArrayBuffer(file)
  })
};
