<template>
  <div class="compare-summary">
    <div class="compare-summary-title">Compare Summary</div>
    <div class="grid-2">
      <div class="compare-summary-diff">
        <div class="compare-summary-diff-list">
          <div
            v-for="diff in diffList"
            :key="diff.id"
            :class="{
              'compare-summary-diff-list-item': true,
              'compare-summary-diff-list-item--add': diff.isAdded,
              'compare-summary-diff-list-item--remove': diff.isDeleted,
              'compare-summary-diff-list-item--update': diff.isUpdated,
            }"
          >
            <div
              class="compare-summary-diff-list-messages"
              v-for="msg in diff.message"
              :key="msg.id"
            >
              {{ msg }}
            </div>
          </div>
        </div>
      </div>
      <div class="compare-summary-kpi" >
        <kpi-card title="Bom Similarity" :value="getSimilarity" />
        <kpi-card title="Updated Material" :value="updatedPer" color="#ffd100" />
        <kpi-card title="Newly Added Material" :value="addedPer" color="#11ab0b" />
        <kpi-card title="Removed Material" :value="deletedPer" color="#ff0000" />
      </div>
    </div>
  </div>
</template>

<script>
import kpiCard from './kpiCard.vue'
export default {
  components: { kpiCard },
  name: 'CompareSummery',
  props: {
    diffList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    getSimilarity () {
      /*
      * For each material it has score of 5 = material itself + 4 fields
      * fields are associated BOM, price, quantity, unit
      */
      let totalScore = 0
      let diffScore = 0
      const MATERIAL_SCORE = 5
      this.diffList.forEach((diff) => {
        totalScore += MATERIAL_SCORE
        if (diff.isAdded || diff.isDeleted) {
          diffScore += MATERIAL_SCORE
        } else {
          diffScore += diff.message.length
        }
      })
      return totalScore
        ? (((totalScore - diffScore) / totalScore) * 100).toFixed(2)
        : 100
    },
    deletedPer () {
      const oldList = this.diffList.filter((diff) => !diff.isAdded) || []
      const deleted = this.diffList.filter((diff) => diff.isDeleted)
      return oldList.length
        ? ((deleted.length / oldList.length) * 100).toFixed(2)
        : 0
    },
    addedPer () {
      const newList = this.diffList.filter((diff) => !diff.isDeleted) || []
      const added = this.diffList.filter((diff) => diff.isAdded)
      return newList.length
        ? ((added.length / newList.length) * 100).toFixed(2)
        : 0
    },
    updatedPer () {
      const oldList = this.diffList.filter((diff) => !diff.isAdded) || []
      const updated = this.diffList.filter((diff) => diff.isUpdated)
      return oldList.length
        ? ((updated.length / oldList.length) * 100).toFixed(2)
        : 0
    }
  }
}
</script>

<style lang="scss" scoped >
.compare-summary {
  width: 100%;
  border-bottom: 1px solid #e8e8e8;

.grid-2 {
  align-items: flex-start;
}
  &-title {
    font-size: 18px;
    font-weight: 500;
  }
  &-diff {
    padding: 10px 0;
    overflow: auto;
    max-height: 300px;
    &-title {
      font-size: 16px;
      font-weight: 500;
    }
    &-list {
      &-item {
        &--add {
          color: #52c41a;
        }
        &--remove {
          color: #f5222d;
        }
        &--update {
          color: #cf8a00;
        }
      }
      &-messages {
        padding: 2px 0;
        position: relative;
        padding-left: 16px;
        &::before {
          content: "•";
          font-size: 28px;
          position: absolute;
          top: -10px;
          left: 0px;
          color: #000000;
        }
      }
    }
  }
  &-kpi {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: stretch;
    & > div {
      width: 48%;
    }
  }
}
</style>
