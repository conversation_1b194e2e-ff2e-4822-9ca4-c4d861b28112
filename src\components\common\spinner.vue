<template>
    <div class="spinner">
      <div class="spinner-inner"></div>
    </div>
  </template>

<script>
export default {
  name: 'spinner',
  data () {
    return {
      isLoading: false
    }
  }
}
</script>

  <style scoped lang="scss">
  .spinner {
    position: relative;
    width: 20px;
    height: 20px;
    margin: 0 0 0 0 ;
  }

  .spinner-inner {
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--brand-color);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  </style>
