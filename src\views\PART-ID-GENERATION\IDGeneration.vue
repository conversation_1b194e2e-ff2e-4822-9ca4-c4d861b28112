<template>
    <div>
        <div class="header-wrap">
            <div class="header-container">
            <div class="xxxl weight-500">Sequence Generation</div>
            </div>
            <div>
                <button v-if="isTenantAdmin" @click="goToCreate" class="btn">+ Create Template</button>
            </div>
        </div>
        <div class="search-container">
          <div></div>
          <div class="input-group search ml-4 searchbox">
            <input
            class="input-search"
            v-model="searchKeyword"
            @input="updateOnSearch"
            type="text"
            placeholder="Search"
            />
  </div>
        </div>
<div class="copy-dtx-table list-table">
  <table>
    <thead>
      <tr>
        <th>Name</th>
        <th>Feature</th>
        <th>Preview</th>
        <th>Created By</th>
      </tr>
    </thead>

    <tbody v-if="!loading && templates.length">
      <tr
        v-for="template in templates"
        :key="template.id"
        @click="goToView(template.id)"
      >
        <td>{{ template.name }}</td>
        <td>{{ template.core_feature.name }}</td>
        <td class="preview-cell elipsis-text" v-overflow-tooltip>
          <span
            v-for="(part, idx) in generateTemplatePreview(template.sequence_rules)"
            :key="idx"
            class="preview-part"
            v-html="part.text"
          ></span>
        </td>
<td>
  {{ template.created_by_user.first_name }}
  {{ template.created_by_user.last_name }}
</td>

</tr>
</tbody>

<!-- No Data Found Row -->
     <tbody v-else-if="!loading && templates.length === 0">
        <tr>
      <td colspan="3" class="no-data">No Data Found</td>
    </tr>
      </tbody>
      <tbody v-else-if="loading">
        <tr>
          <td colspan="6">
            <div class="center">
              <loading-circle />
            </div>
          </td>
        </tr>
      </tbody>
  </table>
</div>
    <div class="pagination-footer">
    <pagination2 :length="totalCount" :pageNumber="pageNumber" :perPage="perPage" @selectPage = "selectPage"/>
    </div>
    </div>
</template>
<script>
import { GetTemplateData } from '@/api'
import { mapGetters } from 'vuex'
import pagination2 from '@/components/common/pagination2.vue'
import loadingCircle from '@/components/common/loadingCircle.vue'
import { debounce } from '@/utils/debounce'
import { alert } from '@/plugins/notification'
import config from '@/config'
export default {
  name: 'PartIdGeneration',
  components: { pagination2, loadingCircle },
  data () {
    return {
      preview: '', // the main uppercase preview
      subscriptLabels: [],
      coreFeatures: config.CORE_FEATURES,
      loading: false,
      searchKeyword: '',
      totalCount: 0,
      pageNumber: 1,
      perPage: 10,
      updateOnSearch: null,
      templates: []
    }
  },
  computed: {
    ...mapGetters(['isTenantAdmin', 'user']),
    userInitials () {
      const firstInitial = this.user?.first_name?.charAt(0) || ''
      const lastInitial = this.user?.last_name?.charAt(0) || ''
      return (firstInitial + lastInitial).toUpperCase()
    }
  },
  created () {
    this.updateOnSearch = debounce(() => {
      this.pageNumber = 1
      this.fetchSequenceTemplates()
    }, 500)
    this.fetchSequenceTemplates()
  },
  mounted () {
    // this.fetchSequenceTemplates()
  },
  methods: {
    generateTemplatePreview (rules) {
      if (!Array.isArray(rules)) return []

      const sorted = rules.slice().sort((a, b) => a.sequence_order - b.sequence_order)

      return sorted.map(rule => {
        const typeRaw = rule.component_name
        const type = typeRaw ? typeRaw.trim().toLowerCase() : 'undefined'
        let text = ''
        let label = null
        if (type === 'auto sequence') {
          const raw = String(rule.start_value ?? '')
          const paddingLength = rule.padding_length || 0
          const padded = paddingLength > 0 ? '0'.repeat(paddingLength) + raw : raw
          text = String(padded)
        } else if (type === 'auto_sequence') {
          const raw = String(rule.start_value ?? '')
          const paddingLength = rule.padding_length || 0
          const padded = paddingLength > 0 ? '0'.repeat(paddingLength) + raw : raw
          text = String(padded)
        } else if (type === 'number') {
          text = rule.default_value != null ? String(rule.default_value) : 'XX'
        } else if (type === 'month') {
          text = 'MM'
        } else if (type === 'year') {
          text = 'YYYY'
        } else if (type === 'date') {
          text = 'DD'
        } else if (type === 'config list') {
          text = '[config]'
          label = 'config'
        } else if (type === 'tags') {
          text = '[tags]'
          label = 'tags'
        } else if (type === 'uuid') {
          text = '[uuid]'
          label = 'uuid'
        } else if (type === 'user initials') {
          text = this.userInitials
        } else if (type === 'text') {
          text = rule?.default_value || ''
        } else {
          text = ''
        }

        if (rule.delimiter_after) {
          text += rule.delimiter_after
        }

        const highlightedText = text.toUpperCase().replace(
          /\[([^\]]+)\]/g,
          '<strong>[$1]</strong>'
        )

        return { text: highlightedText, label }
      })
    },

    goToView (id) {
      this.$router.push(`/settings/sequence-generator/view/${id}`)
    },
    deleteTemplate (id) {
      alert('Delete option disabled for now')
    },
    selectPage (pageNumber) {
      this.pageNumber = pageNumber
      this.fetchSequenceTemplates()
    },
    goToCreate () {
      this.$router.push('/settings/sequence-generator/create')
    },
    fetchSequenceTemplates () {
      const jump = (this.pageNumber - 1) * this.perPage
      this.loading = true
      GetTemplateData(jump, this.perPage, this.searchKeyword).then(res => {
        if (res) {
          this.templates = res.core_sequence_id_template
          this.totalCount = res.core_sequence_id_template_aggregate.aggregate.count
        }
        this.loading = false
      }).catch(err => {
        console.log(err)
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.preview-cell {
  padding: 0 15px;
  margin: 0;
}

.preview-table {
  border-collapse: collapse;
  border: none;
  width: auto;
}

.preview-table td,
.preview-table tr {
  border: none;
  padding: 0;
  margin: 0;
}

.preview-row {
  height: auto;
  line-height: 1; // tight
  padding: 0;
  margin: 0;
}

.preview-part {
  font-size: 12px;
  text-align: center;
  vertical-align: bottom;
  line-height: 1;
}
.preview-label {
  font-size: 9px;
  color: #374151; // slightly darker readable grey
  text-align: center;
  vertical-align: top;
  line-height: 1;
  padding: 0;
  margin: 0;
}

.header {
    &-wrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 2px solid #ddd;
        margin-bottom: 10px;
    }
    &-container {
    padding-block: 10px;
    }
  }
  .search-container {
  width: 100%;
  display: flex;
  margin-bottom: 5px;
  justify-content: flex-end;
  }
    .pagination-footer {
    width: 100%;
    display: flex;
    justify-content: space-between;

    &-total {
      margin-top: 16px;
      display: flex;
      background: #F1F3F5;
      padding: 0.5rem 0.5rem 0.5rem;
      border: 1px solid #F1F3F5;
      border-radius: 0.2em;
      width: fit-content;
      padding-inline: 10px;
      margin-left: auto;
    }
  }
  .no-data {
    text-align: center;
    vertical-align: middle;
  }
@media (max-width: 1366px) and (max-height: 633px) {
  .list-table {
    height: 340px;
  }
}

</style>
