.bom-detail {
  position: relative;

  &-head {
    background-color: var(--brand-color);
    top: 0;

    &>div {
      font-weight: 500;
    }
  }
  &-header {
    position: sticky;
  }

  &-body {
    border-bottom: 1px solid var(--brand-color);
    &.open {
      border-bottom: none;
    }
  }

  .clickable-porduct-code:hover {
    cursor: pointer;
    color: var(--blue-color);
    text-decoration: underline;
  }

  .bom-child {
    background-color: rgba(var(--brand-rgb), 0.1);
    background-color: (rgb(245,245,241), 0.1);
  }

  .bom-title {
    font-weight: 500;
    font-size: 12px;
    padding: 6px 8px;
    .bom-out-of-date {
      color: var(--alert);
      border: 1px solid var(--alert);
      border-radius: 4px;
      padding: 2px 4px;
      margin-left: 4px;
      background-color: rgba(255, 0, 0, 0.1);
    }
  }

  &-row {
    display: flex;
    padding: 6px 4px;
    font-size: 12px;

    &>div {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      text-align: center;
      padding-left: 4px;

      &:nth-child(1) {
        width: 14%;
      }

      &:nth-child(2) {
        width: 14%;
      }

      &:nth-child(3) {
        width: 14%;
      }

      &:nth-child(4) {
        width: 14%;
      }

      &:nth-child(5) {
        width: 15%;
      }

      &:nth-child(6) {
        width: 14%;
      }

      &:nth-child(7) {
        width: 14%;
      }

      &:nth-child(8) {
        width: 14%;
      }

      &:nth-child(9) {
        width: 15%;
      }
      &:nth-child(10) {
        width: 9%;
      }
      &:nth-child(11) {
        width: 9%;
      }
      &:nth-child(12) {
        width: 9%;
      }
      &:nth-child(13) {
        width: 9%;
      }
      &:nth-child(14) {
        width: 9%;
      }
    }
  }

  &-table {
    display: flex;
    padding: 6px 4px;
    font-size: 12px;

    &>div {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      text-align: center;

      &:nth-child(1) {
        width: 24%;
      }

      &:nth-child(2) {
        width: 24%;
      }

      &:nth-child(3) {
        width: 24%;
      }

      &:nth-child(4) {
        width: 14%;
      }

      &:nth-child(5) {
        width: 9%;
      }
    }
  }
}