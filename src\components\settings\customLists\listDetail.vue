<template>
  <div class="list-detail"  v-click-outside="cancelEdit">
    <div v-if="selected.loading" class="fh fw center">
      <loading-circle />
    </div>
    <div
      v-else-if="selected.id === null && selected.data === null"
      class="fh fw center"
    >
      <p>Select a list to view details</p>
    </div>
    <div class="fh" v-else v-click-outside="cancelAddNew">
      <div class="list-detail-bar py-3">
        <h4 class="weight-500 l">{{ selected?.data?.name }}</h4>
      </div>
      <div class="list-detail-table dtx-table">
        <table>
          <thead>
            <tr>
              <th class="col-sl"></th>
              <th class="col-name">Name</th>
              <th class="col-action" v-if="isTenantAdmin" >Action</th>
              <th v-else></th>
            </tr>
          </thead>
          <tbody >
            <tr
              v-for="(item, index) in selected?.data?.custom_list_values"
              :key="index"
              v-show="!item.deleted"
            >
              <td class="col-sl">{{ index + 1 }}</td>
              <td class="col-name" :class="{ 'disabled': addNew }">
                <input
                  v-if="item.editable === true && selectToEdit === item.id"
                  v-model="editName"
                  type="text"
                />
                <p v-else>{{ item.name }}</p>
              </td>
              <td class="col-action" v-if="item.editable && selectToEdit !== item.id && selected.data.custom_list_values.length && isTenantAdmin" >
                <img
                :class="{ 'disabled': addNew }"
                  @click="edit(item)"
                  src="~@/assets/images/edit-icon.svg"
                  alt=""
                />
                <img
                  @click="deleteListItem(item)"
                  :class="{ 'disabled': addNew ||  selected?.data?.custom_list_values.length <= 1}"
                  src="~@/assets/images/delete-icon.svg"
                  alt=""
                />
              </td>
              <td class="col-action" v-else-if="selectToEdit === item.id && item.editable && isTenantAdmin">
                <img
                  @click="save(item, index)"
                  src="~@/assets/images/icons/save-icon.svg"
                  alt=""
                  class="mr-1"
                />
                <img
                  style="width: 18px;"
                  @click="cancelEdit(item)"
                  src="~@/assets/images/close.png"
                  alt=""
                />
              </td>
              <td v-else ></td>
            </tr>
            <tr v-if="addNew">
              <td class="col-sl">
                {{ (selected?.data?.custom_list_values?.length || 0) + 1 }}
              </td>
              <td class="col-name">
                <input v-model="newName" type="text" />
              </td>
              <td class="col-action" v-if="isTenantAdmin">
                <img
                  @click="addNewRow"
                  src="~@/assets/images/icons/save-icon.svg"
                  alt=""
                  class="mr-1"
                />
                <img
                  style="width: 18px;"
                  @click="cancelAddNew"
                  src="~@/assets/images/close.png"
                  alt=""
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="flex flex-end mt-1 s" v-if="isTenantAdmin">
        <button class="btn" v-if="!addNew && !selectToEdit" @click.stop="addNew = true">+ Add New Item </button>
      </div>
    </div>
  </div>
</template>

<script>
import loadingCircle from '../../common/loadingCircle.vue'
import { mapGetters } from 'vuex'
import { DeleteCustomListValues, UpdateCustomListValues, CreateCustomListValues, GetTaskInStatusCount, ChangeDefaultCustomValue, getMaterialAttatchedCount, UpdateMaterialCustomListValue, GetUnitOfMaterialCount, UpdateUOMCustomValue, GetResourceGroupCount, ResourceStateValueCount, MaterialTypeCount } from '@/api'
import { alert, success } from '@/plugins/notification'
import ConfirmationDialog from '@/plugins/confirmationDialog'
import Loader from '@/plugins/loader'
export default {
  components: { loadingCircle },
  name: 'ListDetail',
  props: {
    selected: {
      type: Object,
      default: () => {
        return {
          id: null,
          index: null,
          loading: false,
          data: null
        }
      }
    }
  },
  data () {
    return {
      addNew: false,
      selectToEdit: null,
      editName: ''
    }
  },
  computed: {
    ...mapGetters(['user']),
    isTenantAdmin () {
      return this.user.tenantLevelRole === 'ADMIN'
    }
  },
  methods: {
    edit (item) {
      this.selectToEdit = item.id
      this.editName = item.name
    },
    save (item, index) {
      const loader = new Loader()
      loader.show()
      UpdateCustomListValues(this.selectToEdit, {
        name: this.editName.trim(),
        deleted: false
      }).then((res) => {
        this.$emit('update', { id: this.selected.id, index })
        success('Custom list edited successfully')
        this.selectToEdit = null
        this.editName = ''
      }).catch(err => {
        if (err?.message?.includes('Uniqueness violation')) {
          this.$notify.alert('Custom list value already exists')
        } else {
          this.$notify.alert(err?.message ?? 'Something went wrong')
        }
      }).finally(() => {
        loader.hide()
      })
    },
    deleteListItem (item) {
      const transformedArray = this.selected.data.custom_list_values.map(item => ({
        value: item.id,
        label: item.name
      })).filter(listItem => listItem.value !== item.id)
      const stringId = item?.id.toString()
      Promise.all([
        MaterialTypeCount(stringId),
        ResourceStateValueCount(item.id),
        GetResourceGroupCount(item.id),
        GetUnitOfMaterialCount(item.id),
        GetTaskInStatusCount(item.id), // Get tasks count
        getMaterialAttatchedCount(item.id) // Get materials count
      ]).then(([materialTypeRes, ResourceStateRes, ResourceGroupRes, UomRes, taskRes, materialRes]) => {
        let taskMessage = ''
        if (taskRes.core_tasks_aggregate.aggregate.count || materialRes.core_material_master.length || UomRes.core_material_master.length || materialTypeRes.core_material_master.length || ResourceStateRes.core_material_master.length || ResourceGroupRes.core_material_master.length) {
          if (taskRes.core_tasks_aggregate.aggregate.count) {
            taskMessage = `${item.name} has been attatched to ${taskRes.core_tasks_aggregate.aggregate.count} tasks. Choose a default option.`
          } else if (materialRes.core_material_master.length) {
            taskMessage = `${item.name} has been attached in ${materialRes.core_material_master.length} materials. Choose a default option.`
          } else if (UomRes.core_material_master.length) {
            taskMessage = `${item.name} has been attached in ${UomRes.core_material_master.length} materials. Choose a default option.`
          } else if (ResourceGroupRes.core_material_master.length) {
            taskMessage = `${item.name} has been attached in ${ResourceGroupRes.core_material_master.length} materials. Choose a default option.`
          } else if (ResourceStateRes.core_material_master.length) {
            taskMessage = `${item.name} has been attached in ${ResourceStateRes.core_material_master.length} materials. Choose a default option.`
          } else if (materialTypeRes.core_material_master.length) {
            taskMessage = `${item.name} has been attached in ${materialTypeRes.core_material_master.length} materials. Choose a default option.`
          }
          ConfirmationDialog(`Are you sure to delete the custom list ${item.name} ?`, (res, value) => {
            if (res && value) {
              DeleteCustomListValues(item.id).then((res) => {
                let apiCall
                if (taskRes.core_tasks_aggregate.aggregate.count) {
                  apiCall = ChangeDefaultCustomValue
                } else if (materialRes.core_material_master.length) {
                  apiCall = UpdateMaterialCustomListValue
                } else if (UomRes.core_material_master.length) {
                  apiCall = UpdateUOMCustomValue
                }
                apiCall(item.id, value).then(res => {
                  this.$emit('update', { id: this.selected.id, index: this.selected.index })
                })
              }).catch(err => {
                this.$notify.alert(err?.message ?? 'Something went wrong')
              })
            }
          }, undefined, undefined, undefined, taskMessage, true, transformedArray)
          if (item !== 1) {

          }
        } else {
          ConfirmationDialog(`Are you sure to delete the custom list ${item.name} ?`, res => {
            if (res) {
              DeleteCustomListValues(item.id).then((res) => {
                this.$emit('update', { id: this.selected.id, index: this.selected.index })
              }).catch(err => {
                this.$notify.alert(err?.message ?? 'Something went wrong')
              })
            }
          })
        }
      })
    },
    cancelEdit () {
      this.selectToEdit = null
      this.editName = ''
    },
    cancelAddNew () {
      this.addNew = false
      this.newName = ''
    },
    addNewRow () {
      if (!this.newName) {
        alert('Please enter name')
        return
      }
      CreateCustomListValues([{
        custom_list_id: this.selected.id,
        name: this.newName.trim()
      }]).then((res) => {
        if (res?.insert_custom_list_values?.affected_rows === 0) {
          this.$notify.alert('Custom list value already exists')
          return
        }
        this.$emit('update', { id: this.selected.id })
        this.addNew = false
        this.newName = ''
      }).catch(err => {
        if (err?.message?.includes('Uniqueness violation')) {
          this.$notify.alert('Custom list value already exists')
        } else {
          this.$notify.alert(err?.message ?? 'Something went wrong')
        }
      })
    }
  },
  watch: {
    'selected.id' () {
      this.addNew = false
      this.selectToEdit = null
      this.editName = ''
    }
  }
}
</script>

<style lang="scss" scoped >
.disabled {
    opacity: 0.5;
    box-shadow: none;
    cursor: not-allowed;
    pointer-events: none;
}
.list-detail {
  height: 100%;
  overflow: auto;
  &-bar {
    border-bottom: var(--border);
  }
  &-table {
    .col-sl {
      width: 50px;
    }
    .col-action {
      width: 60px;
      img {
        width: 20px;
        cursor: pointer;
      }
    }
    .col-name {
      input {
        width: 100%;
        border: 1px solid var(--brand-color);
        padding: 4px 5px;
        border-radius: 4px;
        box-shadow: 0 0 3px var(--brand-color);
      }
    }
  }
}
</style>
