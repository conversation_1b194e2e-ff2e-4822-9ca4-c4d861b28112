<template>
  <div v-if="loadingBomDetail" class="fh center">
    <loading-circle />
  </div>
  <div v-else class="bom-compare">
    <div class="bom-compare-bar v-center space-between px-3">
      <h1 class="weight-500 xxl v-center">
        <img
          class="mr-3"
          src="~@/assets/images/icons/arrow-back.svg"
          width="30px"
          alt=""
          @click="goBack"
        />
        BOM Compare ({{ bomDetail.name }})
      </h1>
      <div class="bom-compare-action m v-center">
        <label>From</label>
        <span :value="bomVersionId" v-if="bomVersionId" class="default-version" >V-{{versionNo}}</span>
       <span v-else>
        <select
          class="bom-compare-version"
          v-if="bomVersionList.length"
          v-model="leftVersion"
        >
          <option
            v-for="bomVersion in bomVersionList"
            :key="bomVersion.id"
            :value="bomVersion.id"
          >
            V-{{ bomVersion.version_no }}
            {{ bomVersion.active ? "(Latest)" : "" }}
          </option>
        </select>
      </span>
        <label class="ml-5">To</label>
        <select
          class="bom-compare-version"
          v-if="bomVersionList.length"
          v-model="rightVersion"
        >
          <option
            v-for="bomVersion in bomVersionList"
            :key="bomVersion.id"
            :value="{id:bomVersion.id,versionNo:bomVersion.version_no || false,active:bomVersion?.active|| false}"
          >
            V-{{ bomVersion.version_no }}
            {{ bomVersion.active ? "(Latest)" : "" }}
          </option>
        </select>
      </div>
    </div>

    <div class="bom-compare-container">
      <compare
        :leftVersion="leftVersion"
        :rightVersion="rightVersion?.id"
        :versionNoRight="rightVersion?.versionNo"
        :active="rightVersion?.active"
        :compareAndUpdate="compareAndUpdate"
        ref="compare"
      />
    </div>
  </div>
</template>

<script>
import { GetBomDetailById, GetAllBomVersionListByBomId } from '@/api'
import loadingCircle from '../../../components/common/loadingCircle.vue'
import Compare from '../../../components/bom/compare/compare.vue'
export default {
  components: { loadingCircle, Compare },
  name: 'BomCompare',
  data: () => ({
    bomId: null,
    loadingBomDetail: false,
    bomDetail: null,
    bomVersionList: [],
    // updation of riht version chnaged from id to object whcih includes {{id:bomVersion.id,versionNo:bomVersion.version_no,active:bomVersion.active}
    rightVersion: null,
    leftVersion: null,
    // bomVersionId and vesrionNo are the states which only have the data when its coming from updating out  of date boms
    bomVersionId: null,
    versionNo: null,
    compareAndUpdate: null
  }),
  methods: {
    goBack () {
      this.$router.go(-1)
    },
    getBomDetail () {
      this.loadingBomDetail = true
      const getBomDetailById = GetBomDetailById(this.bomId)
      const getAllBomVersionListByBomId = GetAllBomVersionListByBomId(
        this.bomId
      )
      Promise.all([getBomDetailById, getAllBomVersionListByBomId])
        .then((res) => {
          this.bomDetail = res[0].core_bom_by_pk
          this.bomVersionList = res[1].bom_versions
          this.loadingBomDetail = false
        })
        .catch(() => {
          this.loadingBomDetail = false
        })
    }
  },
  created () {
    this.bomId = this.$route.params.bomId
    this.bomVersionId = this.$route?.query?.bvid
    this.versionNo = this.$route?.query?.bvno
    this.leftVersion = this.bomVersionId
    this.compareAndUpdate = this.$route?.query?.compareAndUpdate
    this.getBomDetail()
  }
}
</script>

<style lang="scss" scoped >
.bom-compare {
  height: 100%;
  &-bar {
    height: 60px;
    margin: -12px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  &-action {
    margin-right: 20px;
    label {
      font-size: 14px;
      color: var(--text-color);
      margin-bottom: 5px;
      display: block;
    }
    .default-version, select {
      margin: 0 10px;
      font-size: 12px;
      background-color: var(--brand-light-color);
      line-height: 1;
      border: 1px solid var(--brand-color);
      border-radius: 4px;
      padding: 4px 12px;
    }
  }
  &-container {
    height: calc(100% - 60px);
    overflow: auto;
  }
}
</style>
