<template>
<div class="company">
    <div class="company-nav py-2 v-center space-between">
      <h1>Company Dashboard</h1>
      <div class="s">
        <button class="btn mx-2" @click="openUserForm = !openUserForm">Add User</button>
        <button class="btn" @click="$router.push('/')" >Add Company</button>
      </div>
    </div>
    <div class="company-kpi py-3 flex">
      <div class="company-kpi-blue p-4">
        <div class="company-kpi-header">Total Projects</div>
        <div class="company-kpi-value mt-3">100</div>
      </div>
      <div class="company-kpi-blue mx-4 p-4">
        <div class="company-kpi-header">Total company</div>
        <div class="company-kpi-value mt-3">50</div>
      </div>
      <div class="company-kpi-brand p-4">
        <img src="~@/assets/images/bg-2.png" alt="" />
        <div class="company-kpi-header">Activity</div>
        <div class="company-kpi-value mt-3">
          Here Projects and company Activity will come in graph format
        </div>
      </div>
    </div>
    <div class="company-invite py-3">
      <h2>Invite your users here</h2>
      <div :class="openUserForm ? 'open-form mt-4' : 'mt-4'" >
        <div class="pb-3" >
          <user-table
            :userList="userList"
            :perPage="perPage"
            :pageNumber="pageNumber"
            :showHeader="true"
          />
          <pagination
            :length="userList.length"
            :perPage="perPage"
            :pageNumber="pageNumber"
            @selectPage="selectPage"
            class="mt-3 mx-2"
          />
        </div>
        <div class="s p-4 invite-user__form" >
          <invite-user />
        </div>
      </div>
    </div>
</div>
</template>

<script>
import UserTable from '../../components/common/userTable.vue'
import Pagination from '../../components/common/pagination.vue'
import InviteUser from '../../components/manage/inviteUser.vue'
export default {
  components: { UserTable, Pagination, InviteUser },
  name: 'companyDashboard',
  data () {
    return {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      companyName: '',
      companyGstNumber: '',
      companyPanNumber: '',
      additionalNotes: '',

      // components state
      openUserForm: false,
      userList: [
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
      ],
      pageNumber: 1,
      perPage: 10
    }
  },
  methods: {
    selectPage (pageNumber) {
      this.pageNumber = pageNumber
    },
    tryInvitecompany () {}
  }
}
</script>

<style lang="scss" scoped >
.company {
  height: 100%;
  &-nav {
    border-bottom: var(--border);
    h1 {
      font-weight: 500;
    }
  }
  &-kpi {
    align-items: stretch;
    color: var(--white);
    font-weight: 600;
    border-bottom: var(--border);
    &-blue {
      height: 180px;
      width: 225px;
      background-image: url("~@/assets/images/bg-1.png");
      border-radius: 6px;
    }
    &-brand {
      position: relative;
      flex-grow: 1;
      border-radius: 6px;
      background: linear-gradient(
        317.26deg,
        #dfa600 -26.68%,
        #ffc467 42.21%,
        #e8892a 90.37%
      );
      & img {
        position: absolute;
        bottom: 10px;
        right: 10px;
        height: 160px;
      }
      & .company-kpi-value {
        font-size: 20px;
      }
    }
    &-header {
      font-size: 24px;
    }
    &-value {
      font-size: 64px;
    }
  }
  &-invite {
    height: calc(100% - 250px);
    & h2 {
      font-weight: 500;
    }
    &--form {
      height: calc(100vh - 390px);
      overflow: auto;
      background: var(--white);
      padding: 20px;
      & > div {
        max-width: 800px;
        margin: auto;
      }
    }
  }
  .invite-user__form {
    display: none;
  }
  .open-form {
    height: calc(100% - 65px);
    display: grid;
    grid-template-columns: 1.5fr 1fr;
    grid-gap: 20px;
    & > div {
      height: 100%;
      border: 1px solid #38333233;
      border-radius: 4px;
      overflow: auto;
    }
    .invite-user__form {
      display: block;
    }
  }
}
</style>
