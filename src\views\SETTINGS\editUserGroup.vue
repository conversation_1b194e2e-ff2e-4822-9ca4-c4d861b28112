<template>
    <div class="user-groups fh">
      <div class="user-groups-bar v-center space-between px-5">
        <div class="flex v-center">
          <img src="~@/assets/images/icons/arrow-back.svg" class="mr-3 pointer" width="30px" height="20px" alt="" @click="$router.go(-1)"/>
          <h1 class="weight-500 xl">
            <input
              class="name-input weight-500 xl"
              @change="isNameChanged = true"
              v-model="userGroup.name"
              autofocus
            />
          </h1>
        </div>
        <div class="v-center">
          <router-link to="/settings/user-groups">
            <button class="btn btn-black mr-3">Cancel</button>
          </router-link>
          <button class="btn" @click="updateUserGroupDetails" :disabled="disableUpdateButton">Update</button>
        </div>
      </div>
      <div class="fh center" v-if="loading">
          <loading-circle />
          </div>
      <div class="user-groups-container">
        <div class="user-groups-description px-5 mt-3 input-group imp">
            <label for="description" class="l">Description:</label>
          <textarea
            @change="isDescriptionChanged = true"
            v-model="userGroup.description"
            placeholder="Description"
            class="form-control"
            rows="3"
          >
          </textarea>
        </div>

        <!-- Dual Select Container -->
        <div class="dual-select-container">
          <div class="select-box">
            <h3>Available Users</h3>
            <input
              class="search-box"
              v-model="searchAvailable"
              placeholder="Search users..."
            />
            <ul>
              <li
                v-for="user in filteredAvailableUsers"
                :key="user.id"
                @click="selectUser(user)"
              >
                <div class="user-info">
                  <span class="user-name">{{ user.name }}</span>
                  <span class="user-details"
                    >{{ user.email }} | {{ user.role }}</span
                  >
                </div>
                <span class="add-icon">
                  <img
                    src="~@/assets/images/icons/plus-icon.svg"
                    width="20px"
                    alt=""
                  />
                </span>
              </li>
            </ul>
          </div>
          <div class="select-box">
            <h3>Selected Users</h3>
            <input
              class="search-box"
              v-model="searchSelected"
              placeholder="Search users..."
            />
            <ul>
              <li
                v-for="user in filteredSelectedUsers"
                :key="user.id"
                @click="deselectUser(user)"
              >
                <div class="user-info">
                  <span class="user-name">{{ user.name }}</span>
                  <span class="user-details"
                    >{{ user.email }} | {{ user.role }}</span
                  >
                </div>
                <span class="remove-icon">
                  <img
                    src="~@/assets/images/icons/close-icon.svg"
                    width="20px"
                    alt=""
                  />
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </template>

<script>
import { mapGetters } from 'vuex'
import { GetUserGroupDetail, UpdateUserGroup, AddUsersToUserGroup, RemoveUsersFromUserGroup, findExistingUserGroup, restoreUserGroup } from '@/api'
import { alert, success } from '@/plugins/notification'
import LoadingCircle from '@/components/common/loadingCircle.vue'
import confirmationDialog from '@/plugins/confirmationDialog'

export default {
  name: 'user-groups-edit',
  components: { LoadingCircle },
  data: () => ({
    loading: false,
    userGroup: {
      id: '',
      name: '',
      description: '',
      users: []
    },
    searchAvailable: '',
    searchSelected: '',
    availableUsers: [],
    selectedUsers: [],
    previouslySelectedUsers: [],
    deletedUsers: new Set(),
    insertedUsers: new Set(),
    isNameChanged: false,
    isDescriptionChanged: false,
    disableUpdateButton: false
  }),
  computed: {
    ...mapGetters(['tenantUsersList']),
    filteredAvailableUsers () {
      return this.availableUsers.filter(
        (user) =>
          user.name
            .toLowerCase()
            .includes(this.searchAvailable.toLowerCase()) ||
          user.email
            .toLowerCase()
            .includes(this.searchAvailable.toLowerCase()) ||
          user.role.toLowerCase().includes(this.searchAvailable.toLowerCase())
      )
    },
    filteredSelectedUsers () {
      return this.selectedUsers.filter(
        (user) =>
          user.name.toLowerCase().includes(this.searchSelected.toLowerCase()) ||
          user.email
            .toLowerCase()
            .includes(this.searchSelected.toLowerCase()) ||
          user.role.toLowerCase().includes(this.searchSelected.toLowerCase())
      )
    }
  },
  methods: {
    validateData () {
      if (!this.userGroup.name.length) {
        alert('Please enter a name for the user group')
        return false
      }
      if (!this.selectedUsers.length) {
        alert('Please select at least one user')
        return false
      }
      if (!this.userGroup.description.length) {
        alert('Please enter a description for the user group')
        return false
      }
      return true
    },
    async updateUserGroupDetails () {
      try {
        if (!this.validateData()) return
        this.disableUpdateButton = true
        const promiseArr = []
        if (this.isNameChanged || this.isDescriptionChanged) {
          const res = await findExistingUserGroup(this.userGroup.name)
          if (res.core_user_group.length && res.core_user_group[0]?.id !== this.userGroup?.id) {
            if (res.core_user_group[0].deleted) {
              confirmationDialog(
                'There is a deleted user group with the same name. Do you want to restore it?',
                (res) => {
                  if (res) {
                    restoreUserGroup(this.newGroup.name).then(
                      () => {
                        this.$router.push('/settings/user-groups')
                      }).catch(() => {
                      alert('Unable to restore user group')
                    })
                  }
                }
              )
            } else {
              alert('User group name already exists')
            }
          } else {
            promiseArr.push(UpdateUserGroup(this.userGroup.id, this.userGroup.name, this.userGroup.description))
            if (this.deletedUsers.size) {
              const deletedUsersArray = Array.from(this.deletedUsers)
              promiseArr.push(RemoveUsersFromUserGroup(deletedUsersArray, this.userGroup.id))
            }
            if (this.insertedUsers.size) {
              const insertedUsersArray = Array.from(this.insertedUsers)
              promiseArr.push(AddUsersToUserGroup(insertedUsersArray))
            }
            await Promise.all(promiseArr)
            success('User group updated successfully')
            this.$router.push('/settings/user-groups')
          }
        } else {
          promiseArr.push(UpdateUserGroup(this.userGroup.id, this.userGroup.name, this.userGroup.description))
          if (this.deletedUsers.size) {
            const deletedUsersArray = Array.from(this.deletedUsers)
            promiseArr.push(RemoveUsersFromUserGroup(deletedUsersArray, this.userGroup.id))
          }
          if (this.insertedUsers.size) {
            const insertedUsersArray = Array.from(this.insertedUsers)
            promiseArr.push(AddUsersToUserGroup(insertedUsersArray))
          }
          await Promise.all(promiseArr)
          success('User group updated successfully')
          this.$router.push('/settings/user-groups')
        }
      } catch (err) {
        if (err.message.includes('Uniqueness violation')) {
          alert('User group name already exists')
        } else {
          alert('Unalbe to update user group')
        }
      } finally {
        this.disableUpdateButton = false
      }
    },
    selectUser (user) {
      this.selectedUsers.push(user)
      this.availableUsers = this.availableUsers.filter((u) => u.id !== user.id)
      if (!this.previouslySelectedUsers.find((u) => u.id === user.id)) {
        this.insertedUsers.add({ user_id: user.id, group_id: this.userGroup.id })
      } else {
        this.deletedUsers.delete(user.id)
      }
    },
    deselectUser (user) {
      this.availableUsers.push(user)
      this.selectedUsers = this.selectedUsers.filter((u) => u.id !== user.id)
      if (this.previouslySelectedUsers.find((u) => u.id === user.id)) {
        this.deletedUsers.add(user.id)
      } else {
        this.insertedUsers.delete({ user_id: user.id, group_id: this.userGroup.id })
      }
    }
  },
  mounted () {
    this.loading = true
    this.id = this.$route.params.id
    GetUserGroupDetail(this.id).then((res) => {
      this.userGroup.id = this.id
      this.userGroup.name = res.core_user_group_by_pk.name
      this.userGroup.description = res.core_user_group_by_pk.description
      this.selectedUsers = res.core_user_group_by_pk.core_user_group_members.map(
        (user) => ({
          id: user.user_id,
          name: `${user.core_user.first_name} ${user.core_user.last_name}`,
          email: user.core_user.email
        })
      )
      this.previouslySelectedUsers = [...this.selectedUsers]
      for (const user of this.tenantUsersList) {
        if (user.status === 1) {
          let isSelected = false
          for (const selectedUser of this.selectedUsers) {
            if (user.associated_user.id === selectedUser.id) {
              selectedUser.role = user.associated_role.name
              isSelected = true
            }
          }
          if (!isSelected) {
            this.availableUsers.push({
              id: user.associated_user.id,
              name: `${user.associated_user.first_name} ${user.associated_user.last_name}`,
              email: user.associated_user.email,
              role: user.associated_role.name
            })
          }
        }
      }
    }).catch(() => {
      alert('Unable to fetch user group details')
    }).finally(() => {
      this.loading = false
    })
  }
}
</script>

  <style lang="scss" scoped>
  textarea {
    width: 100%;
    height: 50px;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 10px;
    font-size: 16px;
    font-family: Arial, sans-serif;
    outline: none;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease-in-out;
    resize: none;
  }
  textarea:focus {
    border-color: var(--bg-color);
    box-shadow: 2px 2px 10px rgba(0, 123, 255, 0.2);
  }
  .user-groups {
    &-bar {
      height: 60px;
      margin-bottom: 0;
      background: var(--bg-color);
      border-bottom: var(--border);
    }
    .name-input {
      border: none;
      border-bottom: solid 0.5px rgba(0, 0, 0, 0.7);
      outline: none;
      padding: 5px;
      max-width: 100%;
      background: transparent;
      &:focus {
        border: solid 0.5px rgba(0, 0, 0, 0.7);
        border-radius: 5px;
      }
    }
    .dual-select-container {
      display: flex;
      justify-content: space-between;
      padding: 20px;
      gap: 20px;
    }
    .select-box {
      width: 50%;
      border: 1px solid #ddd;
      padding: 15px;
      border-radius: 12px;
      background: #fefefe;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }
    .search-box {
      width: 100%;
      padding: 8px;
      margin-bottom: 10px;
      border: 1px solid #ccc;
      border-radius: 6px;
      outline: none;
    }
    h3 {
      text-align: center;
      margin-bottom: 10px;
      font-weight: 600;
      color: #333;
    }
    ul {
      list-style: none;
      padding: 0;
      height: 19rem;
      overflow-y: auto;
    }
    li {
      padding: 12px;
      cursor: pointer;
      border-radius: 6px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: background 0.2s ease-in-out;
    }
    li:hover {
      background: #f5f5f5;
    }
    .user-info {
      display: flex;
      flex-direction: column;
      line-height: 1.4;
    }
    .user-name {
      font-weight: 600;
      color: #222;
    }
    .user-details {
      font-size: 12px;
      color: #666;
    }
    .add-icon,
    .remove-icon {
      font-size: 14px;
      padding: 4px 8px;
      border-radius: 6px;
      background: #e0e0e0;
      transition: background 0.2s ease-in-out;
    }
    .add-icon:hover {
      background: #4caf50;
      color: white;
    }
    .remove-icon:hover {
      background: #d32f2f;
      color: white;
    }
  }
  </style>
