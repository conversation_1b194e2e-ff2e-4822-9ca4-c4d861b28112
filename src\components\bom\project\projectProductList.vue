<template>
  <div class="product-code">
        <div class="product-code__header s  ">
     <div class="space-between p-2 v-center">
      <h2 class="weight-500 l">Product Code</h2>
     </div>
     <div class=" m prod-code-search">
        <input
            type="text"
            @input="updateOnSearch"
            placeholder="Search BOM here"
            v-model="searchKeyWord"
          />
        </div>
    </div>
    <project-code-item v-for="item in productCodeList" :key="item.product_code" :productObject="item" />
  </div>
</template>

<script>
import { GetAllChildProductForProjectBom } from '@/api'
import ProjectCodeItem from './projectCodeItem.vue'
export default {
  name: 'ProjectCodeList',
  components: {
    ProjectCodeItem
  },
  data: () => ({
    productCodeList: [],
    searchKeyWord: ''
  }),
  computed: {
    bomVersionId () {
      return this.$route.query.bomVersionId
    }
  },
  watch: {
    bomVersionId (value) {
      this.getAllProductCodeList()
    }
  },
  mounted () {
    this.updateOnSearch()
  },
  methods: {
    updateOnSearch () {
      this.searchKeyWord = (this.searchKeyWord).toLowerCase()
      if (this.searchKeyWord) {
        this.$store.dispatch('projectBom/addBomSearchKeyword', this.searchKeyWord)
        return this.getAllProductCodeList()
      } else {
        this.$store.dispatch('projectBom/addBomSearchKeyword', '')
      }
    },
    getAllProductCodeList () {
      if (!this.bomVersionId) {
        this.productCodeList = []
        return
      }
      GetAllChildProductForProjectBom(this.bomVersionId).then(res => {
        this.productCodeList = res.bom_versions_by_pk.bom_items.map(item => {
          return {
            product_code_name: item?.core_material?.material_product_code?.product_code,
            product_code: item?.core_material?.material_product_code?.id,
            associated_bom_id: item?.associated_bom_version?.core_bom?.id,
            associated_bom_name: item?.associated_bom_version?.core_bom?.name,
            associated_bom_version_id: item?.associated_bom_version?.id,
            associated_bom_version_name: item?.associated_bom_version?.version_no
          }
        })
      })
    }
  },
  created () {
    this.getAllProductCodeList()
  }
}
</script>

<style lang="scss" scoped >
.product-code {
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: var(--bg-color);
  border-right: 1px solid var(--brand-color);
  position: relative;
  &__header {
    position: sticky;
    top: 0;
    z-index: 1;
    background-color: var(--brand-color);
    display: flex;
    flex-direction: column;
& .prod-code-search{
  background-color: white;
  position: relative;
  margin:0px 5px 5 5px;
  border-radius:5px ;
  border: 1px solid rgba(59, 59, 59, 0.4666666667);
    display: block;
    width: 99%;
    border-radius: 0.285em;
    font-size: 1em;
    height: 2.5em;
    margin-bottom: 4px;
        & input{
      all:none;
      height: 100%;
      border:none;
      padding-inline: 5px;
    }
}
  }
}
</style>
