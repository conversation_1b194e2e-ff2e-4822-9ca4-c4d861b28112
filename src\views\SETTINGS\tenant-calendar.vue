<template>
  <div>
    <div v-if="loading" class="center loader">
      <loading-circle />
    </div>
    <div>
  <div class="main-container">
        <div class="calendar-wrapper">
          <!-- Left Calendar Section -->
          <div class="calendar-section">
          <div class="calendar-header">
            <div class="title weight-500 xxxl">Tenant Level Calendar</div>
            <div class="header-controls">
              <select v-model="selectedMonth" @change="onDateChange">
                <option
                  v-for="(month, index) in monthNames"
                  :value="index"
                  :key="index"
                >
                  {{ month }}
                </option>
              </select>
              <select v-model="selectedYear" @change="onYearChange">
                <option v-for="year in years" :value="year" :key="year">
                  {{ year }}
                </option>
              </select>
          <div class="toggle-btn">
            <button :class="['toggle-option', { active: currentView === 'month' }]" @click="toggleView('month')">Month</button>
            <button :class="['toggle-option', { active: currentView === 'year' }]" @click="toggleView('year')">Year</button>
        </div>
        </div>
        </div>

          <!-- Month View -->
          <div v-if="currentView === 'month'">
            <div class="calendar-">
            <div class="calendar-grid">
              <div class="weekday" v-for="day in weekdays" :key="day">{{ day }}</div>
              <div
                v-for="(box, index) in dayBoxes"
                :key="index"
                class="day-box"
                :class="{ empty: !box.day, holiday: isHoliday(box.day), 'non-working-days': box.day && isNonWorkingDay(selectedYear, selectedMonth, box.day) }"
              >
                {{ box.day }}
              </div>
            </div>
          </div>
          </div>

          <!-- Year View -->
          <div v-else class="year-grid">
            <div
              class="month-box"
              v-for="(month, idx) in monthNames"
              :key="idx"
              @click="onMonthClick(idx)"
            >
              {{ month }}
            </div>
          </div>

<div class="working-days-container">
  <div class="flex justify-between items-center">
    <div class="weight-500 xxl">Working Days</div>
    <br>
    <button class="btn ml-5" @click="startEditing" v-if="!isEditing && isTenantAdmin">Edit</button>
  </div>

  <div class="working-days">
    <div
      class="day-checkbox"
      v-for="(day, index) in weekdays"
      :key="index"
    >
      <label>
        <input
          class="range"
          type="checkbox"
          v-model="workingDays[day]"
          :disabled="!isEditing"
        />
        {{ weekdayFullNames[day] }}
      </label>
    </div>
  </div>

  <!-- Global Time Range Input -->
  <div class="global-time-range">
    <label>Working Hours for Selected Days:</label>
    <div class="time-inputs">
      <input class="range" type="time" v-model="workingHours.start" :disabled="!isEditing" />
      <span>to</span>
      <input class="range" type="time" v-model="workingHours.end" :disabled="!isEditing" />
      <span class="info-icon" :title="'Working hours:' + getWorkingHours()">(?)</span>
    </div>
  </div>

  <!-- Save Button -->
  <button class="btn btn-black mt-5 mr-5" @click="cancelEdit" v-if="isEditing">Cancel</button>
  <button class="btn mt-5" @click="saveWorkingDays" :disabled="disableSaveButton" v-if="isEditing">Save</button>
</div>

        </div>

        <!-- Right Holiday Section -->
        <div class="holiday-section">
  <div class="weight-500 xxl">Holidays</div>
  <br>
  <div class="holiday-boxes">
<div
  v-for="(holiday, index) in holidays"
  :key="holiday.id"
  class="holiday-box flex justify-between items-center p-2 border rounded mb-2"
>
<div class="holiday-actions" v-if="isTenantAdmin">
    <img
      @click="editHoliday(index, holiday)"
      class="holiday-action-icon"
      src="~@/assets/images/pencil.svg"
      alt="Edit"
    />
    <img
      @click="deleteHoliday(index, holiday)"
      class="holiday-action-icon"
      src="~@/assets/images/trash-2.svg"
      alt="Delete"
    />
  </div>  <div>
    <div class="holiday-name">{{ holiday.name }}</div>
    <div class="holiday-date">{{ holiday.date }}</div>
  </div>
</div>

  </div>
  <button
    v-if="isTenantAdmin"
    @click="isAddHoliday = true"
    class="btn btn-black pointer"
  >
    + Add Holiday
  </button>
</div>
      </div>
    </div>
    </div>
    <modal
    :title="'Add Holiday'"
    :open="isAddHoliday === true"
    @close="closeAddHoliday"
    >
  <div style="display: flex; align-items: center; gap: 12px;">
  <label>Name: <input class="range" v-model="newHoliday.name" type="text" style="margin-left: 4px;" /></label>
  <label>Date: <input class="range" v-model="newHoliday.date" type="date" style="margin-left: 4px;" /></label>
  <div style="margin-left: auto; display: flex; gap: 8px;">
    <button class="btn" :disabled="!canSaveHoliday || disableHolidaySaveButton" @click="saveHoliday">Save</button>
    <button class="btn btn-black" @click="cancelHoliday">Cancel</button>
  </div>
</div>
    </modal>
  <modal
  :title="'Edit Holiday'"
  :open="isEditHoliday"
  @close="closeEditHoliday"
  >
    <div  style="display: flex; align-items: center; gap: 12px;">
        <label>
          Holiday Name:
          <input
            v-model="editedHolidayName"
            class="range"
            type="text"
            :placeholder="'Edit holiday name'"
            @input="enableUpdateButton"
          />
        </label>
        <label>Date: <input @input="enableUpdateButton" class="range" v-model="editedHolidayDate" type="date" style="margin-left: 4px;" /></label>
        <div class="modal-actions" style="display: flex; gap: 10px; justify-content: flex-end;">
          <button class="btn btn-black" @click="cancelHolidayEdit">Cancel</button>
          <button
            class="btn"
            :disabled="!isEditEnabled"
            @click="updateHoliday"
          >
            Update
          </button>
        </div>
      </div>
  </modal>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Modal from '@/components/common/modal.vue'
import ConfirmationDialog from '@/plugins/confirmationDialog'
import LoadingCircle from '@/components/common/loadingCircle.vue'
import { GetCalendarList, AddHolidayInCalendar, EditCalendar, UpdateWorkingDays, DeleteCalendarById, UpdateHoliday } from '@/api'
import { alert, success } from '@/plugins/notification'
// import { alert } from '@/plugins/notification'

export default {
  components: {
    LoadingCircle,
    Modal
  },
  name: 'copy-calendar-settings',
  data () {
    const today = new Date()
    const weekdays = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN']
    const fullWeekDays = ['Monday', 'Tuedsay', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    const workingDays = {}
    weekdays.forEach(day => { workingDays[day] = false })

    return {
      disableSaveButton: false,
      disableHolidaySaveButton: false,
      editId: null,
      editedHolidayDate: null,
      backupWorkingDays: {},
      backupWorkingHours: {},
      weekdayFullNames: {
        MON: 'Monday',
        TUE: 'Tuesday',
        WED: 'Wednesday',
        THU: 'Thursday',
        FRI: 'Friday',
        SAT: 'Saturday',
        SUN: 'Sunday'
      },
      indexToDayKey: {
        0: 'SUN',
        1: 'MON',
        2: 'TUE',
        3: 'WED',
        4: 'THU',
        5: 'FRI',
        6: 'SAT'
      },
      isEditHoliday: false,
      editedHolidayName: '',
      isEditEnabled: false,
      holidayIndex: null,
      isEditing: false,
      calendarWorkingDays: [],
      calendarData: {},
      newHoliday: {},
      isAddHoliday: false,
      loading: false,
      currentView: 'month',
      weekdays,
      fullWeekDays,
      currentDate: today,
      selectedMonth: today.getMonth(),
      selectedYear: today.getFullYear(),
      todayYear: today.getFullYear(),
      todayMonth: today.getMonth(),
      dayBoxes: [],
      workingDays,
      workingHours: {
        start: '09:00',
        end: '17:00'
      },
      monthNames: [
        'January', 'February', 'March', 'April',
        'May', 'June', 'July', 'August',
        'September', 'October', 'November', 'December'
      ],
      holidays: []
    }
  },
  computed: {
    ...mapGetters([
      'isTenantAdmin',
      'isOnProjectLevel'
    ]),
    canSaveHoliday () {
      return this.newHoliday?.name?.trim() && this.newHoliday.date
    },
    years () {
      const years = []
      for (let y = this.todayYear - 10; y <= this.todayYear; y++) {
        years.push(y)
      }
      return years
    }
  },
  mounted () {
    this.generateCalendar()
  },
  methods: {
    getWorkingHours () {
      if (this.workingHours.start && this.workingHours.end) {
        const start = this.convertToMinutes(this.workingHours.start)
        const end = this.convertToMinutes(this.workingHours.end)
        return ((end - start) / 60).toFixed(2) // Returning the working hours in decimals
      }
      return 0
    },

    convertToMinutes (time) {
      const [hours, minutes] = time.split(':').map(Number)
      return hours * 60 + minutes
    },
    closeEditHoliday () {
      this.editedHolidayName = ''
      this.isEditHoliday = false
    },
    closeAddHoliday () {
      this.newHoliday = {}
      this.isAddHoliday = false
    },
    startEditing () {
      this.isEditing = true
      this.backupWorkingDays = JSON.parse(JSON.stringify(this.workingDays))
      this.backupWorkingHours = JSON.parse(JSON.stringify(this.workingHours))
    },
    cancelEdit () {
      this.isEditing = false
      this.workingDays = JSON.parse(JSON.stringify(this.backupWorkingDays))
      this.workingHours = JSON.parse(JSON.stringify(this.backupWorkingHours))
    },
    isNonWorkingDay (year, month, day) {
      if (!day) return false

      const date = new Date(year, month, day)
      const weekdayIndex = date.getDay()

      const weekday = this.indexToDayKey[weekdayIndex]
      return !this.workingDays[weekday] // true if it's a non-working day
    },
    deleteHoliday (key, value) {
      ConfirmationDialog(`Are you sure you want to delete the holiday ${value.name} ?`, (res) => {
        if (res) {
          DeleteCalendarById(value.id, false).then(res => {
            if (res) {
              success('Holiday Deleted successfully')
              this.generateCalendar()
            } else {
              alert('Something went wrong.Please try again')
            }
          }).catch(err => {
            alert(`${err.message}`)
          })
        }
      }, '', '', 'Delete Holiday')
    },
    saveHoliday () {
      if (this.disableHolidaySaveButton) return
      this.disableHolidaySaveButton = true
      const newName = this.newHoliday.name.trim()

      const nameExists = this.holidays.some(holiday => holiday.name.trim() === newName)
      const dateExists = this.holidays.some(holiday => {
        console.log(holiday.date, 'are they same', this.formatHolidayDate(this.newHoliday.date))
        return holiday.date === this.formatHolidayDate(this.newHoliday.date)
      })
      console.log(dateExists, 'does the date match?')
      if (dateExists) alert('Holiday on this day already exists')
      if (nameExists) {
        alert('Holiday name already exists')
        this.disableHolidaySaveButton = false
        return
      }
      const data = {
        calendar_id: this.calendarData.id,
        name: this.newHoliday.name,
        date: this.newHoliday.date
      }
      AddHolidayInCalendar(data, false).then(res => {
        console.log('holiday added')
        success('Holiday added successfully')
        this.newHoliday = {}
        this.isAddHoliday = false
        this.generateCalendar()
      }).finally(() => {
        this.disableHolidaySaveButton = false
      })
    },
    cancelHoliday () {
      this.newHoliday = {}
      this.isAddHoliday = false
    },
    editHoliday (index, holiday) {
      this.isEditHoliday = true
      this.holidayIndex = index
      this.editId = holiday.id
      this.editedHolidayName = this.holidays[index].name
      this.editedHolidayDate = new Date(this.holidays[index].date).toLocaleDateString('en-CA')
      this.isEditEnabled = false
    },

    closeModal () {
      this.isEditHoliday = false
      this.editedHolidayName = ''
    },

    enableUpdateButton () {
      this.isEditEnabled = this.editedHolidayName.trim() !== '' || this.editedHolidayDate
    },

    cancelHolidayEdit () {
      this.editedHolidayName = ''
      this.isEditHoliday = false
    },

    updateHoliday () {
      const newName = this.editedHolidayName.trim()

      const nameExists = this.holidays.some(holiday => holiday.name.trim() === newName)

      if (nameExists) {
        alert('Holiday name already exists')
        return
      }
      const data = { name: this.editedHolidayName, date: this.editedHolidayDate }
      UpdateHoliday(data, this.editId, false).then(res => {
        if (res) {
          success('Name changed successfully')
          this.generateCalendar()
          this.closeModal()
        }
      }).catch(err => {
        console.log(err)
      })
      if (this.editedHolidayName.trim()) {
        this.holidays[this.holidayIndex].name = this.editedHolidayName.trim()
      }
    },
    formatHolidayDate (dateString) {
      const options = { year: 'numeric', month: 'long', day: 'numeric' }
      return new Date(dateString).toLocaleDateString('en-US', options)
    },
    toggleView (view) {
      this.currentView = view
    },
    onYearChange () {
      this.onDateChange()
    },
    onDateChange () {
      this.currentDate = new Date(this.selectedYear, this.selectedMonth, 1)
      this.generateCalendar()
    },
    onMonthClick (monthIndex) {
      this.selectedMonth = monthIndex
      this.currentView = 'month'
      this.generateCalendar()
    },
    generateCalendar () {
      this.loading = true
      const year = this.currentDate.getFullYear()
      const month = this.currentDate.getMonth()

      const firstDay = new Date(year, month, 1)
      const startDayIndex = (firstDay.getDay() + 6) % 7 // Monday = 0

      const daysInMonth = new Date(year, month + 1, 0).getDate()
      const totalBoxes = Math.ceil((startDayIndex + daysInMonth) / 7) * 7

      this.dayBoxes = Array.from({ length: totalBoxes }, (_, i) => {
        const day = i >= startDayIndex && i < startDayIndex + daysInMonth
          ? i - startDayIndex + 1
          : null
        return { day }
      })
      GetCalendarList(false).then((res) => {
        this.calendarData = JSON.parse(JSON.stringify(res.core_project_calendar?.[0]))
        const calendar = res?.core_project_calendar?.[0]
        if (!calendar) return 0
        else {
          this.calendarWorkingDays = calendar.calendar_working_days || []
          // Map working days
          const workingDaysFromBackend = calendar?.calendar_working_days || []

          // Reset all to false first
          this.weekdays.forEach(day => { this.workingDays[day] = false })

          workingDaysFromBackend.forEach(day => {
            const index = day.work_day
            const isWorking = day.deleted === false
            const dayKey = this.indexToDayKey[index]
            if (dayKey && isWorking) {
              this.workingDays[dayKey] = true
            }
          })

          // Set working hours
          this.workingHours.start = calendar?.working_hours_start?.slice(0, 5) || '09:00'
          this.workingHours.end = calendar?.working_hours_end?.slice(0, 5) || '17:00'

          // Map holidays
          const backendHolidays = calendar?.calendar_holidays || []
          this.holidays = backendHolidays.map(h => ({
            id: h.id,
            name: h.name,
            date: this.formatHolidayDate(h.date)
          }))
        }
        this.loading = false
      })
    },
    isHoliday (day) {
      if (!day) return false
      const formattedDate = `${this.selectedYear}-${String(this.selectedMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`

      return this.holidays.some(holiday => {
        const holidayDate = new Date(holiday.date).toLocaleDateString('en-CA')
        return holidayDate === formattedDate
      })
    },
    saveWorkingDays () {
      if (this.disableSaveButton) return
      this.disableSaveButton = true
      const dayKeyMap = {
        MON: 'MONDAY',
        TUE: 'TUESDAY',
        WED: 'WEDNESDAY',
        THU: 'THURSDAY',
        FRI: 'FRIDAY',
        SAT: 'SATURDAY',
        SUN: 'SUNDAY'
      }

      // Calculate working hours difference
      const startParts = this.workingHours.start.split(':').map(Number)
      const endParts = this.workingHours.end.split(':').map(Number)
      const startInHours = startParts[0] + startParts[1] / 60
      const endInHours = endParts[0] + endParts[1] / 60
      const workingHoursDiff = endInHours - startInHours

      // Construct calendarData for update
      const calendarData = {
        id: this.calendarData.id,
        name: this.calendarData.name,
        description: this.calendarData.description,
        working_hours_start: this.workingHours.start,
        working_hours_end: this.workingHours.end,
        working_hours: workingHoursDiff
      }

      const data = this.calendarWorkingDays.map(dayObj => {
        const shortKey = Object.keys(dayKeyMap).find(key => dayKeyMap[key] === dayObj.day_name)
        const isSelected = this.workingDays[shortKey] || false

        return {
          where: {
            id: { _eq: dayObj.id }
          },
          _set: {
            deleted: !isSelected
          }
        }
      })

      // Save calendar-level data
      EditCalendar(calendarData).then(() => {
        UpdateWorkingDays(data).then(res => {
          if (res) {
            this.generateCalendar()
            this.isEditing = false
            success('Updated calendar working days and hours')
          }
        }).catch(err => {
          console.log(err)
        }).finally(() => {
          this.disableSaveButton = false
        })
      }).catch(() => {
        alert('Unable to update calendar working days and hours')
        this.disableSaveButton = false
      })
    }

  }
}
</script>

<style scoped lang="scss">
.main-containe {
  max-height: 80vh;
  overflow-y: auto;
}
.calendar-wrapper {
  display: flex;
  width: 100%;
}

.calendar-section {
  width: 75%;
  padding: 1rem;
}

.calendar-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.title {
  margin-bottom: 0.5rem;
}
.range {
      padding: 8px;
      height: 100%;
      border: 0.7px solid var(--brand-color);
      border-radius: 4px;
      padding-inline: 4px;
      background: var(--brand-light-color);
}
.range:disabled {
  background-color: #f0f0f0;
  color: #999;
  cursor: not-allowed;
}

.header-controls {
  padding-top: 5px;
  display: flex;
  justify-content: end;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.header-controls select {
  padding: 6px 10px;
  font-size: 14px;
  border-radius: 4px;
  border: 1px solid #ccc;
}
.toggle-btn {
  height: 2.5em;
  display: inline-flex;
  border: 1px solid #ccc;
  border-radius: 6px;
  background-color: #e0e0e0;
  align-items: center;
  overflow: hidden;
  position: relative;
}

.toggle-option {
  flex: 1;
  padding: 8px 16px;
  border: none;
  background-color: #e0e0e0;
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  position: relative;
  z-index: 1;
}

.toggle-option.active {
  background-color: white;
  color: black;
  border: 2px solid #bbb;
  transition: all 0.15s ease-in-out;
  border-radius: 6px;
  z-index: 2;
}

.toggle-option:first-child.active {
  box-shadow: 4px 0 6px -2px rgba(0, 0, 0, 0.4);
}

.toggle-option:last-child.active {
  box-shadow: -4px 0 6px -2px rgba(0, 0, 0, 0.4)
}
.view-toggle {
  display: flex;
  gap: 8px;
}
/* Tooltip Icon Styling */
.info-icon {
  cursor: pointer;
  font-size: 12px;
  margin-left: 10px; /* Space between the last input and the icon */
  position: relative;
}

/* Optional: Add hover effect to change color */
.info-icon:hover {
  color: #0056b3;
}

.calendar-container {
  border: 1px solid #ccc;
  border-radius: 12px;
  padding: 12px;
  background-color: #fff;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);

  gap: 0.25rem;
  margin-top: 1rem;
}

.weekday {
  border-bottom: 1px solid #ccc;
  padding-left: 5px;
  padding-bottom: 10px;
  margin-bottom: 15px;
  font-weight: 500;
  text-align: left;
}

.day-box {
  border-radius: 6px;
  height: 70px;
  background-color: #fcfcfc;
  color: #000;
  border: 1px solid #ddd;
  text-align: left;
  padding: 0.5rem;
}

.day-box.holiday {
  background-color: rgba(255, 235, 59, 0.2);
  // border: 1px solid #FBC02D;
}
.non-working-days {
  background-color: #ffcccc;
  color: #7a7a7a;
  pointer-events: none;
}

.day-box.empty {
  background: transparent;
  border: none;
}

.year-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-top: 1.5rem;
}

.month-box {
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  text-align: center;
  padding: 1.5rem 0;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: 0.3s;
}

.month-box:hover {
  background-color: #e0e0e0;
}

.holiday-section {
  width: 25%;
  padding: 1rem;
  background: #fafafa;
  border-left: 1px solid #ddd;
  overflow-y: auto;
  height: 80vh;
}

.holiday-boxes {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.holiday-box {
position: relative;
padding: 12px;
border-radius: 12px;
background-color: rgba(255, 235, 59, 0.2);
border: 1px solid #ffe082;
  display: flex;
  flex-direction: column;
  align-items: start;
}
.holiday-image {
  position: absolute;
  cursor: pointer;
  top: 20px;
  right: 10px;
  width: 18px;
  height: 18px;
}

.holiday-name {
  font-size: 13px;
  font-weight: 500;
}

.holiday-date {
  font-size: 12px;
  color: #f57f17;
}
.holiday-actions {
  position: absolute;
  top: 20px;
  right: 10px;
  display: flex;
  gap: 5px;
}

.holiday-action-icon {
  cursor: pointer;
  width: 18px;
  height: 18px;
}

/* Working Days & Hours */
.working-days-container {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #ddd;
}

.working-days {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1rem;
}

.day-checkbox {
  display: flex;
  align-items: center;
  min-width: 100px;
}

.global-time-range {
  margin-top: 1.5rem;
}

.global-time-range label {
  font-weight: 500;
  display: block;
  margin-bottom: 0.5rem;
}

.time-inputs {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.save-button {
  margin-top: 1.5rem;
  padding: 8px 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.save-button:hover {
  background-color: #0056b3;
}
</style>
