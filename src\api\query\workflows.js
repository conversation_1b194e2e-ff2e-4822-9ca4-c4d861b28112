import { GQL } from '../graphQl'

export const GetWorkflowByIdQuery = () => GQL`query getWorkflows($id:uuid!){
  WF_Template (where:{id:{_eq:$id}}){
    id
    name
    workflow_phases {
      id
      name
    }
    workflow_steps {
      id
      name
      department_id
      duration
      phase
      position_x
      position_y
    }
    workflow_transitions {
      next_step
      prev_step
      name
    }
  }
}
`

export const DeleteWfStepsAndTransitionsQuery = () => GQL`mutation DeleteWfStepsAndTransitionsQuery($workflowId: uuid!) {
  delete_WF_Transit(where: {workflow_id: {_eq: $workflowId}}) {
    affected_rows
  }
  delete_WF_Steps(where: {workflow_id: {_eq: $workflowId}}) {
    affected_rows
  }
}
`

export const CreateWorkflowStepsQuery = () => GQL`mutation createWFSteps($data: WF_Steps_insert_input!) {
  insert_WF_Steps_one(object: $data) {
    id
  }
}
`

export const CreateWorkflowTransitionssQuery = () => GQL`mutation createWFTransitions($data: [WF_Transit_insert_input!]!) {
  insert_WF_Transit(objects: $data) {
    affected_rows
  }
}
`
export const getWorkflowInstanceStepDataQuery = () => GQL`query getWorkflowInstanceStepDataQuery($id: uuid!) {
  workflow_instance_steps(where: {workflow_instance_id: {_eq: $id}}) {
    workflow_instance_id
    step_id
    planned_start_date
    planned_end_date
  }
}
}
`
export const getworkflowEscalationHistoryQuery = () => GQL`query getworkflowEscalationHistoryQuery($stepId: uuid, $wfInstanceId: uuid) {
  workflow_step_escalation_history(where: {step_id: {_eq: $stepId}, instance_id: {_eq: $wfInstanceId}}, order_by: {escalation_level: desc}) {
    escalated_at
    escalated_by
    escalated_to
    escalation_level
    escalation_reason
    id
    step_id
    response
    responded_at
    next_escalation_to
    escalated_by_user {
      last_name
      first_name
      email
    }
    escalated_to_user {
      email
      first_name
      id
      last_name
    }
  }
}

`
export const getWorkflowStepWithUsersQuery = () => GQL`query getWorkflowStepWithUsersQuery($versionId: uuid) {
  workflow_stages(where: {template_version_id: {_eq: $versionId}}) {
    id
    core_user_group {
      id
         name
      core_user_group_members (where:{deleted :{_eq:false}}){
      deleted
        user_id
      }
    }
  }
}
`
