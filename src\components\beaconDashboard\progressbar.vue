<template>

    <progress :value="value" :max="100" :style="{'--value':value , '--max': 100, '--progress-value-color':rawColor, '--progress-bg-color':rawbg}"></progress>

</template>
<script>
export default {
  name: 'progressBar',
  data: function () {
    return ({
    }

    )
  },
  props: {
    rawColor: {
      type: String,
      default: 'rgb(107, 107, 71)'
    },
    rawbg: {
      type: String,
      default: 'rgba(107, 107, 71, 0.2)'
    },
    value: {
      type: Number,
      default: 50
    }
  }
}
</script>
<style lang="scss" scoped>
@property --progress-value {
  syntax: '<integer>';
  inherits: true;
  initial-value: 0;
}

.progress-bar-container {
  width: 100%;
}

progress {
  display: block;
  position: relative;
  appearance: none;
  width: 70%;
  height: .8rem;
  border-end-end-radius: 10px;
  counter-reset: progress var(--progress-value);
  --progress-value-string: counter(progress) '%';
  --progress-max-decimal: calc(var(--value, 0) / var(--max, 0));
  --progress-value-decimal: calc(var(--progress-value, 0) / var(--max, 0));
  --progress-value-percent: calc(var(--progress-value-decimal) * 100%);
  --progress-value-color: var(--progress-value-color);
  animation: calc(3s * var(--progress-max-decimal)) linear 0.5s 1 normal both progress;
}

progress::-webkit-progress-bar {
border: 1px groove var(--progress-value-color) ;
background-color: var(--progress-bg-color) ;
  border-radius: 10px;

}

progress[value]::-webkit-progress-value {
    border: 1px groove rgba(212, 212, 212, 0.3) ;
  width: var(--progress-value-percent) !important;
  background-color: var(--progress-value-color);
  border-radius: 10px;
}

progress[value]::-moz-progress-bar {
    border: 1px groove var(--progress-bg-color) ;
  width: var(--progress-value-percent) !important;
  background-color: var(--progress-value-color);
  border-radius: 10px;
}
@keyframes progress {
  from {
    --progress-value: 0;
  } to {
    --progress-value: var(--value);
  }
}
</style>
