<template>
  <compare-project-bom :productBom="true"/>
</template>

<script>
import { GetAllBomList } from '@/api'
import CompareProjectBom from './compareProjectBom.vue'
export default {
  components: { CompareProjectBom },
  name: '<PERSON>mCompar<PERSON>',
  data: () => ({
    rightVersion: null,
    leftVersion: null,
    loadingBomDetail: false,
    productCode: null,
    bomList: []
  }),
  methods: {
    goBack () {
      this.$router.go(-1)
    },
    getBomDetail () {
      this.loadingBomDetail = true
      GetAllBomList(this.productCode)
        .then((res) => {
          this.bomList = res.core_bom
        })
        .catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loadingBomDetail = false
        })
    }
  },
  created () {
    this.productCode = this.$route.params.productCode
    this.getBomDetail()
  }
}
</script>

<style lang="scss" scoped >
.bom-compare {
  height: 100%;
  &-bar {
    height: 60px;
    margin: -12px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  &-action {
    margin-right: 20px;
    label {
      font-size: 14px;
      color: var(--text-color);
      margin-bottom: 5px;
      display: block;
    }
    select {
      margin: 0 10px;
      font-size: 12px;
      background-color: var(--brand-light-color);
      line-height: 1;
      border: 1px solid var(--brand-color);
      border-radius: 4px;
      padding: 4px 12px;
    }
  }
  &-container {
    height: calc(100% - 60px);
    overflow: auto;
  }
}
</style>
