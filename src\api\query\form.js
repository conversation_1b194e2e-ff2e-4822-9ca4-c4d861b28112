import { GQL } from '../graphQl'

export const GetAllFormTypeListQuery = () => GQL`query GetAllFormTypeListQuery {
  core_form_types (where: {enabled: {_eq: true}}) {
    enabled
    feature
    id
    name
    default_fields
  }
}`

export const GetAllFormFieldsQuery = () => GQL`query GetAllFormFieldsQuery {
  core_form_fields {
    caption
    enabled
    id
    key
  }
}`

export const CreateFormQuery = () => GQL`mutation CreateFormQuery(
    $data: [template_fields_insert_input!]!,
    $form_type: Int,
    $name: String,
    $wftId: uuid,
    $sequence_template_id: uuid
  ) {
  insert_core_form_templates(
    objects: {
      form_type: $form_type,
      name: $name,
      workflow_template_id:$wftId,
      sequence_template_id: $sequence_template_id,
      template_versions: {
        data: {template_fields: {data: $data}}
      }
    }
  ) {
    affected_rows
  }
}
`
export const UpdateFormMutation = () => GQL`mutation updateFormTemplate($templateId: uuid!, $templateFields: [template_fields_array!]!, $name: String!, $wftId: uuid, $sequence_template_id: uuid!) {
  update_form_template(template_id: $templateId, template_fields: $templateFields, name: $name, workflow_template_id:$wftId, sequence_template_id: $sequence_template_id) {
    message
  }
}`

export const GetAllFormTemplatesQuery = () => GQL`query GetAllFormTemplatesQuery($conditions: core_form_templates_bool_exp) {
  core_form_templates(where: $conditions, order_by: {created_on: desc}) {
    name
    id
    form_type
    sequence_template_id
    workflow_template_id
    workflow_template {
      name
      id
    }
    core_form_type {
      name
    }
    template_versions {
      active
      version_id
    }
    created_on
    template_created_by {
      first_name
      last_name
    }
  }
}
`
export const GetAllFormTemplatesWithPaginationQuery = () => GQL`query GetAllFormTemplatesWithPaginationQuery($conditions:core_form_templates_bool_exp, $perPage: Int , $skip: Int, $orderBy: [core_form_templates_order_by!] ) {
  core_form_templates(where: $conditions, limit: $perPage, offset: $skip, order_by: $orderBy) {
    name
    id
    form_type
    workflow_template_id
    workflow_template {
      name
      id
    }
    core_form_type {
      name
    }
    template_versions {
      active
      version_id
    }
    created_on
    template_created_by {
      first_name
      last_name
    }
  }
   core_form_templates_aggregate(where: $conditions) {
    aggregate {
      count
    }
  }
}`

export const GetDetailFormTemplateQuery = (id) => GQL`query GetDetailFormTemplateQuery($id: uuid!, $isCollaborator: Boolean!) {
  core_form_templates_by_pk(id: $id) {
    name
    id
    workflow_template_id
    core_sequence_id_template @skip(if: $isCollaborator) {
      name
      id
      id_generation_rules {
        auto_increment
        component_id
        created_at
        custom_list_id
        default_value
        delimiter_after
        id
        is_mandatory
        padding_length
        prefix
        sequence_order
        sequence_template_id
        start_value
        suffix
        tag_type_id
        tag_type {
          tags {
            id
            parent_id
            name
            __typename
          }
          __typename
        }
        validation_type
        validation_value
        component {
          id
          enabled
          is_dynamic
          caption
          __typename
        }
        core_custom_list {
          id
          custom_list_values {
            id
            name
            __typename
          }
          __typename
        }
        __typename
      }
    }
    workflow_template @skip(if:$isCollaborator){
      name
      id
      workflow_versions(where: {active: {_eq: true}}) {
      id
        workflow_stages(where: {start_step: {_eq: true}}) {
          id
          name
          next_transition {
            id
            name
            next_workflow_stage {
              id
              name
            }
          }
          duration
          core_user_group {
            name
            id
          }
        }
        active
        template_id
        version_no
      }
      feature_id
      type_value {
        id
        name
      }
    }
    core_form_type {
      name
      id
    }
    template_versions {
      active
      version_id
      id
      template_fields {
        autogenerated
        caption
        custom_list_id
        field_id
        field_name
        field_type_id
        fixed
        required
        visibility
        template_version_id
        form_field {
          key
        }
      }
    }
  }
}
`

export const GetFormsByFormTemplateIdQuery = () => GQL`query GetFormsByFormTemplateIdQuery($conditions: core_forms_bool_exp, $openFormsConditions: core_forms_bool_exp, $draftFormsConditions: core_forms_bool_exp, $reopenedFormsConditions: core_forms_bool_exp, $closedFormsConditions: core_forms_bool_exp, $limit: Int!, $offset: Int!, $field_id: [uuid!], $orderBy: [core_forms_order_by!]) {
  core_forms_aggregate(where: $conditions) {
    aggregate {
      count
    }
  }
  open_forms: core_forms_aggregate(where: $openFormsConditions) {
    aggregate {
      count
    }
  }
  draft_forms: core_forms_aggregate(where: $draftFormsConditions) {
    aggregate {
      count
    }
  }
  reopened_forms: core_forms_aggregate(where: $reopenedFormsConditions) {
    aggregate {
      count
    }
  }
  closed_forms: core_forms_aggregate(where: $closedFormsConditions) {
    aggregate {
      count
    }
  }
  core_forms(order_by: $orderBy, where: $conditions, limit: $limit, offset: $offset) {
    template_version {
      core_form_template {
        sequence_template_id
      }
    }
    sequence_value
    due_date
    id_int
    status
    form_type_id
    id
    schedule_impact
    cost_impact
    created_by
    created_on
    updated_by
    updated_on
    created_by_user {
      id
      first_name
      last_name
    }
    updated_by_user {
      id
      first_name
      last_name
    }
    forms_metadata_by_id(where: {field_id: {_in: $field_id}}) {
      time_value
      point_value
      int_value
      date_value
      bool_value
      string_value
      field_id
    }
    forms_document_list {
      doc_ext
      doc_name
      blob_key
      version_of
      core_document {
        doc_name
        id
        thumbnail_blob_key
      }
    }
    forms_company_lists(where: {field_id: {_in: $field_id}}) {
      field_id
      company_name
    }
    forms_material_lists(where: {field_id: {_in: $field_id}}) {
      field_id
      core_material {
        material_name
      }
    }
    forms_attachments(where: {field_id: {_in: $field_id}, deleted: {_eq: false}}) {
      field_id
      core_document {
        doc_name
      }
      core_attachment {
        file_name
      }
    }
    forms_config_lists(where: {field_id: {_in: $field_id}}) {
      field_id
      custom_list_value
    }
    forms_bom_lists {
      field_id
      core_bom {
        id
        name
      }
    }
    forms_tag_list(where: {field_id: {_in: $field_id}}) {
      field_id
      tag_id
      tag {
        name
      }
    }
    forms_user_lists {
      field_id
      user_id
      core_user {
        id
        first_name
        last_name
      }
    }
  }
}
`
export const GetAllFormsDataByFormTemplateQuery = () => GQL`query GetAllFormsDataByFormTemplateQuery($conditions: core_forms_bool_exp, $limit: Int!, $offset: Int!) {
  core_forms_aggregate(where: $conditions) {
    aggregate {
      count
      __typename
    }
    __typename
  }
  core_forms(
    order_by: {created_on: desc}
    where: $conditions
    limit: $limit
    offset: $offset
  ) {
    template_version_id
    due_date
    status
    form_type_id
    id
    schedule_impact
    cost_impact
    created_by
    created_on
    updated_by
    updated_on
    created_by_user {
      id
      first_name
      last_name
      __typename
    }
    updated_by_user {
      id
      first_name
      last_name
      __typename
    }
    forms_metadata_by_id {
      time_value
      point_value
      int_value
      date_value
      bool_value
      string_value
      field_id
      __typename
    }
    forms_company_lists{
      field_id
      company_name
      __typename
    }
    forms_material_lists {
      field_id
      core_material {
        material_name
        custom_material_id
        __typename
      }
      __typename
    }
    forms_attachments {
      core_attachment {
        file_name
      }
      core_document {
        doc_name
      }
        field_id
    }
    forms_tag_list {
    field_id
    tag {
      name
    }
  }
    forms_config_lists {
         field_id
      custom_list_value_by_id {
        name
      }
      __typename
    }
    forms_user_lists {
      field_id
      user_id
      core_user {
        id
        first_name
        last_name
        __typename
      }
      __typename
    }
    __typename
  }
}
`
export const GetAllformFieldsOfAllVersionsByTemplateIdQuery = () => GQL`query GetAllformFieldsOfAllVersionsByTemplateIdQuery($templateId: uuid) {
  core_form_templates(where: {id: {_eq: $templateId}}) {
    template_versions {
      id
      template_fields{
        field_id
        field_name
        caption
      }
    }
  }
}`
export const GetAllformFieldsOfAllVersionsByFormTypeIdQuery = () => GQL`query GetAllformFieldsOfAllVersionsByTemplateIdQuery($formType: Int) {
  core_form_templates(where: {form_type: {_eq: $formType}}) {
    template_versions {
      id
      template_fields {
        field_id
        field_name
        caption
      }
    }
    form_type
  }
}`

export const GetFormDataByFormIdQuery = () => GQL`query GetFormDataByFormIdQuery($id: uuid!) {
  core_forms_by_pk(id: $id) {
    due_date
    sequence_value
    form_type_id
    id
    schedule_impact
    cost_impact
    created_by
    status
    workflow_instance_id
    parent_id
    root_parent_id
    next_revisions_aggregate {
      aggregate {
        count(columns: id)
      }
    }
    created_by_user {
      first_name
      last_name
    }
    created_on
    core_project {
      name
    }
    updated_by
    updated_on
    created_by_user {
      id
      first_name
      last_name
    }
      forms_bom_lists{
      field_id
      bom_id
    }
    updated_by_user {
      id
      first_name
      last_name
    }
    template_version_id
    project_id
    forms_document_list {
    field_id
    doc_ext
    doc_name
    created_on
    blob_key
    version_of
    transition_history {
      current_workflow_step {
        name
      }
    }
    core_document {
      doc_name
      id
      parent_id
      thumbnail_blob_key
    }
  }
    forms_attachments (where: {deleted: {_eq: false}}){
      attachments_reference_key
      deleted
      id
      field_id
      form_id
      core_document {
        id
        blob_key
        doc_name
        doc_size
        doc_ext
        created_on
        created_by
        version_of
        thumbnail_blob_key
      }
      core_attachment {
        id
        blob_key
        file_name
        file_size
        file_type
        created_on
        created_by
      }
    }
    forms_company_lists {
      company_id
      company_name
      field_id
    }
    forms_tag_list {
      field_id
      tag_id
      tag {
        id
        parent_id
        name
      }
    }
    forms_contact_list {
      field_id
      id
      name
      phone
      email
      department_custom_list_value {
        id
        name
      }
      description
    }
    forms_config_lists {
      field_id
      custom_list_value
      custom_list_value_id
      custom_list_value_by_id {
        id
        name
      }
    }
    forms_material_lists {
      field_id
      material_id
      quantity
      sequence
      total_cost
      unit_cost
      core_bom {
        name
        id
        product_code
        bom_versions(where:{active:{_eq:true}}) {
          version_no
        id
        }
      }
    }
    forms_metadata_by_id {
    point_value
      bool_value
      date_value
      field_id
      field_type_id
      int_value
      point_value
      product_code_value
      project_id
      string_value
      time_value
    }
    forms_user_lists {
      field_id
      user_id
      target_tenant_id
      tenant_user_association{
        tenant{
        company_name
        id
        }
        }
    }
  }
}
`

// Form Widget Data
export const GetUserListQuery = () => GQL`query GetUserListQuery {
  core_users(where: {status: {_eq: 1}}) {
    email
    first_name
    last_name
    id
    phone
    tenant_users {
      tenant {
        company_name
        id
      }
    }
  }
}`
export const GetUserListQuerywithInvited = () => GQL`query GetUserListQuerywithInvited {
  core_users(where: {status: {_neq: 2}}) {
    email
    first_name
    last_name
    id
    phone
    status
    tenant_users {
      tenant {
        company_name
        id
      }
    }
  }
}`

export const GetMaterialListByIdsQuery = () => GQL`query GetMaterialListByIdsQuery($ids: [uuid!]) {
  core_material_master(where: {id: {_in: $ids}}) {
    material_product_code {
      product_code
    }
    id
    material_name
    product_code
  }
}`

export const GetMaterialListQuery = () => GQL`query GetMaterialListQuery($searchKeyWord: String) {
  core_material_master(
    where: {material_name: {_ilike: $searchKeyWord}, material_description: {_ilike: $searchKeyWord}}
    limit: 20
  ) {
    material_name
    id
  }
}
`
export const getFormIdByformTypeQuery = () => GQL`query getFormIdByformTypeQuery($form_type: Int ) {
  core_form_templates(where: {form_type: {_eq: $form_type}}) {
    id
    name
    form_type
  }
}`

export const deleteFormMutation = () => GQL`mutation deleteForm ($id : uuid!){
  delete_core_forms_by_pk (id: $id) {
    id
  }
}`

export const GetFormCustomFieldsQuery = () => GQL`query GetFormCustomFieldsQuery($conditions:template_fields_bool_exp) {
  template_fields(where: $conditions) {
    field_id
    caption
    field_type_id
    template_version {
      template_id
    }
    form_field {
      caption
      key
    }
  }
}`

export const GetTenantUsersList = () => GQL`query getTenantUsersList($id: uuid!) {
  tenant_user_association(where: {tenant: {id: {_eq: $id}}, status: {_eq: 1}}) {
    user_id
    tenant{
      company_name
      id
    }
    associated_user {
      id
      first_name
      last_name
      email
    }
  }
}`

// // associated_role: {id: {_neq: 4}} ==>disabling viewer roles
export const GetActiveTenantUsersListQuery = () => GQL`query GetActiveTenantUsersListQuery($id: uuid!) {
  tenant_user_association(where: {tenant: {id: {_eq: $id}}, status: {_eq: 1}, associated_role: {id: {_neq: 4}}}) {
    user_id
    tenant{
      company_name
      id
    }
    associated_user {
      id
      first_name
      last_name
      email
    }
      associated_role {
      name
      id
    }
  }
}`
// status: {_ne: 2}} ==>  expecting all users except deleted
// associated_role: {id: {_neq: 4}} ==>disabling viewer roles
export const GetTenantUsersListwithInvitedUsers = () => GQL`query GetTenantUsersListwithInvitedUsers($id: uuid!) {
  tenant_user_association(
    where: {tenant: {id: {_eq: $id}}, status: {_neq: 2}, associated_role: {id: {_neq: 4}}}
  ) {
    user_id
    tenant {
      company_name
      id
    }
    associated_user {
      id
      first_name
      last_name
      email
    }
    associated_role {
      name
      id
    }
  }
}
`

export const getAllAssigneesOfATemplateQuery = (projectId) => GQL`query getAllAssigneesOfATemplate($templateId: uuid!) {
  forms_user_list(
    distinct_on: user_id
    where: {core_form: {template_version: {template_id: {_eq: $templateId}}, project_id: ${projectId ? `{_eq: "${projectId}"}` : '{ _is_null: true }'}}}
  ) {
    core_user {
      first_name
      last_name
      id
    }
  }
}
`
export const getFieldsForCustomFormFiltersQuery = () => GQL`query getFieldsForCustomFormFilters($templateId: uuid!) {
  template_fields(where: {field_type_id: {_in: [1, 2, 4]}, template_version: {template_id: {_eq: $templateId}, active: {_eq: true}}}) {
    field_id
    caption
    field_type_id
    core_custom_list {
      custom_list_values(where: {forms_config_lists: {core_form: {template_version: {template_id: {_eq: $templateId}}}}}) {
        id
        name
      }
    }
  }
}`
export const reAssignToNewUserMutation = () => GQL`mutation updateFormsUserList($targetTenantId: uuid, $oldUserId: uuid, $templateId: uuid, $newUserId: uuid, $status: Int) {
   delete_forms_user_list(where: {user_id:{_eq: $oldUserId}, core_form:{ template_version: {template_id: {_eq: $templateId}} status:{_eq: $status} forms_user_lists:{user_id:{_eq:$newUserId}}}}) {
    affected_rows
  }
update_forms_user_list(
    _set: {target_tenant_id: $targetTenantId, user_id: $newUserId}
    where: {core_form: {status:{_eq: $status} template_version: {template_id: {_eq: $templateId}}}, user_id: {_eq: $oldUserId}}
  ) {
    affected_rows
  }
}`

export const GetStandardBomFormTemplateQuery = () => GQL`query GetStandardBomFormTemplate($conditions: core_form_templates_bool_exp!, $conditions2: template_versions_bool_exp!) {
  core_form_templates(where: $conditions) {
    name
    id
    core_form_type {
      name
      id
    }
    template_versions (where:$conditions2){
      active
      version_id
      id
      template_fields {
        autogenerated
        caption
        custom_list_id
        field_id
        field_name
        field_type_id
        fixed
        required
        visibility
        template_version_id
        form_field {
          key
        }
      }
    }
  }
}`

export const formWorkFlowLinkingMutation = () => GQL`
mutation formWorkFlowLinkingMutation($updates: [core_form_templates_updates!]!) {
  update_core_form_templates_many(updates: $updates) {
    affected_rows
  }
}`

export const reOpenForm = () => GQL`mutation reopenForm($cost_impact: Boolean!, $schedule_impact: Boolean!, $due_date: Date!, $form_id: uuid!, $input_payload: [customFieldData!]!, $step_id: uuid, $workflowStepAssignees:[workflowStepAssigneesData]) {
  reopen_form(cost_impact: $cost_impact, schedule_impact: $schedule_impact, due_date: $due_date, form_id: $form_id, input_payload: $input_payload, step_id: $step_id, workflow_step_assignees: $workflowStepAssignees) {
    message
  }
}`
export const getFormRevision = () => GQL`query GetFormRevision($id: uuid!) {
  core_forms(where: {_or: [{root_parent_id: {_eq: $id}}, {id: {_eq: $id}}]}, order_by: {created_on: desc}) {
    id
    created_on
    updated_on
    status
     next_revisions_aggregate {
      aggregate {
        count(columns: id)
      }
    }
    created_by_user {
      first_name
      last_name
    }
    root_parent_id
    closed_by_user {
      first_name
      last_name
    }
  }
}`
