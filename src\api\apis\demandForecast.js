import http from '../http'
import config from '../../config.js'

export const getDemandForecastData = (body) => {
  const filter = { input: body }
  return http.POST(config.serverEndpoint + '/materials/forecast', filter, 'tenant')
}
export const getResourceTrendsData = (body) => {
  return http.POST(config.serverEndpoint + '/dashboard/projects/resource-production-actual-vs-plan-timeseries', body, 'project')
}
export const getResourceStatusData = (body) => {
  return http.POST(config.serverEndpoint + '/dashboard/projects/resource-status', body, 'project')
}
