<template>
  <div class="mt-1 mb-1">
    <form class="gantt-scale">
      <label for="scale">Scale</label>
      <select id="scale" name="scale" :value="this.$props.zoomLevel" @change="$emit('update-scale',$event)">
        <option value="Days">Days</option>
        <option value="Weeks">Weeks</option>
        <option value="Months">Months</option>
        <option value="quarter">Quartely</option>
        <option value="year">Year</option>
      </select>
    </form>
  </div>
</template>
<script>
export default {
  name: 'GanttZoom',
  props: {
    zoomLevel: String
  }
}
</script>
<style scoped lang="scss">
.gantt-scale{
  display: flex;
  gap:15px;
  justify-content: flex-end;
}
</style>
