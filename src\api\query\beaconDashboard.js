import { GQL } from '../graphQl'

export const getBeaconTemplateVersionIdsQuery = () => GQL`query getBeaconTemplateVersionIdsQuery   ($sales_template_name: String = "BEACONsales", $target_template_name: String = "BEACONTarget") {
    sales_template: template_versions(where: {active: {_eq: true}, core_form_template: {name: {_eq: $sales_template_name}}}) {
      id
    }
    target_template: template_versions(where: {active: {_eq: true}, core_form_template: {name: {_eq: $target_template_name}}}) {
      id
    }
  }
  `
