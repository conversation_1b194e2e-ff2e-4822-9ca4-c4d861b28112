import { crypt, decrypt } from './incodeDecode'
const salt = 'A2F3C'

export default {
  write: function (cname, cvalue, exdays = 365) {
    var d = new Date()
    d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000))
    var expires = 'expires=' + d.toUTCString()
    document.cookie = cname + '=' + crypt(salt, cvalue) + '; ' + expires
  },
  read: function (name) {
    if (document.cookie.indexOf(name) > -1) {
      const a = `; ${document.cookie}`.match(`;\\s*${name}=([^;]+)`)
      const value = a ? a[1] : ''
      if (value === '') {
        return value
      } else {
        return decrypt(salt, value)
      }
    } else {
      return ''
    }
  },
  delete: function (cname) {
    var d = new Date()
    d.setTime(d.getTime() - 1000)
    var expires = 'expires=' + d.toUTCString()
    document.cookie = cname + '=; ' + expires
  }
}
