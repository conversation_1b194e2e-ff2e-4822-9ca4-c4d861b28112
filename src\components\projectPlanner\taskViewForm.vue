<template>
    <div>
      <div class="material-master-detail">
        <div class="grid-2">
          <div class="key-value">
            <span @click="getComments" class="key">Task Name:</span>
            <span class="value">{{ `${this.task.text}` || '--' }}</span>
          </div>
          <div class="key-value">
            <span class="key">Description:</span>
            <span class="value">{{ this.task.description || '--' }}</span>
          </div>
        </div>
        <div class="grid-2 mt-2">
          <div class="key-value">
            <span class="key">Duration(hours):</span>
            <span class="value">{{ `${this.task.duration}` || '--' }}</span>
          </div>
          <div class="key-value">
            <span class="key">Progress:</span>
            <span class="value">{{ `${Math.round(task?.progress * 100)}%` || '--' }}</span>
          </div>
        </div>
        <div class="grid-2 mt-2">
          <div class="key-value">
            <span class="key">Start Date:</span>
            <span class="value">{{ `${new Date(this.task.start_date).toLocaleDateString('en-GB')}` || '--' }}</span>
          </div>
          <div class="key-value">
            <span class="key">End Date:</span>
            <span class="value">{{ `${new Date(this.task.end_date).toLocaleDateString('en-GB')}` || '--' }}</span>
          </div>
        </div>
        <div class="grid-2 mt-2">
          <div class="key-value">
            <span class="key">Slack (hours):</span>
            <span class="value">{{ `${this.task.slack}` || '--' }}</span>
          </div>
          <div class="key-value">
            <span class="key">Cost:</span>
            <span class="value">{{ `${this.task.cost}` || '--' }}</span>
          </div>
        </div>
        <div class="grid-2 mt-2">
          <div class="key-value">
            <span class="key">SPI:</span>
            <span class="value">{{  this.task.spi || '--' }}</span>
          </div>
        </div>
        <div class="grid-2 mt-2">
          <div class="flex key-value">
        <span class="key">Predecessors:</span>
        <div v-if="taskData.predecessorTask.length" class="tags">
          <span v-for="sourceTask in taskData.predecessorTask" :key="sourceTask?.src_task?.id">{{ sourceTask?.src_task?.name }}</span>
        </div>
        <div v-else class="key-value">
          <span class="value">No Predecessors found</span>
        </div>
      </div>
      <div class="flex key-value">
        <span class="key">Successors:</span>
        <div v-if="taskData.successorTask.length > 0" class="tags">
          <span v-for="targetTask in taskData.successorTask" :key="targetTask?.target_task?.id">{{ targetTask?.target_task?.name }}</span>
        </div>
        <div v-else class="key-value">
          <span class="value">No Successors found</span>
        </div>
      </div>
        </div>
        <div class="flex key-value mt-2">
        <span class="key">Tags:</span>
        <div v-if="taskData.tags.length > 0" class="tags">
          <span v-for="tag_material in taskData.tags" :key="tag_material.tag.id">{{ tag_material.tag.name}}</span>
        </div>
        <div v-else class="key-value">
          <span class="value">No tags found</span>
        </div>
      </div>
        <div class="flex key-value mt-2">
        <span class="key">Assignees:</span>
        <div v-if="taskData.task_assignees.length > 0" class="tags">
          <span v-for="task_assignee in taskData.task_assignees" :key="task_assignee.user_id">{{ task_assignee.first_name}} {{ task_assignee.last_name }}</span>
        </div>
        <div v-else class="key-value">
          <span class="value">No Assignees found</span>
        </div>
      </div>
      <div class="flex key-value mt-2 document-session">
        <span class="key">Documents:</span>
            <div v-if="documents.length > 0 " class="tags" ><span v-for="file in documents" :key="file.id">
              <span v-show="file.flag!=='deleted'"  @click="$emit('openDocumentView',file)">  {{ file.core_document.doc_name }}</span></span>
      </div>
     </div>
     <div class="flex key-value mt-2">
        <span class="key">Bom:</span>
        <div v-if="taskBoms.length > 0" class="tags">
          <span @click="openBomInNewTab(bom)" v-for="bom in taskBoms" :key="bom.id">{{ bom.name}}</span>
        </div>
        <div v-else class="key-value">
          <span class="value">No BOM found</span>
        </div>
      </div>
        <!-- <div v-if="taskData.task_assignees.length" class="flex key-value mt-2">
        <span class="key">Assignees:</span>
        <div class="tags">
          <span v-for="task_assignee in taskData.task_assignees" :key="task_assignee.user_id">{{ task_assignee.assignee.first_name}}</span>
        </div>
      </div> -->
      <!-- <div v-else class="key-value mt-2">
        <span class="key">Tags:</span>
        <span class="value">No tags are attached to this material
        </span>
      </div> -->
      <div class=" mt-2">
        <div class="key-value">
          <!-- <div><span class="key">Products and BOMS where this material is used:</span></div> -->
          <div class="task-comment-panel-container">
        <div class="fh fw center" v-if="loading">
          <loading-circle />
        </div>
        <template v-else>
            <div v-if="comments" class="history-navbar">
              <span class="pl-3">Comments</span>
            </div>
            <template v-if="comments.length > 0">
              <task-comment-card
                :view="true"
                v-for="comment in comments"
                :commentData="comment"
                :key="comment.id"
                :level="1"
                @updateComment="getComments"
              />
            </template>
            <span v-else>No comments exist.</span>
          </template>
      </div>
         <!-- <history-table /> -->
        </div>
      </div>
      </div>
    </div>

  </template>

<script>
// import historyTable from './historyTable.vue'
import taskCommentCard from './commentCard.vue'
import LoadingCircle from '../common/loadingCircle.vue'
import { getAllTaskComments } from '../../api/apis/comments'
import { GetAllTaskDocs, GetAllTaskBom } from '../../api/apis/projectPlanner'

export default {
  components: { taskCommentCard, LoadingCircle },
  name: 'custom-detail',
  props: {
    task: {
      type: Object,
      default: () => ({})
    },
    view: {
      type: String,
      default: 'chart'
    }
  },
  data () {
    return {
      taskData: {
        name: '',
        description: '',
        duration: null,
        start_date: '',
        end_date: '',
        progress: '',
        type: '',
        timeSpent: '',
        status: null,
        successorTask: [],
        predecessorTask: [],
        task_assignees: [],
        tags: []
      },
      loading: false,
      comments: [],
      documents: [],
      taskBoms: []
    }
  },
  mounted () {
    this.getComments()
    this.getAllDocuments()
    this.getAllTaskBoms()
    const task = JSON.parse(JSON.stringify(this.$props.task))
    this.taskData.tags = task.tag_tasks
    this.taskData.task_assignees = task.task_assignees
    this.taskData.predecessorTask = task.target_task_links
    this.taskData.successorTask = task.src_task_links
    this.taskData.start_date = new Date(task.start_date)
      .toISOString()
      .substr(0, 10)
    this.taskData.end_date = new Date(task.end_date)
      .toISOString()
      .substr(0, 10)
    this.taskData.timeSpent = task?.timeSpent ?? 0
  },
  methods: {
    openBomInNewTab (bom) {
      const bomId = bom.id
      const versionId = bom.bom_versions.at(-1).id
      const url = this.getBomUrl(bomId, versionId)
      window.open(url, '_blank')
    },
    getBomUrl (bomId, versionId) {
    // Construct and return the URL based on the bom object
      return `/bom/project/bom/${bomId}?bomVersionId=${versionId}` // Example URL construction
    },
    getComments () {
      this.loading = true
      const taskId = this.$props.task.id
      getAllTaskComments(taskId, null)
        .then((res) => {
          this.comments = res.core_comments || []
        })
        .finally(() => {
          this.loading = false
        })
    },
    getAllDocuments () {
      const taskId = this.$props.task.id
      GetAllTaskDocs(taskId).then((res) => {
        this.documents = res.task_document_association
      })
    },
    getAllTaskBoms () {
      const taskId = this.$props.task.id
      GetAllTaskBom(taskId).then((res) => {
        this.taskBoms = res.task_material_association.map((element) => {
          return element.target_bom
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped >
  .task-comment-panel-container {
    max-height: 40vh;
    overflow-y: auto; /* Add vertical scrollbar when content exceeds the max height */
    .span {
    margin-top: 10px; /* Add margin to space out the "No comments exist" message */
  }
  .history-navbar {
    font-weight: bold;
    padding: 10px 0px 10px 0px;
    background-color: var(--brand-color);
    /* border-radius: 5px 5px 0 0; */
    position: sticky;
    top: 0;
    z-index: 10;
  }
  }
.material-master-detail {
  width: 600px;
}

.key-value {
  .key {
    color: var(--text-color-1);
    font-weight: 500;
    font-size: 0.9em;
  }

  .value {
    margin: 0 4px;
    font-size: 0.9em;
    color: var(--text-color);
  }
}

.tags {
  display: flex;
  flex-wrap: wrap;
  cursor: pointer;

  span {
    margin-left: 0.4rem;
    background-color: rgba(var(--brand-rgb), 0.5);
    padding: 0.1rem 0.4rem;
    font-size: small;
    max-height: 3rem;
    border-radius: 0.3rem;
  }
}
.document-session{
  cursor: pointer;
}
</style>
