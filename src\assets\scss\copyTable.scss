.copy-dtx-table {
    max-height: 1050px;
    overflow-y: auto;
    display: block;
    table {
      border-radius: 6px;
      border: 1px solid #E9ECEF;
      width: 100%;
      max-width: 100%;
      font-size: 14px;
      border-collapse: separate;
      border-spacing: 0;
      table-layout: auto;
      position: relative;
      th {
        height: 55px;
        padding: 10px 14px;
        background-color: #F1F3F5;
        font-weight: 600;
        // padding: 14px;
        position: sticky;
        top: 0;
        text-align: left;
        text-overflow: ellipsis;
        font-size: 14px;
        z-index: 2;
      }
      td {
        padding: 10px 14px;
        font-size: 13px;
        font-weight: 400;
        text-align: left;
        white-space: nowrap;
        // overflow: hidden; // commented for form template 
        text-overflow: ellipsis;
        max-width: 200px; 
        & img {
          width: 17px; 
          height: 17px;
          margin: 0 3px;
        }
      }
      tr {
        height: 45px;
        // background-color: #F8F9FA;
      }
    }
    .copy-dtx-table th,
    .copy-dtx-table tr {
      padding: 12px;
    }
    tr:nth-child(odd) {
      background-color: rgba(#E9ECEF, 0.05);
      border: 1px solid #E9ECEF;
    }
      tr:hover {
        background-color: #F1F3F5;
        // cursor: pointer;
        transition: background 0.2s ease-in-out;
        box-shadow: 0px 2px 6px rgba(0,0,0,0.1)
      }
      .table-head, .table-body {
        display: block;
      }
    
      .table-head {
        background-color: #F1F3F5;
        font-weight: 600;
        position: sticky;
        top: 0;
        z-index: 2;
      }
    
      .table-body {
        overflow-y: auto;
        max-height: inherit;
      }
    
      .table-row {
        gap: 1;
        display: flex;
        align-items: center;
        height: 45px;
        border-bottom: 1px solid #E9ECEF;
    
        &:nth-child(odd) {
          background-color: rgba(#E9ECEF, 0.05);
        }
    
        &:hover {
          background-color: #F1F3F5;
          transition: background 0.2s ease-in-out;
          box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
        }
      }
    
      .table-cell {
        flex: 1;
        padding: 10px 14px;
        font-size: 13px;
        font-weight: 400;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
    
        &.center {
          text-align: center;
        }
    
        &.narrow {
          flex: 0 0 100px;
        }
    
        img {
          width: 17px;
          height: 17px;
          margin: 0 3px;
        }
      }
    
      .table-head .table-cell {
        font-weight: 600;
        font-size: 14px;
        background-color: #F1F3F5;
      }
  
    }
    td.location-column {
      text-align: left !important;
      padding-left: 10px;
      cursor: pointer;
    }
    
    /* Make sure the content inside is also aligned left */
    .location-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: fit-content;
      img {
        filter: brightness(0) saturate(100%) invert(30%) sepia(100%) saturate(1000%) hue-rotate(200deg);
      }
      &:hover {
    
        label {
        cursor: pointer;
        color: rgb(60, 60, 218);
        text-decoration: underline;
        }
      }
    }
    td.action-column {
      text-align: center;
      width: fit-content;
      // min-width: 100px;
      white-space: nowrap;
      .action-icons {
        // background-color: aqua;
        flex-direction: column;
        align-items: center;
        width: fit-content;
      }
    }
  
    td.action-column .edit,
    td.action-column .delete {
      width: 17px; 
      height: 17px;
      margin: 0 3px;
      cursor: pointer;
      display: inline-block;
      vertical-align: middle;
    }
  
  
  /* Small Laptops (1200px - 1366px) */
  @media screen and (max-width: 1366px) {
    .copy-dtx-table {
      .table-row {
        height: 60px;
      }
  
      .table-cell {
        padding: 10px;
        font-size: 12px;
      }
  
      .table-head .table-cell {
        font-size: 13px;
      }
    }
    .copy-dtx-table table {
      font-size: 11px;
      // table-layout: fixed;
    }
    .copy-dtx-table th {
      font-size: 13px;
    }
  
    .copy-dtx-table td {
  
      font-size: 12px;
    }
    .copy-dtx-table tr {
      height: 60px;
    }
  
    .copy-dtx-table th,
    .copy-dtx-table td {
      padding: 10px;
    }
  }
  
  /* Smaller Laptops (1024px - 1200px) */
  @media screen and (max-width: 1200px) {
    .copy-dtx-table table {
      font-size: 10px;
      // table-layout: fixed;
    }
  
    .copy-dtx-table th {
      font-size:11px;
    }
  
    .copy-dtx-table td {
      font-size: 10px;
    }
  
    .copy-dtx-table th,
    .copy-dtx-table td {
      padding: 8px;
    }
    .table-cell {
      font-size: 10px;
      padding: 8px;
    }

    .table-head .table-cell {
      font-size: 11px;
    }
  }
  
  @media screen and (max-height: 900px) {
    .copy-dtx-table {
      max-height: 60vh;
    }
  }
  
  /* Small-height screens */
  @media screen and (max-height: 799px) {
    .copy-dtx-table {
      max-height: 64vh;
    }
  }
  
  /* Very small screens or split view */
  @media screen and (max-height: 600px) {
    .copy-dtx-table {
      max-height: 60vh;
    }
  }
  /* Near Breakpoint (900px - 1024px) */
  @media screen and (max-width: 1024px) {
    .table-cell {
      font-size: 9px;
      padding: 4px 6px;
    }

    .table-head .table-cell {
      font-size: 10px;
    }
    .copy-dtx-table table {
      font-size: 9px;
    }
  
    .copy-dtx-table th {
      font-size: 10px;
    }
    .copy-dtx-table td {
      font-size: 9px;
    }
  
    .copy-dtx-table th,
    .copy-dtx-table td {
      padding: 4px 6px;
    }
  }
  .copy-dtx-table-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    max-height: 100%;
  }
  
  .copy-dtx-table-head {
    flex-shrink: 0;
    z-index: 2;
    background-color: #f1f3f5; // match your table head bg
    position: sticky;
    top: 0;
  }
  
  .copy-dtx-table-body {
    flex: 1;
    overflow-y: auto;
  }
  
  /* Extra Small Screens (Below 663px) */
  @media screen and (max-width: 663px) {
    .table-cell {
      font-size: 8px;
      padding: 3px 5px;
    }
    .table-head .table-cell {
      font-size: 9px;
    }
    .copy-dtx-table table {
      font-size: 9px;
    }
    .copy-dtx-table th {
      font-size: 9px;
    }
  
    .copy-dtx-table td {
      font-size: 8px;
    }
  
    .copy-dtx-table th,
    .copy-dtx-table td {
      padding: 3px 5px;
    }
  }
  