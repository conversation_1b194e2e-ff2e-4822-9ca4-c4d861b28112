
<template>
  <div :class="{
    'form-input': true,
    'form-input--required': data.required
    }" >
  <label>{{data.caption}}:
      <img
      v-if="data.visibility"
      class="mx-1"
      width="15"
      src="~@/assets/images/eye.svg"
      alt="view"
      />
      </label>
      <select :disabled="viewOnly" v-model="componentValue" @change="emitChange">
        <option v-for="item in companyList.data" :key="item.id" :value="item.id">
          {{item.company_name}}
        </option>
        <optgroup label="Child Tenants" v-if="childTenantsList.length">
            <option v-for="childTenant in filterChildTenant" :key="childTenant.id"
            :value="childTenant.target_tenant.id" :disabled="childTenant.status === 2 || childTenant.status === 3">
              {{ childTenant.target_tenant.company_name }}
            </option>
          </optgroup>
      </select>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'companyComponent',
  props: {
    mode: {
      type: String,
      default: 'TEMPLATE'
    },
    data: {
      type: Object,
      default: () => {}
    },
    value: {
      type: String,
      default: ''
    },
    viewOnly: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      componentValue: ''
    }
  },
  watch: {
    value (val) {
      this.componentValue = +val
    }
  },
  computed: {
    ...mapGetters('form', ['companyList']),
    ...mapGetters(['tenantList', 'childTenantsList']),
    filterChildTenant () {
      return this.childTenantsList.filter((item) => item.target_tenant.status === 1)
    }
  },
  methods: {
    updateValue () {
    },
    emitChange () {
      this.$emit('customChange', {
        field_id: this.$props.data.field_id,
        value: this.componentValue
      })
    }
  },
  created () {
    this.$store.dispatch('form/getCompanyList')
    this.componentValue = this.value
  }
}
</script>

<style lang="scss" scoped >

</style>
