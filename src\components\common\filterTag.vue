<template>
    <div>
      <div class="filter">
        <div class="searchInput input-group relative mt-1">
          <input type="text" :placeholder="title" @blur="changeDrawerStatus" @focus="onInputFocus"
            @keyup="showSuggestion" v-model.trim="value">
          <div class="resultBox absolute" v-if="suggestion" >
            <ul v-if="suggestions.length > 0">
              <li v-for="element in suggestions" :key="element.id" class="pointer" @click='addTag(element)'>
                {{ element.name }}
              </li>
            </ul>
            <span v-else-if="loading">Loading ... </span>
            <span v-else>
              No Tag Found!
            </span>
          </div>

        </div>
        <div class="tags mx-1 mt-1" v-for="tag in tags" :key="tag.id">
          <span>{{ tag.name }}</span>
          <img class="pointer" @click="removeTag(tag.id)" src="~@/assets/images/icons/close-icon.svg" width="16px" alt="" />
        </div>
      </div>
    </div>
</template>

<script>
import { SearchParentTags, SearchChildrenTags } from '@/api'
import { TagTrie } from '@/utils/tagsHelper'
import { debounce } from '@/utils/debounce'
export default {
  name: 'filterTag',
  props: {
    clear: {
      type: Boolean,
      default: false
    },
    tags: {
      type: Array,
      default () { return [] }
    },
    title: {
      type: String,
      default () { return 'Search Tags' }
    },
    type: {
      type: Number
    }
  },
  data () {
    return {
      value: '',
      loading: false,
      tagTrie: new TagTrie(),
      showSuggestion: null,
      suggestion: false,
      suggestions: []
    }
  },
  watch: {
    clear () {
      if (this.clear === true) {
        this.value = ''
        this.tagTrie.clearAll()
        this.$emit('clear')
      }
    }
  },
  methods: {
    addTag (tag) {
      if (tag) {
        this.tagTrie.insertTag({ name: tag.name, id: tag.id, parentId: tag.parent_id })
        this.$emit('new-tag', tag)
        this.suggestions = []
        this.value = ''
        this.hideSuggestion()
      }
    },
    changeDrawerStatus () {
      setTimeout(() => { this.hideSuggestion() }, 500)
    },
    hideSuggestion () {
      this.suggestion = false
    },
    onInputFocus () {
      this.suggestion = true
      this.getData()
    },
    getData () {
      this.loading = true
      if (!this.$props.tags.length) {
        SearchParentTags(this.value, this.$props.type)
          .then((res) => {
            this.suggestions = res.tag
          }
          )
          .catch((err) =>
            console.log(err)
          ).finally(() => {
            this.loading = false
          })
        return
      }
      SearchChildrenTags(this.$props.tags.at(-1).id, this.value, this.$props.type)
        .then((res) => {
          this.suggestions = res.tag
        }
        )
        .catch((err) =>
          console.log(err)
        ).finally(() => {
          this.loading = false
        })
    },
    removeTag (id) {
      this.tagTrie.deleteTagById(id)
      this.$emit('update-tags', this.tagTrie.getTagArray())
    }
  },
  beforeDestroy () {
    this.$emit('update-tags', [])
  },
  created () {
    this.showSuggestion = debounce(this.getData, 300)
  }
}
</script>

<style scoped lang="scss">
  .filter {
    margin-bottom: 0.8rem;
    display: flex;
    flex-wrap: wrap;

    input {
      padding: 0.85rem;
      border: none;
    }
    span {
      margin-right: 3px;
    }
  }
  .tags {
    display: flex;
    background-color: var(--brand-color);
    padding: 0.6rem;
    font-size: small;
    border-radius: 0.3rem;
  }
  .searchInput {
    border-radius: 5px;
    border: 1px solid rgba(59, 59, 59, 0.4666666667);
    border-radius: 4px;
  }
  .searchInput:focus {
    box-shadow: 0 0 0 1px var(--brand-color-1)
  }
  .resultBox {
    overflow: scroll;
    max-height: 12rem;
    padding: 2px 8px;
    opacity: 1;
    pointer-events: auto;
    z-index: 1000;
    background-color: var(--bg-color);
    width: 100%;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
    li {
      list-style: none;
      padding: 8px 12px;
      width: 100%;
      border-radius: 3px;
    }
    li:last-child {
      padding-bottom: 3px;
    }
    li:hover {
      background: #efefef;
    }
  }
</style>
