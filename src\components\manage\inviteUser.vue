<template>
  <div>
      <div class="v-center">
        <img
        class="mr-3 pointer"
        src="~@/assets/images/icons/arrow-back.svg"
        width="25px"
        alt=""
        @click="goBack"
      />
      <h3>Go back</h3>
      </div>
  <div class="invite-user pt-4">
    <h2>Invite your user here</h2>
    <div class="invite-user--form s">
      <div data-validation="FirstName" class="input-group imp my-4">
        <label>Enter your First Name</label>
        <input
          v-model="firstName"
          placeholder="Enter your First Name"
          type="text"
        />
      </div>
      <div data-validation="LastName" class="input-group imp my-4">
        <label>Enter your Last Name</label>
        <input
          v-model="lastName"
          placeholder="Enter your Last Name"
          type="text"
        />
      </div>
      <div data-validation="Email" class="input-group imp my-4">
        <label>Enter your Emai Id</label>
        <input v-model="email" placeholder="Enter your Emai Id" type="text" />
      </div>
      <div data-validation="PhoneNumber" class="input-group imp my-4">
        <label>Select User Role</label>
        <select v-model="role" >
          <option value="1">Admin</option>
          <option value="2">Editor</option>
          <option value="3">Collaborator</option>
          <option value="4">Viewer</option>
        </select>
      </div>
      <div class="input-group my-4">
         <label>Escalate To</label>
         <custom-dropdown :list="formattedUserListForReportingManagers"
         @select="selectReportingManager"
         @deselect="deselectReportingManager"
         :showDescription="true"
         :searchText="searchTextForReportingManager"
         :selected="selectedReportingManager">
          <div slot="before-items" class="p-2 input-group xs">
            <input placeholder="Search Users" type="text" v-model="searchTextForReportingManager" />
            </div>
          </custom-dropdown>
      </div>
      <div data-validation="TearmsAndCondition" class="v-center my-4">
        <input v-model="agreeTearms" type="checkbox" id="remember_me" />
        <label class="mx-2" for="remember_me"
          >By clicking here you agree to the terms and conditions.</label
        >
      </div>
      <div class="flex flex-end my-4">
        <button @click="resetForm" class="btn btn-black-outline mx-3">Reset</button>
        <button @click="tryInviteUser" :disabled="disableCreateButton" class="btn">Create Account</button>
      </div>
    </div>
  </div>
</div>
</template>

<script>
import { mapGetters } from 'vuex'
import InviteUserValidation from '@/helper/formValidation/inviteUser'
import { InviteUser } from '@/api'
import { alert, success } from '@/plugins/notification'
import CustomDropdown from '@/components/common/customDropdown.vue'
import Config from '@/config'

export default {
  name: 'InviteUserForm',
  components: {
    CustomDropdown
  },
  data () {
    return {
      disableCreateButton: false,
      firstName: '',
      lastName: '',
      email: '',
      role: '4',
      agreeTearms: false,
      selectedReportingManager: null,
      formattedUserListForReportingManagers: [],
      searchTextForReportingManager: ''
    }
  },
  mounted () {
    for (const user of this.tenantUsersList) {
      if (user.status === Config.USER_STATUS_MAP.ACTIVE) {
        this.formattedUserListForReportingManagers.push({
          id: user.associated_user?.id,
          name: user.associated_user?.first_name + ' ' + user.associated_user?.last_name,
          description: user.associated_user?.email
        })
      }
    }
  },
  methods: {
    selectReportingManager (item) {
      this.selectedReportingManager = item
    },
    deselectReportingManager () {
      this.selectedReportingManager = null
    },
    resetForm () {
      this.firstName = ''
      this.lastName = ''
      this.email = ''
      this.role = '4'
      this.agreeTearms = false
      this.selectedReportingManager = null
    },
    tryInviteUser () {
      if (this.disableCreateButton) return
      this.disableCreateButton = true
      const body = {
        firstName: this.firstName,
        lastName: this.lastName,
        email: this.email,
        role: this.role,
        agreeTearms: this.agreeTearms
      }
      if (this.selectedReportingManager) {
        body.reportingManager = this.selectedReportingManager.id
      }
      if (InviteUserValidation(body)) {
        body.agreeTearms = undefined
        InviteUser(body).then(data => {
          if (data.message === 'Tenant invitation sent successfully' || data.message === 'User invited to tenant successfully') {
            success('User invited successfully')
            this.resetForm()
            this.$router.go(-1)
            this.$store.dispatch('setup')
          } else {
            alert('There was an error, inviting the user.')
          }
        }).catch(() => {
          alert('There was an error, inviting the user. Please try again')
        }).finally(() => {
          this.disableCreateButton = false
        })
      } else {
        this.disableCreateButton = false
      }
    },
    goBack () {
      this.$router.go(-1)
    }
  },
  computed: {
    ...mapGetters(['tenantUsersList'])
  },
  created () {}
}
</script>

<style lang="scss" scoped >
.invite-user {
  max-width: 800px;
  margin: auto;
  & h2,
  & h3 {
    font-weight: 500;
  }
  & h3 {
    font-size: 16px;
  }
  &--form {
    background: var(--white);
    padding: 20px;
  }
}
</style>
