import { GQL } from '../graphQl'

export const CreateCustomListQuery = () => GQL`mutation CreateCustomListQuery(
  $data: [custom_list_values_insert_input!]!,
  $name: String
  ) {
  insert_core_custom_list(
    objects: {custom_list_values: {data: $data}, name: $name}
  ) {
    affected_rows
  }
}
`
export const ResourceStateValueCountQuery = () => GQL`query ResourceStateValueCountQuery($id: Int) {
  core_material_master(where: {resource_state_value: {id: {_eq: $id}}}) {
    material_name
  }
}
`
export const MaterialTypeCountQuery = () => GQL`query MaterialTypeCountQuery($id: String) {
  core_material_master(where: {material_type: {_eq: $id}}) {
    material_name
  }
}`
export const GetResourceGroupCountQuery = () => GQL`query getResourceGroupCountQuery($id: Int) {
  core_material_master(where: {resource_group: {_eq: $id}}) {
    material_name
  }
}
`
export const GetActiveCustomListsQuery = () => GQL`query GetActiveCustomListsQuery {
  core_custom_list(where: {deleted: {_eq: false}, tenant_id: {_is_null: false}},order_by: {created_on : asc}) {
    name
    id
    system_generated
    template_fields(where:{template_version:{active:{_eq:true}}}){
    field_id
   }
  }
}
`
export const GetTaskStatusQuery = () => GQL`query getTaskStatusQuery {
  custom_list_values(
    where: {deleted: {_eq: false}, custom_list: {name: {_eq: "Task Status"}}}
  ) {
    id
    name
  }
}`

export const GetCustomListQuery = () => GQL`query GetCustomListQuery($id: Int!) {
  core_custom_list_by_pk(id: $id) {
    name
    id
    custom_list_values(where: {deleted: {_eq: false}} order_by: {name: asc, editable: asc}) {
      editable
      deleted
      id
      name
    }
  }
}
`
// here you can pass array of custom list ids
export const GetCustomListsQuery = () => GQL`query GetCustomListsQuery($ids: [Int!]) {
  core_custom_list(where: {id: {_in: $ids}}) {
    id
    name
    custom_list_values (where: {deleted: {_eq: false}} order_by: {name: asc, editable: asc}){
      editable
      deleted
      id
      name
    }
  }
}`
export const UpdateUnitOfMaterialQuery = () => GQL`mutation changeDefaultValue($oldId: Int, $newId: Int) {
  update_core_material_master(
    where: {unit_of_material: {_eq: $oldId}}
    _set: {unit_of_material: $newId}
  ) {
    affected_rows
  }
}`

export const materialCustomListUpdateQuery = () => GQL`mutation changeDefaultValue($oldId: Int, $newId: Int) {
  update_core_material_master(
    where: {material_group: {_eq: $oldId}}
    _set: {material_group: $newId}
  ) {
    affected_rows
  }
}`

export const ResourceStateQuery = () => GQL`query fetchingResourceState {
  custom_list_values(
    where: {custom_list: {name: {_eq: "Resource State"}}, deleted: {_eq: false}}
  ) {
    id
    name
  }
}`

export const getStatusAssociatedTaskCount = () => GQL`query getStatusAssociatedTaskCountQuery($id: Int){
  core_tasks_aggregate(where: {status: {_eq: $id}}) {
    aggregate {
      count(columns: id)
    }
  }
}
`

export const changeDefaultValueQuery = () => GQL`mutation changeDefaultValue($oldId: Int, $newId: Int) {
  update_core_tasks(where: {status: {_eq: $oldId}}, _set: {status: $newId}) {
    affected_rows
  }
}`

export const DeleteCustomListQuery = () => GQL`mutation delete_custom_list($id:Int!) {
  update_core_custom_list_by_pk(_set: {deleted: true}, pk_columns: {id: $id}) {
    id
  }
}
`

export const UpdateCustomListQuery = () => GQL`mutation update_custom_list($id: Int!, $data: core_custom_list_set_input!) {
  update_core_custom_list_by_pk(_set: $data, pk_columns: {id: $id}) {
    id
  }
}
`

export const CreateCustomListValuesQuery = () => GQL`mutation create_custom_list_values($data: [custom_list_values_insert_input!]!) {
  insert_custom_list_values(objects:$data, on_conflict: {
    constraint: custom_list_values_name_custom_list_id_key, 
    update_columns: deleted, where: {deleted: {_eq: true}}}) {
    affected_rows
  }
}
`
export const UpdateCustomListValuesQuery = () => GQL`mutation update_custom_list_values($id: Int!,$data:custom_list_values_set_input!) {
  update_custom_list_values_by_pk(
    _set: $data,
    pk_columns: {id: $id}
  ) {
    id
  }
}
`

export const DeleteCustomListValuesQuery = () => GQL`mutation delete_custom_list_values($id: Int!) {
  update_custom_list_values_by_pk(
    _set: {deleted: true}
    pk_columns: {id: $id}
  ) {
    id
  }
}
`

export const validateCustomListQuery = () => GQL`query validateCustomListQuery(
  $name: String
  ) {
    core_custom_list(where: {name: {_ilike: $name}, deleted: {_eq: true}}) {
      id
    }
  }`

export const getAttachedMaterialCountQuery = () => GQL`query getAttachedMaterialCountQuery($id: Int) {
  core_material_master(where: {material_group: {_eq: $id}}) {
    material_name
  }
}`

export const GetUnitOfMaterialCountQuery = () => GQL`query GetUnitOfMaterialCountQuery($id: Int) {
  core_material_master(where: {unit_of_material: {_eq: $id}}) {
    material_name
  }
}`
