import config from '@/config'
import {
  runQuery,
  runMutation
} from '../graphQl'
import * as workflowQuery from '../query/workflow'
// import Config from '../../config.js'
import store from '@/store/index.js'
import http from '../http'
import { getworkflowEscalationHistoryQuery } from '../query/workflows'

export const GetAllWorkflowByVersionById = (versionId, tokenType = 'tenant') => {
  return runQuery(workflowQuery.GetAllWorkflowByVersionByIdQuery(), { id: versionId }, tokenType)
}
export const CreateWorkflowTemplateWIthPhases = (data, tokenType = 'tenant') => {
  return runQuery(workflowQuery.CreateWorkflowTemplateWIthPhasesMutation(), { data }, tokenType)
}
export const GetWorkflowTypes = (tokenType = 'tenant') => {
  return runQuery(workflowQuery.GetWorkflowTypesQuery(), { }, tokenType)
}
export const CreateWorkflowSteps = (data, tokenType = 'tenant') => {
  return runQuery(workflowQuery.CreateWorkflowStepsMutation(), { data }, tokenType)
}
export const CreateWorkflowTransition = (data, tokenType = 'tenant') => {
  return runQuery(workflowQuery.CreateWorkflowTransitionMutation(), { data }, tokenType)
}
export const getAllWorkFLowTemplates = (limit, pageNo, searchKeyword = '', tokenType = 'tenant') => {
  return runQuery(workflowQuery.getWorkFLowTemplatesQuery(), { limit, offset: (pageNo - 1) * limit, searchKeyword: `%${searchKeyword}%` }, tokenType)
}
// with out pagination
export const getFullWorkFLowTemplates = () => {
  return runQuery(workflowQuery.getfullWorkFLowTemplatesQuery(), { }, 'tenant')
}
export const GetWorkFlowDataByTemplateId = (templateId, tokenType = 'tenant') => {
  return runQuery(workflowQuery.GetWorkFlowDataByTemplateIdQuery(), { id: templateId }, tokenType)
}
export const updateWorkFlowTemplate = (draft, templateId, updates, tokenType = 'tenant') => {
  return runMutation(workflowQuery.updateWorkFlowTemplateMutaion(), { draft, templateId, updates }, tokenType)
}
export const UpdateWorkflowTemplateName = (id, name, tokenType = 'tenant') => {
  return runMutation(workflowQuery.UpdateWorkflowTemplateNameMutation(), { id, name }, tokenType)
}
export const workflowDataBasedOnInstanceId = (id, isExternalCollaborator = false) => {
  let tokenType = 'tenant'
  if (store.getters?.isOnProjectLevel) {
    tokenType = 'project'
  }
  return runQuery(workflowQuery.workflowDataBasedOnInstanceIdQuery(), { id }, tokenType)
}

export const updateWorkFlowActionApi = async (body) => {
  let tokenType = 'tenant'
  if (store.getters?.isOnProjectLevel) {
    tokenType = 'project'
  }
  return http.POST(config.serverEndpoint + '/workflows/history', body, tokenType)
}

export const getTransitionData = (transitionId) => {
  let tokenType = 'tenant'
  if (store.getters?.isOnProjectLevel) {
    tokenType = 'project'
  }
  return runQuery(workflowQuery.getTransitionDataQuery(), { transitionId }, tokenType)
}
export const getNextStepWithTransitionId = (transitionId) => {
  let tokenType = 'tenant'
  if (store.getters?.isOnProjectLevel) {
    tokenType = 'project'
  }
  return runQuery(workflowQuery.getNextStepWithTransitionIdQuery(), { id: transitionId }, tokenType)
}
export const getworkflowEscalationHistory = (stepId, wfInstanceId) => {
  let tokenType = 'tenant'
  if (store.getters?.isOnProjectLevel) {
    tokenType = 'project'
  }
  return runQuery(getworkflowEscalationHistoryQuery(), { stepId, wfInstanceId }, tokenType)
}

export const getWorkflowDashbordData = (userId, type, isOnProjectLevel = false) => {
  const conditions = {}
  const today = new Date()
  const nextWeek = new Date()
  nextWeek.setDate(today.getDate() + 7)

  const formatDate = (date) => date.toISOString().split('T')[0]
  conditions.user_id = { _eq: userId }
  conditions.is_current = { _eq: true }
  if (type === 'upcoming') {
    conditions.is_current = { _eq: false }
    conditions.planned_start_date = { _gte: formatDate(today), _lte: formatDate(nextWeek) }
  }
  return runQuery(workflowQuery.getDashboardDataQuery(), { conditions }, isOnProjectLevel ? 'project' : 'tenant')
}

export const CheckIfUserGroupIsAttachedToWorkflow = (id) => {
  return runQuery(workflowQuery.CheckIfUserGroupIsAttachedToWorkflowQuery(), { id }, 'tenant')
}
