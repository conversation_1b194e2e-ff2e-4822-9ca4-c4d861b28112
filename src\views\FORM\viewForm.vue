<template>
  <div class="view-form fh">
    <div class="fh center" v-if="loadViewForm">
      <loading-circle />
    </div>
    <div v-else class="fh">
      <div class="view-form--nav v-center space-between">
        <h2 class="weight-500 xxl v-center">
          <img
            src="~@/assets/images/icons/arrow-back.svg"
            width="30"
            @click="goBack"
          />{{ formTemplateData.name }}
        </h2>
        <div class="v-center">
<button class="btn"  v-if="showReOpen"  @click="reopenForm()"> <img width="15px" src="~@/assets/images/icons/undo-icon.svg" v-tooltip="'Reopen Form'"  ></button>
          <button class="btn btn-black v-center" @click="$router.push(`/form/editform/${templateId}/${templateName}/${formData.id}`)" v-if="editPermission"   v-tooltip="'Edit Form'" > <img class="mr-1" src="~@/assets/images/pencil.svg" width="15"> Edit</button>
        </div>
      </div>
      <div class="view-form--maincontainer">
        <div class="view-form--container">
          <div class="fh center" v-if="loading">
      <loading-circle />
    </div>
          <div v-else class="view-form--elements">
            <div class="view-form-autogenerated">
              <div v-if="formData.sequence_value" class="autogenerated">
                <span class="label">Form ID:</span>
                <span class="value">
                  {{ formData.sequence_value }}
                  </span>
                  </div>
              <template v-for="(ele, index) in templateField">
                <div
                  :key="index"
                  v-if="ele.autogenerated && ele.field_name !== 'project_id'"
                >
                  <span class="label">{{ ele.caption }}:</span>
                  <span class="value">{{
                    getValuesFromForm(ele.field_name)
                  }}</span>
                </div>
              </template>
            </div>
            <div
              :class="{
                dateComponent: true,
                'form-input': true,
                'form-input--required': true,
              }"
            >
              <label>Due Date:</label>
              <input v-model="formattedDueDate" :disabled="true" />
            </div>
            <template v-for="(ele, index) in templateField">
              <component
                v-if="
                  !ele.autogenerated &&
                  (ele.form_field.key !== 'BOM' || isOnProjectLevel)
                "
                :ref="ele.field_id"
                :key="index"
                :is="ele.form_field.key + '_COMPONENT'"
                :data="ele"
                :upload="ele?.form_field?.key === 'DOCUMENTS' ? true : false"
                :value="formValueData[ele.field_id]"
                :viewOnly="true"
                mode="VIEW"
              />
            </template>
          </div>
        </div>
        <div class="view-form--sideBar">
          <div class="tab-headers">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        :class="['tab-header', { active: selectedTab === tab.id }]"
        @click="selectedTab = tab.id"
      >
        {{ tab.name }}
      </div>
    </div>
    <div v-if="selectedTab === config.FORM_TAB_STATUS.REVISION_HISTORY || selectedTab ===  config.FORM_TAB_STATUS.HISTORY">
      <version-history :rootFormId="formData.root_parent_id" @selectedVersion="setup" :formId="formData.id" :selectedTab="selectedTab"/>
    </div>
    <div v-if="selectedTab ===  config.FORM_TAB_STATUS.COMMENTS">
      <form-comment-panel
        :form="formData"
        :disabled="true"
        @closeComment="openComment = false"
        :closeOnOutsideClick = true
      />
    </div>
    <div v-if="wftInstaceId && selectedTab === config.FORM_TAB_STATUS.WORKFLOW">
    <div class="activeStep">
      <!-- Top Row -->
      <div class="step-header space-between v-center">
        <div class="v-center">
          <div class="step-name">{{ workflowData?.workflow_stage?.name }}</div>
          <div class="mb-1 ml-2">
            <div
            :class="{
              'v-center workflowStatus status-tag': true,
              'not-started': workflowData?.state_value.name === 'NOT_STARTED',
              'ongoing': workflowData?.state_value.name === 'ONGOING',
              'completed': workflowData?.state_value.name === 'COMPLETED'
            }"
          >
            {{ workflowData?.state_value?.name | statusfomat }}
          </div>
          </div>
        </div>
        <router-link
          :to="`/workflows/${workflowData?.workflow_version?.workflow_template.type_value?.name}/?version=${workflowData?.workflow_version?.id}&wfinstance=${wftInstaceId}`"
          target="_blank"
        >
          <button class="btn btn-2 v-center" v-tooltip="'View Workflow'">
            <img
              src="~@/assets/images/icons/workflows.svg"
              width="18px"
              height="18px"
              alt="View Workflow"
            />
          </button>
        </router-link>
      </div>
      <!-- Dept Row -->
      <div style="margin-top: -18px;" class="flex v-venter">
        <div class="dept">Department : </div>
        <div class="date-value ml-1"> {{ workflowData?.workflow_stage?.core_user_group?.name ?? '--' }}</div>
        <br>
      </div>
      <div class="border"></div>
      <!-- Date/Duration Row -->
      <div class="date-row space-between">
        <div class="date-block">
          <div class="xs label">Start Date</div>
          <div class="flex v-center">
            <div class="date-detail mr-1">Expected : </div>
            <div class="date-value" >{{ workflowData?.workflow_instance_steps?.[0]?.planned_start_date | genericFormatDate }}</div>
          </div>
          <div class="flex v-center">
            <div class="date-detail">Actual : </div>
            <div class="date-value"> {{ new Date() | genericFormatDate }}</div>
          </div>
        </div>

        <div class="duration-block">
          <div class="label">Duration</div>
          <div class="duration-value">{{ workflowData?.workflow_stage?.duration }} hours</div>
        </div>

              <div class="date-block">
                <div class="xs label">End Date</div>
                <div class="v-center">
                  <div class="date-detail">Expected : </div>
                  <div class="date-value"> {{ workflowData?.workflow_instance_steps?.[0]?.planned_end_date | genericFormatDate }}</div>
                </div>
                <div class="flex v-center">
                  <div class="date-detail">Actual : </div>
                  <div class="date-value"> {{ new Date() | genericFormatDate }}</div>
                </div>
             </div>
            </div>
         <!-- Upload Section -->
        <!-- <div class="upload-row">
          <div class="upload-box">
          <div class="weight-500" >File Upload</div>
        <img src="~@/assets/images/icons/upload-icon.svg" class="upload-icon" />
        <button class="btn">Browse</button>
      </div>
  <div class="uploaded-files-box">
    <div class="uploaded-header">Uploaded files</div>
    <div v-if="files.length" class="uploaded-list">
      <div
        v-for="(file, index) in files"
        :key="index"
        class="uploaded-file"
      >
        <div class="file-name">{{ file.name }}</div>
        <div class="file-status">
          <span v-if="file.status === 'uploading'" class="loader"></span>
          <span v-else>{{ file.size }}</span>
        </div>
      </div>
    </div>
      <div v-else class="no-files">
    No Files Attatched
  </div>
  </div>
        </div> -->

    </div>
      </div>
      <div v-if="selectedTab === config.FORM_TAB_STATUS.LINKED_ENTITIES">
        <linked-entities :entities="entities" :form-id="this.$route.params.formId"/>
        </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  // EditForm,
  getDetailFormTemplate,
  GetFormDataByFormId,
  getGivenCustomListsData,
  workflowDataBasedOnInstanceId,
  getFormLinks
} from '@/api'
import { mapGetters } from 'vuex'
import { alert } from '@/plugins/notification'
import { genericFormatDate } from '@/utils/date'
import { timeStampToDate } from '@/filters/dateFilter'
import { GetFormValueMap } from '@/helper/formValue.js'
import config from '@/config'
import UploadDocument from '@/components/document/uploadDocument.vue'
import timeComponent from '@/components/form/elements/timeComponent.vue'
import configurationList from '@/components/form/elements/configurationList.vue'
import fileComponent from '@/components/form/elements/fileComponent.vue'
import dateComponent from '@/components/form/elements/dateComponent.vue'
import longTextComponent from '@/components/form/elements/longTextComponent.vue'
import numberComponent from '@/components/form/elements/numberComponent.vue'
import switchComponent from '@/components/form/elements/switchComponent.vue'
import textComponent from '@/components/form/elements/textComponent.vue'
import companyComponent from '@/components/form/elements/companyComponent.vue'
import userComponent from '@/components/form/elements/userComponent.vue'
import multiUserComponent from '@/components/form/elements/multiUserComponent.vue'
import materialComponent from '@/components/form/elements/materialComponent.vue'
import locationComponent from '@/components/form/elements/locationComponent.vue'
import LoadingCircle from '../../components/common/loadingCircle.vue'
import productCodeComponent from '@/components/form/elements/productCodeComponent.vue'
import contactListComponent from '@/components/form/elements/contactListComponent.vue'
import documentsComponent from '@/components/form/elements/documentsComponent.vue'
import FormCommentPanel from '../../components/form/formCommentPanel.vue'
import bomComponent from '@/components/form/elements/bomComponent.vue'
import TagsComponent from '@/components/form/elements/tagsComponent.vue'
// import confirmationDialog from '@/plugins/confirmationDialog'
import versionHistory from './versionHistory.vue'
import LinkedEntities from './linkedEntities.vue'
// import { alert, success } from '@/plugins/notification'

export default {
  name: 'ViewFormComponent',
  components: {
    BOOLEAN_COMPONENT: switchComponent,
    NUMBER_COMPONENT: numberComponent,
    TEXT_COMPONENT: textComponent,
    LONG_TEXT_COMPONENT: longTextComponent,
    DATE_COMPONENT: dateComponent,
    TIME_COMPONENT: timeComponent,
    CONFIGURATION_LIST_COMPONENT: configurationList,
    ATTACHMENT_COMPONENT: fileComponent,
    COMPANY_COMPONENT: companyComponent,
    USER_COMPONENT: userComponent,
    MULTI_USER_COMPONENT: multiUserComponent,
    MATERIALS_COMPONENT: materialComponent,
    LOCATION_COMPONENT: locationComponent,
    PRODUCT_CODE_COMPONENT: productCodeComponent,
    BOM_COMPONENT: bomComponent,
    TAGS_COMPONENT: TagsComponent,
    CONTACT_LIST_COMPONENT: contactListComponent,
    DOCUMENTS_COMPONENT: documentsComponent,
    LoadingCircle,
    FormCommentPanel,
    versionHistory,
    UploadDocument,
    LinkedEntities
  },
  filters: {
    timeStampToDate,
    genericFormatDate,
    statusfomat (value) {
      if (!value) return ''
      return value.replace(/_/g, ' ')
    }
  },
  data () {
    return {
      entities: [],
      files: [],
      templateId: '',
      templateName: '',
      formTemplateData: {},
      formData: {},
      formValueData: {},
      loading: false,
      openComment: false,
      wftInstaceId: null,
      workFlowId: null,
      workflowData: {},
      selectedTab: 1,
      loadViewForm: false,
      tabs: [
        {
          id: 1,
          name: 'WorkFlow'
        },
        {
          id: 2,
          name: 'Comments'
        },
        {
          id: 3,
          name: 'Version History'
        },
        {
          id: 4,
          name: 'History'
        },
        {
          id: 5,
          name: 'Linked Entities'
        }
      ],
      config: config
    }
  },
  computed: {
    ...mapGetters(['isOnProjectLevel', 'user', 'collaborator', 'openProjectId', 'isExternalCollaborator']),
    formattedDueDate () {
      return timeStampToDate(this.formData.due_date)
    },
    formVersion () {
      return this.formTemplateData?.template_versions?.find(
        (item) => item.id === this.formData.template_version_id
      )
    },
    templateField () {
      return this.formVersion?.template_fields
    },
    editPermission () {
      if (this.formData.status === config.FORM_STATE_MAP.CLOSED) {
        return false
      } else if (
        this.user.tenantLevelRole === 'ADMIN' || this.user.projectLevelRole === 'ADMIN') {
        return true
      } else if (this.wftInstaceId && this.workflowData?.workflow_stage?.core_user_group) {
        for (const user of this.workflowData?.workflow_stage?.core_user_group.core_user_group_members) {
          if (this.user.userId === user.user_id) {
            return true
          }
        }
        if (this.workflowData?.workflow_instance_steps?.[0]?.instance_step_assignees) {
          return this.isAssignee(this.workflowData?.workflow_instance_steps?.[0]?.instance_step_assignees)
        }
      } else if (!this.workflowData?.workflow_stage?.core_user_group) {
        return (this.user.userId === this.formData.created_by_user?.id || this.isAssignee(this.workflowData?.workflow_instance_steps?.[0]?.instance_step_assignees))
      }
      return false
    },
    showReOpen () {
      if (this.formData.status === 4 && (this.user.userId === this.formData.created_by || this.user.tenantLevelRole === 'ADMIN' || this.user.projectLevelRole === 'ADMIN') && !this.collaborator && this.formData.next_revisions_aggregate.aggregate.count === 0) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {
    handleFileUpload (event) {
      const selectedFiles = Array.from(event.target.files)
      console.log(selectedFiles)
      selectedFiles.forEach(file => {
        this.files.push({
          name: file.name,
          status: 'uploading',
          rawFile: file
        })
        // Simulate upload
        setTimeout(() => {
          file.status = 'done'
        }, 1000)
      })
    },
    setup (form) {
      const updatedFormId = form ? form.id : this.$route.params.formId
      this.loading = true
      this.loadViewForm = true
      GetFormDataByFormId(updatedFormId, this.isOnProjectLevel).then(
        (res) => {
          this.formData = res.core_forms_by_pk
          getFormLinks(updatedFormId).then((links) => {
            this.entities = links?.message || []
          }).catch(() => {
            alert('Failed to fetch form links')
          })
          this.formValueData = GetFormValueMap(res.core_forms_by_pk)
          this.wftInstaceId = res.core_forms_by_pk.workflow_instance_id
          this.getWorkFlowStepData(this.wftInstaceId)
          this.getFormTemplateData()
        }
      ).catch(() => {
        alert('Failed to fetch form data')
        this.loading = false
        this.loadViewForm = false
      })
    },
    getValuesFromForm (key) {
      key = key.toLowerCase()
      switch (key) {
      case 'created on':
      case 'created_on':
        if (this.formData.created_on) {
          return timeStampToDate(this.formData.created_on)
        }
        return '--'
      case 'created by':
      case 'created_by': {
        if (this.formData.created_by) {
          return (
            this.formData.created_by_user.first_name +
              ' ' +
              this.formData.created_by_user.last_name
          )
        }
        return '--'
      }
      case 'updated_on':
      case 'updated on':
        if (this.formData.updated_on) {
          return timeStampToDate(this.formData.updated_on)
        }
        return '--'
      case 'updated_by':
      case 'updated by': {
        if (this.formData.updated_by) {
          return (
            this.formData.updated_by_user.first_name +
              ' ' +
              this.formData.updated_by_user.last_name
          )
        }
        return '--'
      }
      case 'project id':
      case 'project_id': {
        if (this.formData.project_id) {
          return this.formData.core_project?.name
        }
        return '--'
      }
      }
    },
    goBack () {
      this.$router.push(
        `/form/${this.templateId}/${this.templateName}`
      )
    },
    getFormTemplateData () {
      this.loading = true
      this.loadViewForm = true
      getDetailFormTemplate(this.templateId).then(async (res) => {
        this.formTemplateData = res.core_form_templates_by_pk
        const customListData = this.findUsedCustomListIds(res.core_form_templates_by_pk.template_versions)
        if (customListData.size > 0) {
          await this.getGivenCustomList([...customListData])
        }
        this.loading = false
        this.loadViewForm = false
      })
    },
    getWorkFlowStepData () {
      if (!this.wftInstaceId) {
        return
      }
      workflowDataBasedOnInstanceId(this.wftInstaceId, this.isExternalCollaborator).then((res) => {
        this.workflowData = res.workflow_instances_by_pk
      })
    },
    isAssignee (assignees = []) {
      for (const assignee of assignees) {
        if (assignee.user_id === this.user.userId) {
          return true
        }
      }
    },
    reopenForm () {
      const formId = this.formData.id
      this.$router.push(`/form/createform/${this.templateId}/${this.templateName}/${formId}`)
      // confirmationDialog(
      //   'Are you sure you want to reopen ?',
      //   (res) => {
      //     if (res) {
      //       EditForm({ formId: formId, status: 3 })
      //         .then((res) => {
      //           this.formData.status = 3
      //           success(res?.message ?? 'Successfully reopened form')
      //         })
      //         .catch(() => {
      //           alert('Failed to reopen form')
      //         })
      //     }
      //   }
      // )
    },
    getFormData (form) {
      const updatedFormId = form ? form.id : this.$route.params.formId
      GetFormDataByFormId(updatedFormId, this.isOnProjectLevel).then(
        (res) => {
          this.formData = res.core_forms_by_pk
          this.formValueData = GetFormValueMap(res.core_forms_by_pk)
          this.wftInstaceId = res.core_forms_by_pk.workflow_instance_id
          this.getWorkFlowStepData(this.wftInstaceId)
          this.getFormTemplateData()
        }
      )
    },
    findUsedCustomListIds (templateVersions) {
      const configIdSet = new Set()
      for (const templateVersion of templateVersions) {
        for (const field of templateVersion.template_fields) {
          if (field.field_type_id === config.FORM_TYPE.CONFIGRATION_LIST) {
            configIdSet.add(field.custom_list_id)
          }
        }
      }
      return configIdSet
    },
    async getGivenCustomList (ids) {
      const customListData = await getGivenCustomListsData(ids)
      const customListMap = {}
      for (const customList of customListData.core_custom_list) {
        customListMap[customList.id] = customList.custom_list_values
      }
      this.$store.dispatch('form/saveCustomListMap', customListMap)
    }
  },
  mounted () {
    this.setup()
    // GetFormDataByFormId(this.$route.params.formId, this.isOnProjectLevel).then(
    //   (res) => {
    //     this.formData = res.core_forms_by_pk
    //     this.formValueData = GetFormValueMap(res.core_forms_by_pk)
    //     this.wftInstaceId = res.core_forms_by_pk.workflow_instance_id
    //     this.getWorkFlowStepData(this.wftInstaceId)
    //     this.getFormTemplateData()
    //   }
    // )
  },
  created () {
    this.templateId = this.$route.params.templateId
    this.templateName = this.$route.params.templateName
    this.setup()
  }
}
</script>

<style lang="scss" scoped>
.view-form {
  &--nav {
    height: 60px;
    margin: -12px;
    padding: 0 20px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }

  &--container {
    padding: 20px;
    margin-top: 10px;
    max-width: 700px;
    // margin: 0 auto;
    height: 100%;
    overflow-y: auto;
    box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
    margin-top: 10px;
    border-radius: 6px;
  }

  &-autogenerated {
    background-color: rgba(var(--brand-rgb), 0.2);
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    grid-gap: 10px;

    & .label {
      font-size: 14px;
      color: var(--brand-color-1);
      font-weight: 500;
    }

    & .value {
      margin-left: 6px;
      font-size: 16px;
      color: var(--text-color);
    }
  }

  &--sideBar {
    // padding: 20px;
    margin-top: 9px;
    max-width: 568px;
    height: calc(100%);
    // overflow-y: auto;
    border-radius: 6px;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;

    // background: white;
    // & .activeStep {
    //   display: flex;
    //   flex-direction: column;
    //   gap: 20px;
    //   box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
    //   padding: 20px;
    //   background: white;
    //   border-radius: 6px;
    //   margin: 1px;
    // }

    & select {
      border: var(--border);
      background-color: transparent;
      border-radius: 4px;
      padding: 4px 8px;
      height: 30px;
      width: 150px;
      outline: none;

      &:focus {
        border-color: var(--brand-color);
      }
    }
  }

  &--maincontainer {
    display: flex;
    justify-content: center;
    gap: 20px;
    padding: 20px;
    height: calc(100% - 60px);
  }

  &-step-selection {
    width: 100%;
    box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
    padding: 20px;
    background: white;
    border-radius: 6px;

    &-list {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      justify-content: center;
    }

    &-item {
      border: 1px solid rgb(73, 73, 73, 0.3);
      padding: 10px 20px;
      border-radius: 5px;
      background-color: rgb(126, 172, 248, 0.6);
      font-size: 12x;
      color: black;
      cursor: pointer;
    }
  }
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  align-items: center;

  & .label {
  }

  & .value {
  }
}

.comment-btn {
  position: fixed;
  bottom: 0;
  right: 0;
  margin: 12px;
  z-index: 9;
  background: var(--white);
  cursor: pointer;
  border-radius: 50%;
  height: 45px;
  width: 45px;
  border: none;
  box-shadow: 4px 4px 4px 0 rgba(0, 0, 0, 0.25);

  & img {
    width: 30px;
    height: 30px;
  }

  &:hover {
    background: var(--brand-color);

    & img {
      filter: invert(1);
    }
  }

  &:active {
    box-shadow: inset 4px 4px 4px 0 rgba(0, 0, 0, 0.25);
  }
}

.dateComponent {
  font-size: 12px;

  & label {
    font-size: 1.3em;
  }
}
.tabs {
  border: 1px solid #ccc;
  border-radius: 6px;
  overflow: hidden;
  font-family: sans-serif;
}

.tab-headers {
  display: flex;
  background-color: #f1f1f1;
  border-bottom: 1px solid #ccc;
}

.tab-header {
  padding: 12px 18px;
  cursor: pointer;
  flex: 1;
  text-align: center;
  transition: background 0.3s;
}

.tab-header:hover {
  background-color: #e0e0e0;
}

.tab-header.active {
  background-color: #fff;
  font-weight: 500;
  border-bottom: 2px solid var(--brand-color);
}

.form-comment {
  position: fixed;
  bottom: 30px;
  right: -700px;
  top: 130px;
  width: 400px;
  z-index: 9;

  transition: right 0.8s ease-in-out;

  &[arial-data="open"] {
    right: 20px;
  }
}
.status-tag {
  padding: 2px 4px;
  border-radius: 6px;
  font-weight: 600;
  display: inline-block;
  min-width: 100px;
  text-align: center;
}
.not-started {
  background-color: rgba(255, 0, 0, 0.1);
  color: purple;
}

.completed {
  background-color: rgba(0, 128, 0, 0.1);
  color: green;
}

.ongoing {
  background-color: rgba(255, 165, 0, 0.1);
  color: orange;
}
.activeStep {
  padding: 5px 5px;
  background: white;
  border-radius: 6px;
  box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  gap: 20px;

  .step-row {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .step-left {
      display: flex;
      flex-direction: column;
      gap: 6px;

      .step-name {
        font-size: 28px;
        font-weight: 500;
      }
    }

    .step-center {
      text-align: center;

      .step-label {
        font-size: 14px;
        // color: gray;
      }

      .step-duration {
        font-size: 20px;
        font-weight: 500;
      }
    }

    .step-right {
      align-self: flex-start;
    }
  }

  .step-department {
    font-size: 20px;
    font-weight: 500;
    color: #333;
  }

  .step-info-row {
    display: flex;
    justify-content: space-between;
    gap: 40px;

    .start-block,
    .end-block {
      display: flex;
      flex-direction: column;
      gap: 10px;
      flex: 1;

      .block-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 6px;
      }

      .block-item {
        display: flex;
        gap: 8px;

        .label {
          font-weight: 500;
          // color: #666;
        }

        .value {
          font-weight: 600;
        }
      }
    }
  }
}
.activeStep {
  padding: 16px 20px 20px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-name {
  font-size: 28px;
  font-weight: 600;
}

.dept {
  font-size: 12px;
  // margin-top: -20px;
  color: #696969;
  font-weight: 400;
}
.border {
  // box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.5);
  border-top: 1px solid #DCDCDC;
}
.date-row {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.label {
  font-size: 12px;
  color: #696969;
  font-weight: 500;
  margin-bottom: 4px;
}

.date-block {
  display: flex;
  flex-direction: column;
  font-size: 14px;
}

.date-detail {
  margin-right: 2px;
  font-size: 12px;
  color: #696969;
  margin-bottom: 2px;
}
.date-value {
  font-size: 12px;
}

.duration-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.duration-value {
  // color: #696969;
  font-size: 12px;
  font-weight: 500;
  margin-top: -5px;
}

.upload-row {
  display: flex;
  gap: 20px;
  padding-top: 16px;
  align-items: flex-start;
  border-top: 1px solid #ccc;
  margin-top: 12px;
}

.upload-box {
flex-direction: column;
border-right: 1px solid #ccc;
  padding-right: 10px;
  width: 30%;
  height: 100%;
  margin-right: 10px;
  text-align: center;
  padding: 20px;
}
.upload-icon {
  width: 40px;
  height: 40px;
  object-fit: contain;
  margin-bottom: 8px;
}

.uploaded-files-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.uploaded-header {
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 15px;
  white-space: nowrap;
}

.uploaded-list {
  overflow-y: auto;
  max-height: 120px;
  padding-right: 8px;
}
.no-files {

}

.uploaded-file {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #eee;
  font-size: 14px;
  color: #333;
}

.file-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
}

.file-status {
  white-space: nowrap;
  font-size: 13px;
  color: #888;
}

.loader {
  width: 14px;
  height: 14px;
  border: 2px solid #ccc;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
