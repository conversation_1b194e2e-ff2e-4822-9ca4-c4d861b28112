import { runQuery, runMutation } from '../graphQl'
import http from '../http/index'
import config from '@/config'
import store from '../../store'

import {
  GetMaterialMasterDataQuery,
  GetMaterialMasterForBomQuery,
  CreateMaterialMaster,
  UpdateMaterialMasterDataQuery,
  GetSingleMaterialMasterDataQuery,
  getProductsUsingMaterialQuery,
  UpdateMaterialStatusQuery,
  unitOfMaterial,
  weightUnit,
  materialGroup,
  resourceGroup,
  ChangeResourceStatusQuery,
  addressLocation,
  removeAttachmentsDocQuery,
  checkMaterialID,
  getProjectLevelProductsUsingMaterialQuery,
  upadateMaterialFormsQuery,
  GetMaterialMasterDataQueryForCsv,
  attachDocToMaterailMutation,
  createMaterialMasterMutation
} from '../query/materialMaster'

export const GetMaterialMasterData = (filter, csvQuery, sorting) => {
  const conditions = []
  console.log(sorting)
  let orderBy = { created_on: 'desc' }
  if (sorting === '2') {
    orderBy = { created_on: 'asc' }
  } else if (sorting === '1') {
    orderBy = { created_on: 'desc' }
  } else if (sorting === '3') {
    orderBy = { material_name: 'asc' }
  } else if (sorting === '4') orderBy = { material_name: 'desc' }
  filter.orderBy = orderBy
  if (filter.tagId) {
    conditions.push({
      tag_materials: { tag_id: { _eq: filter.tagId } }
    })
  }
  if (filter.type === 'material') {
    conditions.push({
      type: {
        _eq: 1
      }
    })
  } else if (filter.type === 'resource') {
    conditions.push({
      type: {
        _eq: 2
      }
    })
  }
  filter.type = undefined
  if (filter.searchKeyword) {
    conditions.push({
      _or: [
        { custom_material_id: { _ilike: filter.searchKeyword } },
        { erp_material_id: { _ilike: filter.searchKeyword } },
        { material_description: { _ilike: filter.searchKeyword } },
        { material_name: { _ilike: filter.searchKeyword } },
        { plm_material_id: { _ilike: filter.searchKeyword } },
        {
          material_product_code: { product_code: { _ilike: filter.searchKeyword } }
        }
      ]
    })
  }
  if (filter.customListItmes) {
    if (Object.hasOwn(filter.customListItmes, 'material_group')) {
      conditions.push({
        material_group: { _eq: filter.customListItmes.material_group.id }
      })
    }
    if (Object.hasOwn(filter.customListItmes, 'unit_of_material')) {
      conditions.push({
        unit_of_material: { _eq: filter.customListItmes?.unit_of_material?.id }
      })
    }
    if (Object.hasOwn(filter.customListItmes, 'weight_unit')) {
      conditions.push({
        weight_unit: { _eq: filter.customListItmes.weight_unit?.id }
      })
    }
    if (Object.hasOwn(filter.customListItmes, 'storage_loc')) {
      conditions.push({
        storage_loc: { _eq: filter.customListItmes.storage_loc.id }
      })
    }
  }

  if (filter.effectiveDate) {
    if (filter.effectiveDate.from && filter.effectiveDate.to) {
      conditions.push({
        effective_date: { _gte: new Date(filter.effectiveDate.from), _lte: new Date(filter.effectiveDate.to) }
      })
    } else if (filter.effectiveDate.from) {
      conditions.push({
        effective_date: { _gte: new Date(filter.effectiveDate.from) }
      })
    } else if (filter.effectiveDate.to) {
      conditions.push({
        effective_date: { _lte: new Date(filter.effectiveDate.to) }
      })
    }
  }
  if (filter.leadtime) {
    if ((filter.leadtime.from || filter.leadtime.from === 0) && filter.leadtime.to) {
      conditions.push({
        lead_time: { _gte: parseInt(filter.leadtime.from), _lte: parseInt(filter.leadtime.to) }
      })
    } else if (filter.leadtime.from || filter.leadtime.from === 0) {
      conditions.push({
        lead_time: { _gte: parseInt(filter.leadtime.from) }
      })
    } else if (filter.leadtime.to) {
      conditions.push({
        lead_time: { _lte: parseInt(filter.leadtime.to) }
      })
    }
  }
  if (filter.resourseStatusId) {
    conditions.push({
      resource_state_value: { id: { _eq: parseInt(filter.resourseStatusId) } }
    })
  }
  if (filter.id) {
    conditions.push({
      id: { _eq: filter.id }
    })
    filter.id = undefined
  }
  filter.resourseStatusId = undefined
  if (conditions.length > 0) {
    filter.conditions = {
      _and: conditions
    }
  }
  filter.effectiveDate = undefined
  filter.leadtime = undefined
  filter.searchKeyword = undefined
  filter.tagId = undefined
  filter.customListItmes = undefined
  if (csvQuery) {
    filter.orderBy = undefined
  }
  return runQuery(csvQuery ? GetMaterialMasterDataQueryForCsv() : GetMaterialMasterDataQuery(), filter, 'tenant')
}

export const GetSingleMaterialMasterData = (filter) => {
  return runQuery(GetSingleMaterialMasterDataQuery(), filter, 'tenant')
}

export const GetMaterialMasterForBom = (filter, project = false) => {
  const conditions = []
  if (filter.searchKeyword) {
    conditions.push({
      _or: [
        { custom_material_id: { _ilike: filter.searchKeyword } },
        { erp_material_id: { _ilike: filter.searchKeyword } },
        { material_description: { _ilike: filter.searchKeyword } },
        { material_name: { _ilike: filter.searchKeyword } },
        { plm_material_id: { _ilike: filter.searchKeyword } },
        {
          material_product_code: { product_code: { _ilike: filter.searchKeyword } }
        }
      ]
    })
  }
  if (filter.tagId) {
    conditions.push({
      tag_materials: { tag_id: { _eq: filter.tagId } }
    })
  }
  conditions.push({
    status: { _neq: 2 }
  })
  if (conditions.length > 0) {
    filter.conditions = {
      _and: conditions
    }
  }
  filter.searchKeyword = undefined
  filter.tagId = undefined
  return runQuery(GetMaterialMasterForBomQuery(), filter, project ? 'project' : 'tenant')
}

export const UpdateMaterialMasterData = (variables) => {
  return runMutation(UpdateMaterialMasterDataQuery(), variables, 'tenant')
}

export const CreateMaterialMasterData = (variables) => {
  return runMutation(CreateMaterialMaster(), variables, 'tenant')
}

export const GetProductsUsingMaterialQuery = (materialId) => {
  if (store.getters?.isOnProjectLevel) {
    const projectId = localStorage.getItem('projectId')
    return runQuery(getProjectLevelProductsUsingMaterialQuery(), { material_id: materialId, project_id: projectId }, 'project')
  } else {
    return runQuery(getProductsUsingMaterialQuery(), { material_id: materialId }, 'tenant')
  }
}

export const UpdateMaterialStatus = (id, status) => {
  return runMutation(UpdateMaterialStatusQuery(), {
    id, status
  }, 'tenant')
}

export const UnitOfMaterial = () => {
  return runQuery(unitOfMaterial(), null, 'tenant')
}

export const WeightUnit = () => {
  return runQuery(weightUnit(), null, 'tenant')
}

export const MaterialGroup = () => {
  return runQuery(materialGroup(), null, 'tenant')
}

export const ResourceGroup = (name) => {
  return runQuery(resourceGroup(), { name }, 'tenant')
}

export const ChangeResourceStatus = (status, id) => {
  return runMutation(ChangeResourceStatusQuery(), { id, status }, 'tenant')
}

export const AddressLocation = () => {
  return runQuery(addressLocation(), null, 'tenant')
}
// to be delelted
// export const attchMaterilIdToDoc = (materialId, docId) => {
//   return runMutation(attchMaterilIdToDocQuery(), {
//     material_id: materialId, docId
//   }, 'tenant')
// }
export const attachDocToMaterail = (attachDocData) => {
  return runMutation(attachDocToMaterailMutation(), {
    data: attachDocData
  }, 'tenant')
}
export const removeAttachments = (materialId, docIds) => {
  return runMutation(removeAttachmentsDocQuery(), {
    docIds,
    materialId
  }, 'tenant')
}

export const getMaterialDataByApi = (body) => {
  return http.POST(config.serverEndpoint + '/filter/materials', body, 'tenant')
}

export const CheckMaterialID = (customMaterialId) => {
  return runQuery(checkMaterialID(), {
    materialID: customMaterialId
  }, 'tenant')
}
export const upadateMaterialForms = (materialId, formId) => {
  return runQuery(upadateMaterialFormsQuery(), {
    materialId, formId
  }, 'tenant')
}

export const createMaterialMaster = (body) => {
  return runMutation(createMaterialMasterMutation(), body, 'tenant')
}
