<template>
    <div class="project">
      <div class="project-bar v-center space-between">
        <h2 class="xxxl weight-500">{{ currentProject.name }}</h2>
        <div class="v-center">
          <button class="btn btn-black mx-2" @click="editProject" v-if="isTenantAdminOrCollaborator">
            Edit Project
          </button>
        </div>
      </div>
      <div class="details">
          <div class="project-row">
            <div class="weight-500">Revenue:</div>
            <div>Rs {{ currentProject.project_revenue }}</div>
          </div>
          <div class="project-row">
            <div class="weight-500">Location:</div>
            <div class="location" @click="openMaps(currentProject.location)" >View On Map</div>
        </div>
        <div>
          <div class="project-row">
            <div class="weight-500">Address:</div>
            <label>{{ currentProject.address.address }}, {{ currentProject.address.city }}, {{ currentProject.address.state }}, {{ currentProject.address.pincode }}</label>
          </div>
        </div>
</div>
<div class="search-container">
  <div class="search-bar input-group search ml-4">
      <input
      class="input-search"
      v-model="searchKeyword"
      type="text"
      placeholder="Search"
      />
  </div>
</div>
      <div class="copy-dtx-table project-table">
        <table>
            <thead>
                <th>Name</th>
                <th>Email</th>
                <th>Role</th>
            </thead>
            <tbody v-for="(user, index) in getSearchResult" :key="index">
                <tr>
                    <td>
                    <div class="project-user-table__body__row__item v-center">
                        <avatar size="24px" :id="user.user_id" class="mr-2" />
                        {{ user.associated_user.first_name + ' ' + user.associated_user.last_name}}
                    </div>
                    </td>
                    <td>{{ user.associated_user.email }}</td>
                    <td>{{ roleMap[user.role_id] }}</td>
                </tr>
            </tbody>
        </table>
      </div>
      <!-- <div class="project-user-table">
        <div class="project-user-table__header">
          <div class="project-user-table__header__item">Name</div>
          <div class="project-user-table__header__item">Email</div>
          <div class="project-user-table__header__item">Role</div>
        </div>
        <div class="project-user-table__body">
          <template v-for="(user, index) in currentProject.project_user_associations">
            <div
              class="project-user-table__body__row"
              :key="user.id"
              v-if="user.status !== 3"
            >
              <div class="project-user-table__body__row__item v-center">
                <avatar size="24px" :id="user.user_id" class="mr-2" />
                {{ user.associated_user.first_name + ' ' + user.associated_user.last_name}}
              </div>
              <div class="project-user-table__body__row__item">
               {{ user.associated_user.email }}
              </div>
              <div v-if="userEditObject.editIndex === index" class="project-user-table__body__row__item">
                <select v-model="userEditObject.userRole">
                  <option v-for="(role, key) in roleMap" :key="key" :value="key">{{ role }}</option>
                </select>
              </div>
              <div v-else class="project-user-table__body__row__item">
                {{ roleMap[user.role_id] }}
              </div>
            </div>
          </template>
        </div>
      </div> -->
      <div class="drawer" :class="drawer ? 'open' : 'close'">
        <project-form
          ref="projectForm"
          :isUpdate="true"
          @cancel="closeDrawer"
          @update="updateProject"
          :drawer="drawer ? true : false"
          :closeOnOutsideClick="true"
          :buttonDisabled="buttonDisabled"
        ></project-form>
      </div>
      <div
        v-if="currentProject.id"
        class="drawer"
        :class="addUserDrawer ? 'open' : 'close'"
      >
        <add-user-to-project
          :projectId="currentProject.id"
          @close="addUserDrawer = false"
        ></add-user-to-project>
      </div>
    </div>
  </template>

<script>
import {
  GetCurrentProjectData,
  UpdateProjectById,
  DeleteUserFromProjectData,
  UpdateUserRoleForProjectData
} from '@/api'
import { mapGetters } from 'vuex'
import { timeStampToDateTime } from '@/filters/dateFilter'
import Avatar from '../../components/common/avatar.vue'
import ProjectForm from '../../components/manage/projectForm.vue'
import AddUserToProject from '../../components/manage/addUserToProject.vue'
import Config from '@/config.js'
import ConfirmationDialog from '@/plugins/confirmationDialog'
import { alert } from '@/plugins/notification'
import { arraySearchForUser } from '@/utils/array'

export default {
  name: 'Project',
  filters: {
    timeStampToDateTime
  },
  components: { ProjectForm, AddUserToProject, Avatar },
  data () {
    return {
      drawer: false,
      addUserDrawer: false,
      roleMap: Config.userRole,
      searchKeyword: '',
      userEditObject: {
        editIndex: -1,
        userId: '',
        userRole: ''
      },
      buttonDisabled: false // this for disabling update button  of project updation
    }
  },
  computed: {
    ...mapGetters(['currentProject', 'getUserById']),
    ...mapGetters(['user', 'getUserById']),
    getSearchResult () {
      return arraySearchForUser(this.currentProject.project_user_associations, this.searchKeyword, { fieldsToSearch: ['first_name', 'last_name'] })
    },
    getLocationObject () {
      const location = this.currentProject.location
        ? this.currentProject.location
          .replace('(', '')
          .replace(')', '')
          .split(',')
        : []
      return {
        latitude: location[0] || '',
        longitude: location[1] || ''
      }
    },
    isTenantAdminOrCollaborator () {
      return (
        this.user.tenantLevelRole === 'ADMIN' || this.user.projectLevelRole === 'ADMIN')
    },
    isTenantAdmin () {
      return (
        this.user.tenantLevelRole === 'ADMIN' || this.user.projectLevelRole === 'ADMIN')
    }
  },
  methods: {
    openMaps (location) {
      const [lat, long] = location.replace(/[()]/g, '').split(',')
      if (long && lat) {
        window.open(`https://www.google.com/maps?q=${lat},${long}`, '_blank')
      }
    },
    editUserRole (user, index) {
      this.userEditObject = {
        editIndex: index,
        userId: user.user_id,
        userRole: user.role_id
      }
    },
    updateUserRole () {
      const { userId, userRole } = this.userEditObject
      UpdateUserRoleForProjectData({
        user_id: userId,
        role_id: userRole
      })
        .then((res) => {
          this.getCurrentProject()
          this.$notify.success('User role updated successfully')
          this.userEditObject = {
            editIndex: -1,
            userId: '',
            userRole: ''
          }
        })
        .catch(() => {
          this.$notify.alert('Something went wrong')
        })
    },
    deleteUser (user) {
      ConfirmationDialog(
        'Are you sure you want to remove this user?',
        (res) => {
          if (res) {
            DeleteUserFromProjectData({
              user_id: user.user_id
            })
              .then((res) => {
                this.getCurrentProject()
                this.$notify.success('User removed successfully')
              })
              .catch(() => {
                this.$notify.alert('Something went wrong')
              })
          }
        }
      )
    },
    openAddUserDrawer () {
      this.addUserDrawer = true
    },
    getUserName (id) {
      const user = this.getUserById(id)
      return user
        ? `${user.associated_user?.first_name || ''} ${
              user.associated_user?.last_name || ''
            }`
        : '--'
    },
    openDrawer () {
      this.drawer = true
    },
    closeDrawer () {
      this.drawer = false
    },
    editProject () {
      this.$refs.projectForm.name = this.currentProject.name
      this.$refs.projectForm.latitude = this.getLocationObject.latitude
      this.$refs.projectForm.longitude = this.getLocationObject.longitude
      this.$refs.projectForm.selectedTenant = this.currentProject?.company
      this.$refs.projectForm.startDate = this.currentProject?.planned_start_date
      this.$refs.projectForm.endDate = this.currentProject?.planned_end_date
      this.$refs.projectForm.cost = this.currentProject?.project_cost
      this.$refs.projectForm.revenue = this.currentProject?.project_revenue
      this.$refs.projectForm.address = this.currentProject?.address?.address
      this.$refs.projectForm.state = this.currentProject?.address?.state
      this.$refs.projectForm.city = this.currentProject?.address?.city
      this.$refs.projectForm.pincode = this.currentProject?.address?.pincode
      this.openDrawer()
    },
    updateProject (obj) {
      this.buttonDisabled = true
      const body = {
        name: obj.name,
        planned_end_date: obj?.planned_end_date,
        planned_start_date: obj?.planned_start_date,
        project_cost: obj?.project_cost,
        project_revenue: obj.project_revenue,
        address: obj.address,
        location:
            obj.latitude && obj.longitude
              ? `(${obj.latitude}, ${obj.longitude})`
              : null
      }
      UpdateProjectById({ id: this.currentProject.id, data: body }).then((res) => {
        this.closeDrawer()
        this.getCurrentProject()
        this.$store.dispatch('fetchProjectList')
        this.$notify.success('Project Updated Successfully')
        this.buttonDisabled = false
      }).catch(() => {
        alert('Project updation failed!')
        this.buttonDisabled = false
      })
    },
    getCurrentProject () {
      GetCurrentProjectData().then((res) => {
        this.$store.commit('setCurrentProject', res?.core_projects?.[0])
      })
    }
  }
}
</script>

  <style lang="scss" scoped >
  .project {
    margin: -12px;
    height: 100%;
    &-table {
      border-bottom: var(--border);
      height: 54%;
      margin: 10px;
    }
    &-bar {
    border-bottom: var(--border);
    padding: 20px;
    }
    .details {
    flex-direction: column;
    padding-block: 10px;
    .location {
      text-decoration: underline;
      color: blue;
    }
    }
    .search-container {
    padding: 0 20px;
      .search-bar {
        margin-left: auto;
        width: 20%;
      }
    }
    .input-search {
    height: fit-content;
  }
    &-row {
    display: grid;
    grid-template-columns: 88px 1fr;
    grid-gap: 10px;
    margin: 10px 0;
    font-size: 16px;
    padding: 0 20px;
    }
    &-user-table {
      margin: 20px 0;
      &__header {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 80px;
        margin: 0 20px;
        font-size: 14px;
        font-weight: 500;
        border-bottom: var(--border);
        background-color: var(--brand-color);
        &__item {
          padding: 8px;
        }
      }
      &__body {
        &__row {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr 80px;
          margin: 0 20px;
          font-size: 13px;
          border-bottom: var(--border);
          &__item {
            padding: 8px;
            & > img {
              cursor: pointer;
              width: 20px;
              margin-right: 4px;
            }
          }
        }
      }
    }
  }
  </style>
