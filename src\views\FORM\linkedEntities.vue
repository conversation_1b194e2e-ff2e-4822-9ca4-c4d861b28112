<template>
  <div class="entities">
    <div v-for="(item, index) in entities" :key="index">
        <div class="entities-item">
            <div class="icon" :class="{ 'highlight-icon' : item.form_id === formId }">
            <img src="~@/assets/images/form-icon.png" alt="" />
            </div>
            <div class="content">
              <div class="header" :class="{ 'highlight-header' : item.form_id === formId }">
                <h6>{{ item.template_name }}</h6>
                <span class="date" v-if="item.updated_on">Updated on {{item.updated_on | timeStampToDate('monthDayYear')}}</span>
              </div>
              <div class="details">
                <p class="m mb-2 sequence-value elipsis-text" v-overflow-tooltip v-if="item.sequence_value">{{ item.sequence_value }}</p>
                <p class="updated-by" v-if="item.updated_by">Updated by {{ item.updated_by }}</p>
              </div>
            </div>
        </div>
      <div class="line" v-if="index!==entities.length-1"></div>
    </div>
  </div>
</template>

<script>
import { timeStampToDate } from '@/filters/dateFilter'
export default {
  name: 'LinkedEntities',
  props: {
    entities: {
      type: Array,
      default: () => []
    },
    formId: {
      type: String,
      default: ''
    }
  },
  filters: {
    timeStampToDate
  }
}
</script>

<style lang="scss" scoped>
.entities {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background-color: var(--bg-color);
  border-radius: 6px;
  height: calc(100vh - 270px);
  overflow-y: auto;

  .line {
      width: 2px;
      background-color: var(--brand-color);
      height: 30px;
      margin-left: 30px;
  }

  .entities-item {
    display: flex;
    align-items: flex-start;
    background: #f7f9fd;
    border-radius: 12px;
    padding: 1rem;
    position: relative;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);

    .highlight-icon {
      background-color: var(--brand-color) !important;
    }

    .highlight-header {
      color: var(--brand-color);
    }

    .icon {
      width: 40px;
      height: 40px;
      background-color: var(--brand-light-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      img {
        width: 20px;
        height: 20px;
      }
    }

    .content {
      flex: 1;
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        h6 {
          font-size: 0.9rem;
          margin: 0;
        }

        .date {
          font-size: 0.85rem;
          color: #a0a0a0;
        }
      }

      .details {
        margin-top: 5px;

         .sequence-value {
          max-width: 70%;
         }
        .updated-by {
          font-size: 0.8rem;
          color: #a0a0a0;
          margin-bottom: 5px;
        }

        .route {
          display: flex;
          align-items: center;
          gap: 5px;
          font-weight: 500;
          font-size: 0.9rem;

          .time {
            margin-left: auto;
            font-size: 0.85rem;
            color: #666;
          }
        }

        .pdf-link {
          display: inline-block;
          margin-top: 6px;
          font-size: 0.85rem;
          color: #3d5af1;
          text-decoration: underline;
        }
      }
    }
  }
}
</style>
