import store from '@/store'
import { runMutation, runQuery } from '../graphQl'
import * as tagQuery from '../query/tag'

export const SearchChildrenTags = (parentId, value, type) =>
  runQuery(tagQuery.getChildrenTags(), {
    parent_id: parentId,
    value,
    typeId: type
  }, 'tenant')

export const SearchParentTags = (value, type) =>
  runQuery(tagQuery.getParentTags(), {
    value,
    typeId: type
  }, 'tenant')

export const InsertTags = (tagName, parentId, type) =>
  runMutation(tagQuery.insertTags(), {
    tagName,
    parentId,
    typeId: type
  }, 'tenant')

export const CheckChildTags = (tagName, parentId, type) =>
  runQuery(tagQuery.checkChildTags(), {
    tagName,
    parentId,
    typeId: type
  }, 'tenant')

export const CheckRootTags = (tagName, type) =>
  runQuery(tagQuery.checkRootTags(), {
    tagName,
    typeId: type
  }, 'tenant')

// Materials

export const AddMaterialTags = (tagMaterials) => {
  return runMutation(tagQuery.addMaterialTags(), {
    tag_materials: tagMaterials
  }, 'tenant')
}

export const GetMaterialTags = (tags) =>
  runQuery(tagQuery.getMaterialTags(), {
    tags
  }, 'tenant')

export const DeleteMaterialTags = (materialId) => {
  return runMutation(tagQuery.deleteMaterialTags(), { materialId }, 'tenant')
}
export const createNewTag = (tagName, parentId, type) =>
  runMutation(tagQuery.CreateTag(), {
    tagName,
    parentId,
    type
  }, 'tenant')

export const checkTagName = (tagName, parentId, type) =>
  runQuery(tagQuery.checkTag(), {
    tagName,
    parentId,
    type
  }, 'tenant')

export const rootTagName = (tagName, type) =>
  runQuery(tagQuery.rootTag(), {
    tagName, type
  }, 'tenant')

export const displayrootTagName = (type) =>
  runQuery(tagQuery.GetRootLevelTagsQuery(), { type }, 'tenant')

export const ToUpdateTagName = (id, name, type) =>
  runMutation(tagQuery.updateTagName(), {
    id,
    name,
    type
  }, 'tenant')

// Task

export const AddTaskTags = (tagTask) =>
  runMutation(tagQuery.addTaskTags(), {
    tag_tasks: tagTask
  }, 'project')

export const GetTaskTags = (tags) =>
  runQuery(tagQuery.getTaskTag(), {
    tags
  }, 'project')

// this function delets all tags associated with given task
export const DeleteTaskTags = (id) =>
  runMutation(tagQuery.deleteTaskTag(), {
    id
  }, 'project')

// this is to delete  all tags assciated with given array of task ids
export const deleteTaskTagsAll = (TaskIds) =>
  runMutation(tagQuery.deleteTaskTagsArray(), { ids: TaskIds }, store.getters?.collaborator ? 'tenant' : 'project')

export const CreateTagTask = (taskTagName, parentId) =>
  runMutation(tagQuery.createTaskTag(), {
    taskTagName,
    parentId
  }, 'tenant')

export const displayingTaskParentTag = () =>
  runQuery(tagQuery.taskParentTag(), {
  }, 'tenant')

export const displayingTaskChildTag = (id) =>
  runQuery(tagQuery.taskChildTag(), {
    parent_id: id
  }, 'tenant')

export const TaskRootTag = (tagName) =>
  runQuery(tagQuery.taskRootTag(), {
    tagName
  }, 'tenant')

export const UpdateTaskTagName = (id, name) =>
  runMutation(tagQuery.updateTaskTagName(), {
    id,
    name
  }, 'tenant')

export const CheckTaskTag = (tagName, parentId) =>
  runQuery(tagQuery.checkTaskTag(), {
    tagName,
    parentId
  }, 'tenant')

export const GetAllTaskTags = () =>
  runQuery(tagQuery.GetAllTaskTagsQuery(), {}, 'tenant')

export const GetAllResourceTags = () => runQuery(tagQuery.GetAllResourceTagsQuery(), {}, 'tenant')

export const GetRootLevelTags = (type) => {
  return runQuery(tagQuery.GetRootLevelTagsQuery(), { type }, 'tenant')
}

export const GetChildLevelTags = (parentId, type) => {
  return runQuery(tagQuery.GetChildLevelTagsQuery(), { parentId, type }, 'tenant')
}
