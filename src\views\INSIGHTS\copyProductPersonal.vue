<template>
    <div class="personal-ins">
      <div class="personal-ins-acc mt-3">
        <div
          v-for="projectId in projectIds"
          :key="projectId"
          class="personal-ins-acc-item"
        >
          <div class="personal-ins-acc-header" @click="toggle(projectId)">
            {{ projectNameMap[projectId] }}
          </div>
          <transition
            name="accordion"
            @before-enter="beforeEnter"
            @enter="enter"
            @leave="leave"
          >
            <div
              class="personal-ins-acc-content px-3"
              v-show="isActive === projectId"
            >
              <h2 for="" class="personal-ins-label" v-if="taskData[projectId]">My Tasks</h2>
              <div class="copy-dtx-table" v-if="taskData[projectId]">
                <table>
                  <thead>
                    <tr>
                      <th>S.No</th>
                      <th>Task Name</th>
                      <th>Planned Start Date</th>
                      <th>Planned End Date</th>
                      <th>Progress</th>
                      <th>State</th>
                      <th>Due</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="(task, index) of taskData[projectId]"
                      :key="index"
                      @dblclick="openEditForm(task.id, task.project_id)"
                    >
                      <td>{{ index + 1 }}</td>
                      <td v-overflow-tooltip>{{ task?.name }}</td>
                      <td v-overflow-tooltip>{{ task?.planned_start_date?.split("T")[0] | genericFormatDate }}</td>
                      <td v-overflow-tooltip>{{ task?.planned_end_date?.split("T")[0] | genericFormatDate }}</td>
                      <!-- <td>{{ task?.progress }}%</td> -->
                      <td v-overflow-tooltip>
                        <div class="flex progress-container gap-1">
                          <progressbar :percentage="task?.progress" />
                          {{ task?.progress }}%
                        </div>
                      </td>
                      <td v-overflow-tooltip v-if="task?.due" :class="{ 'overdue': task.due }" >
                        <div class="status status-overdue">
                            Overdue
                        </div>
                      </td>
                      <td v-overflow-tooltip v-else>
                        --
                      </td>
                      <td v-overflow-tooltip v-if="task?.due" >{{ task?.due }} days</td>
                      <td v-else>--</td>
                      <td>
                        <div class="status" :class="getStatusClass(task?.task_status?.name)">
                            {{ task?.task_status?.name ?? "--" }}
                        </div>
                    </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <h2 for="" class="personal-ins-label" v-if="projectFormMap[projectId]">My Forms</h2>
              <div class="copy-dtx-table" v-for="(formData, templateId) in projectFormMap[projectId]" :key="templateId">
                <p class="template-name weight-400 xl">Template - {{formData[0].template_version.core_form_template.name}}</p>
                <table>
                  <thead>
                    <tr>
                      <th>S.No</th>
                      <template v-if="templateVisibleFieldsMap[templateId] && templateVisibleFieldsMap[templateId]?.length > 0">
                        <th v-for="(field, index) in templateVisibleFieldsMap[templateId]" :key="index">
                          {{ field.caption }}
                        </th>
                      </template>
                      <template v-else>
                        <th>--</th>
                        <th>--</th>
                      </template>
                      <th>Created By</th>
                      <th>Created At</th>
                      <th>Due</th>
                      <th>Status</th>
                      <th>Sequence Id </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                    v-for="(form, index) in formData" :key="form.id"
                    :class="{
                  'personal-ins-table-overdue':form.due,
                  'pointer': true
                }"
                @dblclick="openProjectForm(templateId, form.id, formData[0].template_version.core_form_template.name,projectId)"

                    >
                      <td>{{index + 1}}</td>
                <td v-if="form.customVisibleField1.tooltip?.length" > <span v-tooltip="form.customVisibleField1.tooltip">{{ form.customVisibleField1.value }}</span>  </td>
                <td v-else> {{ form.customVisibleField1.value }} </td>
                <td v-if="form.customVisibleField2.tooltip?.length"> <span v-tooltip="form.customVisibleField2.tooltip" > {{ form.customVisibleField2.value }} </span> </td>
                <td v-else> {{ form.customVisibleField2.value }} </td>
                <td> {{ form.created_by_user?.first_name + ' ' + form.created_by_user?.last_name}} </td>
                <td> {{ form.created_on.split('T')[0] | genericFormatDate }} </td>
                <td> {{ (form.due ?? '--') + ' ' + "days"}} </td>
                <td> {{ formStateMap[form.status] }}</td>
                <td> {{ form.sequence_value ?? '--' }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </transition>
        </div>
      </div>
      <!-- <i v-else>no materials are available...</i> -->
      <modal
        :open="modalOpen"
        @close="closeModal"
        :closeOnOutsideClick="true"
        title="Task Details"
      >
        <task-edit-form
        v-if="selectedTaskData && modalOpen"
          :task="selectedTaskData"
          :open="selectedTaskData && modalOpen"
          :assigneeMap="assigneeMap"
          @close="modalOpen=false"
          @update-and-close="updateAndCloseModal"
        />
      </modal>
    </div>
  </template>
<script>
import { mapGetters, mapMutations } from 'vuex'
import progressbar from '@/components/common/progressbar.vue'
import Loader from '@/plugins/loader'
import { alert, success } from '@/plugins/notification'
import Config from '@/config'
import TaskEditForm from '@/components/timeLine/taskEditForm.vue'
import Modal from '@/components/common/modal.vue'
import {
  getTaskDatabyuserId,
  getFormDatabyuserId,
  getProjectExchangeToken,
  getTaskDatabyTaskId,
  getCalenderDataByids,
  GetUserListByPojIds,
  UpdateTasksWithToken,
  deleteChanagesInTimeLine,
  insertTasksData,
  getTemplateIdsWithUserId
} from '@/api'
// another function is already wrote in insight helper , where it not considering the time
import { getDayDifference, genericFormatDate } from '@/utils/date'
import setVisibleFieldValues from '@/helper/insights/setVisibleFieldValues'

export default {
  name: 'Accordion',
  components: { Modal, TaskEditForm, progressbar },
  filters: {
    genericFormatDate
  },
  data () {
    return {
      projectIds: [],
      taskData: {},
      isActive: null,
      projectNameMap: { 0: 'Master' },
      projectFormMap: {},
      formStateMap: Config.FORM_STATE,
      modalOpen: false,
      selectedTaskData: {},
      assigneeMap: null,
      visibleFields: [],
      templateVisibleFieldsMap: {}
    }
  },
  computed: {
    ...mapGetters(['user', 'isOnProjectLevel', 'collaborator']),
    // this is a token hash map used to store all project tokens
    ...mapGetters('timeLine', ['getProjectTokens', 'getCalederHashMap'])
  },
  methods: {
    ...mapMutations('timeLine', [
      'addNewToken',
      'clearTokenHashMap',
      'addCalenderHashMap'
    ]),
    getStatusClass (status) {
      switch (status) {
      case 'TO DO':
        return 'status-todo'
      case 'IN PROGRESS':
        return 'status-inprogress'
      case 'DONE':
        return 'status-done'
      case 'QA':
        return 'status-qa'
      default:
        return ''
      }
    },
    toggle (index) {
      if (this.isActive === index) {
        this.isActive = null
      } else {
        this.isActive = index
      }
    },
    beforeEnter (el) {
      el.style.height = '0'
    },
    enter (el) {
      el.style.height = el.scrollHeight + 'px'
    },
    leave (el) {
      el.style.height = '0'
    },
    getTaskAndFormData () {
      this.taskData = {}
      this.projectFormMap = {}
      const loader = new Loader()
      loader.show()
      const currentDate = new Date()
      const afterDays = new Date(new Date().setDate(new Date().getDate() + 5))
      getTemplateIdsWithUserId(this.user.userId).then((res) => {
        this.visibleFields = res.template_fields.map(field => {
          if (this.templateVisibleFieldsMap[field.template_version.template_id]) {
            this.templateVisibleFieldsMap[field.template_version.template_id].push({ id: field.field_id, key: field.form_field.key, caption: field.caption })
          } else {
            this.templateVisibleFieldsMap[field.template_version.template_id] = [{ id: field.field_id, key: field.form_field.key, caption: field.caption }]
          }
          return field.field_id
        })
        Promise.all([getTaskDatabyuserId(this.user.userId, currentDate, afterDays, 'tenant'),
          getFormDatabyuserId(afterDays, this.user.userId, this.visibleFields)])
          .then(([res, formRes]) => {
            formRes.core_forms.map((form) => {
              setVisibleFieldValues(form, form.template_version.template_id, this.templateVisibleFieldsMap)
              if (currentDate > new Date(form.due_date)) {
                form.due = Math.round(getDayDifference(form.due_date, currentDate))
              }
              if (this.projectFormMap[form.project_id]) {
                if (this.projectFormMap[form.project_id][form.template_version.template_id]) {
                  this.projectFormMap[form.project_id][form.template_version.template_id].push(form)
                } else {
                  this.projectFormMap[form.project_id][form.template_version.template_id] = [form]
                }
              } else {
                if (form.core_project) {
                  this.projectFormMap[form.project_id] = {}
                  this.projectNameMap[form.project_id] = form.core_project?.name
                  this.projectFormMap[form.project_id][form.template_version.template_id] = [form]
                } else {
                  if (this.projectFormMap[0]) {
                    if (this.projectFormMap[0][form.template_version.template_id]) {
                      this.projectFormMap[0][form.template_version.template_id].push(form)
                    } else {
                      this.projectFormMap[0][form.template_version.template_id] = [form]
                    }
                  } else {
                    this.projectFormMap[0] = {}
                    if (this.projectFormMap[0][form.template_version.template_id]) {
                      this.projectFormMap[0][form.template_version.template_id].push(form)
                    } else {
                      this.projectFormMap[0][form.template_version.template_id] = [form]
                    }
                  }
                }
              }
            })
            for (const task of res.overdue) {
              if (!this.taskData[task.project_id]) {
                this.taskData[task.project_id] = []
                this.projectNameMap[task.project_id] = task?.core_project?.name
              }
              task.due = Math.round(
                getDayDifference(task.planned_end_date, new Date())
              )
              this.taskData[task.project_id].push(task)
            }
            for (const task of res.running) {
              if (!this.taskData[task.project_id]) {
                this.taskData[task.project_id] = []
                this.projectNameMap[task.project_id] = task?.core_project?.name
              }
              this.taskData[task.project_id].push(task)
            }
            for (const task of res.upcomming) {
              if (!this.taskData[task.project_id]) {
                this.taskData[task.project_id] = []
                this.projectNameMap[task.project_id] = task?.core_project?.name
              }
              this.taskData[task.project_id].push(task)
            }
            const projectIdsSet = new Set()
            for (const projectId in this.projectFormMap) {
              if (projectId) {
                projectIdsSet.add(projectId)
              } else {
                projectIdsSet.add(0)
              }
            }
            for (const projectId in this.taskData) {
              projectIdsSet.add(projectId)
            }
            this.projectIds = Array.from(projectIdsSet)
            this.isActive = this.isActive ?? Object.keys(this.taskData)[0] ?? null
            this.taskData = { ...this.taskData }
            loader.hide()

            if (!this.getCalederHashMap || !this.assigneeMap) {
              const filteredKeys = Object.keys(this.projectNameMap).filter(key => key !== '0')
              this.getCalenderData(filteredKeys)
            }
          })
          .catch((err) => {
            loader.hide()
            console.log(err)
            alert('unable to fetch the data')
          })
      }).catch(() => {
        alert('Unable to fetch')
      }).catch(() => {
        loader.hide()
        alert('something went wrong')
      })
    },
    async openEditForm (taskId, projectId) {
      const loader = new Loader()
      loader.show()
      if (!this.getProjectTokens[projectId]) {
        // creating project token for saving the data , and storing the value as
        const response = await getProjectExchangeToken(projectId)
        this.addNewToken({ token: response.message, projectId: projectId })
      }
      getTaskDatabyTaskId(taskId, this.getProjectTokens[projectId])
        .then((res) => {
          this.selectedTaskData = res?.core_tasks_by_pk
          this.selectedTaskData.attached_bom = {
            id: this.selectedTaskData.task_material_associations?.[0]?.id,
            name: this.selectedTaskData.task_material_associations?.[0]?.target_bom.name,
            associationId: this.selectedTaskData.task_material_associations?.[0]?.target_bom.id
          }
          loader.hide()
          this.modalOpen = true
        })
        .catch((err) => {
          console.log(err)
          alert('something went wrong')
        })
    },
    closeModal () {
      this.modalOpen = false
    },
    async updateAndCloseModal (updatedTask) {
      const loader = new Loader()
      try {
        loader.show()
        updatedTask.core_project = { id: this.sActive }
        const deletedAssignees = []
        const deletedDocs = []
        const newAssigneeArray = []
        const newDocArray = []
        const newTagArray = []
        const newTaskMaterialArray = []
        const {
          id: taskId,
          start_date: plannedStartDate,
          end_date: plannedEndDate,
          task_assignees: taskAssignees,
          core_project: { id: projectId },
          task_docs: taskDocs,
          attached_bom: attachedBom,
          tags,
          duration,
          progress,
          description,
          text,
          type
        } = updatedTask
        // checking  project token is avaialble in project token's Hash map
        if (!this.getProjectTokens[projectId]) {
          // creating project token for saving the data , and storing the value as
          const response = await getProjectExchangeToken(projectId)
          this.addNewToken({ token: response.message, projectId: projectId })
        }
        const deletedOperations = []
        const insertOperations = []
        // getting deleted and added assinee ids
        for (const user of taskAssignees) {
          if (user.status === 'new') {
            newAssigneeArray.push({ task_id: taskId, user_id: user.id })
          } else if (user.status === 'deleted') {
            deletedAssignees.push(user.id)
          }
        }
        if (taskDocs?.length) {
          for (const doc of taskDocs) {
            if (doc.flag === 'deleted') {
              deletedDocs.push(doc.id)
            } else if (doc.flag === 'new') {
              newDocArray.push({ task_id: taskId, document_id: doc.id })
            }
          }
        }
        if (tags?.flat()?.length > 0) {
          for (const tag of tags?.flat()) {
            newTagArray.push({ tag_id: tag.id ?? tag.tag.id, task_id: taskId })
          }
        }
        // attaching a bom to  a  task
        attachedBom && newTaskMaterialArray.push(`{task_id: {_eq: "${taskId}"}}, _set: {bom_id:${attachedBom.id}}`)
        // if (updateObject[projectId]) {
        const updateObject = ({
          where: { id: { _eq: taskId } },
          _set: {
            planned_start_date: plannedStartDate,
            planned_end_date: plannedEndDate,
            duration,
            progress,
            type,
            description,
            name: text
          }
        })
        if (deletedAssignees?.length > 0) { deletedOperations.push({ typeName: 'delete_task_assignee', conditions: `{task_id: {_eq: "${taskId}"}, user_id: {_in: [${deletedAssignees}]}}` }) }
        if (deletedDocs?.length > 0) {
          deletedOperations.push({
            typeName: 'delete_task_document_association',
            conditions: `{task_id: {_eq: "${taskId}"}, document_id: {_in:${JSON.stringify(deletedDocs)} }}`
          })
        }
        if (newTaskMaterialArray?.length > 0) { deletedOperations.push({ typeName: 'update_task_material_association', conditions: (newTaskMaterialArray) }) }
        if (newDocArray?.length > 0) { insertOperations.push({ typeName: 'insert_task_document_association', conditions: (newDocArray) }) }
        if (newTagArray.length > 0) { insertOperations.push({ typeName: 'insert_tag_task', conditions: (newTagArray) }) }
        if (newAssigneeArray.length > 0) { insertOperations.push({ typeName: 'insert_task_assignee', conditions: (newAssigneeArray) }) }
        deletedOperations.push({ typeName: 'delete_tag_task', conditions: `{task_id: {_eq: "${taskId}"}}` })
        await Promise.all([UpdateTasksWithToken(updateObject, this.getProjectTokens[this.isActive]), deleteChanagesInTimeLine(this.generateDynamicDeleteQuery(deletedOperations), this.getProjectTokens[this.isActive])])
        insertOperations.length > 0 && await Promise.all([insertTasksData(this.generateDynamicInsertQuery(insertOperations), this.getProjectTokens[this.isActive])])
        loader.hide()
        this.getTaskAndFormData()
        this.modalOpen = false
        success('task updated successfully')
      } catch (err) {
        console.log(err)
        loader.hide()
        alert('something went wrong')
      }
    },
    getCalenderData (projectids) {
      getCalenderDataByids(projectids).then((res) => {
        for (const project of res.core_project_calendar) {
          const days = []
          const holidays = []
          for (const workDays of project.calendar_working_days) {
            days.push(workDays.work_day)
          }
          for (const holiday of project.calendar_holidays) {
            holidays.push(new Date(holiday.date))
          }
          this.addCalenderHashMap({
            calenderData: {
              project_id: project.project_id,
              working_hours: project.working_hours,
              workDays: days,
              holidays: holidays
            },
            projectId: project.project_id
          })
        }
      })
    },
    getUsersList (projectIds) {
      this.assigneeMap = {}
      this.associatedUsers = [] // TO reset users list based on selected project
      const userIdForTemp = []
      GetUserListByPojIds(projectIds).then((res) => {
        this.associatedUsers = res.project_user_association.filter((user) => {
          // this is to store assignees of given projects
          // assiignee map will look like
          // {
          //   prjectid:[userid1,userId2]
          //   prjectid2:[userid1,userId2]
          // }
          if (Object.hasOwn(this.assigneeMap, user.associated_project?.id)) {
            this.assigneeMap[user.associated_project?.id].push(user.associated_user.id)
          } else {
            this.assigneeMap[user.associated_project?.id] = [user.associated_user.id]
          }
          // to remove the duplication need to check whether the user Data is already added or not
          // since there is no project id while fetching project_associated users initilly need to this one
          if (!userIdForTemp.includes(user.associated_user.id)) {
            userIdForTemp.push(user.associated_user.id)
            return true
          }
        }).map((filteredUser) => {
          return {
            label:
              filteredUser.associated_user.first_name +
                ' ' +
                filteredUser.associated_user.last_name,
            value: filteredUser.associated_user.id
          }
        })
      }).catch((err) => {
        console.log(err)
      })
    },
    generateDynamicDeleteQuery (deleteOperations) {
      const mutations = deleteOperations.map(({ typeName, conditions }, index) => `
      ${typeName}_${index + 1}: ${typeName}(where: ${conditions}) {
        affected_rows
      }
    `)
      return `
      mutation deleteChangesTimeLineQuery {
        ${mutations.join('\n')}
      }
    `
    },
    generateDynamicInsertQuery (insertOperation) {
      const mutations = insertOperation.map(({ typeName, conditions }, index) => `
      ${typeName}_${index + 1}: ${typeName}(objects: [${conditions.map(obj => `{${Object.entries(obj).map(([key, value]) => `${key}: ${JSON.stringify(value)}`).join(',')}}`).join(',') }]) {
        affected_rows
      }
    `)

      return `
      mutation insertChangesTimeLineQuery {
        ${mutations.join('\n')}
      }
    `
    },
    openProjectForm (templateId, formId, formName, projectId) {
      this.$router.push(`/form/editform/${templateId}/${formName}/${formId}/${projectId}`)
    }
  },
  mounted () {
    this.getTaskAndFormData()
  }
}
</script>
  <style scoped lang="scss">
  .template-name {
    padding: 2px 0 10px 0;
    margin-bottom: 5px;
    // text-decoration: underline;
  }
  .overdue {
    color: red;
  }
  .status {
    font-size: 13px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
    height: fit-content;
    padding: 3px 4px;
    border-radius: 3px;
    background-color: orange;
    &-todo {
      background-color: #e0e0e0; /* Light grey */
      color: #555555; /* Dark grey */
    }
    &-inprogress {
        background-color: #fff3cd; /* Light yellow */
        color: #856404; /* Dark yellow */
    }
    &-qa {
        background-color: #cce5ff; /* Light blue */
        color: #004085; /* Dark blue */
    }
    &-done {
        background-color: #d4edda; /* Light green */
        color: #155724; /* Dark green */
    }
    &-overdue {
      background-color: #ffcccc; /* Light red */
      color: #d60000; /* Dark red */
    }
  }
  .personal-ins {
    &-label {
      color: black;
      margin-block: 1rem;
    }
    &-table {
      width: 100%;
      table {
        width: 100%;
        position: relative;
        border-collapse: collapse;

        th {
          position: sticky;
          width: auto;
          top: 0px;
          font-weight: 500;
          background-color: var(--brand-color);
        }
        tr {
          z-index: 0;
          cursor: pointer;
        }
        th,
        td {
          text-align: left;
          font-size: 12px;
          padding: 8px 4px;
        }
        tr:nth-child(odd) {
          background-color: rgba(var(--brand-rgb), 0.05);
          border: 1px solid var(--brand-color);
        }
      }
    }
    &-acc {
      border: 1px solid #ddd;
      border-radius: 5px;
      &-item {
        border-top: 1px solid #ddd;
      }
      &-header {
        padding: 15px;
        background: #f7f7f7;
        cursor: pointer;
        &:hover {
          background: #e7e7e7;
        }
      }
      &-content {
        overflow: auto;
        transition: height 0.6s ease;
      }
    }
  }

  /* Transition classes */
  .accordion-enter-active,
  .accordion-leave-active {
    transition: height 0.6s ease;
  }

  .accordion-enter,
  .accordion-leave-to {
    height: 0;
  }
  </style>
