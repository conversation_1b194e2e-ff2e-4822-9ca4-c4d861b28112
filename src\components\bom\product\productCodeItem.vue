<template>
  <div>
    <div
      class="product-code-item v-center"
      :class="{
        selected: product.selected,
        obsolete: !product.active
      }"
    >
      <div
        class="product-code-item__name v-center s elipsis-text"
        @click="goto"
        v-overflow-tooltip
      >
        {{ product.product_code_name }}
        {{product.associatedBomId ? `(${product.associatedBomName})` : ''}}
      </div>
      <div
        class="toggle-button center"
        @click="expand"
        :class="{
          active: product.canExpand,
          rotate: product.open,
        }"
      >
        <img src="~@/assets/images/down-arrow-icon.svg" alt="" />
      </div>
    </div>
    <div v-if="product.open" class="pl-2">
      <div v-if="product.loading" class="p-3 center s">
        <loading-circle />
      </div>
      <div
        v-if="!product.loading && productObject.childrens.length === 0"
        class="product-code-item center"
      >
        <div class="product-code-item__name v-center s elipsis-text">
          No child product code
        </div>
      </div>
      <template v-else>
        <product-code-item
          v-for="childProductCode in productObject.childrens"
          :key="childProductCode.cid"
          :product-object="childProductCode"
        />
      </template>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import LoadingCircle from '../../common/loadingCircle.vue'
import ProductCodeItem from './productCodeItem.vue'
export default {
  name: 'ProductCodeItem',
  components: {
    ProductCodeItem,
    LoadingCircle
  },
  props: {
    productObject: {
      type: Object,
      required: true
    }
  },
  computed: {
    ...mapGetters('productBom', ['productCodeTree']),
    product () {
      return this.productCodeTree.items[this.productObject.cid]
    }
  },
  methods: {
    goto () {
      this.$store.dispatch('productBom/gotoProduct', this.productObject.cid)
    },
    expand () {
      this.$store.dispatch('productBom/expandProduct', this.productObject.cid)
    }
  }
}
</script>

<style lang="scss" scoped >
.product-code-item {
  border: 1px solid var(--brand-color);
  background-color: rgba(var(--brand-rgb), 0.2);
  margin: 6px 6px;
  padding-left: 6px;
  cursor: pointer;
  &.selected {
    background-color: rgba(var(--brand-rgb), 0.6);
  }
  &.obsolete{
    border-color: var(--alert);
    color: var(--alert)
  }
  &__name {
    width: calc(100% - 30px);
    height: 30px;
  }
  & .toggle-button {
    height: 30px;
    width: 30px;
    background-color: rgba(var(--brand-rgb), 1);
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.5;
    &.active {
      opacity: 1;
      cursor: pointer;
      pointer-events: all;
    }
    &.rotate {
      img {
        transform: rotate(0deg);
      }
    }
    & img {
      height: 16px;
      width: 16px;
      transform: rotate(-90deg);
    }
  }
}
</style>
