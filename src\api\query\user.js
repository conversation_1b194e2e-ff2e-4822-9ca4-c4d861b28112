import { GQL } from '../graphQl'
export const GetUserDetailByUserId = (userId) => GQL`query GetUserDetailByUserId {
  core_users(where: {id: {_eq: "${userId}"}}) {
    email
    first_name
    id
    last_name
    phone
    status
  }
}`

export const GetTenantDetailsQuery = () => GQL`query GetTenantDetailsQuery($tenantId: uuid) {
  core_tenants(where: {id: {_eq: $tenantId}}) {
    tenant_users(where: {owner: {_eq: true}}) {
      associated_user {
        first_name
        last_name
        email
      }
    }
    tenant_type
    company_name
    company_email
    company_phone
    GSTIN
    PAN
    company_description
    company_location
    industry_vertical
    industry_vertical_value {
      id
      name
    }
    company_url
    company_logo_blob_key
  }
}`
export const GetTenantDetailsForDtxAdminQuery = () => GQL`query GetTenantDetailsForDtxAdminQuery($tenantId: uuid, $parentTenantId: uuid) {
  core_tenants(where: {id: {_eq: $tenantId}}) {
    tenant_users(where: {owner: {_eq: true}}) {
      associated_user {
        first_name
        last_name
        email
      }
    }
    tenant_type
    company_name
    company_email
    GSTIN
    PAN
    company_url
    company_logo_blob_key
    associated_tenants_by_target_tenant_id(where: {source_tenant_id: {_eq: $parentTenantId}}) {
      company_description
      company_location
      company_phone
      source_tenant_id
      company_email
      status
      target_tenant_id
      industry_vertical
      industry_vertical_value {
      id
      name
      }
    }
  }
}
`
export const updateTenantDetailQuery = () => GQL`mutation UpdateTenantDetails($id: uuid, $body: core_tenants_set_input) {
  update_core_tenants(where: {id: {_eq: $id}}, _set: $body) {
    affected_rows
  }
}`

export const updateTenantCompanyAssciationQuery = () => GQL`mutation updateTenantCompanyAssciation($id: uuid, $payload: tenant_company_association_set_input) {
  update_tenant_company_association(where: {target_tenant_id: {_eq: $id}}, _set: $payload) {
    affected_rows
  }
}`
// export const updateTenantDetailQuery = () => GQL`query updateTenantDetailQuery(body)`

export const GetAllTenantsList = () => GQL`query GetAllTenantsList($userId: uuid!, $targetTenantId: uuid!) {
  core_tenants(where: {tenant_users: {status: {_in: [1, 4]}, user_id: {_eq: $userId}}, status: {_in: [1, 4]}}) {
    id
    company_name
    tenant_users(where: {user_id: {_eq: $userId}}) {
      user_status {
        status_caption
      }
    }
    company_phone
    company_email
    tenant_type
    status
    company_logo_blob_key
    company_url
    GSTIN
    industry_vertical
    industry_vertical_value {
      id
      name
    }
  }
  parent_tenants:tenant_company_association(
    where: {target_tenant_id: {_eq: $targetTenantId}, deleted: {_eq: false}, status:{_in: [1, 4]}}
  ) {
    source_tenant {
      id
      company_name
      status
      tenant_type
      company_logo_blob_key
      company_url
      GSTIN
      industry_vertical
      industry_vertical_value {
        id
        name
      }
    }
  }
  child_tenants:tenant_company_association(
    where: {source_tenant_id: {_eq: $targetTenantId}, deleted: {_eq: false}}
  ) {
    status
    target_tenant {
      id
      company_name
      company_email
      company_phone
      status
      company_logo_blob_key
      company_url
      GSTIN
      industry_vertical
      industry_vertical_value {
        id
        name
      }
    }
  }
}`

export const GetAllTenantsListForBeaconAdmin = () => GQL`query GetAllTenantsListForBeaconAdmin{
  core_tenants(order_by:{created_at: asc}){
    id
    company_name
    company_phone
    company_email
    status
    company_logo_blob_key
    company_url
    industry_vertical
    industry_vertical_value {
      id
      name
    }
    tenant_type_details {
      id
      type
    }
  }
}`

export const UpdateTenantStatus = () => GQL`mutation UpdateTenantStatusById($tenantId:uuid!,$status:Int!){
  update_core_tenants_by_pk(pk_columns: {id: $tenantId}, _set: {status: $status}){
    id
  }
}`

export const GetAllUsers = () => GQL`query GetAllUsers($tenantId: uuid ) {
  tenant_user_association(where: {tenant: {id: {_eq: $tenantId}}}) {
    status
    associated_user {
      id
      first_name
      last_name
      phone
      status
      email
    }
    reporting_manager_user{
      id
      first_name
      last_name
      email
    }
    associated_role {
      id
      name
    }
  }
}`

export const updateTenanUser = () => GQL`mutation($role_id:Int,$status:Int,$user_id:uuid!, $reportingManager: uuid){
  update_tenant_user(role:$role_id,status:$status,user_id:$user_id, reporting_manager: $reportingManager){
    message
  }
}`

export const GetUserDetailsByIds = () => GQL`query GetUserDetailByUserId ($ids: [uuid!]!){
  core_users(where: {id: {_in: $ids}}) {
    first_name
    id
    last_name
  }
`

export const GetAllCollabaratorTenantList = () => GQL`query GetAllCollabaratorTenantList($sourceTenantId: uuid!){
  tenant_company_association(
    where: {source_tenant_id: {_eq: $sourceTenantId}, deleted: {_eq: false}, status:{_in: [1,4] }, target_tenant: {status : {_eq: 1}}}
  ) {
    status
    target_tenant {
      id
      status
      company_name
      company_email
      company_phone
      tenant_users(where: {deleted: {_eq: false}, status: {_eq: 1}}) {
        associated_user {
          first_name
          last_name
          id
        }
      }
    }
  }
}
`

// it returnsa all projects data where current user has admin role
export const getProjectUserAssociationQuery = () => GQL`query getProjectUserAssociationQuery($userId: uuid ) {
  project_user_association(where: {user_id: {_eq: $userId}, role_id: {_eq: 1}}) {
    associated_project {
      active
      deleted
      name
      id
    }
    associated_role {
      id
      name
    }
  }
}`

export const getProjectUserExceptViewerQuery = () => GQL`query getProjectUserExceptViewerQuery($userId: uuid) {
  project_user_association(where: {user_id: {_eq: $userId}, role_id: {_neq: 4}}) {
    associated_project {
      active
      deleted
      name
      id
    }
    associated_role {
      id
      name
    }
  }
}`

export const updateCollaboratingTenantStatusMutation = () => GQL`mutation deactivateCollaboratingTenantMutation ($tenantId: uuid!, $status: Int ) {
  update_tenant_company_association (where : {target_tenant_id : {_eq: $tenantId}}, _set: {status : $status}) {
     affected_rows
  }
}`

export const getProjectUserForCollaboratorQuery = () => GQL`query getProjectUserForCollaboratorQuery($userId: uuid!, $collaboratorId: uuid!) {
  core_projects(
    where: {core_tasks: {task_assignees: {user_id: {_eq: $userId}}, tenant_id: {_eq: $collaboratorId}}}
  ) {
    active
    deleted
    name
    id
  }
}
`
export const getIndustryVerticalQuery = () => GQL`query getIndustryVerticalQuery($name: String!) {
  core_custom_list(where: {name: {_eq: $name}}) {
    name
    id
    custom_list_values(where: {deleted: {_eq: false}}, order_by: {name: asc, editable: asc}) {
      editable
      deleted
      id
      name
    }
  }
}
`

export const GetUserGroupsQuery = () => GQL` query GetUserGroupsQuery ($offset: Int!, $limit: Int!, $conditions: core_user_group_bool_exp!) {
core_user_group (where: $conditions, offset: $offset,
    limit: $limit, order_by: {created_at: desc} ) {
    id
    name
    description
    created_by_user {
      id
      first_name
      last_name
    }
    core_user_group_members (where: {deleted: {_eq: false}}) {
      user_id
      core_user{
        first_name
        last_name
      }
    }
  }
  core_user_group_aggregate (where: $conditions) {
    aggregate {
      count
    }
  }
 }
`
export const GetUserGroupsWithoutLimitQuery = () => GQL` query GetUserGroupsWithoutLimitQuery {
core_user_group (where: {deleted: {_eq: false}}, order_by: {created_at: desc} ) {
    id
    name
    description
    created_by_user {
      id
      first_name
      last_name
    }
    core_user_group_members (where: {deleted: {_eq: false}}) {
      user_id
      core_user{
        first_name
        last_name
      }
    }
  }
  core_user_group_aggregate (where: {deleted: {_eq: false}}) {
    aggregate {
      count
    }
  }
 }
`

export const GetUserGroupDetailQuery = () => GQL` query GetUserGroupDetailQuery ($id: uuid!) {
core_user_group_by_pk (id : $id) {
    id
    name
    description
    core_user_group_members  (where: {deleted: {_eq: false}}) {
      user_id
      core_user{
        first_name
        email
        last_name
      }
    }
  }
 }
`

export const CreateNewUserGroupMutation = () => GQL`mutation CreateNewUserGroupMutation($data: [core_user_group_insert_input!]!) { 
  insert_core_user_group(objects: $data) {
  returning {
    id
    name
    description
  }
  affected_rows
}
}`

export const UpdateUserGroupMutation = () => GQL`mutation UpdateUserGroup ($id: uuid!, $name: String!, $description: String!) {
  update_core_user_group_by_pk(pk_columns: {id: $id}, _set: {name: $name, description: $description}){
    id
  }
}`

export const DeleteUserGroupMutation = () => GQL`mutation DeleteUserGroupMutation($id: uuid!) {
   update_core_user_group_by_pk(pk_columns:{id:$id},_set:{deleted: true}) {
    id
  }
}`

export const AddUsersToUserGroupMutation = () => GQL`mutation AddUsersToUserGroupMutation ($objects: [core_user_group_members_insert_input!]!) {
  insert_core_user_group_members(objects: $objects, on_conflict:{constraint: core_user_group_members_pkey, update_columns:deleted, where:{deleted:{_eq:true}}}) {
    returning {
      group_id
    }
  }
}`

export const RemoveUsersFromUserGroupMutation = () => GQL`mutation RemoveUsersFromUserGroupMutation ($userIds: [uuid!]!, $groupId: uuid!) {
  update_core_user_group_members (where: {user_id: {_in: $userIds}, group_id: {_eq: $groupId}}, _set: {deleted: true}) {
    affected_rows
  }
}`

export const findExistingUserGroupQuery = () => GQL`query findExistingUserGroup ($name: String) {
core_user_group (where: {name: {_eq: $name}}) {
    id
    deleted
  }
}`

export const restoreUserGroupMutation = () => GQL`mutation restoreUserGroupMutation ($name : String!) {
  update_core_user_group (where: {name: {_eq: $name}}, _set: {deleted: false}) {
    returning {
      id
    }
  }
}`

export const GetTenantDetailsByGSTIN = () => GQL`query GetTenantDetailsByGSTIN ($GSTIN: String) {
  core_tenants(where: {GSTIN: {_eq: $GSTIN}}) {
    tenant_users(where: {owner: {_eq: true}}) {
      associated_user {
        first_name
        last_name
        email
      }
    }
    id
    company_name  
    company_phone
    company_email
    tenant_type
    status
    company_logo_blob_key
    company_url
    GSTIN
    PAN
    company_description
    company_location
    industry_vertical
    industry_vertical_value {
      id
      name
    }
  }
}`
