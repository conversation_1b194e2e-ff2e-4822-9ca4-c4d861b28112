<template>
  <div class="project-form" id="project-form">
    <h3>Project Form</h3>
    <div data-validation="projectName" class="input-group imp my-4">
      <label>Enter your Project Name</label>
      <input v-model="name" placeholder="Enter your Project Name" type="text" />
    </div>
    <div data-validation="selectTenant" class="input-group imp">
</div>
    <div class="address">
      <div class="group-1 flex">
      <div data-validation="address" class="input-group imp mr-3">
      <label>Address</label>
      <input v-model="address" placeholder="Address" type="text" />
    </div>
      <div data-validation="state" class="input-group imp">
      <label>State</label>
      <input v-model="state" placeholder="State" type="text" />
    </div>
  </div>
      <div class="group-2 flex">
      <div data-validation="city" class="input-group imp mr-3">
      <label>City</label>
      <input v-model="city" placeholder="City" type="text" />
    </div>
      <div data-validation="pincode" class="input-group imp">
      <label>Pincode</label>
      <input v-model="pincode" placeholder="Pincode" type="text" />
    </div>
  </div>
    </div>
  <div class="date-group flex">
    <div data-validation="startDate" class="input-group mr-5 imp">
      <label>Start Date</label>
      <input v-model="startDate" :max="endDate" placeholder="Start Date" type="date" />
    </div>
    <div data-validation="endDate" class="input-group end-date ml-5 imp">
      <label>End Date</label>
      <input v-model="endDate" :min="startDate" placeholder="End Date" type="date" />
    </div>
  </div>
  <div class="revenue-group flex">
  <div data-validation="revenue" class="input-group imp mr-3">
    <label>Project Revenue</label>
    <input v-model.number="revenue" placeholder="Project Revenue" type="number" step="0.01" min="0"  @keydown="changeNumber"/>
  </div>
  <div data-validation="cost" class="input-group imp">
    <label>Project Cost</label>
    <input v-model.number="cost" placeholder="Project Cost" type="number" step="0.01" min="0"  @keydown="changeNumber"/>
  </div>
</div>
    <div data-validation="longitude" class="input-group my-4">
      <label>Project Location longitude</label>
      <input
        v-model.number="longitude"
        placeholder="Project Location longitude"
        min="-180"
        max="180"
        @keydown="changeNumber"
        type="number"
      />
    </div>
    <div data-validation="latitude" class="input-group my-4">
      <label>Project Location latitude</label>
      <input
        v-model.number="latitude"
        min="-90"
        max="90"
        placeholder="Project Location latitude"
        @keydown="changeNumber"
        type="number"
      />
    </div>
    <div v-if="!isUpdate" class="input-group my-4" >
      <label>Select Project Plan To Copy From:</label>
        <custom-dropdown :list="tenantProjectListFiltered" :selected="selectedProjectPlan" @select="handleCopyProjectPlanChange" @toggle="clearSearchKeyword" @deselect="()=>handleCopyProjectPlanChange()" class="form-template-container-wft">
        <template #before-items>

        <div class="fh column center p-3 form-template-container-wft-search-box"  >
        <input type="text p-2" class="form-template-container-wft-input"  v-model="projectSearchKeyword" placeholder="Search"/>
        <div class="fh center py-4" v-show="tenantProjectListFiltered.length === 0"  >
         No projects found
        </div>
        </div>
        </template>
  </custom-dropdown>
    </div>
    <div v-if="showCopyProjectPlanOptions">
      <input type="checkbox" v-model="assignees" id="assignees">
  <label for="assignees"> Copy Assignees</label><br>
      <input type="checkbox" v-model="documents" id="documents">
  <label for="documents"> Copy Documents</label><br>
  <input type="checkbox" id="docTaskAssoc" v-if="documents" v-model="docTaskAssoc">
<label for="docTaskAssoc" v-if="documents"> Copy Document Associations</label><br v-if="documents">
  <input type="checkbox" id="boms" v-model="boms">
  <label for="boms"> Copy BOMs</label><br>
      <input type="checkbox" id="bomTaskAssoc" v-model="bomTaskAssoc" v-if="boms">
  <label for="bomTaskAssoc" v-if="boms"> Copy Bom Associations</label><br>
    </div>
    <div class="flex flex-end s">
      <button type="button" class="btn btn-black-outline mx-2" @click="cancel">
        Cancel
      </button>
      <button v-if="buttonDisabled" type="button" class="btn"  > <spinner></spinner></button>
      <button v-else-if="!isUpdate" type="button" class="btn" :disabled="buttonDisabled" @click="save">Save</button>
      <button v-else type="button" class="btn" :disabled="buttonDisabled" @click="update">Update</button>
    </div>
  </div>
</template>

<script>
import { emptyString, positiveFloat, isValidLatitudeTenant, isValidLongitudeTenant } from '@/helper/formValidation'
import { mapGetters } from 'vuex'
import { alert } from '../../plugins/notification'
import spinner from '@/components/common/spinner.vue'
import { restrictKeys } from '@/utils/validations'
import { removeErrorBorder } from '@/helper/formValidation/errorBorder'
import customDropdown from '@/components/common/customDropdown.vue'
export default {
  name: 'Project-form',
  components: { spinner, customDropdown },
  props: {
    isUpdate: {
      type: Boolean,
      default: false
    },
    drawer: {
      type: Boolean,
      default: false
    },
    buttonDisabled: {
      type: Boolean,
      default: false
    },
    closeOnOutsideClick: {
      type: Boolean
    }
  },
  data () {
    return {
      name: '',
      address: '',
      pincode: '',
      state: '',
      city: '',
      startDate: '',
      endDate: '',
      revenue: '',
      cost: '',
      latitude: '',
      longitude: '',
      selectedProjectPlan: {},
      showCopyProjectPlanOptions: false,
      assignees: false,
      boms: false,
      bomTaskAssoc: false,
      docTaskAssoc: false,
      documents: false,
      window: window,
      projectSearchKeyword: ''
    }
  },
  computed: {
    ...mapGetters(['tenantList', 'openTenantId', 'tenantProjectList']),
    tenantProjectListFiltered () {
      if (this.projectSearchKeyword) {
        return this.tenantProjectList.filter(project => project.name.toLowerCase().includes(this.projectSearchKeyword.toLowerCase()))
      }
      return this.tenantProjectList
    }
  },
  methods: {
    changeNumber (event) {
      if (['e', 'E', '+', '-'].includes(event.key)) {
        restrictKeys(event)
      }
    },
    resetForm () {
      this.boms = false
      this.bomTaskAssoc = false
      this.assignees = false
      this.showCopyProjectPlanOptions = false
      this.name = ''
      this.address = ''
      this.pincode = ''
      this.state = ''
      this.city = ''
      this.startDate = ''
      this.endDate = ''
      this.revenue = ''
      this.cost = ''
      this.latitude = ''
      this.longitude = ''
      this.selectedProjectPlan = {}
      this.assignees = false
      this.boms = false
      this.bomTaskAssoc = false
      this.docTaskAssoc = false
      this.documents = false
    },
    handleCopyProjectPlanChange (project = null) {
      if (project) {
        this.selectedProjectPlan = { id: project.id, name: project.name }
        this.showCopyProjectPlanOptions = true
      } else {
        this.showCopyProjectPlanOptions = false
        this.selectedProjectPlan = {}
      }
    },
    cancel () {
      this.resetForm()
      const validFields = Array.from(document.querySelectorAll('[data-validation]')).map(el => el.getAttribute('data-validation'))
      removeErrorBorder(validFields)
      this.$emit('cancel')
    },
    save () {
      let count = 0
      if (!emptyString(this.name, 'Project Name', 'projectName', false)) {
        count++
      }
      if (!emptyString(this.address, 'Address', 'address', false)) {
        count++
      }
      if (!emptyString(this.state, 'State', 'state', false)) {
        count++
      }
      if (!emptyString(this.city, 'City', 'city', false)) {
        count++
      }
      if (!emptyString(this.pincode, 'Pincode', 'pincode', false)) {
        count++
      }
      if (!emptyString(this.startDate, 'Start Date', 'startDate', false)) {
        count++
      }
      if (!emptyString(this.endDate, 'End Date', 'endDate', false)) {
        count++
      }
      if (this.startDate > this.endDate) {
        alert('End date cannot be set before Start Date')
        count++
      }
      if (!emptyString(this.revenue, 'Project Revenue', 'revenue', false)) {
        count++
      }
      if (!positiveFloat(this.revenue, 'Project Revenue', 'revenue', false)) {
        count++
      }
      if (!emptyString(this.cost, 'Project Cost', 'cost', false)) {
        count++
      }
      if (!positiveFloat(this.cost, 'Project Cost', 'cost', false)) {
        count++
      }
      if (this.longitude && !isValidLongitudeTenant(this.longitude, 'longitude', 'longitude')) {
        count++
      }
      if (this.latitude && !isValidLatitudeTenant(this.latitude, 'latitude', 'latitude')) {
        count++
      }
      if ((this.latitude && !this.longitude) || (!this.latitude && this.longitude)) {
        if (this.latitude && !this.longitude) {
          emptyString(this.latitude, 'latitude', 'latitude', false)
          alert('Please provide Longitude')
        }
        if (!this.latitude && this.longitude) {
          emptyString(this.latitude, 'latitude', 'latitude', false)
          alert('Please provide Latitude')
        }
        count++
      }
      if (count > 0) {
        alert('Please review the highlighted fields')
        return
      }
      this.buttonDisabled = true
      this.$emit('save', {
        name: this.name,
        company: this.selectedTenant,
        planned_start_date: this.startDate,
        planned_end_date: this.endDate,
        project_cost: this.cost,
        project_revenue: this.revenue,
        address: {
          address: this.address,
          state: this.state,
          city: this.city,
          pincode: this.pincode
        },
        latitude: this.latitude,
        longitude: this.longitude,
        copyProjectPlan: this.selectedProjectPlan.id,
        assignees: this.assignees,
        boms: this.boms,
        bomTaskAssoc: this.bomTaskAssoc && this.boms,
        docTaskAssoc: this.docTaskAssoc && this.documents,
        documents: this.documents
      })
    },
    update () {
      let count = 0
      if (!emptyString(this.name, 'Project Name', 'projectName', false)) {
        count++
      }
      if (!emptyString(this.address, 'Address', 'address', false)) {
        count++
      }
      if (!emptyString(this.state, 'State', 'state', false)) {
        count++
      }
      if (!emptyString(this.city, 'City', 'city', false)) {
        count++
      }
      if (!emptyString(this.pincode, 'Pincode', 'pincode', false)) {
        count++
      }
      if (!emptyString(this.startDate, 'Start Date', 'startDate', false)) {
        count++
      }
      if (!emptyString(this.endDate, 'End Date', 'endDate', false)) {
        count++
      }
      if (this.startDate > this.endDate) {
        alert('End date cannot be set before Start Date')
        count++
      }
      if (!emptyString(this.revenue, 'Project Revenue', 'revenue', false)) {
        count++
      }
      if (!positiveFloat(this.revenue, 'Project Revenue', 'revenue', false)) {
        count++
      }
      if (!emptyString(this.cost, 'Project Cost', 'cost', false)) {
        count++
      }
      if (!positiveFloat(this.cost, 'Project Cost', 'cost', false)) {
        count++
      }
      if (this.longitude && !isValidLongitudeTenant(this.longitude, 'longitude', 'longitude')) {
        count++
      }
      if (this.latitude && !isValidLatitudeTenant(this.latitude, 'latitude', 'latitude')) {
        count++
      }
      if ((this.latitude && !this.longitude) || (!this.latitude && this.longitude)) {
        if (this.latitude && !this.longitude) {
          emptyString(this.latitude, 'latitude', 'latitude', false)
          alert('Please provide Longitude')
        }
        if (!this.latitude && this.longitude) {
          emptyString(this.latitude, 'latitude', 'latitude', false)
          alert('Please provide Latitude')
        }
        count++
      }
      if (count > 0) {
        alert('Please review the highlighted fields')
        return
      }
      this.buttonDisabled = true
      this.$emit('update', {
        name: this.name,
        company: this.company,
        planned_start_date: this.startDate,
        planned_end_date: this.endDate,
        project_cost: this.cost,
        project_revenue: this.revenue,
        address: {
          address: this.address,
          state: this.state,
          city: this.city,
          pincode: this.pincode
        },
        latitude: this.latitude,
        longitude: this.longitude
      })
    },
    handleClickOutside (event) {
      if (!this.$el.contains(event.target)) {
        this.cancel()
      }
    },
    clearSearchKeyword () {
      this.projectSearchKeyword = ''
    },
    keyPress (e) {
      const activeElement = this.window.document.activeElement.tagName.toLowerCase()
      const activeElementCheck = ['input', 'select', 'textarea'].includes(activeElement)
      if (e instanceof KeyboardEvent && e.code === 'Enter' && activeElement !== 'button') {
        this.window.document.activeElement.blur()
      }
      if (!this.drawer || this.buttonDisabled || activeElementCheck) {
        return
      }
      if (e instanceof KeyboardEvent && e.code === 'Escape') {
        this.cancel()
      } else if (e instanceof KeyboardEvent && e.code === 'Enter' && !this.isUpdate) {
        this.save()
      } else if (e instanceof KeyboardEvent && e.code === 'Enter' && this.isUpdate) {
        this.update()
      }
    }
  },
  mounted () {
    if (this.closeOnOutsideClick) {
      document.addEventListener('mousedown', this.handleClickOutside)
    }
  },
  created () {
    this.selectedTenant = this.openTenantId
    document.body.addEventListener('keydown', this.keyPress)
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
    document.removeEventListener('click', this.handleClickOutside)
  }
}
</script>

<style lang="scss" scoped >
.project-form {
  padding: 12px;
  font-size: 12px;
  height: 100%;
  width: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  .end-date{
    margin-left: 3rem;
  }
  h3 {
    font-size: 20px;
    font-weight: 500;
  }
}
</style>
