<template>
    <div class="flex-column h-center column p-3 ">
      <div class="v-center space-between">
        <h2>{{label}}</h2>
        <img v-if="!expand" src="~@/assets/images/icons/expand.svg" alt="" height="24" @click="doExpand" />
        <img v-else src="~@/assets/images/icons/collapse.svg"  @click="doCollapse" height="30" />
      </div>

      <div :class="expand?'categorywisechartExpanded':'categorywisechart'" >
        <apexchart :type="chartType" :height="expand?'100%':'500px'" :options="chartOptions" :series="series"></apexchart>
      </div>
    </div>
    </template>

<script>
import VueApexCharts from 'vue-apexcharts'

export default {
  components: {
    apexchart: VueApexCharts
  },
  props: {
    label: {
      type: String
    },
    curve: {
      type: String,
      default: 'straight'
    },
    marker: {
      type: Boolean,
      default: false
    },
    chartType: {
      type: String,
      default: 'line'
    },
    graphcolors: {
      type: Array
    },
    graphSeries: {
      type: Array,
      default: () => []
    },
    xaxisCategories: {
      type: Array,
      default: () => []
    },
    type: {
      type: String
    }
  },
  data () {
    return {
      series: this.graphSeries,
      expand: false,
      chartOptions: {
        chart: {
          type: this.chartType,
          zoom: {
            autoScaleYaxis: true
          }
        },
        plotOptions: {
          bar: {
            horizontal: this.type === 'horizontal',
            dataLabels: {
              position: 'top'
            }
          }
        },
        markers: {
          size: this.markers ? 5 : 0,
          style: 'hollow'
        },
        stroke: {
          curve: this.curve
        },
        dataLabels: {
          enabled: false,
          offsetX: -6,
          style: {
            fontSize: '12px',
            colors: ['#fff']
          }
        },
        tooltip: {
          shared: true,
          intersect: false
        },
        xaxis: {
          categories: this.xaxisCategories,
          tickPlacement: 'on',
          labels: {
            show: true,
            style: {
              colors: [],
              fontSize: '15px'
            }
          }
        },
        yaxis: {
          labels: {
            show: true,
            style: {
              colors: [],
              fontSize: '15px'
            }
          }
        },
        fill: {
          type: this.chartType === 'area' ? 'gradient' : 'origin',
          gradient: {
            shadeIntensity: 1,
            opacityFrom: 0.7,
            opacityTo: 0.9,
            stops: [0, 100]
          }
        },
        colors: this.graphcolors
      }
    }
  },
  watch: {
    graphSeries: {
      handler: function (newValue, oldValue) {
        this.series = this.graphSeries
      },
      deep: true // This option is necessary to watch changes deeply inside the object/array
    }
  },
  methods: {
    doExpand () {
      this.expand = true
      this.$emit('expand', true)
    },
    doCollapse () {
      this.expand = false
      this.$emit('collapse', true)
    }
  }
}
</script>
  <style lang="scss" scoped>
  .categorywisechartExpanded {
  width: 100%;
  height:calc(100vh - 12rem);
  }
  .categorywisechart {
    padding: 20px;
  width:100%;
  height:500px;
  }
  .apexcharts-toolbar{
    gap: 7px;
    z-index:3!important;
  }
  </style>
