import { GQL } from '../graphQl'

export const getPartIdDataTypesQuery = () => GQL`query getPartIdDataTypesQuery {
  id_generation_components(where: {enabled: {_eq: true}}) {
    caption
    component_type
    default_format
    description
    enabled
    id
    is_dynamic
  }
}`

export const getTemplateDetailsQuery = () => GQL`query GetIdGenerationRules($templateId: uuid!) {
  core_sequence_id_template_by_pk(id: $templateId) {
    name
    feature_id
    created_at
    created_by_user {
      first_name
      last_name
    }
    id_generation_rules {
      auto_increment
      component_id
      component {
        caption
      }
      created_at
      core_custom_list {
        name
        id
      }
      custom_list_id
      default_value
      delimiter_after
      id
      is_mandatory
      padding_length
      prefix
      sequence_order
      sequence_template_id
      start_value
      suffix
      tag_type_id
      validation_type
      validation_value
      custom_list_reset_governed_by{
        sequence_order
      }
       month_reset_governed_by{
        sequence_order
      } 
       tag_reset_governed_by{
        sequence_order
      } 
       year_reset_governed_by{
        sequence_order
      }
    }
  }
}`

export const getTemplateDataQuery = () => GQL`query getAllSequenceTemplate($conditions: core_sequence_id_template_bool_exp, $limit: Int, $offset: Int) {
  core_sequence_id_template_aggregate(where: $conditions) {
    aggregate {
      count(columns: id)
    }
  }
  core_sequence_id_template(where: $conditions, limit: $limit, offset: $offset, order_by: {created_at: desc}) {
    id
    name
    sequence_rules
    core_feature {
      name
      id
    }
    created_by_user {
      first_name
      last_name
    }
  }
}`

export const saveSequenceTemplateGenerationQuery = () => GQL`mutation createSequenceTemplate($rules: [sequence_rules_input!]!, $template_input: sequence_template_input!) {
  create_sequence_template(rules: $rules, template_input: $template_input) {
    message
  }
}`

export const getTenantDefaultsQuery = () => GQL`query getTenantDefaults {
  tenant_defaults {
    tenant_feature_configuration
  }
}`

export const GetIdGenerationRulesByFeatureIdQuery = () => GQL`query GetIdGenerationRulesByFeatureId($id: uuid!) {
  id_generation_rules(
    where: {sequence_template_id: {_eq: $id}},
    order_by: {sequence_order: asc}
  ) {
    auto_increment
    component_id
    created_at
    custom_list_id
    default_value
    delimiter_after
    id
    is_mandatory
    padding_length
    prefix
    sequence_order
    sequence_template_id
    start_value
    suffix
    tag_type_id
    tag_type {
      tags {
        id
        parent_id
        name
      }
    }
    validation_type
    validation_value
    component {
      id
      enabled
      is_dynamic
      caption
    }
    core_custom_list {
      id
      custom_list_values {
        id
        name
      }
    }
  }
}
`
