<template>
      <div class="file-preview s">
        <div class="grid-2">
          <div class="file-preview-image">
            <img :src="image" alt="" />
          </div>
          <div class="file-preview-details">
            <div :class="{
                'input-group':true,
                }">
              <label for="">File Name</label>
              <input type="text" v-model="name" :class="{'border-red':validateImageName}" />
            </div>
            <div class="input-group mt-3">
              <label for="">File Description </label>
              <textarea type="text" :value="file.description" disabled/>
            </div>
            <div class="input-group mt-3">
              <label for="">File Type</label>
              <input type="text" :value="file.doc_ext" disabled />
            </div>
            <div class="input-group mt-3">
              <label for="">File Size</label>
              <input type="text" :value="file.doc_size" disabled />
            </div>
            <div class="flex-end mt-5">
              <button class="btn btn-black mx-3" @click="$emit('cancel')" >Cancel</button>
              <button class="btn" @click="renameDoc" :disabled="buttonDisabled" >Update</button>
            </div>
          </div>
        </div>
      </div>
</template>

<script>
import { renameDocument } from '@/api'
import { alert, success } from '@/plugins/notification'

export default {
  name: 'rename-dialog',
  data () {
    return {
      name: '',
      description: ''
    }
  },
  props: {
    file: {
      type: Object,
      default: () => ({})
    },
    image: {
      type: String,
      default: ''
    },
    updatedName: {
      type: String,
      default: ''
    },
    buttonDisabled: {
      type: Boolean,
      default: false
    },
    open: {
      type: Boolean,
      default: false
    }
  },
  mounted () {
    if (this.$props.updatedName) {
      this.name = this.$props.updatedName.split('.').shift()
    } else {
      this.name = this.$props.file.doc_name.split('.').shift()
    }
    this.description = this.$props.file.description
  },
  computed: {
    // this is checking whether any '.' character is included with the file name
    validateImageName () {
      const regex = /^[^.]*$/
      if (regex.test(this.name)) {
        return false
      } else {
        return true
      }
    }
  },
  methods: {
    renameDoc () {
      this.buttonDisabled = true
      if (this.validateImageName) {
        alert('Dots and Brackets are not allowed in the file name.')
        this.buttonDisabled = false
        return
      }
      if (!this.name) {
        alert('Please enter a valid name')
        this.buttonDisabled = false
        return
      }
      if (this.name === this.$props.file.doc_name) {
        success('Successfully renamed document')
        this.$emit('renamed', null)
        this.buttonDisabled = false
        return
      }
      renameDocument(this.$props.file.id, this.name + '.' + this.file.doc_ext).then(() => {
        success('Successfully renamed document')
        this.$emit('renamed', this.name + '.' + this.file.doc_ext)
      }).catch(() => {
        alert('Failed to rename')
        this.buttonDisabled = false
      })
    },
    keyPress (e) {
      if (!this.open || this.buttonDisabled) {} else if (e instanceof KeyboardEvent && e.code === 'Enter') {
        this.renameDoc()
      }
    }
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
  }
}
</script>

<style lang="scss" scoped>
  .file-preview {
    min-width: 300px;
    .file-preview-image {
      width: 300px;
      height: 300px;
      img {
        width: 100%;
        height: 100%;
        -o-object-fit: cover;
        object-fit: contain;
        -o-object-position: center;
        object-position: center;
        background: #dbdbdb;
        border: 1px solid #cfcfcf;
        border-radius: 10px;
      }
    }
  }
</style>
