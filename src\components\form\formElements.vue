<template>
  <div class="form-elements">
    <div class="l text-center bg-brand py-1 weight-500">Select Elements</div>
    <div class="form-elements-container">
      <drag
        v-for="element in formFields"
        :key="element.id"
        class="form-element"
        :data="element"
        v-show="element.enabled && !ignoreList.includes(element.key)"
      >
        <img :src="iconMap[element.key]" alt="" />
        <div v-overflow-tooltip class="elipsis-text">{{ element.caption }}</div>
      </drag>
    </div>
  </div>
</template>

<script>
import Drag from '@/components/common/drag.vue'
import Config from '@/config'
import { mapGetters } from 'vuex'
const TextIcon = require('@/assets/images/icons/text-icon.svg')
const LongTextIcon = require('@/assets/images/icons/long-text.svg')
const NumberIcon = require('@/assets/images/icons/number-icon.svg')
const DateIcon = require('@/assets/images/icons/date-icon.svg')
const TimeIcon = require('@/assets/images/icons/time-icon.svg')
const SwitchIcon = require('@/assets/images/icons/switch-icon.svg')
const ListIcon = require('@/assets/images/icons/list-icon.svg')
const AttachmentIcon = require('@/assets/images/icons/attachment-icon.svg')
const UserIcon = require('@/assets/images/icons/single-user.svg')
const MultiUserIcon = require('@/assets/images/icons/multiple-users.svg')
const MaterialIcon = require('@/assets/images/material-master-icon.png')
const LocationIcon = require('@/assets/images/icons/location-icon.svg')
const ProductCodeIcon = require('@/assets/images/icons/productcode-icon.svg')
const TagIcon = require('@/assets/images/icons/tag-icon.svg')
const BomIcon = require('@/assets/images/bom-icon.png')
const DocumentIcon = require('@/assets/images/documents-icon.png')

export default {
  name: 'FormElements',
  data: () => ({
    ignoreList: ['MULTI_COMPANY'],
    iconMap: {
      TEXT: TextIcon,
      LONG_TEXT: LongTextIcon,
      NUMBER: NumberIcon,
      DATE: DateIcon,
      TIME: TimeIcon,
      BOOLEAN: SwitchIcon,
      CONFIGURATION_LIST: ListIcon,
      ATTACHMENT: AttachmentIcon,
      COMPANY: TextIcon,
      USER: UserIcon,
      MULTI_USER: MultiUserIcon,
      MATERIALS: MaterialIcon,
      LOCATION: LocationIcon,
      PRODUCT_CODE: ProductCodeIcon,
      TAGS: TagIcon,
      BOM: BomIcon,
      DOCUMENTS: DocumentIcon
    }
  }),
  components: {
    Drag
  },
  methods: {
    startDrag (event, type) {
      event.dataTransfer.setData('type', type)
    }
  },
  mounted () {
    if (parseInt(this.formTemplateType) === Config.STANDARD_MATERIAL_FORM.form_type || parseInt(this.formTemplateType) === Config.STANDARD_BOM_FORM.form_type) {
      this.ignoreList = ['MULTI_COMPANY', 'ATTACHMENT', 'COMPANY', 'USER', 'MULTI_USER', 'MATERIALS', 'LOCATION', 'PRODUCT_CODE', 'BOM', 'TAGS']
    }
  },
  computed: {
    ...mapGetters('form', ['formFields', 'formTemplateType'])
  },
  watch: {
    formTemplateType () {
      if (parseInt(this.formTemplateType) === Config.STANDARD_MATERIAL_FORM.form_type || parseInt(this.formTemplateType) === Config.STANDARD_BOM_FORM.form_type) {
        this.ignoreList = ['MULTI_COMPANY', 'ATTACHMENT', 'COMPANY', 'USER', 'MULTI_USER', 'MATERIALS', 'LOCATION', 'PRODUCT_CODE', 'BOM', 'TAGS']
      }
    }
  }
}
</script>

<style lang="scss" scoped >
.form-elements {
  &-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  .form-element {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 90px;
    width: 90px;
    background: rgba(var(--brand-rgb), 0.3);
    margin: 5px;
    padding: 6px;
    div {
      width: 100%;
      font-size: 12px;
      text-align: center;
    }
    img {
      width: 20px;
      margin-bottom: 4px;
    }
    &:hover {
      background-color: rgba(var(--brand-rgb), 0.5);
    }
  }
}
</style>
