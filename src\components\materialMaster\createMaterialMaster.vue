<template>
  <div class="relative">
  <div class="material-master-create s p-4">
    <div>
       <part-id-preview :components="partIdRules" v-if="partIdRules.length" @update="updatePartIdValues" title="Material Id Preview"/>
    </div>
    <div class="mt-2">
       <part-id-preview
       :components="partIdRulesForProductCode"
       :disabled="!productCode"
       :showToggle="!bomForm"
       v-if="partIdRulesForProductCode.length"
       @update="updatePartIdValuesForProductCode"
       @togglePartIdGeneration="() => productCode = !productCode"
       title="Product Code Preview"/>
    </div>
    <div class="mt-2" :class="partIdRules.length && !partIdRulesForProductCode.length ? '' : 'grid-2'">
      <div class="input-group imp" data-validation="CustomMaterialId" v-if="!partIdRules.length">
        <label v-if="view === 'material'" class="key">Material Id:</label>
        <label v-else class="key">Resource Id:</label>
        <input type="text" v-model="materialMaster.custom_material_id" />
      </div>
      <div class="input-group imp" data-validation="CustomMaterialName">
        <label v-if="view === 'material'" class="key">Material Name:</label>
        <label v-else class="key">Resource Name:</label>
        <input type="text" v-model="materialMaster.material_name" />
      </div>
      <div class="input-group imp" v-if="partIdRulesForProductCode.length">
          <label class="key">Type:</label>
          <select v-model="materialMaster.type" :disabled="true" >
          <option v-for="(value, key) in materialType" :value="key" :key="key">{{ value }}</option>
        </select>
    </div>
    </div>
    <div class="grid-2 mt-2">
      <div class="input-group imp" data-validation="UnitOfMaterial">
        <label class="key">Unit Of Material:</label>
        <select v-if="materialMaster.type === 2" v-model="materialMaster.unit_of_material" disabled="true">
            <option :value="resourceUnit.id" selected >{{ this.resourceUnit.name }}</option>
        </select>
        <select v-else v-model="materialMaster.unit_of_material">
          <option v-for="item in list_of_unit_of_material" :value="item.id" :key="item.id">{{ item.name }}</option>
        </select>
      </div>
      <div class="input-group imp" data-validation="PlmMaterialId">
        <label v-if="view === 'material'" class="key">PLM Material Id:</label>
        <label v-else class="key">PLM Resource Id:</label>
        <input type="text" v-model="materialMaster.plm_material_id" />
      </div>
    </div>
    <!-- <div class="mt-2 new-product">
        <div class="input-group mt-2" :class="bomForm ? ' imp' : ''">
          <label class="key">Product Code:</label>
          <input type="text" v-model="productCodeName" />
        </div>
    </div> -->
    <div class="grid-2 mt-2" v-if="!partIdRulesForProductCode.length">
      <div class="input-group mt-2">
          <label class="key">Product Code:</label>
          <input type="text" v-model="productCodeName"/>
    </div>
        <div class="input-group mt-2 imp">
          <label class="key">Type:</label>
          <select v-model="materialMaster.type" :disabled="true" >
          <option v-for="(value, key) in materialType" :value="key" :key="key">{{ value }}</option>
        </select>
    </div>
  </div>
    <div v-if="moreInput">
    <div class="input-group mt-2">
      <label v-if="view === 'material'" class="key">Material Description:</label>
      <label v-else class="key">Resource Description:</label>
      <textarea type="text" v-model="materialMaster.material_description" />
    </div>
    <div class="grid-2 mt-2">
      <div class="input-group">
        <label class="key">PLM Record Id:</label>
        <input type="text" v-model="materialMaster.plm_record_id" />
      </div>
      <div class="input-group">
        <label v-if="view === 'material'" class="key">ERP Material Id:</label>
        <label v-else class="key">ERP Resource Id:</label>
        <input type="text" v-model="materialMaster.erp_material_id" />
      </div>
    </div>
    <div class="mt-2" :class="view === 'resource' ? 'grid-2' : ''">
    <div v-if="view === 'material'" class="input-group">
      <label class="key" @click="checkData">Material Group:</label>
      <select v-model="materialMaster.material_group">
          <option v-for="item in list_of_material_group" :value="item.id" :key="item.id">{{ item.name }}</option>
        </select>
    </div>
    <div v-else class="input-group mt-2">
      <label class="key" @click="checkData" >Resource Group:</label>
      <select v-model="materialMaster.resource_group">
          <option v-for="item in list_of_material_group" :value="item.id" :key="item.id">{{ item.name }}</option>
        </select>
    </div>
      <div v-if="view === 'resource'" class="input-group mt-2">
        <label class="key">Resource State:</label>
        <select v-model="materialMaster.resource_state">
          <option v-for="item in resource_states" :value="item.id" :key="item.name" >{{item.name}}</option>
        </select>
      </div>
    </div>
    <div class="grid-2 mt-2">
      <div class="input-group" data-validation="GrossWeight">
        <label class="key">Gross Weight:</label>
        <input type="number" v-model.number="materialMaster.gross_weight" @keydown="changeNumber"/>
      </div>
      <div class="input-group">
        <label class="key">Weight Unit:</label>
        <select v-model="materialMaster.weight_unit">
          <option v-for="item in list_of_weight_unit" :value="item.id" :key="item.id">{{ item.name }}</option>
        </select>
      </div>
    </div>
    <div class="grid-2 mt-2">
      <div class="input-group" data-validation="UnitCost">
        <label class="key">Unit Cost:</label>
        <input type="number" v-model.number="materialMaster.unit_cost" @keydown="changeNumber"/>
      </div>
      <div class="input-group" data-validation="UnitSalePrice">
        <label class="key">Unit Sale Price:</label>
        <input type="number" v-model.number="materialMaster.unit_sale_price" @keydown="changeNumber"/>
      </div>
    </div>
    <div class="grid-2 mt-2">
      <div class="input-group" data-validation="Inventory">
        <label class="key">Inventory:</label>
        <input min="0" max="2147483647" type="number" v-model.number="materialMaster.inventory" @keydown="changeNumber"/>
      </div>
      <effectiveDate @date-selected="createEffectiveDate" />
    </div>
    <div class="grid-2 mt-2">
      <div class="input-group">
        <label class="key">Lead Time:</label>
        <input type="number" v-model.number="materialMaster.lead_time" @keydown="changeNumber"/>
      </div>
      <div class="input-group">
        <label class="key">Storage Loc:</label>
        <select v-model="materialMaster.storage_loc" v-if="list_of_address_location.length === 0">
          <option disabled>No address locations available.</option>
          <option v-for="item in list_of_address_location" :value="item.id" :key="item.id">{{ item.name }}</option>
        </select>
        <select v-model="materialMaster.storage_loc" v-else>
          <option v-for="item in list_of_address_location" :value="item.id" :key="item.id">{{ item.name }}</option>
        </select>
      </div>
    </div>
    <div class="grid-1 mt-2">
      <div>
        <label class="key"  >Tags</label>
        <tagInput title="Add tags to material"  :type="1"  :lastParentId="tagGroupData?.tagArray[selected_tag_line]?.at(-1)?.id" @addNewTag="addnewTag" :tagGroupData="tagGroupData" @parentLevelSelected="changeSelectedTagLine"/>
        <div class="tag-container">
          <div class="tags" v-for="tag in selectedTags" :key="tag.id">
            <div class="selected-tags">
              {{ tag.name }}
            </div>
          </div>
          <div class="tag-container"  >
            <label class="key">Attached Tags:</label>
            <div :class="{'tags mt-2 p-1':true, 'tags-line-selected p-1': selected_tag_line===index }"  v-for="(tagParentwise,index) in tagGroupData.tagArray" :key="index" @click="selected_tag_line=index">
              <div v-for="(tag, cindex) in tagParentwise" :key="tag.id "  class="flex v-center tags-line ">
                  <img width="8px" src="~@/assets/images/right-arrow-icon.svg" alt="" :class="{ 'tags-line-first-img': cindex === 0 }" />
                <div :class="{
                  'attached-tags v-center h-center ': true
              }"

                >
                {{ tag.name }}
                    <img
                :class="{
                  'pointer ml-1 close-icon': true,
                  }"
                @click="removeAttachedTags(tag.id)"
                @mouseover="sethoveredtag(cindex,index)"
                @mouseleave="sethoveredtag(null,null)"
                src="~@/assets/images/icons/close-icon.svg" width="16px"
              />
              <div :class="{'attachedTags_overLay': cindex>=hoverd_tag?.cindex && index===hoverd_tag?.index}">
              </div>
              </div>

              </div>
            </div>
            <div v-if="tagGroupData.tagArray.length === 0">
              <b class="no-tag-found">No tag is Attached!</b>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="grid-1 mt-2">
      <label class="key">
        Document</label>
        <div class="document-attach" >
          <div v-if="selectedFileData.length > 0 " class="weight-500 document-attach-view input-group imp " ><span v-for="file in selectedFileData" :key="file.id" class="document-attach-list" v-overflow-tooltip>{{ file.doc_name }}</span></div>
          <span v-tooltip="'Link Documents '" class="document-attach-edit" @click="documentSelectDialog = true"> <img src="~@/assets/images/icons/file-icon.svg" width="10px" height="10px" alt="" >
          </span>
          </div>
    </div>
  </div>
  <div class="input-group mt-3" v-show="moreInput">
      <label>{{ formTemplateData.name }}</label>
    </div>
          <div class="mt-2">
            <template v-for="(ele, index) in templateField">
            <component
              v-if="!ele.autogenerated"
              :ref="ele.field_id"
              :key="index"
              :is="ele.form_field.key + '_COMPONENT'"
              :data="ele"
              mode="CREATE"
              v-show="moreInput"
            />
          </template>
        </div>
  <div class="mt-2 m underline pointer" @click="toggleMore">
    <span v-if="moreInput">
      Show less
    </span>
    <span v-else>
      Add More Details
    </span>
  </div>
  <modal
    :open="documentSelectDialog"
    @close="documentSelectDialog = false"
    :closeOnOutsideClick="true"
    title=""
    >
    <document-selector @close="documentSelectDialog = false" :restrictUpload="true" @files-selected="handleFileSelection" :material-attachment="true"/>
  </modal>
  </div>
  <div class="flex flex-end pt-3 m">
    <div>
      <button class="btn btn-black mr-3 pointer" @click="$emit('close')">CANCEL</button>
      <button class="btn pointer" :disabled="addProduct || buttonDisabled"  @click="createMaterialMasterNew">
        SAVE
      </button>
    </div>
  </div>
  </div>
</template>

<script>
import {
  CreateMaterialMasterData,
  CreateProductCodeData,
  AddMaterialTags,
  UnitOfMaterial,
  WeightUnit,
  AddressLocation,
  CheckMaterialID,
  ResourceState,
  ResourceGroup,
  getDetailFormTemplate,
  SaveFormData,
  attachDocToMaterail,
  GetIdGenerationRulesByFeatureId,
  createMaterialMaster,
  getTenantDefaults,
  getGivenCustomListsData
} from '@/api'
import MaterialMasterValidation from '@/helper/formValidation/materialMaster.js'
import config from '../../config'
import { success, alert } from '@/plugins/notification'
import Loader from '@/plugins/loader'
import TagInput from '../common/tagInput-edit-material.vue'
import { mapGetters, mapMutations } from 'vuex'
import effectiveDate from '../../components/common/effectiveDate.vue'

import Modal from '../common/modal.vue'
import DocumentSelector from '../document/documentSelectorDialog.vue'
import { TagTrie } from '@/utils/tagsHelper'
import timeComponent from '@/components/form/elements/timeComponent.vue'
import configurationList from '@/components/form/elements/configurationList.vue'
import fileComponent from '@/components/form/elements/fileComponent.vue'
import dateComponent from '@/components/form/elements/dateComponent.vue'
import longTextComponent from '@/components/form/elements/longTextComponent.vue'
import numberComponent from '@/components/form/elements/numberComponent.vue'
import switchComponent from '@/components/form/elements/switchComponent.vue'
import textComponent from '@/components/form/elements/textComponent.vue'
import companyComponent from '@/components/form/elements/companyComponent.vue'
import userComponent from '@/components/form/elements/userComponent.vue'
import multiUserComponent from '@/components/form/elements/multiUserComponent.vue'
import materialComponent from '@/components/form/elements/materialComponent.vue'
import locationComponent from '@/components/form/elements/locationComponent.vue'
import productCodeComponent from '@/components/form/elements/productCodeComponent.vue'
import partIdPreview from '../common/partIdPreview.vue'
import { restrictKeys } from '@/utils/validations'
import { GetTenantConfigTypes } from '@/api/apis/tenantConfigFeature'

export default {
  components: {
    TagInput,
    effectiveDate,
    Modal,
    DocumentSelector,
    BOOLEAN_COMPONENT: switchComponent,
    NUMBER_COMPONENT: numberComponent,
    TEXT_COMPONENT: textComponent,
    LONG_TEXT_COMPONENT: longTextComponent,
    DATE_COMPONENT: dateComponent,
    TIME_COMPONENT: timeComponent,
    CONFIGURATION_LIST_COMPONENT: configurationList,
    ATTACHMENT_COMPONENT: fileComponent,
    COMPANY_COMPONENT: companyComponent,
    USER_COMPONENT: userComponent,
    MULTI_USER_COMPONENT: multiUserComponent,
    MATERIALS_COMPONENT: materialComponent,
    LOCATION_COMPONENT: locationComponent,
    PRODUCT_CODE_COMPONENT: productCodeComponent,
    partIdPreview
  },
  name: 'CreateMaterialMaster',
  props: {
    bomForm: {
      type: Boolean,
      default: false
    },
    open: {
      type: Boolean,
      default: false
    },
    view: {
      type: String,
      default: 'material'
    }
  },
  data: () => ({
    productCode: false,
    partIdRules: [],
    partIdValues: [],
    partIdValuesForProductCode: [],
    materialType: {},
    materialName: 'Material Group',
    type: [{ name: 'Material', id: 1 }, { name: 'Resource', id: 2 }],
    documentSelectDialog: false,
    addProduct: false,
    productCodeName: '',
    resourceUnit: {},
    selectedFileData: [],
    materialMaster: {
      custom_material_id: null,
      erp_material_id: null,
      gross_weight: null,
      inventory: null,
      lead_time: null,
      material_description: null,
      material_group: null,
      material_name: null,
      others: null,
      parent_id: null,
      effective_date: null,
      plm_material_id: null,
      plm_record_id: null,
      unit_cost: null,
      storage_loc: null,
      unit_of_material: null,
      unit_sale_price: null,
      weight_unit: null,
      type: null,
      form_id: null,
      source: 4
    },
    moreInput: false,
    list_of_unit_of_material: [],
    partIdRulesForProductCode: [],
    list_of_weight_unit: [],
    resource_states: [],
    list_of_material_group: [],
    list_of_address_location: [],
    selectedTags: [],
    weight_unit: null,
    tagGroupData: {
      firstLevelParents: [],
      tagArray: []
    },
    hoverd_tag: {
      cindex: null,
      index: null
    },
    selected_tag_line: 0,
    tagTrie: new TagTrie(),
    lastParentId: null,
    window: window,
    buttonDisabled: false,
    formTemplateData: {},
    templateId: '',
    loading: true,
    validationErrors: '' // this is for getting valiadtion errors
  }),
  computed: {
    ...mapGetters('tag', ['getTags']),
    ...mapGetters(['isOnProjectLevel', 'tenantDefaultsData']),
    templateField () {
      if (this.formTemplateData && this.formTemplateData.template_versions) {
        const activeVersion =
        this.formTemplateData.template_versions.find((item) => item.active) ||
        {}
        return activeVersion.template_fields || []
      }
      return []
    }
  },
  methods: {
    findUsedCustomListIds (templateVersions) {
      const configIdSet = new Set()
      for (const templateVersion of templateVersions) {
        for (const field of templateVersion.template_fields) {
          if (field.field_type_id === config.FORM_TYPE.CONFIGRATION_LIST) {
            configIdSet.add(field.custom_list_id)
          }
        }
      }
      return configIdSet
    },
    async getGivenCustomList (ids) {
      const customListData = await getGivenCustomListsData(ids)
      const customListMap = {}
      for (const customList of customListData.core_custom_list) {
        customListMap[customList.id] = customList.custom_list_values
      }
      this.$store.dispatch('form/saveCustomListMap', customListMap)
    },
    toggleProductCode () {
      this.productCode = !this.productCode
    },
    updatePartIdValues (values) {
      this.partIdValues = values
    },
    updatePartIdValuesForProductCode (values) {
      this.partIdValuesForProductCode = values
    },
    changeNumber (event) {
      if (['e', 'E', '+', '-'].includes(event.key)) {
        restrictKeys(event)
      }
    },
    checkData () {
    },
    resetForm () {
      this.materialMaster = {
        custom_material_id: null,
        erp_material_id: null,
        gross_weight: null,
        inventory: null,
        lead_time: null,
        material_description: null,
        material_group: null,
        material_name: null,
        others: null,
        parent_id: null,
        plm_material_id: null,
        plm_record_id: null,
        effective_date: null,
        unit_cost: null,
        storage_loc: null,
        unit_of_material: null,
        unit_sale_price: null,
        weight_unit: null,
        source: 4
      }
      this.productCodeName = ''
    },
    handleFileSelection (selectedFileData) {
      // the selectedfiles data is comming from modal (contains  all the datat related to selected docs)
      this.documentSelectDialog = false
      this.selectedFileData = selectedFileData.filter((element) => {
        if (element?.flag && element?.flag === 'deleted') { } else {
          return element
        }
      })
    },
    async submitForm () {
      const form = this.$refs
      const formValue = {}
      this.validationErrors = []
      let isValid = true // Track if the form is valid
      Object.keys(form).forEach((key) => {
        this.validateInputData(form[key][0]?.data, form)
        if (form[key].length && form[key][0]?.data?.autogenerated === false) {
          formValue[key] = form[key][0].componentValue
        }
      })
      if (this.validationErrors.length > 0) {
        isValid = false
        this.validationErrors.forEach(error => alert(error))
        return
      }
      const inputPayload = []
      Object.keys(formValue).forEach((key) => {
        if (formValue[key] !== undefined && formValue[key] !== null) {
          if (formValue[key] !== 0 && formValue[key].length !== 0) {
            inputPayload.push({
              field_id: key,
              value: formValue[key]
            })
          }
        }
      })
      const body = [{
        templateId: this.templateId,
        dueDate: new Date().toISOString().split('T')[0],
        inputPayload: inputPayload,
        status: 1
      }]
      if (inputPayload.length > 0) {
        await SaveFormData(body, this.isOnProjectLevel).then((res) => {
          if (res.message === 'Unable to save form') {
            alert(res.message)
          }
          this.materialMaster.form_id = res.insertedFormIds[0]
        // this means you are uploading a material custom field form
        })
      }
      return isValid
    },
    validateInputData (formInput, form) {
      if (formInput?.form_field?.key === 'USER') {
        if (formInput.required && (!form[formInput.field_id][0].componentValue || !form[formInput.field_id][0].componentValue.length)) {
          this.validationErrors.push(`${formInput.caption} is a mandatory field`)
        }
      }
      if (formInput?.form_field?.key === 'BOOLEAN') {
        // we are comparing the value to null instead of checking if it is a falsy value cause the boolean component value will false in one case and it is valid value
        if (formInput.required && form[formInput.field_id][0].componentValue === null) {
          this.validationErrors.push(`${formInput.caption} is a mandatory field`)
        }
        return
      } else if (formInput?.form_field?.key === 'MATERIALS') {
        if (formInput.required && form[formInput.field_id][0].componentValue.length < 1) {
          this.validationErrors.push(`${formInput.caption} is a mandatory field`)
        }
        return
      }
      if (formInput.required && !form[formInput.field_id][0].componentValue) {
        this.validationErrors.push(`${formInput.caption} is a mandatory field`)
      }
    },
    validateForm () {
      const form = this.$refs
      const formValue = {}
      this.validationErrors = []
      let isValid = true // Track if the form is valid
      Object.keys(form).forEach((key) => {
        this.validateInputData(form[key][0]?.data, form)
        if (form[key].length && form[key][0]?.data?.autogenerated === false) {
          formValue[key] = form[key][0].componentValue
        }
      })
      if (this.validationErrors.length > 0) {
        isValid = false
        this.validationErrors.forEach(error => alert(error))
        return false
      }
      const inputPayload = []
      Object.keys(formValue).forEach((key) => {
        if (formValue[key] !== undefined && formValue[key] !== null) {
          if (formValue[key] && formValue[key] !== 0 && formValue[key].length !== 0) {
            inputPayload.push({
              field_id: key,
              value: formValue[key]
            })
          }
        }
      })
      const res = isValid ? inputPayload : false
      return res
    },
    validatePartIdValues () {
      let isValid = true
      for (const value of this.partIdValues) {
        if (!value.value) {
          alert(`Please enter a value for ${value.caption} in material id`)
          isValid = false
        }
      }
      return isValid
    },
    validatePartIdValuesForProductCode () {
      let isValid = true
      for (const value of this.partIdValuesForProductCode) {
        if (!value.value) {
          alert(`Please enter a value for ${value.caption} in product code`)
          isValid = false
        }
      }
      return isValid
    },
    async createMaterialMasterNew () {
      const loader = new Loader()
      try {
        this.buttonDisabled = true
        if (!this.validatePartIdValues()) {
          this.buttonDisabled = false
          return
        }
        if (this.productCode && !this.validatePartIdValuesForProductCode()) {
          this.buttonDisabled = false
          return
        }
        const skipMaterialIdCheck = this.partIdRules.length > 0
        if (skipMaterialIdCheck) {
          this.materialMaster.custom_material_id = undefined
        }
        if (!MaterialMasterValidation(this.materialMaster, skipMaterialIdCheck)) {
          this.buttonDisabled = false
          return
        }
        if (this.bomForm && !this.productCodeName.length && !this.partIdRulesForProductCode.length) {
          alert('Product Code is Missing')
          this.buttonDisabled = false
          return
        }
        if (this.materialMaster.inventory && this.materialMaster.inventory > 2147483647) {
          alert('Inventory limit exceeded. Lower the value!')
          this.buttonDisabled = false
          return
        }
        loader.show()
        this.materialMaster.form_id = undefined
        if (this.view === 'material') {
          this.materialMaster.resource_group = undefined
          this.materialMaster.resource_state = undefined
        } else this.materialMaster.material_group = undefined
        const body = {
          productCodeInput: this.productCodeName.length ? this.productCodeName.trim() : undefined,
          tagInput: [],
          docInput: this.selectedFileData.map(file => file.id),
          materialInput: this.materialMaster,
          customFieldInput: []
        }
        for (const tagArray of this.tagGroupData.tagArray) {
          for (const tag of tagArray) {
            body.tagInput.push(tag.id)
          }
        }
        const customFieldData = this.validateForm()
        if (customFieldData) {
          body.customFieldInput = customFieldData
        } else {
          this.buttonDisabled = false
          loader.hide()
          return
        }
        if (this.partIdRules.length) {
          const sequenceInput = this.partIdValues.map((value) => {
            return {
              value: value.value,
              delimiter: value.delimiter,
              sequence: value.sequence
            }
          })
          body.sequenceInput = sequenceInput
        }
        if (this.productCode && this.partIdRulesForProductCode.length) {
          const sequenceInput = this.partIdValuesForProductCode.map((value) => {
            return {
              value: value.value,
              delimiter: value.delimiter,
              sequence: value.sequence
            }
          })
          body.productCodeSequenceInput = sequenceInput
        } else {
          body.productCodeSequenceInput = []
        }
        await createMaterialMaster(body)
        success('Material Master Data Created successfully')
        this.$emit('close')
        this.resetForm()
      } catch (err) {
        alert(err.graphQLErrors[0]?.message ?? 'Unable to create material master')
      } finally {
        this.buttonDisabled = false
        loader.hide()
      }
    },
    async createMaterialMaster () {
      this.buttonDisabled = true
      if (!MaterialMasterValidation(this.materialMaster)) {
        this.buttonDisabled = false
        return
      }
      if (this.bomForm && !this.productCodeName.length) {
        alert('Product Code is Missing')
        this.buttonDisabled = false
        return
      }
      const isFormValid = await this.submitForm()
      if (!isFormValid) {
        this.buttonDisabled = false
        return
      }
      if (this.materialMaster.inventory && this.materialMaster.inventory > 2147483647) return alert('Inventory limit exceeded. Lower the value!')
      const loader = new Loader()
      loader.show()
      if (this.productCodeName.length) {
        const body = {
          product_code: this.productCodeName
        }
        await CheckMaterialID(this.materialMaster.custom_material_id)
          .then((data) => {
            if (data.core_material_master.length !== 0) {
              alert('Material ID exists in Material Master. Please enter a unique Material ID')
              this.buttonDisabled = false
              loader.hide()
            } else {
              CreateProductCodeData(body)
                .then((res) => {
                  // const id = res.insert_product_code_one.id
                  // this.materialMaster.product_code = id
                  if (this.view === 'material') {
                    this.materialMaster.resource_group = undefined
                  } else this.materialMaster.material_group = undefined
                  CreateMaterialMasterData(this.materialMaster)
                    .then((res) => {
                      const materialId = res.insert_core_material_master_one.id
                      this.attachDocumentTomaterial(materialId)
                      const tags = [...this.selectedTags, ...this.getTags]
                      if (tags?.length) {
                        const tagMaterials = tags.map(tag => {
                          return {
                            tag_id: tag.id,
                            material_id: materialId
                          }
                        })
                        AddMaterialTags(tagMaterials)
                          .then(() => {
                            success('Material Master Data Created successfully')
                            this.$emit('created', {
                              ...this.materialMaster,
                              product_code_name: this.productCodeName
                            })
                            this.resetForm()
                            loader.hide()
                          })
                          .catch(() => {
                            alert('Something went wrong')
                            this.buttonDisabled = false
                            loader.hide()
                          })
                      } else {
                        success('Material Master Data Created successfully')
                        this.$emit('created', {
                          ...this.materialMaster,
                          product_code_name: this.productCodeName
                        })
                        this.resetForm()
                        loader.hide()
                      }
                    })
                    .catch(() => {
                      alert('Something went wrong')
                      this.buttonDisabled = false
                      loader.hide()
                    })
                })
                .catch((err) => {
                  if (err.message.includes('unique_product_code_validation')) {
                    alert('Product code already exists')
                    this.buttonDisabled = false
                  } else {
                    this.buttonDisabled = false
                    alert('Something went wrong')
                  }
                  loader.hide()
                })
            }
          })
          .catch(() => {
            alert('Something went wrong')
            this.buttonDisabled = false
            loader.hide()
          })
      } else {
        if (this.view === 'material') {
          this.materialMaster.resource_group = undefined
        } else this.materialMaster.material_group = undefined
        await CreateMaterialMasterData(this.materialMaster)
          .then((res) => {
            this.attachDocumentTomaterial(res?.insert_core_material_master_one?.id)
            const materialId = res.insert_core_material_master_one.id
            if (this.tagGroupData.tagArray) {
              const tagMaterials = []
              for (const tagArray of this.tagGroupData.tagArray) {
                for (const tag of tagArray) {
                  tagMaterials.push({
                    tag_id: tag.id,
                    material_id: materialId
                  })
                }
              }
              // here we are deleteing all tags associated with  given material is deleted and then new tags will attached
              AddMaterialTags(tagMaterials)
                .then(() => {
                  success('Material Master Data Created successfully')
                  loader.hide()
                  this.$emit('close')
                })
                .catch((err) => {
                  this.buttonDisabled = false
                  if (err.message.includes('tag_material_pkey')) {
                    alert('Tag Already attached!')
                  } else {
                    alert('Something went wrong')
                  }
                  loader.hide()
                })
            } else {
              success('Material Master Data Created successfully')
              this.$emit('close')
              this.resetForm()
              loader.hide()
            }
          })
          .catch((err) => {
            this.buttonDisabled = false
            if (err.message.includes('core_material_master_custom_material_id_tenant_id_key')) {
              alert('Material ID already exists. Please enter a unique Material ID')
              this.materialMaster.custom_material_id = null
            } else {
              alert('Something went wrong')
            }
            loader.hide()
          })
      }
    },
    async attachDocumentTomaterial (materialId) {
      try {
        const materialAttch = []
        this.selectedFileData.forEach((element) => {
          if (element?.flag === 'deleted') {} else { materialAttch.push({ document_id: element.id, material_id: materialId }) }
        })
        if (materialAttch.length > 0) {
          await attachDocToMaterail(materialAttch)
        }
      } catch (error) {
        console.log(error)
      }
      // docId contains all the id of selected documnents
    },
    createEffectiveDate (dateValue) {
      this.materialMaster.effective_date = dateValue
    },
    ...mapMutations('tag', ['addNewTag', 'updateTags']),
    toggleMore () {
      this.moreInput = !this.moreInput
    },
    listOfUnitOfMaterial () {
      UnitOfMaterial().then((res) => {
        this.list_of_unit_of_material = res.custom_list_values.map((item) => {
          if (item.name === 'Hours (hrs)') {
            if (this.view === 'resource') {
              this.materialMaster.unit_of_material = item.name
              this.materialMaster.unit_of_material = item.id
            }
            this.resourceUnit = item
          }
          return {
            name: item.name,
            id: item.id
          }
        })
      })
    },
    listOfWeightUnit () {
      WeightUnit().then((res) => {
        this.list_of_weight_unit = res.custom_list_values.map((item) => {
          return {
            name: item.name,
            id: item.id
          }
        })
      })
    },
    listOfMaterialGroup () {
      if (this.view === 'material') {
        this.materialName = 'Material Group'
      } else this.materialName = 'Resource Group'
      ResourceGroup(this.materialName).then((res) => {
        this.list_of_material_group = res.custom_list_values.map((item) => {
          return {
            name: item.name,
            id: item.id
          }
        })
      })
    },
    listOfAddressLocation () {
      AddressLocation().then((res) => {
        this.list_of_address_location = res.address_locations
      })
    },
    removeAttachedTags (tagId) {
      this.tagTrie.deleteTagById2(tagId)
      this.tagGroupData = this.tagTrie.groupTagInArray()
    },
    sethoveredtag (cindex, index) {
      this.hoverd_tag.cindex = cindex
      this.hoverd_tag.index = index
    },
    addnewTag (tag) {
      this.tagTrie.insertTag({ name: tag.name, id: tag.id, parentId: tag.parent_id })
      this.tagGroupData = this.tagTrie.groupTagInArray()
    },
    changeSelectedTagLine (id) {
      if (id === null) {
        this.selected_tag_line = id
      } else {
        this.tagGroupData.tagArray.forEach((element, index) => {
          if (element[0].id === id) { this.selected_tag_line = index }
        })
      }
    },
    keyPress (e) {
      const activeElement = this.window.document.activeElement.tagName.toLowerCase()
      const activeElementCheck = ['input', 'select', 'textarea'].includes(activeElement)
      if (e instanceof KeyboardEvent && e.code === 'Enter' && activeElement !== 'button') {
        this.window.document.activeElement.blur()
      }
      if (!this.open || activeElementCheck || this.buttonDisabled) return
      if (e instanceof KeyboardEvent && e.code === 'Enter') {
        this.createMaterialMaster()
      }
    },
    async getSTFtempId () {
      if (this.tenantDefaultsData?.material_temp_id) {
        this.templateId = this.tenantDefaultsData.material_temp_id
        return
      }
      const res = await GetTenantConfigTypes()
      const tenantDefaults = res.tenant_defaults?.[0]
      this.$store.commit('setTenentDefaults', tenantDefaults)
      this.templateId = tenantDefaults.tenant_feature_configuration.MATERIAL?.FORM_TEMPLATE_DEFAULT
    },
    async getFormTemplateData () {
      this.loading = true
      await getDetailFormTemplate(this.templateId).then((res) => {
        this.formTemplateData = res.core_form_templates_by_pk
        const customListData = this.findUsedCustomListIds(res.core_form_templates_by_pk.template_versions)
        if (customListData.size > 0) {
          this.getGivenCustomList([...customListData])
        }
        this.loading = false
      })
    }
  },
  async mounted () {
    if (this.bomForm) {
      this.productCode = true
    }
    if (this.view === 'material') {
      this.materialMaster.type = 1
    } else {
      this.materialMaster.type = 2
    }
    const tenantDefaultsRes = await getTenantDefaults()
    const productCodeFeatureSeq = tenantDefaultsRes.tenant_defaults[0].tenant_feature_configuration?.PRODUCT_CODE?.SEQUENCE_TEMPLATE_DEFAULT
    const materialFeatureSeq = tenantDefaultsRes.tenant_defaults[0].tenant_feature_configuration?.MATERIAL?.SEQUENCE_TEMPLATE_DEFAULT
    if (productCodeFeatureSeq) {
      const res = await GetIdGenerationRulesByFeatureId(productCodeFeatureSeq)
      this.partIdRulesForProductCode = res.id_generation_rules
    }
    if (materialFeatureSeq) {
      const res = await GetIdGenerationRulesByFeatureId(materialFeatureSeq)
      this.partIdRules = res.id_generation_rules
    }
    ResourceState().then(res => {
      res.custom_list_values.forEach(value => {
        if (value.name === 'IDLE') {
          this.materialMaster.resource_state = value.id
        }
        this.resource_states = res.custom_list_values
      })
    })
    this.materialType = config.MATERIAL_TYPE
    this.listOfUnitOfMaterial()
    this.listOfWeightUnit()
    this.listOfMaterialGroup()
    this.listOfAddressLocation()
    await this.getSTFtempId()
    await this.getFormTemplateData()
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
  }
}
</script>

<style lang="scss" scoped >
.material-master-create {
  width: 600px;
  max-height: 70vh;
  padding-right: 5px;
  overflow-y: auto;
  .document-attach{
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
    width: 100%;
    max-height: 100px;
    min-height: 50px;
    // border-radius: 4px;
    // border: 2px solid rgba(var(--brand-rgb),0.4);
    cursor: pointer;
    &-view{
      display: flex;
      flex-wrap: wrap;
      gap: 5px;

    }
    &-edit{
      border: 2px solid rgba(0, 0,0,.3);
      border-radius: 3px;
      padding: 4px 6px;
      width: 25px;
      display: flex;
      justify-content: center;
      margin-left: 5px;
    }
    &-list{
      border: 2px solid rgba(var(--brand-rgb));;
      border-radius: 3px;
      background-color: var(--brand-color);
      padding: 5px 5px 5px 5px ;
    width: 100px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    cursor:context-menu;
    }
    span{
      cursor: pointer;
      user-select: none;
      background-color: transparent;
    }
  }
}
.tags {
  display: flex;
flex-wrap: wrap;
margin-left: 5px;
  margin-right: 5px;
  row-gap: 5px;
   &-line-selected{
    background-color: var(--brand-light-color);
   border: .4px solid var(--brand-color);
   border-radius: 5px;
   min-width: min-content;
   }
}
.tag-container {
  margin-top: 4px;
}

.no-tag-found{
  margin-left: 5px;
}
.attached-tags{
  cursor:pointer;
  background-color: var(--brand-color);
  padding: 0.6rem;
  font-size: small;
  border-radius: 0.4rem;
position:relative;
  &-fade{
background-color: rgb(0, 0,0,.6) ;
color:white
}
  & img{
 z-index:4;
  }
  & img{
    display: none;
  }
}
.tags-line{
  text-overflow: ellipsis;
  overflow: hidden;
   white-space: nowrap;
  & img{
    margin-inline: .5rem;
  }
  &-first-img{
    display: none;
  }
  .attached-tags:hover > img {
display: block;
scale: 1.2;
// -webkit-box-shadow:0px 0px 123px 21px rgba(215,224,221,0.78);
// -moz-box-shadow: 0px 0px 123px 21px rgba(215,224,221,0.78);
// box-shadow: 0px 0px 123px 21px rgba(215,224,221,0.78);
  }
  .attachedTags_overLay{
    background-color: black;
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 5px;
    opacity: .3;
    z-index: 1;
  }
}
</style>
