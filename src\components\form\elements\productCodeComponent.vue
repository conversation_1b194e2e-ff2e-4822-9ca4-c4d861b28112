<template>
  <div :class="{
    'form-input': true,
    'form-input--required': data.required
    }" >
      <label>{{data.caption}}:</label>
      <select :disabled="viewOnly" v-model="componentValue" @change="emitChange">
        <option v-for="item in productCodeList.data" :key="item.id" :value="item.product_code">
          {{item.product_code}}
        </option>
      </select>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'productCodeComponent',
  props: {
    mode: {
      type: String,
      default: 'TEMPLATE'
    },
    data: {
      type: Object,
      default: () => {}
    },
    value: {
      type: Number,
      default: 0
    },
    viewOnly: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      componentValue: ''
    }
  },
  watch: {
    value (val) {
      this.componentValue = +val
    }
  },
  computed: {
    ...mapGetters('form', ['productCodeList'])
  },
  created () {
    this.$store.dispatch('form/getProductCodeList')
    this.componentValue = this.value
  },
  methods: {
    emitChange () {
      this.$emit('customChange', {
        field_id: this.$props.data.field_id,
        value: this.componentValue
      })
    }
  }
}
</script>

<style lang="scss" scoped >

</style>
