<template>
  <div class="upload-document">
    <div class="upload-document-input">
      <input
        ref="inputfile"
        type="file"
        name=""
        :id="'upload-docs-' + compId"
        @change="onChange"
      />
      <label :for="'upload-docs-' + compId">
        <slot />
      </label>
    </div>
    <modal :open="previewPopover" :title="fileName" @close="closePopover">
      <div class="file-preview s">
        <div class="grid-2">
          <div class="file-preview-image">
            <img :src="fileBase64" alt="" />
          </div>
          <div class="file-preview-details">
            <div :class="{
                'input-group':true,
                }">
              <label for="">File Name</label>
              <input type="text" :class="{'border-red':validateImageName || duplicateName}" v-model="fileName" @blur ="checkDuplicateNames" :readonly="is_revision"/>
            </div>
            <div class="input-group mt-3">
              <label for="">File Description </label>
              <textarea type="text" v-model="fileDescription" />
            </div>
            <div class="input-group mt-3">
              <label for="">File Type</label>
              <input type="text" :value="fileType" disabled />
            </div>
            <div class="input-group mt-3">
              <label for="">File Size</label>
              <input type="text" :value="fileSize" disabled />
            </div>
            <div class="flex-end mt-5">
              <button class="btn btn-black mx-3" @click="closePopover">Cancel</button>
              <button class="btn" @click="uploadImage" :disabled="buttonDisabled" >Upload</button>
            </div>
          </div>
        </div>
      </div>
    </modal>
    <modal :open="openWarning" :title="'warning'" @close="openWarning=false">
      <div class="file-preview s px-5 v-center h-center ">
        Kindly choose files having the '.{{ this.folder?.blob_key && folder?.blob_key?.split('.')[1] }}' file extension
      </div>
      <div class="flex-end mt-5">
      <button class="btn btn-black mt-3" @click="openWarning=false">ok</button>
      </div>
    </modal>
  </div>
</template>

<script>
import modal from '../common/modal.vue'
import { generateS3SubmittingUrl, insertDocument, updateLastdocversion } from '@/api'
import { mapGetters } from 'vuex'
import { success, alert } from '@/plugins/notification'
import Loader from '@/plugins/loader'

export default {
  components: { modal },
  props: {
    folder: {
      type: Object,
      default: () => ({})
    },
    is_revision: {
      type: Boolean,
      default: false
    },
    open: {
      type: Boolean,
      default: false
    },
    parentFolder: {
      type: Object,
      default: () => ({})
    },
    // will get all the documents  details in given root directory
    allDocuments: {
      type: Array,
      default: () => ([])
    },
    buttonDisabled: {
      type: Boolean,
      default: false
    }

  },
  data () {
    return {
      fileDescription: '',
      fileData: null,
      fileBase64: null,
      fileName: null,
      fileExtension: null,
      fileType: null,
      fileSize: null,
      previewPopover: false,
      openWarning: false,
      duplicateName: false,
      window: window
    }
  },
  computed: {
    ...mapGetters(['openTenantId']),
    compId () {
      // component unique id
      return this._uid
    },
    // this is checking whether any '.' character is included with the file name
    validateImageName () {
      const regex = /^[^.]*$/
      if (regex.test(this.fileName)) {
        return false
      } else {
        return true
      }
    }
  },
  methods: {
    reset () {
      this.fileData = null
      this.fileBase64 = null
      this.fileName = null
      this.fileExtension = null
      this.fileType = null
      this.fileSize = null
      this.$refs.inputfile.value = ''
      this.duplicateName = false
    },
    closePopover () {
      this.reset()
      this.previewPopover = false
    },
    onChange (e) {
      const file = e.target.files[0]
      if ((this.folder?.blob_key) && file.name?.split('.').pop() !== this.folder?.blob_key?.split('.')[1]) {
        this.openWarning = true
        return
      }
      // this is to  separate file name and extension later while uploading the file name file name and extension will concatenate
      this.fileName = file?.name?.split('.')?.shift()
      this.fileExtension = file?.name?.split('.')?.pop()
      this.fileType = file.type
      this.fileSize = file.size
      this.fileData = file
      const fileReader = new FileReader()
      fileReader.readAsDataURL(file)
      fileReader.onload = (e) => {
        this.fileBase64 = e.target.result
        this.previewPopover = true
      }
    },
    async uploadImage () {
      this.buttonDisabled = true
      // these two conditions are used to validate file names
      if (this.validateImageName) {
        alert('Dots and Brackets are not allowed in the file name.')
        this.buttonDisabled = false
        return
      }
      if (this.checkDuplicateNames()) {
        this.buttonDisabled = false
        return
      }
      if (this.fileName === '') {
        this.buttonDisabled = false
        alert('Please Enter file name')
        return
      }
      const loader = new Loader()
      try {
        loader.show()
        const { url } = await generateS3SubmittingUrl({
          tenantId: this.openTenantId,
          // this.parentFolder?.doc_name ?? this.folder?.doc_name this codition is to
          feature: (this.is_revision ? this.parentFolder?.doc_name ?? this.folder?.doc_name : this.folder?.doc_name).replace(/\s/g, '').split('.')[0].toLowerCase(),
          featureId: (this.is_revision ? this.folder.doc_name : this.fileName).replace(/\s/g, '').split('.')[0].toLowerCase(),
          fileName: this.fileName + '.' + this.fileExtension
        })
        await fetch(url, {
          method: 'PUT',
          body: this.fileData,
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        const blobkey = url.split('?')[0].split('.com/').pop()
        const decodedBlobkey = decodeURIComponent(blobkey)
        // if the document in check out  state then need to upload only one revision ,means only update the blobkey instead of inserting
        if (this.is_revision) {
        // checking whether the last version is in check in state or check out  state
          if (this.folder?.associated_versions?.at(-1)?.state === 3) {
            await updateLastdocversion(decodedBlobkey, this.folder.associated_versions.at(-1).id)
            this.$emit('uploaded')
            loader.hide()
            success('Document uploaded successfully')
            this.closePopover()
            return
          }
        }
        await insertDocument(
          decodedBlobkey,
          this.fileDescription,
          this.fileName + '.' + this.fileExtension,
          this.fileSize,
          null,
          false,
          this.is_revision ? this.folder.parent_id : this.folder.id,
          this.is_revision ? this.folder.id : null,
          this.fileExtension
        )
        this.$emit('uploaded')
        loader.hide()
        success('Document uploaded successfully')
        this.buttonDisabled = false
        this.closePopover()
      } catch (error) {
        console.log(error)
        loader.hide()
        this.buttonDisabled = false
        alert('Something went wrong')
      }
    },
    // the function checks any dupliaction in name
    checkDuplicateNames () {
      for (let i = 0; i < this.allDocuments.length; i++) {
        this.duplicateName = false
        if (this.allDocuments[i].doc_name === this.fileName + '.' + this.fileExtension) {
          alert('Please chanage the file name to aviod duplication')
          this.duplicateName = true
          return true
        }
      }
    },
    keyPress (e) {
      const activeElement = this.window.document.activeElement.tagName.toLowerCase()
      const activeElementCheck = ['input', 'select', 'textarea'].includes(activeElement)
      if (e instanceof KeyboardEvent && e.code === 'Enter' && activeElement !== 'button') {
        this.window.document.activeElement.blur()
      }
      if ((!this.open && !this.previewPopover) || this.buttonDisabled || activeElementCheck) return
      if (e instanceof KeyboardEvent && e.code === 'Escape') {
        this.closePopover()
      } else if (e instanceof KeyboardEvent && e.code === 'Enter') {
        this.uploadImage()
      }
    }
  },
  mounted () {
    this.duplicateName = false
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
  }
}

</script>

<style lang="scss" scoped >
.upload-document {
  &-input {
    // label {
    //   font-size: 1em;
    //   padding: 0.35em 1.2em;
    //   border-radius: 0.3em;
    //   color: var(--black);
    //   font-weight: 500;
    //   border: none;
    //   box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
    //   background-color: var(--brand-color);
    // }
    input {
      display: none;
    }
  }
  .file-preview {
    min-width: 300px;
    .file-preview-image {
      width: 300px;
      height: 300px;
      img {
        width: 100%;
        height: 100%;
        -o-object-fit: cover;
        object-fit: contain;
        -o-object-position: center;
        object-position: center;
        background: #dbdbdb;
        border: 1px solid #cfcfcf;
        border-radius: 10px;
      }
    }
  }
}
.border-red{
  border-color: red;
}
.border-red:focus{
  border-color: red;
  box-shadow: none;
}
</style>
