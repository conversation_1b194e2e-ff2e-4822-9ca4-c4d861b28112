.input-group {
  color: var(--text-color);
  input, select ,textarea {
    border: 1px solid #3B3B3B77;
    display: block;
    width: 100%;
    border-radius: 0.285em;
    font-size: 1em;
    padding: 0.85em;
    background-color: transparent;
    &::placeholder {
      color: var(--text-color);
      opacity: 0.5;
    }
    &:focus {
      box-shadow: 0 0 0 1px var(--brand-color-1);
    }
  }
  label {
    display: block;
    width: 100%;
    font-size: 1.15em;
    margin-bottom: 0.36em;
  }
  &.imp {
    label {
      &::after {
        content: '*';
        color: var(--brand-color-1);
        font-size: 1.2em;
        margin-left: 0.1em;
      }
    }
  }
  &.search {
    position: relative;
    input, select ,textarea {
      padding: 0.45em 0.85em;
      padding-right: 2em;
    }
    &::after {
      content: '';
      display: block;
      height: 1em;
      width: 1em;
      background-image: url('~@/assets/images/search-icon.svg');
      background-repeat: no-repeat;
      background-size: contain;
      position: absolute;
      top: 0.55em;
      right: 0.45em;
    }
  }
}
.data-error {
  input, select ,textarea {
    border-color: var(--alert);
  }
  label {
    color: var(--alert);
  }
}
.input-group input[type='checkbox']:focus {
  box-shadow: none;
}
