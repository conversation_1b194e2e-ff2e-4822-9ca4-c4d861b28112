<template>
  <div class="part-id-preview" :class="{ disabled: disabled }">
    <div class="flex space-between v-center">
      <div>
        <h2 class="title">{{ title }}</h2>
      </div>
      <div v-if="showToggle" class="flex v-center gap-1">
            <label class="switch">
            <input type="checkbox" @click="togglePartIdGeneration" :value="!disabled">
            <span class="slider round"></span>
            </label>
            </div>
    </div>

    <div class="preview-box" :class="disabled ? 'disabled--click' : ''">
      <span v-for="(part, index) in previewPartId" :key="'part-' + index">
        <span v-if="part.clickable" class="selectable-part" @click="openDropdown(part)">{{
          part.displayValue
        }}</span>
        <span v-else>{{ part.displayValue}}</span></span
      >
    </div>
    <div v-if="openDropdownObject.open" :class="disabled ? 'disabled--click' : ''">
      <label>{{ openDropdownObject.type }}</label>
      <custom-dropdown
        @toggle="searchText = ''"
        :search-text="searchText"
        :list="openDropdownObject.list"
        @select="(event) => selectFromList(event)"
        :selected="selectedValues[openDropdownObject.id]"
      >
        <div slot="before-items" class="p-2 input-group xs">
          <input placeholder="Search" type="text" v-model="searchText" />
        </div>
      </custom-dropdown>
    </div>
  </div>
</template>

<script>
import customDropdown from './customDropdown.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'PartIdPreview',
  components: {
    customDropdown
  },
  props: {
    components: {
      type: Array,
      required: true,
      default: () => []
    },
    title: {
      type: String,
      required: true,
      default: 'Part ID Preview'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showToggle: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      values: [],
      tagMap: {},
      previewPartId: '',
      selectedValues: {},
      searchText: '',
      currentDate: '',
      currentMonth: '',
      currentYear: '',
      openDropdownObject: {
        open: false,
        id: '',
        type: '',
        list: []
      }
    }
  },
  computed: {
    ...mapGetters(['user'])
  },
  mounted () {
    const today = new Date()
    this.currentDate = today.getDate().toString().padStart(2, '0')
    this.currentMonth = (today.getMonth() + 1).toString().padStart(2, '0')
    this.currentYear = today.getFullYear()
    this.initPreview()
  },
  methods: {
    togglePartIdGeneration (event) {
      event.stopPropagation()
      this.$emit('togglePartIdGeneration')
    },
    openDropdown (part) {
      if (this.openDropdownObject.id === part.id) {
        this.openDropdownObject = {
          open: false,
          id: '',
          type: '',
          list: []
        }
        return
      }
      this.openDropdownObject.open = true
      this.openDropdownObject.id = part.id
      this.openDropdownObject.type = part.caption
      this.openDropdownObject.list = part.list
      if (part.caption === 'Tags') {
        this.openDropdownObject.tagType = part.tagType
        this.openDropdownObject.suffix = part.suffix
      }
    },
    initPreview () {
      this.values = []
      this.previewPartId = this.components.map((comp) => {
        const displayValue = this.getComponentDisplayValue(comp)
        if (comp.component.caption === 'Tags') {
          if (!this.tagMap[comp.tag_type_id]) {
            this.tagMap[comp.tag_type_id] = {}
            for (const tag of comp.tag_type.tags) {
              this.tagMap[comp.tag_type_id][tag.id] = tag
            }
          }
        }
        if (comp.component.caption === 'Config List' || comp.component.caption === 'Tags') {
          this.values.push({
            sequence: comp.sequence_order,
            value: this.selectedValues[comp.id]?.id ?? null,
            delimiter: comp.delimiter_after ?? null,
            caption: comp.component.caption
          })
        } else {
          this.values.push({
            sequence: comp.sequence_order,
            value: displayValue,
            delimiter: comp.delimiter_after ?? null,
            caption: comp.component.caption
          })
          this.$emit('update', this.values)
        }
        const returnList = {
          displayValue: `${displayValue}${
            comp.delimiter_after ?? ''
          }`,
          clickable:
            comp.component.caption === 'Config List' ||
            comp.component.caption === 'Tags',
          caption: comp.component.caption,
          id: comp.id,
          list: []
        }
        if (comp.component.caption === 'Config List') {
          returnList.list = comp.core_custom_list?.custom_list_values
        } else if (comp.component.caption === 'Tags') {
          returnList.list = comp.tag_type?.tags
          returnList.tagType = comp.tag_type_id
          returnList.suffix = comp.suffix
        }
        return returnList
      })
    },
    selectFromList (selectedItem) {
      if (this.openDropdownObject.type === 'Config List') {
        this.selectedValues[this.openDropdownObject.id] = selectedItem
      } else if (this.openDropdownObject.type === 'Tags') {
        let name = ''
        const getTagCaption = (tag) => {
          name += tag.name
          if (tag.parent_id) {
            name += this.openDropdownObject.suffix
            getTagCaption(this.tagMap[this.openDropdownObject.tagType][tag.parent_id])
          }
        }
        getTagCaption(selectedItem)
        this.selectedValues[this.openDropdownObject.id] = { id: selectedItem.id, name }
      }
      this.initPreview()
    },
    getComponentDisplayValue (comp) {
      switch (comp.component.caption) {
      case 'Text':
        return comp.default_value
      case 'Number':
        return comp.default_value
      case 'User Initials':
        return (
            this.user.first_name?.charAt(0).toUpperCase() +
            this.user.last_name?.charAt(0).toUpperCase()
        )
      case 'Auto Sequence':
        return '###'
      case 'UUID':
        return 'xxxx-xxxx'
      case 'Year':
        return this.currentYear
      case 'Month':
        return this.currentMonth
      case 'Date':
        return this.currentDate
      case 'Tags':
        return this.selectedValues[comp.id]?.name ?? '[Tag_Select]'
      case 'Config List':
        return this.selectedValues[comp.id]?.name ?? '[Custom_List_Select]'
      default:
        return '[value]'
      }
    }
  }
}
</script>

<style lang="scss">
  .disabled {
    opacity: 0.5;
    background-color: rgba(0, 0, 0, 0.4);
    &--click {
      pointer-events: none;
    }
  }
.part-id-preview {
  .switch {
  position: relative;
  display: inline-block;
  width: 49px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--side-bar-color);
  -webkit-transition: .4s;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: .4s;
  transition: .4s;
}

input:checked+.slider {
background-color: var(--brand-color);
}

input:focus+.slider {
  box-shadow: 0 0 1px #2196F3;
}
input:checked+.slider:before {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
}
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}
  padding: 1.5rem;
  background-color: #fff;
  border: 1px solid rgba(59, 59, 59, 0.4666666667);
  border-radius: 0.285em;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  .title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
  }

  .preview-box {
    background-color: var(--bg-color);
    padding: 0.75rem 1rem;
    font-family: monospace;
    border-radius: 0.285em;
    border: 1px solid rgba(59, 59, 59, 0.4666666667);
    font-size: 0.95rem;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.03);
    .selectable-part {
      cursor: pointer;
      color: green;
      text-decoration: underline;
      &:hover {
        color: darken(green, 10%);
      }
    }
  }

  .breakdown {
    .subtitle {
      font-size: 1.125rem;
      font-weight: 600;
      color: #2d3748;
    }

    .components-row {
      display: flex;
      flex-wrap: wrap;
      gap: 0.75rem;
      align-items: center;
      margin-top: 0.5rem;

      .component-box {
        background-color: var(--bg-color);
        border: 1px solid rgba(59, 59, 59, 0.4666666667);
        border-radius: 0.285em;
        padding: 0.75rem 1.25rem;
        text-align: center;
        min-width: 100px;
        transition: background-color 0.2s ease;
        cursor: default;

        &:hover {
          background-color: var(--brand-color);
        }

        .component-caption {
          font-size: 0.7rem;
          font-weight: 600;
          text-transform: uppercase;
          margin-bottom: 0.25rem;
        }

        .component-value {
          font-size: 0.95rem;
          font-family: monospace;
          white-space: nowrap;
          text-overflow: ellipsis;
          &-selector {
            width: 20rem;
          }
        }
      }

      .delimiter {
        font-size: 1.5rem;
        font-weight: bold;
        color: #718096;
      }
    }
  }
}
</style>
