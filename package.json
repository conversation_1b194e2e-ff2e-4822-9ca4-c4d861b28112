{"name": "dtx", "version": "1.0.25", "private": true, "scripts": {"serve": "vue-cli-service serve", "dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-scroller": "^2.0.10", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-stencil": "^2.1.5", "@antv/x6-plugin-transform": "^2.1.8", "@apollo/client": "^3.7.3", "@sentry/vue": "^7.98.0", "apollo-cache-inmemory": "^1.6.6", "apollo-client": "^2.6.10", "apollo-link-http": "^1.5.17", "core-js": "^3.6.5", "dhtmlx-gantt": "^8.0.1", "fabric": "^5.0.0", "graphql": "^15.8.0", "graphql-tag": "^2.12.6", "jspdf": "^2.5.1", "jwt-decode": "^3.1.2", "pdfjs-dist": "^2.5.207", "subscriptions-transport-ws": "^0.11.0", "vue": "^2.6.11", "vue-apexcharts": "^1.6.2", "vue-router": "^3.2.0", "vuex": "^3.4.0", "worker-loader": "^2.0.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-plugin-eslint": "~4.5.15", "@vue/cli-plugin-router": "~4.5.15", "@vue/cli-plugin-vuex": "~4.5.15", "@vue/cli-service": "~4.5.15", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "sass": "^1.26.5", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}}