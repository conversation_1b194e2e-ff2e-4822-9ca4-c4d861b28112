<template>
    <div class="dropdown" @mouseleave="disableDropDown">
      <button :style="dropdownToggleStyle" :class="[assStyleClass, selectedOrNot ]" @click="showDropdown = !showDropdown">
      <div class="dropdown-label" v-overflow-tooltip>{{selectedOption?.name || selectedOption?.company_name || label }} </div>
      </button>
      <div class="dropdown-content-single" v-if="showDropdown && !disabled">
        <input type="text" class="dropdown-content-search" placeholder="Search here..." v-model="searchkeyword">
        <div  v-if="tenantDropdown">
          <label v-for="(tenant, index) in filterTenant"  style="text-align: center" class="option-label" :key="index" v-overflow-tooltip
          @click="() => selectOption({company_name: tenant.company_name, id: tenant.id, collaborator: false, tenant_type: tenant.tenant_type })">{{ tenant.company_name }}</label>
          <h4 style="text-align: center"  v-if="tenantDropdown && filteredParentTenants.length > 0 ">Parent Tenants</h4>
        <label  v-for="(parent, index) in filteredParentTenants"  :key="index"
        style="text-align: center"
        class="option-label"
          v-overflow-tooltip
          @click="() => selectOption({company_name: parent.source_tenant.company_name, id: parent.source_tenant.id, collaborator: true, tenant_type: parent.source_tenant.tenant_type })">
           {{ parent.source_tenant.company_name}}</label>
        <h4 style="text-align: center" v-if="tenantDropdown && filteredChildTenants.length > 0">Child Tenants</h4>
        <div :class="{ 'disabled': disableChildTenant}">
          <label
          v-for="(childTenant, index) in filteredChildTenants"
          :key="index"
          style="text-align: center"
          class="option-label"
          v-overflow-tooltip
          @click="selectOption({company_name: childTenant.target_tenant.company_name, id:childTenant.target_tenant.id, childTenant: true })"
        >
          {{ childTenant.target_tenant.company_name}}
        </label>
        </div>
        </div>
        <div v-if="!tenantDropdown">
          <label
          v-for="(option, index) in filteredOptions"
          :key="index"
          class="option-label"
          v-overflow-tooltip
          :class="{ 'selected-sng': option?.id === selectedOption?.id }"
          @click="selectOption(option)"
        >
          {{ option.name }}
        </label>
        </div>
      </div>
    </div>
  </template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'singleSelectDropdown',
  props: {
    clear: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    options: Array,
    label: {
      type: String,
      default: 'Select an option'
    },
    current: {
      type: Object,
      default: () => {}
    },
    filterTenant: Array,
    tenantDropdown: {
      type: Boolean,
      default: false
    },
    componentfor: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      selectedOption: this.current,
      showDropdown: false,
      searchkeyword: null,
      disableChildTenant: true
    }
  },
  computed: {
    ...mapGetters([
      'parentTenantsList'
    ]),
    dropdownToggleStyle () {
      if (this.label === 'Tags') {
        return {
          height: '34px'
        }
      }
      return {
        height: '100%'
      }
    },
    filteredOptions () {
      if (this.searchkeyword) {
        return this.options.filter((element) => element?.name?.toLowerCase()?.match(new RegExp(this.searchkeyword?.toLowerCase())))
      } else {
        return this.options
      }
    },
    filteredChildTenants () {
      if (this.searchkeyword) {
        return this.options.filter((element) => element?.target_tenant.company_name?.toLowerCase()?.match(new RegExp(this.searchkeyword?.toLowerCase())))
      } else {
        return this.options
      }
    },
    filteredParentTenants () {
      if (this.searchkeyword) {
        return this.parentTenantsList.filter((element) => element?.source_tenant.company_name?.toLowerCase()?.match(new RegExp(this.searchkeyword?.toLowerCase())))
      } else {
        return this.parentTenantsList
      }
    },
    assStyleClass () {
      switch (this.componentfor) {
      case 'materialFilter':
        return 'dropdown-toggle-filter'
      default :
        return 'dropdown-toggle'
      }
    },
    selectedOrNot () {
      if (this.selectedOption?.id > -1 && this.componentfor === 'materialFilter') {
        return 'selectedOne'
      }
      return null
    }
  },
  methods: {
    disableDropDown () {
      this.showDropdown = false
      this.searchkeyword = ''
    },
    selectOption (optionValue) {
      this.selectedOption = optionValue
      this.searchkeyword = ''
      this.$emit('selected', this.selectedOption)
      this.disableDropDown()
    },
    changeSearchKeyword () {
      // if (this.searchkeyword) {
      //   this.filteredOptions = this.options.filter((element) => element?.name?.toLowerCase()?.match(new RegExp(this.searchkeyword?.toLowerCase())))
      // } else {
      //   this.filteredOptions = this.options
      // }
    }

  },
  watch: {
    clear () {
      if (this.clear === true) {
        this.searchkeyword = null
        this.selectedOption = null
        // this.current = null
      }
    },
    searchkeyword () {
      this.changeSearchKeyword()
    },
    current () {
      this.selectedOption = this.current
    }
  }
}
</script>
  <style lang="scss" scoped>
  /* Add your CSS styles here */
  .dropdown {
    position: relative;
    display: inline-block;
    &-label{
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    }
  }

  .dropdown-toggle {
    background-color: var(--brand-light-color);
    border: 1px solid var(--brand-color);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 6px 12px;
    cursor: pointer;
    width: 170px;
    border-radius: 0.3em;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: 100%;
  }

  .dropdown-content-single {
    position: absolute;
    background-color: #fff;
    box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
    z-index: 20;
    width: 100%;
    max-height: 240px;
    overflow: scroll;
    border-top: none; /* Remove top border to improve appearance */
  }
  .option-label:hover{
    background-color: var(--brand-light-color);
  }
  .disabled {
  opacity: 0.5; /* Or any other style you want to apply */
  pointer-events: none; /* Disable pointer events to prevent interaction */
}
  .option-label {
    width: 100%;
    display: block;
    margin-bottom: 5px;
    padding: 3px ;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 1rem;
    overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
  }
.selected-sng{
  color: black;
  background-color: var(--brand-color);
}
  .option-label.selected {
    background-color: var(--brand-color);
    color: #fff;
  }
  .dropdown-content-search{
    margin-block: 5px;
    padding: 5px 10px 5px  10px;
    border-radius: 5px;
    width: 100%;
    border: 1px solid  #888;
  }
  .dropdown-content-search::placeholder {
    color: #888;
    font-style: italic;
    }
    .dropdown-toggle-filter {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 170px;
    border-radius: 0.3em;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    border: 1px solid #3B3B3B77;
    border-radius: 0.285em;
    font-size: 1em;
    padding: 0.85em;
    background-color: transparent;
  }
.selectedOne{
  background-color: var(--brand-light-color) !important;
}
    </style>
