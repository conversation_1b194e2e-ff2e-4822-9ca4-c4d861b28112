<template>
  <div class="add-bom-from-inherit">
    <select-project-bom @selectBom="selectBom" />
    <div class="input-group s">
      <div class="label">BOM Name</div>
      <input
        type="text"
        placeholder="BOM Name"
        v-model="newBomName"
      />
    </div>
    <div class="action v-center flex-end m">
      <button
        class="btn btn-black mt-3"
        @click="$emit('close')"
      >
        Close
      </button>
      <button
        class="btn btn-primary mt-3 ml-3"
        :disabled="!selectedBom.id || disableInheritBomButton"
        @click="inheritBom"
      >
        Inherit
      </button>
    </div>
  </div>
</template>

<script>
import selectProjectBom from './selectProjectBom.vue'
import { GetBomForCopyAndInherit, CreateProjectBom } from '@/api'
import { alert, success } from '@/plugins/notification'
export default {
  components: { selectProjectBom },
  name: 'AddBomFromInherit',
  data () {
    return {
      selectedBom: {},
      newBomName: '',
      disableInheritBomButton: false
    }
  },
  methods: {
    selectBom (bom) {
      this.selectedBom = bom
      this.newBomName = bom.id ? bom.name + ' (Inherit)' : ''
    },
    createNewBom (bomItems, stdBomFormPayoad, templateVersionId) {
      CreateProjectBom(
        this.newBomName,
        bomItems,
        this.selectedBom.id,
        this.selectedBom.versionId,
        null,
        stdBomFormPayoad,
        templateVersionId

      ).then((res) => {
        if (res.insert_bom?.message === 'Bom created Successfully!') {
          success('BOM Copied Successfully')
          this.$emit('onSave')
        } else {
          alert(res.insert_bom?.message ?? 'Something went wrong')
        }
      }).catch((e) => {
        if (e.message.includes('Bom name already exists')) {
          alert('BOM Name already exists')
        } else {
          alert(e?.message ?? 'Something went wrong')
        }
      }).finally(() => {
        this.disableInheritBomButton = false
      })
    },
    inheritBom () {
      this.disableInheritBomButton = true
      if (!this.selectedBom.id) {
        this.disableInheritBomButton = false
        return
      }
      if (!this.newBomName) {
        alert('Please enter BOM name')
        this.disableInheritBomButton = false
        return
      }
      GetBomForCopyAndInherit(this.selectedBom.id, true).then(res => {
        const bom = res.core_bom_by_pk || {}
        const stdBomFormPayoad = []
        for (const fieldId in bom.bom_versions?.[0]?.custom_fields?.values) {
          stdBomFormPayoad.push({
            field_id: fieldId,
            value: bom.bom_versions?.[0]?.custom_fields?.values[fieldId]
          })
        }
        const templateVersionId = bom.bom_versions?.[0]?.custom_fields?.template_version_id
        const bomItems = bom.bom_versions?.[0]?.bom_items?.map((item) => {
          return {
            material_id: item.material_id,
            quantity: item.quantity,
            total_price: item.total_price,
            unit_size: +item.unit_size,
            assoc_bom_version_id: item.associated_bom_version?.id ?? null,
            material_group: item.material_group,
            material_unit_cost: item.material_unit_cost,
            material_unit_sale_price: item.material_unit_sale_price
          }
        }) || []
        this.createNewBom(bomItems, stdBomFormPayoad, templateVersionId)
      }).catch(() => {
        alert('Something went wrong')
        this.disableInheritBomButton = false
      })
    }
  },
  created () {
  }
}
</script>

<style lang="scss" scoped>
.input-group {
  // background: rgba(var(--brand-rgb), 0.3);
  margin: 10px -10px 0 -10px;
  padding: 20px;
  padding-top: 0;
}
.action {
  // background: rgba(var(--brand-rgb), 0.3);
  margin: 0px -10px -10px -10px;
  padding: 20px;
  padding-top: 0;
}
</style>
