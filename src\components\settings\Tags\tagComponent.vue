<template>
<div class="tag_container" :id='"tagIndex"+tagIndex' >
  <div v-if="items.length" >
    <div v-for="(item, index) in items" :key="item.id" :id="'item'+index" :class="{ 'selected': item.id === parentIndex?.id }" class="tag-component v-center" @click="handleClick({ id:item.id, index , tagIndex, name:item.name})">
      <div class="tag-component__name v-center s elipsis-text" >
        {{ item.name }}
      </div>
      <div class="toggle-button center">
        <img src="@/assets/images/down-arrow-icon.svg" alt=""/>
      </div>
    </div>
  </div>
  <div v-else class="no-tags v-center " :style="dynamicMargin">
    No Tag Found
  </div>
</div>
</template>

<script>
export default {
  name: 'tagComponent',
  data () {
    return {
      isVisible: false,
      inActive: null,
      currentMargin: null
    }
  },
  props: {
    items: {
      type: Array,
      required: true
    },
    parentIndex: {
      type: Object,
      required: true
    },
    tagIndex: {
      type: Number,
      required: true
    },
    tagStyleData: {
      type: Object,
      required: true
    }
  },
  methods: {
    handleClick (data) {
      data.prevMargin = this.currentMargin
      if (this.inActive !== data.id) {
        this.inActive = data.id
        this.$emit('tag-selected', data)
        this.$emit('name', data)
      }
    },
    alignChildTags () {
      const div = document.getElementById('item0')
      const styles = window.getComputedStyle(div)
      // Extract the values for height, padding, and border
      const height = parseFloat(styles.height) // Height in pixels
      const paddingTop = parseFloat(styles.paddingTop)
      const paddingBottom = parseFloat(styles.paddingBottom)
      const actualHeight = height + paddingTop + paddingBottom
      const distance = (this.tagStyleData?.prevTagIndex) * (actualHeight) + this.tagStyleData?.prevMargin
      this.currentMargin = distance
      if (this.tagIndex !== 0) { document.getElementById('tagIndex' + (this.tagIndex)).style.marginTop = `${distance}px` }
    },
    updateMargin () {
      const children = document.getElementById('tagIndex0').parentElement.children || []
      const zoomLevel = window.innerWidth / window.outerWidth
      for (let index = 0; index < children.length; index++) {
        const element = children[index]
        element.style.marginTop = element.style.marginTop * parseFloat(zoomLevel)
      }
    }
  },
  computed: {
    dynamicMargin () {
      return {
        margin: this.currentMargin
      }
    }
  },
  mounted () {
    window.addEventListener('resize', this.updateMargin)
    this.alignChildTags()
  },
  destroyed () {
    window.removeEventListener('resize', this.updateMargin)
  }
}
</script>

<style lang="scss" scoped >
.tag-component {
  width: 100%;
  border: 1px solid var(--brand-color);
  background-color: rgba(var(--brand-rgb), 0.2);
  padding-left: 6px;
  cursor: pointer;
  &__name {
    height: 30px;
    width: 100%;
  }
  & .toggle-button {
    height: 30px;
    width: 30px;
    background-color: rgba(var(--brand-rgb), 1);
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.5;
    & img {
      height: 16px;
      width: 16px;
      transform: rotate(-90deg);
    }
  }
}
.selected {
  background-color: #fdd8a3;
}
.tag_container{
  min-width: 230px
}
.no-tags{
  font-size: 10px;
  font-weight: 400;
  border: 1px solid var(--black);
  background-color:#FFF;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
