@import './var.scss';
@import './background.scss';
@import './text.scss';
@import './flex-box.scss';
@import './position.scss';
@import './spacing.scss';
@import './input.scss';
@import './button.scss';
@import './utils.scss';
@import './animation.scss';
@import './table.scss';
@import './copyTable.scss';
@import './bom-detail.scss';
@import url('./copy-bom-detail.scss');
@import './overflowTooltip.scss';
@import './dialog.scss';
@import './form.scss';
@import url('https://fonts.googleapis.com/css2?family=Montserrat&family=Poppins:wght@300;400;500;600;700&display=swap');

html, body {
  height: 100%;
  width: 100%;
  padding: 0;
  margin: 0;
  font-family: 'Montserrat', sans-serif;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
}

h1, h2, h3, h4, h5, h6, p, li,ul, ol, tr,td,th, a {
  padding: 0;
  margin: 0;
  text-decoration: none;
  list-style: none;
  line-height: 1;
}

* {
  box-sizing: border-box;
}

*:focus {
  outline: none;
}

select {
	word-wrap: normal
}

/* width */
::-webkit-scrollbar {
  width: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  background: transparent; 
}
 
/* Handle */
::-webkit-scrollbar-thumb {
  background: var(--brand-color-1); 
	height: 1px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555; 
}

input {
	-moz-appearance: textfield;
}

select {
	-moz-appearance: none;
	text-indent: 0.01px;
	text-overflow: '';
}

textarea, input { outline: none; }
