<template>
  <div class="create-bom">
    <button
      class="btn btn-black mr-2"
      :disabled="checkDisabled"
      @click="createBom"
    >
      Create BOM
    </button>
    <modal
      :open="createBomDialog"
      @close="createBomDialog = false"
      :closeOnOutsideClick="true"
      title="Create BOM"
    >
      <div class="create-bom-modal">
        <!-- <div class="center">
          <img src="~@/assets/images/icons/import-icon.svg" alt="" />
          <div>Import .CSV</div>
        </div> -->
        <div class="center"  v-if="bomlistLength" @click="addBomFromInherit">
          <img src="~@/assets/images/icons/inherit-icon.svg" alt="" />
          <div>Inherit From</div>
        </div>
        <div class="center" v-if="bomlistLength" @click="addBomFromCopy">
          <img src="~@/assets/images/icons/copy-icon.svg" alt="" />
          <div>Copy From</div>
        </div>
        <div class="center" @click="addBomFromScratch">
          <img src="~@/assets/images/icons/add-icon.svg" alt="" />
          <div>Add</div>
        </div>
      </div>
    </modal>
    <modal
      :open="addBomFromCopyDialog"
      @close="addBomFromCopyDialog = false"
      :closeOnOutsideClick="true"
      title="Copy BOM"
    >
      <add-project-bom-from-copy
        v-if="addBomFromCopyDialog && isOnProjectLevel"
        @close="addBomFromCopyDialog = false"
        @onSave="updatebomlist"
      />
      <add-bom-from-copy
        v-if="addBomFromCopyDialog && !isOnProjectLevel"
        :productCode="productCode"
        @close="addBomFromCopyDialog = false"
        @onSave="updatebomlist"
      />
    </modal>
    <modal
      :open="addBomFromInheritDialog"
      @close="addBomFromInheritDialog = false"
      :closeOnOutsideClick="true"
      title="Inherit BOM"
    >
      <add-project-bom-from-inherit
        v-if="addBomFromInheritDialog && isOnProjectLevel"
        @close="[addBomFromCopyDialog = false,addBomFromInheritDialog=false]"
        @onSave="updatebomlist"
      />
      <add-bom-from-inherit
        v-if="addBomFromInheritDialog && !isOnProjectLevel"
        :productCode="productCode"
        @close="addBomFromInheritDialog = false"
        @onSave="updatebomlist"
      />
    </modal>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import modal from '../../common/modal.vue'
import AddBomFromCopy from '../product/addBomFromCopy.vue'
import AddBomFromInherit from '../product/addBomFromInherit.vue'
import AddProjectBomFromCopy from '../project/addProjectBomFromCopy.vue'
import AddProjectBomFromInherit from '../project/addProjectBomFromInherit.vue'
export default {
  components: {
    modal,
    AddBomFromCopy,
    AddBomFromInherit,
    AddProjectBomFromCopy,
    AddProjectBomFromInherit
  },
  name: 'CreateBom2',
  props: {
    bomlistLength: {
      type: Number,
      default: 0
    }
  },
  data: () => ({
    createBomDialog: false,
    addBomFromCopyDialog: false,
    addBomFromInheritDialog: false
  }),
  computed: {
    ...mapGetters(['isOnProjectLevel']),
    ...mapGetters('productBom', ['selectedProductCode', 'obsoleteProductCodes']),
    productCode () {
      return this.$route.params.productCode || null
    },
    checkDisabled () {
      if (this.productCodeStatus === 'inactive') {
        return true
      }
      return this.isOnProjectLevel ? false : !this.productCode
    },
    productCodeStatus () {
      return this.obsoleteProductCodes[this.selectedProductCode]
    }
  },
  methods: {
    createBom () {
      this.createBomDialog = true
    },
    addBomFromScratch () {
      this.createBomDialog = false
      if (this.isOnProjectLevel) {
        this.$router.push('/bom/project/bom/new')
        return
      }
      this.$router.push(
        `/bom/product/${this.$route.params.productCode}/bom/new`
      )
    },
    addBomFromCopy () {
      this.createBomDialog = false
      this.addBomFromCopyDialog = true
    },
    addBomFromInherit () {
      this.createBomDialog = false
      this.addBomFromInheritDialog = true
    },
    updatebomlist () {
      this.addBomFromCopyDialog = false
      this.addBomFromInheritDialog = false
      this.$emit('updatebomlist')
    }
  }
}
</script>

<style lang="scss" scoped >
.create-bom {
  &-modal {
    width: 640px;
    padding: 60px 20px;
    margin: -10px;
    display: flex;
    justify-content: space-around;
    & > div {
      width: 160px;
      height: 160px;
      font-size: 20px;
      background: rgba(var(--brand-rgb), 0.3);
      border-radius: 8px;
      flex-direction: column;
      &:hover {
        box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
      }
    }
  }
}
</style>
