const stringMatch = (s1, s2) => {
  return s1.toLocaleLowerCase().includes(s2.toLocaleLowerCase())
}

const isUUID = (value) => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
  return uuidRegex.test(value)
}

export const arraySearch = (
  arr,
  keyword,
  { searchUUID = false, searchPrivateFields = false, fieldsToSearch = [] } = {
    searchUUID: false,
    searchPrivateFields: false,
    fieldsToSearch: []
  }
) => {
  return arr.filter((item) => {
    for (const key in item) {
      if (fieldsToSearch.length > 0 && !fieldsToSearch.includes(key)) {
        continue
      }
      if (!searchPrivateFields && key.startsWith('_')) {
        continue
      }
      if (!searchUUID && isUUID(item[key])) {
        continue
      }
      if (typeof item[key] === 'string' && stringMatch(item[key], keyword)) {
        return true
      }
    }
    return false
  })
}

// user data is comming as array of object inside that object , associtated user  contains the  data of user
export const arraySearchForUser = (arr, keyword) => {
  return arr.filter((item) => {
    for (const key in item.associated_user) {
      if (typeof item.associated_user[key] === 'string' && stringMatch(item.associated_user[key], keyword)) {
        return true
      }
    }
    return false
  })
}
