<template>
  <div>
    <div class="v-center">
        <img
        class="mr-3 pointer"
        src="~@/assets/images/icons/arrow-back.svg"
        width="25px"
        alt=""
        @click="goBack"
      />
      <h3>Go back</h3>
      </div>
  <div class="tenant-invite py-3">
    <h2>Invite your tenant here</h2>
    <div class="tenant-invite--form mt-3 s">
      <h3>Company Admin detail</h3>
      <div class="grid-2">
        <div data-validation="FirstName" class="input-group imp my-2">
          <label>Enter First Name</label>
          <input
            :disabled="showDetails"
            :style="showDetails ? disabledStyle : null"
            v-model="firstName"
            placeholder="Enter your First Name"
            type="text"
          />
        </div>
        <div data-validation="LastName" class="input-group my-2 imp">
          <label>Enter Last Name</label>
          <input
            :disabled="showDetails"
            :style="showDetails ? disabledStyle : null"
            v-model="lastName"
            placeholder="Enter your Last Name"
            type="text"
          />
        </div>
      </div>
      <div data-validation="Email" class="input-group imp my-2">
          <label>Enter Email ID</label>
          <input v-model="email" :disabled="showDetails"
            :style="showDetails ? disabledStyle : null" placeholder="Enter Email ID" type="text" />
        </div>
      <h3 class="my-3">Company detail</h3>
       <div class="grid-2">
        <div data-validation="CompanyGstNumber" class="input-group imp my-2">
          <label>Enter Company GST Number</label>
          <input
            v-model="GSTIN"
            :disabled="showDetails"
            :style="showDetails ? disabledStyle : null"
            v-click-outside="getTenantDetails"
            placeholder="Enter Company GST Number"
            type="text"
          />
        </div>
        <div data-validation="CompanyPanNumber" class="input-group imp my-2">
          <label>Enter Company PAN Number</label>
          <input
            v-model="PAN"
            :disabled="showDetails"
            :style="showDetails ? disabledStyle : null"
            placeholder="Enter Company PAN Number"
            type="text"
          />
        </div>
      </div>
      <div data-validation="CompanyName" class="input-group imp my-2">
        <label>Enter Company Name</label>
        <input
          v-model="companyName"
          :disabled="isCompanyName"
            :style="isCompanyName ? disabledStyle : null"
          placeholder="Enter Company Name"
          type="text"
        />
      </div>
      <div class="grid-2">
        <div data-validation="CompanyEmail" class="input-group imp my-2">
          <label>Enter Email</label>
          <input v-model="companyEmail" placeholder="Enter Email" type="text" />
        </div>
        <div data-validation="CompanyPhoneNumber" class="input-group imp my-2">
          <label>Enter Company Phone Number</label>
          <input
            v-model="companyPhone"
            placeholder="Enter Company Phone Number"
            type="text"
            maxlength="10"
          />
        </div>
      </div>
      <div class="grid-2">
        <div data-validation="Latitude" class="input-group my-2 imp">
          <label>Company Location latitude</label>
      <input
        v-model.number="latitude"
        placeholder="Company Location latitude"
        type="number"
      />
        </div>
        <div data-validation="Longitude" class="input-group my-2 imp">
          <label>Company Location longitude</label>
      <input
        v-model.number="longitude"
        placeholder="Company Location longitude"
        type="number"
      />
        </div>
        <div  class="input-group my-2">
          <label>Company Description</label>
          <textarea
            v-model="description"
            placeholder="Describe about Company."
            type="text"
          />
        </div>
        <div data-validation="Industry" class="input-group my-2 imp">
          <label>Industry vertical</label>
          <select v-model="selectedIndustry">
            <option value="" disabled selected>Select Industry vertical</option>
            <option v-for="item in industryVerticalList" :value="item.id" :key="item.id">{{ item.name }} </option>
            </select>
        </div>
      </div>
      <div class="grid-2">
        <div data-validation="Address" class="input-group my-2">
          <label>Enter URL</label>
          <input
            placeholder="Enter URL "
            type="text"
            :disabled="isUrl"
            :style="isUrl ? disabledStyle : null"
            v-model="companyUrl"
          />
        </div>
        <div v-if="!iscompanyLogo" class="input-group my-2">
          <label>Upload Image</label>
          <input type="file" accept="image/jpeg, image/png, image/jpg" @change="handleFile" ref="fileInput">
        </div>
      </div>
      <div class="flex-end">
        <button @click="resetForm" class="btn btn btn-black-outline mx-3">Reset</button>
        <button @click="uploadAndInvite" class="btn">Invite Tenant</button>
      </div>
    </div>
  </div>
</div>
</template>

<script>
import InviteTenantValidation from '@/helper/formValidation/inviteTenant'
import { InviteTenants, InviteCollaborator, GetAllTenantsListData, GetAllIndustryVertical, generateS3SubmittingUrl, GetAllTenantsListDataForBeaconAdmin, FetchTenantDetailsByGSTIN } from '@/api'
import { alert, success } from '@/plugins/notification'
import Loader from '@/plugins/loader'
import { mapGetters } from 'vuex'

export default {
  components: {},
  name: 'InviteTenant',
  props: {
    collaborator: Boolean
  },
  data () {
    return {
      firstName: '',
      lastName: '',
      email: '',
      companyName: '',
      PAN: '',
      GSTIN: '',
      companyPhone: '',
      companyEmail: '',
      description: null,
      companyLocation: '',
      paymentStatus: true,
      industryVerticalList: [],
      selectedIndustry: null,
      companyUrl: null,
      iscompanyLogo: false,
      latitude: null,
      longitude: null,
      showDetails: false,
      isCompanyName: false,
      isUrl: false,
      disabledStyle: {
        backgroundColor: '#f0f0f0',
        color: '#a0a0a0'
      }
    }
  },
  computed: {
    ...mapGetters(['user', 'isBeaconAdmin', 'openTenantId'])
  },
  methods: {
    async getTenantDetails () {
      if (this.GSTIN.length === 15 && !this.showDetails) {
        await FetchTenantDetailsByGSTIN(this.GSTIN).then(res => {
          if (res.core_tenants) {
            this.showDetails = true
            this.firstName = res.core_tenants[0].tenant_users[0].associated_user.first_name
            this.lastName = res.core_tenants[0].tenant_users[0].associated_user.last_name
            this.email = res.core_tenants[0].tenant_users[0].associated_user.email
            this.companyName = res.core_tenants[0].company_name
            this.PAN = res.core_tenants[0].PAN
            this.companyPhone = res.core_tenants[0].company_phone
            this.companyEmail = res.core_tenants[0].company_email
            this.description = res.core_tenants[0].company_description
            this.companyLocation = res.core_tenants[0].company_location
            const locationArray = this.companyLocation ? this.companyLocation.replace(/[()]/g, '').split(',') : null
            this.latitude = !locationArray ? null : locationArray[0]
            this.longitude = !locationArray ? null : locationArray[1]
            this.selectedIndustry = res.core_tenants[0].industry_vertical_value.id
            this.companyUrl = res.core_tenants[0].company_url
            this.isUrl = !this.isBeaconAdmin ? !!this.companyUrl : false
            this.iscompanyLogo = !this.isBeaconAdmin ? !!res.core_tenants[0].company_logo_blob_key : false
            this.isCompanyName = !this.isBeaconAdmin ? !!this.companyName : false
          }
        })
      }
    },
    resetForm () {
      this.firstName = ''
      this.lastName = ''
      this.email = ''
      this.companyName = ''
      this.PAN = ''
      this.GSTIN = ''
      this.companyPhone = ''
      this.companyEmail = ''
      this.description = null
      this.companyLocation = ''
      this.paymentStatus = true
      this.latitude = null
      this.longitude = null
      this.selectedIndustry = null
      this.companyUrl = null
      this.showDetails = false
      this.isUrl = false
      this.isCompanyName = false
      this.iscompanyLogo = false
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = null
      }
    },
    async tryInviteTenant (body) {
      body.paymentStatus = undefined
      body.latitude = undefined
      body.longitude = undefined
      const loader = new Loader()
      loader.show();
      (this.collaborator ? InviteCollaborator : InviteTenants)(body).then(data => {
        const successResponses = ['The tenant was successfully linked as a collaborative tenant', 'User Created Successfully!', 'Tenant Created Successfully!']
        if (successResponses.includes(data.message)) {
          success(data.message)
          this.resetForm()
          if (!this.isBeaconAdmin) {
            this.getAllTenantList()
          } else {
            GetAllTenantsListDataForBeaconAdmin().then((res) => {
              this.$store.commit('setTenentList', res?.core_tenants)
            })
          }
          this.$router.go(-1)
        } else {
          loader.hide()
          alert(data.message ?? 'There was an error, re-inviting the Tenant.')
        }
      }).catch((err) => {
        loader.hide()
        alert(err?.message ?? 'There was an error, inviting the tenant. Please try again')
      }).finally(() => {
        loader.hide()
      })
    },
    goBack () {
      this.$router.go(-1)
    },
    getAllTenantList () {
      GetAllTenantsListData(this.user.userId, this.user.tenantId).then((res) => {
        this.$store.commit('setChildTenantList', res?.child_tenants)
      })
    },
    handleFile (e) {
      this.selectedFile = e.target.files[0]
    },
    async uploadAndInvite () {
      try {
        const body = {
          firstName: this.firstName,
          lastName: this.lastName,
          email: this.email,
          companyName: this.companyName,
          PAN: this.PAN,
          GSTIN: this.GSTIN,
          companyPhone: this.companyPhone,
          companyEmail: this.companyEmail,
          description: this.description,
          companyLocation: `(${this.latitude}, ${this.longitude})`,
          paymentStatus: this.paymentStatus,
          industryVertical: this.selectedIndustry,
          companyUrl: this.companyUrl,
          latitude: this.latitude,
          longitude: this.longitude
        }
        if (InviteTenantValidation(body)) {
          if (this.selectedFile) {
            const file = this.selectedFile
            const fileReader = new FileReader()
            fileReader.readAsDataURL(file)
            fileReader.onload = async (e) => {
              const { url } = await generateS3SubmittingUrl({
                tenantId: this.openTenantId,
                feature: 'metaData',
                featureId: 0,
                fileName: file.name
              })
              await fetch(url, {
                method: 'PUT',
                body: file,
                headers: {
                  'Content-Type': 'multipart/form-data'
                }
              })
              const blobkey = url.split('?')[0].split('.com/').pop()
              body.companyLogoBlobKey = blobkey
              this.tryInviteTenant(body)
            }
          } else {
            this.tryInviteTenant(body)
          }
        }
      } catch (err) {
        alert('unable to upload and invite')
      }
    },
    listOfIndustryVertical () {
      const name = 'Industry Vertical'
      GetAllIndustryVertical(name).then((res) => {
        this.industryVerticalList = res.core_custom_list[0].custom_list_values
      })
    }
  },
  mounted () {
    // !this.isBeaconAdmin && this.getAllTenantList()
    this.listOfIndustryVertical()
  }
}
</script>

<style lang="scss" scoped>
.tenant-invite {
  max-width: 800px;
  margin: auto;
  & h2, & h3 {
    font-weight: 500;
  }
  & h3 {
    font-size: 16px;
  }
  &--form {
    background: var(--white);
    padding: 20px;
    & > div {
      margin: auto;
    }
  }
}
.input-group {
  input, select ,textarea {
    font-size: 1.11em;
  }
}
</style>
