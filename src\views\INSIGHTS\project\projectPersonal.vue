<template>
    <div class="insight-personal">
        <h2 for="" class="personal-ins-label" v-if="taskData.length">My Tasks</h2>
      <div class="personal-ins-table" v-if="taskData.length">
        <table>
          <thead>
            <tr>
              <th>Sl.No</th>
              <th>Task Name</th>
              <th>Planned Start Date </th>
              <th>Planned End Date</th>
              <th>Progress</th>
              <th>Due</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(task, index) in taskData"
            :key="task.id"
              :class="{
                'personal-ins-table-overdue':task.due,
                'pointer': true

              }"
              @dblclick="openEditForm(task.id, task.project_id)"
            >
              <td> {{index+1}} </td>
              <td> {{task.name}} </td>
              <td> {{task.planned_start_date.split('T')[0]}} </td>
              <td> {{task.planned_end_date.split('T')[0]}} </td>
              <td> {{task.progress}}% </td>
              <td> {{task.due?? ' -- '}} days </td>
              <td> {{task.task_status.name}} </td>
            </tr>
          </tbody>
        </table>
      </div>
      <h2 for="" class="personal-ins-label" v-if="Object.keys(formTemplateMap).length">My Forms</h2>
      <!-- this key is template id  -->
        <div class="personal-ins-table mb-5" v-for="(formData, key) in formTemplateMap" :key="key">
          <p class="mb-2">{{formData[0].template_version.core_form_template.name}}</p>
          <table >
            <thead>
              <tr>
                <th>Sl.No</th>
                <th>{{ templateVisibleFieldsMap[formData[0].template_version.template_id]?.length ?  templateVisibleFieldsMap[formData[0].template_version.template_id][0].caption : '--'}} </th>
                <th>{{templateVisibleFieldsMap[formData[0].template_version.template_id]?.length === 2 ?  templateVisibleFieldsMap[formData[0].template_version.template_id][1].caption : '--'}} </th>
                <th>Created By</th>
                <th>Created At</th>
                <th>Due</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(form, index) in formData" :key="form.id" :class="{
                  'personal-ins-table-overdue':form.due,
                  'pointer': true
                }"
                @dblclick="openProjectForm(key, form.id, formData[0].template_version.core_form_template.name)"
                >
                <td> {{ index + 1 }} </td>
                <td v-if="form.customVisibleField1.tooltip.length" > <span v-tooltip="form.customVisibleField1.tooltip">{{ form.customVisibleField1.value }}</span>  </td>
                <td v-else> {{ form.customVisibleField1.value }} </td>
                <td v-if="form.customVisibleField2.tooltip.length"> <span v-tooltip="form.customVisibleField2.tooltip" > {{ form.customVisibleField2.value }} </span> </td>
                <td v-else> {{ form.customVisibleField2.value }} </td>
                <td> {{ form.created_by_user?.first_name + ' ' + form.created_by_user?.last_name}} </td>
                <td> {{ form?.created_on?.split('T')[0] }} </td>
                <td> {{ (form.due ?? '--') + ' ' + "days"}} </td>
                <td> {{ formStateMap[form.status] }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      <modal
      :open="modalOpen"
      @close="modalOpen=false"
      :closeOnOutsideClick="true"
      title="Task Details"
    >
      <task-edit-form
      v-if="selectedTaskData && modalOpen"
        :task="selectedTaskData"
        :assigneeMap="assigneeMap"
        @close="modalOpen=false"
        @update-and-close="updateAndCloseModal"
      />
    </modal>
     </div>
</template>
<script>
import { mapGetters, mapMutations } from 'vuex'
import Loader from '@/plugins/loader'
import { alert, success } from '@/plugins/notification'
import Config from '@/config'
import {
  getTemplateIdsWithUserId,
  getTaskDatabyuserId,
  getFormDatabyuserId,
  getTaskDatabyTaskId,
  UpdateTasksWithToken,
  insertTasksData,
  deleteChanagesInTimeLine,
  GetUserListByPojIds,
  getCalenderDataByids
} from '@/api'
import Modal from '@/components/common/modal.vue'
import TaskEditForm from '@/components/timeLine/taskEditForm.vue'
// another function is already wrote in insight helper , where it not considering the time
import { getDayDifference } from '@/utils/date'
import setVisibleFieldValues from '@/helper/insights/setVisibleFieldValues'
export default {
  name: 'personalInsights',
  data: function () {
    return {
      fieldValues: [],
      fields: [],
      values: [],
      templateFieldMap: {},
      fieldIds: [],
      taskData: null,
      formStateMap: Config.FORM_STATE,
      formTemplateMap: {},
      modalOpen: false,
      selectedTaskData: {},
      assigneeMap: {},
      templateVisibleFieldsMap: {}
    }
  },
  components: { Modal, TaskEditForm },
  methods: {
    ...mapMutations('timeLine', [
      'addNewToken',
      'clearTokenHashMap',
      'addCalenderHashMap'
    ]),
    getTaskAndFormData () {
      this.taskData = []
      this.formTemplateMap = {}
      const loader = new Loader()
      loader.show()
      const currentDate = new Date()
      const afterDays = new Date(new Date().setDate(new Date().getDate() + 5))
      let token = 'project'
      if (this.collaborator) {
        token = 'tenant'
      }
      getTemplateIdsWithUserId(this.user.userId).then(res => {
        this.value = []
        this.fieldValues = res.template_fields.map(field => {
          if (this.templateVisibleFieldsMap[field.template_version.template_id]) {
            this.templateVisibleFieldsMap[field.template_version.template_id].push({ id: field.field_id, key: field.form_field.key, caption: field.caption })
          } else {
            this.templateVisibleFieldsMap[field.template_version.template_id] = [{ id: field.field_id, key: field.form_field.key, caption: field.caption }]
          }
          this.value.push({ value: field.caption, field_id: field.field_id })
          return field.field_id
        })
        Promise.all([getTaskDatabyuserId(this.user.userId, currentDate, afterDays, token),
          getFormDatabyuserId(afterDays, this.user.userId, this.fieldValues, token)])
          .then(([res, formRes]) => {
            formRes.core_forms.map((form) => {
              setVisibleFieldValues(form, form.template_version.template_id, this.templateVisibleFieldsMap)
              if (currentDate > new Date(form.due_date)) {
                form.due = Math.round(getDayDifference(form.due_date, currentDate))
              }
              if (this.formTemplateMap[form.template_version.template_id]) {
                this.formTemplateMap[form.template_version.template_id].push(form)
              } else {
                this.formTemplateMap[form.template_version.template_id] = [form]
              }
            })
            res.overdue.map((item) => {
              item.due = Math.round(getDayDifference(item.planned_end_date, currentDate))
            })
            this.taskData = [...res.overdue, ...res.running, ...res.upcomming]
            loader.hide()
          })
      })

        .catch(err => {
          loader.hide()
          console.log(err)
          alert('unable to fetch the data')
        })
    },
    async openEditForm (taskId, projectId) {
      const loader = new Loader()
      loader.show()
      // this line is for setting token , this is done like this to make the code similar to product level
      this.addNewToken({ token: localStorage.getItem('dtxProjectToken'), projectId: projectId })

      getTaskDatabyTaskId(taskId, this.getProjectTokens[projectId])
        .then((res) => {
          this.selectedTaskData = res?.core_tasks_by_pk
          this.selectedTaskData.attached_bom = {
            id: this.selectedTaskData?.task_material_associations?.[0]?.id,
            name: this.selectedTaskData?.task_material_associations?.[0]?.target_bom.name,
            associationId: this.selectedTaskData?.task_material_associations?.[0]?.target_bom.id
          }
          loader.hide()
          this.modalOpen = true
        })
        .catch((err) => {
          console.log(err)
          alert('something went wrong')
        })
    },
    async updateAndCloseModal (updatedTask) {
      const loader = new Loader()
      try {
        loader.show()
        updatedTask.core_project = { id: this.sActive }
        const deletedAssignees = []
        const deletedDocs = []
        const newAssigneeArray = []
        const newDocArray = []
        const newTagArray = []
        const newTaskMaterialArray = []
        const {
          id: taskId,
          start_date: plannedStartDate,
          end_date: plannedEndDate,
          task_assignees: taskAssignees,
          core_project: { id: projectId },
          task_docs: taskDocs,
          attached_bom: attachedBom,
          tags,
          duration,
          progress,
          description,
          text,
          type
        } = updatedTask
        // checking  project token is avaialble in project token's Hash map
        if (!this.getProjectTokens[projectId]) {
          // this line is for setting token , this is done like this to make the code similar to product level
          this.addNewToken({ token: localStorage.getItem('dtxProjectToken'), projectId: projectId })
        }
        const deletedOperations = []
        const insertOperations = []
        // getting deleted and added assinee ids
        for (const user of taskAssignees) {
          if (user.status === 'new') {
            newAssigneeArray.push({ task_id: taskId, user_id: user.id })
          } else if (user.status === 'deleted') {
            deletedAssignees.push(user.id)
          }
        }
        if (taskDocs?.length) {
          for (const doc of taskDocs) {
            if (doc.flag === 'deleted') {
              deletedDocs.push(doc.id)
            } else if (doc.flag === 'new') {
              newDocArray.push({ task_id: taskId, document_id: doc.id })
            }
          }
        }
        if (tags?.flat()?.length > 0) {
          for (const tag of tags?.flat()) {
            newTagArray.push({ tag_id: tag.id ?? tag.tag.id, task_id: taskId })
          }
        }
        // attaching a bom to  a  task
        attachedBom && newTaskMaterialArray.push(`{task_id: {_eq: "${taskId}"}}, _set: {bom_id:${attachedBom.id}}`)
        // if (updateObject[projectId]) {
        const updateObject = ({
          where: { id: { _eq: taskId } },
          _set: {
            planned_start_date: plannedStartDate,
            planned_end_date: plannedEndDate,
            duration,
            progress,
            type,
            description,
            name: text
          }
        })
        if (deletedAssignees.length > 0) { deletedOperations.push({ typeName: 'delete_task_assignee', conditions: `{task_id: {_eq: "${taskId}"}, user_id: {_in: [${deletedAssignees}]}}` }) }
        if (deletedDocs.length > 0) {
          deletedOperations.push({
            typeName: 'delete_task_document_association',
            conditions: `{task_id: {_eq: "${taskId}"}, document_id: {_in:${JSON.stringify(deletedDocs)} }}`
          })
        }
        if (newTaskMaterialArray.length > 0) { deletedOperations.push({ typeName: 'update_task_material_association', conditions: (newTaskMaterialArray) }) }
        if (newDocArray?.length > 0) { insertOperations.push({ typeName: 'insert_task_document_association', conditions: (newDocArray) }) }
        if (newTagArray.length > 0) { insertOperations.push({ typeName: 'insert_tag_task', conditions: (newTagArray) }) }
        if (newAssigneeArray.length > 0) { insertOperations.push({ typeName: 'insert_task_assignee', conditions: (newAssigneeArray) }) }
        deletedOperations.push({ typeName: 'delete_tag_task', conditions: `{task_id: {_eq: "${taskId}"}}` })
        await Promise.all([UpdateTasksWithToken(updateObject, this.getProjectTokens[this.isActive]), deleteChanagesInTimeLine(this.generateDynamicDeleteQuery(deletedOperations), this.getProjectTokens[this.isActive])])
        insertOperations.length > 0 && await Promise.all([insertTasksData(this.generateDynamicInsertQuery(insertOperations), this.getProjectTokens[this.isActive])])
        loader.hide()
        this.getTaskAndFormData()
        this.modalOpen = false
        success('task updated successfully')
        this.selectedTaskData = null
      } catch (err) {
        console.log(err)
        loader.hide()
        alert('something went wrong')
      }
    },
    closeEditForm () {
      this.modalOpen = false
    },
    getCalenderData (projectids) {
      getCalenderDataByids(projectids).then((res) => {
        for (const project of res.core_project_calendar) {
          const days = []
          const holidays = []
          for (const workDays of project.calendar_working_days) {
            days.push(workDays.work_day)
          }
          for (const holiday of project.calendar_holidays) {
            holidays.push(new Date(holiday.date))
          }
          this.addCalenderHashMap({
            calenderData: {
              project_id: project.project_id,
              working_hours: project.working_hours,
              workDays: days,
              holidays: holidays
            },
            projectId: project.project_id
          })
        }
      })
    },
    getUsersList (projectIds) {
      this.assigneeMap = {}
      this.associatedUsers = [] // TO reset users list based on selected project
      const userIdForTemp = []
      GetUserListByPojIds(projectIds).then((res) => {
        this.associatedUsers = res.project_user_association.filter((user) => {
          // this is to store assignees of given projects
        // assiignee map will look like
        // {
        //   prjectid:[userid1,userId2]
        //   prjectid2:[userid1,userId2]
        // }
          if (Object.hasOwn(this.assigneeMap, user.associated_project?.id)) {
            this.assigneeMap[user.associated_project?.id].push(user.associated_user.id)
          } else {
            this.assigneeMap[user.associated_project?.id] = [user.associated_user.id]
          }
          // to remove the duplication need to check whether the user Data is already added or not
          // since there is no project id while fetching project_associated users initilly need to this one
          if (!userIdForTemp.includes(user.associated_user.id)) {
            userIdForTemp.push(user.associated_user.id)
            return true
          }
        }).map((filteredUser) => {
          return {
            label:
            filteredUser.associated_user?.first_name +
              ' ' +
              filteredUser.associated_user?.last_name,
            value: filteredUser.associated_user.id
          }
        })
      }).catch((err) => {
        console.log(err)
      })
    },
    generateDynamicDeleteQuery (deleteOperations) {
      const mutations = deleteOperations.map(({ typeName, conditions }, index) => `
    ${typeName}_${index + 1}: ${typeName}(where: ${conditions}) {
      affected_rows
    }
  `)
      return `
    mutation deleteChangesTimeLineQuery {
      ${mutations.join('\n')}
    }
  `
    },
    generateDynamicInsertQuery (insertOperation) {
      const mutations = insertOperation.map(({ typeName, conditions }, index) => `
    ${typeName}_${index + 1}: ${typeName}(objects: [${conditions.map(obj => `{${Object.entries(obj).map(([key, value]) => `${key}: ${JSON.stringify(value)}`).join(',')}}`).join(',') }]) {
      affected_rows
    }
  `)

      return `
    mutation insertChangesTimeLineQuery {
      ${mutations.join('\n')}
    }
  `
    },
    openProjectForm (templateId, formId, formName) {
      this.$router.push(`/form/editform/${templateId}/${formName}/${formId}`)
    }
  },
  beforeDestroy () {
    this.clearTokenHashMap()
  },
  computed: {
    ...mapGetters(['user', 'isOnProjectLevel', 'collaborator', 'openProjectId', 'projectIdForCollaborator']),
    ...mapGetters('timeLine', ['getProjectTokens', 'getCalederHashMap'])
  },
  watch: {
    '$store.state.projectIdForCollaborator' () {
      this.getTaskAndFormData()
    }
  },
  mounted () {
    this.getTaskAndFormData()
    this.getCalenderData([this.openProjectId])
    // this.getUsersList([this.openProjectId])
  }
}

</script>
<style scoped lang="scss">
.personal-ins {
    &-label{
color:black;
margin-block: 1rem;
    }
    &-table{
      width: 100%;
      table {
      width: 100%;
      position: relative;
      border-collapse: collapse;

      th {
        position: sticky;
        width:auto;
        top: 0px;
        font-weight: 500;
        background-color: var(--brand-color);
      }
      tr{
        z-index: 0;
      }
      th,
      td {
        text-align: left;
        font-size: 12px;
        padding: 8px 4px;
      }
      tr:nth-child(odd) {
        background-color: rgba(var(--brand-rgb), 0.05);
        border: 1px solid var(--brand-color);
      }
    }
    &-overdue{
        color:red
      }
  }
}
</style>
