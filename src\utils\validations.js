export function isValidLatitude (latitude) {
  const latitudeRegex = /^(-?([1-8]?[0-9](\.[0-9]{1,6})?|90(\.0{1,6})?))$/
  return latitudeRegex.test(latitude)
}

export function isValidLongitude (longitude) {
  const longitudeRegex = /^(-?1[0-7]?[0-9]{1,2}|180(\.0{1,6})?)$|^(-?[0-9]{1,2}(\.[0-9]{1,6})?)$/
  return longitudeRegex.test(longitude)
}

export function restrictKeys (event) {
  // Prevent typing 'e', 'E', '+', and '-'
  if (['e', 'E', '+', '-'].includes(event.key)) {
    event.preventDefault() // Prevent the key from being typed
  }
}
