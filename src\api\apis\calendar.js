import { runQuery, runMutation } from '../graphQl'
import * as calendarQuery from '../query/calendar'
import store from '../../store'

export const GetCurrentCalendarList = () => {
  let token = 'project'
  let isCollaborator = false
  if (!store.getters?.isOnProjectLevel) {
    token = 'tenant'
  }
  if (store.getters?.collaborator) {
    isCollaborator = true
  }
  return runQuery(calendarQuery.GetCurrentCalendarListQuery(), { isCollaborator }, token)
}

export const GetCalendarList = (isOnProjectLevel, isCollaborator = false) => {
  const conditions = {}
  if (!isOnProjectLevel) {
    conditions.project_id = { _is_null: true }
  }
  return runQuery(calendarQuery.GetCalendarListQuery(), { isCollaborator, conditions }, 'tenant')
}

export const GetCurrentCalendarListByToken = (token) => {
  const isCollaborator = false
  return runQuery(calendarQuery.GetCurrentCalendarListQuery(), { isCollaborator }, 'current', token)
}

export const IfExistingCalendar = (calendar) => {
  return runQuery(calendarQuery.CalendarExistCheck(), { calendar }, 'project')
}

export const EditCalendar = (data, isOnProjectLevel = false) => {
  return runMutation(calendarQuery.UpdateCalendar(), data, isOnProjectLevel ? 'project' : 'tenant')
}

export const EditCalendarWorkingDays = (id, data) => {
  return runMutation(calendarQuery.updateCalendarWorkDays(), { data, id }, 'project')
}

export const UpdateWorkingDays = (data) => {
  return runMutation(calendarQuery.UpdateWorkingDaysQuery(), { data }, 'tenant')
}

export const UpdateHoliday = (data, id, isOnProjectLevel = false) => {
  return runMutation(calendarQuery.UpdateHolidayQuery(), { data, id }, isOnProjectLevel ? 'project' : 'tenant')
}

export const AddNewCalendar = (data) => {
  return runMutation(calendarQuery.AddCalendar(), { data }, 'project')
}

export const CheckCalendarHoliday = (id) => {
  return runQuery(calendarQuery.CalendarHolidayExists(), { id }, 'project')
}

export const AddHolidayInCalendar = (data, isOnProjectLevel = false) => {
  return runMutation(calendarQuery.AddHolidaysInCalendar(), { data }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}

export const DeleteCalendarById = (id, isOnProjectLevel = false) => {
  return runMutation(calendarQuery.DeleteCalendarById(), { id }, isOnProjectLevel ? 'project' : 'tenant')
}
// here wed sre getiing the data based on tenant  token
export const GetCalendarBasedOnProjectId = (projectId) => {
  return runMutation(calendarQuery.GetCalendarBasedOnProjectIdQuery(), { projectId }, store.getters.collaborator ? 'tenant' : 'project')
}

export const getAllCalendarData = (projectId) => {
  return runMutation(calendarQuery.GetCalendarDataByProjectIdQuery(), { project_id: projectId }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
