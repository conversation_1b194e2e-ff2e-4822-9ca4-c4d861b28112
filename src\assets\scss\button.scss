button.btn {
  font-size: 1em;
  padding: 0.5em 1.2em;
  border-radius: 0.3em;
  color: var(--black);
  font-weight: 500;
  border: none;
  box-shadow: 0.1em 0.1em 0.1em rgba(0,0,0,0.25);
  background-color: var(--brand-color);
  &:active {
    box-shadow: inset 0.2em 0.2em 0.2em rgba(0,0,0,0.25);
  }
  &:focus{
    box-shadow: rgba(6, 24, 44, 0.4) 0px 0px 0px 2px, rgba(6, 24, 44, 0.65) 0px 4px 6px -1px, rgba(255, 255, 255, 0.08) 0px 1px 0px inset;
  }
  &-diselected {
    background-color: var(--side-bar-color);
  }
  &-success {
    background-color: var(--success);
  }
  &-warning {
    background-color: var(--warning);
  }
  &-error {
    background-color: var(--alert);
  }
  &-black {
    color: var(--white);
    background-color: var(--black);
  }
  &-white-text { 
    color: var(--white);
  }
  &-outline {
    background-color: transparent;
    color: var(--brand-color-1);
    border: 1px solid var(--brand-color-1);
  }
  &-black-outline {
    background-color: transparent;
    color: var(--black);
    border: 1px solid var(--black);
  }
  &-success-outline {
    background-color: transparent;
    color: var(--success);
    border: 1px solid var(--success);
  }
  &-error-outline {
    background-color: transparent;
    color: var(--alert);
    border: 1px solid var(--alert);
  }
  &-warning-outline {
    background-color: transparent;
    color: var(--warning);
    border-color: var(--warning);
    box-shadow: none;
  }

  &:disabled {
    opacity: 0.5;
    box-shadow: none;
    cursor: not-allowed;
  }
}

.pnt-disabled{
  pointer-events: none;
  cursor: not-allowed;
  user-select: none;
}