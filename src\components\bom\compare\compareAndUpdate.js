/* eslint-disable brace-style */

export const compareAndUpdate = (parentL, parentIn, childbom) => {
  parentL = sortByTwoProperties(parentL, 'material_id', 'unit_size', true)
  parentIn = sortByTwoProperties(parentIn, 'material_id', 'unit_size', true)
  childbom = sortByTwoProperties(childbom, 'material_id', 'unit_size', true)

  // const parentLatIndexArray = parentL?.map((item) => item?.material_id)
  // const parentIntIndexArray = parentIn?.map((item) => item?.material_id)
  // const childIndexArray = childbom?.map((item) => item?.material_id)
  const updatedBOM = []
  const messages = []
  //     const indexL = parentLatIndexArray.indexOf(parentIntIndexArray[i])
  //     if (indexL !== -1) {
  //       if (parentIn[i]?.material_id === parentL[indexL]?.material_id) {
  //         if (parentIn[i]?.unit_size === parentL[indexL]?.unit_size) {
  //         // push the code to updateBom after checking bom itmes
  //           if (
  //             parentIn[i]?.quantity === parentL[indexL]?.quantity &&
  //             // parentIn[i]?.total_price === parentL[indexL]?.total_price &&
  //             parentIn[i]?.associated_product_code_bom ===
  //             parentL[indexL]?.associated_product_code_bom
  //           ) {
  //             updatedBOM.push({
  //               material_id: parentIn[i]?.material_id,
  //               material_name: parentIn[i]?.core_material?.material_name,
  //               unit_size: 0,
  //               total_price: 0,
  //               quantity: 0,
  //               associated_product_code_bom: parentIn[i]?.associated_product_code_bom,
  //               associated_bom_version: parentIn[i]?.associated_bom_version,
  //               core_material: parentIn[i]?.core_material

  //             })
  //             parentLatIndexArray[indexL] = null
  //             parentIntIndexArray[i] = null
  //           } else {
  //             // let assprodCodeDiff
  //             // if (parentL[indexL]?.associated_product_code_bom === parentIn[i]?.associated_product_code_bom) { assprodCodeDiff = false } else { assprodCodeDiff = true }
  //             updatedBOM.push({
  //               material_id: parentL[indexL]?.material_id,
  //               material_name: parentL[indexL]?.core_material?.material_name,
  //               unit_size: parentL[indexL]?.unit_size,
  //               total_price: parentL[indexL]?.total_price,
  //               quantity: parentL[indexL]?.quantity - parentIn[i]?.quantity,
  //               associated_product_code_bom: parentL[indexL]?.associated_product_code_bom,
  //               associated_bom_version: parentL[indexL]?.associated_bom_version,
  //               core_material: parentL[indexL]?.core_material

  //             })
  //             parentIntIndexArray[i] = null
  //             parentLatIndexArray[indexL] = null
  //           }
  //         } else {
  //           for (let j = i, m = 0; j < parentLatIndexArray?.length; j++, m++) {
  //             if (parentIn[i]?.material_id === parentL[indexL + m]?.material_id) {
  //               if (parentIn[i].unit_size === parentL[indexL + m].unit_size) {
  //                 if (
  //                     parentIn[i]?.quantity === parentL[indexL + m]?.quantity &&
  //                     // parentIn[i]?.total_price === parentL[indexL + m]?.total_price &&
  //                     parentIn[i]?.associated_product_code_bom ===
  //                     parentL[indexL + m]?.associated_product_code_bom
  //                 ) {
  //                   updatedBOM.push({
  //                     material_id: parentIn[i]?.material_id,
  //                     material_name: parentIn[i]?.core_material?.material_name,
  //                     unit_size: parentIn[i]?.unit_size,
  //                     total_price: 0,
  //                     quantity: 0,
  //                     associated_product_code_bom: parentIn[i]?.associated_product_code_bom,
  //                     associated_bom_version: parentIn[i]?.associated_bom_version,
  //                     core_material: parentIn[i]?.core_material,
  //                     data: {
  //                       latestPIndex: indexL,
  //                       initalPIndex: indexL + m
  //                     }
  //                   })
  //                   parentIntIndexArray[i] = null
  //                   parentLatIndexArray[indexL + m] = null
  //                 } else {
  //                   // let assprodCodeDiff
  //                   // if (parentL[indexL + m]?.associated_product_code_bom === parentIn[i]?.associated_product_code_bom) { assprodCodeDiff = false } else { assprodCodeDiff = true }
  //                   updatedBOM.push({
  //                     material_id: parentL[indexL + m]?.material_id,
  //                     material_name: parentL[indexL + m]?.core_material?.material_name,
  //                     unit_size: parentIn[i]?.unit_size,
  //                     total_price: parentL[indexL + m]?.total_price - parentIn[i]?.total_price,
  //                     quantity: parentL[indexL + m]?.quantity - parentIn[i]?.quantity,
  //                     associated_product_code_bom: parentL[indexL + m]?.associated_product_code_bom,
  //                     associated_bom_version: parentL[indexL + m]?.associated_bom_version,
  //                     core_material: parentL[indexL + m]?.core_material,
  //                     data: {
  //                       latestPIndex: indexL + m,
  //                       initalPIndex: i
  //                     }
  //                   })
  //                   parentLatIndexArray[indexL + m] = null
  //                   parentIntIndexArray[i] = null
  //                 }
  //               }
  //             } else {
  //               break
  //             }
  //           }
  //         }
  //       }
  //     } else {
  //       // indexl == -1
  //       updatedBOM.push({
  //         material_id: parentIn[i]?.material_id,
  //         material_name: parentIn[i]?.core_material?.material_name,
  //         unit_size: parentIn[i]?.unit_size,
  //         total_price: -parentIn[i]?.total_price,
  //         quantity: -parentIn[i]?.quantity,
  //         associated_product_code_bom: parentIn[i]?.associated_product_code_bom,
  //         associated_bom_version: parentIn[i]?.associated_bom_version,
  //         core_material: parentIn[i]?.core_material

  //       })
  //       parentIntIndexArray[i] = null
  //     }
  //     if (parentIn[i]?.material_id !== parentIn[i + 1]?.material_id) {
  //       for (let t = 0; t < parentLatIndexArray.length; t++) {
  //         if (parentLatIndexArray[t] === parentIn[i]?.material_id) {
  //           updatedBOM.push({
  //             material_id: parentL[t]?.material_id,
  //             material_name: parentL[t]?.core_material?.material_name,
  //             unit_size: parentL[t]?.unit_size,
  //             total_price: parentL[t]?.total_price,
  //             quantity: parentL[t]?.quantity,
  //             associated_product_code_bom: parentL[t]?.associated_product_code_bom,
  //             associated_bom_version: parentL[t]?.associated_bom_version,
  //             core_material: parentIn[t]?.core_material

  //           })
  //           parentLatIndexArray[t] = null
  //         }
  //       }
  //     }
  //   }
  //   parentIntIndexArray.forEach((materilaId, index) => {
  //     if (materilaId !== null) {
  //       updatedBOM.push({
  //         material_id: parentIn[index]?.material_id,
  //         material_name: parentIn[index]?.core_material?.material_name,
  //         unit_size: parentIn[index]?.unit_size,
  //         total_price: -parentIn[index]?.total_price,
  //         quantity: -parentIn[index]?.quantity,
  //         associated_product_code_bom: parentIn[index]?.associated_product_code_bom,
  //         associated_bom_version: parentIn[index]?.associated_bom_version,
  //         core_material: parentIn[index]?.core_material
  //       })
  //     }
  //   })
  //   parentLatIndexArray.forEach((materilalId, index) => {
  //     if (materilalId !== null) {
  //       updatedBOM.push({
  //         material_id: parentL[index]?.material_id,
  //         material_name: parentL[index]?.core_material?.material_name,
  //         unit_size: parentL[index]?.unit_size,
  //         total_price: parentL[index]?.total_price,
  //         quantity: parentL[index]?.quantity,
  //         associated_product_code_bom: parentL[index]?.associated_product_code_bom,
  //         associated_bom_version: parentL[index]?.associated_bom_version,
  //         core_material: parentL[index]?.core_material
  //       })
  //     }
  //   })
  //   // comparison of latest child bom and updateBom
  //   const childBomNewVersion = []
  //   const materialDiffArray = sortByTwoProperties(updatedBOM, 'material_id', 'unit_size', true)
  //   for (let i = 0; i < childIndexArray.length; i++) {
  //     for (let j = 0; j < materialDiffArray.length; j++) {
  //       if (childbom[i]?.material_id === materialDiffArray[j]?.material_id && materialDiffArray[j] !== null) {
  //         if (childbom[i]?.unit_size === materialDiffArray[j]?.unit_size) {
  //           childBomNewVersion.push({
  //             material_id: childbom[i]?.material_id,
  //             unit_size: childbom[i]?.unit_size,
  //             quantity: childbom[i].quantity + materialDiffArray[j].quantity,
  //             total_price: childbom[i].total_price + materialDiffArray[j].total_price,
  //             // here pusing the latest ass. bom id from latest parent
  //             associated_product_code_bom: materialDiffArray[j].associated_product_code_bom,
  //             associated_bom_version: materialDiffArray[j]?.associated_bom_version,
  //             core_material: childbom[i]?.core_material
  //           })
  //           const qc = materialDiffArray[j].quantity === 0 ? false : materialDiffArray[j]?.quantity
  //           const tc = materialDiffArray[j].total_price === 0 ? false : materialDiffArray[j]?.total_price
  //           const apc = childbom[i]?.associated_product_code_bom !== materialDiffArray[j]?.associated_product_code_bom
  //           const message = {
  //             added: false,
  //             message: `Material (${childbom[i]?.core_material?.material_name} and unit Size ${childbom[i].unit_size} )\n ${qc ? 'quantity changed by ' + materialDiffArray[j].quantity + '\n' : ''}${tc ? 'total price changed by ' + materialDiffArray[j].total_price + '\n' : ''} ${apc ? 'associated Bom version has been changed , will take  associated bom of latest parent \n' : ''}`
  //           }
  //           if (tc || qc || apc) messages.push(message)
  //           childIndexArray[i] = null
  //           materialDiffArray[j] = null
  //         }
  //       }
  //     }
  //   }
  //   childIndexArray.forEach((materialId, index) => {
  //     if (materialId !== null) {
  //       childBomNewVersion.push(childbom[index])
  //     }
  //   })
  //   materialDiffArray.forEach((materialId, index) => {
  //     if (materialId !== null) {
  //       if (materialId?.quantity >= 1) {
  //         childBomNewVersion.push(materialDiffArray[index])
  //         messages.push({
  //           added: true,
  //           message: `New material with name ${materialDiffArray[index].material_name} and unit size ${materialDiffArray[index]?.unit_size} has been added `
  //         })
  //       } else {
  //         childBomNewVersion.push(materialDiffArray[index])
  //         messages.push({
  //           added: false,
  //           message: `Material  with name ${materialDiffArray[index].material_name},(${materialDiffArray[index].core_material.custom_material_id}) and unit size ${materialDiffArray[index].unit_size} has been deleted `
  //         })
  //       }
  //     }
  //   })
  //   // childBomNewVersion.filter((element) => {
  //   //   if (element.quantity > 0) {
  //   //     return element
  //   //   } else {
  //   //     messages.push()
  //   //   }
  //   // })
  // return ({ bom: childBomNewVersion, message: messages })

  for (const [i, materialIn] of Object.entries(parentIn)) {
    let notFoundParentI = true
    for (const [j, materialL] of Object.entries(parentL)) {
      if (materialIn?.material_id && materialL?.material_id && materialIn?.material_id === materialL?.material_id) {
        // both unit size and associated version are  same
        if (materialIn.unit_size === materialL.unit_size && materialIn.associated_bom_version?.core_bom?.id === materialL?.associated_bom_version?.core_bom?.id) {
          const quantityDiff = materialL.quantity - materialIn.quantity
          updateLatestVersion(materialL, materialIn.material_id, materialIn.unit_size, quantityDiff, materialIn.associated_product_code_bom, materialIn.associated_bom_version, [materialIn.associated_bom_version, materialL.associated_bom_version], 1)
          notFoundParentI = false
          parentIn[i] = parentL[j] = null
        }
        // unit size is different but  associated bom is same
        else if (!materialIn.unit_size === materialL.unit_size && materialIn.associated_bom_version?.core_bom?.id === materialL?.associated_bom_version?.core_bom?.id) {
          updateLatestVersion(materialL, materialIn.material_id, materialIn.unit_size, -(materialIn.quantity), materialIn.associated_product_code_bom, materialIn.associated_bom_version, [materialIn.associated_bom_version, materialL.associated_bom_version], 2)
          // MESSAGE NEW MATERIAL ADDED AND REMOVED ONE MATERIAL
          let flag = null
          for (const [k, childMaterial] of Object.entries(childbom)) {
            if (childMaterial?.material_id === materialL.material_id && childMaterial.unit_size === materialL.unit_size) {
              flag = true
              const diff = childMaterial.quantity + materialIn.quantity
              const temp = { ...childMaterial, quantity: diff, assBomSelelction: [{ ...materialIn.associated_bom_version }, { ...childMaterial.associated_bom_version }], state: 'new', selected: true }
              updatedBOM.push(temp)
              messages.push(` New material ${childMaterial.core_material.custom_material_id} has been added  with ${diff} quantity and unit size  ${materialL.unit_size}`)
              childbom.splice(k, 1)
            }
          }
          if (!flag) {
            // MESSAGE NEW MATERIAL ADDED
            messages.push(` New material ${materialIn.core_material.custom_material_id} has been added`)

            updatedBOM.push({ ...materialIn, state: 'new', selected: true })
          }
          notFoundParentI = false
          parentIn[i] = parentL[j] = null
        } // unit size is same but associated bom version in different
        else if (materialIn.unit_size === materialL.unit_size && materialIn.associated_bom_version?.core_bom?.id !== materialL?.associated_bom_version?.core_bom?.id) {
          const quantityDiff = materialL.quantity - materialIn.quantity
          // MESSASGE BOM VERSION HAS CHNAGED
          updateLatestVersion(materialL, materialIn.material_id, materialIn.unit_size, quantityDiff, materialIn.associated_product_code_bom, materialL.associated_bom_version, [materialIn.associated_bom_version, materialL.associated_bom_version], 3)
          parentIn[i] = parentL[j] = null
          notFoundParentI = false
        }
        // both unit size and associated version is differnt
        else {
          notFoundParentI = false
          updateLatestVersion(materialIn, materialIn.material_id, materialIn.unit_size, -(materialIn.quantity), materialIn.associated_product_code_bom, materialIn.associated_bom_version, [materialIn.associated_bom_version, materialL.associated_bom_version], 4)
          updateLatestVersion(materialL, materialL.material_id, materialL.unit_size, materialL.quantity, materialL.associated_product_code_bom, materialL.associated_bom_version, [materialIn.associated_bom_version, materialL.associated_bom_version], 4)
          parentIn[i] = parentL[j] = null
        }
      }
    }
    if (notFoundParentI) {
      updateLatestVersion(materialIn, materialIn.material_id, materialIn.unit_size, -(materialIn.quantity), materialIn.associated_product_code_bom, materialIn.associated_bom_version, [], null)
    }
  }
  for (const materialL of parentL) {
    if (materialL) {
      updateLatestVersion(materialL, materialL.material_id, materialL.unit_size, materialL.quantity, materialL.associated_product_code_bom, materialL.associated_bom_version, [], null)
    }
  }

  return ({
    bom: [...updatedBOM, ...(childbom.map((element) => {
      element.state = 'old'
      return element }))],
    message: messages
  })
  function updateLatestVersion (
    oldMaterial,
    materialId,
    unitSize,
    diff,
    selectedBomid = null,
    selectedversion = null,
    associatedversions = [],
    cond) {
    let flag = false
    for (const [k, material] of Object.entries(childbom)) {
      if (material?.material_id === materialId && material.unit_size === unitSize) {
        const temp = { ...material, quantity: material.quantity + diff, associated_product_code_bom: selectedBomid, associated_bom_version: selectedversion, associatedversions: associatedversions, state: null }
        updatedBOM.push(temp)
        childbom.splice(k, 1)
        flag = true
        if (material.associated_bom_version) {
          associatedversions = [...associatedversions, material.associated_bom_version]
        }
        if (material.quantity + diff <= 0) {
          messages.push(`material ${material.core_material.custom_material_id} has been  deleted `)
        }
        else if (diff && selectedversion?.core_bom?.id === material?.associated_bom_version?.core_bom?.id)
        { messages.push(`material ${material.core_material.custom_material_id} has changed by ${diff} quantity `) }
        else if (diff && selectedversion?.core_bom?.id !== material.associated_bom_version?.core_bom?.id) {
          messages.push(`material ${material.core_material.custom_material_id} has changed by ${diff} quantity  and new bom ${selectedversion?.core_bom?.name + ' && version-' + selectedversion?.version_no } has associated`)
        }
        else if (!diff && selectedversion?.core_bom?.id !== material?.associated_bom_version?.core_bom?.id) {
          messages.push(`material ${material.core_material?.custom_material_id} has attached by new bom ${selectedversion?.core_bom?.name + ' && version-' + material?.associated_bom_version
            ?.version_no }`)
        }

        break
      }
    }

    if (!flag) {
      let state = null
      if (diff < 0) {
        messages.push(`material ${oldMaterial.core_material.custom_material_id} has been  deleted`)
        updatedBOM.push({ ...oldMaterial, quantity: 0, associated_product_code_bom: selectedBomid, associated_bom_version: selectedversion, associatedversions: associatedversions, state: state, selected: true })
        return
      }
      else if (selectedBomid)
      {
        state = 'new'
        messages.push(`New material ${oldMaterial.core_material.custom_material_id}  with ${oldMaterial.quantity} quantity  and  ${selectedversion?.core_bom?.name + '&& version-' + oldMaterial.associated_bom_version
          .version_no } bom has been added `) }
      else {
        state = 'new'
        messages.push(`New material ${oldMaterial.core_material.custom_material_id} has been added  with ${oldMaterial.quantity} quantity`)
      }
      updatedBOM.push({ ...oldMaterial, associated_product_code_bom: selectedBomid, associated_bom_version: selectedversion, associatedversions: associatedversions, state: state, selected: true })
    }
  }
// case 1 : both unit size and associated version are  same
// case 2 : unit size is different but  associated bom is same
// case 3 :  unit size is same but associated bom version in different
// case 4 :
}
function sortByTwoProperties (array, property1, property2, ascending) {
  // Check if the ascending parameter is a boolean value
  if (typeof ascending !== 'boolean') {
    throw new Error('The ascending parameter must be a boolean value')
  }

  // Create a comparator function that compares two objects based on the two properties
  function compareObjects (a, b) {
    // Compare the values of the two properties
    const comparison1 = a[property1] < b[property1] ? -1 : a[property1] > b[property1] ? 1 : 0
    const comparison2 = a[property2] < b[property2] ? -1 : a[property2] > b[property2] ? 1 : 0

    // Return the result of the comparison
    return ascending ? comparison1 + comparison2 : comparison1 - comparison2
  }

  // Sort the array using the comparator function
  array.sort(compareObjects)

  // Return the sorted array
  return array
}

// const reArrangeMaterialData = (data) => {
//   return data?.map((element) => {
//     return ({
//       associated_bom_version: element.associated_bom_version,
//       associated_product_code_bom: element?.associated_product_code_bom,
//       material_id: element?.material_id,
//       quantity: element?.quantity,
//       total_price: element?.total_price,
//       unit_size: element?.unit_size

//     })
//   })
// }
