<template>
  <div class="mb-3">
    <div :class="{
    'form-input v-center ': true,
    'form-input--required': data.required,

    }" > <label>{{data.caption}}:</label> <img
      v-if="data.visibility"
      class="mx-1"
      width="15"
      src="~@/assets/images/eye.svg"
      alt="view"
      /></div>
    <div class="filter" >
      <div class="searchInput input-group relative mt-1"  >
        <input :disabled="viewOnly" type="text" placeholder="Search Tags" v-click-outside="hideSuggestion"  @focus="onInputFocus"
          @keyup="showSuggestion" v-model.trim="searchText" class="pointer">
        <div class="resultBox absolute" v-if="suggestion">
          <ul v-if="suggestions.length > 0">
            <li v-for="element in suggestions" :key="element.id" class="pointer" @click='addTag(element)'>
              {{ element.name }}
            </li>
            <li v-if="lastParentId">Select <span @click="showRootTags" class="tag-modal-link">Another Parent Tag</span></li>
          </ul>
          <span v-else-if="loading">Loading ... </span>
          <span v-else>
            No Tag Found
            <h5>Or</h5>
            Select
            <span @click="showRootTags" class="tag-modal-link">Another Parent Tag</span>
          </span>
        </div>
      </div>
    </div>
    <div class="mt-2">
          <div class="tags" v-for="tag in selectedTags" :key="tag.id">
            <div class="selected-tags">
              {{ tag.name }}
            </div>
          </div>
          <div class="mt-2" v-if="tagGroupData.tagArray.length">
            <label class="key">Attached Tags:</label>
            <div class="tags mt-2 p-1" :class="{'tags-line-selected p-1': selected_tag_line===index }"  v-for="(tagParentwise,index) in tagGroupData.tagArray" :key="index" @click="handleTagLineChange(index)">
              <div v-for="(tag, cindex) in tagParentwise" :key="tag.id "  class="flex v-center tags-line pointer ">
                  <img width="8px" src="~@/assets/images/right-arrow-icon.svg" alt="" :class="{ 'tags-line-first-img': cindex === 0 }" />
                <div :class="{
                  'attached-tags v-center h-center ': true
              }"

                >
                {{ tag.name }}
                    <img
                v-if="!viewOnly"
                :class="{
                  'pointer ml-1 close-icon': true,
                  }"
                @click.stop="removeAttachedTags(tag.id, tag.parentId)"
                @mouseover="sethoveredtag(cindex,index)"
                @mouseleave="sethoveredtag(null,null)"
                src="~@/assets/images/icons/close-icon.svg" width="16px"
              />
              <div :class="{'attachedTags_overLay': cindex>=hoverd_tag?.cindex && index===hoverd_tag?.index}">
              </div>
              </div>

              </div>
            </div>
          </div>
        </div>
  </div>
</template>

<script>
import { SearchParentTags, SearchChildrenTags } from '@/api'
import { TagTrie } from '@/utils/tagsHelper'
import { debounce } from '@/utils/debounce'
import { alert } from '@/plugins/notification'
export default {
  name: 'tags-component',
  props: {
    type: {
      type: Number,
      default: 3
    },
    mode: {
      type: String,
      default: 'TEMPLATE'
    },
    data: {
      type: Object,
      default: () => {}
    },
    value: {
      type: Array,
      default: () => []
    },
    viewOnly: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    value (val) {
      this.componentValue = val
    }
  },
  data () {
    return {
      insert: [],
      delete: [],
      componentValue: [],
      loading: false,
      showSuggestion: null,
      suggestion: false,
      suggestions: [],
      selected_tag_line: null,
      openTagModal: false,
      showError: false,
      selectedTags: [],
      tagTrie: new TagTrie(),
      attachedTags: [],
      tagGroupData: {
        firstLevelParents: [],
        tagArray: []
      },
      lastParentId: null,
      hoverd_tag: {
        cindex: null,
        index: null
      },
      searchText: ''
    }
  },
  computed: {
    EmptyInputError () {
      return this.searchText.trim() === ''
    }
  },
  created () {
    if ((this.mode === 'EDIT' || this.mode === 'VIEW') && this.value?.length) {
      this.attachedTags = this.value.map(item => {
        return {
          id: item.tag.id,
          name: item.tag.name,
          parentId: item.tag.parent_id
        }
      })
      this.tagTrie._generateTreeFromUnorderedList(this.attachedTags)
      this.tagGroupData = this.tagTrie.groupTagInArray()
    }
    this.showSuggestion = debounce(this.getData, 300)
  },
  methods: {
    emitChange () {
      const value = {}
      if (this.insert.length) {
        value.insert = this.insert
      }
      if (this.delete.length) {
        value.delete = this.delete
      }
      this.$emit('customChange', {
        field_id: this.$props.data.field_id,
        value
      })
    },
    handleTagLineChange (index) {
      if (this.viewOnly) return
      this.selected_tag_line = index
      this.lastParentId = this.tagGroupData?.tagArray[index]?.at(-1)?.id
    },
    removeChildTags (tagId) {
      let isTagAlreadyAttached = false
      for (const tag of this.attachedTags) {
        if (tag.id === tagId) {
          isTagAlreadyAttached = true
        }
        if (tag.parentId === tagId) {
          this.removeChildTags(tag.id)
        }
      }
      if (isTagAlreadyAttached) {
        this.delete.push(tagId)
        this.emitChange()
      } else {
        for (let i = 0; i < this.insert.length; i++) {
          if (this.insert[i] === tagId) {
            this.insert.splice(i, 1)
            break
          }
        }
      }
    },
    removeAttachedTags (tagId, parentId) {
      this.lastParentId = null
      this.selected_tag_line = null
      this.tagTrie.deleteTagById2(tagId)
      this.tagGroupData = this.tagTrie.groupTagInArray()
      const tagArray = this.tagTrie.getTagArray()
      this.componentValue = tagArray.map((tag) => tag.id)
      if (this.mode === 'EDIT') {
        // const isTagAlreadyAttached = this.attachedTags.find((item) => item.id === tagId)
        let isTagAlreadyAttached = false
        for (const tag of this.attachedTags) {
          if (tag.id === tagId) {
            isTagAlreadyAttached = true
          }
          if (tag.parentId === tagId) {
            this.removeChildTags(tag.id)
          }
        }
        if (isTagAlreadyAttached) {
          this.delete.push(tagId)
          this.emitChange()
        } else {
          for (let i = 0; i < this.insert.length; i++) {
            if (this.insert[i] === tagId) {
              this.insert.splice(i, 1)
              break
            }
          }
        }
      }
      this.getData()
    },
    sethoveredtag (cindex, index) {
      this.hoverd_tag.cindex = cindex
      this.hoverd_tag.index = index
    },
    addTag (tag) {
      if (tag) {
        this.tagTrie.insertTag({ name: tag.name, id: tag.id, parentId: tag.parent_id })
        this.tagGroupData = this.tagTrie.groupTagInArray()
        this.suggestions = []
        this.searchText = ''
        if (tag.parent_id === null) {
          this.handleParentLevelSelection(tag.id)
        }
        this.lastParentId = this.tagGroupData?.tagArray[this.selected_tag_line]?.at(-1)?.id
        const tagArray = this.tagTrie.getTagArray()
        this.componentValue = tagArray.map((tag) => tag.id)
        if (this.mode === 'EDIT') {
          const isTagAlreadyAttached = this.attachedTags.find((item) => item.id === tag.id)
          if (!isTagAlreadyAttached) {
            this.insert.push(tag.id)
            this.emitChange()
          } else {
            for (let i = 0; i < this.delete.length; i++) {
              if (this.delete[i] === tag.id) {
                this.delete.splice(i, 1)
                break
              }
            }
          }
        }
      }
    },
    handleParentLevelSelection (id) {
      if (id === null) {
        this.selected_tag_line = id
      } else {
        this.tagGroupData.tagArray.forEach((element, index) => {
          if (element[0].id === id) { this.selected_tag_line = index }
        })
      }
    },
    hideSuggestion () {
      this.suggestion = false
    },
    onInputFocus () {
      this.suggestion = true
      this.getData()
    },
    getData () {
      this.loading = true
      if (!this.lastParentId) {
        SearchParentTags(this.searchText, this.$props.type)
          .then((res) => {
            this.suggestions = res.tag
          }
          )
          .catch(() =>
            alert('Error while fetching tags')
          ).finally(() => {
            this.loading = false
          })
        return
      }
      SearchChildrenTags(this.lastParentId, this.searchText, this.$props.type)
        .then((res) => {
          this.suggestions = res.tag
        }
        )
        .catch(() =>
          alert('Error while fetching tags')
        ).finally(() => {
          this.loading = false
        })
    },
    validateInput () {
      this.showError = this.searchText.trim() === ''
    },
    showRootTags (event) {
      event.stopPropagation()
      this.suggestion = true
      this.loading = true
      SearchParentTags(this.searchText, this.$props.type)
        .then((res) => {
          this.suggestions = res.tag.filter(item => {
            if (!this.tagGroupData?.firstLevelParents?.includes(item.id)) {
              return {
                id: item.id,
                name: item.name
              }
            }
          })
          if (this.suggestions.length <= 0) {
            alert('all the parent level tags alredy attached')
          }
        })
        .catch(() =>
          alert('Error while fetching tags')
        ).finally(() => {
          this.loading = false
        })
      this.$emit('parentLevelSelected', null)
    }
  },
  beforeDestroy () {
    this.$emit('update-tags', [])
  }
}
</script>

<style scoped lang="scss">
.filter {
  margin-bottom: 0.8rem;
  display: flex;
  flex-wrap: wrap;

  input {
    padding: 0.85em;
    border: none;
  }

  span {
    margin-right: 3px;
  }
}

.tags {
  display: flex;
flex-wrap: wrap;
margin-left: 5px;
  margin-right: 5px;
  row-gap: 5px;
   &-line-selected{
    background-color: var(--brand-light-color);
   border: .4px solid var(--brand-color);
   border-radius: 5px;
   min-width: min-content;
   }
}

.searchInput {
  border-radius: 5px;
  border: 1px solid rgba(9, 8, 8, 0.9);
  border-radius: 4px;
}

.searchInput:focus {
  box-shadow: 0 0 0 1px var(--brand-color-1)
}

.resultBox {
  overflow: scroll;
  max-height: 12rem;
  padding: 2px 8px;
  opacity: 1;
  pointer-events: auto;
  z-index: 1000;
  background-color: var(--bg-color);
  width: 100%;
  box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;

  li {
    list-style: none;
    padding: 8px 12px;
    width: 100%;
    border-radius: 3px;
  }

  li:last-child {
    padding-bottom: 3px;
  }

  li:hover {
    background: #efefef;
  }

}

.tag-modal-link {
  color: blue;
  text-decoration: underline;
  cursor: pointer;
}

.create-new-input {
  width: 260px;
  max-height: 700px;
  overflow: auto;
  padding: 10px;
}

.btn {
  margin-right: 12px;
}

.error-text {
  color: red;
  padding-top: 12px;
}
.error-test-default{
  color: red;
  padding-top: 12px;
  visibility: hidden;
}

.no-tag-found{
  margin-left: 5px;
}
.attached-tags{
  cursor:pointer;
  background-color: var(--brand-color);
  padding: 0.6rem;
  font-size: small;
  border-radius: 0.4rem;
position:relative;
  &-fade{
background-color: rgb(0, 0,0,.6) ;
color:white
}
  & img{
 z-index:4;
  }
  & img{
    display: none;
  }
}
.tags-line{
  text-overflow: ellipsis;
  overflow: hidden;
   white-space: nowrap;
  & img{
    margin-inline: .5rem;
  }
  &-first-img{
    display: none;
  }
  .attached-tags:hover > img {
  display: block;
  scale: 1.2;
// -webkit-box-shadow:0px 0px 123px 21px rgba(215,224,221,0.78);
// -moz-box-shadow: 0px 0px 123px 21px rgba(215,224,221,0.78);
// box-shadow: 0px 0px 123px 21px rgba(215,224,221,0.78);
  }
  .attachedTags_overLay{
    background-color: black;
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 5px;
    opacity: .3;
    z-index: 1;
  }
}
</style>
