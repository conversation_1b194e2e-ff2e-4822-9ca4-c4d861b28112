import ApolloClient from 'apollo-client'
import { HttpLink } from 'apollo-link-http'
import { ApolloLink, split } from 'apollo-link'
import { getMainDefinition } from 'apollo-utilities'
import { WebSocketLink } from '@apollo/client/link/ws'
import { InMemoryCache } from 'apollo-cache-inmemory'
import { gql } from '@apollo/client/core'
import { alert } from '../plugins/notification'
import Config from '../config.js'
import { logout } from './session'
import { SubscriptionClient } from 'subscriptions-transport-ws'

const UNAUTHENTICATION_ERROR = 'Malformed Authorization header'

const httpLink = new HttpLink({ uri: Config.graphQLEndpoint })

const wsLink = new WebSocketLink(new SubscriptionClient(Config.graphQLWsEndpoint, {
  reconnect: true,
  connectionParams: () => {
    const tenantToken = localStorage.getItem(Config.localstorageKeys.TENANT)
    return {
      headers: {
        Authorization: `Bearer ${tenantToken}`
      }
    }
  }

}))

const authMiddleware = new ApolloLink((operation, forward) => {
  // add the authorization to the headers
  let token = localStorage.getItem(Config.localstorageKeys.AUTH)
  switch (operation.getContext().token) {
  case 'auth': token = localStorage.getItem(Config.localstorageKeys.AUTH); break
  case 'tenant': token = localStorage.getItem(Config.localstorageKeys.TENANT); break
  case 'project': token = localStorage.getItem(Config.localstorageKeys.PROJECT); break
  case 'current': token = operation.getContext().tokenString; break // this is for getting token parameter itself ,
  default: token = localStorage.getItem(Config.localstorageKeys.AUTH); break
  }
  operation.setContext({
    headers: {
      authorization: token ? `Bearer ${token}` : ''
    }
  })
  return forward(operation)
})

const httpAuthLink = authMiddleware.concat(httpLink)

const splitLink = split(
  ({ query }) => {
    const definition = getMainDefinition(query)
    return (
      definition.kind === 'OperationDefinition' &&
      definition.operation === 'subscription'
    )
  },
  wsLink,
  httpAuthLink
)

export const apolloClient = new ApolloClient({
  link: splitLink,
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'no-cache',
      errorPolicy: 'ignore'
    },
    query: {
      fetchPolicy: 'no-cache',
      errorPolicy: 'all'
    }
  }
})

export const runQuery = (query, variables, token = 'auth', tokenString = null) => { // token string contains actual token which is passwd an arguement to this this function
  return new Promise((resolve, reject) => {
    apolloClient.query({
      query,
      variables: variables,
      context: {
        token: token,
        tokenString
      }
    }).then((response) => {
      if (response.errors) {
        const error = response.errors[0]
        // alert('failed to fetch data')
        if (error.message === UNAUTHENTICATION_ERROR) {
          localStorage.setItem('lastPath', window.location.pathname)
          logout()
        }
        // --starts --- needs to be deleted once code goes production
        if (error.message === 'hasura cloud limit of 60 requests/minute exceeded') {
          alert(error.message)
        } else {
          alert('failed to fetch data')
        }

        // -- ends ----
        if (error.extensions.code === 'invalid-jwt') {
          alert('Not Authorized')
          logout()
        }
        reject(response.errors)
      } else {
        resolve(response.data)
      }
    })
      .catch((error) => {
        alert('failed to fetch data')
        reject(error)
      })
  })
}

export const runMutation = (mutation, variables, token = 'auth', tokenString = null) => { // token string contains actual token which is passwd an arguement to this this function
  return new Promise((resolve, reject) => {
    apolloClient.mutate({
      mutation,
      variables: variables,
      context: {
        token: token,
        tokenString
      }
    }).then((response) => {
      if (response.errors) {
        const error = response.errors[0]
        // alert('failed to update data')
        if (error.message === UNAUTHENTICATION_ERROR) {
          localStorage.setItem('lastPath', window.location.pathname)
          logout()
        }
        if (error.extensions.code === 'invalid-jwt') {
          alert('Not Authorized')
          logout()
        }
        reject(response.errors)
      } else {
        resolve(response.data)
      }
    })
      .catch((error) => {
        reject(error)
      })
  })
}

export const runSubscription = (query, variables, callback, token = 'tenant') => {
  const observable = apolloClient.subscribe({
    query,
    variables: variables
  })
  const subscription = observable.subscribe({
    next: (response) => {
      callback(response.data.core_notifications, false)
    },
    error: (error) => {
      callback(error, true)
      console.error('Subscription error:', error)
    }
  })
  return subscription
}

export const GQL = gql
