.form-input {
  font-size: 12px;
  margin-bottom: 1em;
  position: relative;
  &--required {
    label {
      &::after {
        content: '*';
        color: var(--brand-color);
        font-size: 1.2em;
        margin-left: 0.1em;
      }
    }
  }
  label {
    display: block;
    font-size: 1.3em;
    margin-bottom: 0.3em;
  }
  input, select, textarea {
    width: 100%;
    padding: 0.85em;
    border: 1px solid rgb(179, 179, 179);
    border-radius: 0.3em;
    background-color: transparent;
    &:focus {
      border-color: var(--brand-color);
    }
  }
  &--options {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: #fff;
    border: 1px solid rgb(179, 179, 179);
    border-radius: 0.3em;
    z-index: 1;
    padding: 10px;
    max-height: 200px;
    overflow-y: auto;
  }
  &--option {
    padding: 0.5em;
    cursor: pointer;
    align-items: flex-start;
    &:hover {
      background-color: rgb(223, 223, 223);
    }
    &__name {
      font-size: 1em;
    }
    &__email {
      font-size: 0.9em;
      opacity: 0.7;
    }
    & input[type="checkbox"] {
      width: auto;
      margin-right: 0.5em;
    }
  }
}