
const addTooltip = (el, text) => {
  el.addEventListener('mouseenter', showTooltip)
  el.addEventListener('mouseleave', hideTooltip)
  el.__tooltipAttached__ = true
}

const showTooltip = (e) => {
  const el = e.target
  const targetBox = el.getBoundingClientRect()
  const text = el.innerText

  const tooltip = document.createElement('div')
  tooltip.classList.add('overflow-tooltip')
  tooltip.innerText = text
  tooltip.id = 'overflow-tooltip'

  // Append to DOM before measuring dimensions
  document.body.appendChild(tooltip)

  const toolTipBox = tooltip.getBoundingClientRect() // Measure now
  const screenWidth = window.innerWidth
  const screenHeight = window.innerHeight

  const bottom = targetBox.y + toolTipBox.height + 10 < screenHeight
  const top = targetBox.y > toolTipBox.height + 10
  const right = targetBox.x + toolTipBox.width / 2 + 10 < screenWidth
  const left = targetBox.x > toolTipBox.width / 2 + 10

  let direction = 'top'
  if (top && left && right && bottom) {
    direction = 'top'
  } else if (top) {
    if (right) {
      direction = 'right'
    } else if (left) {
      direction = 'left'
    }
  } else if (bottom) {
    if (bottom && left && right) {
      direction = 'bottom'
    } else if (right) {
      direction = 'right'
    } else if (left) {
      direction = 'left'
    }
  }
  switch (direction) {
  case 'left':
    tooltip.setAttribute('class', 'tooltip-container-left')
    tooltip.style.left = `${targetBox.left - toolTipBox.width - 10}px`
    tooltip.style.top = `${targetBox.top + targetBox.height / 2 - toolTipBox.height / 2}px`
    break
  case 'right':
    tooltip.style.left = `${targetBox.left + targetBox.width + 10}px`
    tooltip.style.top = `${targetBox.top + targetBox.height / 2 - toolTipBox.height / 2}px`
    break
  case 'top':
  {
    tooltip.setAttribute('class', 'overflow-tooltip')
    tooltip.style.left = `${targetBox.left + targetBox.width / 2}px`
    tooltip.style.top = `${targetBox.top}px`
    break }
  default:
    tooltip.setAttribute('class', 'tooltip-container-bottom')
    tooltip.style.left = `${targetBox.left + targetBox.width / 2 - toolTipBox.width / 2}px`
    tooltip.style.top = `${targetBox.top + targetBox.height + 10}px`
  }
}

const hideTooltip = () => {
  const tooltip = document.getElementById('overflow-tooltip')
  if (tooltip) {
    tooltip.remove()
  }
}
const removeEventListeners = (el) => {
  el.removeEventListener('mouseenter', showTooltip)
  el.removeEventListener('mouseleave', hideTooltip)
  el.__tooltipAttached__ = false
}
const initTooltip = (el) => {
  const isOverflowing = Math.ceil(el.offsetWidth) < Math.floor(el.scrollWidth)
  if (isOverflowing && !el.__tooltipAttached__) {
    addTooltip(el)
  } else if (!isOverflowing && el.__tooltipAttached__) {
    removeEventListeners(el)
  }
}

const overflowTooltip = {
  bind (el) {
    // Listen to window resize
    el.__resizeHandler__ = () => initTooltip(el)
    window.addEventListener('resize', el.__resizeHandler__)

    // ResizeObserver for dynamic size changes
    if (window.ResizeObserver) {
      el.__resizeObserver__ = new ResizeObserver(() => initTooltip(el))
      el.__resizeObserver__.observe(el)
    }
  },
  inserted (el) {
    initTooltip(el)
  },
  update (el) {
    removeEventListeners(el)
    initTooltip(el)
  },
  componentUpdated (el) {
    removeEventListeners(el)
    initTooltip(el)
  },
  unbind (el) {
    removeEventListeners(el)
    hideTooltip()
    window.removeEventListener('resize', el.__resizeHandler__)
    delete el.__resizeHandler__

    if (el.__resizeObserver__) {
      el.__resizeObserver__.disconnect()
      delete el.__resizeObserver__
    }
  }
}

// export the directive
export default overflowTooltip
