<template>
  <div class="beacon-loading">
    <div class="v-center">
      L
      <loading-circle class="mr-2 ml-4" />
      <!-- <div class="mr-2 ml-4">
        <div class="rotating">
          <div class="outer-circle"></div>
          <div class="inner-circle"></div>
          <div class="triangle-1"></div>
          <div class="triangle-2"></div>
          <div class="triangle-3"></div>
          <div class="triangle-4"></div>
          <div class="triangle-5"></div>
          <div class="triangle-6"></div>
          <div class="triangle-7"></div>
          <div class="triangle-8"></div>
        </div>
        <div class="triangle-9"></div>
      </div> -->
      ADING
      <div class="dot mt-4"></div>
    </div>
  </div>
</template>

<script>
import loadingCircle from './loadingCircle.vue'
export default {
  components: { loadingCircle }
}
</script>

<style lang="scss" scoped>
.beacon-loading {
  position: fixed;
  height: 100vh;
  width: 100vw;
  top: 0;
  left: 0;
  background: white;
  display: flex;
  justify-content: center;
  align-content: center;
  font-size: 40px;
  font-weight: 500;
  & > div {
    & > div {
        position: relative;
      .rotating {
        font-size: 38px;
        height: 0.8em;
        width: 0.8em;
        animation: rotate 2s linear infinite;
        @keyframes rotate {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      }
      .outer-circle {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        background-color: var(--brand-color);
        border-radius: 50%;
      }
      .inner-circle {
        top: 50%;
        left: 50%;
        position: absolute;
        height: 75%;
        width: 75%;
        transform: translate(-50%, -50%);
        background-color: var(--white);
        border-radius: 50%;
      }
      .triangle-1 {
        position: absolute;
        height: 0;
        width: 0;
        top: -0.2em;
        left: 0.35em;
        border-right: 0.07em solid transparent;
        border-left: 0.07em solid transparent;
        border-bottom: 0.25em solid var(--brand-color);
      }
      .triangle-2 {
        position: absolute;
        height: 0;
        width: 0;
        top: 0.8em;
        left: 0.35em;
        border-right: 0.07em solid transparent;
        border-left: 0.07em solid transparent;
        border-top: 0.25em solid var(--brand-color);
      }
      .triangle-5 {
        position: absolute;
        height: 0;
        width: 0;
        top: 0.3em;
        left: 0.87em;
        border-right: 0.07em solid transparent;
        border-left: 0.07em solid transparent;
        border-top: 0.25em solid var(--brand-color);
        transform: rotate(-90deg);
      }
      .triangle-8 {
        position: absolute;
        height: 0;
        width: 0;
        top: 0.3em;
        left: -0.16em;
        border-right: 0.07em solid transparent;
        border-left: 0.07em solid transparent;
        border-top: 0.25em solid var(--brand-color);
        transform: rotate(90deg);
      }
      .triangle-3 {
        position: absolute;
        height: 0;
        width: 0;
        top: 0em;
        left: 0.67em;
        border-right: 0.07em solid transparent;
        border-left: 0.07em solid transparent;
        border-bottom: 0.15em solid var(--brand-color);
        transform: rotate(45deg);
      }
      .triangle-4 {
        position: absolute;
        height: 0;
        width: 0;
        top: 0.65em;
        left: 0.7em;
        border-right: 0.07em solid transparent;
        border-left: 0.07em solid transparent;
        border-bottom: 0.15em solid var(--brand-color);
        transform: rotate(135deg);
      }
      .triangle-6 {
        position: absolute;
        height: 0;
        width: 0;
        top: 0.65em;
        left: 0em;
        border-right: 0.07em solid transparent;
        border-left: 0.07em solid transparent;
        border-bottom: 0.15em solid var(--brand-color);
        transform: rotate(225deg);
      }
      .triangle-7 {
        position: absolute;
        height: 0;
        width: 0;
        top: 0em;
        left: 0.02em;
        border-right: 0.07em solid transparent;
        border-left: 0.07em solid transparent;
        border-bottom: 0.15em solid var(--brand-color);
        transform: rotate(-45deg);
      }
      .triangle-9 {
            position: absolute;
    height: 0;
    width: 0;
    top: 0.1em;
    left: -0.3em;
    border-right: 0.075em solid transparent;
    border-left: 0.075em solid transparent;
    border-top: 0.5em solid var(--brand-color);
    transform: rotate(90deg);
      }
    }
  }
  .dot {
    height: 10px;
    width: 10px;
    background-color: var(--white);
    border-radius: 50%;
    animation: load 2s linear infinite;
    @keyframes load {
      0% {
        box-shadow: 14px 0 0 var(--white), 28px 0 0 var(--white), 42px 0 0 var(--white);
      }
      16.7% {
        box-shadow: 14px 0 0 var(--brand-color), 28px 0 0 var(--white), 42px 0 0 var(--white);
      }
      33.33% {
        box-shadow: 14px 0 0 var(--brand-color), 28px 0 0 var(--brand-color), 42px 0 0 var(--white);
      }
      50% {
        box-shadow: 14px 0 0 var(--brand-color), 28px 0 0 var(--brand-color), 42px 0 0 var(--brand-color);
      }
      66.7% {
        box-shadow: 14px 0 0 var(--brand-color), 28px 0 0 var(--brand-color), 42px 0 0 var(--white);
      }
      83.33% {
        box-shadow: 14px 0 0 var(--brand-color), 28px 0 0 var(--white), 42px 0 0 var(--white);
      }
      100% {
        box-shadow: 14px 0 0 var(--white), 28px 0 0 var(--white), 42px 0 0 var(--white);
      }
    }
  }
}
</style>
