export const getGanttTypeFromString = (type) => {
  if (type === 'task') {
    return 1
  } else if (type === 'project') {
    return 2
  } else if (type === 'milestone') {
    return 3
  } else {
    return 1
  }
}

export const getGanttTypeFromNumber = (type) => {
  type = parseInt(type)
  if (type === 1) {
    return 'task'
  } else if (type === 2) {
    return 'project'
  } else if (type === 3) {
    return 'milestone'
  } else {
    return 'task'
  }
}
