<template>
  <div class="calendar-settings">
    <div class="box flex-column">
      <div class="working-hours">
        <div class="input-group imp">
            <div class="input-group-row mb-3">
              <label class="mb-3">Calendar Name</label>
              <div data-validation="calendarName" class="input-field">
                <input class="input-name" v-model="parsedData.name" type="text" placeholder="Calendar Name">
              </div>
            </div>
            <div class="input-group-row mb-3">
              <label class="mb-3">Description</label>
              <div data-validation="description" class="input-field">
                <input class="input-description" v-model="parsedData.description" type="text" placeholder="Description">
              </div>
            </div>
          </div>
          <label class="mb-5 bold">Choose the Working Days</label>
          <br><br>
          <div div class="grid-2">
            <div class="input-group imp mb-3" v-for="(day, index) in parsedData.calendar_working_days" :key="day.id">
              <div class="input-group-row">
                <label class="mb-3">{{ day.day_name }}</label>
                <div class="input-field ml-3">
                  <input class="checkbox-weekend-off" :checked="day.deleted === false" type="checkbox" @change="toggleCheckbox(index)"/>
                </div>
              </div>
            </div>
          </div>
        <div class="input-group imp mb-3">
          <div class="input-group-row">
            <label class="mb-3">Start Time</label>
            <div class="input-field ml-3">
              <input class="checkbox-weekend-off" v-model="parsedData.working_hours_start" type="time"/>
            </div>
          </div>
          <br>
          <div class="input-group-row">
            <label class="mb-3">End Time</label>
            <div class="input-field ml-3">
              <input class="checkbox-weekend-off" v-model="parsedData.working_hours_end" type="time" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex-end pt-4">
      <button class="btn btn-black ml-4 pointer" @click="cancelEdit">Cancel</button>
      <button class="btn ml-4 pointer" @click="submitCalendar">Save</button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { EditCalendar, EditCalendarWorkingDays } from '@/api'
import { emptyString } from '@/helper/formValidation'
import { alert, success } from '@/plugins/notification'
export default {
  props: {
    editCalendar: {
      type: Object,
      required: true
    }
  },
  watch: {
    '$editCalendar.calendar_working_days' () {
      if (this.editCalendar.calendar_working_days) {
      }
    }
  },
  mounted () {
    this.parsedData = JSON.parse(JSON.stringify(this.editCalendar)) ?? {}
    this.calendarWorkingDays = this.parsedData.calendar_working_days
  },
  data () {
    return {
      parsedData: {},
      calendarWorkingDays: []
    }
  },
  computed: {
    ...mapGetters([
      'isOnProjectLevel', 'user'
    ])
  },
  methods: {
    toggleCheckbox (index) {
      this.parsedData.calendar_working_days[index].deleted = !this.parsedData.calendar_working_days[index].deleted
    },
    cancelEdit () {
      this.$emit('cancel')
    },
    submitCalendar () {
      if (!emptyString(this.parsedData.name, 'Calendar Name', 'calendarName')) {
        return
      }
      if (!emptyString(this.parsedData.description, 'Description', 'description')) {
        return
      }
      const startDateTime = new Date(`1970-01-01T${this.parsedData.working_hours_start}Z`)
      const endDateTime = new Date(`1970-01-01T${this.parsedData.working_hours_end}Z`)
      const timeDiff = endDateTime - startDateTime
      const hours = Math.floor(timeDiff / 3600000)
      const minutes = Math.floor((timeDiff % 3600000) / 60000)
      const fractionOfHour = minutes / 60
      const totalWorkingHours = hours + fractionOfHour // Calculate the total working hours as a float
      this.parsedData.working_hours = Number(totalWorkingHours.toFixed(2))
      const submitData = {
        id: this.editCalendar?.id,
        name: this.parsedData.name,
        description: this.parsedData.description,
        working_hours: this.parsedData.working_hours,
        working_hours_start: this.parsedData.working_hours_start,
        working_hours_end: this.parsedData.working_hours_end
      }
      EditCalendar(submitData, this.isOnProjectLevel).then(res => {
        const promises = this.parsedData.calendar_working_days.map(item => EditCalendarWorkingDays(item.id, item.deleted))
        Promise.allSettled(promises)
          .then(results => {
            success('Calendar edited successfully')
            this.$emit('update')
          })
          .catch(error => {
            console.error('Error in adding calendar working days', error)
          })
      })
        .catch((error) => {
          if (error.message.includes('core_project_calendar_name_key')) {
            alert('Calendar name exists for another tenant!')
          } else {
            alert('Something went wrong.Try Again')
            alert(error.message)
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.bold {
  font-weight: bold;
}
.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* Two columns with equal width */
  gap: 10px; /* Adjust the gap between items */
}
.input-group-row {
  display: flex;
  align-items: center;
}

.input-field {
  display: flex;
  align-items: center;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mx-2 {
  margin: 0 0.5rem;
}

.input-start,
.input-end {
  min-width: 100px; // Adjust as needed
}

.time-input-row {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.day-label {
  margin-right: 1rem;
}

.calendar-settings {
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.box {
  overflow-y: auto;
  padding: 1rem;
}
</style>
