<template>
    <div class="time-sheet fh column">
      <div class="time-sheet-header space-between px-3">
        <h1 class="weight-500 xxl">Time-sheet</h1>
        <div class="flex  v-center">
          <button v-if="!editMode && notViewerProjects.length > 0 " class="btn btn-black mr-3 pointer v-center" @click="editMode=true" > Edit</button>
          <button v-if="checkRole && !collaborator" class="btn btn-brand mr-3 pointer v-center" @click="$router.push('teamWise')">Project Wise</button>
          <button v-if="notViewerProjects.length > 0 && !collaborator" class="btn btn-brand mr-3 pointer" @click="$router.push('reports')">See Reports</button>
        </div>
      </div>
      <div class="time-sheet-bar flex space-between">
        <div class="flex v-center mt-4 time-sheet-bar-add">
          Date Range
           <span class="time-sheet-bar-week ml-4 mr-1 v-center">
            <input type="week" class="" :max="maxWeekEntry"  @change="handleSelelctedDate" :value="date.selectedYear + '-W' + date.selectedWeek" >
            <img
            width="20px"
            class=" time-sheet-bar-calender "
            src="~@/assets/images/calender-icon.svg"
            alt=""
          /></span>
          <button class="time-sheet-bar-next mr-3 v-center" @click="selectNextWeek(-1)">
            <img width="8px" class="input " src="~@/assets/images/left-arrow-icon.svg" alt=""  />
          </button>
          <span class="v-center flex ">{{ date.startDate }} {{ ' - ' }} {{ date.endDate }}</span>
          <button class="time-sheet-bar-next mx-3 v-center" v-show="checkeditableWeek" @click = "selectNextWeek(1)">
            <img width="8px" src="~@/assets/images/right-arrow-icon.svg" alt=""  />
          </button>

          <button v-if ="editMode" class="time-sheet-header-reload pointer v-center ml-3"   v-tooltip="'Click to get last week template'" @click="getLastWeekTemplate">
             <img width="18px" class="input " src="~@/assets/images/icons/reload-icon.svg" alt="" />
          </button>
        </div>
        <div>
        </div>
      </div>
      <time-sheet-table
      :date="date"
      :editMode="editMode"
      @removeEditMode="removeEditMode"
      :lastWeekDates="lastWeekDates"
      :notViewerProjects="notViewerProjects"
      />

    </div>
  </template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import { alert } from '@/plugins/notification'
import timeSheetTable from '../../components/timeSheet/timeSheetTable.vue'
import {
  startAndEndDateFinder,
  getMonthInString,
  getDayToString,
  getWeekNumber
} from '@/utils/date'
import { getProjectUserExceptViewer, getProjectUserForCollaborator } from '@/api'
export default {
  name: 'update',
  components: {
    timeSheetTable
  },
  data: () => ({
    date: {
      selectedWeek: null,
      selectedYear: null,
      startDate: null,
      formatedDates: [], // it has all  the dates in iso format from mon to sun
      weekDays: []
    },
    notViewerProjects: [],
    editMode: false,
    lastWeekDates: [],
    maxWeekEntry: new Date().getFullYear() + '-W' + getWeekNumber(new Date()).toString().padStart(2, 0)
  }),
  computed: {
    ...mapGetters(['user', 'getUserById', 'adminProjetcts', 'collaborator']),
    checkRole () {
      if (this.user.tenantLevelRole === 'ADMIN' || this.adminProjetcts.length > 0) { return (true) } else return false
    },
    checkEdit () {
      return this.user.projectLevelRole !== 'VIEWER'
    },
    checkeditableWeek () {
      if ((getWeekNumber(new Date()) <= getWeekNumber(this.date.formatedDates[6])) && new Date().getFullYear() <= this.date.formatedDates[6].getFullYear()) {
        return false
      } else return true
    }
  },
  methods: {
    ...mapMutations('timeLine', ['clearTokenHashMap']),
    handleSelelctedDate (e) {
      // e.target.value ==2023-W28 will come like this
      const [year, week] = e.target.value.split('-W')
      this.getNewDateFormats(week, year)
    },
    getNewDateFormats (week, year) {
      this.date.selectedWeek = week
      this.date.selectedYear = year
      this.date.formatedDates = startAndEndDateFinder(week, year)
      this.date.startDate = getMonthInString(this.date.formatedDates[0].getMonth()) + ' ' + this.date.formatedDates[0].getDate()
      this.date.endDate = getMonthInString(this.date.formatedDates[6].getMonth()) + ' ' + this.date.formatedDates[6].getDate()
      this.date.weekDays = []
      this.date.formatedDates.forEach(element => {
        this.date.weekDays.push(getDayToString(element.getDay()) + ' - ' + element.getDate())
      })
    },
    selectNextWeek (changer) {
      let currentDate = null
      if (changer === 1) {
        currentDate = new Date(this.date.formatedDates[6])
        if ((getWeekNumber(new Date()) <= getWeekNumber(this.date.formatedDates[6])) && new Date().getFullYear() <= this.date.formatedDates[6].getFullYear()) {
          return
        }
      } else {
        currentDate = new Date(this.date.formatedDates[0])
      }

      currentDate.setDate(currentDate.getDate() + changer)
      this.getNewDateFormats(getWeekNumber(currentDate), currentDate.getFullYear())
    },
    openEditMode () {
      this.editMode = true
    },
    removeEditMode () {
      this.editMode = false
    },
    getLastWeekTemplate () {
      const currentDate = new Date(this.date.formatedDates[0])
      currentDate.setDate(currentDate.getDate() - 1)
      this.lastWeekDates = startAndEndDateFinder(getWeekNumber(currentDate), currentDate.getFullYear())
    },
    // here  the projects where user has role as viewer has excepted
    getAllAssociatedProjects () {
      if (this.collaborator) {
        getProjectUserForCollaborator(this.user.userId).then((res) => {
          this.notViewerProjects = res.core_projects
        })
      } else {
        getProjectUserExceptViewer(this.user.userId).then((res) => {
          this.notViewerProjects = res?.project_user_association.map((obj) => { return (obj.associated_project) })
        }
        ).catch(() => {
          alert('Unable to fetch projects')
        })
      }
    }
  },

  mounted () {
    this.getAllAssociatedProjects()
  },
  created () {
    this.getNewDateFormats(getWeekNumber(new Date()), new Date().getFullYear())
  },
  destroyed () {
    this.clearTokenHashMap()
  }
}
</script>
    <style lang="scss" scoped>
  .time-sheet {
    height: calc(100% - 60px);
    &-header {
      height: 60px;
      margin: -12px;
      margin-bottom: 0;
      background: var(--bg-color);
      border-bottom: var(--border);
      display: flex;
      align-items: center;
      &-reload{
        font-size: 1em;
  padding: 0.2em .4em;
  border-radius: 0.3em;
  color: var(--black);
  font-weight: 500;
  border: none;
  box-shadow: 0.1em 0.1em 0.1em rgba(0,0,0,0.25);
  background-color: var(--brand-color);
  display: flex;
  justify-content: center;
  align-items: center;
  &:active {
    box-shadow: inset 0.2em 0.2em 0.2em rgba(0,0,0,0.25);
  }
      }
      &-btn-box{
        height: 100%;
      }
    }
      &-toggle{
          &.toggle {
        &-left {
          border-radius: 0.3rem 0px 0px 0.3rem;
        }

        &-right {
          border-radius: 0px 0.3rem 0.3rem 0px;
        }

        &-selected {
          background-color: var(--brand-color);
        }
      }
  }
    &-bar {
      &-add{
      }
      &-next {
        border: 1px solid #d9d9d9;
        padding: 4px;
        background-color: #d9d9d9;
        border-radius: 2px;
      }
      &-next:active {
        background-color: var(--brand-color);
      }
      &-week{
        position: relative;
        & input{
          position: absolute;
      width: 20px;
      height: 20px;
      padding: 0;
      border: none;
      opacity: 0;
        }
      }
    }

    &-toggle {
      font-size: 1em;
      padding: 0.5em 1.2em;
      color: var(--black);
      font-weight: 500;
      border: none;
      box-shadow: 0.1em 0.1em 0.1em rgba(0, 0, 0, 0.25);
      background-color: var(--side-bar-color);

      &:active {
        box-shadow: inset 0.2em 0.2em 0.2em rgba(0, 0, 0, 0.25);
      }
    }
  }

  </style>
